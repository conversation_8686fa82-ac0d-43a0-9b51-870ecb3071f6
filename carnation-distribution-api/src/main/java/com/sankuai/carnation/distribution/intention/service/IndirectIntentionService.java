package com.sankuai.carnation.distribution.intention.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.intention.dto.IndirectIntentionDTO;

/**
 * 功能描述: 弱意向服务
 *
 * <AUTHOR>
 * @date 2022/04/25
 **/
public interface IndirectIntentionService {

    /**
     * 添加一条弱意向订单
     */
    RemoteResponse<Void> addIndirectOrderIntention(IndirectIntentionDTO indirectIntention);
}
