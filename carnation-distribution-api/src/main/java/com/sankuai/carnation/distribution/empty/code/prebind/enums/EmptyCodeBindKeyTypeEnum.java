package com.sankuai.carnation.distribution.empty.code.prebind.enums;

import lombok.Getter;

/**
 * 功能描述: 空码绑定业务键类型
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Getter
public enum EmptyCodeBindKeyTypeEnum {

    UNKNOWN(0, "未知"),
    DP_POI(1, "点评门店"),
    MT_POI(2, "美团门店"),
    DISTRIBUTOR_V1(3, "最初版的分销者，对应distributor表"),
    SHOP_CODE(4,"优惠码门店码"),
    GOODS_CODE(5,"优惠码商品码"),
    NOTE_CODE(6,"笔记码"),
    STAFF_CODE(7,"员工码"),
    BRAND_VERIFY_ID(8,"品牌验真码ID")
    ;


    private final int code;

    private final String desc;

    EmptyCodeBindKeyTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EmptyCodeBindKeyTypeEnum fromCode(int code) {
        for (EmptyCodeBindKeyTypeEnum enumValue : EmptyCodeBindKeyTypeEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }
}
