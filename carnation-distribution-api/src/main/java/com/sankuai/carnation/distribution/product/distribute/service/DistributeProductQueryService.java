package com.sankuai.carnation.distribution.product.distribute.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.product.distribute.dto.DistributeProductConfigDTO;
import com.sankuai.carnation.distribution.product.distribute.request.DistributeProductPageQueryRequest;

import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/6/1
 **/
public interface DistributeProductQueryService {

    /**
     * 分页查询商品
     */
    RemoteResponse<List<DistributeProductConfigDTO>> pageQueryProduct(DistributeProductPageQueryRequest pageQueryRequest);
}
