package com.sankuai.carnation.distribution.intention.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.intention.dto.third.ThirdOrderIntentionDTO;
import com.sankuai.carnation.distribution.intention.dto.third.ThirdOrderQrCodeDTO;
import com.sankuai.carnation.distribution.intention.dto.third.request.CreateAndBindIntentionRequest;
import com.sankuai.carnation.distribution.intention.dto.third.request.ThirdOrderBindUserRequest;
import com.sankuai.carnation.distribution.intention.dto.third.request.CreateThirdOrderQrCodeRequest;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/11/9
 **/
public interface ThirdOrderIntentionService {

    RemoteResponse<ThirdOrderQrCodeDTO> createQrCode(CreateThirdOrderQrCodeRequest request);

    RemoteResponse<Boolean> createIntentionAndBindUser(CreateAndBindIntentionRequest request);

    RemoteResponse<Boolean> bindUser(ThirdOrderBindUserRequest request);

    RemoteResponse<ThirdOrderIntentionDTO> getByThirdOrderId(String bizCode, String thirdOrderId);
}
