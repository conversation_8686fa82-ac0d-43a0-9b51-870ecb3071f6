package com.sankuai.carnation.distribution.promocode.privilege.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 【优惠码】商家权益模板
 */
@Data
public class MerchantPrivilegeTemplateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 权益代码
     */
    private String code;
    /**
     * 权益名称
     */
    private String name;
    /**
     * 状态。0=无效，1=有效，2=审核中，3=审核驳回
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date addTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 权益扩展信息
     */
    private MerchantPrivilegeTemplateExtDTO privilegeExt;
}