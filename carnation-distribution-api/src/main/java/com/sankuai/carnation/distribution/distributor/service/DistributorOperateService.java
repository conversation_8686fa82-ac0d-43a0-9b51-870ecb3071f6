package com.sankuai.carnation.distribution.distributor.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.dto.operate.DistributorOperateDTO;

import java.util.List;

/**
 * 功能描述: 分销员操作接口
 *
 * <AUTHOR>
 * @date 2021/12/24
 **/
public interface DistributorOperateService {

    /**
     * 增加分销员
     */
    RemoteResponse<Long> register(DistributorOperateDTO operateDTO);

    /**
     * 批量注册分销人
     */
    RemoteResponse<String> importDistributor(String fileUrl, List<String> receiverList);
}
