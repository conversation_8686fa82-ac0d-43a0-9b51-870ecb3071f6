package com.sankuai.carnation.distribution.groundpromotion.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.groundpromotion.dto.GroundFastCreateDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/25
 * @description
 */
public interface GroundFastCreateService {

    /**
     * [快速创建地推活动] 加载并创建活动
     * @param groundFastCreateDTO 活动基本信息
     * @return 成功创建的活动map，key为点评城市id，value为地推活动id
     */
    RemoteResponse<Map<Integer, Long>> loadAndCreateGroundPromotion(GroundFastCreateDTO groundFastCreateDTO);

    /**
     * [快速创建地推活动] 加载并创建活动（不同地推类型分开创建）
     * @param groundFastCreateDTO 活动基本信息
     * @return 成功创建的活动map，key为点评城市id，value为地推活动id列表
     */
    RemoteResponse<Map<Integer, List<Long>>> loadAndCreateGroundPromotionV2(GroundFastCreateDTO groundFastCreateDTO);
}
