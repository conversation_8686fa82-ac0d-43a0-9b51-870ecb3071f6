package com.sankuai.carnation.distribution.privatelive.distribution.response;

import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jinjianxia
 * @CreateTime: 2024/8/8 14:19
 * @Description:
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class QueryDistributorGroupForAnchorResponse implements Serializable {
    private Long totalNum;

    private List<DistributorGroup> distributorGroupList = new ArrayList<>();

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @ToString
    public static class DistributorGroup implements Serializable {
        private Long distributorGroupId;
        private String companyName;
        private String distributorGroupName;
        private Boolean bindStatus;
    }
}
