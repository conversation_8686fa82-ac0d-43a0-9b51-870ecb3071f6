package com.sankuai.carnation.distribution.groundpromotion.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.groundpromotion.dto.*;
import com.sankuai.carnation.distribution.promocode.privilege.dto.PageResponseDTO;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/10/19
 * @Description: 地推模板服务
 */
public interface GroundPromotionTemplateService {

    /**
     * 模糊查询模板列表
     * @param request
     * @return
     */
    PageResponseDTO<List<GroundPromotionTemplateInfoDTO>> search(GroundPromotionTemplateQueryRequest request);

    /**
     * 根据模板id查询模板详情
     * @param templateId
     * @return
     */
    RemoteResponse<GroundPromotionTemplateDTO> queryByTemplateId(long templateId);

    /**
     * 编辑模板
     * @param groundPromotionTemplateDTO
     * @return
     */
    RemoteResponse<Boolean> edit(GroundPromotionTemplateDTO groundPromotionTemplateDTO);

    /**
     * 新建模板
     * @param groundPromotionTemplateDTO
     * @return
     */
    RemoteResponse<Boolean> insert(GroundPromotionTemplateDTO groundPromotionTemplateDTO);

    /**
     * 删除模板
     * @param templateId
     * @return
     */
    RemoteResponse<Boolean> delete(long templateId);

    void loadAndParseStandardProductExcel(String fileKey, String url);

    RemoteResponse<GroundPromotionStandardProductExcelInfoDTO> getStandardProductExcelParseResult(String fileKey);

    RemoteResponse<List<GroundPromotionStandardProductTemplateDTO>> queryStandardProductByTemplateId(long templateId);


}
