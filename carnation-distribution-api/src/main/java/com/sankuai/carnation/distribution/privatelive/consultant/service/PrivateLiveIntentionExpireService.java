package com.sankuai.carnation.distribution.privatelive.consultant.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveIntentionExpireTimeDTO;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/1/4
 * @Description:
 */
public interface PrivateLiveIntentionExpireService {

    RemoteResponse<PrivateLiveIntentionExpireTimeDTO> queryByLiveId(String liveId);

    RemoteResponse<Boolean> init(String liveId);
}
