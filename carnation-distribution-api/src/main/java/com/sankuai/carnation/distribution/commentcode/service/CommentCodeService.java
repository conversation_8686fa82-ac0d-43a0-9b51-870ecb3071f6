package com.sankuai.carnation.distribution.commentcode.service;

import com.sankuai.carnation.distribution.commentcode.dto.CommentCodeDTO;

/**
 * 功能描述:开店宝点评码生成
 *
 * <AUTHOR>
 * @date 2023/06/27
 **/
public interface CommentCodeService {

    CommentCodeDTO produceCode(Long dpShopId);

    /**
     * 生成动态二维码数据
     * @param dpShopId 点评码主键
     */
    CommentCodeDTO produceDynamicCode(Long dpShopId);

}
