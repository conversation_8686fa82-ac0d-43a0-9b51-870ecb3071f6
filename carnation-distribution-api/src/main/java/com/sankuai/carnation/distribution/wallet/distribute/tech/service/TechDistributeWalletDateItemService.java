package com.sankuai.carnation.distribution.wallet.distribute.tech.service;

import com.sankuai.carnation.distribution.wallet.fundamental.dto.WalletDataDTO;

import java.util.List;

public interface TechDistributeWalletDateItemService {

    /**
     * 根据统一订单id查询wallet_data_item
     * @param unifiedOrderId
     * @return
     */
    List<WalletDataDTO> getWalletDataByUnifiedOrderId(String unifiedOrderId);
}
