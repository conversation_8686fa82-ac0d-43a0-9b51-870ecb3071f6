package com.sankuai.carnation.distribution.privatelive.consultant.dto.account;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeChatUserInfoResponse implements Serializable {
    /**
     * name = "openId",
     * description = "小程序openId"
     */
    private String openId;

    /**
     *  name = "unionId",
     *  description = "小程序unionId"
     */
    private String unionId;
}