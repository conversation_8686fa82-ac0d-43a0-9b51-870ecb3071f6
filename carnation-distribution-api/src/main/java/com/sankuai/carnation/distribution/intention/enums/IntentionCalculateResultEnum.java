package com.sankuai.carnation.distribution.intention.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2022/04/21
 **/
@Getter
public enum IntentionCalculateResultEnum {

    UNKNOWN(0, "未知"),
    DISTRIBUTION_ORDER(1, "渠道分销单"),
    NOT_DISTRIBUTION_ORDER(2, "非分销单"),
    WAIT_FOR_CONFIRM(3, "待确定");

    private final int code;

    private final String desc;

    IntentionCalculateResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static IntentionCalculateResultEnum fromCode(int code) {
        for (IntentionCalculateResultEnum enumValue : IntentionCalculateResultEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }

    public static List<Integer> getFinishStatus() {
        return Lists.newArrayList(
                NOT_DISTRIBUTION_ORDER.getCode(),
                DISTRIBUTION_ORDER.getCode()
        );
    }
}
