<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.meituan.carnation</groupId>
        <artifactId>carnation-distribution-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>carnation-distribution-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>carnation-distribution-service</name>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.hotel</groupId>
                <artifactId>travel-cerberus</artifactId>
                <version>1.7.3</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.beautycontent</groupId>
                <artifactId>dzproduct-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.deal</groupId>
                <artifactId>deal-flow-api</artifactId>
                <version>1.0.22</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.inf.leaf</groupId>
                <artifactId>leaf-idl</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>pay-common</artifactId>
                <version>1.2.12</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.mpmkt.coupon</groupId>
                <artifactId>mkt-coupon-manage-common-api</artifactId>
                <version>0.3.3</version>
            </dependency>

            <dependency>
                <groupId>com.dianping</groupId>
                <artifactId>gmkt-coupon-common-api</artifactId>
                <version>1.3.95</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.mobile</groupId>
                <artifactId>adapter-platform-client</artifactId>
                <version>1.0.15</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.meituan.pay</groupId>
            <artifactId>mwalletProxySdk</artifactId>
            <version>1.4.70-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-distribution-settle-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.pamela</groupId>
            <artifactId>pamela-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-shopcateprop-api</artifactId>
            <version>0.5.7</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.22</version>
        </dependency>

        <!-- mdp starter -->
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <!-- mdp starter end -->

        <!-- tools start -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>dzproduct-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-flow-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <version>1.1.16</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.medicine</groupId>
            <artifactId>medicine-exception-utils</artifactId>
            <version>1.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.conch.certify</groupId>
            <artifactId>tokenAccessSdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.7</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-maven-plugin</artifactId>
            <version>1.3.7</version>
        </dependency>

        <dependency>
            <groupId>com.itfsw</groupId>
            <artifactId>mybatis-generator-plugin</artifactId>
            <version>1.3.8</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.11</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lz4</artifactId>
                    <groupId>net.jpountz.lz4</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
            <version>1.0.65</version>
        </dependency>


        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
            <version>0.0.87</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-fundamental-threadpool</artifactId>
            <version>0.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.cip</groupId>
            <artifactId>cip-growth-acquisition-api</artifactId>
            <version>1.2.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.dts</groupId>
            <artifactId>dtsUtils</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- tools end -->

        <dependency>
            <groupId>com.meituan.carnation</groupId>
            <artifactId>carnation-distribution-api</artifactId>
            <version>${carnation-distribution-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-beautibot-api</artifactId>
            <version>1.4.7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-zone-remote-api</artifactId>
            <version>2.1.10.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
            <version>1.5.7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>udb-open-thrift</artifactId>
            <version>1.0.10</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.web</groupId>
            <artifactId>idl-common</artifactId>
            <version>1.6.7</version>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.7</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mobile</groupId>
            <artifactId>adapter-platform-client</artifactId>
        </dependency>

        <!-- 商品服务 -->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-shop-api</artifactId>
            <version>2.3.11</version>
        </dependency>

        <!--价格服务-->
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <version>0.0.18</version>
        </dependency>

        <!-- 标品信息查询 -->
        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>dzviewscene-poisummary-api</artifactId>
            <version>0.0.16</version>
        </dependency>
        <!-- 标品信息查询 end -->
        <!-- 标品信息填充 -->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.hotel</groupId>
            <artifactId>travel-cerberus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>

        <!-- 短链服务 start -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mobile-oss-api</artifactId>
            <version>1.1.3.44</version>

            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 短链服务 end -->

        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-biz-api</artifactId>
            <version>2.9.11</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>technician-vc-api</artifactId>
            <version>1.18.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>ugc-review-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--二维码-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.3.0</version>
        </dependency>
        <!--二维码-->

        <dependency>
            <groupId>com.dianping.open</groupId>
            <artifactId>open-picture-utils</artifactId>
            <version>1.1.12</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-user</artifactId>
            <version>4.0.1</version>
        </dependency>

        <!-- 美团联盟 -->
        <dependency>
            <groupId>com.sankuai.cps.union.datacenter</groupId>
            <artifactId>unionbase</artifactId>
            <version>0.8.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.10</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 美团联盟 end -->

        <!-- 到综订单 -->
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-service</artifactId>
            <version>2.4.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mptrade</groupId>
            <artifactId>groupbuy-common</artifactId>
            <version>2.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.product.dztg</groupId>
                    <artifactId>product-dztg-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.product.dztg</groupId>
            <artifactId>product-dztg-model</artifactId>
            <version>1.4.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xmd-log4j2</artifactId>
                    <groupId>com.meituan.inf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 到综订单 end -->
        <!-- bcp 对账 -->
        <dependency>
            <groupId>com.dianping.frog</groupId>
            <artifactId>frog-sdk</artifactId>
            <version>1.3.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poros-high-level-client</artifactId>
                    <groupId>com.sankuai.meituan</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- bcp 对账 end -->
        <!-- id映射工具包 -->
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-idmapper</artifactId>
            <version>5.0.3</version>
        </dependency>
        <!-- id映射工具包 end -->
        <!-- 手艺人交易通用工具包 -->
        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-trade-common</artifactId>
            <version>0.0.6</version>
        </dependency>
        <!-- 手艺人交易通用工具包 end -->
        <!-- crow -->
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-fundamental-crow-client</artifactId>
            <version>3.0.13</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- crow end -->
        <!-- talos -->
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-talos</artifactId>
            <version>2.0.1</version>
        </dependency>
        <!-- talos end -->
        <!-- 商品服务 -->
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-api</artifactId>
            <version>2.7.40</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 商品服务 end -->
        <!-- 抵用券 -->
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-open-remote</artifactId>
            <version>0.1.59</version>
            <exclusions>
                <exclusion>
                    <artifactId>swallow-consumerclient</artifactId>
                    <groupId>com.dianping.swallow</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-entity</artifactId>
            <version>0.2.98</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-issue-api</artifactId>
            <version>0.0.67</version>
        </dependency>
        <!-- 抵用券 end -->

        <!-- 鉴权相关的lib包 -->
        <dependency>
            <groupId>com.sankuai.nibmp.infra</groupId>
            <artifactId>amp-authenticate-lib</artifactId>
            <version>1.0.65</version><!--最新包请到maven仓库查阅-->
        </dependency>

        <!-- 鉴权公共包，可以单独引入 -->
        <dependency>
            <groupId>com.sankuai.nibmp.infra</groupId>
            <artifactId>amp-common</artifactId>
            <version>1.0.79</version><!--最新包请到maven仓库查阅-->
        </dependency>

        <!-- 团单服务 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-base-api</artifactId>
            <version>2.0.16</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-voucher-query-api</artifactId>
            <version>1.0.8</version>
        </dependency>
        <!-- 团单服务 end -->
        <!-- 创券服务 -->
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-management-remote</artifactId>
            <version>0.2.78</version>
        </dependency>
        <!-- 创券服务 end -->

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-search-api</artifactId>
            <version>0.5.3</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-marketing-investment-api</artifactId>
            <version>0.4.5</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-meta-tag-manage-api</artifactId>
            <version>1.0.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.24</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-event-manage-api</artifactId>
            <version>2.7.65</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.nibmp.infra</groupId>
            <artifactId>amp-attribute-lib</artifactId>
            <version>1.0.75</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-event-datapools-api</artifactId>
            <version>2.1.26</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mpmkt</groupId>
            <artifactId>mkt-data-pool-api</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-common</artifactId>
            <version>1.2.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>carnation-campaign-api</artifactId>
            <version>3.2.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>xmlbeans</artifactId>
                    <groupId>org.apache.xmlbeans</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.45</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.medicalcosmetology</groupId>
            <artifactId>product-selectify-api</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gis-api</artifactId>
            <version>1.3.19</version>
        </dependency>
        <!--城市服务-->
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-sakura-api</artifactId>
            <version>1.4.58</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mafka-kafka_2.9.2</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--招商系统-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>gmm-investment-activity-rule-api</artifactId>
            <version>2.0.12</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>beauty-launch-api</artifactId>
            <version>0.1.37</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-publish-api</artifactId>
            <version>1.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
            <version>1.8.98</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-admin-api</artifactId>
            <version>1.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-proxy-api</artifactId>
            <version>1.6.6</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
            <version>0.8.10</version>
        </dependency>

        <!-- 解析excel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.4</version>
        </dependency>

        <!-- 数仓swan -->
        <dependency>
            <groupId>com.sankuai.swan.udqs</groupId>
            <artifactId>Swan-udqs-api</artifactId>
            <version>1.6.0</version>
        </dependency>

        <!-- org服务 -->
        <dependency>
            <groupId>com.sankuai.meituan.org</groupId>
            <artifactId>open-sdk</artifactId>
            <version>5.0.8</version>
        </dependency>

        <!-- 团购券查询服务 -->
        <dependency>
            <groupId>com.dianping.receipt</groupId>
            <artifactId>rs-receipt-query-api</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>rs-receipt-record-api</artifactId>
            <version>1.1.5</version>
        </dependency>

        <!-- 优惠券 -->
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-query-api</artifactId>
            <version>0.0.89</version>
        </dependency>

        <!-- 商品货架服务 -->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-query-api</artifactId>
            <version>1.5.20</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
            <version>1.4.12</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 满天星打款 -->
        <dependency>
            <groupId>com.meituan.flexiblework</groupId>
            <artifactId>flexiblework_salary_client</artifactId>
            <version>1.0.43</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm-all</artifactId>
                    <groupId>asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 满天星打款 end -->
        <!-- 次卡 -->
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>timescard-exposure-api</artifactId>
            <version>0.2.1</version>
        </dependency>
        <!-- 次卡 end -->
        <dependency>
            <groupId>com.meituan.medicine</groupId>
            <artifactId>medical-excel-utils</artifactId>
            <version>0.1.7</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.nibtp</groupId>
            <artifactId>trade-client</artifactId>
            <version>1.10.38</version>
        </dependency>
        <!-- 餐地推系统 -->
        <dependency>
            <groupId>com.sankuai.foodtrade.rebate.qrcode</groupId>
            <artifactId>rebate-qr-api</artifactId>
            <version>0.0.44</version>
        </dependency>
        <!-- 餐地推系统 end -->

        <!-- 团购交易事件消息-->
        <dependency>
            <groupId>com.dianping.dztrade</groupId>
            <artifactId>dztrade-common-light</artifactId>
            <version>2.1.3</version>
        </dependency>
        <!-- 团购交易事件消息 end -->

        <!-- 泛商品交易事件消息 -->
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 泛商品交易事件消息 end-->

        <!-- 泛商品券基础服务 -->
        <dependency>
            <groupId>com.dianping.coupon</groupId>
            <artifactId>dz-coupon-base-api</artifactId>
            <version>1.2.5</version>
        </dependency>
        <!-- 泛商品券基础服务 end -->

        <!-- 泛商品券查询服务 -->
        <dependency>
            <groupId>com.dianping.coupon</groupId>
            <artifactId>dz-coupon-query-api</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- 泛商品券查询服务 end -->

        <!-- 查询订单分摊金额 -->
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>unifiedorder-onlinequery-api</artifactId>
            <version>1.7.9</version>
        </dependency>
        <!-- 查询订单分摊金额 end -->

        <dependency>
            <groupId>com.dianping.swallow</groupId>
            <artifactId>swallow-consumerclient</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 退款事件 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>refund-platform-api</artifactId>
            <version>0.2.6</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>refund-platform-application-api</artifactId>
            <version>2.0.10</version>
        </dependency>
        <!-- 退款事件 end-->

        <!-- USS新客服务 -->
        <dependency>
            <groupId>com.meituan.data</groupId>
            <artifactId>uss_proto</artifactId>
            <version>2.5.0</version>
        </dependency>
        <!-- USS新客服务 end-->

        <!-- 商家账号映射 -->
        <dependency>
            <groupId>com.sankuai.sjst.ecom</groupId>
            <artifactId>epassport-dpadapter-client</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!-- 商家账号映射 end -->

        <!-- 私域直播主播 -->
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-operation-api</artifactId>
            <version>1.0.12</version>
        </dependency>
        <!-- 私域直播主播 -->

        <!-- 私域直播直播间 -->
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-biz-api</artifactId>
            <version>1.0.74</version>
        </dependency>
        <!-- 私域直播直播间 -->

        <!-- 主播sdk -->
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-auth-sdk</artifactId>
            <version>1.0.8</version>
        </dependency>
        <!-- 主播sdk -->

        <dependency>
            <groupId>com.sankuai.technician</groupId>
            <artifactId>technician-trade-api</artifactId>
            <version>0.5.15</version>
        </dependency>
        <!-- 地推落地社群卷 -->
        <dependency>
            <groupId>com.sankuai.scrm</groupId>
            <artifactId>scrm-core-api</artifactId>
            <version>2.0.40</version>
        </dependency>
        <!-- 地推落地社群卷 end-->
        <!--微信零钱发放 -->
        <dependency>
            <groupId>com.meituan.payment.fundstransfer</groupId>
            <artifactId>thrift</artifactId>
            <version>0.8.43</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.10</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--微信零钱发放 end -->
        <!-- 到综-医疗企微返现 -->
        <dependency>
            <groupId>com.sankuai.nibmsc</groupId>
            <artifactId>msc-deliver-api</artifactId>
            <version>0.4.26</version>
        </dependency>
        <!--到综-医疗企微返现 end -->
        <!-- 销售，门店业务线 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>rotate-territory-api</artifactId>
            <version>3.8.14</version>
        </dependency>
        <!-- 销售，门店业务线 end -->
        <dependency>
            <groupId>com.sankuai.medicalcosmetology</groupId>
            <artifactId>offline-code-api</artifactId>
            <version>1.0.26</version>
        </dependency>

        <!-- 订单更新扩展字段 -->
        <dependency>
            <groupId>com.meituan.mptrade.groupbuy.api</groupId>
            <artifactId>order-api</artifactId>
            <version>1.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-common-api</artifactId>
            <version>0.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tuangou-category-api</artifactId>
            <version>1.0.7</version>
        </dependency>

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.12</version>
        </dependency>

        <!-- 佣金 -->
        <dependency>
            <groupId>com.dianping.tssettle</groupId>
            <artifactId>zsts-commission-api</artifactId>
            <version>2.4.15</version>
        </dependency>

        <!-- 账号门店服务 -->
        <dependency>
            <groupId>com.sankuai.nibmp.infra</groupId>
            <artifactId>amp-authorize-lib</artifactId>
            <version>1.0.82</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>dzusergrowth-distribution-plan-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>growth-privatelive-api</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp</groupId>
            <artifactId>unity-distributed-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.10</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp</groupId>
            <artifactId>unity-migration-framework</artifactId>
        </dependency>
        <!-- Mockito核心依赖 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito内联模拟静态方法的依赖 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dianping.technician</groupId>
            <artifactId>technician-common-ddd</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.testapi</groupId>
            <artifactId>testapi-sdk</artifactId>
            <version>1.0.14</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>uac-common-sdk</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-fundamental-crow-api</artifactId>
            <version>3.0.3.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>test</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.0.7.RELEASE</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <layoutFactory
                                            implementation="com.sankuai.athena.boot.loader.AthenaLayoutFactory"></layoutFactory>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>athena-home</groupId>
                                <artifactId>athena-boot-loader</artifactId>
                                <version>1.0.1</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
