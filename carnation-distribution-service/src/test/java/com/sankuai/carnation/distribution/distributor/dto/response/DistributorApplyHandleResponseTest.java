package com.sankuai.carnation.distribution.distributor.dto.response;

import org.junit.Assert;
import org.junit.Test;

public class DistributorApplyHandleResponseTest {

    /**
     * 测试构造函数和属性赋值
     */
    @Test
    public void testConstructorAndProperties() {
        Long expectedDistributorId = 1L;
        Integer expectedStatus = 0;

        DistributorApplyHandleResponse response = new DistributorApplyHandleResponse(expectedDistributorId, expectedStatus);

        Assert.assertEquals("Distributor ID should match", expectedDistributorId, response.getDistributorId());
        Assert.assertEquals("Status should match", expectedStatus, response.getStatus());
    }

    /**
     * 测试setter和getter方法
     */
    @Test
    public void testSettersAndGetters() {
        DistributorApplyHandleResponse response = new DistributorApplyHandleResponse();
        Long expectedDistributorId = 2L;
        Integer expectedStatus = 1;

        response.setDistributorId(expectedDistributorId);
        response.setStatus(expectedStatus);

        Assert.assertEquals("Distributor ID should be set and get correctly", expectedDistributorId, response.getDistributorId());
        Assert.assertEquals("Status should be set and get correctly", expectedStatus, response.getStatus());
    }

    /**
     * 测试Builder模式
     */
    @Test
    public void testBuilder() {
        Long expectedDistributorId = 3L;
        Integer expectedStatus = 2;

        DistributorApplyHandleResponse response = DistributorApplyHandleResponse.builder()
                .distributorId(expectedDistributorId)
                .status(expectedStatus)
                .build();

        Assert.assertEquals("Distributor ID should be built correctly", expectedDistributorId, response.getDistributorId());
        Assert.assertEquals("Status should be built correctly", expectedStatus, response.getStatus());
    }
}
