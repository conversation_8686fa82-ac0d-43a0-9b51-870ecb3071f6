package com.sankuai.carnation.distribution.groundpromotion.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.beauty.clove.model.commonGoods.CouponInfo;
import com.dianping.gmkt.event.api.api.EventRpcService;
import com.dianping.gmkt.event.api.enums.EventErrorCode;
import com.dianping.gmkt.event.api.enums.EventException;
import com.dianping.gmkt.event.api.enums.UserPlatform;
import com.dianping.gmkt.event.api.v2.model.EventInfo;
import com.dianping.gmkt.event.api.v2.model.PigeonResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.groundpromotion.dto.DrawCouponRequest;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.ResourceDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.OperationCouponRequest;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeForCService;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionPageServiceImplDrawAndGetCouponInfosTest {

    @InjectMocks
    private GroundPromotionPageServiceImpl groundPromotionPageService;

    @Mock
    private PromoCodeForCService promoCodeForCService;

    @Mock
    private EventRpcService eventRpcService;

    private DrawCouponRequest drawCouponRequest;

    private Method method;

    @Before
    public void setUp() throws Exception {
        drawCouponRequest = new DrawCouponRequest();
        drawCouponRequest.setDpShopId(123L);
        drawCouponRequest.setPlatform(1);
        method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("drawAndGetCouponInfos", DrawCouponRequest.class);
        method.setAccessible(true);
    }

    /**
     * Test the scenario where the promoCodeForCService returns null.
     */
    @Test
    public void testDrawAndGetCouponInfosPromoCodeForCServiceReturnsNull() throws Throwable {
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(null);
        List<CouponInfo> result = (List<CouponInfo>) method.invoke(groundPromotionPageService, drawCouponRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test the scenario where the promoCodeForCService returns an unsuccessful response.
     */
    @Test
    public void testDrawAndGetCouponInfosPromoCodeForCServiceReturnsUnsuccessfulResponse() throws Throwable {
        RemoteResponse<OperationInfoDTO> remoteResponse = RemoteResponse.fail("Error");
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(remoteResponse);
        List<CouponInfo> result = (List<CouponInfo>) method.invoke(groundPromotionPageService, drawCouponRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test the scenario where the AwardInfoDTO is null.
     */
    @Test
    public void testDrawAndGetCouponInfosAwardInfoDTONull() throws Throwable {
        OperationInfoDTO operationInfoDTO = new OperationInfoDTO();
        ResourceDTO resourceDTO = new ResourceDTO();
        operationInfoDTO.setResourceConfig(resourceDTO);
        RemoteResponse<OperationInfoDTO> remoteResponse = RemoteResponse.success(operationInfoDTO);
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(remoteResponse);
        List<CouponInfo> result = (List<CouponInfo>) method.invoke(groundPromotionPageService, drawCouponRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test the scenario where the platform is invalid.
     */
    @Test
    public void testDrawAndGetCouponInfosInvalidPlatform() throws Throwable {
        try {
            // Set invalid platform
            drawCouponRequest.setPlatform(999);
            // Mock the service to throw EventException for invalid platform
            when(promoCodeForCService.queryOperationConfig(argThat(request -> request.getQrClientType() == 999))).thenThrow(new EventException(EventErrorCode.COUPON_Platform_NOT_Validate));
            method.invoke(groundPromotionPageService, drawCouponRequest);
            fail("Expected EventException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof EventException);
            EventException eventException = (EventException) e.getCause();
            assertEquals(EventErrorCode.COUPON_Platform_NOT_Validate, eventException.getErrorCode());
        }
    }
}
