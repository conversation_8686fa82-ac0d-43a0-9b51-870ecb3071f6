//package com.sankuai.carnation.distribution.privatelive.distribution.domain.model;
//
//import static org.junit.Assert.*;
//import static org.mockito.Mockito.*;
//import static org.mockito.Mockito.*;
//
//import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
//import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
//import com.sankuai.carnation.distribution.privatelive.distribution.config.PrivateLiveDistributionApplyConfig;
//import com.sankuai.carnation.distribution.privatelive.distribution.config.PrivateLiveDistributionConfigUtils;
//import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
//import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
//import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
//import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
//import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
//import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
//import com.sankuai.technician.ddd.aggregate.Aggregate;
//import com.sankuai.technician.ddd.aggregate.Aggregate;
//import org.junit.Before;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.Test;
//import org.mockito.InjectMocks;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.MockitoAnnotations;
//
///**
// * 测试PrivateLiveAnchorDistributorGroupBO的passAudit方法
// */
//public class PrivateLiveAnchorDistributorGroupBOTest {
//
//    @InjectMocks
//    private PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO;
//
//    @Mock
//    private PrivateLiveAnchorDistributorGroupContext context;
//
//    @Mock
//    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;
//
//    @Mock
//    private DistributorGroupAppService distributorGroupAppService;
//
//    @Mock
//    private Aggregate<PrivateLiveAnchorDistributorGroupBO> aggregateRoot;
//
//    private PrivateLiveDistributionApplyConfig configMock;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//    }
//
//    /**
//     * 测试状态为CANCELED时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testPassAuditWithStatusCanceled() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.CANCELED);
//        // act
//        privateLiveAnchorDistributorGroupBO.passAudit(context);
//        // assert 由expected处理
//    }
//
//    /**
//     * 测试状态为REJECT时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testPassAuditWithStatusReject() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.REJECT);
//        // act
//        privateLiveAnchorDistributorGroupBO.passAudit(context);
//        // assert 由expected处理
//    }
//
//    /**
//     * 测试状态为PASS时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testPassAuditWithStatusPass() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        // act
//        privateLiveAnchorDistributorGroupBO.passAudit(context);
//        // assert 由expected处理
//    }
//
//    /**
//     * 测试正常流程
//     */
//    @Test
//    public void testPassAuditSuccess() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.WAITING);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        when(context.getDistributorGroupAppService()).thenReturn(distributorGroupAppService);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        // act
//        privateLiveAnchorDistributorGroupBO.passAudit(context);
//        // assert
//        verify(distributorGroupAppService, times(1)).passAudit(any());
//        assert privateLiveAnchorDistributorGroupBO.getStatus() == DistributionApproveStatusEnum.PASS;
//    }
//
//    /**
//     * 测试状态为CANCELED时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testRejectAuditStatusCanceled() throws Throwable {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.CANCELED);
//        // act
//        privateLiveAnchorDistributorGroupBO.rejectAudit(context);
//        // assert
//        // BizSceneException expected
//    }
//
//    /**
//     * 测试状态为PASS时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testRejectAuditStatusPass() throws Throwable {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        // act
//        privateLiveAnchorDistributorGroupBO.rejectAudit(context);
//        // assert
//        // BizSceneException expected
//    }
//
//    /**
//     * 测试状态为REJECT时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testRejectAuditStatusReject() throws Throwable {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.REJECT);
//        // act
//        privateLiveAnchorDistributorGroupBO.rejectAudit(context);
//        // assert
//        // BizSceneException expected
//    }
//
//    /**
//     * PASS拒绝修改
//     */
//    @Test(expected = BizSceneException.class)
//    public void testRejectAuditStatusApply() throws Throwable {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        // act
//        privateLiveAnchorDistributorGroupBO.rejectAudit(context);
//    }
//
//    /**
//     * 测试 create 方法，正常情况
//     */
//    @Test
//    public void testCreateNormal() {
//        // arrange
//        PrivateLiveAnchorDistributorGroupContext context = mock(PrivateLiveAnchorDistributorGroupContext.class);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        when(privateLiveAnchorDistributorGroupRepository.save(any(Aggregate.class))).thenReturn(privateLiveAnchorDistributorGroupBO);
//        // act
//        PrivateLiveAnchorDistributorGroupBO result = privateLiveAnchorDistributorGroupBO.create(context);
//        // assert
//        //        verify(privateLiveAnchorDistributorGroupRepository, times(1)).save(any(PrivateLiveAnchorDistributorGroupBO.class));
//        assertNotNull(result);
//    }
//
//    /**
//     * 测试 create 方法，当 context 为 null 时抛出异常
//     */
//    @Test(expected = NullPointerException.class)
//    public void testCreateContextNull() {
//        // arrange
//        PrivateLiveAnchorDistributorGroupContext context = null;
//        // act
//        privateLiveAnchorDistributorGroupBO.create(context);
//        // assert 由于预期抛出异常，所以不需要写断言
//    }
//
//    /**
//     * 测试 create 方法，当 PrivateLiveAnchorDistributorGroupRepository 的 save 方法返回 null 时
//     */
//    @Test(expected = BizSceneException.class)
//    public void testCreateSaveReturnsNull() {
//        // arrange
//        PrivateLiveAnchorDistributorGroupContext context = mock(PrivateLiveAnchorDistributorGroupContext.class);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        //        when(privateLiveAnchorDistributorGroupRepository.save(any(PrivateLiveAnchorDistributorGroupBO.class))).thenReturn(null);
//        // act
//        PrivateLiveAnchorDistributorGroupBO result = privateLiveAnchorDistributorGroupBO.create(context);
//    }
//
//    /**
//     * 测试 create 方法，当 PrivateLiveAnchorDistributorGroupRepository 的 save 方法抛出异常时
//     */
//    @Test(expected = RuntimeException.class)
//    public void testCreateSaveThrowsException() {
//        // arrange
//        PrivateLiveAnchorDistributorGroupContext context = mock(PrivateLiveAnchorDistributorGroupContext.class);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        //        when(privateLiveAnchorDistributorGroupRepository.save(any(PrivateLiveAnchorDistributorGroupBO.class))).thenThrow(new RuntimeException());
//        // act
//        privateLiveAnchorDistributorGroupBO.create(context);
//        // assert 由于预期抛出异常，所以不需要写断言
//    }
//
//    /**
//     * 测试 logStatusDesc 方法，当 status 为 null 时
//     */
//    @Test
//    public void testLogStatusDescStatusNull() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(null);
//        // act
//        privateLiveAnchorDistributorGroupBO.logStatusDesc();
//        // assert
//        assertEquals(DistributionApproveStatusEnum.UN_APPLY, privateLiveAnchorDistributorGroupBO.getStatus());
//        assertNotNull(privateLiveAnchorDistributorGroupBO.getStatusDesc());
//    }
//
//    /**
//     * 测试 logStatusDesc 方法，当 status 为 WAITING 时
//     */
//    @Test
//    public void testLogStatusDescStatusWaiting() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.WAITING);
//        // act
//        privateLiveAnchorDistributorGroupBO.logStatusDesc();
//        // assert
//        assertEquals("您已提交申请，请耐心等待结果", privateLiveAnchorDistributorGroupBO.getStatusDesc());
//    }
//
//    /**
//     * 测试 logStatusDesc 方法，当 status 为 PASS 时
//     */
//    @Test
//    public void testLogStatusDescStatusPass() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        // act
//        privateLiveAnchorDistributorGroupBO.logStatusDesc();
//        // assert
//        assertEquals("您已成功申请，请勿重复申请", privateLiveAnchorDistributorGroupBO.getStatusDesc());
//    }
//
//    /**
//     * 测试 logStatusDesc 方法，当 status 为 REJECT 时
//     */
//    @Test
//    public void testLogStatusDescStatusReject() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.REJECT);
//        // act
//        privateLiveAnchorDistributorGroupBO.logStatusDesc();
//        // assert
//        assertEquals("您的审核未通过，请联系工作人员后重新申请", privateLiveAnchorDistributorGroupBO.getStatusDesc());
//    }
//
//    /**
//     * 测试当状态为WAITING时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testInvalidStatusWaitingThrowsBizSceneException() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.WAITING);
//        // act
//        privateLiveAnchorDistributorGroupBO.invalid(context);
//        // assert is handled by the expected exception
//    }
//
//    /**
//     * 测试当状态不为WAITING时，状态被设置为CANCELED，并且调用repository的save方法
//     */
//    @Test
//    public void testInvalidStatusNotWaitingSetsStatusToCanceledAndSaves() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        // act
//        privateLiveAnchorDistributorGroupBO.invalid(context);
//        // assert
//        verify(privateLiveAnchorDistributorGroupRepository, times(1)).save(aggregateRoot);
//        assert (privateLiveAnchorDistributorGroupBO.getStatus() == DistributionApproveStatusEnum.CANCELED);
//    }
//
//    /**
//     * 测试当状态为CANCELED时，状态被设置为CANCELED，并且调用repository的save方法
//     */
//    @Test
//    public void testInvalidStatusCanceledSetsStatusToCanceledAndSaves() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.CANCELED);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        // act
//        privateLiveAnchorDistributorGroupBO.invalid(context);
//        // assert
//        verify(privateLiveAnchorDistributorGroupRepository, times(1)).save(aggregateRoot);
//        assert (privateLiveAnchorDistributorGroupBO.getStatus() == DistributionApproveStatusEnum.CANCELED);
//    }
//
//    /**
//     * 测试当状态为REJECTED时，状态被设置为CANCELED，并且调用repository的save方法
//     */
//    @Test
//    public void testInvalidStatusRejectedSetsStatusToCanceledAndSaves() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.REJECT);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        // act
//        privateLiveAnchorDistributorGroupBO.invalid(context);
//        // assert
//        verify(privateLiveAnchorDistributorGroupRepository, times(1)).save(aggregateRoot);
//        assert (privateLiveAnchorDistributorGroupBO.getStatus() == DistributionApproveStatusEnum.CANCELED);
//    }
//
//    /**
//     * 测试当状态为APPROVED时，状态被设置为CANCELED，并且调用repository的save方法
//     */
//    @Test
//    public void testInvalidStatusApprovedSetsStatusToCanceledAndSaves() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        // act
//        privateLiveAnchorDistributorGroupBO.invalid(context);
//        // assert
//        verify(privateLiveAnchorDistributorGroupRepository, times(1)).save(aggregateRoot);
//        assert (privateLiveAnchorDistributorGroupBO.getStatus() == DistributionApproveStatusEnum.CANCELED);
//    }
//
//    /**
//     * 测试状态为WAITING时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testReRegisterStatusWaiting() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.WAITING);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        // act
//        privateLiveAnchorDistributorGroupBO.reRegister(context);
//        // assert is handled by the expected exception
//    }
//
//    /**
//     * 测试状态为PASS时抛出BizSceneException异常
//     */
//    @Test(expected = BizSceneException.class)
//    public void testReRegisterStatusPass() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        // act
//        privateLiveAnchorDistributorGroupBO.reRegister(context);
//        // assert is handled by the expected exception
//    }
//
//    /**
//     * 测试状态为REJECT时，状态变更为WAITING并保存
//     */
//    @Test
//    public void testReRegisterStatusReject() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.REJECT);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        when(context.getPrivateLiveAnchorDistributorGroupRepository()).thenReturn(privateLiveAnchorDistributorGroupRepository);
//        // act
//        privateLiveAnchorDistributorGroupBO.reRegister(context);
//        // assert
//        verify(privateLiveAnchorDistributorGroupRepository, times(1)).save(aggregateRoot);
//        assert (privateLiveAnchorDistributorGroupBO.getStatus() == DistributionApproveStatusEnum.WAITING);
//    }
//
//    /**
//     * 测试其他状态时，不抛出异常也不保存
//     */
//    @Test
//    public void testReRegisterOtherStatus() {
//        // arrange
//        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.UN_APPLY);
//        when(context.getAggregateRoot()).thenReturn(aggregateRoot);
//        // act
//        privateLiveAnchorDistributorGroupBO.reRegister(context);
//        // assert
//        verify(privateLiveAnchorDistributorGroupRepository, never()).save(any(Aggregate.class));
//    }
//}
