package com.sankuai.carnation.distribution.distributor.appication.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributor.appication.impl.DistributorAppServiceImpl;
import com.sankuai.carnation.distribution.distributor.assembler.DistributorVOAssembler;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBindBO;
import com.sankuai.carnation.distribution.distributor.domain.service.DistributorDomainService;
import com.sankuai.carnation.distribution.distributor.dto.request.DistributorApplyHandleRequest;
import com.sankuai.carnation.distribution.distributor.dto.request.PageQueryDistributorRequest;
import com.sankuai.carnation.distribution.distributor.dto.response.DistributorApplyHandleResponse;
import com.sankuai.carnation.distribution.distributor.enums.DistributorBindUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.repository.DistributorRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.utils.NullableUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DistributorAppServiceImplTest {

    @InjectMocks
    private DistributorAppServiceImpl distributorAppService;

    @Mock
    private DistributorRepository distributorRepository;

    @Mock
    private DistributorVOAssembler distributorVOAssembler;

    @Mock
    private DistributorDomainService distributorDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试pageQueryDistributor方法，当传入的请求导致返回空列表时
     */
    @Test(expected = NullPointerException.class)
    public void testPageQueryDistributorReturnsEmptyList() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        PageDataDTO<DistributorBindBO> pageDataDTO = new PageDataDTO<>(new PageInfoDTO(), Collections.emptyList());
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(pageDataDTO);
        PageDataDTO<DistributorBindModel> result = distributorAppService.pageQueryDistributor(request);
        assertNotNull("结果不应为null", result);
        assertTrue("列表应为空", result.getList().isEmpty());
        verify(distributorRepository, times(1)).pageQueryDistributor(request);
        verify(distributorVOAssembler, never()).buildDistributorBindModel(any(DistributorBindBO.class));
    }

    /**
     * 测试pageQueryDistributor方法，当传入的请求导致返回非空列表时
     */
    @Test(expected = NullPointerException.class)
    public void testPageQueryDistributorReturnsNonEmptyList() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        DistributorBindBO distributorBindBO = new DistributorBindBO();
        PageDataDTO<DistributorBindBO> pageDataDTO = new PageDataDTO<>(new PageInfoDTO(), Arrays.asList(distributorBindBO));
        DistributorBindModel distributorBindModel = new DistributorBindModel();
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(pageDataDTO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenReturn(distributorBindModel);
        PageDataDTO<DistributorBindModel> result = distributorAppService.pageQueryDistributor(request);
        assertNotNull("结果不应为null", result);
        assertFalse("列表不应为空", result.getList().isEmpty());
        assertEquals("列表大小应为1", 1, result.getList().size());
        assertSame("列表中的元素应与预期相同", distributorBindModel, result.getList().get(0));
        verify(distributorRepository, times(1)).pageQueryDistributor(request);
        verify(distributorVOAssembler, times(1)).buildDistributorBindModel(distributorBindBO);
    }


    /**
     * 测试pageQueryDistributor方法，当distributorRepository返回null时
     */
    @Test(expected = NullPointerException.class)
    public void testPageQueryDistributorWhenRepositoryReturnsNull() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(null);
        distributorAppService.pageQueryDistributor(request);
    }

    /**
     * 测试pageQueryDistributor方法，当distributorRepository抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testPageQueryDistributorWhenRepositoryThrowsException() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        when(distributorRepository.pageQueryDistributor(request)).thenThrow(new RuntimeException());
        distributorAppService.pageQueryDistributor(request);
    }

    /**
     * 测试pageQueryDistributor方法，当distributorVOAssembler抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testPageQueryDistributorWhenAssemblerThrowsException() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        DistributorBindBO distributorBindBO = new DistributorBindBO();
        PageDataDTO<DistributorBindBO> pageDataDTO = new PageDataDTO<>(new PageInfoDTO(), Arrays.asList(distributorBindBO));
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(pageDataDTO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenThrow(new RuntimeException());
        distributorAppService.pageQueryDistributor(request);
    }






    /**
     * 测试queryAllByDistributorGroupId方法，当分销商组ID有效且有多页数据返回时
     */
    @Test
    public void testQueryAllByDistributorGroupIdWithMultiplePageData() {
        Long distributorGroupId = 1L;
        int pageSize = 20;
        PageQueryDistributorRequest request = PageQueryDistributorRequest.builder().distributorGroupId(distributorGroupId).pageNum(1).pageSize(pageSize).build();
        DistributorBindBO distributorBindBO = new DistributorBindBO();
        List<DistributorBindBO> distributorBindBOs = new ArrayList<>(Collections.nCopies(pageSize, distributorBindBO));
        PageDataDTO<DistributorBindBO> pageDataDTO = new PageDataDTO<>(null, distributorBindBOs);
        when(distributorRepository.pageQueryDistributor(any(PageQueryDistributorRequest.class))).thenReturn(pageDataDTO).thenReturn(new PageDataDTO<>(null, new ArrayList<>()));
        DistributorBindModel distributorBindModel = new DistributorBindModel();
        when(distributorVOAssembler.buildDistributorBindModel(any(DistributorBindBO.class))).thenReturn(distributorBindModel);
        List<DistributorBindModel> result = distributorAppService.queryAllByDistributorGroupId(distributorGroupId);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(pageSize, result.size());
        verify(distributorRepository, times(2)).pageQueryDistributor(any(PageQueryDistributorRequest.class));
        verify(distributorVOAssembler, times(pageSize)).buildDistributorBindModel(distributorBindBO);
    }



    /**
     * 测试 queryDistributorById 方法，当传入的 distributorId 对应的 DistributorBO 不存在时
     */
    @Test
    public void testQueryDistributorByIdWhenDistributorBONotExists() {
        // arrange
        Long distributorId = 1L;
        when(distributorRepository.queryByDistributorId(distributorId)).thenReturn(null);
        // act
        DistributorModel result = distributorAppService.queryDistributorById(distributorId);
        // assert
        assertNull(result);
        verify(distributorRepository).queryByDistributorId(distributorId);
        verify(distributorVOAssembler, never()).buildDistributorModel(any(DistributorBO.class));
    }

    /**
     * 测试 queryDistributorById 方法，当传入的 distributorId 对应的 DistributorBO 存在时
     */
    @Test
    public void testQueryDistributorByIdWhenDistributorBOExists() {
        // arrange
        Long distributorId = 1L;
        DistributorBO distributorBO = mock(DistributorBO.class);
        DistributorModel expectedModel = mock(DistributorModel.class);
        when(distributorRepository.queryByDistributorId(distributorId)).thenReturn(distributorBO);
        when(distributorVOAssembler.buildDistributorModel(distributorBO)).thenReturn(expectedModel);
        // act
        DistributorModel result = distributorAppService.queryDistributorById(distributorId);
        // assert
        assertNotNull(result);
        assertEquals(expectedModel, result);
        verify(distributorRepository).queryByDistributorId(distributorId);
        verify(distributorVOAssembler).buildDistributorModel(distributorBO);
    }


    /**
     * 测试 queryDistributorById 方法，当 DistributorBO 存在但转换为 DistributorModel 时返回 null
     */
    @Test
    public void testQueryDistributorByIdWhenDistributorBOExistsButModelIsNull() {
        // arrange
        Long distributorId = 2L;
        DistributorBO distributorBO = mock(DistributorBO.class);
        when(distributorRepository.queryByDistributorId(distributorId)).thenReturn(distributorBO);
        when(distributorVOAssembler.buildDistributorModel(distributorBO)).thenReturn(null);
        // act
        DistributorModel result = distributorAppService.queryDistributorById(distributorId);
        // assert
        assertNull(result);
        verify(distributorRepository).queryByDistributorId(distributorId);
        verify(distributorVOAssembler).buildDistributorModel(distributorBO);
    }

    /**
     * 测试queryBindByDistributorId方法，当distributorId对应的DistributorBindBO为空时
     */
    @Test
    public void testQueryBindByDistributorId_WhenDistributorBindBOIsNull() {
        // arrange
        Long distributorId = 1L;
        when(distributorRepository.queryBindByDistributorId(distributorId)).thenReturn(null);
        // act
        DistributorBindModel result = distributorAppService.queryBindByDistributorId(distributorId);
        // assert
        assertNull(result);
        verify(distributorRepository, times(1)).queryBindByDistributorId(distributorId);
        verify(distributorVOAssembler, never()).buildDistributorBindModel(any(DistributorBindBO.class));
    }

    /**
     * 测试queryBindByDistributorId方法，当distributorId对应的DistributorBindBO不为空时
     */
    @Test
    public void testQueryBindByDistributorId_WhenDistributorBindBOIsNotNull() {
        // arrange
        Long distributorId = 1L;
        DistributorBindBO distributorBindBO = mock(DistributorBindBO.class);
        DistributorBindModel expectedModel = mock(DistributorBindModel.class);
        when(distributorRepository.queryBindByDistributorId(distributorId)).thenReturn(distributorBindBO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenReturn(expectedModel);
        // act
        DistributorBindModel result = distributorAppService.queryBindByDistributorId(distributorId);
        // assert
        assertNotNull(result);
        assertEquals(expectedModel, result);
        verify(distributorRepository, times(1)).queryBindByDistributorId(distributorId);
        verify(distributorVOAssembler, times(1)).buildDistributorBindModel(distributorBindBO);
    }


    /**
     * 测试queryBindByDistributorId方法，当DistributorVOAssembler转换过程中抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryBindByDistributorId_WhenAssemblerThrowsException() {
        // arrange
        Long distributorId = 1L;
        DistributorBindBO distributorBindBO = mock(DistributorBindBO.class);
        when(distributorRepository.queryBindByDistributorId(distributorId)).thenReturn(distributorBindBO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenThrow(new RuntimeException());
        // act
        distributorAppService.queryBindByDistributorId(distributorId);
        // assert
        // RuntimeException is expected to be thrown
    }

    /**
     * 测试 queryDistributorByAccountId 方法，当传入的 accountId 对应的 DistributorBindBO 不存在时
     */
    @Test
    public void testQueryDistributorByAccountIdWhenDistributorBindBONotExist() {
        Long accountId = 1L;
        when(distributorRepository.queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId)).thenReturn(null);
        DistributorBindModel result = distributorAppService.queryDistributorByAccountId(accountId);
        assertNull(result);
        verify(distributorRepository, times(1)).queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId);
    }

    /**
     * 测试 queryDistributorByAccountId 方法，当传入的 accountId 对应的 DistributorBindBO 存在时
     */
    @Test
    public void testQueryDistributorByAccountIdWhenDistributorBindBOExist() {
        Long accountId = 1L;
        DistributorBindBO distributorBindBO = new DistributorBindBO();
        distributorBindBO.setDistributorId(accountId);
        distributorBindBO.setStatus(DistributionApproveStatusEnum.PASS);
        DistributorBindModel expectedModel = new DistributorBindModel();
        expectedModel.setDistributorId(accountId);
        expectedModel.setStatus(DistributionApproveStatusEnum.PASS);
        when(distributorRepository.queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId)).thenReturn(distributorBindBO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenReturn(expectedModel);
        DistributorBindModel result = distributorAppService.queryDistributorByAccountId(accountId);
        assertNotNull(result);
        assertEquals(expectedModel.getDistributorId(), result.getDistributorId());
        assertEquals(expectedModel.getStatus(), result.getStatus());
        verify(distributorRepository, times(1)).queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId);
        verify(distributorVOAssembler, times(1)).buildDistributorBindModel(distributorBindBO);
    }



    /**
     * 测试 queryDistributorByAccountId 方法，当 DistributorBindBO 存在但 DistributorVOAssembler 返回 null 时
     */
    @Test
    public void testQueryDistributorByAccountIdWhenAssemblerReturnsNull() {
        Long accountId = 2L;
        DistributorBindBO distributorBindBO = new DistributorBindBO();
        distributorBindBO.setDistributorId(accountId);
        distributorBindBO.setStatus(DistributionApproveStatusEnum.PASS);
        when(distributorRepository.queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId)).thenReturn(distributorBindBO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenReturn(null);
        DistributorBindModel result = distributorAppService.queryDistributorByAccountId(accountId);
        assertNull(result);
        verify(distributorRepository, times(1)).queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId);
        verify(distributorVOAssembler, times(1)).buildDistributorBindModel(distributorBindBO);
    }

    /**
     * 测试 queryDistributorByAccountId 方法，当 DistributorBindBO 存在且 DistributorVOAssembler 抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryDistributorByAccountIdWhenAssemblerThrowsException() {
        Long accountId = 3L;
        DistributorBindBO distributorBindBO = new DistributorBindBO();
        distributorBindBO.setDistributorId(accountId);
        distributorBindBO.setStatus(DistributionApproveStatusEnum.PASS);
        when(distributorRepository.queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), accountId)).thenReturn(distributorBindBO);
        when(distributorVOAssembler.buildDistributorBindModel(distributorBindBO)).thenThrow(new RuntimeException("Assembler error"));
        distributorAppService.queryDistributorByAccountId(accountId);
    }

    /**
     * 测试 queryAllByDistributorGroupIds 方法，当传入的 distributorGroupIds 为空时
     */
    @Test
    public void testQueryAllByDistributorGroupIds_EmptyList() {
        List<DistributorModel> result = distributorAppService.queryAllByDistributorGroupIds(Collections.emptyList());
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 queryAllByDistributorGroupIds 方法，当传入的 distributorGroupIds 不为空，但查询结果为空时
     */
    @Test
    public void testQueryAllByDistributorGroupIds_NoResults() {
        when(distributorRepository.queryDistributorByGroupIds(anyList())).thenReturn(Collections.emptyList());
        List<DistributorModel> result = distributorAppService.queryAllByDistributorGroupIds(Arrays.asList(1L, 2L));
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 queryAllByDistributorGroupIds 方法，当传入的 distributorGroupIds 不为空，且查询结果不为空时
     */
    @Test
    public void testQueryAllByDistributorGroupIds_WithResults() {
        List<DistributorBO> distributorBOList = Arrays.asList(new DistributorBO());
        when(distributorRepository.queryDistributorByGroupIds(anyList())).thenReturn(distributorBOList);
        DistributorModel distributorModel = new DistributorModel();
        when(distributorVOAssembler.buildDistributorModel(any(DistributorBO.class))).thenReturn(distributorModel);
        List<DistributorModel> result = distributorAppService.queryAllByDistributorGroupIds(Arrays.asList(1L, 2L));
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertSame(distributorModel, result.get(0));
    }

    /**
     * 测试审核通过的情况
     */
    @Test
    public void testPassAuditSuccess() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 1, 1L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(1L).status(DistributionApproveStatusEnum.PASS).build();
        when(distributorDomainService.passAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.passAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(1L), response.getDistributorId());
        assertEquals(Integer.valueOf(DistributionApproveStatusEnum.PASS.getCode()), response.getStatus());
    }

    /**
     * 测试审核通过但状态为空的情况
     */
    @Test
    public void testPassAuditSuccessButStatusIsNull() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 1, 1L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(1L).status(null).build();
        when(distributorDomainService.passAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.passAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(1L), response.getDistributorId());
        assertNull(response.getStatus());
    }


    /**
     * 测试rejectAudit方法，当审核被驳回时
     */
    @Test
    public void testRejectAuditWhenAuditRejected() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 2, 3L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(3L).status(DistributionApproveStatusEnum.REJECT).build();
        when(distributorDomainService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.rejectAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(3), response.getDistributorId());
        assertEquals(Integer.valueOf(DistributionApproveStatusEnum.REJECT.getCode()), response.getStatus());
    }


    /**
     * 测试rejectAudit方法，当审核状态为审核通过时
     */
    @Test
    public void testRejectAuditWhenAuditPassed() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 2, 3L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(3L).status(DistributionApproveStatusEnum.PASS).build();
        when(distributorDomainService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.rejectAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(3), response.getDistributorId());
        assertEquals(Integer.valueOf(DistributionApproveStatusEnum.PASS.getCode()), response.getStatus());
    }

    /**
     * 测试rejectAudit方法，当审核状态为等待审核时
     */
    @Test
    public void testRejectAuditWhenAuditWaiting() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 2, 3L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(3L).status(DistributionApproveStatusEnum.WAITING).build();
        when(distributorDomainService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.rejectAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(3), response.getDistributorId());
        assertEquals(Integer.valueOf(DistributionApproveStatusEnum.WAITING.getCode()), response.getStatus());
    }

    /**
     * 测试rejectAudit方法，当审核状态为未申请时
     */
    @Test
    public void testRejectAuditWhenAuditUnApplied() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 2, 3L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(3L).status(DistributionApproveStatusEnum.UN_APPLY).build();
        when(distributorDomainService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.rejectAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(3), response.getDistributorId());
        assertEquals(Integer.valueOf(DistributionApproveStatusEnum.UN_APPLY.getCode()), response.getStatus());
    }

    /**
     * 测试rejectAudit方法，当审核状态为已取消时
     */
    @Test
    public void testRejectAuditWhenAuditCanceled() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 2, 3L);
        DistributorBindBO distributorBindBO = DistributorBindBO.builder().distributorId(3L).status(DistributionApproveStatusEnum.CANCELED).build();
        when(distributorDomainService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(distributorBindBO);
        // act
        DistributorApplyHandleResponse response = distributorAppService.rejectAudit(request);
        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(3), response.getDistributorId());
        assertEquals(Integer.valueOf(DistributionApproveStatusEnum.CANCELED.getCode()), response.getStatus());
    }

    /**
     * 测试queryDistributor方法，当返回非空列表时
     */
    @Test
    public void testQueryDistributorReturnsNonEmptyList() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        PageDataDTO<DistributorBindBO> pageDataDTO = new PageDataDTO<>();
        List<DistributorBindBO> expectedList = Collections.singletonList(new DistributorBindBO());
        pageDataDTO.setList(expectedList);
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(pageDataDTO);
        // act
        List<DistributorBindBO> result = distributorAppService.queryDistributor(request);
        // assert
        assertEquals(expectedList, result);
        verify(distributorRepository, times(1)).pageQueryDistributor(request);
    }

    /**
     * 测试queryDistributor方法，当返回空列表时
     */
    @Test
    public void testQueryDistributorReturnsEmptyList() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        PageDataDTO<DistributorBindBO> pageDataDTO = new PageDataDTO<>();
        pageDataDTO.setList(Lists.newArrayList());
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(pageDataDTO);
        // act
        List<DistributorBindBO> result = distributorAppService.queryDistributor(request);
        // assert
        assertEquals(Collections.emptyList(), result);
        verify(distributorRepository, times(1)).pageQueryDistributor(request);
    }

    /**
     * 测试queryDistributor方法，当返回null时
     */
    @Test
    public void testQueryDistributorReturnsNull() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();
        when(distributorRepository.pageQueryDistributor(request)).thenReturn(null);
        // act
        List<DistributorBindBO> result = distributorAppService.queryDistributor(request);
        // assert
        assertEquals(Collections.emptyList(), result);
        verify(distributorRepository, times(1)).pageQueryDistributor(request);
    }
}
