package com.sankuai.carnation.distribution.wallet.fundamental.bean.generator;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.commission.enums.RebateEventSourceEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletAccountBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountBO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletAccountStatusEnum;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/6
 **/
public class WalletAccountTestBeanGenerator {

    public static WalletActivityAccountBO generateWalletActivityAccountBO(long amount) {
        return generateWalletActivityAccountBO(WalletTestConstants.mockWalletAccountId, amount);
    }

    public static WalletActivityAccountBO generateWalletActivityAccountBO(long walletAccountId, long amount) {
        WalletActivityAccountBO activityAccountBO = new WalletActivityAccountBO();
        activityAccountBO.setActivityAccountId(WalletTestConstants.mockActivityAccountId);
        activityAccountBO.setWalletAccountId(walletAccountId);
        activityAccountBO.setWalletAccountBO(generateWalletAccountBO(walletAccountId, amount));
        activityAccountBO.setActivitySource(RebateEventSourceEnum.PROMO_CODE_REBATE_EVENT.getCode());
        activityAccountBO.setActivityId(0L);
        activityAccountBO.setPayStrategyId(WalletTestConstants.mockPayStrategyId);
        activityAccountBO.setPayStrategy(WalletPayStrategyTestBeanGenerator.generatePrivacyWalletPayStrategyBO());
        activityAccountBO.setAmount(amount);
        activityAccountBO.setStatus(WalletAccountStatusEnum.AVAILABLE.getCode());
        return activityAccountBO;
    }

    public static WalletAccountBO generateWalletAccountBO(long amount) {
        return generateWalletAccountBO(WalletTestConstants.mockWalletAccountId, amount);
    }

    public static WalletAccountBO generateWalletAccountBO(long walletAccountId, long amount) {
        WalletAccountBO walletAccountBO = new WalletAccountBO();
        walletAccountBO.setWalletId(walletAccountId);
        walletAccountBO.setBizCode(WalletTestConstants.mockBizCode);
        walletAccountBO.setBindAccountType(1);
        walletAccountBO.setBindAccountId("accountId");
        walletAccountBO.setPayStrategyId(WalletTestConstants.mockPayStrategyId);
        walletAccountBO.setPayStrategy(WalletPayStrategyTestBeanGenerator.generatePrivacyWalletPayStrategyBO());
        walletAccountBO.setActivityAccountList(Lists.newArrayList());
        walletAccountBO.setAmount(amount);
        walletAccountBO.setStatus(WalletAccountStatusEnum.AVAILABLE.getCode());
        return walletAccountBO;
    }
}
