package com.sankuai.carnation.distribution.privatelive.consultant.converter;

import com.sankuai.carnation.distribution.privatelive.consultant.dto.CustomerAssetsDTO;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalInfo;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTag;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Date;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class CustomerAssetsDTOConverterTest {

    /**
     * Tests the converter method when both groupList and tagList properties of customerInfo are not empty.
     */
    @Test
    public void testConverter_GroupListAndTagListNotEmpty() throws Throwable {
        // Arrange
        CustomerCapitalInfo customerInfo = new CustomerCapitalInfo();
        customerInfo.setUnionId("unionId");
        customerInfo.setMtUserId(1L);
        customerInfo.setAvatarUrl("avatarUrl");
        customerInfo.setNickName("nickName");
        customerInfo.setWxId("wxId");
        customerInfo.setLastHandleTime(new Date());
        CustomerCapitalInfo.GroupInfo groupInfo = new CustomerCapitalInfo.GroupInfo();
        groupInfo.setGroupName("groupName");
        customerInfo.setGroupList(Arrays.asList(groupInfo));
        CustomerCapitalTag tag = new CustomerCapitalTag();
        tag.setTagName("tagName");
        customerInfo.setTagList(Arrays.asList(tag));
        // Act
        CustomerAssetsDTO customerAssetsDTO = CustomerAssetsDTOConverter.converter(customerInfo);
        // Assert
        assertNotNull(customerAssetsDTO);
        assertEquals("unionId", customerAssetsDTO.getUnionId());
        assertEquals(Long.valueOf(1), customerAssetsDTO.getMtUserId());
        assertEquals("avatarUrl", customerAssetsDTO.getAvatar());
        assertEquals("nickName", customerAssetsDTO.getNickName());
        assertEquals("wxId", customerAssetsDTO.getWxId());
        assertEquals(1, customerAssetsDTO.getCommunityNameList().size());
        assertEquals("groupName", customerAssetsDTO.getCommunityNameList().get(0));
        assertEquals(1, customerAssetsDTO.getFollowTagList().size());
        assertEquals("tagName", customerAssetsDTO.getFollowTagList().get(0));
    }

    /**
     * Tests the converter method when the groupList property of customerInfo is empty.
     */
    @Test
    public void testConverter_GroupListEmpty() throws Throwable {
        // Arrange
        CustomerCapitalInfo customerInfo = new CustomerCapitalInfo();
        customerInfo.setUnionId("unionId");
        customerInfo.setMtUserId(1L);
        customerInfo.setAvatarUrl("avatarUrl");
        customerInfo.setNickName("nickName");
        customerInfo.setWxId("wxId");
        customerInfo.setLastHandleTime(new Date());
        customerInfo.setGroupList(null);
        // Act
        CustomerAssetsDTO customerAssetsDTO = CustomerAssetsDTOConverter.converter(customerInfo);
        // Assert
        assertNotNull(customerAssetsDTO);
        assertEquals("unionId", customerAssetsDTO.getUnionId());
        assertEquals(Long.valueOf(1), customerAssetsDTO.getMtUserId());
        assertEquals("avatarUrl", customerAssetsDTO.getAvatar());
        assertEquals("nickName", customerAssetsDTO.getNickName());
        assertEquals("wxId", customerAssetsDTO.getWxId());
        // Adjusted to handle potential null
        assertEquals(0, customerAssetsDTO.getCommunityNameList() != null ? customerAssetsDTO.getCommunityNameList().size() : 0);
    }

    /**
     * Tests the converter method when the tagList property ofInfo is empty.
     */
    @Test
    public void testConverter_TagListEmpty() throws Throwable {
        // Arrange
        CustomerCapitalInfo customerInfo = new CustomerCapitalInfo();
        customerInfo.setUnionId("unionId");
        customerInfo.setMtUserId(1L);
        customerInfo.setAvatarUrl("avatarUrl");
        customerInfo.setNickName("nickName");
        customerInfo.setWxId("wxId");
        customerInfo.setLastHandleTime(new Date());
        customerInfo.setTagList(null);
        // Act
        CustomerAssetsDTO customerAssetsDTO = CustomerAssetsDTOConverter.converter(customerInfo);
        // Assert
        assertNotNull(customerAssetsDTO);
        assertEquals("unionId", customerAssetsDTO.getUnionId());
        assertEquals(Long.valueOf(1), customerAssetsDTO.getMtUserId());
        assertEquals("avatarUrl", customerAssetsDTO.getAvatar());
        assertEquals("nickName", customerAssetsDTO.getNickName());
        assertEquals("wxId", customerAssetsDTO.getWxId());
        // Adjusted to handle potential null
        assertEquals(0, customerAssetsDTO.getFollowTagList() != null ? customerAssetsDTO.getFollowTagList().size() : 0);
    }
}
