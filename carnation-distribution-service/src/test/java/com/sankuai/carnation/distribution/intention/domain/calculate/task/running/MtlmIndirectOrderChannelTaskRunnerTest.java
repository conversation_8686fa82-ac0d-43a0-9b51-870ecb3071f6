package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.assertFalse;

/**
 * 测试MtlmIndirectOrderChannelTaskRunner的isOrderHitTask方法
 */
public class MtlmIndirectOrderChannelTaskRunnerTest {

    private MtlmIndirectOrderChannelTaskRunner runner;
    private IntentionCalculateTaskParamBO taskParam;
    private OrderInfoBO orderInfo;

    @Before
    public void setUp() {
        runner = new MtlmIndirectOrderChannelTaskRunner();
        taskParam = Mockito.mock(IntentionCalculateTaskParamBO.class);
        orderInfo = Mockito.mock(OrderInfoBO.class);
    }

    /**
     * 测试isOrderHitTask方法，期望返回false
     */
    @Test
    public void testIsOrderHitTaskExpectFalse() {
        // arrange
        // 由于isOrderHitTask方法的实现直接返回false，不需要设置mock对象的行为

        // act
        boolean result = runner.isOrderHitTask(taskParam, orderInfo);

        // assert
        assertFalse("期望isOrderHitTask方法返回false", result);
    }
}
