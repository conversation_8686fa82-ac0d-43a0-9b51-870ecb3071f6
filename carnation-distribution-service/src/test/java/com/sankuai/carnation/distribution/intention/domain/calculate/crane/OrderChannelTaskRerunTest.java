package com.sankuai.carnation.distribution.intention.domain.calculate.crane;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.carnation.distribution.promocode.rebate.bo.RbOrderVerifyRebateCommissionBO;
import com.sankuai.carnation.distribution.promocode.rebate.bo.TechRbOrderVerifyRebateExtInfoBO;
import com.sankuai.carnation.distribution.promocode.rebate.enums.PromoCodeRebateBizTypeEnum;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.commisson.repository.RebateLimitRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * 测试OrderChannelTaskRerun类的deleteCommissionRecord方法
 */
public class OrderChannelTaskRerunTest {

    @InjectMocks
    private OrderChannelTaskRerun orderChannelTaskRerun;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rebateCommissionDataService;

    @Mock
    private RebateLimitRepository rebateLimitRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试deleteCommissionRecord方法，当Lion配置为true且rebateCommissionBO不为null时，应调用deleteByOrder和deleteByUnifiedOrderId
     */
    @Test
    public void testDeleteCommissionRecord_ConfigTrueAndRebateCommissionBONotNull() throws InterruptedException {
        // arrange
        String orderId = "testOrderId";
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getBoolean(Environment.getAppName(), "delete.rebate.commission", false)).thenReturn(true);
            RbOrderVerifyRebateCommissionBO rebateCommissionBO = new RbOrderVerifyRebateCommissionBO();
            when(rebateCommissionDataService.queryByOrderId(PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode(), orderId, TechRbOrderVerifyRebateExtInfoBO.class)).thenReturn(rebateCommissionBO);

            // act
            orderChannelTaskRerun.deleteCommissionRecord(orderId);

            // assert
            verify(rebateCommissionDataService, times(1)).deleteByOrder(PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode(), orderId);
            verify(rebateLimitRepository, times(1)).deleteByUnifiedOrderId(orderId);
        }
    }

    /**
     * 测试deleteCommissionRecord方法，当Lion配置为true但rebateCommissionBO为null时，不应调用deleteByOrder和deleteByUnifiedOrderId
     */
    @Test
    public void testDeleteCommissionRecord_ConfigTrueAndRebateCommissionBONull() throws InterruptedException {
        // arrange
        String orderId = "testOrderId";
        try (MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedLion.when(() -> Lion.getBoolean(Environment.getAppName(), "delete.rebate.commission", false)).thenReturn(true);
            when(rebateCommissionDataService.queryByOrderId(PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode(), orderId, TechRbOrderVerifyRebateExtInfoBO.class)).thenReturn(null);

            // act
            orderChannelTaskRerun.deleteCommissionRecord(orderId);

            // assert
            verify(rebateCommissionDataService, never()).deleteByOrder(PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode(), orderId);
            verify(rebateLimitRepository, never()).deleteByUnifiedOrderId(orderId);
        }
    }

}
