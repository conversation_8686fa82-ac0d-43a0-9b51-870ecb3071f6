package com.sankuai.carnation.distribution.privatelive.account.converter;

import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试PrivateLiveWechatAccountConverter的toPoJo方法
 */
public class PrivateLiveWechatAccountConverterTest {

    @InjectMocks
    private PrivateLiveWechatAccountConverter converter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试当PrivateLiveAccountBO的status为null时，转换结果的status应为null
     */
    @Test
    public void testToPoJoStatusNull() {
        // arrange
        PrivateLiveAccountBO bo = new PrivateLiveAccountBO();
        bo.setStatus(null);

        // act
        PrivateLiveConsultantAccount result = converter.toPoJo(bo);

        // assert
        assertNull(result.getStatus());
    }

    /**
     * 测试当PrivateLiveAccountBO的status为VALID时，转换结果的status应为1
     */
    @Test
    public void testToPoJoStatusValid() {
        // arrange
        PrivateLiveAccountBO bo = new PrivateLiveAccountBO();
        bo.setStatus(DistributionStatusEnum.VALID);

        // act
        PrivateLiveConsultantAccount result = converter.toPoJo(bo);

        // assert
        assertNotNull(result.getStatus());
        assertEquals(Integer.valueOf(DistributionStatusEnum.VALID.getCode()), result.getStatus());
    }

    /**
     * 测试当PrivateLiveAccountBO的status为INVALID时，转换结果的status应为2
     */
    @Test
    public void testToPoJoStatusInvalid() {
        // arrange
        PrivateLiveAccountBO bo = new PrivateLiveAccountBO();
        bo.setStatus(DistributionStatusEnum.INVALID);

        // act
        PrivateLiveConsultantAccount result = converter.toPoJo(bo);

        // assert
        assertNotNull(result.getStatus());
        assertEquals(Integer.valueOf(DistributionStatusEnum.INVALID.getCode()), result.getStatus());
    }

    /**
     * 测试当PrivateLiveAccountBO的status为UNKNOWN时，转换结果的status应为0
     */
    @Test
    public void testToPoJoStatusUnknown() {
        // arrange
        PrivateLiveAccountBO bo = new PrivateLiveAccountBO();
        bo.setStatus(DistributionStatusEnum.UNKNOWN);

        // act
        PrivateLiveConsultantAccount result = converter.toPoJo(bo);

        // assert
        assertNotNull(result.getStatus());
        assertEquals(Integer.valueOf(DistributionStatusEnum.UNKNOWN.getCode()), result.getStatus());
    }/**
     * 测试toEntity方法，当传入的PrivateLiveConsultantAccount状态为有效时
     */
    @Test
    public void testToEntityWithValidStatus() {
        // arrange
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setStatus(1); // 设置为有效状态

        // act
        PrivateLiveAccountBO result = converter.toEntity(account);

        // assert
        assertNotNull(result);
        assertEquals(DistributionStatusEnum.VALID, result.getStatus());
    }

    /**
     * 测试toEntity方法，当传入的PrivateLiveConsultantAccount状态为无效时
     */
    @Test
    public void testToEntityWithInvalidStatus() {
        // arrange
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setStatus(2); // 设置为无效状态

        // act
        PrivateLiveAccountBO result = converter.toEntity(account);

        // assert
        assertNotNull(result);
        assertEquals(DistributionStatusEnum.INVALID, result.getStatus());
    }

    /**
     * 测试toEntity方法，当传入的PrivateLiveConsultantAccount状态为未知时
     */
    @Test
    public void testToEntityWithUnknownStatus() {
        // arrange
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setStatus(0); // 设置为未知状态

        // act
        PrivateLiveAccountBO result = converter.toEntity(account);

        // assert
        assertNotNull(result);
        assertEquals(DistributionStatusEnum.UNKNOWN, result.getStatus());
    }

}
