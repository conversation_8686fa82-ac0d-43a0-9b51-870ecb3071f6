package com.sankuai.carnation.distribution.intention.domain.calculate.crane;

import com.alibaba.fastjson.JSON;
import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.service.BeautiBotUserService;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelTaskStatusEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.OrderChannelCalculateMerger;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.OrderChannelResultDomainService;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.running.OrderChannelTaskRunner;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalRunningTaskMapper;
import com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalTaskMapper;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalTaskWithBLOBs;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/11/13
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderChannelTaskRerunRunningTaskTest {

    @InjectMocks
    private OrderChannelTaskRerun orderChannelTaskRerun;

    @Mock
    private DistributionOrderChannelCalRunningTaskMapper runningTaskMapper;

    @Mock
    private BeautiBotUserService beautiBotUserService;

    @Mock
    private OrderInfoAdaptor orderInfoAdaptor;

    @Mock
    private OrderChannelCalculateMerger calculateMerger;

    @Mock
    private OrderChannelResultDomainService resultDomainService;

    @Mock
    private OrderChannelTaskRunner taskRunner;

    private static final String RERUN_ORDER_ID = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.rerun.order-id";

    private static final String RERUN_WRITE_DB = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.rerun.write-db";

    private static final String RERUN_SWITCH = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.param-check.switch";

    private static final String RERUN_TIME_OUT = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.time.out.rerun";

    private List<String> orderIdList = Lists.newArrayList();

    private List<DistributionOrderChannelCalRunningTaskWithBLOBs> runningTaskList = Lists.newArrayList();

    @Before
    public void setUp() {
        orderIdList.add("1");
    }

    /**
     * 测试重计算执行任务，且任务为null
     */
    @Test
    public void testRerunRunningTaskNull() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);

            runningTaskList.add(null);
            when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(runningTaskList);

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);
                // act
                orderChannelTaskRerun.rerunRunningTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }

    /**
     * 测试重计算执行任务，且任务已超时，允许重计算
     */
    @Test
    public void testRerunRunningTaskTimeOut() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);
            mockLionFactory.when(() -> Lion.getBoolean(anyString(), eq(RERUN_TIME_OUT))).thenReturn(true);

            DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = new DistributionOrderChannelCalRunningTaskWithBLOBs();
            runningTask.setStatus(OrderChannelTaskStatusEnum.TIME_OUT.getCode());
            runningTask.setOrderType(1);
            runningTask.setOrderId("1");
            runningTask.setTaskType(1);
            runningTask.setCalTaskId(1L);
            IntentionCalculateTaskParamBO formerTaskParam = new IntentionCalculateTaskParamBO();
            formerTaskParam.setOrderType(runningTask.getOrderType());
            formerTaskParam.setOrderId(runningTask.getOrderId());
            formerTaskParam.setDistributionCode("code");
            runningTask.setParam(JSON.toJSONString(formerTaskParam));
            runningTask.setId(1L);
            runningTaskList.add(runningTask);
            when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(runningTaskList);

            when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(new OrderInfoBO());

            OrderChannelTaskRunningResultBO rerunResult = new OrderChannelTaskRunningResultBO();
            rerunResult.setBusinessChannel("channel");
            when(taskRunner.calculateTaskResult(anyInt(), anyLong(), any())).thenReturn(rerunResult);

            mockLionFactory.when(() -> Lion.getBoolean(anyString(), eq(RERUN_WRITE_DB))).thenReturn(true);
            doReturn(1).when(runningTaskMapper).updateByPrimaryKeySelective(any());

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);

                // act
                orderChannelTaskRerun.rerunRunningTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }

    /**
     * 测试重计算执行任务，且任务运行中
     */
    @Test
    public void testRerunRunningTaskRunning() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);

            DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = new DistributionOrderChannelCalRunningTaskWithBLOBs();
            runningTask.setStatus(OrderChannelTaskStatusEnum.RUNNING.getCode());
            runningTaskList.add(runningTask);
            when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(runningTaskList);

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);

                // act
                orderChannelTaskRerun.rerunRunningTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }

    /**
     * 测试重计算执行任务，且任务已结束
     */
    @Test
    public void testRerunRunningTaskEnd() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);

            DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = new DistributionOrderChannelCalRunningTaskWithBLOBs();
            runningTask.setStatus(OrderChannelTaskStatusEnum.TIME_OUT.getCode());
            runningTask.setOrderType(1);
            runningTask.setOrderId("1");
            runningTask.setTaskType(1);
            runningTask.setCalTaskId(1L);
            IntentionCalculateTaskParamBO formerTaskParam = new IntentionCalculateTaskParamBO();
            formerTaskParam.setOrderType(runningTask.getOrderType());
            formerTaskParam.setOrderId(runningTask.getOrderId());
            formerTaskParam.setDistributionCode("code");
            runningTask.setParam(JSON.toJSONString(formerTaskParam));
            runningTask.setId(1L);
            runningTaskList.add(runningTask);
            when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(runningTaskList);

            when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(new OrderInfoBO());

            OrderChannelTaskRunningResultBO rerunResult = new OrderChannelTaskRunningResultBO();
            rerunResult.setBusinessChannel("channel");
            when(taskRunner.calculateTaskResult(anyInt(), anyLong(), any())).thenReturn(rerunResult);

            mockLionFactory.when(() -> Lion.getBoolean(anyString(), eq(RERUN_WRITE_DB))).thenReturn(true);
            doReturn(1).when(runningTaskMapper).updateByPrimaryKeySelective(any());

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);

                // act
                orderChannelTaskRerun.rerunRunningTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }
}
