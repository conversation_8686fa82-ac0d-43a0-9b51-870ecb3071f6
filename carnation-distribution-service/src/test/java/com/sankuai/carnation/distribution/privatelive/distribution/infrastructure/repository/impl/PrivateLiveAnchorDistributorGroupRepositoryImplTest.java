package com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.converter.PrivateLiveDistributorGroupConverter;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.example.PrivateLiveAnchorDistributorGroupExample;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.mapper.PrivateLiveAnchorDistributorGroupMapper;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.po.PrivateLiveAnchorDistributorGroup;
import com.sankuai.carnation.distribution.privatelive.distribution.utils.PageHelperUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveAnchorDistributorGroupRepositoryImplTest {


    @Mock
    private PrivateLiveDistributorGroupConverter privateLiveDistributorGroupConverter;

    @Mock
    private PrivateLiveAnchorDistributorGroupMapper privateLiveAnchorDistributorGroupMapper;


    @InjectMocks
    private PrivateLiveAnchorDistributorGroupRepositoryImpl repository;

    private final Long anchorId = 1L;

    private final Long distributorGroupId = 1L;

    @Mock
    private PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepositoryImpl privateLiveAnchorDistributorGroupRepository;

    @Mock
    private PageHelperUtils pageHelperUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试查询分销组ID且状态为审核通过时返回正确的BO
     */
    @Test
    public void testQueryByDistributorGroupIdWithStatusPassReturnsBO() {
        Long distributorGroupId = 1L;
        List<Integer> statusList = Collections.singletonList(DistributionApproveStatusEnum.PASS.getCode());
        PrivateLiveAnchorDistributorGroup po = new PrivateLiveAnchorDistributorGroup();
        po.setStatus(DistributionApproveStatusEnum.PASS.getCode());
        List<PrivateLiveAnchorDistributorGroup> poList = Collections.singletonList(po);
        PrivateLiveAnchorDistributorGroupBO expectedBO = new PrivateLiveAnchorDistributorGroupBO();
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any())).thenReturn(poList);
        when(privateLiveDistributorGroupConverter.toEntity(any())).thenReturn(expectedBO);
        PrivateLiveAnchorDistributorGroupBO result = repository.queryByDistributorGroupId(distributorGroupId);
        verify(privateLiveAnchorDistributorGroupMapper, times(1)).selectByExample(any());
        verify(privateLiveDistributorGroupConverter, times(1)).toEntity(po);
        assertNotNull(result);
        assertEquals(expectedBO, result);
    }

    /**
     * 测试查询分销组ID且状态为审核通过时，数据库无数据返回null
     */
    @Test
    public void testQueryByDistributorGroupIdWithStatusPassReturnsNull() {
        Long distributorGroupId = 1L;
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        PrivateLiveAnchorDistributorGroupBO result = repository.queryByDistributorGroupId(distributorGroupId);
        verify(privateLiveAnchorDistributorGroupMapper, times(1)).selectByExample(any());
        assertNull(result);
    }


    /**
     * 测试查询分销组ID和状态列表为空时返回null
     */
    @Test
    public void testQueryByDistributorGroupIdWithEmptyStatusListReturnsNull() {
        Long distributorGroupId = 1L;
        List<Integer> statusList = Collections.emptyList();
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(Collections.emptyList());
        PrivateLiveAnchorDistributorGroupBO result = repository.queryByDistributorGroupId(distributorGroupId, statusList);
        assertNull(result);
    }

    /**
     * 测试查询分销组ID和状态列表不为空，但未找到数据时返回null
     */
    @Test
    public void testQueryByDistributorGroupIdWithNonEmptyStatusListReturnsNull() {
        Long distributorGroupId = 1L;
        List<Integer> statusList = Arrays.asList(1, 2);
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(Collections.emptyList());
        PrivateLiveAnchorDistributorGroupBO result = repository.queryByDistributorGroupId(distributorGroupId, statusList);
        assertNull(result);
    }

    /**
     * 测试查询分销组ID和状态列表不为空，找到多条数据时抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testQueryByDistributorGroupIdWithNonUniqueResultThrowsException() {
        Long distributorGroupId = 1L;
        List<Integer> statusList = Arrays.asList(1, 2);
        List<PrivateLiveAnchorDistributorGroup> groups = Arrays.asList(new PrivateLiveAnchorDistributorGroup(), new PrivateLiveAnchorDistributorGroup());
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(groups);
        repository.queryByDistributorGroupId(distributorGroupId, statusList);
    }

    /**
     * 测试查询分销组ID和状态列表不为空，找到一条数据时返回正确的BO
     */
    @Test
    public void testQueryByDistributorGroupIdWithSingleResultReturnsBO() {
        Long distributorGroupId = 1L;
        List<Integer> statusList = Arrays.asList(1, 2);
        PrivateLiveAnchorDistributorGroup group = PrivateLiveAnchorDistributorGroup.builder()
                .distributorGroupId(1L)
                .build();
        PrivateLiveAnchorDistributorGroupBO expectedBO = new PrivateLiveAnchorDistributorGroupBO();
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(Collections.singletonList(group));
        when(privateLiveDistributorGroupConverter.toEntity(any(PrivateLiveAnchorDistributorGroup.class))).thenReturn(expectedBO);
        PrivateLiveAnchorDistributorGroupBO result = repository.queryByDistributorGroupId(distributorGroupId, statusList);
        assertNotNull(result);
        assertEquals(expectedBO, result);
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryByAnchorIdAndDistributorGroupIdEmptyResult() {
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(Collections.emptyList());
        PrivateLiveAnchorDistributorGroupBO result = repository.queryByAnchorIdAndDistributorGroupId(anchorId, distributorGroupId);
        assertNull(result);
    }

    /**
     * 测试查询结果为多个对象的情况，期望抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testQueryByAnchorIdAndDistributorGroupIdMultipleResults() {
        PrivateLiveAnchorDistributorGroup po1 = new PrivateLiveAnchorDistributorGroup();
        po1.setAnchorId(anchorId);
        po1.setDistributorGroupId(distributorGroupId);
        PrivateLiveAnchorDistributorGroup po2 = new PrivateLiveAnchorDistributorGroup();
        po2.setAnchorId(anchorId);
        po2.setDistributorGroupId(distributorGroupId);
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(Arrays.asList(po1, po2));
        repository.queryByAnchorIdAndDistributorGroupId(anchorId, distributorGroupId);
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryByAnchorIdResultEmpty() {
        when(privateLiveAnchorDistributorGroupMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        PageDataDTO<PrivateLiveAnchorDistributorGroupBO> result = repository.queryByAnchorId(1L, 1L, 10L);
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
    }


    /**
     * 测试 countByAnchorId 方法，当给定有效的 anchorId 时
     */
    @Test
    public void testCountByAnchorIdValidAnchorId() {
        // arrange
        Long anchorId = 1L;
        Long expectedCount = 5L;
        PrivateLiveAnchorDistributorGroupExample doCriteria = new PrivateLiveAnchorDistributorGroupExample();
        doCriteria.createCriteria().andAnchorIdEqualTo(anchorId).andStatusEqualTo(1).andIsDeleteEqualTo(0);
        when(privateLiveAnchorDistributorGroupMapper.countByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(expectedCount);
        // act
        Long actualCount = repository.countByAnchorId(anchorId);
        // assert
        assertEquals(expectedCount, actualCount);
        verify(privateLiveAnchorDistributorGroupMapper, times(1)).countByExample(any(PrivateLiveAnchorDistributorGroupExample.class));
    }


    /**
     * 测试 countByAnchorId 方法，当数据库中不存在对应 anchorId 的数据时
     */
    @Test
    public void testCountByAnchorIdNoDataFound() {
        // arrange
        // 假设这是一个数据库中不存在的 anchorId
        Long anchorId = 999L;
        Long expectedCount = 0L;
        when(privateLiveAnchorDistributorGroupMapper.countByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(expectedCount);
        // act
        Long actualCount = repository.countByAnchorId(anchorId);
        // assert
        assertEquals(expectedCount, actualCount);
        verify(privateLiveAnchorDistributorGroupMapper, times(1)).countByExample(any(PrivateLiveAnchorDistributorGroupExample.class));
    }

    /**
     * 测试 countByAnchorId 方法，当数据库操作出现异常时
     */
    @Test(expected = RuntimeException.class)
    public void testCountByAnchorIdDatabaseException() {
        // arrange
        Long anchorId = 1L;
        when(privateLiveAnchorDistributorGroupMapper.countByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenThrow(new RuntimeException("Database error"));
        // act
        repository.countByAnchorId(anchorId);
        // assert
        // Expected exception
    }

    /**
     * 测试createPoExample方法，当BO中的version和id都不为null时
     */
    @Test
    public void testCreatePoExampleWithNonNullVersionAndId() {
        // arrange
        when(privateLiveAnchorDistributorGroupBO.getVersion()).thenReturn(1);
        when(privateLiveAnchorDistributorGroupBO.getId()).thenReturn(100L);
        // act
        PrivateLiveAnchorDistributorGroupExample result = repository.createPoExample(privateLiveAnchorDistributorGroupBO);
        // assert
        assertEquals(1, result.getOredCriteria().size());
        assertEquals(Integer.valueOf(1), result.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
        assertEquals(Long.valueOf(100L), result.getOredCriteria().get(0).getAllCriteria().get(1).getValue());
    }



    /**
     * 测试createPoExample方法，当BO中的id为null时
     */
    @Test(expected = RuntimeException.class)
    public void testCreatePoExampleWithNullId() {
        // arrange
        when(privateLiveAnchorDistributorGroupBO.getVersion()).thenReturn(1);
        when(privateLiveAnchorDistributorGroupBO.getId()).thenReturn(null);
        // act
        PrivateLiveAnchorDistributorGroupExample result = repository.createPoExample(privateLiveAnchorDistributorGroupBO);
        // assert
        assertEquals(1, result.getOredCriteria().size());
        assertEquals(Integer.valueOf(1), result.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
    }





    /**
     * 测试正常场景 - 数据库中存在符合条件的记录
     */
    @Test
    public void testCountByStatusNormal() {
        // arrange
        Mockito.when(privateLiveAnchorDistributorGroupMapper.countByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(5L);

        // act
        Integer result = repository.countByStatus(1L, 1);

        // assert
        assertEquals("应返回正确的记录数", Integer.valueOf(5), result);
    }

    /**
     * 测试边界场景 - 数据库中不存在符合条件的记录
     */
    @Test
    public void testCountByStatusBoundary() {
        // arrange
        Mockito.when(privateLiveAnchorDistributorGroupMapper.countByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenReturn(0L);

        // act
        Integer result = repository.countByStatus(1L, 1);

        // assert
        assertEquals("应返回0表示没有找到记录", Integer.valueOf(0), result);
    }

    /**
     * 测试异常场景 - 查询过程中发生异常
     */
    @Test(expected = RuntimeException.class)
    public void testCountByStatusException() {
        // arrange
        Mockito.when(privateLiveAnchorDistributorGroupMapper.countByExample(any(PrivateLiveAnchorDistributorGroupExample.class))).thenThrow(new RuntimeException());

        // act
        repository.countByStatus(1L, 1);

        // assert 由于设置了expected，所以不需要写断言，如果方法抛出了RuntimeException，则测试通过
    }
}
