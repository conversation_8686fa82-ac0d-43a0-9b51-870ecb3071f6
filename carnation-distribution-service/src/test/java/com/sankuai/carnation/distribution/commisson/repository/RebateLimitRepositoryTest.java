package com.sankuai.carnation.distribution.commisson.repository;

import com.sankuai.carnation.distribution.commisson.repository.dao.DistributorRebateLimitTabMapper;
import com.sankuai.carnation.distribution.commisson.repository.example.DistributorRebateLimitTabExample;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class RebateLimitRepositoryTest {

    @InjectMocks
    private RebateLimitRepository rebateLimitRepository;

    @Mock
    private DistributorRebateLimitTabMapper mapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试deleteByUnifiedOrderId方法，当mapper.deleteByExample返回大于0时
     */
    @Test
    public void testDeleteByUnifiedOrderId_ReturnTrue() {
        // arrange
        String unifiedOrderId = "testOrderId";
        when(mapper.deleteByExample(any(DistributorRebateLimitTabExample.class))).thenReturn(1);

        // act
        boolean result = rebateLimitRepository.deleteByUnifiedOrderId(unifiedOrderId);

        // assert
        assertTrue(result);
    }

    /**
     * 测试deleteByUnifiedOrderId方法，当mapper.deleteByExample返回0时
     */
    @Test
    public void testDeleteByUnifiedOrderId_ReturnFalse() {
        // arrange
        String unifiedOrderId = "testOrderId";
        when(mapper.deleteByExample(any(DistributorRebateLimitTabExample.class))).thenReturn(0);

        // act
        boolean result = rebateLimitRepository.deleteByUnifiedOrderId(unifiedOrderId);

        // assert
        assertFalse(result);
    }
}
