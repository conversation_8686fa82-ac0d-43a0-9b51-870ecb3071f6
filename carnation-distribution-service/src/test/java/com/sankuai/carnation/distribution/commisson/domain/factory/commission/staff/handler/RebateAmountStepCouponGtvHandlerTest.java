package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.handler;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleStepConfigDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleStepRuleDTO;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.distributionplan.utils.Func1;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class RebateAmountStepCouponGtvHandlerTest {

    @InjectMocks
    private RebateAmountStepCouponGtvHandler rebateAmountStepCouponCalcHandler;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;

    @Mock
    private RedisLockService redisLockService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试calculate方法，当Redis锁获取成功且满足活动规则时
     */
    @Test
    public void testCalculate_LockSuccessAndRuleMatched() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = getRebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        when(rbOrderVerifyRebateCommissionDataService.loadByActivityId(anyLong())).thenReturn(1L);
        when(redisLockService.tryLock(anyString(), (Func1<Object>) any(), anyInt())).thenReturn(RemoteResponse.success(new RebateAmountResult()));

        // act
        RemoteResponse<RebateAmountResult> result = rebateAmountStepCouponCalcHandler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(result);
        assertEquals("success", result.getMsg());
    }

    /**
     * 测试calculate方法，当Redis锁获取失败时
     */
    @Test
    public void testCalculate_LockFail() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();

        when(redisLockService.tryLock(anyString(), (Func1<Object>) any(), anyInt())).thenThrow(new RuntimeException("Lock acquisition failed"));

        // act
        try {
            rebateAmountStepCouponCalcHandler.calculate(request, rebateActivityRuleDTO);
            fail("Expected an RuntimeException to be thrown");
        } catch (RuntimeException e) {
            // assert
            assertEquals("Lock acquisition failed", e.getMessage());
        }
    }

    /**
     * 测试场景：正常情况下计算返利金额
     */
    @Test
    public void testGetRebateAmountResultRemoteResponse_Normal() {
        // arrange
        RebateAmountCalcRequest request = getRebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        when(rbOrderVerifyRebateCommissionDataService.sumVerifyAmountByActivityId(anyLong())).thenReturn(100L);

        // act
        RemoteResponse<RebateAmountResult> result = rebateAmountStepCouponCalcHandler.getRebateAmountResultRemoteResponse(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(100, result.getData().getRebateAmount());
        assertEquals(500, result.getData().getVerifyAmount());
        assertEquals(2, result.getData().getStepLevel());
    }

    /**
     * 测试场景：不满足活动规则
     */
    @Test
    public void testGetRebateAmountResultRemoteResponse_NotMatchRule() {
        // arrange
        RebateAmountCalcRequest request = getRebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        when(rbOrderVerifyRebateCommissionDataService.sumVerifyAmountByActivityId(anyLong())).thenReturn(100000L);

        // act
        RemoteResponse<RebateAmountResult> result = rebateAmountStepCouponCalcHandler.getRebateAmountResultRemoteResponse(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("不满足活动规则", result.getData().getReason());

    }


    private RebateActivityRuleDTO getRebateActivityRuleDTO() {
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleDTO = new RebateSettleRuleDTO();

        RebateSettleStepRuleDTO stepRule = new RebateSettleStepRuleDTO();
        RebateSettleRuleStepConfigDTO rebateSettleRuleStepConfigDTO = new RebateSettleRuleStepConfigDTO();
        rebateSettleRuleStepConfigDTO.setStepIndex(0);
        rebateSettleRuleStepConfigDTO.setStepStart(1L);
        rebateSettleRuleStepConfigDTO.setStepEnd(100L);
        rebateSettleRuleStepConfigDTO.setRebateProportion(1000L);

        RebateSettleRuleStepConfigDTO rebateSettleRuleStepConfigDTO2 = new RebateSettleRuleStepConfigDTO();
        rebateSettleRuleStepConfigDTO2.setStepIndex(1);
        rebateSettleRuleStepConfigDTO2.setStepStart(101L);
        rebateSettleRuleStepConfigDTO2.setStepEnd(10000L);
        rebateSettleRuleStepConfigDTO2.setRebateProportion(2000L);


        stepRule.setRuleStep(Lists.newArrayList(rebateSettleRuleStepConfigDTO, rebateSettleRuleStepConfigDTO2));
        ruleDTO.setStepRule(stepRule);
        rebateActivityRuleDTO.setRule(ruleDTO);
        return rebateActivityRuleDTO;
    }

    @NotNull
    private RebateAmountCalcRequest getRebateAmountCalcRequest() {
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");
        request.setTechApplyRecordId(1L);
        request.setUserId(1L);
        request.setPlatform(1);
        request.setPayCent(1000L);
        request.setOrderVerifyTimes(2);
        return request;
    }

}
