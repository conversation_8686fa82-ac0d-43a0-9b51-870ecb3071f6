package com.sankuai.carnation.distribution.privatelive.distribution.startar.service;

/**
 * @Author: jinjian<PERSON>
 * @CreateTime: 2024/9/5 10:21
 * @Description:
 */

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.account.application.service.PrivateLiveWechatAccountAppService;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveWechatAccountRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.application.model.DistributionQrCodeInfo;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveDistributionQrCodeAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.PrivateLiveAccountTypeEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributionQrCodeRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.PrivateLiveDistributionQrCodeResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveDistributionQrCodeServiceImplTest {

    @InjectMocks
    private PrivateLiveDistributionQrCodeServiceImpl privateLiveDistributionQrCodeServiceImpl;

    @Mock
    private PrivateLiveDistributionQrCodeAppService privateLiveDistributionQrCodeAppService;

    @Mock
    private PrivateLiveWechatAccountAppService privateLiveWechatAccountAppService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试请求参数为空的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterQrCode_RequestIsNull() {
        privateLiveDistributionQrCodeServiceImpl.getOrCreateRegisterQrCode(null);
    }

    /**
     * 测试账号类型为UNKNOWN的情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterQrCode_AccountTypeIsUnknown() {
        PrivateLiveDistributionQrCodeRequest request = new PrivateLiveDistributionQrCodeRequest(0, 1L, 1L, "wxToken");
        privateLiveDistributionQrCodeServiceImpl.getOrCreateRegisterQrCode(request);
    }

    /**
     * 测试用户非法访问的情况
     */
    @Test
    public void testGetOrCreateRegisterQrCode_UserIllegalAccess() {
        PrivateLiveDistributionQrCodeRequest request = new PrivateLiveDistributionQrCodeRequest(3, 1L, 1L, "wxToken");
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(null);

        RemoteResponse<PrivateLiveDistributionQrCodeResponse> result = privateLiveDistributionQrCodeServiceImpl.getOrCreateRegisterQrCode(request);

        assertNotNull(result);
        assertTrue(!result.failure().isSuccess());
        assertEquals("server error", result.getMsg());
    }

    /**
     * 测试成功获取分销员二维码的情况
     */
    @Test
    public void testGetOrCreateRegisterQrCode_SuccessDistributor() {
        // arrange
        PrivateLiveDistributionQrCodeRequest request = PrivateLiveDistributionQrCodeRequest.builder()
                .accountType(PrivateLiveAccountTypeEnum.DISTRIBUTOR.getCode())
                .anchorId(1L)
                .distributorGroupId(2L)
                .wxToken("wxToken")
                .build();
        PrivateLiveAccountBO accountBO = PrivateLiveAccountBO.builder()
                .id(3L)
                .anchorId(1L)
                .accountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP))
                .distributorGroup(new DistributorGroupBO())
                .build();
        DistributionQrCodeInfo qrCodeInfo = new DistributionQrCodeInfo("codeUrl", "imageUrl");

        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        when(privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(any(Long.class), any(Long.class))).thenReturn(qrCodeInfo);

        // act
        RemoteResponse<PrivateLiveDistributionQrCodeResponse> response = privateLiveDistributionQrCodeServiceImpl.getOrCreateRegisterQrCode(request);

        // assert
        Assert.assertTrue(response.isSuccess());
        assert "imageUrl".equals(response.getData().getQrCodeUrl());
    }

    /**
     * 测试当前账户未注册为团长，不能邀请团员加入的情况
     */
    @Test(expected = BizSceneException.class)
    public void testGetOrCreateRegisterQrCode_AccountNotRegisteredAsDistributorGroup() {
        PrivateLiveDistributionQrCodeRequest request = new PrivateLiveDistributionQrCodeRequest(3, 1L, 1L, "wxToken");
        PrivateLiveAccountBO accountBO = mock(PrivateLiveAccountBO.class);
        when(accountBO.distributorGroup()).thenReturn(false);
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);

        privateLiveDistributionQrCodeServiceImpl.getOrCreateRegisterQrCode(request);
    }
}
