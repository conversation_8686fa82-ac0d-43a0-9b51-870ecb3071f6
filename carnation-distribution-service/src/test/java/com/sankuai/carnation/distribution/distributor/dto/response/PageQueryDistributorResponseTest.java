package com.sankuai.carnation.distribution.distributor.dto.response;

import org.junit.Test;
import static org.junit.Assert.*;

public class PageQueryDistributorResponseTest {

    /**
     * 测试空构造函数
     */
    @Test
    public void testNoArgsConstructor() {
        PageQueryDistributorResponse response = new PageQueryDistributorResponse();
        assertNotNull(response);
    }

    /**
     * 测试属性赋值和获取
     */
    @Test
    public void testProperties() {
        PageQueryDistributorResponse response = new PageQueryDistributorResponse();
        response.setTotalNum(10);
        assertEquals(Integer.valueOf(10), response.getTotalNum());
    }

    /**
     * 测试Builder构造
     */
    @Test
    public void testBuilder() {
        PageQueryDistributorResponse response = PageQueryDistributorResponse.builder()
                .totalNum(10)
                .build();
        assertEquals(Integer.valueOf(10), response.getTotalNum());
    }

    /**
     * 测试StatisticInfo内部类
     */
    @Test
    public void testStatisticInfo() {
        PageQueryDistributorResponse.StatisticInfo statisticInfo = new PageQueryDistributorResponse.StatisticInfo(1, 20);
        assertEquals(Integer.valueOf(1), statisticInfo.getStatus());
        assertEquals(Integer.valueOf(20), statisticInfo.getTotalNum());
    }
}
