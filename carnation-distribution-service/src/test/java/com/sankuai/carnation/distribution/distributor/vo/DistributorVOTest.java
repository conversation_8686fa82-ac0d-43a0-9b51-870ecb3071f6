package com.sankuai.carnation.distribution.distributor.vo;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class DistributorVOTest {

    private DistributorVO distributorVO;

    @Before
    public void setUp() throws Exception {
        distributorVO = DistributorVO.builder()
                .accountId(1L)
                .distributorId(2L)
                .groupId("group123")
                .nickname("nickname")
                .avatarUrl("url")
                .shareName("shareName")
                .mobile("**********")
                .wechatNumber("wechat123")
                .status(1)
                .actualName("actualName")
                .build();
    }

    /**
     * 测试获取 accountId
     */
    @Test
    public void testGetAccountId() throws Throwable {
        assertEquals(Long.valueOf(1), distributorVO.getAccountId());
    }

    /**
     * 测试设置和获取 distributorId
     */
    @Test
    public void testSetAndGetDistributorId() throws Throwable {
        distributorVO.setDistributorId(3L);
        assertEquals(Long.valueOf(3), distributorVO.getDistributorId());
    }

    // 类似地，可以为其他字段编写测试用例
    /**
     * 测试设置和获取 groupId
     */
    @Test
    public void testSetAndGetGroupId() throws Throwable {
        distributorVO.setGroupId("newGroup123");
        assertEquals("newGroup123", distributorVO.getGroupId());
    }

    /**
     * 测试设置和获取 nickname
     */
    @Test
    public void testSetAndGetNickname() throws Throwable {
        distributorVO.setNickname("newNickname");
        assertEquals("newNickname", distributorVO.getNickname());
    }

    /**
     * 测试设置和获取 avatarUrl
     */
    @Test
    public void testSetAndGetAvatarUrl() throws Throwable {
        distributorVO.setAvatarUrl("newUrl");
        assertEquals("newUrl", distributorVO.getAvatarUrl());
    }

    /**
     * 测试设置和获取 shareName
     */
    @Test
    public void testSetAndGetShareName() throws Throwable {
        distributorVO.setShareName("newShareName");
        assertEquals("newShareName", distributorVO.getShareName());
    }

    /**
     * 测试设置和获取 mobile
     */
    @Test
    public void testSetAndGetMobile() throws Throwable {
        distributorVO.setMobile("0987654321");
        assertEquals("0987654321", distributorVO.getMobile());
    }

    /**
     * 测试设置和获取 wechatNumber
     */
    @Test
    public void testSetAndGetWechatNumber() throws Throwable {
        distributorVO.setWechatNumber("newWechat123");
        assertEquals("newWechat123", distributorVO.getWechatNumber());
    }

    /**
     * 测试设置和获取 status
     */
    @Test
    public void testSetAndGetStatus() throws Throwable {
        distributorVO.setStatus(2);
        assertEquals(Integer.valueOf(2), distributorVO.getStatus());
    }

    /**
     * 测试设置和获取 actualName
     */
    @Test
    public void testSetAndGetActualName() throws Throwable {
        distributorVO.setActualName("newActualName");
        assertEquals("newActualName", distributorVO.getActualName());
    }

}
