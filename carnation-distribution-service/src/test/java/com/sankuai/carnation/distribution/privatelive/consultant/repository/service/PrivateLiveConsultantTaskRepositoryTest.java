package com.sankuai.carnation.distribution.privatelive.consultant.repository.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantTaskExample;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantTaskMapper;
import com.sankuai.carnation.distribution.utils.MobileTokenUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantTaskRepositoryTest {

    @InjectMocks
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private PrivateLiveConsultantTaskMapper privateLiveConsultantTaskMapper;

    @Mock
    private MobileTokenUtil mobileTokenUtil;

    @InjectMocks
    private PrivateLiveConsultantTaskRepository repository;

    @Before
    public void setUp() {
        when(privateLiveConsultantTaskMapper.countByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(1L);
    }

    private void setUpCommonMocks() throws Exception {
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(Collections.emptyList());
        // Correctly handle the TException by using doReturn when mocking the method that throws it
        doReturn("mockToken").when(mobileTokenUtil).getMobileToken(any(String.class));
    }

    @Test
    public void testCountByConditionLiveIdIsNull() throws Exception {
        long result = privateLiveConsultantTaskRepository.countByCondition(null, "consultantName", "phoneNo", Lists.newArrayList());
        assertEquals(0, result);
    }

    @Test
    public void testCountByConditionAllParamsAreNull() throws Exception {
        long result = privateLiveConsultantTaskRepository.countByCondition("liveId", null, null, Lists.newArrayList());
        assertEquals(1, result);
    }

    @Test
    public void testCountByConditionConsultantNameIsNotNull() throws Exception {
        long result = privateLiveConsultantTaskRepository.countByCondition("liveId", "consultantName", null, Lists.newArrayList());
        assertEquals(1, result);
    }

    @Test
    public void testCountByConditionPhoneNoIsNotNull() throws Exception {
        when(mobileTokenUtil.getMobileToken("phoneNo")).thenReturn("token");
        long result = privateLiveConsultantTaskRepository.countByCondition("liveId", null, "phoneNo", Lists.newArrayList());
        assertEquals(1, result);
    }

    @Test
    public void testCountByConditionAllParamsAreNotNull() throws Exception {
        when(mobileTokenUtil.getMobileToken("phoneNo")).thenReturn("token");
        long result = privateLiveConsultantTaskRepository.countByCondition("liveId", "consultantName", "phoneNo", Lists.newArrayList());
        assertEquals(1, result);
    }

    @Test
    public void testPageLoadByConditionLiveIdIsNull() throws Throwable {
        setUpCommonMocks();
        // arrange
        String liveId = null;
        String consultantName = "consultantName";
        String phoneNo = "phoneNo";
        int offset = 0;
        int limit = 10;
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.pageLoadByCondition(liveId, consultantName, phoneNo, Lists.newArrayList(), offset, limit);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPageLoadByConditionAllParamsAreNull() throws Throwable {
        setUpCommonMocks();
        // arrange
        String liveId = "liveId";
        String consultantName = null;
        String phoneNo = null;
        int offset = 0;
        int limit = 10;
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.pageLoadByCondition(liveId, consultantName, phoneNo, Lists.newArrayList(), offset, limit);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPageLoadByConditionConsultantNameIsNotNull() throws Throwable {
        setUpCommonMocks();
        // arrange
        String liveId = "liveId";
        String consultantName = "consultantName";
        String phoneNo = null;
        int offset = 0;
        int limit = 10;
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.pageLoadByCondition(liveId, consultantName, phoneNo, Lists.newArrayList(), offset, limit);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPageLoadByConditionPhoneNoIsNotNull() throws Throwable {
        setUpCommonMocks();
        // arrange
        String liveId = "liveId";
        String consultantName = null;
        String phoneNo = "phoneNo";
        int offset = 0;
        int limit = 10;
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.pageLoadByCondition(liveId, consultantName, phoneNo, Lists.newArrayList(), offset, limit);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPageLoadByConditionAllParamsAreNotNull() throws Throwable {
        setUpCommonMocks();
        // arrange
        String liveId = "liveId";
        String consultantName = "consultantName";
        String phoneNo = "phoneNo";
        int offset = 0;
        int limit = 10;
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.pageLoadByCondition(liveId, consultantName, phoneNo, Lists.newArrayList(), offset, limit);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testPageLoadByConditionBoundaryValues() throws Throwable {
        setUpCommonMocks();
        // arrange
        String liveId = "liveId";
        String consultantName = "consultantName";
        String phoneNo = "phoneNo";
        int offset = Integer.MAX_VALUE;
        int limit = Integer.MAX_VALUE;
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.pageLoadByCondition(liveId, consultantName, phoneNo, Lists.newArrayList(), offset, limit);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试查询任务通过任务ID和顾问ID，当返回非空列表
     */
    @Test
    public void testQueryTaskByTaskIdAndConsultantId_ReturnsNonEmptyList() {
        // arrange
        long taskId = 1L;
        long consultantId = 1L;
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(Collections.singletonList(task));
        // act
        PrivateLiveConsultantTask result = privateLiveConsultantTaskRepository.queryTaskByTaskIdAndConsultantId(taskId, consultantId);
        // assert
        assertNotNull(result);
        verify(privateLiveConsultantTaskMapper, times(1)).selectByExample(any(PrivateLiveConsultantTaskExample.class));
    }

    /**
     * 测试查询任务通过任务ID和顾问ID，当返回空列表
     */
    @Test
    public void testQueryTaskByTaskIdAndConsultantId_ReturnsEmptyList() {
        // arrange
        long taskId = 1L;
        long consultantId = 1L;
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(Collections.emptyList());
        // act
        PrivateLiveConsultantTask result = privateLiveConsultantTaskRepository.queryTaskByTaskIdAndConsultantId(taskId, consultantId);
        // assert
        assertNull(result);
        verify(privateLiveConsultantTaskMapper, times(1)).selectByExample(any(PrivateLiveConsultantTaskExample.class));
    }

    /**
     * 测试 loadByTypeAndConsultantId 方法，当 consultantIds 为空时
     */
    @Test
    public void testLoadByTypeAndConsultantIdWithEmptyConsultantIds() {
        // arrange
        String liveId = "live123";
        Integer taskType = 1;
        List<Long> consultantIds = Collections.emptyList();
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(Collections.emptyList());
        // act
        List<PrivateLiveConsultantTask> result = repository.loadByTypeAndConsultantId(liveId, taskType, consultantIds);
        // assert
        assertTrue(result.isEmpty());
        verify(privateLiveConsultantTaskMapper, times(1)).selectByExample(any(PrivateLiveConsultantTaskExample.class));
    }

    /**
     * 测试 loadByTypeAndConsultantId 方法，当 consultantIds 不为空时
     */
    @Test
    public void testLoadByTypeAndConsultantIdWithNonEmptyConsultantIds() {
        // arrange
        String liveId = "live123";
        Integer taskType = 1;
        List<Long> consultantIds = Arrays.asList(1L, 2L);
        List<PrivateLiveConsultantTask> expectedTasks = Arrays.asList(new PrivateLiveConsultantTask());
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(expectedTasks);
        // act
        List<PrivateLiveConsultantTask> result = repository.loadByTypeAndConsultantId(liveId, taskType, consultantIds);
        // assert
        assertFalse(result.isEmpty());
        assertEquals(expectedTasks, result);
        verify(privateLiveConsultantTaskMapper, times(1)).selectByExample(any(PrivateLiveConsultantTaskExample.class));
    }

    /**
     * 测试 loadByTypeAndConsultantId 方法，当 taskType 为 null 时
     */
    @Test
    public void testLoadByTypeAndConsultantIdWithNullTaskType() {
        // arrange
        String liveId = "live123";
        Integer taskType = null;
        List<Long> consultantIds = Arrays.asList(1L, 2L);
        List<PrivateLiveConsultantTask> expectedTasks = Arrays.asList(new PrivateLiveConsultantTask());
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(expectedTasks);
        // act
        List<PrivateLiveConsultantTask> result = repository.loadByTypeAndConsultantId(liveId, taskType, consultantIds);
        // assert
        assertFalse(result.isEmpty());
        assertEquals(expectedTasks, result);
        verify(privateLiveConsultantTaskMapper, times(1)).selectByExample(any(PrivateLiveConsultantTaskExample.class));
    }
}
