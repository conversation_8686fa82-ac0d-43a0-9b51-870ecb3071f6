package com.sankuai.carnation.distribution.distributionplan.acl;

import com.dianping.general.unified.search.api.dealgroupsearch.GeneralDealGroupSearchService;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.DealGroupSearchDTO;
import com.dianping.general.unified.search.api.dealgroupsearch.response.GeneralDealGroupSearchResponse;
import com.sankuai.carnation.distribution.distributionplan.acl.impl.GeneralDealGroupSearchAclImpl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealGroupSearchRequest;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralDealGroupSearchResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@RunWith(MockitoJUnitRunner.class)
public class GeneralDealGroupSearchAclImplTest {

    @InjectMocks
    private GeneralDealGroupSearchAclImpl generalDealGroupSearchAcl;

    @Mock
    private GeneralDealGroupSearchService generalDealGroupSearchService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    /**
     * 测试searchDealGroups方法，当searchRequest为null时抛出RuntimeException
     */
    @Test
    public void testSearchDealGroupsWhenSearchRequestIsNull() {
        GeneralDealGroupSearchResult generalDealGroupSearchResult = generalDealGroupSearchAcl.searchDpDealGroups(null);
        Assert.assertEquals(generalDealGroupSearchResult, GeneralDealGroupSearchResult.emptyResult());

    }

    /**
     * 测试searchDealGroups方法，当响应为null时抛出DistributionPlanException
     */
    @Test
    public void testSearchDealGroupsWhenResponseIsNull() {
        DealGroupSearchRequest searchRequest = buildRequest();
        when(generalDealGroupSearchService.searchDealGroups(any())).thenReturn(null);

        GeneralDealGroupSearchResult generalDealGroupSearchResult = generalDealGroupSearchAcl.searchDpDealGroups(searchRequest);
        Assert.assertEquals(generalDealGroupSearchResult, GeneralDealGroupSearchResult.emptyResult());

    }

    /**
     * 测试searchDealGroups方法，当响应的success为false时抛出DistributionPlanException
     */
    @Test
    public void testSearchDealGroupsWhenResponseIsNotSuccess() {
        DealGroupSearchRequest searchRequest = buildRequest();
        GeneralDealGroupSearchResponse response = new GeneralDealGroupSearchResponse();
        response.setSuccess(false);
        response.setResultMessage("失败");
        when(generalDealGroupSearchService.searchDealGroups(any())).thenReturn(response);

        GeneralDealGroupSearchResult generalDealGroupSearchResult = generalDealGroupSearchAcl.searchDpDealGroups(searchRequest);
        Assert.assertEquals(generalDealGroupSearchResult, GeneralDealGroupSearchResult.emptyResult());

    }

    /**
     * 测试searchDealGroups方法，当响应的result为null时抛出DistributionPlanException
     */
    @Test
    public void testSearchDealGroupsWhenResultIsNull() {
        DealGroupSearchRequest searchRequest = buildRequest();
        GeneralDealGroupSearchResponse response = new GeneralDealGroupSearchResponse();
        response.setSuccess(true);
        when(generalDealGroupSearchService.searchDealGroups(any())).thenReturn(response);

        GeneralDealGroupSearchResult generalDealGroupSearchResult = generalDealGroupSearchAcl.searchDpDealGroups(searchRequest);
        Assert.assertEquals(generalDealGroupSearchResult, GeneralDealGroupSearchResult.emptyResult());

    }

    /**
     * 测试searchDealGroups方法，正常情况
     */
    @Test
    public void testSearchDealGroupsSuccess() {
        DealGroupSearchRequest searchRequest = buildRequest();
        GeneralDealGroupSearchResponse response = new GeneralDealGroupSearchResponse();
        response.setSuccess(true);
        DealGroupSearchDTO dto = new DealGroupSearchDTO();
        dto.setDealGroupId(1L);
        response.setResult(Collections.singletonList(dto));
        response.setTotalHits(1L);
        when(generalDealGroupSearchService.searchDealGroups(any())).thenReturn(response);

        GeneralDealGroupSearchResult result = generalDealGroupSearchAcl.searchDpDealGroups(searchRequest);

        Assert.assertNotNull(result.getProductIdList());
        Assert.assertEquals(1, result.getProductIdList().size());
    }


    private DealGroupSearchRequest buildRequest() {
        return DealGroupSearchRequest.builder()
                .customerId(1L)
                .pageSize(10)
                .pageNo(1)
                .productName("测试")
                .productId(1L)
                .dpShopId(1L)
                .build();

    }

}