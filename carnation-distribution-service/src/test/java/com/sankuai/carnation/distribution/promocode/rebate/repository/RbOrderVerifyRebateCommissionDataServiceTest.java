package com.sankuai.carnation.distribution.promocode.rebate.repository;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.carnation.distribution.promocode.rebate.repository.dao.RbOrderVerifyRebateCommissionExtMapper;
import com.sankuai.carnation.distribution.promocode.rebate.repository.dao.RbOrderVerifyRebateCommissionMapper;
import com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission;
import com.sankuai.carnation.distribution.promocode.rebate.repository.example.RbOrderVerifyRebateCommissionExample;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RbOrderVerifyRebateCommissionDataServiceTest {

    @Mock
    private RbOrderVerifyRebateCommissionExtMapper mapper;

    @InjectMocks
    private RbOrderVerifyRebateCommissionDataService service;

    @Before
    public void setUp() {
        // 初始化操作，如有需要
    }

    /**
     * 测试活动ID、用户ID和平台标识均有效，且groupNewUser为null的场景
     */
    @Test
    public void testLoadByActivityIdAndUserWithValidInputsAndGroupNewUserIsNull() {
        // arrange
        Long activityId = 1L;
        long userId = 1L;
        int platform = 1;
        String groupNewUser = null;
        when(mapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        List<RbOrderVerifyRebateCommission> result = service.loadByActivityIdAndUser(activityId, userId, platform, groupNewUser);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试活动ID、用户ID和平台标识均有效，且groupNewUser非null的场景
     */
    @Test
    public void testLoadByActivityIdAndUserWithValidInputsAndGroupNewUserIsNotNull() {
        // arrange
        Long activityId = 1L;
        long userId = 1L;
        int platform = 1;
        String groupNewUser = "YES";
        when(mapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        List<RbOrderVerifyRebateCommission> result = service.loadByActivityIdAndUser(activityId, userId, platform, groupNewUser);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试deleteByOrder方法，当mapper.deleteByExample返回大于0时
     */
    @Test
    public void testDeleteByOrder_ReturnTrue() {
        // arrange
        int bizType = 1;
        String orderId = "orderId";
        when(mapper.deleteByExample(any(RbOrderVerifyRebateCommissionExample.class))).thenReturn(1);
        // act
        boolean result = service.deleteByOrder(bizType, orderId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试deleteByOrder方法，当mapper.deleteByExample返回0时
     */
    @Test
    public void testDeleteByOrder_ReturnFalse() {
        // arrange
        int bizType = 1;
        String orderId = "orderId";
        when(mapper.deleteByExample(any(RbOrderVerifyRebateCommissionExample.class))).thenReturn(0);
        // act
        boolean result = service.deleteByOrder(bizType, orderId);
        // assert
        assertFalse(result);
    }

}
