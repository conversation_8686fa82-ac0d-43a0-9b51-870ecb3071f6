package com.sankuai.carnation.distribution.privatelive.distribution.startar.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLivePlanDistributorGroupAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryDistributorGroupByPlanRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryDistributorGroupForAnchorRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryPlanAndDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryDistributorGroupByPlanResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryDistributorGroupForAnchorResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryPlanAndDistributorGroupResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class PrivateLivePlanDistributorGroupServiceImplTest {

    @InjectMocks
    private PrivateLivePlanDistributorGroupServiceImpl privateLivePlanDistributorGroupServiceImpl;

    @Mock
    private PrivateLivePlanDistributorGroupAppService privateLivePlanDistributorGroupAppService;

    @InjectMocks
    private PrivateLivePlanDistributorGroupServiceImpl privateLivePlanDistributorGroupService;

    @InjectMocks
    private PrivateLivePlanDistributorGroupServiceImpl service;

    @Mock
    private PrivateLivePlanDistributorGroupAppService appService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当返回空列表时
     */
    @Test
    public void testQueryPrivateLiveDistributorGroupListReturnsEmptyList() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        QueryDistributorGroupByPlanResponse mockResponse = new QueryDistributorGroupByPlanResponse(0L, new ArrayList<>());
        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenReturn(mockResponse);
        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);
        assertNotNull(response);
        assertTrue(response.getData().getDistributorGroupList().isEmpty());
        verify(privateLivePlanDistributorGroupAppService, times(1)).queryDistributorGroupByPlan(any());
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当返回非空列表时
     */
    @Test
    public void testQueryPrivateLiveDistributorGroupListReturnsNonEmptyList() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        List<QueryDistributorGroupByPlanResponse.DistributorGroup> distributorGroups = new ArrayList<>();
        distributorGroups.add(new QueryDistributorGroupByPlanResponse.DistributorGroup());
        QueryDistributorGroupByPlanResponse mockResponse = new QueryDistributorGroupByPlanResponse(1L, distributorGroups);
        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenReturn(mockResponse);
        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);
        assertNotNull(response);
        assertFalse(response.getData().getDistributorGroupList().isEmpty());
        assertEquals(1, response.getData().getDistributorGroupList().size());
        verify(privateLivePlanDistributorGroupAppService, atLeastOnce()).queryDistributorGroupByPlan(any());
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当appService抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryPrivateLiveDistributorGroupListThrowsException() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenThrow(new RuntimeException());
        privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当分页查询多次且每次返回结果大小等于pageSize时
     */
    @Test
    public void testQueryPrivateLiveDistributorGroupListWithMultiplePages() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        int pageSize = 20;
        // 构造两页数据，每页数据量等于pageSize
        List<QueryDistributorGroupByPlanResponse.DistributorGroup> page1Groups = new ArrayList<>();
        for (int i = 0; i < pageSize; i++) {
            page1Groups.add(new QueryDistributorGroupByPlanResponse.DistributorGroup());
        }
        List<QueryDistributorGroupByPlanResponse.DistributorGroup> page2Groups = new ArrayList<>();
        // 第二页只有一条数据
        page2Groups.add(new QueryDistributorGroupByPlanResponse.DistributorGroup());
        QueryDistributorGroupByPlanResponse page1Response = new QueryDistributorGroupByPlanResponse((long) pageSize, page1Groups);
        QueryDistributorGroupByPlanResponse page2Response = new QueryDistributorGroupByPlanResponse(1L, page2Groups);
        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenReturn(page1Response, page2Response);
        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);
        assertNotNull(response);
        // 总数据量应为pageSize + 1
        assertEquals(pageSize + 1, response.getData().getDistributorGroupList().size());
        verify(privateLivePlanDistributorGroupAppService, times(2)).queryDistributorGroupByPlan(any());
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当第一次查询结果大小正好等于pageSize，但后续没有更多数据时
     */
    @Test
    public void testQueryPrivateLiveDistributorGroupListWithExactPageSizeResult() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        int pageSize = 20;
        // 构造第一次查询结果，数据量等于pageSize
        List<QueryDistributorGroupByPlanResponse.DistributorGroup> page1Groups = new ArrayList<>();
        for (int i = 0; i < pageSize; i++) {
            page1Groups.add(new QueryDistributorGroupByPlanResponse.DistributorGroup());
        }
        // 第二次查询返回空列表
        List<QueryDistributorGroupByPlanResponse.DistributorGroup> page2Groups = new ArrayList<>();
        QueryDistributorGroupByPlanResponse page1Response = new QueryDistributorGroupByPlanResponse((long) pageSize, page1Groups);
        QueryDistributorGroupByPlanResponse page2Response = new QueryDistributorGroupByPlanResponse(0L, page2Groups);
        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenReturn(page1Response, page2Response);
        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);
        assertNotNull(response);
        // 总数据量应为pageSize
        assertEquals(pageSize, response.getData().getDistributorGroupList().size());
        verify(privateLivePlanDistributorGroupAppService, times(2)).queryDistributorGroupByPlan(any());
    }

    /**
     * 测试queryDistributorGroupForAnchor方法，当请求参数完全有效时的场景
     */
    @Test
    public void testQueryDistributorGroupForAnchorSuccess() throws Exception {
        // arrange
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(1L, "liveId", 1L, 10L);
        QueryDistributorGroupForAnchorResponse expectedResponse = new QueryDistributorGroupForAnchorResponse();
        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupForAnchor(request)).thenReturn(expectedResponse);
        // act
        RemoteResponse<QueryDistributorGroupForAnchorResponse> result = privateLivePlanDistributorGroupServiceImpl.queryDistributorGroupForAnchor(request);
        // assert
        assertNotNull(result);
        assertEquals(expectedResponse, result.getData());
    }

    /**
     * 测试queryDistributorGroupForAnchor方法，当请求参数为null时的场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryDistributorGroupForAnchorRequestIsNull() throws Exception {
        // arrange
        QueryDistributorGroupForAnchorRequest request = null;
        // act
        privateLivePlanDistributorGroupServiceImpl.queryDistributorGroupForAnchor(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryDistributorGroupForAnchor方法，当直播id为null时的场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryDistributorGroupForAnchorLiveIdIsNull() throws Exception {
        // arrange
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(1L, null, 1L, 10L);
        // act
        privateLivePlanDistributorGroupServiceImpl.queryDistributorGroupForAnchor(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryDistributorGroupForAnchor方法，当主播id为null时的场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryDistributorGroupForAnchorAnchorIdIsNull() throws Exception {
        // arrange
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(null, "liveId", 1L, 10L);
        // act
        privateLivePlanDistributorGroupServiceImpl.queryDistributorGroupForAnchor(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryDistributorGroupForAnchor方法，当页码或页数为null时的场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryDistributorGroupForAnchorPageNumOrPageSizeIsNull() throws Exception {
        // arrange
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(1L, "liveId", null, null);
        // act
        privateLivePlanDistributorGroupServiceImpl.queryDistributorGroupForAnchor(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试 queryPlanAndDistributorGroup 方法，当请求参数完全正确时，应返回成功的响应。
     */
    @Test
    public void testQueryPlanAndDistributorGroupSuccess() throws Exception {
        // arrange
        QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest("liveId", 1L);
        QueryPlanAndDistributorGroupResponse expectedResponse = new QueryPlanAndDistributorGroupResponse("liveId", 1L);
        when(privateLivePlanDistributorGroupAppService.queryPlanAndDistributorGroup(request)).thenReturn(expectedResponse);
        // act
        RemoteResponse<QueryPlanAndDistributorGroupResponse> result = privateLivePlanDistributorGroupServiceImpl.queryPlanAndDistributorGroup(request);
        // assert
        assertNotNull(result);
        assertEquals("liveId", result.getData().getLiveId());
        assertEquals(Long.valueOf(1), result.getData().getDistributorGroupId());
    }

    /**
     * 测试 queryPlanAndDistributorGroup 方法，当请求参数为 null 时，应抛出 IllegalArgumentException 异常。
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryPlanAndDistributorGroupWithNullRequest() throws Exception {
        // arrange
        QueryPlanAndDistributorGroupRequest request = null;
        // act
        privateLivePlanDistributorGroupServiceImpl.queryPlanAndDistributorGroup(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试 queryPlanAndDistributorGroup 方法，当 liveId 为 null 时，应抛出 IllegalArgumentException 异常。
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryPlanAndDistributorGroupWithNullLiveId() throws Exception {
        // arrange
        QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest(null, 1L);
        // act
        privateLivePlanDistributorGroupServiceImpl.queryPlanAndDistributorGroup(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试 queryPlanAndDistributorGroup 方法，当 distributorGroupId 为 null 时，应抛出 IllegalArgumentException 异常。
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryPlanAndDistributorGroupWithNullDistributorGroupId() throws Exception {
        // arrange
        QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest("liveId", null);
        // act
        privateLivePlanDistributorGroupServiceImpl.queryPlanAndDistributorGroup(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试正常情况下的分页查询
     */
//    @Test
//    public void testPageQueryPrivateLiveDistributorGroupListSuccess() {
//        // arrange
//        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("liveId", 1, 10, false, false);
//        QueryDistributorGroupByPlanResponse expectedResponse = new QueryDistributorGroupByPlanResponse();
//        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(request)).thenReturn(expectedResponse);
//        // act
//        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupService.pageQueryPrivateLiveDistributorGroupList(request);
//        // assert
//        assertNotNull(response);
//        assertEquals(expectedResponse, response.getData());
//    }

    /**
     * 测试请求参数为null时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListRequestNull() {
        // arrange
        QueryDistributorGroupByPlanRequest request = null;
        // act
        privateLivePlanDistributorGroupService.pageQueryPrivateLiveDistributorGroupList(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试直播id为空时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListLiveIdBlank() {
        // arrange
        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("", 1, 10, false, false, false);
        // act
        privateLivePlanDistributorGroupService.pageQueryPrivateLiveDistributorGroupList(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试页码为null或小于等于0时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListPageNumInvalid() {
        // arrange
        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("liveId", 0, 10, false, false, false);
        // act
        privateLivePlanDistributorGroupService.pageQueryPrivateLiveDistributorGroupList(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试页数量为null或大于20时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListPageSizeInvalid() {
        // arrange
        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("liveId", 1, 21, false, false, false);
        // act
        privateLivePlanDistributorGroupService.pageQueryPrivateLiveDistributorGroupList(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryDistributorGroupCountByLiveId方法，当liveIds为空时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryDistributorGroupCountByLiveIdWithEmptyLiveIds() throws Throwable {
        // arrange
        List<String> liveIds = Arrays.asList();
        // act
        service.queryDistributorGroupsCount(liveIds);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryDistributorGroupCountByLiveId方法，当liveIds为null时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryDistributorGroupCountByLiveIdWithNullLiveIds() throws Throwable {
        // arrange
        List<String> liveIds = null;
        // act
        service.queryDistributorGroupsCount(liveIds);
        // assert is handled by the expected exception
    }


}
