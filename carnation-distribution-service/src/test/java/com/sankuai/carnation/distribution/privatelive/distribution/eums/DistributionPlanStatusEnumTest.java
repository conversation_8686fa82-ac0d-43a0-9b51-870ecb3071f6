package com.sankuai.carnation.distribution.privatelive.distribution.eums;

import org.junit.Test;
import static org.junit.Assert.*;

public class DistributionPlanStatusEnumTest {

    /**
     * 测试枚举值WAITING是否正确初始化
     */
    @Test
    public void testEnumValueWAITING() {
        assertEquals("待配置", DistributionPlanStatusEnum.WAITING.getDesc());
        assertEquals(0, DistributionPlanStatusEnum.WAITING.getCode());
    }

    /**
     * 测试枚举值ENABLE是否正确初始化
     */
    @Test
    public void testEnumValueENABLE() {
        assertEquals("生效中", DistributionPlanStatusEnum.ENABLE.getDesc());
        assertEquals(1, DistributionPlanStatusEnum.ENABLE.getCode());
    }

    /**
     * 测试枚举值DISABLE是否正确初始化
     */
    @Test
    public void testEnumValueDISABLE() {
        assertEquals("已停用", DistributionPlanStatusEnum.DISABLE.getDesc());
        assertEquals(2, DistributionPlanStatusEnum.DISABLE.getCode());
    }

    /**
     * 测试枚举值EXPIRE是否正确初始化
     */
    @Test
    public void testEnumValueEXPIRE() {
        assertEquals("已失效", DistributionPlanStatusEnum.EXPIRE.getDesc());
        assertEquals(3, DistributionPlanStatusEnum.EXPIRE.getCode());
    }

    /**
     * 测试枚举值FAIL是否正确初始化
     */
    @Test
    public void testEnumValueFAIL() {
        assertEquals("配置失败", DistributionPlanStatusEnum.FAIL.getDesc());
        assertEquals(4, DistributionPlanStatusEnum.FAIL.getCode());
    }
}
