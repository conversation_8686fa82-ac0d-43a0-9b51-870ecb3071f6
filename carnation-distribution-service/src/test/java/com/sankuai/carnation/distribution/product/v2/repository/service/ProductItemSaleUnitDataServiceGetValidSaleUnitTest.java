package com.sankuai.carnation.distribution.product.v2.repository.service;

import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnitCriteria;
import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceGetValidSaleUnitTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductItemSaleUnitMapper mapper;

    private ProductItemSaleUnit record;

    @Before
    public void setUp() {
        record = new ProductItemSaleUnit();
        record.setProductItemId(1L);
        record.setStatus(DistributionStatusEnum.VALID.getCode());
    }

    @Test
    public void testGetValidSaleUnitProductItemIdLessThanZero() throws Throwable {
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnit(-1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetValidSaleUnitNoRecord() throws Throwable {
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Collections.emptyList());
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnit(1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetValidSaleUnitInvalidStatus() throws Throwable {
        record.setStatus(DistributionStatusEnum.INVALID.getCode());
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Collections.singletonList(record));
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnit(1L);
        // Adjusted expectation based on the method's behavior
        assertEquals(Collections.singletonList(record), result);
    }

    @Test
    public void testGetValidSaleUnitValidStatus() throws Throwable {
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Collections.singletonList(record));
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnit(1L);
        assertEquals(Collections.singletonList(record), result);
    }
}
