package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.dztrade.enums.RelatedOrderTypeEnum;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.common.enums.AmountSubType;
import com.dianping.pay.order.common.enums.AmountType;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.api.common.enums.UnifiedOrderField;
import com.dianping.pay.unifiedorder.api.common.enums.UnifiedOrderSkuField;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderDiscountDTO;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderPaymentDetailDTO;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderSKUDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.idmapper.service.CityMapperService;
import com.meituan.nibtp.trade.client.buy.enums.OrderExtraFieldEnum;
import com.meituan.nibtp.trade.client.buy.enums.OrderPaymentDetailExtraFieldEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.*;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.common.*;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.live.OrderLiveParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.StaffCodeBargainBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.TechnicianDistributionExtParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.DzOrderAdaptor;
import com.sankuai.carnation.distribution.intention.domain.calculate.utils.ProductTypeAnalyser;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelRunningTaskDataService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import com.sankuai.carnation.distribution.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/11/25
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class DzOrderAdaptorTest {

    @InjectMocks
    private DzOrderAdaptor dzOrderAdaptor;

    @Mock
    private GetUnifiedOrderService getUnifiedOrderService;

    @Mock
    private CityMapperService cityMapperService;

    @Mock
    private OrderChannelRunningTaskDataService runningTaskDataService;

    private UnifiedOrderWithId unifiedOrderWithId = new UnifiedOrderWithId();

    private OrderInfoBO orderInfoBO = new OrderInfoBO();

    private static final String PASS_PARAM_DISTRIBUTION_KEY = "DISTRIBUTION_BASIC_INFO";

    private static final String PROMOTE_CHANNEL_KEY = "PROMOTE_CHANNEL_INFO";

    private static final String M_LIVE_DISTRIBUTION_KEY = "m_live";

    private static final String M_LIVE_ID_KEY = "mLiveId";

    private static final int SKU_PURCHASE_TO_CONSUME_RATIO = 6032;

    @Before
    public void setUp() {
        // 初始化统一订单模型
        unifiedOrderWithId.setUnifiedOrderId("123");
        unifiedOrderWithId.setLongOrderId(123L);
        unifiedOrderWithId.setAddTime(DateUtils.parse("2024-12-31 12:00:00"));
        unifiedOrderWithId.setPaySuccessTime(DateUtils.parse("2024-12-31 12:00:00"));
        unifiedOrderWithId.setBuySuccessTime(DateUtils.parse("2024-12-31 12:00:00"));
        unifiedOrderWithId.setPlatform(20050400);
        unifiedOrderWithId.setBizType(6);
        UnifiedOrderSKUDTO skudto = new UnifiedOrderSKUDTO();
        Map<Integer,String> skuExtraFields = Maps.newHashMap();
        skuExtraFields.put(6032, "1");
        skuExtraFields.put(UnifiedOrderSkuField.mtDealId.fieldKey, "3");
        skudto.setSkuExtraFields(skuExtraFields);
        skudto.setQuantity(1);
        skudto.setSkuId("2");
        skudto.setSpugId("3");
        skudto.setSkuTitle("skuTitle");
        skudto.setUnitValue(BigDecimal.ONE);
        unifiedOrderWithId.setSkus(Lists.newArrayList(skudto));
        unifiedOrderWithId.setMtUserId(456L);
        unifiedOrderWithId.setUserId(456L);
        unifiedOrderWithId.setLongMtShopId(789L);
        unifiedOrderWithId.setLongShopId(789L);
        Map<Integer, String> extraFields = Maps.newHashMap();
        extraFields.put(UnifiedOrderField.distributionParam.fieldKey, "distributionCode");
        unifiedOrderWithId.setExtraFields(extraFields);
        PassParamDistributionBO passParamDistributionBO = new PassParamDistributionBO();
        PassParamDistributionInfoBO passParamDistributionInfoBO = new PassParamDistributionInfoBO();
        passParamDistributionInfoBO.setDistributionType("distributionType");
        passParamDistributionInfoBO.setBizExtend("bizExtend");
        passParamDistributionBO.setDistributionInfoList(Lists.newArrayList(passParamDistributionInfoBO));
        Map<String, String> nibExtraFields = Maps.newHashMap();
        nibExtraFields.put("DISTRIBUTION_BASIC_INFO", JSON.toJSONString(passParamDistributionBO));
        nibExtraFields.put("dpCityId", "1");
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);
        UnifiedOrderPaymentDetailDTO paymentDetailDTO = new UnifiedOrderPaymentDetailDTO();
        paymentDetailDTO.setPaymentDetailID("1");
        paymentDetailDTO.setAmountId("2");
        paymentDetailDTO.setAmount(BigDecimal.ONE);
        paymentDetailDTO.setAmountType(1);
        paymentDetailDTO.setSkuId("3");
        paymentDetailDTO.setPaymentReceiptId("4");
        paymentDetailDTO.setOrderPaymentId("5");
        paymentDetailDTO.setPayPlatform(20050400);
        paymentDetailDTO.setAddTime(DateUtils.parse("2024-12-31 12:00:00"));
        unifiedOrderWithId.setPaymentDetails(Lists.newArrayList(paymentDetailDTO));
        unifiedOrderWithId.setCityId(1);
        UnifiedOrderDiscountDTO unifiedOrderDiscountDTO = new UnifiedOrderDiscountDTO();
        unifiedOrderDiscountDTO.setDiscountType(AmountType.COUPON.value);
        unifiedOrderDiscountDTO.setDiscountAmount(BigDecimal.ONE);
        unifiedOrderDiscountDTO.setDiscountId("1");
        unifiedOrderWithId.setDiscounts(Lists.newArrayList(unifiedOrderDiscountDTO));
        unifiedOrderWithId.setTotalAmount(BigDecimal.ONE);

        // 初始化订单bo
        orderInfoBO.setOrderType(DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode());
        orderInfoBO.setOrderId("123");
        orderInfoBO.setLongOrderId(unifiedOrderWithId.getLongOrderId());
        orderInfoBO.setOrderTime(unifiedOrderWithId.getAddTime());
        orderInfoBO.setPayTime(unifiedOrderWithId.getPaySuccessTime());
        orderInfoBO.setBuySuccessTime(unifiedOrderWithId.getBuySuccessTime());
        orderInfoBO.setProductType(ProductTypeAnalyser.fromOrderBizType(unifiedOrderWithId.getBizType()).getCode());
        orderInfoBO.setOrderBizType(unifiedOrderWithId.getBizType());
        orderInfoBO.setProductId(analyseProductId(unifiedOrderWithId));
        orderInfoBO.setDpProductId(NumberUtils.toLong(unifiedOrderWithId.getFirstSku().getSpugId()));
        orderInfoBO.setPlatform(PlatformEnum.MT.getCode());
        orderInfoBO.setUserId(unifiedOrderWithId.getMtUserId());
        orderInfoBO.setPayPlatform(PayPlatform.getByCode(unifiedOrderWithId.getPlatform()));
        orderInfoBO.setShopId(unifiedOrderWithId.getLongMtShopId());

        orderInfoBO.setDistributionCode(getOrderDistributionCode(unifiedOrderWithId));
        orderInfoBO.setTechExtParam(getTechExtParam(unifiedOrderWithId));
        orderInfoBO.setExtDistributionInfo(getPassParam(unifiedOrderWithId));
        orderInfoBO.setDistributionBasicInfo(getDistributionBasicInfo(unifiedOrderWithId));
        orderInfoBO.setPaymentDetailList(getOrderPayment(unifiedOrderWithId));
        orderInfoBO.setSkuList(getOrderSkuList(unifiedOrderWithId));
        orderInfoBO.setCityId(1);
        orderInfoBO.setDiscountList(pickUpDiscount(unifiedOrderWithId));
        orderInfoBO.setLiveParam(getLiveParam(unifiedOrderWithId));
        orderInfoBO.setTradePlatform(unifiedOrderWithId.getPlatform());
        orderInfoBO.setTotalAmount(unifiedOrderWithId.getTotalAmount());
        orderInfoBO.setStaffCodeBargainBO(getStaffCodeBargainBO(unifiedOrderWithId, orderInfoBO));
        orderInfoBO.setCommissionFlag(getCommissionFlag(unifiedOrderWithId));

        orderInfoBO.setActualPayAmount(getOrderActualPayAmount(unifiedOrderWithId));
        orderInfoBO.setExtMagicMemberCouponInfo(getMagicMemberCoupon(unifiedOrderWithId));
    }

    @Test
    public void testStaffBargainMainOrderNoExt() {
        Map<String, String> nibExtraFields = unifiedOrderWithId.getNibExtraFields();
        nibExtraFields.put("general.ext.extraOrderType", String.valueOf(RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId()));
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);

        // arrange
        Mockito.when(getUnifiedOrderService.getByUnifiedOrderId(any())).thenReturn(unifiedOrderWithId);
        Mockito.when(cityMapperService.dp2mt(anyInt())).thenReturn(1);

        // act
        OrderInfoBO result = dzOrderAdaptor.getOrderInfo("123");

        // assert
        assertEquals(null, result.getStaffCodeBargainBO());
    }

    @Test
    public void testStaffBargainMainOrderNoCodeId() {
        Map<String, String> nibExtraFields = unifiedOrderWithId.getNibExtraFields();
        PassParamDistributionBO passParamDistributionBO = new PassParamDistributionBO();
        PassParamDistributionInfoBO passParamDistributionInfoBO = new PassParamDistributionInfoBO();
        passParamDistributionInfoBO.setDistributionType("promotion_code");
        Map<String, String> bizExtendMap = Maps.newHashMap();
        passParamDistributionInfoBO.setBizExtend(JSON.toJSONString(bizExtendMap));
        passParamDistributionBO.setDistributionInfoList(Lists.newArrayList(passParamDistributionInfoBO));
        nibExtraFields.put("DISTRIBUTION_BASIC_INFO", JSON.toJSONString(passParamDistributionBO));
        nibExtraFields.put("general.ext.extraOrderType", String.valueOf(RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId()));
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);

        // arrange
        Mockito.when(getUnifiedOrderService.getByUnifiedOrderId(any())).thenReturn(unifiedOrderWithId);
        Mockito.when(cityMapperService.dp2mt(anyInt())).thenReturn(1);

        // act
        OrderInfoBO result = dzOrderAdaptor.getOrderInfo("123");

        // assert
        assertEquals(null, result.getStaffCodeBargainBO());
    }

    @Test
    public void testStaffBargainBalanceOrder() {
        Map<String, String> nibExtraFields = unifiedOrderWithId.getNibExtraFields();
        nibExtraFields.put("general.ext.extraOrderType", String.valueOf(RelatedOrderTypeEnum.DEAL_GROUP_BALANCE_PAYMENT_ORDER.getId()));
        nibExtraFields.put("general.ext.mainOrderId", "123");
        PrepayInfoBO prepayInfoBO = new PrepayInfoBO();
        prepayInfoBO.setTotalAmount(new BigDecimal(1.1));
        prepayInfoBO.setPrepayAmount(new BigDecimal(2.2));
        prepayInfoBO.setBalancePaymentAmount(new BigDecimal(3.3));
        nibExtraFields.put("general.ext.prepayInfo", JSON.toJSONString(prepayInfoBO));
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);

        // arrange
        Mockito.when(getUnifiedOrderService.getByUnifiedOrderId(any())).thenReturn(unifiedOrderWithId);
        Mockito.when(cityMapperService.dp2mt(anyInt())).thenReturn(1);

        OrderChannelTaskRunningResultBO runningResultBO = new OrderChannelTaskRunningResultBO();
        runningResultBO.setBusinessChannel(DistributionBusinessChannelEnum.STAFF_BARGAIN_MULTIPLE_ORDERS.getCode());
        runningResultBO.setCalculateResult(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode());
        StaffCodeBargainBO staffCodeBargain = new StaffCodeBargainBO();
        staffCodeBargain.setTechId(1);
        staffCodeBargain.setStaffBargainCodeId(1L);
        runningResultBO.setStaffCodeBargain(staffCodeBargain);
        DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        runningTask.setResult(JSON.toJSONString(runningResultBO));
        Mockito.when(runningTaskDataService.forceGetTaskByOrderAndType(anyInt(), anyString(), anyInt())).thenReturn(runningTask);

        // act
        OrderInfoBO result = dzOrderAdaptor.getOrderInfo("123");

        // assert
        assertEquals(1L, result.getStaffCodeBargainBO().getStaffBargainCodeId());
        assertEquals(1, result.getStaffCodeBargainBO().getTechId());
    }

    @Test
    public void testStaffBargainMainOrder() {
        Map<String, String> nibExtraFields = unifiedOrderWithId.getNibExtraFields();
        PassParamDistributionBO passParamDistributionBO = new PassParamDistributionBO();
        PassParamDistributionInfoBO passParamDistributionInfoBO = new PassParamDistributionInfoBO();
        passParamDistributionInfoBO.setDistributionType("promotion_code");
        Map<String, String> bizExtendMap = Maps.newHashMap();
        bizExtendMap.put("staffBargainCodeId", "1");
        bizExtendMap.put("techId", "1");

        passParamDistributionInfoBO.setBizExtend(JSON.toJSONString(bizExtendMap));
        passParamDistributionBO.setDistributionInfoList(Lists.newArrayList(passParamDistributionInfoBO));
        nibExtraFields.put("DISTRIBUTION_BASIC_INFO", JSON.toJSONString(passParamDistributionBO));
        nibExtraFields.put("general.ext.extraOrderType", String.valueOf(RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId()));

        PrepayInfoBO prepayInfoBO = new PrepayInfoBO();
        prepayInfoBO.setTotalAmount(new BigDecimal(1.1));
        prepayInfoBO.setPrepayAmount(new BigDecimal(2.2));
        prepayInfoBO.setBalancePaymentAmount(new BigDecimal(3.3));
        nibExtraFields.put("general.ext.prepayInfo", JSON.toJSONString(prepayInfoBO));
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);

        // arrange
        Mockito.when(getUnifiedOrderService.getByUnifiedOrderId(any())).thenReturn(unifiedOrderWithId);
        Mockito.when(cityMapperService.dp2mt(anyInt())).thenReturn(1);

        // act
        OrderInfoBO result = dzOrderAdaptor.getOrderInfo("123");

        // assert
        assertEquals(1L, result.getStaffCodeBargainBO().getStaffBargainCodeId());
        assertEquals(1, result.getStaffCodeBargainBO().getTechId());
    }

    @Test
    public void testMagicMemberCoupon() {
        Map<String, String> nibExtraFields = unifiedOrderWithId.getNibExtraFields();
        nibExtraFields.put(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey(), "true");
        unifiedOrderWithId.setNibExtraFields(nibExtraFields);

        List<UnifiedOrderPaymentDetailDTO> paymentDetailDTOList = unifiedOrderWithId.getPaymentDetails();
        UnifiedOrderPaymentDetailDTO magicMemberCouponPaymentDetailDTO = new UnifiedOrderPaymentDetailDTO();
        magicMemberCouponPaymentDetailDTO.setAmountSubType(AmountSubType.PLATFORM_MAGICAL_MEMBER_COUPON_SUB_TYPE.subType);
        Map<String, String> extraData = Maps.newHashMap();
        extraData.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_FLAG.getKey(), "true");
        extraData.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_ALLOWANCE_SCHEME.getKey(), "schemeId");
        extraData.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_INFLATED_FLAG.getKey(), "true");
        extraData.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_MMC_CATEGORY.getKey(), "mmc_buy");
        magicMemberCouponPaymentDetailDTO.setExtraData(extraData);
        paymentDetailDTOList.add(magicMemberCouponPaymentDetailDTO);
        unifiedOrderWithId.setPaymentDetails(paymentDetailDTOList);

        // arrange
        Mockito.when(getUnifiedOrderService.getByUnifiedOrderId(any())).thenReturn(unifiedOrderWithId);
        Mockito.when(cityMapperService.dp2mt(anyInt())).thenReturn(1);

        // act
        OrderInfoBO result = dzOrderAdaptor.getOrderInfo("123");

        // assert
        assertEquals(getMagicMemberCoupon(unifiedOrderWithId), result.getExtMagicMemberCouponInfo());
    }

    private long analyseProductId(UnifiedOrderWithId unifiedOrder) {
        boolean isMt = PayPlatform.isMtPlatform(unifiedOrder.getPlatform());
        ProductTypeEnum productType = ProductTypeAnalyser.fromOrderBizType(unifiedOrder.getBizType());
        long productId = NumberUtils.toLong(unifiedOrder.getFirstSku().getSpugId());
        if (ProductTypeEnum.TUAN_DEAL.equals(productType) && isMt) {
            Long mtProductId = Optional.of(unifiedOrder)
                    .map(UnifiedOrderWithId::getFirstSku)
                    .map(UnifiedOrderSKUDTO::getSkuExtraFields)
                    .map(skuFields -> skuFields.get(UnifiedOrderSkuField.mtDealId.fieldKey))
                    .map(NumberUtils::toLong)
                    .orElse(0L);
            if (mtProductId > 0L) {
                productId = mtProductId;
            }
        }
        return productId;
    }

    private String getOrderDistributionCode(UnifiedOrderWithId unifiedOrder) {
        String distributionCode = null;
        if (MapUtils.isNotEmpty(unifiedOrder.getExtraFields())) {
            distributionCode = unifiedOrder.getExtraFields().getOrDefault(UnifiedOrderField.distributionParam.fieldKey, "");
            if (StringUtils.isBlank(distributionCode) && MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields())) {
                String nibDistribution = unifiedOrder.getNibExtraFields().get("distribution_param");
                distributionCode = Optional.ofNullable(nibDistribution).orElse(distributionCode);
            }
        }
        return StringUtils.isNotBlank(distributionCode) ? distributionCode : "";
    }

    private TechnicianDistributionExtParamBO getTechExtParam(UnifiedOrderWithId unifiedOrder) {
        String techExtParamStr = Optional.of(unifiedOrder)
                .map(UnifiedOrderWithId::getExtraFields)
                .map(extFields -> extFields.get(UnifiedOrderField.craftsmanExtParam.fieldKey))
                .filter(StringUtils::isNotBlank)
                .orElse("");
        if (StringUtils.isBlank(techExtParamStr)) {
            techExtParamStr = Optional.of(unifiedOrder)
                    .map(UnifiedOrderWithId::getNibExtraFields)
                    .map(extFields -> extFields.get(String.valueOf(UnifiedOrderField.craftsmanExtParam.fieldKey)))
                    .filter(StringUtils::isNotBlank)
                    .orElse("");
        }
        if (StringUtils.isNotBlank(techExtParamStr)) {
            return JSON.parseObject(techExtParamStr, TechnicianDistributionExtParamBO.class);
        }
        return null;
    }

    private PassParamDistributionBO getDistributionBasicInfo(UnifiedOrderWithId unifiedOrder) {
        String passParam = Optional.of(unifiedOrder)
                .map(UnifiedOrderWithId::getNibExtraFields)
                .map(ext -> ext.get(PASS_PARAM_DISTRIBUTION_KEY))
                .orElse("");
        if (StringUtils.isBlank(passParam)) {
            return null;
        }
        PassParamDistributionBO passParamBO = JSON.parseObject(passParam, PassParamDistributionBO.class);
        if (CollectionUtils.isEmpty(passParamBO.getDistributionInfoList())) {
            return null;
        }
        return passParamBO;
    }

    private Map<String, String> getPassParam(UnifiedOrderWithId unifiedOrder) {
        Map<String, String> map = Maps.newHashMap();
        if (MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get("DISTRIBUTION_BASIC_INFO"))) {
            String passParamDistributionString = unifiedOrder.getNibExtraFields().get("DISTRIBUTION_BASIC_INFO");
            PassParamDistributionBO passParamDistributionBO = JSON.parseObject(passParamDistributionString, PassParamDistributionBO.class);
            if (CollectionUtils.isNotEmpty(passParamDistributionBO.getDistributionInfoList())) {
                map = passParamDistributionBO.getDistributionInfoList().stream()
                        .filter(Objects::nonNull)
                        .filter(passParam -> StringUtils.isNotBlank(passParam.getDistributionType()) && StringUtils.isNotBlank(passParam.getBizExtend()))
                        .collect(Collectors.toMap(PassParamDistributionInfoBO::getDistributionType, PassParamDistributionInfoBO::getBizExtend));
            }
        }
        return map;
    }

    private List<OrderPaymentDetailBO> getOrderPayment(UnifiedOrderWithId unifiedOrder) {
        List<OrderPaymentDetailBO> payDetailList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(unifiedOrder.getPaymentDetails())) {
            payDetailList = unifiedOrder.getPaymentDetails().stream()
                    .filter(Objects::nonNull)
                    .map(payment -> {
                        OrderPaymentDetailBO paymentDetailBO = new OrderPaymentDetailBO();
                        paymentDetailBO.setPaymentDetailId(payment.getPaymentDetailID());
                        paymentDetailBO.setAmountId(payment.getAmountId());
                        paymentDetailBO.setAmount(payment.getAmount());
                        paymentDetailBO.setAmountType(payment.getAmountType());
                        paymentDetailBO.setSkuId(payment.getSkuId());
                        paymentDetailBO.setPaymentReceiptId(payment.getPaymentReceiptId());
                        paymentDetailBO.setOrderPaymentId(payment.getOrderPaymentId());
                        paymentDetailBO.setPayPlatform(payment.getPayPlatform());
                        paymentDetailBO.setAddTime(payment.getAddTime());
                        return paymentDetailBO;
                    })
                    .collect(Collectors.toList());
        }
        return payDetailList;
    }

    private List<OrderSkuBO> getOrderSkuList(UnifiedOrderWithId unifiedOrder) {
        List<OrderSkuBO> orderSkuList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(unifiedOrder.getSkus())) {
            orderSkuList = unifiedOrder.getSkus().stream()
                    .filter(Objects::nonNull)
                    .map(orderSku -> {
                        OrderSkuBO skuBO = new OrderSkuBO();
                        int purchaseToConsumeRatio = Optional.of(orderSku)
                                .map(UnifiedOrderSKUDTO::getSkuExtraFields)
                                .map(map -> map.get(SKU_PURCHASE_TO_CONSUME_RATIO))
                                .map(val -> NumberUtils.toInt(val, 1))
                                .orElse(1);
                        skuBO.setQuantity(orderSku.getQuantity());
                        skuBO.setSkuVerifyCount(purchaseToConsumeRatio);
                        skuBO.setTotalVerifyCount(orderSku.getQuantity() * purchaseToConsumeRatio);
                        skuBO.setSkuId(NumberUtils.toLong(orderSku.getSkuId()));
                        skuBO.setProductId(NumberUtils.toLong(orderSku.getSpugId()));
                        skuBO.setUnitValue(orderSku.getUnitValue());
                        return skuBO;
                    })
                    .collect(Collectors.toList());
        }
        return orderSkuList;
    }

    private List<OrderDiscountBO> pickUpDiscount(UnifiedOrderWithId unifiedOrder) {
        if (CollectionUtils.isEmpty(unifiedOrder.getDiscounts())) {
            return Lists.newArrayList();
        }
        return unifiedOrder.getDiscounts().stream()
                .filter(discount -> discount.getDiscountType() == AmountType.COUPON.value
                        || discount.getDiscountType() == AmountType.SHOPCOUPON.value)
                .map(discount -> OrderDiscountBO.builder()
                        .type(discount.getDiscountType())
                        .discountAmount(discount.getDiscountAmount())
                        .discountId(discount.getDiscountId())
                        .build()
                )
                .collect(Collectors.toList());
    }

    private OrderLiveParamBO getLiveParam(UnifiedOrderWithId unifiedOrder) {
        // 团单：DISTRIBUTION_BASIC_INFO 参数示例，格式参考线上示例订单：170677436036898300088433
        long liveId = 0;
        String liveChannel = null;
        if (MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(PASS_PARAM_DISTRIBUTION_KEY))) {
            String passParamDistributionString = unifiedOrder.getNibExtraFields().get(PASS_PARAM_DISTRIBUTION_KEY);
            PassParamDistributionBO passParamDistributionBO = JSON.parseObject(passParamDistributionString, PassParamDistributionBO.class);
            if (CollectionUtils.isNotEmpty(passParamDistributionBO.getDistributionInfoList())) {
                PassParamDistributionInfoBO livePassParam = passParamDistributionBO.getDistributionInfoList().stream()
                        .filter(Objects::nonNull)
                        .filter(passParam -> StringUtils.isNotBlank(passParam.getDistributionType()) && StringUtils.isNotBlank(passParam.getBizExtend()))
                        .filter(passParam -> passParam.getDistributionType().equalsIgnoreCase(M_LIVE_DISTRIBUTION_KEY))
                        .findFirst()
                        .orElse(null);
                long analyseId = Optional.ofNullable(livePassParam)
                        .map(param -> JSON.parseObject(param.getBizExtend()))
                        .filter(json -> json.containsKey(M_LIVE_ID_KEY))
                        .map(json -> json.get(M_LIVE_ID_KEY).toString())
                        .map(NumberUtils::toLong)
                        .filter(id -> id > 0L)
                        .orElse(0L);
                if (analyseId > 0L) {
                    liveId = analyseId;
                    liveChannel = livePassParam.getDistributionType();
                }
            }
        }
        if (liveId > 0L) {
            return new OrderLiveParamBO(liveChannel, liveId);
        }
        // 团单 PROMOTE_CHANNEL_INFO 参数示例，格式参考线上示例订单：170677436036898300088433
        if (MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(PROMOTE_CHANNEL_KEY))) {
            String promoteChannelString = unifiedOrder.getNibExtraFields().get(PROMOTE_CHANNEL_KEY);
            PromoteChannelInfoBo promoteChannelInfoBo = JSON.parseObject(promoteChannelString, PromoteChannelInfoBo.class);
            PromoteChannelInfoBo livePromoteChannelInfoBO = Optional.ofNullable(promoteChannelInfoBo)
                    .filter(param -> StringUtils.isNotBlank(param.getPromoteType()))
                    .filter(param -> param.getPromoteType().equalsIgnoreCase(M_LIVE_DISTRIBUTION_KEY))
                    .orElse(null);
            long analyseId = Optional.ofNullable(livePromoteChannelInfoBO)
                    .filter(param -> StringUtils.isNotBlank(param.getPromoteExtend()))
                    .map(param -> JSON.parseObject(param.getPromoteExtend()))
                    .filter(json -> json.containsKey(M_LIVE_ID_KEY))
                    .map(json -> json.get(M_LIVE_ID_KEY).toString())
                    .map(NumberUtils::toLong)
                    .filter(id -> id > 0L)
                    .orElse(0L);
            if (analyseId > 0L) {
                liveId = analyseId;
                liveChannel = promoteChannelInfoBo.getPromoteType();
            }
        }
        if (liveId > 0L) {
            return new OrderLiveParamBO(liveChannel, liveId);
        }
        // 次卡，扩展字段 60254 ，格式参考线上订单：170662498930617200120658
        String promoteChannelString = null;
        if (MapUtils.isNotEmpty(unifiedOrder.getExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getExtraFields().get(UnifiedOrderField.promoteChannelInfo.fieldKey))) {
            promoteChannelString = unifiedOrder.getExtraFields().get(UnifiedOrderField.promoteChannelInfo.fieldKey);
        }
        if (StringUtils.isBlank(promoteChannelString) && MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(String.valueOf(UnifiedOrderField.promoteChannelInfo.fieldKey)))) {
            promoteChannelString = unifiedOrder.getNibExtraFields().get(String.valueOf(UnifiedOrderField.promoteChannelInfo.fieldKey));
        }
        if (StringUtils.isNotBlank(promoteChannelString)) {
            PromoteChannelInfoBo promoteChannelInfoBo = JSON.parseObject(promoteChannelString, PromoteChannelInfoBo.class);
            PromoteChannelInfoBo livePromoteChannelInfoBO = Optional.ofNullable(promoteChannelInfoBo)
                    .filter(param -> StringUtils.isNotBlank(param.getPromoteType()))
                    .filter(param -> param.getPromoteType().equalsIgnoreCase(M_LIVE_DISTRIBUTION_KEY))
                    .orElse(null);
            long analyseId = Optional.ofNullable(livePromoteChannelInfoBO)
                    .filter(param -> StringUtils.isNotBlank(param.getPromoteExtend()))
                    .map(param -> JSON.parseObject(param.getPromoteExtend()))
                    .filter(json -> json.containsKey(M_LIVE_ID_KEY))
                    .map(json -> json.getString(M_LIVE_ID_KEY))
                    .map(NumberUtils::toLong)
                    .filter(id -> id > 0L)
                    .orElse(0L);
            if (analyseId > 0L) {
                liveId = analyseId;
                liveChannel = promoteChannelInfoBo.getPromoteType();
            }
        }
        if (liveId > 0L) {
            return new OrderLiveParamBO(liveChannel, liveId);
        }
        return null;
    }

    private StaffCodeBargainBO getStaffCodeBargainBO(UnifiedOrderWithId unifiedOrder, OrderInfoBO orderInfoBO) {
        if (unifiedOrder == null || unifiedOrder.getNibExtraFields() == null || !unifiedOrder.getNibExtraFields().containsKey("general.ext.extraOrderType")) {
            return null;
        }
        int extraOrderType = NumberUtils.toInt(unifiedOrder.getNibExtraFields().get("general.ext.extraOrderType"), 0);
        if (extraOrderType != RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId() && extraOrderType != RelatedOrderTypeEnum.DEAL_GROUP_BALANCE_PAYMENT_ORDER.getId()) {
            return null;
        }
        StaffCodeBargainBO staffCodeBargainBO = new StaffCodeBargainBO();
        staffCodeBargainBO.setExtraOrderType(extraOrderType);
        if (extraOrderType == RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId()) {
            // 主订单，需要确保有staffBargainCodeId
            Map<String, String> map = orderInfoBO.getExtDistributionInfo();
            if (map == null || !map.containsKey("promotion_code")) {
                return null;
            }
            String bizExtend = map.get("promotion_code");
            Map<String, String> bizExtendMap = JsonUtil.toMap(bizExtend, String.class, String.class);
            if (!bizExtendMap.containsKey("staffBargainCodeId")) {
                return null;
            }
            staffCodeBargainBO.setTechId(NumberUtils.toInt(bizExtendMap.get("techId")));
            staffCodeBargainBO.setStaffBargainCodeId(NumberUtils.toLong(bizExtendMap.get("staffBargainCodeId")));

            String relatedOrderList = unifiedOrder.getNibExtraFields().get("general.ext.relatedOrderList");
            List<RelateOrderBO> relateOrderBOS = JSON.parseArray(relatedOrderList, RelateOrderBO.class);
            staffCodeBargainBO.setRelateOrders(relateOrderBOS);
        } else if (extraOrderType == RelatedOrderTypeEnum.DEAL_GROUP_BALANCE_PAYMENT_ORDER.getId()) {
            String mainOrderId = unifiedOrder.getNibExtraFields().get("general.ext.mainOrderId");
            staffCodeBargainBO.setMainOrderId(mainOrderId);
        }
        String prepayInfo = unifiedOrder.getNibExtraFields().get("general.ext.prepayInfo");
        PrepayInfoBO prepayInfoBO = JSON.parseObject(prepayInfo, PrepayInfoBO.class);
        staffCodeBargainBO.setTotalAmount(prepayInfoBO.getTotalAmount());
        staffCodeBargainBO.setPrepayAmount(prepayInfoBO.getPrepayAmount());
        staffCodeBargainBO.setBalancePaymentAmount(prepayInfoBO.getBalancePaymentAmount());
        return staffCodeBargainBO;
    }

    private String getCommissionFlag(UnifiedOrderWithId unifiedOrder) {
        Map<Integer, String> extMap = Optional.of(unifiedOrder)
                .map(UnifiedOrderWithId::getExtraFields)
                .orElse(Maps.newHashMap());
        return extMap.get(UnifiedOrderField.commissionFree.fieldKey);
    }

    private BigDecimal getOrderActualPayAmount(UnifiedOrderWithId unifiedOrder) {
        if (CollectionUtils.isEmpty(unifiedOrder.getPaymentDetails())) {
            return unifiedOrder.getTotalAmount();
        }
        List<Integer> amountTypeList = Lists.newArrayList(AmountType.MEITUANPAY.value, AmountType.MERCHANT_MEITUANPAY.value, AmountType.MT_DISTRIBUTION_PAY.value);
        return unifiedOrder.getPaymentDetails().stream()
                .filter(Objects::nonNull)
                .filter(payment -> amountTypeList.contains(payment.getAmountType()))
                .map(UnifiedOrderPaymentDetailDTO::getAmount)
                .reduce(BigDecimal::add)
                .orElse(unifiedOrder.getTotalAmount());
    }

    private Map<String, String> getMagicMemberCoupon(UnifiedOrderWithId unifiedOrder) {
        Map<String, String> map = Maps.newHashMap();
        // 字段定义见https://km.sankuai.com/collabpage/2156390666
        if (unifiedOrder.getNibExtraFields() != null && unifiedOrder.getNibExtraFields().containsKey(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey())) {
            // 是否搭售神券  "true" or "false"
            map.put(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey(), unifiedOrder.getNibExtraFields().get(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey()));
        }
        if (CollectionUtils.isNotEmpty(unifiedOrder.getPaymentDetails())) {
            UnifiedOrderPaymentDetailDTO magicMemberCouponPayment = unifiedOrder.getPaymentDetails()
                    .stream()
                    .filter(unifiedOrderPaymentDetailDTO ->
                            unifiedOrderPaymentDetailDTO != null &&
                                    unifiedOrderPaymentDetailDTO.getAmountSubType() != null &&
                                    unifiedOrderPaymentDetailDTO.getAmountSubType().equals(AmountSubType.PLATFORM_MAGICAL_MEMBER_COUPON_SUB_TYPE.subType))
                    .findFirst().orElse(null);
            if (magicMemberCouponPayment != null && magicMemberCouponPayment.getExtraData() != null) {
                Map<String, String> extraData = magicMemberCouponPayment.getExtraData();
                // 是否使用神券 "true" or "false"
                map.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_FLAG.getKey(), extraData.get(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_FLAG.getKey()));
                // 神券补贴方案 "补贴方案id"
                map.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_ALLOWANCE_SCHEME.getKey(), extraData.get(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_ALLOWANCE_SCHEME.getKey()));
                // 神券是否已膨胀 "true" or "false"
                map.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_INFLATED_FLAG.getKey(), extraData.get(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_INFLATED_FLAG.getKey()));
                // 神券类型 "mmc_common"免费券 or "mmc_buy"付费券
                map.put(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_MMC_CATEGORY.getKey(), extraData.get(OrderPaymentDetailExtraFieldEnum.MAGIC_MEMBER_COUPON_MMC_CATEGORY.getKey()));
            }
        }
        return map;
    }
}
