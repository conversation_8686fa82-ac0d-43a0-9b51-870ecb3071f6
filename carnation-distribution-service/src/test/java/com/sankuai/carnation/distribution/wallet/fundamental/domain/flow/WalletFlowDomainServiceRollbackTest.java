package com.sankuai.carnation.distribution.wallet.fundamental.domain.flow;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountAmountChangeResultBO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletDataItemStatusEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateFlowStatusEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateFlowTypeEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateTypeEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.exceptions.WalletFlowException;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletOperateFlowDataService;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WalletFlowDomainServiceRollbackTest {

    @Mock
    private WalletOperateFlowDataService flowDataService;

    @Mock
    private WalletFlowTransactionalService transactionalService;

    private WalletDataItem createWalletDataItem(Integer status, Integer operateType) {
        WalletDataItem item = new WalletDataItem();
        item.setId(1L);
        item.setStatus(status);
        item.setOperateType(operateType);
        item.setWalletActivityAccountId(1L);
        return item;
    }

    /**
     * Test rollback with rollback status
     */
    @Test
    public void testRollbackWithRollbackStatus() throws Throwable {
        WalletDataItem item = createWalletDataItem(WalletDataItemStatusEnum.ROLLBACK.getCode(), WalletOperateTypeEnum.ADDITION.getCode());
        List<WalletOperateFlow> mockFlowList = new ArrayList<>();
        WalletOperateFlow mockFlow = new WalletOperateFlow();
        mockFlow.setWalletActivityAccountId(1L);
        mockFlow.setFormerAmount(100L);
        mockFlow.setCurrentAmount(90L);
        mockFlow.setOperateType(WalletOperateTypeEnum.SUBTRACT.getCode());
        mockFlow.setAmount(10L);
        mockFlow.setStatus(WalletOperateFlowStatusEnum.SUCCESS.getCode());
        mockFlowList.add(mockFlow);
        when(flowDataService.queryFlowByItemId(eq(1L), eq(WalletOperateTypeEnum.SUBTRACT.getCode()), eq(WalletOperateFlowTypeEnum.ROLLBACK.getCode()))).thenReturn(mockFlowList);
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.rollback(item, "operator", "reason");
        assertNotNull(result);
        assertTrue(result.isSuccess());
    }

    /**
     * Test successful rollback
     */
    @Test
    public void testRollbackSuccess() throws Throwable {
        WalletDataItem item = createWalletDataItem(WalletDataItemStatusEnum.COLLECTED.getCode(), WalletOperateTypeEnum.ADDITION.getCode());
        when(flowDataService.addFlow(any())).thenReturn(1L);
        when(flowDataService.processing(anyLong(), isNull())).thenReturn(true);
        WalletActivityAccountAmountChangeResultBO expectedResult = WalletActivityAccountAmountChangeResultBO.buildSuccess(1L, 100L, 90L, 2, 10L);
        when(transactionalService.rollback(any(), any())).thenReturn(expectedResult);
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.rollback(item, "operator", "reason");
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(expectedResult.getActivityAccountId(), result.getActivityAccountId());
        assertEquals(expectedResult.getFormerAmount(), result.getFormerAmount());
        assertEquals(expectedResult.getCurrentAmount(), result.getCurrentAmount());
        verify(flowDataService).addFlow(any());
        verify(flowDataService).processing(anyLong(), isNull());
        verify(transactionalService).rollback(any(), any());
    }

    @InjectMocks
    private WalletFlowDomainService flowDomainService;
}