package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.rebate.dto.*;
import com.dianping.gmkt.event.api.rebate.enums.RebateSettleConditionKey;
import com.dianping.gmkt.event.api.rebate.enums.RebateSettleConditionKeyType;
import com.dianping.gmkt.event.api.rebate.service.RebateRuleCheckService;
import com.dianping.lion.client.Lion;
import com.sankuai.carnation.distribution.bcp.BcpCheckRule;
import com.sankuai.carnation.distribution.bcp.BcpCheckRuleHelper;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateRuleCheckResult;
import com.sankuai.technician.trade.api.settle.tradereturn.enums.ReturnBizLineEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 测试RebateRuleCheckUtils的checkRule方法
 */
@RunWith(MockitoJUnitRunner.class)
public class RebateRuleCheckUtilsTest {

    @InjectMocks
    private RebateRuleCheckUtils rebateRuleCheckUtils;

    private static final String REBATE_VERIFY_CHECK_SWITCH = "promoCode.rebateVerifyCheckSwitch";

    @Mock
    private RebateRuleCheckService rebateRuleCheckService;

    /**
     * 测试订单金额满足最低金额规则，且满足新老客户的阶梯规则
     */
    @Test
    public void testCheckRuleValid() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = mock(RebateAmountCalcRequest.class);
        // 假设支付金额为1000
        when(request.getPayCent()).thenReturn(1000L);
        RebateActivityRuleDTO rebateActivityRuleDTO = mock(RebateActivityRuleDTO.class);
        RebateSettleRuleDTO rule = mock(RebateSettleRuleDTO.class);
        when(rebateActivityRuleDTO.getRule()).thenReturn(rule);
        RebateSettleConditionDTO conditionDTO = new RebateSettleConditionDTO();
        conditionDTO.setKey(RebateSettleConditionKey.ORDER_PRICE.code);
        conditionDTO.setKeyType(RebateSettleConditionKeyType.GREATER_OR_EQUALS.code);
        // 最低金额规则为500
        conditionDTO.setValue("500");
        when(rule.getCondition()).thenReturn(Arrays.asList(new RebateSettleConditionDTO[] { conditionDTO }));
        // 假设校验成功
        when(rebateRuleCheckService.checkVerify(any())).thenReturn(new CommonResponse().success(RebateRuleVerifyCheckResult.success()));
        // act
        RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
        // assert
        assertTrue(result.isValid());
    }

    /**
     * 测试订单金额不满足最低金额规则
     */
    @Test
    public void testCheckRuleInvalidOrderAmount() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = mock(RebateAmountCalcRequest.class);
        // 假设支付金额为400
        when(request.getPayCent()).thenReturn(400L);
        RebateActivityRuleDTO rebateActivityRuleDTO = mock(RebateActivityRuleDTO.class);
        RebateSettleRuleDTO rule = mock(RebateSettleRuleDTO.class);
        when(rebateActivityRuleDTO.getRule()).thenReturn(rule);
        RebateSettleConditionDTO conditionDTO = new RebateSettleConditionDTO();
        conditionDTO.setKey(RebateSettleConditionKey.ORDER_PRICE.code);
        conditionDTO.setKeyType(RebateSettleConditionKeyType.GREATER_OR_EQUALS.code);
        // 最低金额规则为500
        conditionDTO.setValue("500");
        when(rule.getCondition()).thenReturn(Arrays.asList(new RebateSettleConditionDTO[] { conditionDTO }));
        // act
        RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
        // assert
        assertFalse(result.isValid());
        assertEquals("订单不满足最低金额规则", result.getReason());
    }

    /**
     * Test when preCalc is true (skip verification)
     */
    @Test
    public void testCheckRule_PreCalcTrue() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setPreCalc(true);
        request.setPayCent(1000L);
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rule.setCondition(Collections.emptyList());
        rebateActivityRuleDTO.setRule(rule);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBoolean(any(), any(), anyBoolean())).thenReturn(true);
            // act
            RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
            // assert
            assertTrue(result.isValid());
            verify(rebateRuleCheckService, never()).checkVerify(any());
        }
    }

    /**
     * Test successful verification with complete request data
     */
    @Test
    public void testCheckRule_SuccessfulVerification_CompleteData() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setPreCalc(false);
        request.setPayCent(1000L);
        request.setOrderId("123456");
        request.setOrderTime(new Date());
        request.setVerifyTime(new Date());
        request.setProductType(1);
        request.setOrderDpShopId(100L);
        request.setVerifyDpShopId(100L);
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        RebateSettleConditionDTO timeCondition = new RebateSettleConditionDTO();
        timeCondition.setKey(RebateSettleConditionKey.SCAN_TIME.code);
        rule.setCondition(Arrays.asList(timeCondition));
        rebateActivityRuleDTO.setRule(rule);
        RebateRuleVerifyCheckResult verifyCheckResult = mock(RebateRuleVerifyCheckResult.class);
        when(verifyCheckResult.isSuccess()).thenReturn(true);
        CommonResponse<RebateRuleVerifyCheckResult> response = new CommonResponse<>();
        response.setData(verifyCheckResult);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBoolean(eq("gmkt-event-manage-service"), eq("promoCode.rebateVerifyCheckSwitch"), eq(false))).thenReturn(true);
            when(rebateRuleCheckService.checkVerify(any(RebateRuleVerifyCheckRequest.class))).thenReturn(response);
            // act
            RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
            // assert
            assertTrue(result.isValid());
            verify(rebateRuleCheckService).checkVerify(any(RebateRuleVerifyCheckRequest.class));
        }
    }

    /**
     * Test verification failure with detailed error message
     */
    @Test
    public void testCheckRule_VerificationFailure_DetailedError() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setPreCalc(false);
        request.setPayCent(1000L);
        request.setOrderId("123456");
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rule.setCondition(Collections.emptyList());
        rebateActivityRuleDTO.setRule(rule);
        RebateRuleVerifyCheckResult verifyCheckResult = mock(RebateRuleVerifyCheckResult.class);
        when(verifyCheckResult.isSuccess()).thenReturn(false);
        when(verifyCheckResult.getDesc()).thenReturn("核销时间不在有效期内");
        CommonResponse<RebateRuleVerifyCheckResult> response = new CommonResponse<>();
        response.setData(verifyCheckResult);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBoolean(eq("gmkt-event-manage-service"), eq("promoCode.rebateVerifyCheckSwitch"), eq(false))).thenReturn(true);
            when(rebateRuleCheckService.checkVerify(any(RebateRuleVerifyCheckRequest.class))).thenReturn(response);
            // act
            RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
            // assert
            assertFalse(result.isValid());
            assertEquals("核销时间不在有效期内", result.getReason());
            verify(rebateRuleCheckService).checkVerify(any(RebateRuleVerifyCheckRequest.class));
        }
    }

    /**
     * Test verify check success scenario
     */
    @Test
    public void testCheckRule_VerifyCheckSuccess() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setPreCalc(false);
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        // Initialize condition list
        rule.setCondition(Collections.emptyList());
        rebateActivityRuleDTO.setRule(rule);
        RebateRuleVerifyCheckResult verifyCheckResult = mock(RebateRuleVerifyCheckResult.class);
        when(verifyCheckResult.isSuccess()).thenReturn(true);
        CommonResponse<RebateRuleVerifyCheckResult> response = new CommonResponse<>();
        response.setData(verifyCheckResult);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBoolean(eq("gmkt-event-manage-service"), eq("promoCode.rebateVerifyCheckSwitch"), eq(false))).thenReturn(true);
            when(rebateRuleCheckService.checkVerify(any(RebateRuleVerifyCheckRequest.class))).thenReturn(response);
            // act
            RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
            // assert
            assertTrue(result.isValid());
            verify(rebateRuleCheckService).checkVerify(any(RebateRuleVerifyCheckRequest.class));
        }
    }

    /**
     * Test verify check failure scenario
     */
    @Test
    public void testCheckRule_VerifyCheckFailure() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setPreCalc(false);
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        // Initialize condition list
        rule.setCondition(Collections.emptyList());
        rebateActivityRuleDTO.setRule(rule);
        RebateRuleVerifyCheckResult verifyCheckResult = mock(RebateRuleVerifyCheckResult.class);
        when(verifyCheckResult.isSuccess()).thenReturn(false);
        when(verifyCheckResult.getDesc()).thenReturn("Verification failed");
        CommonResponse<RebateRuleVerifyCheckResult> response = new CommonResponse<>();
        response.setData(verifyCheckResult);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBoolean(eq("gmkt-event-manage-service"), eq("promoCode.rebateVerifyCheckSwitch"), eq(false))).thenReturn(true);
            when(rebateRuleCheckService.checkVerify(any(RebateRuleVerifyCheckRequest.class))).thenReturn(response);
            // act
            RebateRuleCheckResult result = rebateRuleCheckUtils.checkRule(request, rebateActivityRuleDTO);
            // assert
            assertFalse(result.isValid());
            assertEquals("Verification failed", result.getReason());
            verify(rebateRuleCheckService).checkVerify(any(RebateRuleVerifyCheckRequest.class));
        }
    }
}