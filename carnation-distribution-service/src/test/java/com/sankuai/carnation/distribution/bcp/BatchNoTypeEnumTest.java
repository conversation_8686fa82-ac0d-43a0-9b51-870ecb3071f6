package com.sankuai.carnation.distribution.bcp;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class BatchNoTypeEnumTest {

    /**
     * 测试 getByCode 方法，输入的 code 与枚举值中的某个 code 相等
     */
    @Test
    public void testGetByCodeMatch() throws Throwable {
        // arrange
        int code = 0;
        // Assuming MONTH_BATCH is the only enum instance with code 0
        BatchNoTypeEnum instance = BatchNoTypeEnum.MONTH_BATCH;
        // act
        BatchNoTypeEnum result = instance.getByCode(code);
        // assert
        assertEquals(BatchNoTypeEnum.MONTH_BATCH, result);
    }

    /**
     * 测试 getByCode 方法，输入的 code 与枚举值中的所有 code 都不相等
     */
    @Test
    public void testGetByCodeNoMatch() throws Throwable {
        // arrange
        int code = 2;
        // act
        BatchNoTypeEnum result = BatchNoTypeEnum.MONTH_BATCH.getByCode(code);
        // assert
        assertNull(result);
    }
}
