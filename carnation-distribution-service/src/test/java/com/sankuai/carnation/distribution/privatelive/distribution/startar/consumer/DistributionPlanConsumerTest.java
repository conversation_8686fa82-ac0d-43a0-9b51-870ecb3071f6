package com.sankuai.carnation.distribution.privatelive.distribution.startar.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;

/**
 * 测试 DistributionPlanConsumer 的 afterPropertiesSet 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class DistributionPlanConsumerTest {

    @Mock
    private MafkaClient mafkaClientMock;

    @Mock
    private IConsumerProcessor consumerMock;

    private DistributionPlanConsumer distributionPlanConsumer;

    @Before
    public void setUp() throws Exception {
        distributionPlanConsumer = new DistributionPlanConsumer();
        // Mock MafkaClient 的静态方法
        Mockito.mockStatic(MafkaClient.class);
        when(MafkaClient.buildConsumerFactory((String) any(), anyString())).thenReturn(consumerMock);
    }


    /**
     * 测试 afterPropertiesSet 方法异常情况，模拟 buildConsumerFactory 抛出异常
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Exception {
        // arrange
        when(MafkaClient.buildConsumerFactory((String) any(), anyString())).thenThrow(new RuntimeException("创建consumer失败"));

        // act
        distributionPlanConsumer.afterPropertiesSet();

        // assert
        // 预期抛出异常，无需额外断言
    }
}
