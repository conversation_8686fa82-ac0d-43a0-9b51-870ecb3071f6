package com.sankuai.carnation.distribution.distributor.dto.request;

import org.junit.Test;
import static org.junit.Assert.*;

public class DistributorPassAuditRequestTest {

    /**
     * 测试 DistributorPassAuditRequest 的 setter 和 getter 方法
     */
    @Test
    public void testDistributorPassAuditRequestSettersAndGetters() {
        Long anchorId = 1L;
        Long distributorId = 2L;
        
        DistributorPassAuditRequest request = new DistributorPassAuditRequest();
        request.setAnchorId(anchorId);
        request.setDistributorId(distributorId);
        
        assertEquals(anchorId, request.getAnchorId());
        assertEquals(distributorId, request.getDistributorId());
    }

    /**
     * 测试 DistributorPassAuditRequest 使用构造函数初始化
     */
    @Test
    public void testDistributorPassAuditRequestConstructor() {
        Long anchorId = 1L;
        Long distributorId = 2L;
        
        DistributorPassAuditRequest request = new DistributorPassAuditRequest(anchorId, distributorId);
        
        assertEquals(anchorId, request.getAnchorId());
        assertEquals(distributorId, request.getDistributorId());
    }

    /**
     * 测试 DistributorPassAuditRequest 使用 Builder 模式
     */
    @Test
    public void testDistributorPassAuditRequestBuilder() {
        Long anchorId = 1L;
        Long distributorId = 2L;
        
        DistributorPassAuditRequest request = DistributorPassAuditRequest.builder()
                .anchorId(anchorId)
                .distributorId(distributorId)
                .build();
        
        assertEquals(anchorId, request.getAnchorId());
        assertEquals(distributorId, request.getDistributorId());
    }
}
