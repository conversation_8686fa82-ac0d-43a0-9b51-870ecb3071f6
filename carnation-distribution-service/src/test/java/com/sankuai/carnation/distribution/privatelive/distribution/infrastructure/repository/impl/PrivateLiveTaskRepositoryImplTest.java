package com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantTaskMapper;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.converter.PrivateLiveTaskConverter;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskBO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PrivateLiveTaskRepositoryImplTest {

    @InjectMocks
    private PrivateLiveTaskRepositoryImpl privateLiveTaskRepositoryImpl;

    @Mock
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Mock
    private PrivateLiveTaskConverter privateLiveTaskConverter;

    private PrivateLiveTaskBO mockPrivateLiveTaskBO;

    private PrivateLiveConsultantTask mockPrivateLiveConsultantTask;

    @Mock
    private PrivateLiveConsultantTaskMapper privateLiveConsultantTaskMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryByAccountIdAndLive_ReturnNull() {
        // arrange
        Long accountId = 1L;
        String liveId = "live123";
        when(taskRepository.loadByConsultantIdAndLiveId(accountId, liveId)).thenReturn(null);
        // act
        PrivateLiveTaskBO result = privateLiveTaskRepositoryImpl.queryByAccountIdAndLive(accountId, liveId);
        // assert
        assertNull(result);
    }

    /**
     * 测试查询结果不为空的情况
     */
    @Test
    public void testQueryByAccountIdAndLive_ReturnNotNull() {
        // arrange
        Long accountId = 1L;
        String liveId = "live123";
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        when(taskRepository.loadByConsultantIdAndLiveId(accountId, liveId)).thenReturn(task);
        PrivateLiveTaskBO expectedBO = new PrivateLiveTaskBO();
        when(privateLiveTaskConverter.toEntity(task)).thenReturn(expectedBO);
        // act
        PrivateLiveTaskBO result = privateLiveTaskRepositoryImpl.queryByAccountIdAndLive(accountId, liveId);
        // assert
        assertNotNull(result);
        assertEquals(expectedBO, result);
    }






    /**
     * 测试save方法，当privateLiveTaskBO的id为null且insert操作失败时，应抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testSave_InsertScenarioFailure() {
        // arrange
        mockPrivateLiveTaskBO.setId(null);
        when(privateLiveTaskConverter.toPoJo(any(PrivateLiveTaskBO.class))).thenReturn(mockPrivateLiveConsultantTask);
        // 假设插入操作失败，返回null
        when(taskRepository.insert(any(PrivateLiveConsultantTask.class))).thenReturn(null);
        // act
        privateLiveTaskRepositoryImpl.save(mockPrivateLiveTaskBO);
        // assert
        // 预期抛出异常
    }

    /**
     * 测试save方法，当privateLiveTaskBO的id不为null且update操作失败时，应抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testSave_UpdateScenarioFailure() {
        // arrange
        when(privateLiveTaskConverter.toPoJo(any(PrivateLiveTaskBO.class))).thenReturn(mockPrivateLiveConsultantTask);
        // 假设更新操作失败
        when(taskRepository.update(any(PrivateLiveConsultantTask.class))).thenReturn(0L);
        // act
        privateLiveTaskRepositoryImpl.save(mockPrivateLiveTaskBO);
        // assert
        // 预期抛出异常
    }

    /**
     * 测试save方法，当privateLiveTaskBO的id为null且insert操作返回的id为null时，应抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testSave_InsertScenario_ReturnNull() {
        // arrange
        when(mockPrivateLiveTaskBO.getId()).thenReturn(null);
        when(privateLiveTaskConverter.toPoJo(any(PrivateLiveTaskBO.class))).thenReturn(mockPrivateLiveConsultantTask);
        when(taskRepository.insert(any(PrivateLiveConsultantTask.class))).thenReturn(null);

        // act
        privateLiveTaskRepositoryImpl.save(mockPrivateLiveTaskBO);

        // assert
        // Expected exception
    }


}
