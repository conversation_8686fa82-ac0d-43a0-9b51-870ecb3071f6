package com.sankuai.carnation.distribution.distributor.domain.service.impl;

import com.sankuai.carnation.distribution.distributor.converter.DistributorAttrConverter;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBindBO;
import com.sankuai.carnation.distribution.distributor.dto.request.DistributorApplyHandleRequest;
import com.sankuai.carnation.distribution.distributor.enums.DistributorBindUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.repository.DistributorRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DistributorDomainServiceImplTest {

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private DistributorRepository distributorRepository;

    @Mock
    private DistributorAttrConverter distributorAttrConverter;

    @InjectMocks
    private DistributorDomainServiceImpl distributorDomainService;

    private DistributorBO newDistributorBO;

    private DistributorBindBO distributorBindBO;

    private DistributorApplyHandleRequest request;

    private DistributorBO distributorBO;

    @Before
    public void setUp() {
        newDistributorBO = new DistributorBO();
        newDistributorBO.setUserId(1L);
        newDistributorBO.setDistributorAttrList(new ArrayList<>());
        distributorBindBO = new DistributorBindBO();
        distributorBindBO.setDistributorId(1L);
        distributorBindBO.setStatus(DistributionApproveStatusEnum.WAITING);
    }

    /**
     * 测试分销员绑定，当分销员不存在时
     */
    @Test
    public void testBindDistributor_WhenDistributorNotExist() {
        when(distributorRepository.queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), newDistributorBO.getUserId())).thenReturn(null);
        DistributorBO result = distributorDomainService.bindDistributor(newDistributorBO);
        verify(distributorRootService, times(1)).setDistributorWithAudit(any(DistributorBO.class), eq(DistributionApproveStatusEnum.WAITING));
        verify(distributorRootService, times(0)).addDistributorAttr(any());
        assert !ObjectUtils.isEmpty(result);
    }



    /**
     * 测试分销员绑定，当分销员已存在但没有团队信息
     */
    @Test
    public void testBindDistributor_WhenDistributorExistWithoutGroup() {
        distributorBindBO.setDistributorGroupBO(null);
        when(distributorRepository.queryDistributorByAccountId(DistributorBindUserTypeEnum.WX_USER.getCode(), newDistributorBO.getUserId())).thenReturn(distributorBindBO);
        DistributorBO result = distributorDomainService.bindDistributor(newDistributorBO);
        verify(distributorRootService, times(1)).setDistributorWithAudit(any(DistributorBO.class), eq(DistributionApproveStatusEnum.WAITING));
        verify(distributorRootService, times(0)).addDistributorAttr(any());
        assert !ObjectUtils.isEmpty(result);
    }








    /**
     * 测试rejectAudit方法，当分销人不存在时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testRejectAuditDistributorNotExist() throws Throwable {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest();
        request.setDistributorId(1L);
        when(distributorRepository.queryByDistributorId(anyLong())).thenReturn(null);
        // act
        distributorDomainService.rejectAudit(request);
        // assert
        // BizSceneException expected
    }

    /**
     * 测试rejectAudit方法，当分销人存在时正常处理
     */
    @Test
    public void testRejectAuditDistributorExist() throws Throwable {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest();
        request.setDistributorId(1L);
        DistributorBO distributorBO = mock(DistributorBO.class);
        DistributorBindBO expectedBindBO = new DistributorBindBO();
        when(distributorRepository.queryByDistributorId(anyLong())).thenReturn(distributorBO);
        when(distributorBO.rejectAudit(any())).thenReturn(expectedBindBO);
        // act
        DistributorBindBO result = distributorDomainService.rejectAudit(request);
        // assert
        assertNotNull(result);
        assertEquals(expectedBindBO, result);
    }

    /**
     * 测试rejectAudit方法，当分销人存在且拒绝审核操作成功时
     */
    @Test
    public void testRejectAuditSuccess() throws Throwable {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest();
        request.setDistributorId(1L);
        DistributorBO distributorBO = mock(DistributorBO.class);
        DistributorBindBO expectedBindBO = new DistributorBindBO();
        when(distributorRepository.queryByDistributorId(anyLong())).thenReturn(distributorBO);
        when(distributorBO.rejectAudit(any())).thenReturn(expectedBindBO);

        // act
        DistributorBindBO result = distributorDomainService.rejectAudit(request);

        // assert
        assertNotNull(result);
        assertEquals(expectedBindBO, result);
    }

    /**
     * 测试rejectAudit方法，当分销人存在但拒绝审核操作抛出异常时
     */
    @Test(expected = Exception.class)
    public void testRejectAuditThrowsException() throws Throwable {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest();
        request.setDistributorId(1L);
        DistributorBO distributorBO = mock(DistributorBO.class);
        when(distributorBO.rejectAudit(any())).thenThrow(new Exception("拒绝审核操作失败"));

        // act
        distributorDomainService.rejectAudit(request);

        // assert
        // Exception expected
    }

}
