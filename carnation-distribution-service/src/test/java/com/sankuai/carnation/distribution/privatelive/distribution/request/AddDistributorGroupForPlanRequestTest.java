package com.sankuai.carnation.distribution.privatelive.distribution.request;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class AddDistributorGroupForPlanRequestTest {

    private AddDistributorGroupForPlanRequest request;

    @Before
    public void setUp() throws Exception {
        request = new AddDistributorGroupForPlanRequest();
    }

    /**
     * 测试设置和获取 anchorId
     */
    @Test
    public void testSetAndGetAnchorId() {
        // arrange
        Long expected = 123L;
        request.setAnchorId(expected);

        // act
        Long actual = request.getAnchorId();

        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试设置和获取 liveId
     */
    @Test
    public void testSetAndGetLiveId() {
        // arrange
        String expected = "live123";
        request.setLiveId(expected);

        // act
        String actual = request.getLiveId();

        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试设置和获取 distributorGroupId
     */
    @Test
    public void testSetAndGetDistributorGroupId() {
        // arrange
        Long expected = 456L;
        request.setDistributorGroupId(expected);

        // act
        Long actual = request.getDistributorGroupId();

        // assert
        assertEquals(expected, actual);
    }
}
