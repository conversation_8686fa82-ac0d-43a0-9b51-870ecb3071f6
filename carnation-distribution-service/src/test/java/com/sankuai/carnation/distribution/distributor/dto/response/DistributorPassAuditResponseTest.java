package com.sankuai.carnation.distribution.distributor.dto.response;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试 DistributorPassAuditResponse 类的属性赋值和获取功能
 */
public class DistributorPassAuditResponseTest {

    /**
     * 测试 distributorId 属性的赋值和获取
     */
    @Test
    public void testDistributorIdSetterGetter() {
        // arrange
        Long expectedId = 12345L;
        DistributorPassAuditResponse response = new DistributorPassAuditResponse();
        response.setDistributorId(expectedId);

        // act
        Long actualId = response.getDistributorId();

        // assert
        assertEquals(expectedId, actualId);
    }

    /**
     * 测试 status 属性的赋值和获取
     */
    @Test
    public void testStatusSetterGetter() {
        // arrange
        Integer expectedStatus = 1;
        DistributorPassAuditResponse response = new DistributorPassAuditResponse();
        response.setStatus(expectedStatus);

        // act
        Integer actualStatus = response.getStatus();

        // assert
        assertEquals(expectedStatus, actualStatus);
    }

    /**
     * 测试使用全参数构造函数
     */
    @Test
    public void testAllArgsConstructor() {
        // arrange
        Long expectedId = 12345L;
        Integer expectedStatus = 1;

        // act
        DistributorPassAuditResponse response = new DistributorPassAuditResponse(expectedId, expectedStatus);

        // assert
        assertEquals(expectedId, response.getDistributorId());
        assertEquals(expectedStatus, response.getStatus());
    }

    /**
     * 测试使用 Builder 模式
     */
    @Test
    public void testBuilder() {
        // arrange
        Long expectedId = 12345L;
        Integer expectedStatus = 1;

        // act
        DistributorPassAuditResponse response = DistributorPassAuditResponse.builder()
                .distributorId(expectedId)
                .status(expectedStatus)
                .build();

        // assert
        assertEquals(expectedId, response.getDistributorId());
        assertEquals(expectedStatus, response.getStatus());
    }
}
