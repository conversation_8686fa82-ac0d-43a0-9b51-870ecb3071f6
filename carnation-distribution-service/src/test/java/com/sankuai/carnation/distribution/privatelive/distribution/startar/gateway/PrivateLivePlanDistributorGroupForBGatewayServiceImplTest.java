package com.sankuai.carnation.distribution.privatelive.distribution.startar.gateway;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLivePlanDistributorGroupAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.AnchorHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.request.AddDistributorGroupForPlanRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryDistributorGroupByPlanRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.AddDistributorGroupForPlanResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PrivateLivePlanDistributorGroupForBGatewayServiceImplTest {

    @InjectMocks
    private PrivateLivePlanDistributorGroupForBGatewayServiceImpl privateLivePlanDistributorGroupForBGatewayServiceImpl;

    @Mock
    private PrivateLivePlanDistributorGroupAppService privateLivePlanDistributorGroupAppService;

    @Mock
    private AnchorHelper anchorHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况下的分页查询
     */
//    @Test
//    public void testPageQueryPrivateLiveDistributorGroupListSuccess() throws Exception {
//        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("liveId", 1, 10, false, false);
//        QueryDistributorGroupByPlanResponse expectedResponse = new QueryDistributorGroupByPlanResponse();
//        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(request)).thenReturn(expectedResponse);
//
//        RemoteResponse<QueryDistributorGroupByPlanResponse> result = privateLivePlanDistributorGroupForBGatewayServiceImpl.pageQueryPrivateLiveDistributorGroupList(request);
//
//        assertNotNull(result);
//        assertEquals(expectedResponse, result.getData());
//        verify(privateLivePlanDistributorGroupAppService, times(1)).queryDistributorGroupByPlan(request);
//    }

    /**
     * 测试请求参数为null时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListRequestNull() throws Exception {
        privateLivePlanDistributorGroupForBGatewayServiceImpl.pageQueryPrivateLiveDistributorGroupList(null);
    }

    /**
     * 测试直播id为空时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListLiveIdEmpty() throws Exception {
        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("", 1, 10, false, false, false);
        privateLivePlanDistributorGroupForBGatewayServiceImpl.pageQueryPrivateLiveDistributorGroupList(request);
    }

    /**
     * 测试页码为null或小于等于0时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListPageNumInvalid() throws Exception {
        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("liveId", 0, 10, false, false, false);
        privateLivePlanDistributorGroupForBGatewayServiceImpl.pageQueryPrivateLiveDistributorGroupList(request);
    }

    /**
     * 测试分页数量为null或小于等于0时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryPrivateLiveDistributorGroupListPageSizeInvalid() throws Exception {
        QueryDistributorGroupByPlanRequest request = new QueryDistributorGroupByPlanRequest("liveId", 1, 0, false, false, false);
        privateLivePlanDistributorGroupForBGatewayServiceImpl.pageQueryPrivateLiveDistributorGroupList(request);
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当请求参数正确时，应返回成功响应
     */
    @Test
    public void testJoinPrivateLiveZoomSuccess() throws Exception {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(1L, "liveId123", 2L);
        AddDistributorGroupForPlanResponse addDistributorGroupForPlanResponse = new AddDistributorGroupForPlanResponse("liveId123", 2L);
        RemoteResponse<AddDistributorGroupForPlanResponse> expectedResponse = RemoteResponse.success(addDistributorGroupForPlanResponse);
        when(privateLivePlanDistributorGroupAppService.joinPrivateLiveZoom(request)).thenReturn(addDistributorGroupForPlanResponse);

        // act
        RemoteResponse<AddDistributorGroupForPlanResponse> result = privateLivePlanDistributorGroupForBGatewayServiceImpl.joinPrivateLiveZoom(request);

        // assert
        assertEquals(expectedResponse.getData().getLiveId(), result.getData().getLiveId());
        assertEquals(expectedResponse.getData().getDistributorGroupId(), result.getData().getDistributorGroupId());
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当请求参数为 null 时，应抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinPrivateLiveZoomRequestIsNull() throws Exception {
        // arrange
        AddDistributorGroupForPlanRequest request = null;

        // act
        privateLivePlanDistributorGroupForBGatewayServiceImpl.joinPrivateLiveZoom(request);

        // assert is handled by the expected exception
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当主播 ID 为 null 时，应抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinPrivateLiveZoomAnchorIdIsNull() throws Exception {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(null, "liveId123", 2L);

        // act
        privateLivePlanDistributorGroupForBGatewayServiceImpl.joinPrivateLiveZoom(request);

        // assert is handled by the expected exception
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当直播 ID 为空时，应抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinPrivateLiveZoomLiveIdIsEmpty() throws Exception {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(1L, "", 2L);

        // act
        privateLivePlanDistributorGroupForBGatewayServiceImpl.joinPrivateLiveZoom(request);

        // assert is handled by the expected exception
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当分销商 ID 为 null 时，应抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinPrivateLiveZoomDistributorGroupIdIsNull() throws Exception {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(1L, "liveId123", null);

        // act
        privateLivePlanDistributorGroupForBGatewayServiceImpl.joinPrivateLiveZoom(request);

        // assert is handled by the expected exception
    }
}
