package com.sankuai.carnation.distribution.promocode.privilege.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.promocode.privilege.dto.DeliverPrivilegeRequest;
import com.sankuai.carnation.distribution.promocode.privilege.dto.DeliverPrivilegeResult;
import com.sankuai.carnation.distribution.promocode.privilege.enums.DeliverPrivilegeResultEnum;
import com.sankuai.carnation.distribution.promocode.privilege.service.PromoCodeMerchantPrivilegeCacheService;
import com.sankuai.carnation.distribution.promocode.privilege.service.PromoCodeMerchantPrivilegeDeliveryRecordLocalService;
import com.sankuai.carnation.distribution.promocode.privilege.service.PromoCodeMerchantPrivilegeDeliveryService;
import com.sankuai.carnation.distribution.utils.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PromoCodeMerchantPrivilegeDeliveryServiceImplTest {

    @InjectMocks
    private PromoCodeMerchantPrivilegeDeliveryServiceImpl service;

    @Mock
    private PromoCodeMerchantPrivilegeDeliveryRecordLocalService privilegeDeliveryRecordLocalService;

    @Mock
    private PromoCodeMerchantPrivilegeCacheService promoCodeMerchantPrivilegeCacheService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 deliverPrivilege 方法，当请求参数为 null 时
     */
    @Test
    public void testDeliverPrivilegeRequestIsNull() {
        // arrange
        DeliverPrivilegeRequest request = null;

        // act
        RemoteResponse<DeliverPrivilegeResult> response = service.deliverPrivilege(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试 deliverPrivilege 方法，当发放权益成功时
     */
    @Test
    public void testDeliverPrivilegeSuccess() throws Exception {
        // arrange
        DeliverPrivilegeRequest request = mock(DeliverPrivilegeRequest.class);
        when(request.getDpShopId()).thenReturn(1L);
        DeliverPrivilegeResult deliverPrivilegeResult = new DeliverPrivilegeResult(DeliverPrivilegeResultEnum.SUCCESS, "成功");
        when(privilegeDeliveryRecordLocalService.deliverPrivilegeAndAutoAudit(request)).thenReturn(deliverPrivilegeResult);

        // act
        RemoteResponse<DeliverPrivilegeResult> response = service.deliverPrivilege(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(DeliverPrivilegeResultEnum.SUCCESS.getCode(), response.getData().getResult());
        verify(promoCodeMerchantPrivilegeCacheService, times(1)).shopCache(1L, 10 * 24 * 3600);
    }

    /**
     * 测试 deliverPrivilege 方法，当发放权益异常时
     */
    @Test
    public void testDeliverPrivilegeThrowsException() throws Exception {
        // arrange
        DeliverPrivilegeRequest request = mock(DeliverPrivilegeRequest.class);
        when(privilegeDeliveryRecordLocalService.deliverPrivilegeAndAutoAudit(request)).thenThrow(new RuntimeException("内部错误"));

        // act
        RemoteResponse<DeliverPrivilegeResult> response = service.deliverPrivilege(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("内部错误", response.getMsg());
    }
}
