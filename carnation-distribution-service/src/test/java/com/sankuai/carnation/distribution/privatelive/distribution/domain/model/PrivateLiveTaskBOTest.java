package com.sankuai.carnation.distribution.privatelive.distribution.domain.model;

import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.privatelive.account.enums.PrivateLiveTaskTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ManageOperateTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveUserIntentionServiceImpl;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveTaskRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * 测试PrivateLiveTaskBO的create方法
 */
public class PrivateLiveTaskBOTest {

    @Mock
    private PrivateLiveTaskRepository privateLiveTaskRepository;

    @Mock
    private PrivateLiveDistributorCodeService privateLiveDistributorCodeService;

    @InjectMocks
    private PrivateLiveTaskBO privateLiveTaskBO;

    @Mock
    private PrivateLiveUserIntentionServiceImpl privateLiveUserIntentionService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试create方法，正常情况
     */
    @Test
    public void testCreateNormal() {
        // arrange
        PrivateLiveTaskContext context = mock(PrivateLiveTaskContext.class);
        when(context.getPrivateLiveTaskRepository()).thenReturn(privateLiveTaskRepository);
        when(context.getPrivateLiveDistributorCodeService()).thenReturn(privateLiveDistributorCodeService);
        privateLiveTaskBO.setId(1L);
        privateLiveTaskBO.setLiveId("liveId");
        // act
        privateLiveTaskBO.create(context);
        // assert
        verify(privateLiveTaskRepository, times(1)).save(privateLiveTaskBO);
        verify(privateLiveDistributorCodeService, times(1)).createCodeByConsultantTaskId(1L, "liveId");
    }

    /**
     * 测试create方法，当context为null时抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testCreateContextNull() {
        // arrange
        PrivateLiveTaskContext context = null;
        // act
        privateLiveTaskBO.create(context);
        // assert
        // 期望抛出NullPointerException
    }

    /**
     * 测试create方法，当PrivateLiveTaskRepository.save抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCreateSaveThrowsException() {
        // arrange
        PrivateLiveTaskContext context = mock(PrivateLiveTaskContext.class);
        when(context.getPrivateLiveTaskRepository()).thenReturn(privateLiveTaskRepository);
        doThrow(new RuntimeException()).when(privateLiveTaskRepository).save(any(PrivateLiveTaskBO.class));
        privateLiveTaskBO.setId(1L);
        privateLiveTaskBO.setLiveId("liveId");
        // act
        privateLiveTaskBO.create(context);
        // assert
        // 期望抛出RuntimeException
    }

    /**
     * 测试create方法，当PrivateLiveDistributorCodeService.createCodeByConsultantTaskId抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCreateCreateCodeByConsultantTaskIdThrowsException() {
        // arrange
        PrivateLiveTaskContext context = mock(PrivateLiveTaskContext.class);
        when(context.getPrivateLiveTaskRepository()).thenReturn(privateLiveTaskRepository);
        when(context.getPrivateLiveDistributorCodeService()).thenReturn(privateLiveDistributorCodeService);
        doNothing().when(privateLiveTaskRepository).save(any(PrivateLiveTaskBO.class));
        doThrow(new RuntimeException()).when(privateLiveDistributorCodeService).createCodeByConsultantTaskId(anyLong(), anyString());
        privateLiveTaskBO.setId(1L);
        privateLiveTaskBO.setLiveId("liveId");
        // act
        privateLiveTaskBO.create(context);
        // assert
        // 期望抛出RuntimeException
    }



    /**
     * 测试审核通过，状态为PASS
     */
    @Test
    public void testPassAuditStatusPass() {
        PrivateLiveTaskContext context = mock(PrivateLiveTaskContext.class);
        when(context.getPrivateLiveTaskRepository()).thenReturn(privateLiveTaskRepository);
        when(context.getPrivateLiveUserIntentionService()).thenReturn(privateLiveUserIntentionService);
        privateLiveTaskBO.setStatus(ConsultantTaskApproveStatusEnum.PASS);
        PrivateLiveTaskBO result = privateLiveTaskBO.passAudit(context);
        verify(privateLiveTaskRepository, never()).save(any(PrivateLiveTaskBO.class));
        assertSame("Result should be the same instance", privateLiveTaskBO, result);
    }

    /**
     * 测试审核通过，状态为REJECT，期望抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testPassAuditStatusReject() {
        PrivateLiveTaskContext context = mock(PrivateLiveTaskContext.class);
        privateLiveTaskBO.setStatus(ConsultantTaskApproveStatusEnum.REJECT);
        privateLiveTaskBO.passAudit(context);
    }

    /**
     * 测试审核通过，状态为CANCELED，期望抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testPassAuditStatusCanceled() {
        PrivateLiveTaskContext context = mock(PrivateLiveTaskContext.class);
        privateLiveTaskBO.setStatus(ConsultantTaskApproveStatusEnum.CANCELED);
        privateLiveTaskBO.passAudit(context);
    }




    /**
     * 测试任务类型不匹配时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testReRegisterTaskTypeMismatch() {
        // arrange
        PrivateLiveTaskContext context = new PrivateLiveTaskContext();
        context.setTaskType(PrivateLiveTaskTypeEnum.CONSULTANT);
        privateLiveTaskBO.setTaskType(PrivateLiveTaskTypeEnum.DISTRIBUTOR);
        // act
        privateLiveTaskBO.reRegister(context);
        // assert is handled by the expected exception
    }

    /**
     * 测试任务状态为等待或通过时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testReRegisterTaskWaitingOrPassed() {
        // arrange
        PrivateLiveTaskContext context = new PrivateLiveTaskContext();
        context.setTaskType(PrivateLiveTaskTypeEnum.CONSULTANT);
        privateLiveTaskBO.setTaskType(PrivateLiveTaskTypeEnum.CONSULTANT);
        privateLiveTaskBO.setStatus(ConsultantTaskApproveStatusEnum.WAITING);
        // act
        privateLiveTaskBO.reRegister(context);
        // assert is handled by the expected exception
    }

    /**
     * 测试正常情况下任务重新注册
     */
    @Test
    public void testReRegisterSuccess() {
        // arrange
        PrivateLiveTaskContext context = new PrivateLiveTaskContext();
        context.setTaskType(PrivateLiveTaskTypeEnum.CONSULTANT);
        context.setPrivateLiveTaskRepository(privateLiveTaskRepository);
        privateLiveTaskBO.setTaskType(PrivateLiveTaskTypeEnum.CONSULTANT);
        privateLiveTaskBO.setStatus(ConsultantTaskApproveStatusEnum.UN_APPLY);
        // act
        privateLiveTaskBO.reRegister(context);
        // assert
        verify(privateLiveTaskRepository, times(1)).save(privateLiveTaskBO);
        assert (privateLiveTaskBO.getStatus() == ConsultantTaskApproveStatusEnum.WAITING);
    }
}
