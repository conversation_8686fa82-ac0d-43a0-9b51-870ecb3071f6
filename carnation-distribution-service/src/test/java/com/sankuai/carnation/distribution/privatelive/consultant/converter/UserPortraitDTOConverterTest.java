package com.sankuai.carnation.distribution.privatelive.consultant.converter;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.UserPortraitDTO;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalGroupActionInfo;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalInfo;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalLiveActionInfo;
import com.sankuai.dz.srcm.user.dto.IntentionProductDTO;
import java.util.Date;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/9/3
 */
@RunWith(MockitoJUnitRunner.class)
public class UserPortraitDTOConverterTest {

    @Before
    public void setUp() throws Exception {
        // 在这里可以进行一些测试环境的初始化
    }

    /**
     * 测试converter方法，当CustomerCapitalInfo为null时
     */
    @Test(expected = NullPointerException.class)
    public void testConverterWithNullCustomerCapitalInfo() {
        UserPortraitDTO result = UserPortraitDTOConverter.converter(null);
    }

    /**
     * 测试converter方法，当CustomerCapitalInfo包含完整的直播观看记录信息时
     */
    @Test
    public void testConverterWithFullLiveWatchRecordInfo() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        CustomerCapitalLiveActionInfo liveActionInfo = mock(CustomerCapitalLiveActionInfo.class);
        when(customerCapitalInfo.getLiveActionInfo()).thenReturn(liveActionInfo);
        when(liveActionInfo.getAllViewDuration()).thenReturn(3600L); // 1小时
        when(liveActionInfo.getViewSize()).thenReturn(3);
        when(liveActionInfo.getLastViewTime()).thenReturn(new java.util.Date());

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
        assertNotNull(result.getLiveWatchRecord());
        assertEquals("60分钟0秒", result.getLiveWatchRecord().getCumulativeDuration());
        assertEquals(3, result.getLiveWatchRecord().getInterviewDegree());
    }

    /**
     * 测试converter方法，当CustomerCapitalInfo包含群邀约信息时
     */
    @Test
    public void testConverterWithGroupInviteInfo() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        CustomerCapitalGroupActionInfo groupActionInfo = mock(CustomerCapitalGroupActionInfo.class);
        when(customerCapitalInfo.getGroupActionInfoList()).thenReturn(java.util.Collections.singletonList(groupActionInfo));
        when(groupActionInfo.getGroupName()).thenReturn("测试群组");
        when(groupActionInfo.getEnterTime()).thenReturn(new java.util.Date());
        when(groupActionInfo.getInviteSize()).thenReturn(5);

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
        assertFalse(result.getGroupInvite().isEmpty());
        assertEquals("测试群组", result.getGroupInvite().get(0).getGroupName());
        assertEquals(5, result.getGroupInvite().get(0).getInvitePeopleNum());
    }

    /**
     * 测试converter方法，当CustomerCapitalInfo包含群邀约信息时
     */
    @Test
    public void testConverterIntentionProductDTO() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        IntentionProductDTO intentionProductDTO = new IntentionProductDTO();
        intentionProductDTO.setProductId(1L);
        intentionProductDTO.setProductType(1);
        intentionProductDTO.setAddTime(new Date());
        intentionProductDTO.setLabel("");
        intentionProductDTO.setViewProductTimes(5);
        intentionProductDTO.setActionType(5);

        when(customerCapitalInfo.getIntentionProductIdList()).thenReturn(Lists.newArrayList(intentionProductDTO));

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testConverterIntentionProductDTOToActionType1() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        IntentionProductDTO intentionProductDTO = new IntentionProductDTO();
        intentionProductDTO.setProductId(1L);
        intentionProductDTO.setProductType(1);
        intentionProductDTO.setAddTime(new Date());
        intentionProductDTO.setLabel("");
        intentionProductDTO.setActionType(1);

        when(customerCapitalInfo.getIntentionProductIdList()).thenReturn(Lists.newArrayList(intentionProductDTO));

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
    }
    @Test
    public void testConverterIntentionProductDTOToActionType2() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        IntentionProductDTO intentionProductDTO = new IntentionProductDTO();
        intentionProductDTO.setProductId(1L);
        intentionProductDTO.setProductType(1);
        intentionProductDTO.setAddTime(new Date());
        intentionProductDTO.setLabel("");
        intentionProductDTO.setActionType(2);

        when(customerCapitalInfo.getIntentionProductIdList()).thenReturn(Lists.newArrayList(intentionProductDTO));

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
    }
    @Test
    public void testConverterIntentionProductDTOToActionType3() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        IntentionProductDTO intentionProductDTO = new IntentionProductDTO();
        intentionProductDTO.setProductId(1L);
        intentionProductDTO.setProductType(1);
        intentionProductDTO.setAddTime(new Date());
        intentionProductDTO.setLabel("");
        intentionProductDTO.setActionType(3);

        when(customerCapitalInfo.getIntentionProductIdList()).thenReturn(Lists.newArrayList(intentionProductDTO));

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
    }
    @Test
    public void testConverterIntentionProductDTOToActionType4() {
        // arrange
        CustomerCapitalInfo customerCapitalInfo = mock(CustomerCapitalInfo.class);
        IntentionProductDTO intentionProductDTO = new IntentionProductDTO();
        intentionProductDTO.setProductId(1L);
        intentionProductDTO.setProductType(1);
        intentionProductDTO.setAddTime(new Date());
        intentionProductDTO.setLabel("");
        intentionProductDTO.setActionType(4);

        when(customerCapitalInfo.getIntentionProductIdList()).thenReturn(Lists.newArrayList(intentionProductDTO));

        // act
        UserPortraitDTO result = UserPortraitDTOConverter.converter(customerCapitalInfo);

        // assert
        assertNotNull(result);
    }
}