package com.sankuai.carnation.distribution.distributor.vo;

import org.junit.Assert;
import org.junit.Test;

/**
 * 测试 ApplyDistributorVO 类的构造函数和 getter/setter 方法
 */
public class ApplyDistributorVOTest {

    @Test
    public void testApplyDistributorVOSetAndGet() {
        // arrange
        ApplyDistributorVO vo = new ApplyDistributorVO();
        vo.setAccountId(1L);
        vo.setDistributorId(2L);
        vo.setGroupId("group123");
        vo.setNickname("nickname");
        vo.setAvatarUrl("avatarUrl");
        vo.setShareName("shareName");
        vo.setMobile("*********");
        vo.setWechatNumber("wechat123");
        vo.setStatus(1);
        vo.setActualName("actualName");
        vo.setDistributorGroupName("distributorGroupName");
        vo.setDistributorGroupId(3L);

        // act and assert
        Assert.assertEquals(Long.valueOf(1), vo.getAccountId());
        Assert.assertEquals(Long.valueOf(2), vo.getDistributorId());
        Assert.assertEquals("group123", vo.getGroupId());
        Assert.assertEquals("nickname", vo.getNickname());
        Assert.assertEquals("avatarUrl", vo.getAvatarUrl());
        Assert.assertEquals("shareName", vo.getShareName());
        Assert.assertEquals("*********", vo.getMobile());
        Assert.assertEquals("wechat123", vo.getWechatNumber());
        Assert.assertEquals(Integer.valueOf(1), vo.getStatus());
        Assert.assertEquals("actualName", vo.getActualName());
        Assert.assertEquals("distributorGroupName", vo.getDistributorGroupName());
        Assert.assertEquals(Long.valueOf(3), vo.getDistributorGroupId());
    }
}
