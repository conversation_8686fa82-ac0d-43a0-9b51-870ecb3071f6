package com.sankuai.carnation.distribution.privatelive.account.dto;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class ConsultantVOTest {

    private ConsultantVO consultantVO;

    @Before
    public void setUp() throws Exception {
        consultantVO = new ConsultantVO();
    }

    /**
     * 测试设置和获取 accountId
     */
    @Test
    public void testAccountId() throws Throwable {
        Long accountId = 123L;
        consultantVO.setAccountId(accountId);
        assertEquals(accountId, consultantVO.getAccountId());
    }

    /**
     * 测试设置和获取 groupId
     */
    @Test
    public void testGroupId() throws Throwable {
        String groupId = "testGroup";
        consultantVO.setGroupId(groupId);
        assertEquals(groupId, consultantVO.getGroupId());
    }

    // 以下省略其他字段的测试用例，原理相同

    /**
     * 测试设置和获取 status
     */
    @Test
    public void testStatus() throws Throwable {
        Integer status = 1;
        consultantVO.setStatus(status);
        assertEquals(status, consultantVO.getStatus());
    }

    /**
     * 测试设置和获取 taskType
     */
    @Test
    public void testTaskType() throws Throwable {
        Integer taskType = 1;
        consultantVO.setTaskType(taskType);
        assertEquals(taskType, consultantVO.getTaskType());
    }
}
