package com.sankuai.carnation.distribution.distributor.dto.request;

import org.junit.Test;
import static org.junit.Assert.*;

public class QueryDistributorGroupRequestTest {

    /**
     * 测试属性设置和获取
     */
    @Test
    public void testProperties() {
        Long anchorId = 1L;
        String wxToken = "wxToken";
        Long distributorGroupId = 2L;

        QueryDistributorGroupRequest request = new QueryDistributorGroupRequest();
        request.setAnchorId(anchorId);
        request.setWxToken(wxToken);
        request.setDistributorGroupId(distributorGroupId);

        assertEquals(anchorId, request.getAnchorId());
        assertEquals(wxToken, request.getWxToken());
        assertEquals(distributorGroupId, request.getDistributorGroupId());
    }

    /**
     * 测试使用构造函数设置属性
     */
    @Test
    public void testConstructor() {
        Long anchorId = 1L;
        String wxToken = "wxToken";
        Long distributorGroupId = 2L;

        QueryDistributorGroupRequest request = new QueryDistributorGroupRequest(anchorId, wxToken, distributorGroupId);

        assertEquals(anchorId, request.getAnchorId());
        assertEquals(wxToken, request.getWxToken());
        assertEquals(distributorGroupId, request.getDistributorGroupId());
    }

    /**
     * 测试Builder模式
     */
    @Test
    public void testBuilder() {
        Long anchorId = 1L;
        String wxToken = "wxToken";
        Long distributorGroupId = 2L;

        QueryDistributorGroupRequest request = QueryDistributorGroupRequest.builder()
                .anchorId(anchorId)
                .wxToken(wxToken)
                .distributorGroupId(distributorGroupId)
                .build();

        assertEquals(anchorId, request.getAnchorId());
        assertEquals(wxToken, request.getWxToken());
        assertEquals(distributorGroupId, request.getDistributorGroupId());
    }
}
