package com.sankuai.carnation.distribution.wallet.fundamental.bean.generator;

import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletPayStrategyBO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletPayTypeEnum;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/6
 **/
public class WalletPayStrategyTestBeanGenerator {

    public static WalletPayStrategyBO generatePrivacyWalletPayStrategyBO() {
        WalletPayStrategyBO payStrategyBO = new WalletPayStrategyBO();
        payStrategyBO.setStrategyId(WalletTestConstants.mockPayStrategyId);
        payStrategyBO.setPayType(WalletPayTypeEnum.PERSONAL_ACCOUNT.getCode());
        payStrategyBO.setMtUserId(123L);
        return payStrategyBO;
    }
}
