package com.sankuai.carnation.distribution.product.v2.repository.service;

import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Method;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceAddItemSaleUnitTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductItemSaleUnitMapper mapper;

    private ProductItemSaleUnit po;

    @Before
    public void setUp() {
        po = new ProductItemSaleUnit();
        po.setProductItemId(1L);
        po.setProductType(1);
        po.setProductId(1L);
        po.setSkuId(1L);
        po.setAmount(1);
        po.setStatus(1);
    }

    private boolean invokeAddItemSaleUnit(ProductItemSaleUnit po) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("addItemSaleUnit", ProductItemSaleUnit.class);
        method.setAccessible(true);
        try {
            return (boolean) method.invoke(productItemSaleUnitDataService, po);
        } catch (Exception e) {
            if (e.getCause() instanceof ProductItemException) {
                throw (ProductItemException) e.getCause();
            }
            throw e;
        }
    }

    @Test(expected = ProductItemException.class)
    public void testAddItemSaleUnitNullPo() throws Throwable {
        invokeAddItemSaleUnit(null);
    }

    @Test(expected = ProductItemException.class)
    public void testAddItemSaleUnitInvalidPo() throws Throwable {
        po.setProductItemId(null);
        invokeAddItemSaleUnit(po);
    }

    @Test
    public void testAddItemSaleUnitInsertSuccess() throws Throwable {
        when(mapper.insertSelective(any(ProductItemSaleUnit.class))).thenReturn(1);
        boolean result = invokeAddItemSaleUnit(po);
        assertTrue(result);
        verify(mapper, times(1)).insertSelective(any(ProductItemSaleUnit.class));
    }

    @Test
    public void testAddItemSaleUnitInsertFail() throws Throwable {
        when(mapper.insertSelective(any(ProductItemSaleUnit.class))).thenReturn(0);
        boolean result = invokeAddItemSaleUnit(po);
        assertFalse(result);
        verify(mapper, times(1)).insertSelective(any(ProductItemSaleUnit.class));
    }
}
