package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;


import com.dianping.tpfun.publish.api.sku.entity.ProductEntity;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.product.ProductAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.wechat.WechatUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl.PrivateLiveConsultantPerformanceGatewayServiceImpl;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantOrderService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.sankuai.dz.srcm.user.dto.LiveUserInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

public class PrivateLiveConsultantPerformanceGatewayServiceImplTest {

    @Mock
    private PrivateLiveConsultantOrderService privateLiveConsultantOrderService;

    @Mock
    private PrivateLiveConsultantSummaryService privateLiveConsultantSummaryService;

    @Mock
    private ProductAclService productAclService;

    @Mock
    private UserAuthorizeUtil userAuthorizeUtil;

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;

    @Mock
    private WechatUserAclService wechatUserAclService;

    @InjectMocks
    private PrivateLiveConsultantPerformanceGatewayServiceImpl service;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：当用户鉴权信息为空时
     */
    @Test
    public void testPageList_WhenUserAuthInfoIsNull() {
        // arrange
        OrderPerformanceRequest request = new OrderPerformanceRequest();
        request.setTaskId(1L);
        request.setWxToken("token");

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(null);

        // act
        PaginationRemoteResponse<OrderPerformanceDTO> result = service.pageList(request);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：当用户鉴权信息为空时
     */
    @Test
    public void testTabList_WhenUserAuthInfoIsNull() {
        // arrange

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(null);

        // act
        RemoteResponse<List<CommonFilterItemDTO>> res = service.tabList(1L, "token");
        // assert
        assertNull(res.getData());
    }

    /**
     * 测试场景：当用户鉴权信息为空时
     */
    @Test
    public void testGmvSummary_WhenUserAuthInfoIsNull() {
        // arrange
        CounselorGmvSummaryRequest request = new CounselorGmvSummaryRequest();
        request.setTaskId(1L);
        request.setWxToken("token");

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(null);

        // act
        RemoteResponse<CounselorGmvSummaryDTO> res = service.gmvSummary(request);
        // assert
        assertNull(res.getData());
    }


    /**
     * 测试场景：当订单列表查询成功，但返回空列表时
     */
    @Test
    public void testPageList_WhenOrderListIsEmpty() {
        // arrange
        OrderPerformanceRequest request = new OrderPerformanceRequest();
        request.setTaskId(1L);
        request.setWxToken("token");

        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setConsultantTaskId(1L);

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(dto);
        when(privateLiveConsultantOrderService.queryConsultantOrderInfo(any())).thenReturn(PaginationRemoteResponse.success(new ArrayList<>(), 0));

        // act
        PaginationRemoteResponse<OrderPerformanceDTO> result = service.pageList(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：当订单列表查询失败时
     */
    @Test
    public void testPageList_WhenOrderListQueryFails() {
        // arrange
        OrderPerformanceRequest request = new OrderPerformanceRequest();
        request.setTaskId(1L);
        request.setWxToken("token");

        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setConsultantTaskId(1L);

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(dto);
        when(privateLiveConsultantOrderService.queryConsultantOrderInfo(any())).thenReturn(PaginationRemoteResponse.failure("查询失败"));

        // act
        PaginationRemoteResponse<OrderPerformanceDTO> result = service.pageList(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：当订单列表查询成功，且返回非空列表时
     */
    @Test
    public void testPageList_WhenOrderListIsNotEmpty() {
        // arrange
        OrderPerformanceRequest request = new OrderPerformanceRequest();
        request.setTaskId(1L);
        request.setWxToken("token");

        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setConsultantTaskId(1L);

        PrivateLiveConsultantOrderInfoDTO privateLiveConsultantOrderInfoDTO = new PrivateLiveConsultantOrderInfoDTO();
        privateLiveConsultantOrderInfoDTO.setLongOrderId(1234L);
        privateLiveConsultantOrderInfoDTO.setOrderId("1234");
        privateLiveConsultantOrderInfoDTO.setMtProductId(3333333L);
        privateLiveConsultantOrderInfoDTO.setProductType(5);
        privateLiveConsultantOrderInfoDTO.setMtUserId(2333L);
        privateLiveConsultantOrderInfoDTO.setTotalMoney(123L);
        privateLiveConsultantOrderInfoDTO.setProductNum(1);
        privateLiveConsultantOrderInfoDTO.setAddTime(new Date());
        privateLiveConsultantOrderInfoDTO.setStatus(1);
        privateLiveConsultantOrderInfoDTO.setOrderGroupStatus(1);

        List<PrivateLiveConsultantOrderInfoDTO> orderInfoList = Lists.newArrayList(privateLiveConsultantOrderInfoDTO);

        PaginationRemoteResponse<PrivateLiveConsultantOrderInfoDTO> response = PaginationRemoteResponse.success(orderInfoList, 1);

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(dto);

        ProductEntity productEntity = new ProductEntity();
        productEntity.setId(3333333);
        productEntity.setName("测试商品");

        List<ProductEntity> products = Lists.newArrayList(productEntity);

        when(productAclService.getProducts(any())).thenReturn(products);
        LiveUserInfoDTO liveUserInfoDTO = new LiveUserInfoDTO();
        liveUserInfoDTO.setNickName("测试1");
        liveUserInfoDTO.setAvatar("测试22");
        when(privateSphereUserAclService.queryLiveUserByMtUserId(anyLong(),anyLong())).thenReturn(liveUserInfoDTO);

        when(privateLiveConsultantOrderService.queryConsultantOrderInfo(any())).thenReturn(response);

        // act
        PaginationRemoteResponse<OrderPerformanceDTO> result = service.pageList(request);

        // assert
        assertNotNull(result);
    }
}