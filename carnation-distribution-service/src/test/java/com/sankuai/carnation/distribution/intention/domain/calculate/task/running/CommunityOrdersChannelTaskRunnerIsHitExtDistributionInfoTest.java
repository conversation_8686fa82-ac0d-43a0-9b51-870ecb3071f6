package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import org.apache.commons.collections4.MapUtils;
import java.util.HashMap;
import java.util.Map;
import com.sankuai.carnation.distribution.intention.dto.DistributorDetailDTO;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderDiscountBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.common.PassParamDistributionBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.coupon.record.bo.UserIntentionCouponBO;
import com.sankuai.carnation.distribution.intention.domain.user.code.bo.UserCodeIntentionBO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.product.bargain.medical.domain.bo.MedicalBargainProductBO;
import com.sankuai.carnation.distribution.product.bargain.medical.utils.MedicalBargainProductConstants;
import com.sankuai.carnation.distribution.product.bargain.medical.utils.MedicalBargainProductIntentionUtils;
import com.sankuai.dz.srcm.automatedmanagement.request.coupon.attribution.ScrmCouponAttributionRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.coupon.attribution.ScrmCouponAttributionResponse;
import com.sankuai.dz.srcm.automatedmanagement.service.ScrmCouponAttributionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class CommunityOrdersChannelTaskRunnerIsHitExtDistributionInfoTest {

    private CommunityOrdersChannelTaskRunner communityOrdersChannelTaskRunner;

    @Before
    public void setUp() {
        communityOrdersChannelTaskRunner = new CommunityOrdersChannelTaskRunner();
    }

    @Test
    public void testIsHitExtDistributionInfoWhenExtDistributionInfoIsNull() throws Throwable {
        Map<String, String> extDistributionInfo = null;
        boolean result = communityOrdersChannelTaskRunner.isHitExtDistributionInfo(extDistributionInfo);
        assertFalse(result);
    }

    @Test
    public void testIsHitExtDistributionInfoWhenExtDistributionInfoIsNotEmptyButNotContainsCommunityCode() throws Throwable {
        Map<String, String> extDistributionInfo = new HashMap<>();
        MapUtils.putAll(extDistributionInfo, new Object[] { "otherKey", "otherValue" });
        boolean result = communityOrdersChannelTaskRunner.isHitExtDistributionInfo(extDistributionInfo);
        assertFalse(result);
    }

    @Test
    public void testIsHitExtDistributionInfoWhenExtDistributionInfoIsNotEmptyAndContainsCommunityCode() throws Throwable {
        Map<String, String> extDistributionInfo = new HashMap<>();
        MapUtils.putAll(extDistributionInfo, new Object[] { "community", "社群" });
        boolean result = communityOrdersChannelTaskRunner.isHitExtDistributionInfo(extDistributionInfo);
        assertTrue(result);
    }
}
