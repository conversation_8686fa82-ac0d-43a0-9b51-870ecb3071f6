package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.bcp.PromoCodeBcpCheckServiceImpl;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.promocode.rebate.domain.TechPromoCodeRebateDomainService;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.Date;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplCalculateNoFrameRebateAmount1Test {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private OrderReceiptQueryService orderReceiptQueryService;

    @Mock
    private UserScanRecordService userScanRecordService;

    @Mock
    private TechPromoCodeRebateDomainService techPromoCodeRebateDomainService;

    private CommissionVerifyCalculationRequest request;

    private OrderInfoBO orderInfoBO;

    @Before
    public void setUp() {
        request = new CommissionVerifyCalculationRequest();
        request.setOrderId("123");
        orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderTime(new Date());
    }

    @Test
    public void testCalculateNoFrameRebateAmountNormal() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountVerifyTimeGreaterThanOrderTime() throws Throwable {
        orderInfoBO.setOrderTime(new Date(System.currentTimeMillis() - 10000));
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountNoScan() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountScanTimeExceedLimit() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountOrderReceiptIsNull() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountTechApplyRecordIdIsZero() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountActivityRuleIsNull() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountActivityTypeIsNotNormal() throws Throwable {
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }
}
