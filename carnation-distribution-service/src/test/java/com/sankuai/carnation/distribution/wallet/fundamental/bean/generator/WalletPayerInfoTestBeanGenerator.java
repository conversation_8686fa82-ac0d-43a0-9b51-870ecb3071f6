package com.sankuai.carnation.distribution.wallet.fundamental.bean.generator;

import com.sankuai.carnation.distribution.wallet.payer.domain.PayerInfo;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/6
 **/
public class WalletPayerInfoTestBeanGenerator {

    public static PayerInfo generatePayerInfo() {
        PayerInfo payerInfo = new PayerInfo();
        payerInfo.setBizCode(WalletTestConstants.mockBizCode);
        payerInfo.setBizLine(WalletTestConstants.mockBizLine);
        payerInfo.setPayer(WalletTestConstants.mockPayer);
        return payerInfo;
    }
}
