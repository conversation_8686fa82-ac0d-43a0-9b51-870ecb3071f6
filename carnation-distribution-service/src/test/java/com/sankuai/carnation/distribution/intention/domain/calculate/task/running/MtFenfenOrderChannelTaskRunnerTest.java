package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.dto.DistributorDTO;
import com.sankuai.carnation.distribution.distributor.service.DistributorQueryService;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.dto.DistributionIntentionDTO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.service.DistributionIntentionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MtFenfenOrderChannelTaskRunnerTest {

    @InjectMocks
    private MtFenfenOrderChannelTaskRunner mtFenfenOrderChannelTaskRunner;

    @Mock
    private DistributionIntentionService intentionService;

    @Mock
    private DistributorQueryService distributorQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetSupportTaskType() {
        assertEquals(OrderChannelCalculateTaskTypeEnum.MT_FENFEN_INTENTION.getCode(), mtFenfenOrderChannelTaskRunner.getSupportTaskType());
    }

    @Test
    public void testIsOrderHitTask() {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfo = new OrderInfoBO();
        // 模拟 hitTaskAccordingByDistributionCode 方法
        MtFenfenOrderChannelTaskRunner spyRunner = spy(mtFenfenOrderChannelTaskRunner);
        doReturn(true).when(spyRunner).hitTaskAccordingByDistributionCode(taskParam, orderInfo);
        assertTrue(spyRunner.isOrderHitTask(taskParam, orderInfo));
    }

    @Test
    public void testCalculate() throws Exception {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setDistributionCode("testCode");
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();

        DistributionIntentionDTO intentionDTO = new DistributionIntentionDTO();
        intentionDTO.setContainsIntention(true);
        intentionDTO.setDistributorId(1L);
        when(intentionService.queryDistributionIntentionByOrder(any())).thenReturn(RemoteResponse.success(intentionDTO));

        DistributorDTO distributorDTO = new DistributorDTO();
        distributorDTO.setDistributorCode("testCode");
        RemoteResponse<DistributorDTO> distributorResponse = RemoteResponse.success(distributorDTO);
        when(distributorQueryService.getDistributor(1L)).thenReturn(distributorResponse);

        OrderChannelTaskRunningResultBO result = mtFenfenOrderChannelTaskRunner.calculate(taskParam, orderInfoBO, task);

        assertNotNull(result);
        assertEquals(DistributionBusinessChannelEnum.MT_FENFEN.getCode(), result.getBusinessChannel());
        assertEquals("testCode", result.getDistributionCode());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
        assertNotNull(result.getDistributor());
    }

    @Test
    public void testIsSyncTask() {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        assertTrue(mtFenfenOrderChannelTaskRunner.isSyncTask(taskParam));
    }
}
