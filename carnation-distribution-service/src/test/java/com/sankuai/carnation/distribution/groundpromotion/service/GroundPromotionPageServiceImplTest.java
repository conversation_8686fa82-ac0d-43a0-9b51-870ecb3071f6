package com.sankuai.carnation.distribution.groundpromotion.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.gmkt.event.api.api.EventRpcService;
import com.dianping.gmkt.event.api.enums.EventErrorCode;
import com.dianping.gmkt.event.api.enums.PrizeType;
import com.dianping.gmkt.event.api.enums.UserPlatform;
import com.dianping.gmkt.event.api.promoqrcode.dto.PromoQRCodeActivityRequestDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.ActivityDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.AwardConfigDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.AwardDTO;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService;
import com.dianping.gmkt.event.api.v2.model.CouponInfo;
import com.dianping.gmkt.event.api.v2.model.CouponPrizeDetail;
import com.dianping.gmkt.event.api.v2.model.DrawResult;
import com.dianping.gmkt.event.api.v2.model.EventInfo;
import com.dianping.gmkt.event.api.v2.model.PigeonResponse;
import com.dianping.gmkt.event.api.v2.model.RichDrawLog;
import com.dianping.gmkt.event.api.v2.request.EidRequest;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.idmapper.enums.ServiceTypeEnum;
import com.meituan.beauty.idmapper.service.UserMapperService;
import com.meituan.mdp.pamela.sdk.PamelaReporter;
import com.meituan.mdp.pamela.sdk.reporter.Reporter;
import com.meituan.mdp.pamela.sdk.reporter.ReporterDecorators;
import com.sankuai.carnation.distribution.distributor.service.BizDistributorService;
import com.sankuai.carnation.distribution.groundpromotion.dto.CouponInfoDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.DrawCouponRequest;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryCouponRequest;
import com.sankuai.carnation.distribution.groundpromotion.exception.GroundPromotionException;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.GroundPromotionQueryService;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.PoiAcl;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundDrawLogRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPromotionAdvancedInfoRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPromotionProjectDealRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPromotionRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPromotionTemplateRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundQrLogRepository;
import com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.ResourceDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeForCService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionPageServiceImplTest {

    @InjectMocks
    private GroundPromotionPageServiceImpl groundPromotionPageService;

    @Mock
    private EventRpcService eventRpcService;

    private Method drawAndGetCouponInfosMethod;

    @Mock
    private AwardInfoDTO awardInfoDTO;

    @Mock
    private AwardActivityDTO awardActivityDTO;

    private int platform = 1;

    private Date now = new Date();

    @Mock
    private PromoCodeForCService promoCodeForCService;

    private long dpShopId = 1L;

    private int qrClientType = 1;

    @Before
    public void setUp() throws NoSuchMethodException {
        drawAndGetCouponInfosMethod = GroundPromotionPageServiceImpl.class.getDeclaredMethod("drawAndGetCouponInfos", EidRequest.class);
        drawAndGetCouponInfosMethod.setAccessible(true);
    }

    private List<String> invokePrivateMethod(int platform, AwardInfoDTO awardInfoDTO) throws Exception {
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getActivityMultiAwardECodes", int.class, AwardInfoDTO.class);
        method.setAccessible(true);
        return (List<String>) method.invoke(groundPromotionPageService, platform, awardInfoDTO);
    }

    private boolean invokeQueryCouponSettingFromPromoCode(long dpShopId, int qrClientType) throws Exception {
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("queryCouponSettingFromPromoCode", long.class, int.class);
        method.setAccessible(true);
        return (boolean) method.invoke(groundPromotionPageService, dpShopId, qrClientType);
    }

    // Helper method to create QueryCouponRequest instance using reflection
    private QueryCouponRequest createQueryCouponRequest() throws Exception {
        Constructor<QueryCouponRequest> constructor = QueryCouponRequest.class.getDeclaredConstructor(Long.class, Long.class, Long.class, int.class, int.class, long.class, int.class, int.class);
        constructor.setAccessible(true);
        return constructor.newInstance(1L, 1L, 1L, 1, 1, 1L, 1, 1);
    }

    // Helper method to invoke private method getDrawLogs using reflection
    private List<RichDrawLog> invokeGetDrawLogs(QueryCouponRequest queryCouponRequest) throws Exception {
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawLogs", QueryCouponRequest.class);
        method.setAccessible(true);
        return (List<RichDrawLog>) method.invoke(groundPromotionPageService, queryCouponRequest);
    }

    private AwardActivityDTO createAwardDTO(String code, Date startTime, Date endTime, Integer platform) {
        AwardActivityDTO awardDTO = new AwardActivityDTO();
        awardDTO.setCode(code);
        awardDTO.setStartTime(startTime);
        awardDTO.setEndTime(endTime);
        awardDTO.setPlatform(platform);
        return awardDTO;
    }

    private String invokePrivateMethod(String methodName, Class<?>[] parameterTypes, Object[] parameters) throws Exception {
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return (String) method.invoke(groundPromotionPageService, parameters);
    }

    @Test
    public void testDrawAndGetCouponInfos() throws Throwable {
        // Arrange
        EidRequest eidRequest = new EidRequest();
        eidRequest.setEcode("testEcode");
        EventInfo eventInfo = new EventInfo();
        eventInfo.setStartTime(new Date(System.currentTimeMillis() - 100000));
        eventInfo.setEndTime(new Date(System.currentTimeMillis() + 100000));
        PigeonResponse<EventInfo> eventInfoResponse = new PigeonResponse<>(eventInfo);
        eventInfoResponse.setCode("200");
        eventInfoResponse.setErrorCode(EventErrorCode.SUCCESS.getCode());
        DrawResult drawResult = new DrawResult();
        // Fix: Convert int to Byte
        drawResult.setType(Byte.valueOf((byte) PrizeType.Coupon_Group.code));
        CouponPrizeDetail couponPrizeDetail = new CouponPrizeDetail();
        couponPrizeDetail.setPrizeCouponDetail(Collections.singletonList(new CouponInfo()));
        drawResult.setPrizeData(couponPrizeDetail);
        PigeonResponse<DrawResult> drawResultResponse = new PigeonResponse<>(drawResult);
        drawResultResponse.setCode("200");
        drawResultResponse.setErrorCode(EventErrorCode.SUCCESS.getCode());
        when(eventRpcService.queryActivity(anyString())).thenReturn(eventInfoResponse);
        when(eventRpcService.attendEvent(any(EidRequest.class))).thenReturn(drawResultResponse);
        // Act
        List<CouponInfo> result = (List<CouponInfo>) drawAndGetCouponInfosMethod.invoke(groundPromotionPageService, eidRequest);
        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetActivityMultiAwardECodesWhenAwardConfigIsNull() throws Throwable {
        List<String> result = invokePrivateMethod(platform, null);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetActivityMultiAwardECodesWhenMultiAwardConfigIsEmpty() throws Throwable {
        when(awardInfoDTO.getMultiAwardConfig()).thenReturn(new ArrayList<>());
        List<String> result = invokePrivateMethod(platform, awardInfoDTO);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetActivityMultiAwardECodesWhenAwardDTOListIsEmpty() throws Throwable {
        List<AwardActivityDTO> awardDTOList = new ArrayList<>();
        List<List<AwardActivityDTO>> multiAwardConfig = Arrays.asList(awardDTOList);
        when(awardInfoDTO.getMultiAwardConfig()).thenReturn(multiAwardConfig);
        List<String> result = invokePrivateMethod(platform, awardInfoDTO);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetActivityMultiAwardECodesWhenGetValidECodeReturnsEmpty() throws Throwable {
        List<AwardActivityDTO> awardDTOList = Arrays.asList(awardActivityDTO);
        List<List<AwardActivityDTO>> multiAwardConfig = Arrays.asList(awardDTOList);
        when(awardInfoDTO.getMultiAwardConfig()).thenReturn(multiAwardConfig);
        List<String> result = invokePrivateMethod(platform, awardInfoDTO);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeResponseIsNull() throws Throwable {
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(null);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeResponseIsNotSuccess() throws Throwable {
        RemoteResponse<OperationInfoDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(false);
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(response);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeDataIsNull() throws Throwable {
        RemoteResponse<OperationInfoDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(null);
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(response);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeResourceConfigIsNull() throws Throwable {
        RemoteResponse<OperationInfoDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        OperationInfoDTO operationInfoDTO = mock(OperationInfoDTO.class);
        when(response.getData()).thenReturn(operationInfoDTO);
        when(operationInfoDTO.getResourceConfig()).thenReturn(null);
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(response);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeMainECodeIsEmpty() throws Throwable {
        RemoteResponse<OperationInfoDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        OperationInfoDTO operationInfoDTO = mock(OperationInfoDTO.class);
        when(response.getData()).thenReturn(operationInfoDTO);
        ResourceDTO resourceDTO = mock(ResourceDTO.class);
        when(operationInfoDTO.getResourceConfig()).thenReturn(resourceDTO);
        when(resourceDTO.getAwardInfo()).thenReturn(null);
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(response);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeMultiECodesIsEmpty() throws Throwable {
        RemoteResponse<OperationInfoDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        OperationInfoDTO operationInfoDTO = mock(OperationInfoDTO.class);
        when(response.getData()).thenReturn(operationInfoDTO);
        ResourceDTO resourceDTO = mock(ResourceDTO.class);
        when(operationInfoDTO.getResourceConfig()).thenReturn(resourceDTO);
        when(resourceDTO.getAwardInfo()).thenReturn(null);
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(response);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    @Test
    public void testQueryCouponSettingFromPromoCodeECodesIsEmpty() throws Throwable {
        RemoteResponse<OperationInfoDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        OperationInfoDTO operationInfoDTO = mock(OperationInfoDTO.class);
        when(response.getData()).thenReturn(operationInfoDTO);
        ResourceDTO resourceDTO = mock(ResourceDTO.class);
        when(operationInfoDTO.getResourceConfig()).thenReturn(resourceDTO);
        when(resourceDTO.getAwardInfo()).thenReturn(null);
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(response);
        assertFalse(invokeQueryCouponSettingFromPromoCode(dpShopId, qrClientType));
    }

    /**
     * 测试 awardConfig 为 null 的情况
     */
    @Test
    public void testGetActivityMainAwardECodeAwardConfigIsNull() throws Throwable {
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getActivityMainAwardECode", int.class, AwardInfoDTO.class);
        method.setAccessible(true);
        String result = (String) method.invoke(groundPromotionPageService, 1, null);
        assertEquals("", result);
    }

    /**
     * 测试 awardConfig.getMainAwardConfig() 为空的情况
     */
    @Test
    public void testGetActivityMainAwardECodeMainAwardConfigIsEmpty() throws Throwable {
        when(awardInfoDTO.getMainAwardConfig()).thenReturn(null);
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getActivityMainAwardECode", int.class, AwardInfoDTO.class);
        method.setAccessible(true);
        String result = (String) method.invoke(groundPromotionPageService, 1, awardInfoDTO);
        assertEquals("", result);
    }

    @Test
    public void testGetDrawLogsWhenPromoCodeForCServiceReturnsNull() throws Throwable {
        QueryCouponRequest queryCouponRequest = createQueryCouponRequest();
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(null);
        List<RichDrawLog> result = invokeGetDrawLogs(queryCouponRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDrawLogsWhenPromoCodeForCServiceReturnsEmptyResponse() throws Throwable {
        QueryCouponRequest queryCouponRequest = createQueryCouponRequest();
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(null);
        List<RichDrawLog> result = invokeGetDrawLogs(queryCouponRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDrawLogsWhenPromoCodeForCServiceReturnsValidResponse() throws Throwable {
        QueryCouponRequest queryCouponRequest = createQueryCouponRequest();
        when(promoCodeForCService.queryOperationConfig(any())).thenReturn(null);
        List<RichDrawLog> result = invokeGetDrawLogs(queryCouponRequest);
        assertNotNull(result);
        // Additional assertions can be added based on the expected behavior
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyAndCodeIsNotEmptyAndQueryActivityReturnsNonNullAndCodeIs200AndErrorCodeIsSuccessAndPlatformMatches() throws Throwable {
        // Create an AwardActivityDTO with the correct platform
        // 确保开始时间在now之前
        Date startTime = new Date(now.getTime() - 1000);
        // 确保结束时间在now之后
        Date endTime = new Date(now.getTime() + 10000);
        AwardActivityDTO awardDTO = createAwardDTO("code", startTime, endTime, platform);
        // Create an EventInfo with the correct platform and date range
        EventInfo eventInfo = new EventInfo();
        eventInfo.setStartTime(startTime);
        eventInfo.setEndTime(endTime);
        eventInfo.setPlatform(com.dianping.gmkt.event.api.enums.UserPlatform.getByCode(platform));
        // Mock the queryActivity method to return a valid PigeonResponse with SUCCESS error code
        PigeonResponse<EventInfo> response = new PigeonResponse<>(true, EventErrorCode.SUCCESS.getCode(), "success", eventInfo);
        when(eventRpcService.queryActivity("code")).thenReturn(response);
        // Invoke the private method and assert the result
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("code", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsEmpty() throws Throwable {
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Collections.emptyList(), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyButAllCodesAreEmpty() throws Throwable {
        AwardActivityDTO awardDTO = createAwardDTO("", now, new Date(now.getTime() + 10000), platform);
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyButQueryActivityReturnsNull() throws Throwable {
        AwardActivityDTO awardDTO = createAwardDTO("code", now, new Date(now.getTime() + 10000), platform);
        when(eventRpcService.queryActivity("code")).thenReturn(null);
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyAndCodeIsNotEmptyButQueryActivityReturnsNonNullButCodeIsNot200() throws Throwable {
        AwardActivityDTO awardDTO = createAwardDTO("code", now, new Date(now.getTime() + 10000), platform);
        when(eventRpcService.queryActivity("code")).thenReturn(new PigeonResponse<>(false, "500", "error", null));
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyAndCodeIsNotEmptyAndQueryActivityReturnsNonNullAndCodeIs200ButErrorCodeIsNotSuccess() throws Throwable {
        AwardActivityDTO awardDTO = createAwardDTO("code", now, new Date(now.getTime() + 10000), platform);
        when(eventRpcService.queryActivity("code")).thenReturn(new PigeonResponse<>(false, "500", "error", null));
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyAndCodeIsNotEmptyAndQueryActivityReturnsNonNullAndCodeIs200AndErrorCodeIsSuccessButPlatformDoesNotMatch() throws Throwable {
        Date startTime = new Date(now.getTime() - 1000);
        Date endTime = new Date(now.getTime() + 10000);
        AwardActivityDTO awardDTO = createAwardDTO("code", startTime, endTime, platform + 1);
        EventInfo eventInfo = new EventInfo();
        eventInfo.setStartTime(startTime);
        eventInfo.setEndTime(endTime);
        eventInfo.setPlatform(com.dianping.gmkt.event.api.enums.UserPlatform.getByCode(platform + 1));
        when(eventRpcService.queryActivity("code")).thenReturn(new PigeonResponse<>(true, EventErrorCode.SUCCESS.getCode(), "success", eventInfo));
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyAndCodeIsNotEmptyButQueryActivityReturnsNull() throws Throwable {
        AwardActivityDTO awardDTO = createAwardDTO("code", now, new Date(now.getTime() + 10000), platform);
        when(eventRpcService.queryActivity("code")).thenReturn(null);
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }

    @Test
    public void testGetValidECodeWhenAwardDTOListIsNotEmptyAndCodeIsNotEmptyAndQueryActivityReturnsNonNullButCodeIsNot200() throws Throwable {
        AwardActivityDTO awardDTO = createAwardDTO("code", now, new Date(now.getTime() + 10000), platform);
        when(eventRpcService.queryActivity("code")).thenReturn(new PigeonResponse<>(false, "500", "error", null));
        String result = invokePrivateMethod("getValidECode", new Class<?>[] { int.class, List.class, Date.class }, new Object[] { platform, Arrays.asList(awardDTO), now });
        assertEquals("", result);
    }
}
