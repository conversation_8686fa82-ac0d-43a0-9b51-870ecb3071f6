package com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAnchorAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantApplicantDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantTaskDataDetailDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PrivateLiveConsultantBaseRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.ConsultantTaskSummaryDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.TaskDataDetailModuleEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserTypeCount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import com.sankuai.dz.srcm.pchat.service.ScrmLiveManageService;
import com.sankuai.dz.srcm.user.dto.PrivateSphereCustomerCapitalOverview;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTagStatDTO;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTagStatRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomsQueryRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.live.LiveStatusEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.saasconfig.TraceAbilityTypeEnum;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginCustomerInfo;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginUserInfo;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.mockito.Mockito;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveTaskForCGatewayServiceImplConstructFollowTipsTest {

    @Mock
    private UserAuthorizeUtil userAuthorizeUtil;

    @Mock
    private PrivateLiveConsultantAccountRepository accountRepository;

    @Mock
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Mock
    private PrivateLiveConsultantSummaryService summaryService;

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;

    @Mock
    private RedisStoreClient redisStoreClient;

    @Mock
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Mock
    private PrivateLiveAnchorAclService privateLiveAnchorAclService;

    @Mock
    private WeChatUserService weChatUserService;

    @Mock
    private PrivateLiveAclService privateLiveAclService;

    @Mock
    private ScrmLiveManageService scrmLiveManageService;

    private PrivateLiveConsultantApplicantDTO applicantDTO;

    private ConsultantTaskSummaryDTO summaryDTO;

    private PrivateSphereCustomerCapitalOverview capitalOverview;

    private List<PrivateLiveUserTypeCount> privateLiveUserTypeCount;

    private String hotProduct;

    @Before
    public void setUp() {
        // Initialize applicantDTO
        applicantDTO = new PrivateLiveConsultantApplicantDTO();
        applicantDTO.setConsultantTaskId(123L);
        applicantDTO.setLiveId("liveId");
        // Initialize summaryDTO
        summaryDTO = new ConsultantTaskSummaryDTO();
        summaryDTO.setConsultantTaskId(123L);
        // Initialize capitalOverview
        capitalOverview = new PrivateSphereCustomerCapitalOverview();
        // Initialize privateLiveUserTypeCount using setters
        PrivateLiveUserTypeCount userTypeCount = new PrivateLiveUserTypeCount();
        userTypeCount.setConsultantTaskId(123L);
        userTypeCount.setType(1);
        userTypeCount.setCount(10);
        privateLiveUserTypeCount = Arrays.asList(userTypeCount);
        // Initialize hotProduct
        hotProduct = "product";
        // Mock redisStoreClient behavior with explicit StoreKey parameter
        when(redisStoreClient.<String>get(any(StoreKey.class))).thenReturn(null);
    }

    private PrivateLiveConsultantTaskDataDetailDTO.FollowTips invokeConstructFollowTips(TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum) throws Exception {
        Method method = PrivateLiveTaskForCGatewayServiceImpl.class.getDeclaredMethod("constructFollowTips", TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.class, ConsultantTaskSummaryDTO.class, PrivateSphereCustomerCapitalOverview.class, PrivateLiveConsultantApplicantDTO.class, List.class, String.class);
        method.setAccessible(true);
        return (PrivateLiveConsultantTaskDataDetailDTO.FollowTips) method.invoke(service, taskDataDetailModuleFollowEnum, summaryDTO, capitalOverview, applicantDTO, privateLiveUserTypeCount, hotProduct);
    }

    @Test
    public void testConstructFollowTipsSeedFollow() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("SEED_FOLLOW");
        when(scrmLiveManageService.queryLiveWxType(any(String.class))).thenReturn(RemoteResponse.success(1));
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testConstructFollowTipsOther() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("LOST_FOLLOW");
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNull(result);
    }

    @Test
    public void testConstructFollowTipsLostFollow() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("LOST_FOLLOW");
        summaryDTO.setWaitToPayOrderCnt(1);
        summaryDTO.setCancelOrderCnt(1);
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testConstructFollowTipsPaidFollow() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("PAID_FOLLOW");
        summaryDTO.setPayOrderCnt(1);
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testConstructFollowTipsHotFollow() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("HOT_FOLLOW");
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testConstructFollowTipsHighIntentionFollow() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("HIGH_INTENTION_FOLLOW");
        capitalOverview.setHighIntentionSize(1L);
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testConstructFollowTipsUnVerifyFollow() throws Throwable {
        // arrange
        TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum = TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.valueOf("UN_VERIFY_FOLLOW");
        summaryDTO.setWaitToVerifyOrderCnt(1);
        // act
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips result = invokeConstructFollowTips(taskDataDetailModuleFollowEnum);
        // assert
        assertNotNull(result);
    }

    @InjectMocks
    private PrivateLiveTaskForCGatewayServiceImpl service;
}
