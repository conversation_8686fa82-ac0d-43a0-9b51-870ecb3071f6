package com.sankuai.carnation.distribution.aunt.converter;

import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import org.junit.Assert;
import org.junit.Test;

public class SelectifyProductTypeConverterTest {

    /**
     * 测试productType为null的情况
     */
    @Test
    public void testConverter2SelectifyProductTypeNull() {
        // arrange
        Integer productType = null;
        // act
        Integer result = SelectifyProductTypeConverter.converter2SelectifyProductType(productType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试productType等于TUAN_DEAL的情况
     */
    @Test
    public void testConverter2SelectifyProductTypeTuanDeal() {
        // arrange
        Integer productType = ProductTypeEnum.TUAN_DEAL.getCode();
        // act
        Integer result = SelectifyProductTypeConverter.converter2SelectifyProductType(productType);
        // assert
        Assert.assertEquals(Integer.valueOf(2), result);
    }

    /**
     * 测试productType等于UNKNOWN的情况
     */
    @Test
    public void testConverter2SelectifyProductTypeUnknown() {
        // arrange
        Integer productType = ProductTypeEnum.UNKNOWN.getCode();
        // act
        Integer result = SelectifyProductTypeConverter.converter2SelectifyProductType(productType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试productType等于MT_DEAL_GROUP的情况
     */
    @Test
    public void testConverter2SelectifyProductTypeMtDealGroup() {
        // arrange
        Integer productType = ProductTypeEnum.MT_DEAL_GROUP.getCode();
        // act
        Integer result = SelectifyProductTypeConverter.converter2SelectifyProductType(productType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试productType不等于TUAN_DEAL且不等于UNKNOWN且不等于MT_DEAL_GROUP的情况
     */
    @Test
    public void testConverter2SelectifyProductTypeOther() {
        // arrange
        Integer productType = ProductTypeEnum.TIME_CARD.getCode();
        // act
        Integer result = SelectifyProductTypeConverter.converter2SelectifyProductType(productType);
        // assert
        Assert.assertEquals(Integer.valueOf(1), result);
    }
}
