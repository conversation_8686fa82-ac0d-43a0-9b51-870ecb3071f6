package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;


import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.intention.modify.PrivateLiveIntentionModifyDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveIntentionModifyRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.IntentionModifyTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.PrivateLiveIntentionException;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionExpireTime;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveIntentionExpireTimeRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveIntentionModifyDomainServiceTest {


    @InjectMocks
    private PrivateLiveIntentionModifyDomainService modifyDomainService;

    @Mock
    private PrivateLiveIntentionExpireTimeRepository intentionExpireTimeRepository;

    @Mock
    private PrivateLiveDistributorCodeService distributorCodeService;

    @Mock
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testModifyOrderIntention_WxUser_ReturnsDirectly() throws PrivateLiveIntentionException {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setWxId("wxId");
        request.setMtUserId(null); // 模拟微信用户

        // act
        modifyDomainService.modifyIntention(request, IntentionModifyTypeEnum.ORDER_INTENTION_MODIFY.getCode());

        // assert
        // 由于微信用户不进行订单意向修改，验证方法是否能正常执行到最后，不抛出异常即为通过
    }

    @Test(expected = RuntimeException.class)
    public void testModifyOrderIntention_NewConsultantIdIsZero_ThrowsException() throws PrivateLiveIntentionException {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setMtUserId(1L);
        request.setNewConsultantTaskId(0L); // 新咨询师ID为0
        request.setLiveId("liveId");

        when(distributorCodeService.loadCodeByLiveId(anyString())).thenReturn(RemoteResponse.fail("新咨询师ID为0"));

        // act
        modifyDomainService.modifyIntention(request, IntentionModifyTypeEnum.ORDER_INTENTION_MODIFY.getCode());

        // assert
        // 预期抛出RuntimeException异常
    }

}
