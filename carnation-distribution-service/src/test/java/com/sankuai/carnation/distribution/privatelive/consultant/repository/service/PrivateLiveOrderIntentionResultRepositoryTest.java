package com.sankuai.carnation.distribution.privatelive.consultant.repository.service;

import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveOrderIntentionResultExample;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveOrderIntentionResultMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @Description:
 */
public class PrivateLiveOrderIntentionResultRepositoryTest {

    @InjectMocks
    private PrivateLiveOrderIntentionResultRepository repository;

    @Mock
    private PrivateLiveOrderIntentionResultMapper orderIntentionResultMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试查询顾问用户统计信息，所有参数均为null
     */
    @Test
    public void testQueryConsultantUserStatisticsAllParamsNull() {
        // arrange
        List<PrivateLiveConsultantUserStatistics> expected = new ArrayList<>();
        when(orderIntentionResultMapper.selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class))).thenReturn(expected);

        // act
        List<PrivateLiveConsultantUserStatistics> result = repository.queryConsultantUserStatistics(null, null, null);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(orderIntentionResultMapper, times(1)).selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class));
    }

    /**
     * 测试查询顾问用户统计信息，所有参数均有效
     */
    @Test
    public void testQueryConsultantUserStatisticsAllParamsValid() {
        // arrange
        List<PrivateLiveConsultantUserStatistics> expected = new ArrayList<>();
        when(orderIntentionResultMapper.selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class))).thenReturn(expected);

        // act
        List<PrivateLiveConsultantUserStatistics> result = repository.queryConsultantUserStatistics(1L, 1L, "liveId");

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(orderIntentionResultMapper, times(1)).selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class));
    }

    /**
     * 测试查询顾问用户统计信息，userId为负值
     */
    @Test
    public void testQueryConsultantUserStatisticsUserIdNegative() {
        // arrange
        List<PrivateLiveConsultantUserStatistics> expected = new ArrayList<>();
        when(orderIntentionResultMapper.selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class))).thenReturn(expected);

        // act
        List<PrivateLiveConsultantUserStatistics> result = repository.queryConsultantUserStatistics(1L, -1L, "liveId");

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(orderIntentionResultMapper, times(1)).selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class));
    }

    /**
     * 测试查询顾问用户统计信息，liveId为空字符串
     */
    @Test
    public void testQueryConsultantUserStatisticsLiveIdEmpty() {
        // arrange
        List<PrivateLiveConsultantUserStatistics> expected = new ArrayList<>();
        when(orderIntentionResultMapper.selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class))).thenReturn(expected);

        // act
        List<PrivateLiveConsultantUserStatistics> result = repository.queryConsultantUserStatistics(1L, 1L, "");

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(orderIntentionResultMapper, times(1)).selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class));
    }

    /**
     * 测试查询顾问用户统计信息，liveId为null
     */
    @Test
    public void testQueryConsultantUserStatisticsLiveIdNull() {
        // arrange
        List<PrivateLiveConsultantUserStatistics> expected = new ArrayList<>();
        when(orderIntentionResultMapper.selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class))).thenReturn(expected);

        // act
        List<PrivateLiveConsultantUserStatistics> result = repository.queryConsultantUserStatistics(1L, 1L, null);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(orderIntentionResultMapper, times(1)).selectSummaryDataByExample(any(PrivateLiveOrderIntentionResultExample.class));
    }

    @Test
    public void testQueryBossOrderByLiveIdNormal() {
        // arrange
        String liveId = "testLiveId";
        List<PrivateLiveOrderIntentionResult> expectedResults = Arrays.asList(
                new PrivateLiveOrderIntentionResult(),
                new PrivateLiveOrderIntentionResult()
        );
        when(orderIntentionResultMapper.selectByExample(any(PrivateLiveOrderIntentionResultExample.class))).thenReturn(expectedResults);

        // act
        List<PrivateLiveOrderIntentionResult> results = repository.queryBossOrderByLiveId(liveId);

        // assert
        assertNotNull(results);
        assertEquals(2, results.size());
        verify(orderIntentionResultMapper, times(1)).selectByExample(any(PrivateLiveOrderIntentionResultExample.class));
    }
}
