package com.sankuai.carnation.distribution.privatelive.consultant.repository.service;

import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantAccountExample;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantAccountMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PrivateLiveConsultantAccountRepositoryTest {

    @InjectMocks
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @Mock
    private PrivateLiveConsultantAccountMapper privateLiveConsultantAccountMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试根据unionId和openId加载账户，账户存在的场景
     */
    @Test
    public void testLoadByUnionIdAndOpenId_AccountExist() {
        // arrange
        String unionId = "testUnionId";
        String openId = "testOpenId";
        PrivateLiveConsultantAccount expectedAccount = new PrivateLiveConsultantAccount(1L, unionId, openId, 2L, 1, null, null);
        List<PrivateLiveConsultantAccount> accountList = new ArrayList<>();
        accountList.add(expectedAccount);
        when(privateLiveConsultantAccountMapper.selectByExample(any(PrivateLiveConsultantAccountExample.class))).thenReturn(accountList);

        // act
        PrivateLiveConsultantAccount result = privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(unionId, openId);

        // assert
        assertNotNull(result);
        assertEquals(unionId, result.getUnionId());
        assertEquals(openId, result.getOpenId());
    }

    /**
     * 测试根据unionId和openId加载账户，账户不存在的场景
     */
    @Test
    public void testLoadByUnionIdAndOpenId_AccountNotExist() {
        // arrange
        String unionId = "nonExistUnionId";
        String openId = "nonExistOpenId";
        when(privateLiveConsultantAccountMapper.selectByExample(any(PrivateLiveConsultantAccountExample.class))).thenReturn(new ArrayList<>());

        // act
        PrivateLiveConsultantAccount result = privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(unionId, openId);

        // assert
        assertNull(result);
    }


}
