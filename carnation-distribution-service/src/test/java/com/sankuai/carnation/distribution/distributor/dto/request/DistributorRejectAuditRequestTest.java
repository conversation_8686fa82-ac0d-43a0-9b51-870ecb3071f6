package com.sankuai.carnation.distribution.distributor.dto.request;

import org.junit.Test;
import static org.junit.Assert.*;

public class DistributorRejectAuditRequestTest {

    /**
     * 测试 anchorId 的 getter 和 setter
     */
    @Test
    public void testAnchorIdGetterSetter() {
        DistributorRejectAuditRequest request = new DistributorRejectAuditRequest();
        request.setAnchorId(123L);
        assertEquals(Long.valueOf(123), request.getAnchorId());
    }

    /**
     * 测试 distributorId 的 getter 和 setter
     */
    @Test
    public void testDistributorIdGetterSetter() {
        DistributorRejectAuditRequest request = new DistributorRejectAuditRequest();
        request.setDistributorId(456L);
        assertEquals(Long.valueOf(456), request.getDistributorId());
    }

    /**
     * 测试使用构造函数初始化对象
     */
    @Test
    public void testConstructorInitialization() {
        DistributorRejectAuditRequest request = new DistributorRejectAuditRequest(123L, 456L);
        assertEquals(Long.valueOf(123), request.getAnchorId());
        assertEquals(Long.valueOf(456), request.getDistributorId());
    }

    /**
     * 测试使用 Builder 模式初始化对象
     */
    @Test
    public void testBuilderInitialization() {
        DistributorRejectAuditRequest request = DistributorRejectAuditRequest.builder()
                .anchorId(123L)
                .distributorId(456L)
                .build();
        assertEquals(Long.valueOf(123), request.getAnchorId());
        assertEquals(Long.valueOf(456), request.getDistributorId());
    }
}
