package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.limit.RebateRuleLimitInterface;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplGetRebateLimitResultTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private RebateRuleLimitInterface limitInterface;

    private CommissionVerifyCalculationRequest request;

    private RebateAmountResult rebateAmountResult;

    private RebateActivityRuleDTO rule;

    @Before
    public void setUp() throws Exception {
        request = new CommissionVerifyCalculationRequest();
        rebateAmountResult = new RebateAmountResult();
        rule = new RebateActivityRuleDTO();
        injectLimitInterfaceList(Collections.singletonList(limitInterface));
    }

    private void injectLimitInterfaceList(List<RebateRuleLimitInterface> limitInterfaces) throws Exception {
        Field limitInterfaceListField = PromoCodeBcpCheckServiceImpl.class.getDeclaredField("limitInterfaceList");
        limitInterfaceListField.setAccessible(true);
        limitInterfaceListField.set(promoCodeBcpCheckService, limitInterfaces);
    }

    @Test
    public void testGetRebateLimitResultWhenLimitInterfaceListIsEmpty() throws Throwable {
        injectLimitInterfaceList(Collections.emptyList());
        RebateLimitResult result = promoCodeBcpCheckService.getRebateLimitResult(request, rebateAmountResult, rule);
        assertFalse(result.isResult());
    }

    @Test
    public void testGetRebateLimitResultWhenAllHandleResultReturnFalse() throws Throwable {
        when(limitInterface.handleResult(request, rebateAmountResult, rule)).thenReturn(new RebateLimitResult());
        RebateLimitResult result = promoCodeBcpCheckService.getRebateLimitResult(request, rebateAmountResult, rule);
        assertFalse(result.isResult());
    }

    @Test
    public void testGetRebateLimitResultWhenExistHandleResultReturnTrue() throws Throwable {
        RebateLimitResult handleResult = new RebateLimitResult();
        handleResult.setResult(true);
        when(limitInterface.handleResult(request, rebateAmountResult, rule)).thenReturn(handleResult);
        RebateLimitResult result = promoCodeBcpCheckService.getRebateLimitResult(request, rebateAmountResult, rule);
        assertTrue(result.isResult());
    }
}
