package com.sankuai.carnation.distribution.privatelive.consultant.converter;

import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantApplicantDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalInfoRequest;
import org.junit.Test;
import static org.junit.Assert.*;

public class CustomerCapitalInfoRequestConverterTest {

    /**
     * 测试 converter 方法，当 PrivateLiveUserIntentionResult 对象的 wxId 字段值为空时
     */
    @Test
    public void testConverterWhenWxIdIsEmpty() {
        // arrange
        PrivateLiveUserIntentionResult request = new PrivateLiveUserIntentionResult();
        request.setUnionId("unionId");
        request.setUserId(1L);
        request.setLiveId("liveId");
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setConsultantTaskId(1L);
        // act
        CustomerCapitalInfoRequest result = CustomerCapitalInfoRequestConverter.converter(request, dto);
        // assert
        assertEquals("unionId", result.getUnionId());
        assertEquals(Long.valueOf(1L), result.getMtUserId());
        assertEquals("liveId", result.getLiveId());
        assertEquals(Long.valueOf(1L), result.getConsultantTaskId());
        assertNull(result.getWxId());
    }

    /**
     * 测试 converter 方法，当 PrivateLiveUserIntentionResult 对象的 wxId 字段值不为空时
     */
    @Test
    public void testConverterWhenWxIdIsNotEmpty() {
        // arrange
        PrivateLiveUserIntentionResult request = new PrivateLiveUserIntentionResult();
        request.setUnionId("unionId");
        request.setUserId(1L);
        request.setLiveId("liveId");
        request.setWxId("wxId");
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setConsultantTaskId(1L);
        // act
        CustomerCapitalInfoRequest result = CustomerCapitalInfoRequestConverter.converter(request, dto);
        // assert
        assertEquals("unionId", result.getUnionId());
        assertEquals(Long.valueOf(1L), result.getMtUserId());
        assertEquals("liveId", result.getLiveId());
        assertEquals(Long.valueOf(1L), result.getConsultantTaskId());
        assertEquals("wxId", result.getWxId());
    }
}
