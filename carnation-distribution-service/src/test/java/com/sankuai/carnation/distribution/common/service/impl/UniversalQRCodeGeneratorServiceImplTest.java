package com.sankuai.carnation.distribution.common.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.repository.dao.CommonQrCodeMapper;
import com.sankuai.carnation.distribution.common.repository.example.CommonQrCodeExample;
import com.sankuai.carnation.distribution.common.repository.model.CommonQrCode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UniversalQRCodeGeneratorServiceImplTest {

    @InjectMocks
    private UniversalQRCodeGeneratorServiceImpl universalQRCodeGeneratorService;

    @Mock
    private CommonQrCodeMapper commonQrCodeMapper;

    /**
     * 测试 secretKey 或 bizTypes 为空的情况
     */
    @Test
    public void testQueryQRCodeBySecretKeyAndBizTypesWithEmptyParams() throws Throwable {
        // arrange
        String secretKey = "";
        List<Integer> bizTypes = Collections.emptyList();
        // act
        RemoteResponse<QRCodeConfigDTO> response = universalQRCodeGeneratorService.queryQRCodeBySecretKeyAndBizTypes(secretKey, bizTypes);
        // assert
        assertEquals("参数校验失败", response.getMsg());
    }

    /**
     * 测试数据库查询结果为空的情况
     */
    @Test
    public void testQueryQRCodeBySecretKeyAndBizTypesWithEmptyResult() throws Throwable {
        // arrange
        String secretKey = "testSecretKey";
        List<Integer> bizTypes = Arrays.asList(1, 2, 3);
        when(commonQrCodeMapper.selectByExample(any(CommonQrCodeExample.class))).thenReturn(Collections.emptyList());
        // act
        RemoteResponse<QRCodeConfigDTO> response = universalQRCodeGeneratorService.queryQRCodeBySecretKeyAndBizTypes(secretKey, bizTypes);
        // assert
        assertEquals("数据库未查询到对应数据", response.getMsg());
    }

    /**
     * 测试数据库查询结果不为空的情况
     */
    @Test
    public void testQueryQRCodeBySecretKeyAndBizTypesWithNonEmptyResult() throws Throwable {
        // arrange
        String secretKey = "testSecretKey";
        List<Integer> bizTypes = Arrays.asList(1, 2, 3);
        CommonQrCode commonQrCode = new CommonQrCode();
        commonQrCode.setBizType(1);
        commonQrCode.setBizId(1L);
        commonQrCode.setBizIdStr("1");
        commonQrCode.setCodeUrl("http://test.com");
        commonQrCode.setCodeImage("http://test.com/image");
        commonQrCode.setAddTime(new java.util.Date());
        when(commonQrCodeMapper.selectByExample(any(CommonQrCodeExample.class))).thenReturn(Arrays.asList(commonQrCode));
        // act
        RemoteResponse<QRCodeConfigDTO> response = universalQRCodeGeneratorService.queryQRCodeBySecretKeyAndBizTypes(secretKey, bizTypes);
        // assert
        assertEquals("success", response.getMsg());
        assertEquals(secretKey, response.getData().getSecretKey());
        assertEquals(Integer.valueOf(1), response.getData().getBizType());
        assertEquals(Long.valueOf(1), response.getData().getBizId());
        assertEquals("1", response.getData().getBizIdStr());
        assertEquals("http://test.com", response.getData().getCodeUrl());
        assertEquals("http://test.com/image", response.getData().getImageUrl());
    }
}
