package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.order.promo.type.OrderPromoTypeDTO;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeOrderService;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.dianping.gmkt.event.api.scan.request.UserScanRequest;
import com.dianping.technician.service.TechnicianCenterService;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.*;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.TechnicianDistributionExtParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.mq.delay.task.OrderChannelCalDelayTaskProducer;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionItemTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.mptrade.groupbuy.thrift.api.response.ApiUpdateOrderResponse;
import com.sankuai.mptrade.groupbuy.thrift.api.service.ApiGroupBuyOrderService;
import com.sankuai.technician.category.service.TechCategoryQueryService;
import com.sankuai.technician.info.make.service.MakingTechQueryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Lists;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/10/12
 **/
@RunWith(MockitoJUnitRunner.class)
public class PromoCodeOrderChannelTaskRunnerTest {

    private final String mockOrderId = "orderId";

    private final long mockParentTaskId = 1L;

    private final int mockTechId = 2;

    private final long mockShopId = 3L;

    private final long mockCustomerId = 4L;

    @Mock
    private UserScanRecordService userScanRecordService;

    @Mock
    private PromoQRCodeOrderService promoCodeOrderService;

    @Mock
    private OrderChannelCalDelayTaskProducer delayTaskProducer;

    @Mock
    private PromoQRCodeCService promoQRCodeCService;

    @Mock
    private ShopMapperService shopMapperService;

    @Mock
    private DistributorActivityService distributorActivityService;

    @Mock
    private TechnicianCenterService technicianCenterService;

    @Mock
    private MakingTechQueryService makingTechQueryService;

    @Mock
    private TechCategoryQueryService techCategoryQueryService;

    @Mock
    private ApiGroupBuyOrderService apiGroupBuyOrderService;

    @InjectMocks
    private PromoCodeOrderChannelTaskRunner runner;

    @Test
    public void testTaskPostProcessor() throws Exception {
        OrderChannelTaskResultBO taskResult = mockTaskResult(DistributionBusinessChannelEnum.PROMO_CODE, 0, 0L, mockCustomerId);

        ApiUpdateOrderResponse response = new ApiUpdateOrderResponse();
        response.setSuccess(true);
        when(apiGroupBuyOrderService.updateOrderExtraFields(any())).thenReturn(response);

        runner.taskPostProcessor(mockParentTaskId, taskResult);

        verify(apiGroupBuyOrderService, times(1)).updateOrderExtraFields(any());
    }

    /**
     * 测试场景：订单命中任务，存在强归因
     */
    @Test
    public void testIsOrderHitTask_WithStrongIntention() {
        // arrange
        OrderInfoBO orderInfo = mockOrderInfoBO();
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();

        List<UserScanRecordDTO> scanRecordList = Lists.newArrayList();
        UserScanRecordDTO userScanRecordDTO = new UserScanRecordDTO();
        scanRecordList.add(userScanRecordDTO);

        when(userScanRecordService.getScanRecord(any(UserScanRequest.class))).thenReturn(PromoQRCodeResponse.success(scanRecordList));

        // act
        boolean result = runner.isOrderHitTask(taskParam, orderInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：订单命中任务，存在扫码记录
     */
    @Test
    public void testIsOrderHitTask_WithScanRecord() {
        // arrange
        OrderInfoBO orderInfo = mockOrderInfoBO();
        //Map<String, String> extDistributionInfo = new HashMap<>();
        //extDistributionInfo.put("PROMOTION_CODE", "value");
        //orderInfo.setExtDistributionInfo(extDistributionInfo);
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();

        List<UserScanRecordDTO> scanRecordList = Lists.newArrayList();
        UserScanRecordDTO userScanRecordDTO = new UserScanRecordDTO();
        scanRecordList.add(userScanRecordDTO);

        when(userScanRecordService.getScanRecord(any(UserScanRequest.class))).thenReturn(PromoQRCodeResponse.success(scanRecordList));

        // act
        boolean result = runner.isOrderHitTask(taskParam, orderInfo);

        // assert
        assertTrue(result);
    }

    /**
     * 测试calculate方法，当OrderPromoTypeDTO有值时，应该返回分销订单结果
     */
    @Test
    public void testCalculate_WhenOrderPromoTypeDTONotNULL() throws Exception {
        // arrange

        OrderInfoBO orderInfo = mockOrderInfoBO();

        OrderPromoTypeDTO orderPromoTypeDTO = new OrderPromoTypeDTO();
        orderPromoTypeDTO.setPromoTypeList(Collections.singletonList(1));
        orderPromoTypeDTO.setScanTime(new Date());
        orderPromoTypeDTO.setIntentionType(IntentionTypeEnum.STRONG_INTENTION.getCode());

        CommonResponse<OrderPromoTypeDTO> commonResponse = new CommonResponse<>();
        commonResponse.success(orderPromoTypeDTO);

        when(promoCodeOrderService.calculateOrderPromoTypeResponse(any(String.class))).thenReturn(commonResponse);

        // act
        OrderChannelTaskRunningResultBO result = runner.calculate(null, orderInfo, null);

        // assert
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
    }

    /**
     * 测试calculate方法，当promoCodeOrderService返回失败时，应抛出RuntimeException
     */
    @Test(expected = RuntimeException.class)
    public void testCalculate_WhenPromoCodeOrderServiceFails_ShouldThrowRuntimeException() throws Exception {
        // arrange
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setOrderId("testOrderId");

        CommonResponse<OrderPromoTypeDTO> commonResponse = new CommonResponse<>();
        commonResponse.setCode(CommonResponse.CODE_SYSTEM_ERROR);
        commonResponse.setMsg("Service Error");

        when(promoCodeOrderService.calculateOrderPromoTypeResponse(any(String.class))).thenReturn(commonResponse);

        // act
        runner.calculate(null, orderInfo, null);
    }



    @Test
    public void testResumeCalculate() throws Exception {
        // arrange

        OrderInfoBO orderInfo = mockOrderInfoBO();

        OrderPromoTypeDTO orderPromoTypeDTO = new OrderPromoTypeDTO();
        orderPromoTypeDTO.setPromoTypeList(Collections.singletonList(1));
        orderPromoTypeDTO.setScanTime(new Date());
        orderPromoTypeDTO.setIntentionType(IntentionTypeEnum.STRONG_INTENTION.getCode());
        when(promoCodeOrderService.calculateOrderPromoType(any(String.class))).thenReturn(orderPromoTypeDTO);

        // act
        OrderChannelTaskRunningResultBO result = runner.resumeCalculate(null, orderInfo, null);

        // assert
        assert result.getCalculateResult() == IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode();
        assert result.getIntentionType() == IntentionTypeEnum.STRONG_INTENTION.getCode();
    }





    private OrderInfoBO mockOrderInfoBO() {
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderId(mockOrderId);
        Map<String, String> extDistributionInfo = new HashMap<>();
        extDistributionInfo.put("promotion_code", "{\"staffBargainCodeId\":\"123\"}");
        orderInfoBO.setExtDistributionInfo(extDistributionInfo);
        return orderInfoBO;
    }

    private OrderChannelTaskResultBO mockTaskResult(DistributionBusinessChannelEnum channel, int techId, long shopId, long customerId) {
        OrderChannelTaskResultBO taskResult = new OrderChannelTaskResultBO();
        taskResult.setOrderInfo(mockOrderInfoBO());
        taskResult.setPredictEndTime(null);
        taskResult.setDistributeActivity(mockDistributeActivityBO(techId, shopId, customerId));
        taskResult.setStaffCodeBargain(null);
        taskResult.setPrivateLiveBO(null);
        taskResult.setOrderType(IntentionItemTypeEnum.UNIFIED_ORDER.getCode());
        taskResult.setOrderId(mockOrderId);
        taskResult.setCalculateResult(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode());
        taskResult.setBusinessChannel(channel.getCode());
        taskResult.setDistributionCode("");
        taskResult.setIntentionType(IntentionTypeEnum.WEAK_INTENTION.getCode());
        taskResult.setDistributor(null);
        taskResult.setResultExt(null);
        taskResult.setAttachments(null);

        return taskResult;
    }

    private DistributeActivityBO mockDistributeActivityBO(int techId, long shopId, long customerId) {
        DistributeActivityBO bo = new DistributeActivityBO();
        bo.setTechId(techId);
        bo.setShopId(shopId);
        bo.setCustomerId(customerId);
        return bo;
    }
}
