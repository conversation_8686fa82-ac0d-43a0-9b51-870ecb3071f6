package com.sankuai.carnation.distribution.wallet.distribute.tech.service.impl;

import com.dianping.lion.client.Lion;
import com.dianping.technician.biz.dto.AuthInfoDTO;
import com.dianping.technician.biz.dto.auth.AuthInfoQueryRequest;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.wallet.distribute.tech.domain.TechDistributeAccountDomainService;
import com.sankuai.carnation.distribution.wallet.distribute.tech.dto.TechWalletChangeResultDTO;
import com.sankuai.carnation.distribution.wallet.distribute.tech.dto.TechWalletWithdrawResultDTO;
import com.sankuai.carnation.distribution.wallet.distribute.tech.request.TechMoneyAddOrSubtractRequest;
import com.sankuai.carnation.distribution.wallet.distribute.tech.request.TechWalletRollbackRequest;
import com.sankuai.carnation.distribution.wallet.distribute.tech.utils.TechBizCodeUtils;
import com.sankuai.carnation.distribution.wallet.distribute.tech.utils.TechDistributeConstants;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.WalletAccountDomainService;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletAccountBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountAmountChangeResultBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.flow.WalletFlowDomainService;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.flow.bo.WalletDataItemBO;
import com.sankuai.carnation.distribution.wallet.fundamental.exceptions.WalletAccountException;
import com.sankuai.carnation.distribution.wallet.fundamental.exceptions.WalletFlowException;
import com.sankuai.technician.account.service.TechAccountAuthQueryService;
import com.sankuai.technician.process.distribute.query.TechDistributionQuery;
import com.sankuai.technician.process.distribute.response.TechDistributionAccountDTO;
import com.sankuai.technician.process.distribute.service.TechDistributionSignUpQueryActicityService;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class TechDistributeWalletOperateServiceImplTest {

    @InjectMocks
    private TechDistributeWalletOperateServiceImpl service;

    @Mock
    private TechDistributeAccountDomainService techAccountDomainService;

    @Mock
    private WalletFlowDomainService flowDomainService;

    @Mock
    private WalletAccountDomainService walletAccountDomainService;

    @Mock
    private TechAccountAuthQueryService techAccountAuthQueryService;

    @Mock
    private TechDistributionSignUpQueryActicityService techSignUpQueryService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试addMoney方法，当请求参数为null时抛出WalletFlowException异常
     */
    @Test
    public void testAddMoneyRequestIsNull() throws Throwable {
        // arrange
        TechMoneyAddOrSubtractRequest request = null;

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.addMoney(request);

        assertEquals("请求不能为空", response.getMsg());
    }

    /**
     * 测试addMoney方法，当请求参数有效且操作成功时
     */
    @Test
    public void testAddMoneySuccess() throws Throwable {
        // arrange
        TechMoneyAddOrSubtractRequest request = getTechMoneyAddOrSubtractRequest();
        WalletActivityAccountBO activityAccount = getWalletActivityAccountBO();

        when(techAccountDomainService.getActivityAccountByTechId(anyString(), anyInt())).thenReturn(activityAccount);

        WalletActivityAccountAmountChangeResultBO changeResultBO = new WalletActivityAccountAmountChangeResultBO(true, 1L, 0L, 100L, 1, 100L, "");
        when(flowDomainService.addOperateFlow(any())).thenReturn(changeResultBO);

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.addMoney(request);

        // assert
        assertTrue(response.isSuccess());
        assertEquals(100L, response.getData().getChangeAmount());
    }


    /**
     * 测试addMoney方法，当账户未注册时抛出WalletAccountException异常
     */
    @Test
    public void testAddMoneyAccountNotRegistered() throws Throwable {
        // arrange
        TechMoneyAddOrSubtractRequest request = getTechMoneyAddOrSubtractRequest();

        when(techAccountDomainService.getActivityAccountByTechId(anyString(), anyInt())).thenReturn(null);
        when(techAccountDomainService.registerActivityAccount(any())).thenReturn(1L);
        when(walletAccountDomainService.getActivityAccountById(anyLong())).thenReturn(null);

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.addMoney(request);

        // assert
        assertEquals("账号未注册", response.getMsg());

    }

    /**
     * 测试addMoney方法，当操作失败时
     */
    @Test
    public void testAddMoneyOperationFailed() throws Throwable {
        // arrange
        TechMoneyAddOrSubtractRequest request = getTechMoneyAddOrSubtractRequest();
        WalletActivityAccountBO activityAccount = getWalletActivityAccountBO();

        when(techAccountDomainService.getActivityAccountByTechId(anyString(), anyInt())).thenReturn(activityAccount);

        WalletActivityAccountAmountChangeResultBO changeResultBO = new WalletActivityAccountAmountChangeResultBO(false, 1L, 0L, 100L, 1, 100L, "失败原因");
        when(flowDomainService.addOperateFlow(any())).thenReturn(changeResultBO);

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.addMoney(request);

        // assert
        assertFalse(response.isSuccess());
        assertEquals("失败原因", response.getMsg());
    }


    /**
     * 测试回滚方法，手艺人id不合法
     */
    @Test
    public void testRollbackTechIdIsInvalid() {
        // arrange
        TechWalletRollbackRequest request = new TechWalletRollbackRequest();
        request.setTechId(-1);

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.rollback(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("手艺人id不合法", response.getMsg());
    }

    /**
     * 测试回滚方法，账号未注册
     */
    @Test
    public void testRollbackAccountNotRegistered() throws WalletAccountException {
        // arrange
        TechWalletRollbackRequest request = getTechWalletRollbackRequest();
        when(techAccountDomainService.getAccountByTechId(any(), anyInt())).thenReturn(null);

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.rollback(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("账号未注册", response.getMsg());
    }


    /**
     * 测试回滚方法，无可回滚数据
     */
    @Test
    public void testRollbackNoDataToRollback() throws WalletAccountException {
        // arrange
        TechWalletRollbackRequest request = getTechWalletRollbackRequest();
        WalletAccountBO walletAccountBO = getWalletAccountBO();
        when(techAccountDomainService.getAccountByTechId(any(), anyInt())).thenReturn(walletAccountBO);
        when(flowDomainService.getDateItemByAccountAndBiz(anyLong(), any())).thenReturn(new ArrayList<>());

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.rollback(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("无可以回滚数据", response.getMsg());
    }

    /**
     * 测试回滚方法，系统错误
     */
    @Test
    public void testRollbackSystemError() throws WalletAccountException, WalletFlowException {
        // arrange
        TechWalletRollbackRequest request = getTechWalletRollbackRequest();
        WalletAccountBO walletAccountBO = getWalletAccountBO();
        when(techAccountDomainService.getAccountByTechId(any(), anyInt())).thenReturn(walletAccountBO);
        List<WalletDataItemBO> dataItemList = new ArrayList<>();
        WalletDataItemBO dataItemBO = new WalletDataItemBO();
        dataItemBO.setItemId(1L);
        dataItemList.add(dataItemBO);
        when(flowDomainService.getDateItemByAccountAndBiz(anyLong(), any())).thenReturn(dataItemList);
        when(flowDomainService.rollback(any(), any(), any())).thenThrow(new RuntimeException());

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.rollback(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("系统错误", response.getMsg());
    }

    /**
     * 测试回滚方法，成功回滚数据
     */
    @Test
    public void testRollbackSuccess() throws WalletAccountException, WalletFlowException {
        // arrange
        TechWalletRollbackRequest request = getTechWalletRollbackRequest();
        WalletAccountBO walletAccountBO = getWalletAccountBO();
        when(techAccountDomainService.getAccountByTechId(any(), anyInt())).thenReturn(walletAccountBO);
        List<WalletDataItemBO> dataItemList = new ArrayList<>();
        WalletDataItemBO dataItemBO = new WalletDataItemBO();
        dataItemBO.setItemId(1L);
        dataItemBO.setOperateType(1); // 假设为加款操作
        dataItemBO.setWalletActivityAccountId(1L);
        dataItemList.add(dataItemBO);
        when(flowDomainService.getDateItemByAccountAndBiz(anyLong(), any())).thenReturn(dataItemList);
        when(flowDomainService.rollback(any(), any(), any())).thenReturn(new WalletActivityAccountAmountChangeResultBO(true, 1L, 100L, 90L, 2, 10L, ""));
        WalletActivityAccountBO activityAccount = getWalletActivityAccountBO();
        when(walletAccountDomainService.getActivityAccountById(anyLong())).thenReturn(activityAccount);

        // act
        RemoteResponse<TechWalletChangeResultDTO> response = service.rollback(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(10L, response.getData().getChangeAmount());
    }


    /**
     * 测试提现方法，当业务线为空时
     */
    @Test
    public void testWithdrawV2BizCodeIsEmpty() {
        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("", 1);
        assertNotNull(response);
        assertEquals("业务线未设置", response.getMsg());

        response = service.withdrawV2("BIZ_CODE", -1);
        assertNotNull(response);
        assertEquals("手艺人id未设置", response.getMsg());

    }

    /**
     * 测试提现方法，当账号未注册时
     */
    @Test
    public void testWithdrawV2AccountNotRegistered() throws WalletAccountException {
        when(techAccountDomainService.getAccountByTechId(any(String.class), any(Integer.class))).thenReturn(null);
        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("tech_distribution", 1);
        assertNotNull(response);
        assertEquals("账号未注册", response.getMsg());
    }

    /**
     * 测试提现方法，当职人尚未实名时
     */
//    @Test
//    public void testWithdrawV2TechNotIdentified() {
//        when(techAccountDomainService.getAccountByTechId(any(String.class), any(Integer.class))).thenReturn(getWalletAccountBO());
//
//        when(techAccountAuthQueryService.searchAuthInfo(any(AuthInfoQueryRequest.class))).thenReturn(getListTechnicianResp());
//        when(techSignUpQueryService.queryTechDistributionAccount(any(TechDistributionQuery.class))).thenReturn(null);
//        MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class);
//        mocked.when(() -> Lion.getBoolean("technician-vc-service", "technician-vc-service.deferUserRealNameAuthenticationSwitch", false)).thenReturn(true);
//
//        MockedStatic<TechBizCodeUtils> mockedStatic = mockStatic(TechBizCodeUtils.class);
//        mockedStatic.when(() -> TechBizCodeUtils.isBizCodeLegal(anyString())).thenReturn(true);
//
//
//        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("tech_distribution", 1);
//        assertNotNull(response);
//        assertTrue(response.getMsg().contains("职人尚未实名"));
//    }

    /**
     * 测试提现方法，当提现成功时
     */
    @Test
    public void testWithdrawV2Success() throws WalletAccountException, WalletFlowException {
        when(techAccountDomainService.getAccountByTechId(any(String.class), any(Integer.class))).thenReturn(getWalletAccountBO());
        when(techAccountAuthQueryService.searchAuthInfo(any(AuthInfoQueryRequest.class))).thenReturn(getListTechnicianResp());
        when(techSignUpQueryService.queryTechDistributionAccount(any(TechDistributionQuery.class))).thenReturn(getTechDistributionAccountDTOTechnicianResp());
        MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getBoolean("technician-vc-service", "technician-vc-service.deferUserRealNameAuthenticationSwitch", false)).thenReturn(false);

        MockedStatic<TechBizCodeUtils> mockedStatic = mockStatic(TechBizCodeUtils.class);
        mockedStatic.when(() -> TechBizCodeUtils.isBizCodeLegal(anyString())).thenReturn(true);

        WalletActivityAccountAmountChangeResultBO mockChangeResult = new WalletActivityAccountAmountChangeResultBO(true, 1L, 100L, 200L, 1, 100L, "");
        when(flowDomainService.withdrawAccount(any(WalletAccountBO.class), any(String.class))).thenReturn(mockChangeResult);

        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("tech_distribution", 1);
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(100L, response.getData().getChangeAmount());

        mocked.close();
        mockedStatic.close();

    }

    /**
     * 测试提现方法，当提现失败时
     */
    @Test
    public void testWithdrawV2Fail() throws WalletAccountException, WalletFlowException {

        MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getBoolean("technician-vc-service", "technician-vc-service.deferUserRealNameAuthenticationSwitch", false)).thenReturn(false);

        MockedStatic<TechBizCodeUtils> mockedStatic = mockStatic(TechBizCodeUtils.class);
        mockedStatic.when(() -> TechBizCodeUtils.isBizCodeLegal(anyString())).thenReturn(true);

        when(techAccountDomainService.getAccountByTechId(any(String.class), any(Integer.class))).thenReturn(getWalletAccountBO());

        WalletActivityAccountAmountChangeResultBO mockChangeResult = new WalletActivityAccountAmountChangeResultBO(false, 1L, 100L, 100L, 1, 0L, "提现失败");
        when(flowDomainService.withdrawAccount(any(WalletAccountBO.class), any(String.class))).thenReturn(mockChangeResult);

        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("tech_distribution", 1);
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("提现失败", response.getMsg());

        mockedStatic.close();
        mocked.close();
    }

    /**
     * 测试提现方法，当发生WalletAccountException异常时
     */
    @Test
    public void testWithdrawV2WalletAccountException() throws WalletAccountException {

        MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getBoolean("technician-vc-service", "technician-vc-service.deferUserRealNameAuthenticationSwitch", false)).thenReturn(false);

        MockedStatic<TechBizCodeUtils> mockedStatic = mockStatic(TechBizCodeUtils.class);
        mockedStatic.when(() -> TechBizCodeUtils.isBizCodeLegal(anyString())).thenReturn(true);

        when(techAccountDomainService.getAccountByTechId(any(String.class), any(Integer.class))).thenThrow(new WalletAccountException("账号异常"));
        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("BIZ_CODE", 1);
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("账号异常", response.getMsg());

        mocked.close();
        mockedStatic.close();
    }

    /**
     * 测试提现方法，当发生WalletFlowException异常时
     */
    @Test
    public void testWithdrawV2WalletFlowException() throws WalletAccountException, WalletFlowException {
        WalletAccountBO mockWalletAccount = getWalletAccountBO();
        when(techAccountDomainService.getAccountByTechId(any(String.class), any(Integer.class))).thenReturn(mockWalletAccount);
        when(flowDomainService.withdrawAccount(any(WalletAccountBO.class), any(String.class))).thenThrow(new WalletFlowException("流程异常"));

        MockedStatic<Lion> mocked = Mockito.mockStatic(Lion.class);
        mocked.when(() -> Lion.getBoolean("technician-vc-service", "technician-vc-service.deferUserRealNameAuthenticationSwitch", false)).thenReturn(false);

        MockedStatic<TechBizCodeUtils> mockedStatic = mockStatic(TechBizCodeUtils.class);
        mockedStatic.when(() -> TechBizCodeUtils.isBizCodeLegal(anyString())).thenReturn(true);

        RemoteResponse<TechWalletWithdrawResultDTO> response = service.withdrawV2("BIZ_CODE", 1);
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("流程异常", response.getMsg());

        mockedStatic.close();
        mocked.close();

    }

    @NotNull
    private WalletActivityAccountBO getWalletActivityAccountBO() {
        WalletActivityAccountBO activityAccount = new WalletActivityAccountBO();
        activityAccount.setActivityAccountId(1L);
        activityAccount.setWalletAccountId(1L);
        WalletAccountBO walletAccountBO = new WalletAccountBO();
        walletAccountBO.setBizCode("tech_distribution");
        walletAccountBO.setBindAccountId("123");
        activityAccount.setWalletAccountBO(walletAccountBO);
        return activityAccount;
    }

    @NotNull
    private TechMoneyAddOrSubtractRequest getTechMoneyAddOrSubtractRequest() {
        TechMoneyAddOrSubtractRequest request = new TechMoneyAddOrSubtractRequest();
        request.setBizCode("tech_distribution");
        request.setBizUniqueId("123");
        request.setTechId(1);
        request.setAmount(100L);
        request.setOperator("operator");
        request.setUnifiedOrderId("123");
        request.setVerificationId("123");
        return request;
    }

    @NotNull
    private TechWalletRollbackRequest getTechWalletRollbackRequest() {
        TechWalletRollbackRequest request = new TechWalletRollbackRequest();
        request.setTechId(1);
        request.setBizUniqueId("uniqueId");
        request.setOperator("operator");
        request.setReason("reason");
        return request;
    }

    @NotNull
    private WalletAccountBO getWalletAccountBO() {
        WalletAccountBO walletAccountBO = new WalletAccountBO();
        walletAccountBO.setWalletId(1L);
        return walletAccountBO;
    }

    @NotNull
    private TechnicianResp<TechDistributionAccountDTO> getTechDistributionAccountDTOTechnicianResp() {
        TechDistributionAccountDTO techDistributionAccountDTO = new TechDistributionAccountDTO();
        techDistributionAccountDTO.setHasSunflowerAccount(true);
        TechnicianResp<TechDistributionAccountDTO> mockTechAccountResp = TechnicianResp.success(techDistributionAccountDTO);
        return mockTechAccountResp;
    }

    @NotNull
    private TechnicianResp<List<AuthInfoDTO>> getListTechnicianResp() {
        List<AuthInfoDTO> mockResponseList = new ArrayList<>();
        AuthInfoDTO mockAuthInfoDTO = new AuthInfoDTO();
        mockAuthInfoDTO.setUserIDLong(1L);
        mockResponseList.add(mockAuthInfoDTO);
        TechnicianResp<List<AuthInfoDTO>> mockAuthResp = new TechnicianResp<>();
        mockAuthResp.setData(mockResponseList);
        return mockAuthResp;
    }


}
