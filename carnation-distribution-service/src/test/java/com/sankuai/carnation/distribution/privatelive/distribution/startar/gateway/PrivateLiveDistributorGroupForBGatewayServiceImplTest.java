package com.sankuai.carnation.distribution.privatelive.distribution.startar.gateway;

import static org.junit.Assert.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
/**
 * @Author: jinjianxia
 * @CreateTime: 2024/9/5 11:05
 * @Description:
 */
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ManageOperateTypeEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.application.model.DistributionQrCodeInfo;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveAnchorDistributorGroupAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveDistributionQrCodeAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.enums.PrivateLiveAnchorDistributorGroupEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.AnchorHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.request.DistributorGroupApplyHandleRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PageQueryDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributionQrCodeGatewayRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.DistributorGroupApplyHandleResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.PageQueryDistributorGroupResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.PrivateLiveDistributionQrCodeGatewayResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.gateway.PrivateLiveDistributorGroupForBGatewayServiceImpl;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import java.util.Arrays;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveDistributorGroupForBGatewayServiceImplTest {

    @InjectMocks
    private PrivateLiveDistributorGroupForBGatewayServiceImpl privateLiveDistributorGroupForBGatewayServiceImpl;

    @Mock
    private PrivateLiveDistributionQrCodeAppService privateLiveDistributionQrCodeAppService;

    @Mock
    private PrivateLiveAnchorDistributorGroupAppService privateLiveAnchorDistributorGroupAppService;

    @Mock
    private AnchorHelper anchorHelper;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;

    @Mock
    private DistributorGroupAppService distributorGroupAppService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getOrCreateRegisterQrCode方法，当请求有效且主播ID匹配时返回成功
     */
    @Test
    public void testGetOrCreateRegisterQrCode_ValidRequest_Success() {
        // arrange
        Long anchorId = 1L;
        String expectedQrCodeUrl = "http://example.com/qr";
        PrivateLiveDistributionQrCodeGatewayRequest request = new PrivateLiveDistributionQrCodeGatewayRequest(anchorId);
        when(privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId)).thenReturn(new DistributionQrCodeInfo(expectedQrCodeUrl, null));
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(anchorId);
            // act
            RemoteResponse<PrivateLiveDistributionQrCodeGatewayResponse> response = privateLiveDistributorGroupForBGatewayServiceImpl.getOrCreateRegisterQrCode(request);
            // assert
            assertNotNull(response);
            assertEquals("success", response.getMsg());
            assertEquals(expectedQrCodeUrl, response.getData().getQrCodeUrl());
        }
    }


    /**
     * 测试getOrCreateRegisterQrCode方法，当请求的主播ID与登录主播ID不匹配时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testGetOrCreateRegisterQrCode_AnchorIdMismatch_ThrowsException() {
        // arrange
        Long requestAnchorId = 1L;
        Long mainAnchorId = 2L;
        PrivateLiveDistributionQrCodeGatewayRequest request = new PrivateLiveDistributionQrCodeGatewayRequest(requestAnchorId);
        //        when(AnchorAuthUtils.getMainAnchorId()).thenReturn(mainAnchorId);
        // act
        privateLiveDistributorGroupForBGatewayServiceImpl.getOrCreateRegisterQrCode(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试getOrCreateRegisterQrCode方法，当请求中的主播ID为null时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterQrCode_NullAnchorId_ThrowsException() {
        // arrange
        PrivateLiveDistributionQrCodeGatewayRequest request = new PrivateLiveDistributionQrCodeGatewayRequest(null);
        // act
        privateLiveDistributorGroupForBGatewayServiceImpl.getOrCreateRegisterQrCode(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试处理申请，同意操作
     */
    @Test
    public void testHandleApplyAgree() throws Throwable {
        // arrange
        DistributorGroupApplyHandleRequest request = new DistributorGroupApplyHandleRequest(ManageOperateTypeEnum.AGREE.getCode(), 1L, 2L, "groupName", "customerId", "customerName");
        DistributorGroupApplyHandleResponse expectedResponse = new DistributorGroupApplyHandleResponse(1L, 2L, 1);
        when(privateLiveAnchorDistributorGroupAppService.passAudit(any())).thenReturn(expectedResponse);
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            // act
            RemoteResponse<DistributorGroupApplyHandleResponse> response = privateLiveDistributorGroupForBGatewayServiceImpl.handleApply(request);
            // assert
            assertNotNull(response);
            assertEquals(expectedResponse, response.getData());
        }
    }

    /**
     * 测试处理申请，拒绝操作
     */
    @Test
    public void testHandleApplyReject() throws Throwable {
        // arrange
        DistributorGroupApplyHandleRequest request = new DistributorGroupApplyHandleRequest(ManageOperateTypeEnum.REJECT.getCode(), 1L, 2L, "groupName", "customerId", "customerName");
        DistributorGroupApplyHandleResponse expectedResponse = new DistributorGroupApplyHandleResponse(1L, 2L, 0);
        when(privateLiveAnchorDistributorGroupAppService.rejectAudit(any())).thenReturn(expectedResponse);
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            // act
            RemoteResponse<DistributorGroupApplyHandleResponse> response = privateLiveDistributorGroupForBGatewayServiceImpl.handleApply(request);
            // assert
            assertNotNull(response);
            assertEquals(expectedResponse, response.getData());
        }
    }

    /**
     * 测试处理申请，非法操作类型
     */
    @Test(expected = IllegalArgumentException.class)
    public void testHandleApplyInvalidOperation() throws Throwable {
        // arrange
        DistributorGroupApplyHandleRequest request = new DistributorGroupApplyHandleRequest(999, 1L, 2L, "groupName", "customerId", "customerName");
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            // act
            privateLiveDistributorGroupForBGatewayServiceImpl.handleApply(request);
        }
        // assert
        // Expected exception
    }

    /**
     * 测试处理申请，请求主播ID与登录主播ID不匹配
     */
    @Test(expected = BizSceneException.class)
    public void testHandleApplyAnchorIdMismatch() throws Throwable {
        // arrange
        //        when(AnchorAuthUtils.getMainAnchorId()).thenReturn(999L);
        DistributorGroupApplyHandleRequest request = new DistributorGroupApplyHandleRequest(ManageOperateTypeEnum.AGREE.getCode(), 1L, 2L, "groupName", "customerId", "customerName");
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(999L);
            // act
            privateLiveDistributorGroupForBGatewayServiceImpl.handleApply(request);
            // assert
            // Expected exception
        }
    }

    /**
     * 测试分页查询分销商团队信息成功的场景
     */
    @Test
    public void testPageQueryDistributorGroupSuccess() throws Exception {
        // arrange
        PageQueryDistributorGroupRequest request = new PageQueryDistributorGroupRequest(1L, 1, 1, 10);
        PageDataDTO<PageQueryDistributorGroupResponse.DistributorGroup> pageDataDTO = new PageDataDTO<>(PageInfoDTO.builder().currentPageNum(1).pageSize(10).totalCount(1).totalPageCount(1).build(), Arrays.asList(new PageQueryDistributorGroupResponse.DistributorGroup(1L, "公司名", "团队名", 10, "12345678901")));
        when(distributorGroupAppService.pageQueryDistributorGroup(request)).thenReturn(pageDataDTO);
        when(privateLiveAnchorDistributorGroupRepository.countByStatus(1L, PrivateLiveAnchorDistributorGroupEnum.PASS.getCode())).thenReturn(1);
        when(privateLiveAnchorDistributorGroupRepository.countByStatus(1L, PrivateLiveAnchorDistributorGroupEnum.WAITING.getCode())).thenReturn(1);
        when(privateLiveAnchorDistributorGroupRepository.countByStatus(1L, PrivateLiveAnchorDistributorGroupEnum.REJECT.getCode())).thenReturn(1);
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            // act
            RemoteResponse<PageQueryDistributorGroupResponse> result = privateLiveDistributorGroupForBGatewayServiceImpl.pageQueryDistributorGroup(request);
            // assert
            assertEquals(Integer.valueOf(1), result.getData().getPass());
            assertEquals(Integer.valueOf(1), result.getData().getReject());
            assertEquals(Integer.valueOf(1), result.getData().getWaiting());
            assertEquals(Integer.valueOf(1), result.getData().getTotalNum());
            assertEquals(1, result.getData().getDistributorGroupList().size());
        }
    }

    /**
     * 测试分页查询分销商团队信息时，请求参数为空的异常场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorGroupRequestNull() throws Exception {
        // arrange
        PageQueryDistributorGroupRequest request = null;
        // act
        privateLiveDistributorGroupForBGatewayServiceImpl.pageQueryDistributorGroup(request);
        // assert
        // Expected exception
    }

    /**
     * 测试分页查询分销商团队信息时，主播ID为空的异常场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorGroupAnchorIdNull() throws Exception {
        // arrange
        PageQueryDistributorGroupRequest request = new PageQueryDistributorGroupRequest(null, 1, 1, 10);
        // act
        privateLiveDistributorGroupForBGatewayServiceImpl.pageQueryDistributorGroup(request);
        // assert
        // Expected exception
    }

    /**
     * 测试分页查询分销商团队信息时，状态为空的异常场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorGroupStatusNull() throws Exception {
        // arrange
        PageQueryDistributorGroupRequest request = new PageQueryDistributorGroupRequest(1L, null, 1, 10);
        // act
        privateLiveDistributorGroupForBGatewayServiceImpl.pageQueryDistributorGroup(request);
        // assert
        // Expected exception
    }

    /**
     * 测试getOrCreateRegisterQrCode方法，当请求的主播ID与登录主播ID不匹配时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testGetOrCreateRegisterQrCode_AnchorIdMismatch_ThrowsBizSceneException() {
        // arrange
        Long anchorId = 1L;
        Long mainAnchorId = 2L; // 登录主播ID与请求主播ID不同
        PrivateLiveDistributionQrCodeGatewayRequest request = new PrivateLiveDistributionQrCodeGatewayRequest(anchorId);
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(mainAnchorId);
            // act
            privateLiveDistributorGroupForBGatewayServiceImpl.getOrCreateRegisterQrCode(request);
            // assert is handled by the expected exception
        }
    }

    /**
     * 测试getOrCreateRegisterQrCode方法，当请求中的主播ID为null时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterQrCode_AnchorIdNull_ThrowsIllegalArgumentException() {
        // arrange
        PrivateLiveDistributionQrCodeGatewayRequest request = new PrivateLiveDistributionQrCodeGatewayRequest(null);
        // act
        privateLiveDistributorGroupForBGatewayServiceImpl.getOrCreateRegisterQrCode(request);
    }
}
