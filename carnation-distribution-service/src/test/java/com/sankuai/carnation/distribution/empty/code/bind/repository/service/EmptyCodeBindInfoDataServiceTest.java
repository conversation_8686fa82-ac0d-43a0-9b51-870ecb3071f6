package com.sankuai.carnation.distribution.empty.code.bind.repository.service;

import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

public class EmptyCodeBindInfoDataServiceTest {

    @InjectMocks
    private EmptyCodeBindInfoDataService emptyCodeBindInfoDataService;

    @Mock
    private UniversalQRCodeGeneratorService qrCodeGeneratorService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGenerateEmptyQrCodeByBizTypeBizTypeNotInEnum() throws Throwable {
        Integer bizType = 999;
        Boolean needLogo = false;
        Boolean needPadding = false;
        QRCodeConfigDTO result = emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(bizType, needLogo, needPadding);
        assertNull(result);
    }

    @Test
    public void testGenerateEmptyQrCodeByBizTypeNeedLogoFalse() throws Throwable {
        Integer bizType = 1;
        Boolean needLogo = false;
        Boolean needPadding = false;
        RemoteResponse<QRCodeConfigDTO> response = mock(RemoteResponse.class);
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setLogoFileUrl("");
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(qrCodeConfigDTO);
        when(qrCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(response);
        QRCodeConfigDTO result = emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(bizType, needLogo, needPadding);
        assertNotNull(result);
        assertTrue(result.getLogoFileUrl().isEmpty());
    }

    @Test
    public void testGenerateEmptyQrCodeByBizTypeNeedLogoTrue() throws Throwable {
        Integer bizType = 1;
        Boolean needLogo = true;
        Boolean needPadding = false;
        RemoteResponse<QRCodeConfigDTO> response = mock(RemoteResponse.class);
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setLogoFileUrl("https://img.meituan.net/beautyimg/2e9a1378e2fa97128729a7063f716f0b5713.png");
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(qrCodeConfigDTO);
        when(qrCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(response);
        QRCodeConfigDTO result = emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(bizType, needLogo, needPadding);
        assertNotNull(result);
        assertFalse(result.getLogoFileUrl().isEmpty());
    }

    @Test
    public void testGenerateEmptyQrCodeByBizTypeInsertQRCodeFail() throws Throwable {
        Integer bizType = 1;
        Boolean needLogo = true;
        Boolean needPadding = false;
        RemoteResponse<QRCodeConfigDTO> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(false);
        when(qrCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(response);
        QRCodeConfigDTO result = emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(bizType, needLogo, needPadding);
        assertNull(result);
    }

    @Test
    public void testGenerateEmptyQrCodeByBizTypeInsertQRCodeSuccess() throws Throwable {
        Integer bizType = 1;
        Boolean needLogo = true;
        Boolean needPadding = false;
        RemoteResponse<QRCodeConfigDTO> response = mock(RemoteResponse.class);
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setLogoFileUrl("https://img.meituan.net/beautyimg/2e9a1378e2fa97128729a7063f716f0b5713.png");
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(qrCodeConfigDTO);
        when(qrCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(response);
        QRCodeConfigDTO result = emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(bizType, needLogo, needPadding);
        assertNotNull(result);
    }
}
