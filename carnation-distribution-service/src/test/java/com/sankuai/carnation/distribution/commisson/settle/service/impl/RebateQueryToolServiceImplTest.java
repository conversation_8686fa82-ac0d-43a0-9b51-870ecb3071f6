package com.sankuai.carnation.distribution.commisson.settle.service.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.event.api.commission.dto.OrderCommissionRateDTO;
import com.dianping.gmkt.event.api.commission.service.OrderCommissionRateQueryService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateAmountResponse;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryPageResponse;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryRequest;
import com.sankuai.carnation.distribution.commisson.repository.QueryRebateForOrderOrShopIdService;
import com.sankuai.technician.trade.api.settle.dto.SettleBillModelQuery;
import com.sankuai.technician.trade.api.settle.dto.SettleBillQueryModel;
import com.sankuai.technician.trade.api.settle.service.SettleBillQueryRemoteService;
import com.sankuai.technician.trade.types.enums.SettleAccountRoleType;
import com.sankuai.technician.trade.types.enums.SettleBillStatus;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RebateQueryToolServiceImplTest {

    @InjectMocks
    private RebateQueryToolServiceImpl rebateQueryToolService;

    @Mock
    private QueryRebateForOrderOrShopIdService queryRebateForOrderOrShopIdService;

    @Mock
    private SettleBillQueryRemoteService settleBillQueryRemoteService;

    @Mock
    private OrderCommissionRateQueryService orderCommissionRateQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试请求参数为空
     */
    @Test
    public void testQueryRebateRecord_RequestIsNull() {
        RemoteResponse<RebateQueryPageResponse> response = rebateQueryToolService.queryRebateRecord(null);
        assertNotNull(response);
        assertEquals("请求参数为空", response.getMsg());
    }

    /**
     * 测试订单ID和门店ID都为空
     */
    @Test
    public void testQueryRebateRecord_OrderIdAndShopIdAreNull() {
        RebateQueryRequest request = new RebateQueryRequest();
        RemoteResponse<RebateQueryPageResponse> response = rebateQueryToolService.queryRebateRecord(request);
        assertNotNull(response);
        assertEquals("请确认查询条件", response.getMsg());
    }

    /**
     * 测试通过订单ID查询返利记录，但结果为空
     */
    @Test
    public void testQueryRebateRecord_QueryByOrderIdEmptyResult() {
        RebateQueryRequest request = new RebateQueryRequest();
        request.setOrderId("123");
        when(queryRebateForOrderOrShopIdService.queryRebateForOrder(request)).thenReturn(new ArrayList<>());
        RemoteResponse<RebateQueryPageResponse> response = rebateQueryToolService.queryRebateRecord(request);
        assertTrue(response.getData().getTotalNum() == 0);
        assertTrue(CollectionUtils.isEmpty(response.getData().getResults()));
        verify(queryRebateForOrderOrShopIdService, times(1)).queryRebateForOrder(request);
    }

    /**
     * 测试通过门店ID查询返利记录，但开始时间大于结束时间
     */
    @Test
    public void testQueryRebateRecord_ShopIdWithInvalidTimeRange() {
        RebateQueryRequest request = new RebateQueryRequest();
        request.setDpShopId(1L);
        request.setStartTime(2L);
        request.setEndTime(1L);
        RemoteResponse<RebateQueryPageResponse> response = rebateQueryToolService.queryRebateRecord(request);
        assertNotNull(response);
        assertEquals("开始时间不能大于结束时间", response.getMsg());
    }

    /**
     * 测试通过门店ID查询返利记录，结果为空
     */
    @Test
    public void testQueryRebateRecord_QueryByShopIdEmptyResult() {
        RebateQueryRequest request = new RebateQueryRequest();
        request.setDpShopId(1L);
        request.setStartTime(1L);
        request.setEndTime(2L);
        when(queryRebateForOrderOrShopIdService.queryRebateForShopId(request)).thenReturn(new ArrayList<>());
        RemoteResponse<RebateQueryPageResponse> response = rebateQueryToolService.queryRebateRecord(request);
        assertTrue(response.getData().getTotalNum() == 0);
        assertTrue(CollectionUtils.isEmpty(response.getData().getResults()));
        verify(queryRebateForOrderOrShopIdService, times(1)).queryRebateForShopId(request);
    }

    /**
     * 测试系统异常情况
     */
    @Test
    public void testQueryRebateRecord_SystemException() {
        RebateQueryRequest request = new RebateQueryRequest();
        request.setOrderId("123");
        when(queryRebateForOrderOrShopIdService.queryRebateForOrder(any(RebateQueryRequest.class))).thenThrow(new RuntimeException("系统异常"));
        RemoteResponse<RebateQueryPageResponse> response = rebateQueryToolService.queryRebateRecord(request);
        assertNotNull(response);
        assertEquals("系统异常", response.getMsg());
        verify(queryRebateForOrderOrShopIdService, times(1)).queryRebateForOrder(any(RebateQueryRequest.class));
    }

    /**
     * 测试订单ID为空时的场景
     */
    @Test
    public void testQueryRebateAmountOrderIdIsNull() {
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount(null);
        assertNotNull(result);
        assertEquals("参数不合法", result.getMsg());
    }

    /**
     * 测试查询到的结算单为空时的场景
     */
    @Test
    public void testQueryRebateAmountSettleBillIsEmpty() {
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(new ArrayList<>());
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("orderId");
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
    }

    /**
     * 测试结算金额为零且有无效原因的场景
     */
    @Test
    public void testQueryRebateAmountSettleAmountIsZero() {
        List<SettleBillQueryModel> models = new ArrayList<>();
        SettleBillQueryModel model = new SettleBillQueryModel();
        model.setStatus(SettleBillStatus.VAILD.getStatus());
        model.setRoleType(SettleAccountRoleType.TECHNICIAN.getType());
        model.setSettleAmount(BigDecimal.ZERO);
        model.setInvalidReason("Test Reason");
        models.add(model);
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(models);
        when(queryRebateForOrderOrShopIdService.getProductTypeByOrderId(anyString())).thenReturn(1);
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("orderId");
        assertNotNull(result);
        assertEquals("Test Reason", result.getData().getNotRebateReason());
    }

    /**
     * 测试结算金额非零且佣金率查询成功的场景
     */
    @Test
    public void testQueryRebateAmountSettleAmountNonZeroCommissionRateSuccess() {
        List<SettleBillQueryModel> models = new ArrayList<>();
        SettleBillQueryModel model = new SettleBillQueryModel();
        model.setStatus(SettleBillStatus.VAILD.getStatus());
        model.setRoleType(SettleAccountRoleType.TECHNICIAN.getType());
        model.setSettleAmount(new BigDecimal("100"));
        models.add(model);
        OrderCommissionRateDTO commissionRateDTO = new OrderCommissionRateDTO("orderId", 0.1);
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(models);
        when(queryRebateForOrderOrShopIdService.getProductTypeByOrderId(anyString())).thenReturn(1);
        when(orderCommissionRateQueryService.queryOrderCommissionRate(anyString())).thenReturn(commissionRateDTO);
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("orderId");
        assertNotNull(result);
        assertEquals("10.00%", result.getData().getCommissionRate());
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testQueryRebateAmountException() {
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenThrow(new RuntimeException());
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("orderId");
        assertNotNull(result);
    }

    /**
     * 测试订单ID有效，但查询到的结算单为空的场景
     */
    @Test
    public void testQueryRebateAmountWithValidOrderIdButEmptySettleBills() {
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(new ArrayList<>());
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("validOrderId");
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("validOrderId", result.getData().getOrderId());
    }

    /**
     * 测试订单ID有效，但未查询到相关结算单的场景
     */
    @Test
    public void testQueryRebateAmountNoSettleBillsFound() {
        when(queryRebateForOrderOrShopIdService.getProductTypeByOrderId("validOrderId")).thenReturn(1);
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(new ArrayList<>());
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("validOrderId");
        assertNotNull(result);
        assertEquals("success", result.getMsg());
        assertNotNull(result.getData());
        assertEquals("validOrderId", result.getData().getOrderId());
        verify(queryRebateForOrderOrShopIdService, times(1)).getProductTypeByOrderId("validOrderId");
        verify(settleBillQueryRemoteService, times(1)).querySettleBill(any(SettleBillModelQuery.class));
    }

    /**
     * 测试订单ID有效，查询到相关结算单，但结算金额为0的场景
     */
    @Test
    public void testQueryRebateAmountWithZeroSettleAmount() {
        List<SettleBillQueryModel> settleBillQueryModels = new ArrayList<>();
        SettleBillQueryModel model = new SettleBillQueryModel();
        model.setStatus(SettleBillStatus.VAILD.getStatus());
        model.setRoleType(SettleAccountRoleType.TECHNICIAN.getType());
        model.setSettleAmount(BigDecimal.ZERO);
        model.setInvalidReason("Not eligible for rebate");
        settleBillQueryModels.add(model);
        when(queryRebateForOrderOrShopIdService.getProductTypeByOrderId("validOrderId")).thenReturn(1);
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(settleBillQueryModels);
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("validOrderId");
        assertNotNull(result);
        assertEquals("success", result.getMsg());
        assertNotNull(result.getData());
        assertTrue(BigDecimal.ZERO.compareTo(result.getData().getCommissionAmount()) == 0);
        assertEquals("Not eligible for rebate", result.getData().getNotRebateReason());
    }

    /**
     * 测试订单ID有效，查询到相关结算单，结算金额大于0，但未查询到佣金比率的场景
     */
    @Test
    public void testQueryRebateAmountWithPositiveSettleAmountAndNoCommissionRate() {
        List<SettleBillQueryModel> settleBillQueryModels = new ArrayList<>();
        SettleBillQueryModel model = new SettleBillQueryModel();
        model.setStatus(SettleBillStatus.VAILD.getStatus());
        model.setRoleType(SettleAccountRoleType.TECHNICIAN.getType());
        model.setSettleAmount(new BigDecimal("100.00"));
        settleBillQueryModels.add(model);
        when(queryRebateForOrderOrShopIdService.getProductTypeByOrderId("validOrderId")).thenReturn(1);
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(settleBillQueryModels);
        when(orderCommissionRateQueryService.queryOrderCommissionRate("validOrderId")).thenReturn(null);
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("validOrderId");
        assertNotNull(result);
        assertEquals("success", result.getMsg());
        assertNotNull(result.getData());
        assertEquals(new BigDecimal("100.00"), result.getData().getCommissionAmount());
        assertEquals(null, result.getData().getCommissionRate());
    }

    /**
     * 测试订单ID有效，查询到相关结算单，结算金额大于0，且查询到佣金比率的场景
     */
    @Test
    public void testQueryRebateAmountWithPositiveSettleAmountAndCommissionRate() {
        List<SettleBillQueryModel> settleBillQueryModels = new ArrayList<>();
        SettleBillQueryModel model = new SettleBillQueryModel();
        model.setStatus(SettleBillStatus.VAILD.getStatus());
        model.setRoleType(SettleAccountRoleType.TECHNICIAN.getType());
        model.setSettleAmount(new BigDecimal("100.00"));
        settleBillQueryModels.add(model);
        OrderCommissionRateDTO orderCommissionRateDTO = new OrderCommissionRateDTO("validOrderId", 0.1);
        when(queryRebateForOrderOrShopIdService.getProductTypeByOrderId("validOrderId")).thenReturn(1);
        when(settleBillQueryRemoteService.querySettleBill(any(SettleBillModelQuery.class))).thenReturn(settleBillQueryModels);
        when(orderCommissionRateQueryService.queryOrderCommissionRate("validOrderId")).thenReturn(orderCommissionRateDTO);
        RemoteResponse<RebateAmountResponse> result = rebateQueryToolService.queryRebateAmount("validOrderId");
        assertNotNull(result);
        assertEquals("success", result.getMsg());
        assertNotNull(result.getData());
        assertEquals(new BigDecimal("100.00"), result.getData().getCommissionAmount());
        assertEquals("10.00%", result.getData().getCommissionRate());
    }
}
