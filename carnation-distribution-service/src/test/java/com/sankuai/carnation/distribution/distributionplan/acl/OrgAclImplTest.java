package com.sankuai.carnation.distribution.distributionplan.acl;

import com.alibaba.fastjson.JSON;
import com.sankuai.carnation.distribution.distributionplan.acl.impl.OrgAclImpl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.EmpInfo;
import com.sankuai.carnation.distribution.config.model.OrgConfig;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.queryservice.exception.MDMThriftException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrgAclImplTest {
    @InjectMocks
    private OrgAclImpl orgAclImpl;

    @Mock
    private EmpService empService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 empId 为 null 时抛出 IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryEmpInfoEmpIdIsNull() throws Throwable {
        orgAclImpl.queryEmpInfo(null);
    }

    /**
     * 测试正常情况下查询员工信息
     */
    @Test
    public void testQueryEmpInfoNormal() throws Throwable {
        // arrange
        Long empId = 1L;
        Emp emp = new Emp();
        emp.setMis("mis");
        emp.setName("name");
        when(empService.query("1", null)).thenReturn(emp);

        // act
        EmpInfo result = orgAclImpl.queryEmpInfo(empId);

        // assert
        assertNotNull(result);
        assertEquals("mis", result.getMis());
        assertEquals("name", result.getName());
    }

    /**
     * 测试查询过程中抛出 MDMThriftException
     */
    @Test
    public void testQueryEmpInfoThrowsMDMThriftException() throws Throwable {
        // arrange
        Long empId = 1L;
        when(empService.query("1", null)).thenThrow(new MDMThriftException());

        // act
        EmpInfo result = orgAclImpl.queryEmpInfo(empId);

        // assert
        assertNull(result);
    }

    /**
     * 测试查询结果为 null
     */
    @Test
    public void testQueryEmpInfoResultIsNull() throws Throwable {
        // arrange
        Long empId = 1L;
        when(empService.query("1", null)).thenReturn(null);

        // act
        EmpInfo result = orgAclImpl.queryEmpInfo(empId);

        // assert
        assertNull(result);
    }

    /**
     * 测试查询结果的 mis 或 name 为空
     */
    @Test
    public void testQueryEmpInfoMisOrNameIsEmpty() throws Throwable {
        // arrange
        Long empId = 1L;
        Emp emp = new Emp();
        emp.setMis("");
        emp.setName("");
        when(empService.query("1", null)).thenReturn(emp);

        // act
        EmpInfo result = orgAclImpl.queryEmpInfo(empId);

        // assert
        assertNull(result);
    }
}
