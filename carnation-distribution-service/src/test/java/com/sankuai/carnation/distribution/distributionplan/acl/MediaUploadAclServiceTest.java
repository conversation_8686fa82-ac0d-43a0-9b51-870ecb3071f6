package com.sankuai.carnation.distribution.distributionplan.acl;

import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.carnation.distribution.common.acl.MediaUploadAclService;
import com.sankuai.carnation.distribution.common.bo.MediaUploadSignBO;
import com.sankuai.carnation.distribution.common.bo.MediaUploadSignReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class MediaUploadAclServiceTest {

    @InjectMocks
    private MediaUploadAclService mediaUploadAclService;

    /**
     * 测试getUploadSign方法，正常情况
     */
    @Test
    public void testGetUploadSignNormal() {
        // arrange
        MediaUploadSignReq request = new MediaUploadSignReq(10, 10);

        try (MockedStatic<Kms> mockedStatic = mockStatic(Kms.class)) {
            mockedStatic.when(() -> Kms.getByName(anyString(), anyString())).thenReturn("12345");
            // act
            MediaUploadSignBO actualResponse = mediaUploadAclService.getUploadSign(request);

            // assert
            assertNotNull(actualResponse);
        }

    }
}