package com.sankuai.carnation.distribution.product.v2.repository.service;

import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.privatelive.ProductActionDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.enums.ProductActionTypeEnum;
import com.sankuai.medicalcosmetology.product.selectify.api.request.ProductActionRequest;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceHandleAuntSelectifyTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductItemSaleUnitMapper mapper;

    @Mock
    private ProductSelectifyAclService productSelectifyAclService;

    private ProductItemBO productItemBO;

    private Map<String, ProductItemSaleUnit> formerMap;

    private List<ProductItemSaleUnit> addList;

    private List<ProductItemSaleUnit> updateList;

    private List<ProductItemSaleUnit> removeList;

    @Before
    public void setUp() {
        productItemBO = new ProductItemBO();
        formerMap = new HashMap<>();
        addList = new ArrayList<>();
        updateList = new ArrayList<>();
        removeList = new ArrayList<>();
    }

    private void invokeHandleAuntSelectify(ProductItemBO productItemBO, Map<String, ProductItemSaleUnit> formerMap, List<ProductItemSaleUnit> addList, List<ProductItemSaleUnit> updateList, List<ProductItemSaleUnit> removeList) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("handleAuntSelectify", ProductItemBO.class, Map.class, List.class, List.class, List.class);
        method.setAccessible(true);
        method.invoke(productItemSaleUnitDataService, productItemBO, formerMap, addList, updateList, removeList);
    }

    @Test
    public void testHandleAuntSelectifyUpdateListIsEmpty() throws Throwable {
        invokeHandleAuntSelectify(productItemBO, formerMap, addList, updateList, removeList);
        assertEquals(0, addList.size());
        assertEquals(0, removeList.size());
        verify(productSelectifyAclService, never()).productAction(any(ProductActionRequest.class));
    }
}
