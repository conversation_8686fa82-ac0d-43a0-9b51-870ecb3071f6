package com.sankuai.carnation.distribution.distributionplan.service.impl;


import api.ut.helper.rpc.IEmpRemoteService_QueryByEmpId_MockHelper;
import api.ut.helper.rpc.ProcessService_FetchHistory_MockHelper;
import api.ut.helper.rpc.RequisitionQueryService_LoadRequisitionDetail_MockHelper;
import com.beust.jcommander.internal.Maps;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.hotel.dlm.lock.Lock;
import com.meituan.hotel.dlm.service.IDistributedLockManager;
import com.sankuai.carnation.distribution.common.acl.AmazonS3AclService;
import com.sankuai.carnation.distribution.common.acl.MediaUploadAclService;
import com.sankuai.carnation.distribution.common.bo.MediaUploadSignBO;
import com.sankuai.carnation.distribution.common.bo.MediaUploadSignReq;
import com.sankuai.carnation.distribution.distributionplan.acl.GeneralCategoryAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.OrgAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.PlatformAuditAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.EmpInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralCategoryTree;
import com.sankuai.carnation.distribution.distributionplan.enums.RequisitionStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.RequisitionTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.RequisitionUploadSceneEnum;
import com.sankuai.carnation.distribution.distributionplan.request.operate.*;
import com.sankuai.carnation.distribution.distributionplan.service.impl.operate.PlatChannelDistributionPlanRequisitionServiceImpl;
import com.sankuai.carnation.distribution.distributionplan.service.model.PlatDistributionChannel;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.distributionplan.vo.operate.*;
import com.sankuai.dz.srcm.basic.dto.emp.EmpInfoDTO;
import com.sankuai.dzusergrowth.common.api.enums.DistributionChannelEnum;
import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.*;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionCommissionTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanPageQueryRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionDetailLoadRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanCreator;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionCommandService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import com.sankuai.meituan.org.openapi.iface.IEmpRemoteService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/10/7
 */
public class PlatChannelDistributionPlanRequisitionServiceImplTest {

    @InjectMocks
    private PlatChannelDistributionPlanRequisitionServiceImpl service;

    @Mock
    private RequisitionCommandService requisitionCommandService;

    @Mock
    private RequisitionQueryService requisitionQueryService;

    @Mock
    private AmazonS3AclService amazonS3AclService;

    @Mock
    private RedisLockService redisLockService;

    @Mock
    private IDistributedLockManager distributedLockManager;

    @Mock
    private DistributionPlanQueryService distributionPlanQueryService;

    @Mock
    private GeneralCategoryAcl generalCategoryAcl;

    @Mock
    private MediaUploadAclService mediaUploadAclService;

    @Mock
    protected IEmpRemoteService iEmpRemoteService;

    @Mock
    protected PlatformAuditAcl platformAuditAcl;

    @Mock
    protected OrgAcl orgAcl;

    private RequisitionQueryService_LoadRequisitionDetail_MockHelper requisitionQueryService_LoadRequisitionDetail_MockHelper;
    private ProcessService_FetchHistory_MockHelper processService_FetchHistory_MockHelper;
    private IEmpRemoteService_QueryByEmpId_MockHelper iEmpRemoteService_QueryByEmpId_MockHelper;

    @Before
    public void setUp() throws Exception {
        String dataPath = System.getProperty("user.dir") + "/src/test/java/api/ut/cases/rpc/PlatChannelDistributionPlanRequisitionServiceImpl/loadRequisitionDetail/data/LoadRequisitionDetail_default_Test/";

        MockitoAnnotations.openMocks(this);
        processService_FetchHistory_MockHelper
                = ProcessService_FetchHistory_MockHelper.buildMock(platformAuditAcl, dataPath);
        iEmpRemoteService_QueryByEmpId_MockHelper
                = IEmpRemoteService_QueryByEmpId_MockHelper.buildMock(iEmpRemoteService, dataPath);
        when(orgAcl.queryEmpInfo(anyLong())).thenReturn(buildEmpInfoDTO("xiaohong", "小红"));
    }

    private EmpInfo buildEmpInfoDTO(String mis, String name) {
        EmpInfo empInfo = new EmpInfo();
        empInfo.setMis(mis);
        empInfo.setName(name);
        return empInfo;
    }

    /**
     * 测试提交申请验证，当远程服务调用成功且返回成功时
     */
    @Test
    public void testsubmitRequisition_WhenRemoteCallSuccess() {
        // arrange
        RequisitionSubmitRequest request = new RequisitionSubmitRequest();
        request.setTitle("测试");
        request.setChannel(DistributionChannelEnum.QUAN_QUAN.getCode());
        request.setTradeType(TradeTypeEnum.GROUPBUY_PAY.getCode());
        request.setBackground("测试");
        request.setDetailFile("https://123.com");
        request.setRequisitionType(RequisitionTypeEnum.IMPORT.getType());
        request.setCreatorId("1234");

        mockDistributedLockBehavior();
        // act
        RemoteResponse result = service.submitRequisition(request);

        // assert
        // todo redisLockService.tryLock 方法mock不住返回值
        assertEquals(ResponseEnum.FAILURE.code, result.getCode());
    }

    /**
     * 测试提交申请验证，当远程服务调用成功且返回成功时
     */
    @Test
    public void testValidateRequisitionSubmit_WhenRemoteCallSuccess() {
        // arrange
        RequisitionSubmitRequest request = new RequisitionSubmitRequest();
        request.setTitle("测试");
        request.setChannel(DistributionChannelEnum.QUAN_QUAN.getCode());
        request.setTradeType(TradeTypeEnum.GROUPBUY_PAY.getCode());
        request.setBackground("测试");
        request.setDetailFile("https://123.com");
        request.setRequisitionType(RequisitionTypeEnum.IMPORT.getType());
        request.setCreatorId("1234");
        Response<Boolean> apiResponse = Response.buildSuccess(true);

        when(requisitionCommandService.validateRequisitionSubmit(any())).thenReturn(apiResponse);

        // act
        RemoteResponse<Boolean> result = service.validateRequisitionSubmit(request);

        // assert
        assertTrue(result.getData());
    }

    /**
     * 测试请求参数验证失败的场景
     */
    @Test
    public void testPageQueryRequisition_InvalidRequest() {
        RequisitionPageQueryRequest request = new RequisitionPageQueryRequest();

        PaginationRemoteResponse<RequisitionVO> response = service.pageQueryRequisition(request);

        assertEquals(ResponseEnum.FAILURE.code, response.getCode());
        verify(requisitionQueryService, never()).pageQueryRequisition(any());
    }

    /**
     * 测试远程服务调用失败的场景
     */
    @Test
    public void testPageQueryRequisition_ServiceCallFailed() {
        RequisitionPageQueryRequest request = buildRequisitionPageQueryRequest();

        // 模拟远程服务调用失败
        when(requisitionQueryService.pageQueryRequisition(any())).thenReturn(new Response<>(Response.ERROR_CODE, "Service call failed"));

        PaginationRemoteResponse<RequisitionVO> response = service.pageQueryRequisition(request);

        assertEquals(ResponseEnum.FAILURE.code, response.getCode());
    }

    /**
     * 测试远程服务调用成功，但返回空数据的场景
     */
    @Test
    public void testPageQueryRequisition_EmptyData() {
        RequisitionPageQueryRequest request = buildRequisitionPageQueryRequest();
        // 模拟远程服务调用成功，但返回空数据
        when(requisitionQueryService.pageQueryRequisition(any())).thenReturn(Response.buildSuccess(PageResult.empty(1, 0)));

        PaginationRemoteResponse<RequisitionVO> response = service.pageQueryRequisition(request);

        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals(0, response.getTotalHit());
    }

    /**
     * 测试远程服务调用成功，并返回非空数据的场景
     */
    @Test
    public void testPageQueryRequisition_SuccessWithData() {
        RequisitionPageQueryRequest request = buildRequisitionPageQueryRequest();
        // 构造远程服务调用返回的数据
        List<RequisitionDTO> requisitionDTOList = Lists.newArrayList();
        RequisitionDTO requisitionDTO = new RequisitionDTO();
        requisitionDTO.setRequisitionId(1L);
        requisitionDTO.setRequisitionType(1);
        requisitionDTO.setChannel("1234");
        requisitionDTO.setTitle("1234");
        requisitionDTO.setSubmitDate(new Date());
        requisitionDTO.setCreatorId("1234");
        requisitionDTO.setStatus(1);

        requisitionDTOList.add(requisitionDTO);
        PageResult<RequisitionDTO> pageResult = new PageResult<>(requisitionDTOList, 1, 1, 10);
        // 模拟远程服务调用成功，并返回非空数据
        when(requisitionQueryService.pageQueryRequisition(any())).thenReturn(new Response<>(Response.SUCCESS_CODE, "Success", pageResult));

        PaginationRemoteResponse<RequisitionVO> response = service.pageQueryRequisition(request);

        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals(1, response.getData().size());
    }

    /**
     * 测试请求参数校验失败的场景
     */
    @Test
    public void testLoadRequisitionDetail_InvalidRequest() {
        // arrange
        RequisitionDetailRequest request = new RequisitionDetailRequest();

        // act
        RemoteResponse<RequisitionDetailVO> result = service.loadRequisitionDetail(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试查询申请单详情失败的场景
     */
    @Test
    public void testLoadRequisitionDetail_QueryFailed() {
        // arrange
        RequisitionDetailRequest request = new RequisitionDetailRequest();
        request.setRequisitionId("1");
        RequisitionDetailLoadRequest detailLoadRequest = new RequisitionDetailLoadRequest();
        detailLoadRequest.setRequisitionId(Long.parseLong(request.getRequisitionId()));
        when(requisitionQueryService.loadRequisitionDetail(detailLoadRequest)).thenReturn(new Response<>(Response.ERROR_CODE, "查询失败"));

        // act
        RemoteResponse<RequisitionDetailVO> result = service.loadRequisitionDetail(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试查询申请单详情成功但数据为空的场景
     */
    @Test
    public void testLoadRequisitionDetail_QuerySuccessButDataIsNull() {
        // arrange
        RequisitionDetailRequest request = new RequisitionDetailRequest();
        request.setRequisitionId("1");
        RequisitionDetailLoadRequest detailLoadRequest = new RequisitionDetailLoadRequest();
        detailLoadRequest.setRequisitionId(Long.parseLong(request.getRequisitionId()));
        when(requisitionQueryService.loadRequisitionDetail(detailLoadRequest)).thenReturn(new Response<>(Response.SUCCESS_CODE, "查询成功", null));

        // act
        RemoteResponse<RequisitionDetailVO> result = service.loadRequisitionDetail(request);

        // assert
        assertNotNull(result);
        assertNull(result.getData());
    }

    /**
     * 测试查询申请单详情成功且数据非空的场景
     */
    @Test
    public void testLoadRequisitionDetail_QuerySuccessWithData() {
        // arrange
        RequisitionDetailRequest request = new RequisitionDetailRequest();
        request.setRequisitionId("1");
        RequisitionDetailLoadRequest detailLoadRequest = new RequisitionDetailLoadRequest();
        detailLoadRequest.setRequisitionId(Long.parseLong(request.getRequisitionId()));
        RequisitionDetailDTO dto = getRequisitionDetailDTO();
        when(requisitionQueryService.loadRequisitionDetail(detailLoadRequest)).thenReturn(Response.buildSuccess(dto));
        when(amazonS3AclService.getS3DownloadUrl(anyString(), anyString())).thenReturn("https://123.com");
        when(requisitionQueryService.queryAllAuditNodeDetail()).thenReturn(Response.buildSuccess(buildAuditNodeDetailResponse()));

        // act
        RemoteResponse<RequisitionDetailVO> result = service.loadRequisitionDetail(request);

        // assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals("1", result.getData().getRequisitionId());
        assertEquals("title", result.getData().getTitle());
    }

    private List<RequisitionAuditNodeDTO> buildAuditNodeDetailResponse() {
        List<RequisitionAuditNodeDTO> requisitionAuditNodeDTOList = new ArrayList<>();
        requisitionAuditNodeDTOList.add(new RequisitionAuditNodeDTO("运营主管", 1));
        requisitionAuditNodeDTOList.add(new RequisitionAuditNodeDTO("BU head", 2));
        requisitionAuditNodeDTOList.add(new RequisitionAuditNodeDTO("财务FP", 3));
        requisitionAuditNodeDTOList.add(new RequisitionAuditNodeDTO("到综商业产品策略", 4));
        return requisitionAuditNodeDTOList;
    }

    /**
     * 测试分页查询分销计划，正常情况
     */
    @Test
    public void testPageQueryDistributionPlanSuccess() {
        // arrange
        PlatChannelDistributionPlanPageQueryRequest request = new PlatChannelDistributionPlanPageQueryRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTradeType(TradeTypeEnum.GROUPBUY_PAY.getCode());
        request.setChannelList(Lists.newArrayList(DistributionChannelEnum.QUAN_QUAN.getCode()));
        request.setProductCategoryIdList(Lists.newArrayList(1L));
        request.setCommissionRate("30");

        DistributionPlanDTO planDTO = getDistributionPlanDTO();

        PageResult<DistributionPlanDTO> pageResult = new PageResult<>(Collections.singletonList(planDTO), 1, 1, 10);
        Response<PageResult<DistributionPlanDTO>> response = new Response<>(200, "Success", pageResult);

        when(distributionPlanQueryService.pageQueryDistributionPlan(any(DistributionPlanPageQueryRequest.class))).thenReturn(response);

        GeneralCategoryTree childTree = new GeneralCategoryTree();
        childTree.setPlatformCategoryId(1L);
        childTree.setCategoryName("测试1");
        GeneralCategoryTree generalCategoryTree = new GeneralCategoryTree();
        generalCategoryTree.setPlatformCategoryId(101L);
        generalCategoryTree.setCategoryName("测试");
        generalCategoryTree.setChildList(Lists.newArrayList(childTree));
        when(generalCategoryAcl.getCategoryTreeByTradeType(anyInt())).thenReturn(Lists.newArrayList(generalCategoryTree));

        // act
        PaginationRemoteResponse<PlatChannelDistributionPlanVO> result = service.pageQueryDistributionPlan(request);

        // assert
        assertNotNull(result);
        assertEquals(200, result.getCode());
        assertEquals(1, result.getData().size());
        assertEquals(Long.valueOf(1), result.getData().get(0).getPlanId());
    }

    @Test
    public void testPageQueryHistoryDistributionPlan_Success() {
        // arrange
        DistributionPlanHistoryPageQueryRequest request = new DistributionPlanHistoryPageQueryRequest(1L, 1, 10);
        DistributionPlanDTO distributionPlanDTO = getDistributionPlanDTO();
        List<DistributionPlanDTO> planDTOList = Collections.singletonList(distributionPlanDTO);
        when(distributionPlanQueryService.pageQueryDistributionPlanHistory(any())).thenReturn(Response.buildSuccess(new PageResult<>(planDTOList, 1, 1, 10)));

        DistributionPlanRelatedRequisitionDTO requisitionDTO = new DistributionPlanRelatedRequisitionDTO();
        requisitionDTO.setPlanId(1L);
        requisitionDTO.setRequisitionId(1L);
        requisitionDTO.setRequisitionBackground("111");

        when(requisitionQueryService.queryRequisitionByPlanIds(any())).thenReturn(Response.buildSuccess(Collections.singletonList(requisitionDTO)));

        // act
        PaginationRemoteResponse<PlatChannelHistoryDistributionPlanVO> response = service.pageQueryHistoryDistributionPlan(request);

        // assert
        assertNotNull(response);
        assertFalse(response.getData().isEmpty());
        assertEquals(1L, response.getTotalHit());
    }

    /**
     * 测试请求参数验证失败的情况
     */
    @Test
    public void testQueryRequisitionTemplateDownloadLink_InvalidRequest() {
        // arrange
        RequisitionTemplateDownloadLinkRequest request = new RequisitionTemplateDownloadLinkRequest();
        request.setRequisitionType(null); // 设置一个无效的请求类型，模拟验证失败的情况

        // act
        RemoteResponse<RequisitionTemplateDownloadLinkVO> response = service.queryRequisitionTemplateDownloadLink(request);

        // assert
        assertFalse("应当返回失败的响应", response.isSuccess());
    }

    /**
     * 测试模板文件不存在的情况
     */
    @Test
    public void testQueryRequisitionTemplateDownloadLink_FileNotExist() {
        // arrange
        RequisitionTemplateDownloadLinkRequest request = new RequisitionTemplateDownloadLinkRequest();
        request.setRequisitionType(1);
        when(amazonS3AclService.doesObjectExist(anyString(), anyString())).thenReturn(false);

        // act
        RemoteResponse<RequisitionTemplateDownloadLinkVO> response = service.queryRequisitionTemplateDownloadLink(request);

        // assert
        assertFalse("应当返回失败的响应", response.isSuccess());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryRequisitionTemplateDownloadLink_Success() {
        // arrange
        RequisitionTemplateDownloadLinkRequest request = new RequisitionTemplateDownloadLinkRequest();
        request.setRequisitionType(1);
        when(amazonS3AclService.doesObjectExist(anyString(), anyString())).thenReturn(true);
        when(amazonS3AclService.getS3DownloadUrl(anyString(), anyString())).thenReturn("http://example.com/template.xlsx");

        // act
        RemoteResponse<RequisitionTemplateDownloadLinkVO> response = service.queryRequisitionTemplateDownloadLink(request);

        // assert
        assertTrue("应当返回成功的响应", response.isSuccess());
        assertNotNull("返回的数据不应为空", response.getData());
        assertEquals("返回的模板URL应与预期相符", "http://example.com/template.xlsx", response.getData().getTemplateUrl());
    }

    /**
     * 测试queryProductCategoryTree方法，当请求参数验证成功，并且成功获取类别树时
     */
    @Test
    public void testQueryProductCategoryTree_Success() {
        // arrange
        ProductCategoryTreeQueryRequest request = new ProductCategoryTreeQueryRequest(TradeTypeEnum.GROUPBUY_PAY.getCode());
        List<GeneralCategoryTree> childTreeList1 = Lists.newArrayList(
                new GeneralCategoryTree(101L, "Category11", 2, 1, null, null, null),
                new GeneralCategoryTree(102L, "Category22", 2, 2, null, null, null)
        );
        List<GeneralCategoryTree> childTreeList2 = Lists.newArrayList(
                new GeneralCategoryTree(201L, "Category21", 2, 1, null, null, null),
                new GeneralCategoryTree(202L, "Category22", 2, 2, null, null, null)
        );
        List<GeneralCategoryTree> categoryTreeList = Lists.newArrayList(
                new GeneralCategoryTree(1L, "Category1", 1, 1, childTreeList1, null, null),
                new GeneralCategoryTree(2L, "Category2", 1, 2, childTreeList2, null, null)
        );
        when(generalCategoryAcl.getCategoryTreeByTradeType(anyInt())).thenReturn(categoryTreeList);

        // act
        RemoteResponse<List<ProductCategoryTreeVO>> response = service.queryProductCategoryTree(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(2, response.getData().size());
    }

    /**
     * 测试查询分销产品交易类型列表成功的情况
     */
    @Test
    public void testQueryDistributionProductTradeTypeListSuccess() {
        // arrange
        Map<String, String> tradeTypeMap = Maps.newHashMap();
        tradeTypeMap.put("1", "类型1描述");
        tradeTypeMap.put("2", "类型2描述");

        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            mockedStatic.when(() -> Lion.getMap(anyString(), eq("plat.operate.distribution.product.trade.type"), any())).thenReturn(tradeTypeMap);

            // act
            RemoteResponse<List<DistributionProductTradeTypeVO>> response = service.queryDistributionProductTradeTypeList();

            // assert
            assertTrue(response.isSuccess());
            assertNotNull(response.getData());
            assertEquals(2, response.getData().size());
        }
    }

    /**
     * 测试queryDistributionChannelList方法，当获取渠道列表成功时
     */
    @Test
    public void testQueryDistributionChannelListSuccess() {
        // arrange
        List<PlatDistributionChannel> expectedList = Lists.newArrayList(new PlatDistributionChannel(DistributionChannelEnum.QUAN_QUAN.getCode(), "圈圈分销"));
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            mockedStatic.when(() -> Lion.getList(anyString(), eq("plat.operate.distribution.channel.config.list"), any())).thenReturn(expectedList);

            // act
            RemoteResponse<List<DistributionChannelVO>> response = service.queryDistributionChannelList();

            // assert
            assertTrue(response.isSuccess());
        }
    }

    /**
     * 测试请求参数验证成功，且获取上传签名成功的情况
     */
    @Test
    public void testQueryRequisitionUploadVerifySign_ValidRequest() {
        RequisitionUploadVerifySignRequest request = new RequisitionUploadVerifySignRequest();
        request.setUploadScene(RequisitionUploadSceneEnum.UPLOAD_PLAT_CHANNEL_COMMISSION_CONFIG_FILE.getCode());
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            mockedStatic.when(() -> Lion.getInt(anyString(), eq("distribution.plan.requisition.upload.verify.sign.expire.minutes"), any())).thenReturn(3);

            MediaUploadSignBO signBO = new MediaUploadSignBO("signature", "policy", "accessKey", "key", "uploadUrl", "preSignedUrl");
            when(mediaUploadAclService.getUploadSign(any(MediaUploadSignReq.class))).thenReturn(signBO);

            RemoteResponse<RequisitionUploadVerifySignVO> response = service.queryRequisitionUploadVerifySign(request);

            assertTrue("应当返回成功", response.isSuccess());
            assertNotNull("返回的签名对象不应为null", response.getData());
            assertEquals("签名应当匹配", "signature", response.getData().getSignature());
        }
    }

    private DistributionPlanDTO getDistributionPlanDTO() {
        DistributionPlanDTO planDTO = new DistributionPlanDTO();
        planDTO.setPlanId(1L);
        planDTO.setChannel(DistributionChannelEnum.QUAN_QUAN.getCode());
        planDTO.setBeginTime(new Date(1727193600000L));
        planDTO.setEndTime(new Date(4095219606000L));
        planDTO.setCommissionRateMap(Collections
                .singletonMap(DistributionCommissionTypeEnum.PLAT_CHANNEL_COMMISSION.getCode(), new BigDecimal("0.1")));
        planDTO.setSubject(new DistributionPlanSubjectDTO(1L, 1, "1", "1", null));
        planDTO.setCreator(DistributionPlanCreator.builder().creatorId("1234").build());
        return planDTO;
    }

    private RequisitionDetailDTO getRequisitionDetailDTO() {
        RequisitionDetailFileDTO requisitionDetailFileDTO = new RequisitionDetailFileDTO();
        requisitionDetailFileDTO.setDetailFileName("123");
        requisitionDetailFileDTO.setTotalCount(1);
        requisitionDetailFileDTO.setSucceedCount(1);
        requisitionDetailFileDTO.setFailedCount(0);
        requisitionDetailFileDTO.setImportingCount(0);
        return new RequisitionDetailDTO(1L, RequisitionTypeEnum.IMPORT.getType()
                , 1, DistributionChannelEnum.QUAN_QUAN.getCode(), "title", "background", requisitionDetailFileDTO, 1);
    }

    private RequisitionPageQueryRequest buildRequisitionPageQueryRequest() {
        RequisitionPageQueryRequest request = new RequisitionPageQueryRequest();
        request.setChannel(DistributionChannelEnum.QUAN_QUAN.getCode());
        request.setCreator("1234");
        request.setRequisitionType(RequisitionTypeEnum.IMPORT.getType());
        request.setStatus(RequisitionStatusEnum.APPROVED.getType());
        request.setPageNo(1);
        request.setPageSize(10);
        return request;
    }

    /**
     * 测试取消申请单，当申请单ID为空时抛出异常
     */
    @Test
    public void testCancelRequisition_WhenRequisitionIdIsNull() {
        RequisitionCancelRequest request = new RequisitionCancelRequest(null, "creatorId");

        RemoteResponse<Boolean> booleanRemoteResponse = service.cancelRequisition(request);
        assertTrue(!booleanRemoteResponse.isSuccess());
    }

    /**
     * 测试取消申请单，当申请人ID为空时抛出异常
     */
    @Test
    public void testCancelRequisition_WhenCreatorIdIsNull() {
        RequisitionCancelRequest request = new RequisitionCancelRequest(1L, null);

        RemoteResponse<Boolean> booleanRemoteResponse = service.cancelRequisition(request);
        assertTrue(!booleanRemoteResponse.isSuccess());
    }

    /**
     * 测试取消申请单，当远程调用失败时返回失败响应
     */
    @Test
    public void testCancelRequisition_WhenRemoteCallFails() {
        RequisitionCancelRequest request = new RequisitionCancelRequest(1L, "creatorId");
        Response<Boolean> response = new Response<>(Response.ERROR_CODE, "Failed", false);

        when(requisitionCommandService.cancelRequisition(any())).thenReturn(response);

        RemoteResponse<Boolean> result = service.cancelRequisition(request);

        assertTrue(!result.isSuccess());
    }

    /**
     * 测试取消申请单，当远程调用成功时返回成功响应
     */
    @Test
    public void testCancelRequisition_WhenRemoteCallSucceeds() {
        RequisitionCancelRequest request = new RequisitionCancelRequest(1L, "creatorId");
        Response<Boolean> response = new Response<>(Response.SUCCESS_CODE, "Success", true);

        when(requisitionCommandService.cancelRequisition(any())).thenReturn(response);

        RemoteResponse<Boolean> result = service.cancelRequisition(request);

        assertTrue(result.isSuccess());
    }

    /**
     * 测试 queryBuInfoByTradeType 方法，当请求参数为 null 时
     */
    @Test
    public void testQueryBuInfoByTradeTypeWithNullRequest() {
        // arrange
        BuInfoQueryRequest request = null;

        // act
        RemoteResponse<List<BuInfoVO>> response = service.queryBuInfoByTradeType(request);

        // assert
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试 queryBuInfoByTradeType 方法，当交易类型为 null 时
     */
    @Test
    public void testQueryBuInfoByTradeTypeWithNullTradeType() {
        // arrange
        BuInfoQueryRequest request = new BuInfoQueryRequest(null);

        // act
        RemoteResponse<List<BuInfoVO>> response = service.queryBuInfoByTradeType(request);

        // assert
        assertEquals("交易类型不能为空", response.getMsg());
    }

    /**
     * 测试 queryBuInfoByTradeType 方法，当 requisitionQueryService 返回失败时
     */
    @Test
    public void testQueryBuInfoByTradeTypeWithServiceFailure() {
        // arrange
        BuInfoQueryRequest request = new BuInfoQueryRequest(1);
        when(requisitionQueryService.queryBuDetailList(any())).thenReturn(new Response<>(Response.ERROR_CODE, "查询bu信息失败"));

        // act
        RemoteResponse<List<BuInfoVO>> response = service.queryBuInfoByTradeType(request);

        // assert
        assertEquals("查询bu信息失败", response.getMsg());
    }

    /**
     * 测试 queryBuInfoByTradeType 方法，当 requisitionQueryService 返回成功时
     */
    @Test
    public void testQueryBuInfoByTradeTypeWithSuccess() {
        // arrange
        BuInfoQueryRequest request = new BuInfoQueryRequest(1);
        List<BuDetailDTO> buDetailDTOList = Arrays.asList(new BuDetailDTO(1, "BU1"), new BuDetailDTO(2, "BU2"));
        when(requisitionQueryService.queryBuDetailList(any())).thenReturn(new Response<>(Response.SUCCESS_CODE, "success", buDetailDTOList));

        // act
        RemoteResponse<List<BuInfoVO>> response = service.queryBuInfoByTradeType(request);

        // assert
        assertEquals("success", response.getMsg());
        assertEquals(2, response.getData().size());
        assertEquals(Integer.valueOf(1), response.getData().get(0).getBuId());
        assertEquals("BU1", response.getData().get(0).getBuName());
    }

    /**
     * 测试 queryBuInfoByTradeType 方法，当 requisitionQueryService 返回的数据为 null 时
     */
    @Test
    public void testQueryBuInfoByTradeTypeWithNullData() {
        // arrange
        BuInfoQueryRequest request = new BuInfoQueryRequest(1);
        when(requisitionQueryService.queryBuDetailList(any())).thenReturn(new Response<>(Response.SUCCESS_CODE, "success", null));

        // act
        RemoteResponse<List<BuInfoVO>> response = service.queryBuInfoByTradeType(request);

        // assert
        assertEquals("查询bu信息失败", response.getMsg());
    }


    private void mockDistributedLockBehavior() {
        Mockito.when(distributedLockManager.getReentrantLock(anyString(), anyInt())).thenReturn(new Lock() {
            @Override
            public void lock() {
            }

            @Override
            public boolean tryLock() {
                return true;
            }

            @Override
            public boolean tryLock(long l, TimeUnit timeUnit) {
                return true;
            }

            @Override
            public void lockInterruptibly() {
            }

            @Override
            public void unlock() {
            }

            @Override
            public String getName() {
                return null;
            }
        });
    }
}