package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.bcp.PromoCodeBcpCheckServiceImpl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.TechnicianDistributionExtParamBO;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import org.junit.Before;

public class PromoCodeBcpCheckServiceImplGetOrderTechChannelTest {

    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Test
    public void testGetOrderTechChannelWhenTechExtParamIsNull() throws Throwable {
        PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService = new PromoCodeBcpCheckServiceImpl();
        OrderInfoBO orderInfo = new OrderInfoBO();
        String result = promoCodeBcpCheckService.getOrderTechChannel(orderInfo);
        assertEquals("Expected the method to handle null input gracefully", "", result);
    }

    @Test
    public void testGetOrderTechChannelWhenChannelIsNull() throws Throwable {
        PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService = new PromoCodeBcpCheckServiceImpl();
        OrderInfoBO orderInfo = new OrderInfoBO();
        TechnicianDistributionExtParamBO techExtParam = new TechnicianDistributionExtParamBO();
        orderInfo.setTechExtParam(techExtParam);
        String result = promoCodeBcpCheckService.getOrderTechChannel(orderInfo);
        assertEquals("", result);
    }

    @Test
    public void testGetOrderTechChannelWhenChannelIsNotNull() throws Throwable {
        PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService = new PromoCodeBcpCheckServiceImpl();
        OrderInfoBO orderInfo = new OrderInfoBO();
        TechnicianDistributionExtParamBO techExtParam = new TechnicianDistributionExtParamBO();
        techExtParam.setChannel("testChannel");
        orderInfo.setTechExtParam(techExtParam);
        String result = promoCodeBcpCheckService.getOrderTechChannel(orderInfo);
        assertEquals("testChannel", result);
    }
}
