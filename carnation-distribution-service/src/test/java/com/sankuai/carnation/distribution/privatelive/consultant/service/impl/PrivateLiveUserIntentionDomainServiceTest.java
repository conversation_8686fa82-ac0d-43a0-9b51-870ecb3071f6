package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionLog;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionLogRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveUserIntentionDomainServiceTest {

    @Mock
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Mock
    private PrivateLiveUserIntentionLogRepository privateLiveUserIntentionLogRepository;

    @InjectMocks
    private PrivateLiveUserIntentionDomainService privateLiveUserIntentionDomainService;

    private PrivateLiveUserIntentionResult originIntentionResult;

    @Before
    public void setUp() {
        originIntentionResult = new PrivateLiveUserIntentionResult(1L, "wxId", "unionId", 2L, "wxNickName", "distributorCode", "liveId", 3L, 1, 1, new Date(), new Date(), 2);
    }

    /**
     * 测试更新意向状态和类型以及记录日志的正常情况
     */
    @Test
    public void testUpdateIntentionStatusAndIntentionTypeAndLog_Normal() {
        // arrange
        int status = 2;
        String remark = "remark";

        // act
        privateLiveUserIntentionDomainService.updateIntentionStatusAndIntentionTypeAndLog(originIntentionResult, status, remark);

        // assert
        verify(userIntentionResultRepository).updateStatusAndIntentionTypeById(1L, status, 2);
        verify(privateLiveUserIntentionLogRepository).insert(any(PrivateLiveUserIntentionLog.class));
    }


    @Test
    public void testModifyIntentionBelongNormal() {
        // arrange
        PrivateLiveUserIntentionResult originIntentionResult = new PrivateLiveUserIntentionResult();
        originIntentionResult.setId(1L);
        originIntentionResult.setWxId("wxId");
        originIntentionResult.setUnionId("unionId");
        originIntentionResult.setUserId(1L);
        originIntentionResult.setLiveId("liveId");
        originIntentionResult.setConsultantTaskId(1L);
        originIntentionResult.setStatus(1);
        originIntentionResult.setIntentionType(1);

        long consultantTaskId = 2L;
        String distributorCode = "newDistributorCode";
        String remark = "remark";
        long modifyId = 2L;

        // act
        privateLiveUserIntentionDomainService.modifyIntentionBelong(originIntentionResult, consultantTaskId, distributorCode, remark, modifyId);

        // assert
        verify(userIntentionResultRepository, times(1)).update(eq(1L), any(PrivateLiveUserIntentionResult.class));
        verify(privateLiveUserIntentionLogRepository, times(1)).insert(any(PrivateLiveUserIntentionLog.class));
    }


    @Test
    public void testModifyIntentionBelongNormal1() throws Throwable {
        // arrange
        PrivateLiveUserIntentionResult originIntentionResult = new PrivateLiveUserIntentionResult();
        originIntentionResult.setId(1L);
        originIntentionResult.setWxId("wx123");
        originIntentionResult.setUnionId("union123");
        originIntentionResult.setUserId(123L);
        originIntentionResult.setLiveId("live123");
        originIntentionResult.setConsultantTaskId(123L);
        originIntentionResult.setStatus(1);
        originIntentionResult.setUserType(1);
        originIntentionResult.setIntentionType(1);

        long consultantTaskId = 456L;
        String distributorCode = "dist456";
        int userType = 2;
        String remark = "Test Remark";
        long modifyId = 789L;

        when(userIntentionResultRepository.update(any(Long.class), any(PrivateLiveUserIntentionResult.class))).thenReturn(1);

        // act
        privateLiveUserIntentionDomainService.modifyIntentionBelong(originIntentionResult, consultantTaskId, distributorCode, userType, remark, modifyId);

        // assert
        verify(userIntentionResultRepository).update(any(Long.class), any(PrivateLiveUserIntentionResult.class));
        verify(privateLiveUserIntentionLogRepository).insert(any(PrivateLiveUserIntentionLog.class));
    }
}
