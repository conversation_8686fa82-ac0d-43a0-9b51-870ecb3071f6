//package com.sankuai.carnation.distribution.privatelive.distribution.config;
//
//import com.alibaba.fastjson.JSONException;
//import com.dianping.lion.client.Lion;
//import org.junit.Before;
//import org.junit.Test;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//
//import static org.junit.Assert.*;
//import static org.mockito.Mockito.*;
//
//public class PrivateLiveDistributionConfigUtilsTest {
//
//    @Mock
//    private Lion lionMock;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//        System.setProperty("lion.env", "dev");
////        when(Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn("{}");
//    }
//
//    /**
//     * 测试配置为空时返回默认配置
//     */
//    @Test
//    public void testGetPrivateLiveDistributionConfigWithEmptyConfig() {
//        // arrange
//        String emptyConfig = "{}";
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn(emptyConfig);
//
//            // act
//            PrivateLiveDistributionQrCodeConfig result = PrivateLiveDistributionConfigUtils.getPrivateLiveDistributionConfig(1);
//
//            // assert
//            assertNotNull(result);
//            assertNull(result.getAccountType());
//        }
//    }
//
//    /**
//     * 测试配置不为空但未找到对应账户类型配置时返回默认配置
//     */
//    @Test
//    public void testGetPrivateLiveDistributionConfigWithNonMatchingAccountType() {
//        // arrange
//        String configWithNonMatchingAccountType = "{\"roleConfigList\":[{\"accountType\":2,\"testDomain\":\"test\",\"prdDomain\":\"prod\",\"pathUrl\":\"/path\"}]}";
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn(configWithNonMatchingAccountType);
//
//            // act
//            PrivateLiveDistributionQrCodeConfig result = PrivateLiveDistributionConfigUtils.getPrivateLiveDistributionConfig(1);
//
//            // assert
//            assertNotNull(result);
//            assertNull(result.getAccountType());
//        }
//    }
//
//    /**
//     * 测试配置不为空且找到对应账户类型配置时返回正确配置
//     */
//    @Test
//    public void testGetPrivateLiveDistributionConfigWithMatchingAccountType() {
//        // arrange
//        String configWithMatchingAccountType = "{\"roleConfigList\":[{\"accountType\":1,\"testDomain\":\"test\",\"prdDomain\":\"prod\",\"pathUrl\":\"/path\"}]}";
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn(configWithMatchingAccountType);
//
//            // act
//            PrivateLiveDistributionQrCodeConfig result = PrivateLiveDistributionConfigUtils.getPrivateLiveDistributionConfig(1);
//
//            // assert
//            assertNotNull(result);
//            assertEquals(Integer.valueOf(1), result.getAccountType());
//            assertEquals("test", result.getTestDomain());
//            assertEquals("prod", result.getPrdDomain());
//            assertEquals("/path", result.getPathUrl());
//        }
//    }
//
//    /**
//     * 测试配置解析异常时抛异常
//     */
//    @Test(expected = JSONException.class)
//    public void testGetPrivateLiveDistributionConfigWithJsonParseException() {
//        // arrange
//        String invalidJsonConfig = "{invalid_json}";
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn(invalidJsonConfig);
//
//            // act
//            PrivateLiveDistributionQrCodeConfig result = PrivateLiveDistributionConfigUtils.getPrivateLiveDistributionConfig(1);
//
//            // assert
//            assertNotNull(result);
//            assertNull(result.getAccountType());
//        }
//    }
//
//    /**
//     * 测试配置为空时返回默认配置
//     */
//    @Test
//    public void testGetDistributionApplyConfigWithEmptyConfig() {
//        // arrange
//        Integer accountType = 1;
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn("{}");
//
//            // act
//            PrivateLiveDistributionApplyConfig result = PrivateLiveDistributionConfigUtils.getDistributionApplyConfig(accountType);
//
//            // assert
//            assertNotNull(result);
//            assertNull(result.getAccountType());
//        }
//    }
//
//    /**
//     * 测试配置不为空但未找到对应账户类型配置时返回默认配置
//     */
//    @Test
//    public void testGetDistributionApplyConfigWithNonMatchingAccountType() {
//        // arrange
//        Integer accountType = 1;
//        String configJson = "{\"distributionApplyConfigList\":[{\"accountType\":2,\"unApplyStatusDesc\":\"desc\"}]}";
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn(configJson);
//
//            // act
//            PrivateLiveDistributionApplyConfig result = PrivateLiveDistributionConfigUtils.getDistributionApplyConfig(accountType);
//
//            // assert
//            assertNotNull(result);
//            assertNull(result.getAccountType());
//        }
//    }
//
//    /**
//     * 测试配置不为空且找到对应账户类型配置
//     */
//    @Test
//    public void testGetDistributionApplyConfigWithMatchingAccountType() {
//        // arrange
//        Integer accountType = 1;
//        String configJson = "{\"distributionApplyConfigList\":[{\"accountType\":1,\"unApplyStatusDesc\":\"desc\"}]}";
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn(configJson);
//
//            // act
//            PrivateLiveDistributionApplyConfig result = PrivateLiveDistributionConfigUtils.getDistributionApplyConfig(accountType);
//
//            // assert
//            assertNotNull(result);
//            assertEquals(accountType, result.getAccountType());
//            assertEquals("desc", result.getUnApplyStatusDesc());
//        }
//    }
//
//    /**
//     * 测试配置解析异常时抛异常
//     */
//    @Test(expected = JSONException.class)
//    public void testGetDistributionApplyConfigWithJsonParseException() {
//        // arrange
//        Integer accountType = 1;
//        try (MockedStatic<Lion> mockedStatic = Mockito.mockStatic(Lion.class)) {
//
//            mockedStatic.when(() -> Lion.getStringValue(eq(PrivateLiveDistributionConfigUtils.PRIVATE_LIVE_DISTRIBUTION_CONFIG_KEY), anyString())).thenReturn("invalid json");
//
//            // act
//            PrivateLiveDistributionApplyConfig result = PrivateLiveDistributionConfigUtils.getDistributionApplyConfig(accountType);
//
//            // assert
//            assertNotNull(result);
//            assertNull(result.getAccountType());
//        }
//    }
//}