package com.sankuai.carnation.distribution.common.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.commission.dto.CalculateResult;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class CatLogUtilsTest {


    /**
     * 测试calculateVerifyCat方法，当calculateResultList为null时
     */
    @Test
    public void testCalculateVerifyCatWhenListIsNull() {
        // Mock静态方法
        try (MockedStatic<Cat> mocked = Mockito.mockStatic(Cat.class)) {
            // arrange
            List<CalculateResult> calculateResultList = null;

            // act
            CatLogUtils.calculateVerifyCat(calculateResultList);

            // assert
            mocked.verify(() -> Cat.logEvent("RebateActivityCommissionCalculator", "error"), times(1));
        }
    }

    @Test
    public void testCalculateVerifyCatWhenListIsNotNull() {
        // Mock静态方法
        try (MockedStatic<Cat> mocked = Mockito.mockStatic(Cat.class)) {
            // arrange
            CalculateResult calculateResult = new CalculateResult();
            calculateResult.setCommissionCent(0);
            List<CalculateResult> calculateResultList = Lists.newArrayList(calculateResult);

            // act
            CatLogUtils.calculateVerifyCat(calculateResultList);

            // assert
            mocked.verify(() -> Cat.logEvent("RebateActivityCommissionCalculator", "commissionCent=0 but noReason"), times(1));
        }
    }



}
