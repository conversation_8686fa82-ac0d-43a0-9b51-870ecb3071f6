package com.sankuai.carnation.distribution.privatelive.consultant.repository.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionQueryRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.UserIntentionStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionResultExample;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveUserIntentionResultMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveUserIntentionResultRepositoryTest {

    @InjectMocks
    private PrivateLiveUserIntentionResultRepository repository;

    @Mock
    private PrivateLiveUserIntentionResultMapper mapper;

    private PrivateLiveUserIntentionQueryRequest request;

    private List<PrivateLiveUserIntentionResult> expected;

    @InjectMocks
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Mock
    private PrivateLiveUserIntentionResultMapper userIntentionResultMapper;

    @Before
    public void setUp() {
        request = new PrivateLiveUserIntentionQueryRequest();
        expected = Collections.singletonList(new PrivateLiveUserIntentionResult());
    }

    /**
     * 测试 query 方法，当 request 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testQueryWhenRequestIsNull() {
        repository.query(null);
    }





    /**
     * 测试queryByTaskIdAndUserId方法，当用户意向结果不存在时，应返回false。
     */
    @Test
    public void testQueryByTaskIdAndUserIdWhenResultNotExists() throws Throwable {
        // arrange
        long userId = 1L;
        long consultantTaskId = 1L;
        when(mapper.selectByExample(any(PrivateLiveUserIntentionResultExample.class))).thenReturn(Collections.emptyList());
        // act
        boolean result = repository.queryByTaskIdAndUserId(userId, consultantTaskId, UserIntentionStatusEnum.VALID_STATUS_LIST);
        // assert
        assertFalse(result);
    }

    @Test
    public void testQueryBossByUserIdAndLiveId_NullLiveId() {
        // arrange
        List<Long> userIdList = Arrays.asList(1L, 2L);
        String liveId = "1111";
        // act
        List<PrivateLiveUserIntentionResult> privateLiveUserIntentionResults = repository.queryBossByUserIdAndLiveId(userIdList, liveId);
        assertNotNull(privateLiveUserIntentionResults);
    }

    /**
     * 测试批量查询用户意向结果，用户ID列表为空
     */
    @Test
    public void testBatchQueryByUserIdWithEmptyUserIds() {
        List<PrivateLiveUserIntentionResult> results = userIntentionResultRepository.batchQueryByUserId("liveId", Collections.emptyList());
        assertTrue(results.isEmpty());
    }

    /**
     * 测试批量查询用户意向结果，正常情况
     */
    @Test
    public void testBatchQueryByUserIdSuccess() {
        when(userIntentionResultMapper.selectByExample(any())).thenReturn(Collections.singletonList(new PrivateLiveUserIntentionResult()));
        List<PrivateLiveUserIntentionResult> results = userIntentionResultRepository.batchQueryByUserId("liveId", Arrays.asList(1L, 2L));
        assertFalse(results.isEmpty());
    }

    /**
     * 测试批量查询用户意向结果，返回结果为空
     */
    @Test
    public void testBatchQueryByUserIdWithNoResults() {
        when(userIntentionResultMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<PrivateLiveUserIntentionResult> results = userIntentionResultRepository.batchQueryByUserId("liveId", Arrays.asList(1L, 2L));
        assertTrue(results.isEmpty());
    }


    @Test
    public void testUpdateStatusAndIntentionTypeByIdSuccess() {
        // arrange
        long id = 1L;
        int status = 2;
        int intentionType = 3;
        when(userIntentionResultMapper.updateByExampleSelective(any(PrivateLiveUserIntentionResult.class), any(PrivateLiveUserIntentionResultExample.class))).thenReturn(1);

        // act
        int result = repository.updateStatusAndIntentionTypeById(id, status, intentionType);

        // assert
        verify(userIntentionResultMapper).updateByExampleSelective(any(PrivateLiveUserIntentionResult.class), any(PrivateLiveUserIntentionResultExample.class));
        assert result == 1 : "更新操作应该成功";
    }


}
