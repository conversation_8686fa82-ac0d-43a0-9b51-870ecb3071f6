package com.sankuai.carnation.distribution.intention.domain.calculate.mq.order.rerun;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.carnation.distribution.intention.dto.OrderChannelRerunDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.ArgumentMatchers.anyString;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class OrderChannelTaskRerunNotifyProducerSendMessageTest {

    @Mock
    private IProducerProcessor producer;

    @InjectMocks
    private OrderChannelTaskRerunNotifyProducer orderChannelTaskRerunNotifyProducer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Assuming there's a way to inject the producer indirectly, which is not shown here due to the lack of context.
    }

    /**
     * Tests the scenario where the message is sent successfully.
     */
    @Test
    public void testSendMessageSuccess() throws Throwable {
        // Arrange
        String orderId = "123";
        Integer taskType = 1;
        String remark = "remark";
        String bizId = "bizId";
        OrderChannelRerunDTO orderChannelRerunDTO = new OrderChannelRerunDTO();
        orderChannelRerunDTO.setOrderId(orderId);
        orderChannelRerunDTO.setTaskType(taskType);
        orderChannelRerunDTO.setRemark(remark);
        orderChannelRerunDTO.setBizId(bizId);
        ProducerResult result = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(anyString())).thenReturn(result);
        // Act
        orderChannelTaskRerunNotifyProducer.sendMessage(orderId, taskType, remark, bizId);
        // Assert
        verify(producer, times(1)).sendMessage(anyString());
    }

    /**
     * Tests the scenario where the message sending fails.
     */
    @Test
    public void testSendMessageFailure() throws Throwable {
        // Arrange
        String orderId = "123";
        Integer taskType = 1;
        String remark = "remark";
        String bizId = "bizId";
        OrderChannelRerunDTO orderChannelRerunDTO = new OrderChannelRerunDTO();
        orderChannelRerunDTO.setOrderId(orderId);
        orderChannelRerunDTO.setTaskType(taskType);
        orderChannelRerunDTO.setRemark(remark);
        orderChannelRerunDTO.setBizId(bizId);
        ProducerResult result = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(anyString())).thenReturn(result);
        // Act
        orderChannelTaskRerunNotifyProducer.sendMessage(orderId, taskType, remark, bizId);
        // Assert
        verify(producer, times(1)).sendMessage(anyString());
    }

    /**
     * Tests the scenario where an exception is thrown during message sending.
     */
    @Test
    public void testSendMessageException() throws Throwable {
        // Arrange
        String orderId = "123";
        Integer taskType = 1;
        String remark = "remark";
        String bizId = "bizId";
        when(producer.sendMessage(anyString())).thenThrow(new Exception());
        // Act
        orderChannelTaskRerunNotifyProducer.sendMessage(orderId, taskType, remark, bizId);
        // Assert
        verify(producer, times(1)).sendMessage(anyString());
    }
}
