package com.sankuai.carnation.distribution.privatelive.distribution.domain.service.impl;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.intention.modify.ConsultantAuthorizeStatusProducer;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveTaskManageOperateRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ManageOperateTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.ConsultantAuthLog;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.ConsultantAuthLogRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveUserIntentionServiceImpl;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveDistributorTaskCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveTaskRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.facebook.swift.codec.$internal.asm.util.CheckClassAdapter.verify;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveTaskDomainServiceImplTest {

    @InjectMocks
    private PrivateLiveTaskDomainServiceImpl privateLiveTaskDomainService;

    @Mock
    private PrivateLiveTaskRepository privateLiveTaskRepository;

    @InjectMocks
    private PrivateLiveTaskDomainServiceImpl service;

    @Mock
    private ConsultantAuthLogRepository authLogRepository;

    @Mock
    private PrivateLiveUserIntentionServiceImpl privateLiveUserIntentionService;

    @Mock
    private ConsultantAuthorizeStatusProducer consultantAuthorizeStatusProducer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 初始化操作，如有需要
    }

    /**
     * 测试 batchJoinPrivateLiveTask 方法，当 distributorList 为空时
     */
    @Test
    public void testBatchJoinPrivateLiveTaskWithEmptyDistributorList() {
        PrivateLiveDistributorTaskCmd cmd = new PrivateLiveDistributorTaskCmd();
        cmd.setDistributorList(Lists.newArrayList());
        List<PrivateLiveTaskBO> result = privateLiveTaskDomainService.batchJoinPrivateLiveTask(cmd);
        assertTrue("结果应为空列表", result.isEmpty());
    }



    /**
     * 测试场景：当分销商列表为空时，应返回空列表
     */
    @Test
    public void testBatchJoinPrivateLiveTask_WithEmptyDistributorList() {
        // arrange
        PrivateLiveDistributorTaskCmd cmd = PrivateLiveDistributorTaskCmd.builder().anchorId(1L).liveId("live123").distributorList(Collections.emptyList()).build();
        // act
        List<PrivateLiveTaskBO> result = privateLiveTaskDomainService.batchJoinPrivateLiveTask(cmd);
        // assert
        assertTrue("当分销商列表为空时，应返回空列表", result.isEmpty());
    }

    /**
     * 测试场景：当分销商列表为null时，应返回空列表
     */
    @Test
    public void testBatchJoinPrivateLiveTask_WithNullDistributorList() {
        // arrange
        PrivateLiveDistributorTaskCmd cmd = PrivateLiveDistributorTaskCmd.builder().anchorId(1L).liveId("live123").distributorList(null).build();
        // act
        List<PrivateLiveTaskBO> result = privateLiveTaskDomainService.batchJoinPrivateLiveTask(cmd);
        // assert
        assertTrue("当分销商列表为null时，应返回空列表", result.isEmpty());
    }

    /**
     * 测试场景：当分销商列表不为空，但所有分销商已存在相应任务时，应返回非空列表，列表中的任务BO应与预期相符
     */
    @Test
    public void testBatchJoinPrivateLiveTask_WithExistingTasks() {
        // arrange
        DistributorModel distributorModel = DistributorModel.builder().accountId(1L).nickname("测试分销商").build();
        PrivateLiveDistributorTaskCmd cmd = PrivateLiveDistributorTaskCmd.builder().anchorId(1L).liveId("live123").distributorList(Arrays.asList(distributorModel)).build();
        PrivateLiveTaskBO existingTaskBO = new PrivateLiveTaskBO();
        existingTaskBO.setAccountId(1L);
        existingTaskBO.setNickname("测试分销商");
        when(privateLiveTaskRepository.queryByAccountIdAndLive(any(Long.class), any(String.class))).thenReturn(existingTaskBO);
        // act
        List<PrivateLiveTaskBO> result = privateLiveTaskDomainService.batchJoinPrivateLiveTask(cmd);
        // assert
        assertFalse("当分销商列表不为空，但所有分销商已存在相应任务时，应返回非空列表", result.isEmpty());
        assertEquals("返回的任务BO应与预期相符", existingTaskBO, result.get(0));
    }

    /**
     * 测试场景：当分销商列表不为空，且部分分销商已存在相应任务时，应正确处理已存在和新任务
     */
    @Test
    public void testBatchJoinPrivateLiveTask_WithSomeExistingTasks() {
        // arrange
        DistributorModel existingDistributor = DistributorModel.builder().accountId(1L).nickname("已存在的分销商").build();
        DistributorModel newDistributor = DistributorModel.builder().accountId(2L).build();
    }


    /**
     * 测试 joinPrivateLiveTask 方法，当 PrivateLiveTaskBO 不为空，且自动审批关闭
     */
    @Test
    public void testJoinPrivateLiveTaskWithAutoApproveDisabledAndTaskBONotNull() {
        PrivateLiveTaskBO existingTaskBO = new PrivateLiveTaskBO();
        existingTaskBO.setAutoApprove(false);
        PrivateLiveTaskCmd cmd = new PrivateLiveTaskCmd(1L, "group1", "live123", 1L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", 1L, "wechatNumber", null, false);
        when(privateLiveTaskRepository.queryByAccountIdAndLive(any(Long.class), any(String.class))).thenReturn(existingTaskBO);
        PrivateLiveTaskBO result = privateLiveTaskDomainService.joinPrivateLiveTask(cmd);
        assertNotNull("结果不应为空", result);
        assertFalse("自动审批应关闭", result.isAutoApprove());
    }

    /**
     * 测试 joinPrivateLiveTask 方法，当 PrivateLiveTaskBO 不为空，且自动审批开启
     */
    @Test
    public void testJoinPrivateLiveTaskWithAutoApproveEnabledAndTaskBONotNull() {
        PrivateLiveTaskBO existingTaskBO = new PrivateLiveTaskBO();
        existingTaskBO.setAutoApprove(true);
        PrivateLiveTaskCmd cmd = new PrivateLiveTaskCmd(1L, "group1", "live123", 1L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", 1L, "wechatNumber", null, true);
        when(privateLiveTaskRepository.queryByAccountIdAndLive(any(Long.class), any(String.class))).thenReturn(existingTaskBO);
        PrivateLiveTaskBO result = privateLiveTaskDomainService.joinPrivateLiveTask(cmd);
        assertNotNull("结果不应为空", result);
        assertTrue("自动审批应开启", result.isAutoApprove());
    }

    /**
     * 测试passAudit方法，当autoApprove为true时
     */
    @Test
    public void testPassAuditWithAutoApproveTrue() {
        // arrange
        PrivateLiveTaskBO requestBO = new PrivateLiveTaskBO();
        requestBO.setAutoApprove(true);
        requestBO.setOperateType(ManageOperateTypeEnum.AGREE);
        // act
        service.passAudit(requestBO);
        // assert
//        verify(authLogRepository, times(1)).insert(any(ConsultantAuthLog.class));
//        verify(consultantAuthorizeStatusProducer, times(1)).sendMessage(any(PrivateLiveTaskManageOperateRequest.class));
    }

    /**
     * 测试passAudit方法，当autoApprove为false时
     */
    @Test
    public void testPassAuditWithAutoApproveFalse() {
        // arrange
        PrivateLiveTaskBO requestBO = new PrivateLiveTaskBO();
        requestBO.setAutoApprove(false);
        requestBO.setOperateType(ManageOperateTypeEnum.REJECT);
        // act
        service.passAudit(requestBO);
        // assert
//        verify(authLogRepository, times(1)).insert(any(ConsultantAuthLog.class));
//        verify(consultantAuthorizeStatusProducer, times(1)).sendMessage(any(PrivateLiveTaskManageOperateRequest.class));
    }

    /**
     * 测试passAudit方法，当operateType为null时
     */
    @Test
    public void testPassAuditWithOperateTypeNull() {
        // arrange
        PrivateLiveTaskBO requestBO = new PrivateLiveTaskBO();
        requestBO.setAutoApprove(true);
        requestBO.setOperateType(null);
        // act
        service.passAudit(requestBO);
        // assert
//        verify(authLogRepository, times(1)).insert(any(ConsultantAuthLog.class));
//        verify(consultantAuthorizeStatusProducer, times(1)).sendMessage(any(PrivateLiveTaskManageOperateRequest.class));
    }
}
