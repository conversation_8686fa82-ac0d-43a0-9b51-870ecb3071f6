package com.sankuai.carnation.distribution.groundpromotion.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.event.api.enums.EventErrorCode;
import com.dianping.gmkt.event.api.enums.EventException;
import com.dianping.gmkt.event.api.promoqrcode.enums.PromoCodeType;
import com.dianping.gmkt.event.api.v2.model.DrawResult;
import com.dianping.gmkt.event.api.v2.request.EidRequest;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryCouponRequest;
import com.sankuai.carnation.distribution.groundpromotion.enums.QrPlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.ResourceDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.OperationCouponRequest;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeForCService;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionPageServiceImplGetDrawResultTest {

    @InjectMocks
    private GroundPromotionPageServiceImpl groundPromotionPageService;

    @Mock
    private PromoCodeForCService promoCodeForCService;

    @Mock
    private ExecutorService executor;

    @Before
    public void setUp() {
        reset(promoCodeForCService, executor);
    }

    /**
     * Test the scenario where the promoCodeForCService returns null.
     */
    @Test
    public void testGetDrawResult_NullResponse() throws Throwable {
        // arrange
        QueryCouponRequest queryCouponRequest = QueryCouponRequest.builder().groundPromotionId(123L).userId(456L).dpShopId(789L).platform(QrPlatformEnum.DIANPING.getCode()).source(1).templateId(1L).bizLine(1).cityId(1).build();
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(null);
        // Use reflection to invoke the private method
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawResult", QueryCouponRequest.class);
        method.setAccessible(true);
        List<DrawResult> result = (List<DrawResult>) method.invoke(groundPromotionPageService, queryCouponRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test the scenario where the promoCodeForCService returns a response with no data.
     */
    @Test
    public void testGetDrawResult_NoDataInResponse() throws Throwable {
        // arrange
        QueryCouponRequest queryCouponRequest = QueryCouponRequest.builder().groundPromotionId(123L).userId(456L).dpShopId(789L).platform(QrPlatformEnum.DIANPING.getCode()).source(1).templateId(1L).bizLine(1).cityId(1).build();
        RemoteResponse<OperationInfoDTO> response = RemoteResponse.success(null);
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(response);
        // Use reflection to invoke the private method
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawResult", QueryCouponRequest.class);
        method.setAccessible(true);
        List<DrawResult> result = (List<DrawResult>) method.invoke(groundPromotionPageService, queryCouponRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test the scenario where the ResourceDTO.getAwardInfo() is null.
     */
    @Test
    public void testGetDrawResult_NullAwardInfo() throws Throwable {
        // arrange
        QueryCouponRequest queryCouponRequest = QueryCouponRequest.builder().groundPromotionId(123L).userId(456L).dpShopId(789L).platform(QrPlatformEnum.DIANPING.getCode()).source(1).templateId(1L).bizLine(1).cityId(1).build();
        OperationInfoDTO operationInfoDTO = new OperationInfoDTO();
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setAwardInfo(null);
        operationInfoDTO.setResourceConfig(resourceDTO);
        RemoteResponse<OperationInfoDTO> response = RemoteResponse.success(operationInfoDTO);
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(response);
        // Use reflection to invoke the private method
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawResult", QueryCouponRequest.class);
        method.setAccessible(true);
        List<DrawResult> result = (List<DrawResult>) method.invoke(groundPromotionPageService, queryCouponRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test the scenario where the executor service fails to process some tasks.
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetDrawResult_ExecutorServiceFailure() throws Throwable {
        // arrange
        QueryCouponRequest queryCouponRequest = QueryCouponRequest.builder().groundPromotionId(123L).userId(456L).dpShopId(789L).platform(QrPlatformEnum.DIANPING.getCode()).source(1).templateId(1L).bizLine(1).cityId(1).build();
        OperationInfoDTO operationInfoDTO = new OperationInfoDTO();
        ResourceDTO resourceDTO = new ResourceDTO();
        AwardInfoDTO awardInfoDTO = new AwardInfoDTO();
        awardInfoDTO.setMainAwardConfig(Collections.singletonList(new AwardActivityDTO()));
        resourceDTO.setAwardInfo(awardInfoDTO);
        operationInfoDTO.setResourceConfig(resourceDTO);
        RemoteResponse<OperationInfoDTO> response = RemoteResponse.success(operationInfoDTO);
        when(promoCodeForCService.queryOperationConfig(any(OperationCouponRequest.class))).thenReturn(response);
        // Mock the Future and executor behavior
        Future<List<DrawResult>> future = (Future<List<DrawResult>>) mock(Future.class);
        // Use reflection to invoke the private method
        Method method = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawResult", QueryCouponRequest.class);
        method.setAccessible(true);
        List<DrawResult> result = (List<DrawResult>) method.invoke(groundPromotionPageService, queryCouponRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
