package com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatUserInfoResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/9/3
 */
@RunWith(MockitoJUnitRunner.class)
public class PrivateLivePermissionForCGatewayServiceImplTest {
    @InjectMocks
    private PrivateLivePermissionForCGatewayServiceImpl service;

    @Mock
    private WeChatUserService weChatUserService;
    /**
     * 测试wxToken为空的情况
     */
    @Test
    public void testQueryWechatUserInfoWxTokenIsEmpty() {
        // arrange
        String wxToken = "";

        // act
        RemoteResponse<WeChatUserInfoResponse> result = service.queryWechatUserInfo(wxToken);

        // assert
        assertNotNull(result);
        assertEquals("wxToken为空", result.getMsg());
    }

    /**
     * 测试未查询到用户信息的情况
     */
    @Test
    public void testQueryWechatUserInfoUserNotFound() {
        // arrange
        String wxToken = "validToken";
        when(weChatUserService.getWeChatUserInfoByToken(wxToken)).thenReturn(null);

        // act
        RemoteResponse<WeChatUserInfoResponse> result = service.queryWechatUserInfo(wxToken);

        // assert
        assertNotNull(result);
        assertEquals("未查询到用户信息", result.getMsg());
    }

    /**
     * 测试查询到用户信息，但返回码不是200的情况
     */
    @Test
    public void testQueryWechatUserInfoResponseCodeNot200() {
        // arrange
        String wxToken = "validToken";
        RemoteResponse<WeChatLoginInfo> mockResponse = RemoteResponse.fail("error");
        when(weChatUserService.getWeChatUserInfoByToken(wxToken)).thenReturn(mockResponse);

        // act
        RemoteResponse<WeChatUserInfoResponse> result = service.queryWechatUserInfo(wxToken);

        // assert
        assertNotNull(result);
        assertEquals("未查询到用户信息", result.getMsg());
    }

    /**
     * 测试成功查询到用户信息的情况
     */
    @Test
    public void testQueryWechatUserInfoSuccess() {
        // arrange
        String wxToken = "validToken";
        WeChatLoginInfo loginInfo = new WeChatLoginInfo();
        loginInfo.setOpenId("openId");
        loginInfo.setUnionId("unionId");
        RemoteResponse<WeChatLoginInfo> mockResponse = RemoteResponse.success(loginInfo);
        when(weChatUserService.getWeChatUserInfoByToken(wxToken)).thenReturn(mockResponse);

        // act
        RemoteResponse<WeChatUserInfoResponse> result = service.queryWechatUserInfo(wxToken);

        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("openId", result.getData().getOpenId());
        assertEquals("unionId", result.getData().getUnionId());
    }

    @Test
    public void testLoginV2ParamBlank() {
        RemoteResponse<WeChatLoginInfo> response = service.loginV2(WeChatLoginRequest.builder().build());
        assertFalse(response.isSuccess());
    }

    @Test
    public void testLoginV2Error() {
        when(weChatUserService.getWeChatUserInfo(any())).thenReturn(RemoteResponse.fail("error"));
        RemoteResponse<WeChatLoginInfo> response = service.loginV2(WeChatLoginRequest.builder().code("xxx").build());
        assertFalse(response.isSuccess());
    }

    @Test
    public void testLoginV2Success() {
        WeChatLoginInfo loginInfo = WeChatLoginInfo.builder().unionId("xxx").openId("xxxx").sessionKey("xxxxxxx").wxToken("").build();
        when(weChatUserService.getWeChatUserInfo(any())).thenReturn(RemoteResponse.success(loginInfo));
        RemoteResponse<WeChatLoginInfo> response = service.loginV2(WeChatLoginRequest.builder().code("xxx").build());
        assertTrue(response.isSuccess());
    }
}