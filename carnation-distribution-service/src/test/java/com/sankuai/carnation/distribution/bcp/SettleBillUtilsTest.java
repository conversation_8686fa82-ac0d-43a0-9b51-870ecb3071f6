package com.sankuai.carnation.distribution.bcp;

import static org.junit.Assert.*;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SettleBillUtilsTest {

    /**
     * 测试 needTradeRiskControl 方法，当 BcpCheckRule 对象为 null 时，应返回 true
     */
    @Test
    public void testNeedTradeRiskControlWhenRuleIsNull() throws Throwable {
        // arrange
        BcpCheckRule rule = null;
        // act
        boolean result = SettleBillUtils.needTradeRiskControl(rule);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 needTradeRiskControl 方法，当 BcpCheckRule 对象不为 null 时，应返回 BcpCheckRule 对象的 needTradeRiskControl 属性值
     */
    @Test
    public void testNeedTradeRiskControlWhenRuleIsNotNull() throws Throwable {
        // arrange
        BcpCheckRule rule = new BcpCheckRule();
        rule.setNeedTradeRiskControl(true);
        // act
        boolean result = SettleBillUtils.needTradeRiskControl(rule);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 parseBatchNo 方法，当 rule 为 null 时
     */
    @Test
    public void testParseBatchNoRuleIsNull() {
        // arrange
        BcpCheckRule rule = null;
        Date actionTime = new Date();
        // act
        String result = SettleBillUtils.parseBatchNo(rule, actionTime);
        // assert
        assertNull(result);
    }

    /**
     * 测试 parseBatchNo 方法，当 rule 不为 null，且 batchNoType 为 DAY_BATCH 时
     */
    @Test
    public void testParseBatchNoBatchNoTypeIsDayBatch() {
        // arrange
        BcpCheckRule rule = new BcpCheckRule();
        rule.setBatchNoType(BatchNoTypeEnum.DAY_BATCH);
        Date actionTime = new Date();
        // act
        String result = SettleBillUtils.parseBatchNo(rule, actionTime);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    /**
     * 测试 parseBatchNo 方法，当 rule 不为 null，且 batchNoType 不为 DAY_BATCH 时
     */
    @Test
    public void testParseBatchNoBatchNoTypeIsNotDayBatch() {
        // arrange
        BcpCheckRule rule = new BcpCheckRule();
        rule.setBatchNoType(BatchNoTypeEnum.MONTH_BATCH);
        Date actionTime = new Date();
        // act
        String result = SettleBillUtils.parseBatchNo(rule, actionTime);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}"));
    }
}
