package com.sankuai.carnation.distribution.intention.domain.calculate.task;

import com.alibaba.fastjson.JSONObject;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelTaskStatusEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.OrderChannelRerunTransaction;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.running.OrderChannelTaskRunner;
import com.sankuai.carnation.distribution.intention.dto.OrderChannelRerunDTO;
import com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalRunningTaskMapper;
import com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalTaskMapper;
import com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelRerunLogMapper;
import com.sankuai.technician.trade.api.order.application.service.OrderSyncService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogWithBLOBs;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderChannelRerunTransactionTest {

    @InjectMocks
    private OrderChannelRerunTransaction orderChannelRerunTransaction;

    @Mock
    private DistributionOrderChannelRerunLogMapper rerunLogMapper;

    @Mock
    private DistributionOrderChannelCalRunningTaskMapper runningTaskMapper;

    @Mock
    private DistributionOrderChannelCalTaskMapper taskMapper;

    @Mock
    private OrderChannelTaskRunner taskRunner;

    @Mock
    private OrderChannelCalculateMerger calculateMerger;

    @Mock
    private OrderSyncService orderSyncService;

    private OrderChannelRerunDTO orderChannelRerunDTO;

    @Before
    public void setUp() {
        orderChannelRerunDTO = new OrderChannelRerunDTO();
        orderChannelRerunDTO.setBizId("testBizId");
        orderChannelRerunDTO.setOrderId("testOrderId");
        orderChannelRerunDTO.setTaskType(1);
    }

    @Test
    public void testRerunAndUpdateChannelTask_ExistRerunLog() {
        when(rerunLogMapper.selectByExample(any())).thenReturn(Lists.newArrayList(new DistributionOrderChannelRerunLogWithBLOBs()));
        boolean result = orderChannelRerunTransaction.rerunAndUpdateChannelTask(orderChannelRerunDTO);
        assertTrue(result);
    }

    @Test
    public void testRerunAndUpdateChannelTask_NoRerunLogAndNoRunningTaskOrTask() {
        when(rerunLogMapper.selectByExample(any())).thenReturn(Lists.newArrayList());
        when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList());
        when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList());
        boolean result = orderChannelRerunTransaction.rerunAndUpdateChannelTask(orderChannelRerunDTO);
        assertFalse(result);
    }

    @Test
    public void testRerunAndUpdateChannelTask_RerunResultSame() {
        when(rerunLogMapper.selectByExample(any())).thenReturn(Lists.newArrayList());

        IntentionCalculateTaskParamBO formerTaskParam = IntentionCalculateTaskParamBO.builder().orderType(1).orderId("testOrderId").distributionCode("distributionCode").build();
        OrderChannelTaskRunningResultBO formerResult = new OrderChannelTaskRunningResultBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        runningTask.setParam(JSONObject.toJSONString(formerTaskParam));
        runningTask.setResult(JSONObject.toJSONString(formerResult));
        runningTask.setStatus(OrderChannelTaskStatusEnum.ENDED.getCode());
        runningTask.setOrderType(1);
        runningTask.setOrderId("testOrderId");
        runningTask.setTaskType(1);
        runningTask.setCalTaskId(1L);
        when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList(runningTask));
        when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList(new DistributionOrderChannelCalTaskWithBLOBs()));

        when(taskRunner.calculateTaskResult(anyInt(), anyLong(), any())).thenReturn(formerResult);

        boolean result = orderChannelRerunTransaction.rerunAndUpdateChannelTask(orderChannelRerunDTO);
        assertTrue(result);
    }

    @Test
    public void testRerunAndUpdateChannelTask_RerunResultSuccess() {
        when(rerunLogMapper.selectByExample(any())).thenReturn(Lists.newArrayList());

        DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        IntentionCalculateTaskParamBO formerTaskParam = IntentionCalculateTaskParamBO.builder().orderType(1).orderId("testOrderId").distributionCode("distributionCode").build();
        OrderChannelTaskRunningResultBO formerResult = new OrderChannelTaskRunningResultBO();
        runningTask.setParam(JSONObject.toJSONString(formerTaskParam));
        runningTask.setResult(JSONObject.toJSONString(formerResult));
        runningTask.setStatus(OrderChannelTaskStatusEnum.ENDED.getCode());
        runningTask.setOrderType(1);
        runningTask.setOrderId("testOrderId");
        runningTask.setTaskType(1);
        runningTask.setCalTaskId(1L);
        when(runningTaskMapper.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList(runningTask));

        DistributionOrderChannelCalTaskWithBLOBs task = new DistributionOrderChannelCalTaskWithBLOBs();
        task.setParams(JSONObject.toJSONString(formerTaskParam));
        OrderChannelTaskResultBO formerTaskResult = new OrderChannelTaskResultBO();
        task.setResult(JSONObject.toJSONString(formerTaskResult));
        task.setStatus(OrderChannelTaskStatusEnum.ENDED.getCode());
        task.setOrderType(1);
        task.setOrderId("testOrderId");
        task.setId(1L);
        when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(Lists.newArrayList(task));

        OrderChannelTaskRunningResultBO rerunResult = new OrderChannelTaskRunningResultBO();
        rerunResult.setBusinessChannel("private_live");
        when(taskRunner.calculateTaskResult(anyInt(), anyLong(), any())).thenReturn(rerunResult);
        doReturn(1).when(runningTaskMapper).updateByPrimaryKeySelective(any());
        when(calculateMerger.merge(anyLong(), anyBoolean())).thenReturn(formerTaskResult);

        doReturn(1).when(rerunLogMapper).insertSelective(any());
        doReturn(new TechnicianResp()).when(orderSyncService).notifySyncOrderAndVerify(any());

        boolean result = orderChannelRerunTransaction.rerunAndUpdateChannelTask(orderChannelRerunDTO);
        assertTrue(result);
    }
}
