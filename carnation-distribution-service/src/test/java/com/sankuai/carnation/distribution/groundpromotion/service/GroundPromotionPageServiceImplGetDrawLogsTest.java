package com.sankuai.carnation.distribution.groundpromotion.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.event.api.api.EventRpcService;
import com.dianping.gmkt.event.api.enums.EventErrorCode;
import com.dianping.gmkt.event.api.v2.model.PigeonResponse;
import com.dianping.gmkt.event.api.v2.model.RichDrawLog;
import com.dianping.gmkt.event.api.v2.request.EidRequest;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryCouponRequest;
import com.sankuai.carnation.distribution.groundpromotion.exception.GroundPromotionException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class GroundPromotionPageServiceImplGetDrawLogsTest {

    @InjectMocks
    private GroundPromotionPageServiceImpl groundPromotionPageService;

    @Mock
    private EventRpcService eventRpcService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetDrawLogsNullResponseScenario() throws Throwable {
        EidRequest eidRequest = new EidRequest();
        Method getDrawLogsMethod = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawLogs", EidRequest.class);
        getDrawLogsMethod.setAccessible(true);
        // Mock the eventRpcService to return null, which should trigger the GroundPromotionException
        when(eventRpcService.checkWinedRichPrizes(eidRequest)).thenReturn(null);
        try {
            getDrawLogsMethod.invoke(groundPromotionPageService, eidRequest);
            fail("Expected GroundPromotionException to be thrown");
        } catch (InvocationTargetException e) {
            // Check if the cause of the InvocationTargetException is the expected GroundPromotionException
            assertTrue(e.getCause() instanceof GroundPromotionException);
        }
    }

    // Other test cases remain unchanged
    @Test
    public void testGetDrawLogsNormalScenario() throws Throwable {
        EidRequest eidRequest = new EidRequest();
        PigeonResponse<List<RichDrawLog>> pigeonResponse = new PigeonResponse<>(Collections.singletonList(new RichDrawLog()));
        when(eventRpcService.checkWinedRichPrizes(eidRequest)).thenReturn(pigeonResponse);
        Method getDrawLogsMethod = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawLogs", EidRequest.class);
        getDrawLogsMethod.setAccessible(true);
        List<RichDrawLog> result = (List<RichDrawLog>) getDrawLogsMethod.invoke(groundPromotionPageService, eidRequest);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetDrawLogsErrorResponseScenario() throws Throwable {
        EidRequest eidRequest = new EidRequest();
        PigeonResponse<List<RichDrawLog>> pigeonResponse = new PigeonResponse<>(false, "500", "Error", null);
        when(eventRpcService.checkWinedRichPrizes(eidRequest)).thenReturn(pigeonResponse);
        Method getDrawLogsMethod = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawLogs", EidRequest.class);
        getDrawLogsMethod.setAccessible(true);
        List<RichDrawLog> result = (List<RichDrawLog>) getDrawLogsMethod.invoke(groundPromotionPageService, eidRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDrawLogsNullDataScenario() throws Throwable {
        EidRequest eidRequest = new EidRequest();
        PigeonResponse<List<RichDrawLog>> pigeonResponse = new PigeonResponse<>(true, "200", "Success", null);
        when(eventRpcService.checkWinedRichPrizes(eidRequest)).thenReturn(pigeonResponse);
        Method getDrawLogsMethod = GroundPromotionPageServiceImpl.class.getDeclaredMethod("getDrawLogs", EidRequest.class);
        getDrawLogsMethod.setAccessible(true);
        List<RichDrawLog> result = (List<RichDrawLog>) getDrawLogsMethod.invoke(groundPromotionPageService, eidRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
