package com.sankuai.carnation.distribution.privatelive.consultant.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.beauty.ibot.service.BeautiBotUserService;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.intention.modify.PrivateLiveIntentionModifyDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionQueryRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveIntentionModifyRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteRelationInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteWxUserInfo;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/13
 * @Description:
 */
@Ignore
@RunWith(MockitoJUnitRunner.class)
public class UserIntentionFixJobTest {

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;
    @Mock
    private PrivateLiveConsultantVerifyService privateLiveConsultantVerifyService;
    @Mock
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;
    @Mock
    private PrivateLiveIntentionModifyDomainService modifyDomainService;
    @Mock
    private BeautiBotUserService beautiBotUserService;

    @InjectMocks
    private UserIntentionFixJob userIntentionFixJob;

    private JSONArray jsonArray;
    private JSONObject jsonObject;
    private List<JSONArray> sheetList;

    @Before
    public void setUp() {
        jsonArray = new JSONArray();
        jsonObject = new JSONObject();
        jsonObject.put("wxId", "testWxId");
        jsonObject.put("liveId", "testLiveId");
        jsonArray.add(jsonObject);
        sheetList = new ArrayList<>();
        sheetList.add(jsonArray);
    }

    /**
     * 测试场景：正常情况下修复用户意向
     */
    @Test
    public void testGroupUnIntentionFixSuccess() throws Throwable {
        // arrange
        PrivateLiveUserIntentionResult privateLiveUserIntentionResult = new PrivateLiveUserIntentionResult();
        privateLiveUserIntentionResult.setConsultantTaskId(0L);
        when(userIntentionResultRepository.query(any())).thenReturn(Lists.newArrayList(privateLiveUserIntentionResult));

        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = new MemberInviteRelationInfoDTO();
        MemberInviteWxUserInfo memberInviteWxUserInfo = new MemberInviteWxUserInfo();
        memberInviteWxUserInfo.setDistance(1);
        memberInviteWxUserInfo.setWxId("wxId");
        memberInviteWxUserInfo.setUnionId("unionId");
        memberInviteRelationInfoDTO.setUpInviteUserInfoList(Lists.newArrayList(memberInviteWxUserInfo));
        when(privateSphereUserAclService.queryInviteRelation(anyString(), anyString(), anyInt())).thenReturn(memberInviteRelationInfoDTO);

        when(privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(anyString(), anyString())).thenReturn(RemoteResponse.fail(""));

        doNothing().when(modifyDomainService).modifyIntention(any(), anyInt());

        // act
        userIntentionFixJob.groupUnIntentionFix();

        // assert
        verify(privateSphereUserAclService, times(1)).queryInviteRelation(anyString(), anyString(), anyInt());
        verify(privateLiveConsultantVerifyService, times(1)).checkByUnionIdAndLiveId(anyString(), anyString());
        verify(userIntentionResultRepository, times(2)).query(any());
        verify(modifyDomainService, times(1)).modifyIntention(any(), anyInt());
    }

    /**
     * 测试场景：当不存在有效的社群待归因记录时
     */
    @Test
    public void testGroupUnIntentionFixWhenNoValidRecord() throws Throwable {
        // arrange
        when(userIntentionResultRepository.query(any())).thenReturn(Lists.newArrayList());

        // act
        userIntentionFixJob.groupUnIntentionFix();

        // assert
        verify(privateSphereUserAclService, never()).queryInviteRelation(anyString(), anyString(), anyInt());
        verify(privateLiveConsultantVerifyService, never()).checkByUnionIdAndLiveId(anyString(), anyString());
        verify(userIntentionResultRepository, times(1)).query(any());
        verify(modifyDomainService, never()).modifyIntention(any(), anyInt());
    }

    /**
     * 测试场景：当查询邀约关系失败时
     */
    @Test
    public void testGroupUnIntentionFixWhenQueryInviteRelationFails() throws Throwable {
        // arrange
        PrivateLiveUserIntentionResult privateLiveUserIntentionResult = new PrivateLiveUserIntentionResult();
        privateLiveUserIntentionResult.setConsultantTaskId(0L);
        when(userIntentionResultRepository.query(any())).thenReturn(Lists.newArrayList(privateLiveUserIntentionResult));
        when(privateSphereUserAclService.queryInviteRelation(anyString(), anyString(), anyInt())).thenReturn(null);
        doNothing().when(modifyDomainService).modifyIntention(any(), anyInt());

        // act
        userIntentionFixJob.groupUnIntentionFix();

        // assert
        verify(privateSphereUserAclService, times(1)).queryInviteRelation(anyString(), anyString(), anyInt());
        verify(privateLiveConsultantVerifyService, never()).checkByUnionIdAndLiveId(anyString(), anyString());
        verify(userIntentionResultRepository, times(1)).query(any());
        verify(modifyDomainService, times(1)).modifyIntention(any(), anyInt());
    }
}
