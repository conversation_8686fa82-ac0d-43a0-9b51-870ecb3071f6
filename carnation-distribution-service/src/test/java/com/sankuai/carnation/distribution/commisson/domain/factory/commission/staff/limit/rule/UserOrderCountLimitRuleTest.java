package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.limit.rule;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleLimitRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.sankuai.carnation.distribution.commisson.limit.enums.RebateLimitReasonType;
import com.sankuai.carnation.distribution.commisson.limit.rule.UserOrderCountLimitRule;
import com.sankuai.carnation.distribution.commisson.repository.RebateLimitRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * @description :
 * @date : 2024/7/31
 */
@RunWith(MockitoJUnitRunner.class)
public class UserOrderCountLimitRuleTest {

    @InjectMocks
    private UserOrderCountLimitRule shopAmountLimitRule;

    @Mock
    private RebateLimitRepository rebateLimitRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private RebateActivityRuleDTO buildRebateActivityRuleDTO(int count) {
        RebateActivityRuleDTO rule = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleData = new RebateSettleRuleDTO();
        RebateSettleLimitRuleDTO rebateSettleLimitRule = new RebateSettleLimitRuleDTO();
        rebateSettleLimitRule.setUserOrderCountLimit(count);
        ruleData.setRebateSettleLimitRule(rebateSettleLimitRule);
        rule.setRule(ruleData);
        return rule;
    }

    private CommissionVerifyCalculationRequest defaultRequest() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("orderId");
        request.setTechId(1);
        return request;
    }

    private RebateAmountResult calculationResult(long rebateAmount) {
        RebateAmountResult rebateAmountOrder = new RebateAmountResult();
        rebateAmountOrder.setRebateAmount(rebateAmount);
        return rebateAmountOrder;
    }

    /**
     * 测试当订单总数小于限制规则时的情况
     */
    @Test
    public void testCalculatorResultWhenSumAllOrderResultEqualsLimitRule() {
        // arrange
        RebateActivityRuleDTO limitRule = buildRebateActivityRuleDTO(10); //活动限制10
        RebateAmountResult rebateAmountResult = calculationResult(100); // 订单计算返利100

        // 模拟queryOrderCount返回的次数，已返利5单
        when(rebateLimitRepository.queryOrderCount(any(), any(), any(), anyInt())).thenReturn(5L);
        // act
        RebateLimitResult result = shopAmountLimitRule.handleResult(defaultRequest(), rebateAmountResult, limitRule);

        // assert
        assertFalse("结果应为false，因为订单总数小于限制规则", result.isResult());
        assertEquals(Long.valueOf(0), result.getRebateAmount());
        assertEquals(null, result.getReason());
    }

    /**
     * 测试当订单总数等于限制规则时的情况
     */
    @Test
    public void testCalculatorResultSumAllOrderResultPlusRebateAmountGreaterThanLimitRule() {
        // arrange
        RebateActivityRuleDTO limitRule = buildRebateActivityRuleDTO(10); //活动限制10
        RebateAmountResult rebateAmountResult = calculationResult(200); // 订单计算返利200

        // 已经返利10单
        when(rebateLimitRepository.queryOrderCount(any(), any(), any(), anyInt())).thenReturn(10L);

        // act
        RebateLimitResult result = shopAmountLimitRule.handleResult(defaultRequest(), rebateAmountResult, limitRule);

        // assert
        assertTrue("结果应为true，因为订单总数等于限制规则", result.isResult());
        assertEquals("返利金额应为0", Long.valueOf(0), result.getRebateAmount());
        assertEquals("原因应该为：与同一顾客可返利订单数已达上限,本单未返利", RebateLimitReasonType.USER_TRANSACTION_LIMIT_REACHED.getReason(), result.getReason());
    }

    /**
     * 测试当订单总数大于限制规则时的情况
     */
    @Test
    public void testCalculatorResultSumAllOrderResultPlusRebateAmountNotExceedLimitRule() {
        // arrange
        RebateActivityRuleDTO limitRule = buildRebateActivityRuleDTO(10); //活动限制10
        RebateAmountResult rebateAmountResult = calculationResult(100); // 订单计算返利100

        // queryOrderCount
        when(rebateLimitRepository.queryOrderCount(any(), any(), any(), anyInt())).thenReturn(15L);

        // act
        RebateLimitResult result = shopAmountLimitRule.handleResult(defaultRequest(), rebateAmountResult, limitRule);

        // assert
        assertTrue("结果应为true，因为订单总数等于限制规则", result.isResult());
        assertEquals("返利金额应为0", Long.valueOf(0), result.getRebateAmount());
        assertEquals("原因应该为：与同一顾客可返利订单数已达上限,本单未返利", RebateLimitReasonType.USER_TRANSACTION_LIMIT_REACHED.getReason(), result.getReason());
    }
}
