package com.sankuai.carnation.distribution.distributor.repository.impl;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.converter.DistributorGroupAttrConverter;
import com.sankuai.carnation.distribution.distributor.converter.DistributorGroupBindConverter;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBindBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupAttrBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBindBO;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupAttrMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupBindMapper;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttrCriteria;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBindCriteria;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.util.ObjectUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * DistributorGroupRepositoryImpl的queryByGroupId方法测试
 */
public class DistributorGroupRepositoryImplTest {

    @InjectMocks
    private DistributorGroupRepositoryImpl distributorGroupRepositoryImpl;

    @Mock
    private DistributorGroupRootService distributorGroupRootService;

    @Mock
    private DistributorGroupBindMapper distributorGroupBindMapper;

    @InjectMocks
    private DistributorGroupRepositoryImpl distributorGroupRepository;

    @Mock
    private DistributorGroupBindConverter distributorGroupBindConverter;

    @Mock
    private DistributorGroupAttrMapper distributorGroupAttrMapper;

    @Mock
    private DistributorGroupAttrConverter distributorGroupAttrConverter;

    private final Long distributorId = 1L;

    private final List<Integer> statusList = Lists.newArrayList(1, 2, 3);

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    /**
     * 测试queryByGroupId方法，当DistributorGroupBO为空时
     */
    @Test
    public void testQueryByGroupIdWhenDistributorGroupBOIsNull() {
        // arrange
        Long groupId = 1L;
        when(distributorGroupRootService.getDistributorGroup(anyInt())).thenReturn(null);
        // act
        DistributorGroupBO result = distributorGroupRepositoryImpl.queryByGroupId(groupId);
        // assert
        assertNull(result);
    }

    /**
     * 测试queryByGroupId方法，当DistributorGroupBO不为空且DistributorGroupAttrList为空时
     */
    @Test
    public void testQueryByGroupIdWhenDistributorGroupBOIsNotNullAndDistributorGroupAttrListIsEmpty() {
        // arrange
        Long groupId = 1L;
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        when(distributorGroupRootService.getDistributorGroup(anyInt())).thenReturn(distributorGroupBO);
        // act
        DistributorGroupBO result = distributorGroupRepositoryImpl.queryByGroupId(groupId);
        // assert
        assertNotNull(result);
        assertTrue(ObjectUtils.isEmpty(result.getDistributorGroupAttrList()));
    }


    /**
     * 测试 save 方法，正常保存情况
     */
    @Test
    public void testSaveSuccess() throws Exception {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        when(distributorGroupRootService.setDistributorGroup(any(DistributorGroupBO.class))).thenReturn(1);
        // act
        int result = distributorGroupRepositoryImpl.save(distributorGroupBO);
        // assert
        verify(distributorGroupRootService, times(1)).setDistributorGroup(any(DistributorGroupBO.class));
        assert result == 1;
    }

    /**
     * 测试 save 方法，抛出异常情况
     */
    @Test(expected = BizSceneException.class)
    public void testSaveThrowsException() throws Exception {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        when(distributorGroupRootService.setDistributorGroup(any(DistributorGroupBO.class))).thenThrow(new RuntimeException());
        // act
        distributorGroupRepositoryImpl.save(distributorGroupBO);
        // assert is handled by the expected exception
    }

    /**
     * 测试 save 方法，当 distributorGroupRootService 返回 0 时
     */
    @Test
    public void testSaveReturnZero() throws Exception {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        when(distributorGroupRootService.setDistributorGroup(any(DistributorGroupBO.class))).thenReturn(0);
        // act
        int result = distributorGroupRepositoryImpl.save(distributorGroupBO);
        // assert
        verify(distributorGroupRootService, times(1)).setDistributorGroup(any(DistributorGroupBO.class));
        assert result == 0;
    }

    /**
     * 测试 save 方法，当 distributorGroupRootService 抛出 BizSceneException 时
     */
    @Test(expected = BizSceneException.class)
    public void testSaveThrowsBizSceneException() throws Exception {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        when(distributorGroupRootService.setDistributorGroup(any(DistributorGroupBO.class))).thenThrow(new BizSceneException("保存分销商（团长）失败"));
        // act
        distributorGroupRepositoryImpl.save(distributorGroupBO);
        // assert is handled by the expected exception
    }

    /**
     * 测试查询关联分销组列表，输入为空列表
     */
    @Test
    public void testQueryRelationDistributorGroupList_EmptyInput() {
        // arrange
        List<Long> distributorIdList = Lists.newArrayList();
        // act
        Map<Long, DistributorBindBO> result = distributorGroupRepository.queryRelationDistributorGroupList(distributorIdList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试查询关联分销组列表，查询结果为空
     */
    @Test
    public void testQueryRelationDistributorGroupList_EmptyResult() {
        // arrange
        List<Long> distributorIdList = Lists.newArrayList(1L, 2L);
        when(distributorGroupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(Lists.newArrayList());
        // act
        Map<Long, DistributorBindBO> result = distributorGroupRepository.queryRelationDistributorGroupList(distributorIdList);
        // assert
        assertTrue(result.isEmpty());
    }



    /**
     * 测试queryRelationDistributorGroup方法，当传入的distributorId对应的分销商不存在时
     */
    @Test
    public void testQueryRelationDistributorGroup_DistributorNotExist() {
        // arrange
        Long distributorId = 1L;
        when(distributorGroupRootService.batchQueryDistributorGroupMap(anyList())).thenReturn(new HashMap<>());
        // act
        DistributorBindBO result = distributorGroupRepository.queryRelationDistributorGroup(distributorId);
        // assert
        assertNull(result);
    }



    /**
     * 测试queryRelationDistributorGroup方法，当传入的distributorIdList为空时
     */
    @Test
    public void testQueryRelationDistributorGroupList_DistributorIdListIsEmpty() {
        // arrange
        List<Long> distributorIdList = Collections.emptyList();
        // act
        Map<Long, DistributorBindBO> result = distributorGroupRepository.queryRelationDistributorGroupList(distributorIdList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试查询分销商组绑定，当传入的分销商ID列表和状态列表都不为空时
     */
    @Test
    public void testQueryDistributorGroupBind_WithValidDistributorIdListAndStatusList() {
        // arrange
        List<Long> distributorIdList = Arrays.asList(1L, 2L);
        List<Integer> statusList = Arrays.asList(1, 2);
        DistributorGroupBind mockBind = new DistributorGroupBind(1L, 1, 1L, 1, null, null);
        DistributorGroupBindBO mockBindBO = new DistributorGroupBindBO();
        mockBindBO.setDistributorGroupId(1L);
        mockBindBO.setDistributorId(1L);
        mockBindBO.setBindStatus(1);
        when(distributorGroupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(Collections.singletonList(mockBind));
        when(distributorGroupBindConverter.toEntity(mockBind)).thenReturn(mockBindBO);
        // act
        List<DistributorGroupBindBO> result = distributorGroupRepository.queryDistributorGroupBind(distributorIdList, statusList);
        // assert
        assertFalse("结果不应为空", ObjectUtils.isEmpty(result));
        assertEquals("结果列表大小应为1", 1, result.size());
        assertEquals("绑定状态应相等", mockBindBO.getBindStatus(), result.get(0).getBindStatus());
    }

    /**
     * 测试查询分销商组绑定，当传入的分销商ID列表为空时
     */
    @Test
    public void testQueryDistributorGroupBind_WithEmptyDistributorIdList() {
        // arrange
        List<Long> distributorIdList = Collections.emptyList();
        List<Integer> statusList = Arrays.asList(1, 2);
        // act
        List<DistributorGroupBindBO> result = distributorGroupRepository.queryDistributorGroupBind(distributorIdList, statusList);
        // assert
        assertTrue("结果应为空", ObjectUtils.isEmpty(result));
    }

    /**
     * 测试查询分销商组绑定，当数据库中没有匹配的记录时
     */
    @Test
    public void testQueryDistributorGroupBind_WithNoMatchingRecords() {
        // arrange
        List<Long> distributorIdList = Arrays.asList(1L, 2L);
        List<Integer> statusList = Arrays.asList(1, 2);
        when(distributorGroupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(Collections.emptyList());
        // act
        List<DistributorGroupBindBO> result = distributorGroupRepository.queryDistributorGroupBind(distributorIdList, statusList);
        // assert
        assertTrue("结果应为空", ObjectUtils.isEmpty(result));
    }








    /**
     * 测试 queryDistributorGroupAttrMap 方法，输入为空列表
     */
    @Test
    public void testQueryDistributorGroupAttrMapEmptyInput() {
        // arrange
        List<Long> distributorGroupIdList = Arrays.asList();
        // act
        Map<Long, List<DistributorGroupAttrBO>> result = distributorGroupRepositoryImpl.queryDistributorGroupAttrMap(distributorGroupIdList);
        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 queryDistributorGroupAttrMap 方法，正常情况
     */
    @Test
    public void testQueryDistributorGroupAttrMapNormal() {
        // arrange
        List<Long> distributorGroupIdList = Arrays.asList(1L, 2L);
        DistributorGroupAttr distributorGroupAttr1 = new DistributorGroupAttr();
        distributorGroupAttr1.setGroupId(1);
        DistributorGroupAttr distributorGroupAttr2 = new DistributorGroupAttr();
        distributorGroupAttr2.setGroupId(2);
        List<DistributorGroupAttr> distributorGroupAttrList = Arrays.asList(distributorGroupAttr1, distributorGroupAttr2);
        when(distributorGroupAttrMapper.selectByExample(any(DistributorGroupAttrCriteria.class))).thenReturn(distributorGroupAttrList);
        when(distributorGroupAttrConverter.toEntity(any(DistributorGroupAttr.class))).thenAnswer(invocation -> {
            DistributorGroupAttr attr = invocation.getArgument(0);
            return new DistributorGroupAttrBO(null, attr.getGroupId(), null, null, null, DistributionStatusEnum.VALID);
        });
        // act
        Map<Long, List<DistributorGroupAttrBO>> result = distributorGroupRepositoryImpl.queryDistributorGroupAttrMap(distributorGroupIdList);
        // assert
        assertFalse("结果不应为空", result.isEmpty());
        assertEquals("应包含两个键", 2, result.keySet().size());
        assertTrue("应包含键 1", result.containsKey(1L));
        assertTrue("应包含键 2", result.containsKey(2L));
    }


    /**
     * 测试 queryDistributorGroupAttrMap 方法，数据库返回空列表
     */
    @Test
    public void testQueryDistributorGroupAttrMapDbReturnsEmpty() {
        // arrange
        List<Long> distributorGroupIdList = Arrays.asList(1L, 2L);
        when(distributorGroupAttrMapper.selectByExample(any(DistributorGroupAttrCriteria.class))).thenReturn(Arrays.asList());
        // act
        Map<Long, List<DistributorGroupAttrBO>> result = distributorGroupRepositoryImpl.queryDistributorGroupAttrMap(distributorGroupIdList);
        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 queryDistributorGroupAttrMap 方法，转换过程中发生异常
     */
    @Test(expected = RuntimeException.class)
    public void testQueryDistributorGroupAttrMapConversionThrowsException() {
        // arrange
        List<Long> distributorGroupIdList = Arrays.asList(1L, 2L);
        DistributorGroupAttr distributorGroupAttr1 = new DistributorGroupAttr();
        distributorGroupAttr1.setGroupId(1);
        List<DistributorGroupAttr> distributorGroupAttrList = Arrays.asList(distributorGroupAttr1);
        when(distributorGroupAttrMapper.selectByExample(any(DistributorGroupAttrCriteria.class))).thenReturn(distributorGroupAttrList);
        when(distributorGroupAttrConverter.toEntity(any(DistributorGroupAttr.class))).thenThrow(new RuntimeException("Conversion error"));
        // act
        distributorGroupRepositoryImpl.queryDistributorGroupAttrMap(distributorGroupIdList);
        // assert is handled by the expected exception
    }

    /**
     * 测试 queryDistributorGroupAttrMap 方法，输入列表包含重复值
     */
    @Test
    public void testQueryDistributorGroupAttrMapWithDuplicateValues() {
        // arrange
        List<Long> distributorGroupIdList = Arrays.asList(1L, 1L);
        DistributorGroupAttr distributorGroupAttr1 = new DistributorGroupAttr();
        distributorGroupAttr1.setGroupId(1);
        List<DistributorGroupAttr> distributorGroupAttrList = Arrays.asList(distributorGroupAttr1, distributorGroupAttr1);
        when(distributorGroupAttrMapper.selectByExample(any(DistributorGroupAttrCriteria.class))).thenReturn(distributorGroupAttrList);
        when(distributorGroupAttrConverter.toEntity(any(DistributorGroupAttr.class))).thenAnswer(invocation -> {
            DistributorGroupAttr attr = invocation.getArgument(0);
            return new DistributorGroupAttrBO(null, attr.getGroupId(), null, null, null, DistributionStatusEnum.VALID);
        });
        // act
        Map<Long, List<DistributorGroupAttrBO>> result = distributorGroupRepositoryImpl.queryDistributorGroupAttrMap(distributorGroupIdList);
        // assert
        assertFalse("结果不应为空", result.isEmpty());
        assertEquals("应包含一个键", 1, result.keySet().size());
        assertEquals("键 1 应对应两个元素", 2, result.get(1L).size());
    }

    /**
     * 测试查询分销商绑定关系，结果为空
     */
    @Test
    public void testQueryDistributorGroupBind_ReturnsNull() {
        // arrange
        when(distributorGroupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(new ArrayList<>());
        // act
        DistributorGroupBindBO result = distributorGroupRepository.queryDistributorGroupBind(distributorId, statusList);
        // assert
        assertNull(result);
    }



    /**
     * 测试查询分销商绑定关系，结果为多个绑定关系，抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryDistributorGroupBind_ThrowsExceptionForMultipleBinds() {
        // arrange
        List<DistributorGroupBind> binds = new ArrayList<>();
        binds.add(new DistributorGroupBind(1L, 1, distributorId, 1, null, null));
        binds.add(new DistributorGroupBind(2L, 2, distributorId, 2, null, null));
        when(distributorGroupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(binds);
        // act
        distributorGroupRepository.queryDistributorGroupBind(distributorId, statusList);
        // assert is handled by the expected exception
    }

    /**
     * 测试查询分销商绑定关系，传入空状态列表
     */
    @Test
    public void testQueryDistributorGroupBind_EmptyStatusList() {
        // arrange
        when(distributorGroupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(new ArrayList<>());
        // act
        DistributorGroupBindBO result = distributorGroupRepository.queryDistributorGroupBind(distributorId, new ArrayList<>());
        // assert
        assertNull(result);
    }
}
