package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.handler;

import com.dianping.gmkt.event.api.rebate.dto.RebateSettleConditionDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.dianping.gmkt.event.api.rebate.enums.RebateSettleConditionKey;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateAmountOrderDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.meituan.beauty.idmapper.service.DealMapperService;
import com.meituan.mpmkt.data.pool.api.dto.DataPoolResponse;
import com.meituan.mpmkt.data.pool.api.service.DataPoolImportQueryService;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class RebateAmountOrderPriceProportionCalcHandlerTest {

    @InjectMocks
    private RebateAmountOrderPriceProportionCalcHandler handler;

    @Mock
    private DistributorActivityService distributorActivityService;

    @Mock
    private DataPoolImportQueryService dataPoolImportQueryService;

    @Mock
    private DealMapperService dealMapperService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试calculate方法，当CommonResponse返回成功且数据非空时
     */
    @Test
    public void testCalculateSuccessWithData() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");
        request.setPayCent(1000);
        request.setOrderVerifyTimes(1);

        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleDTO = new RebateSettleRuleDTO();
        ruleDTO.setCondition(Lists.newArrayList());
        rebateActivityRuleDTO.setRule(ruleDTO);

        RebateAmountOrderDTO rebateAmountOrderDTO = new RebateAmountOrderDTO();
        rebateAmountOrderDTO.setOrderId("orderId");
        rebateAmountOrderDTO.setOrderPrice(1000L);
        rebateAmountOrderDTO.setRebateAmount(100L);
        rebateAmountOrderDTO.setReason("Test Reason");

        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(CommonResponse.CODE_SUCCESS);
        commonResponse.setData(Collections.singletonList(rebateAmountOrderDTO));

        when(distributorActivityService.calculateOrderRebateAmount(any())).thenReturn(commonResponse);

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals("orderId", response.getData().getOrderId());
        assertEquals(100L, response.getData().getRebateAmount());
        assertEquals("Test Reason", response.getData().getReason());
    }

    /**
     * 测试calculate方法，当CommonResponse返回失败时
     */
    @Test
    public void testCalculateFailure() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleDTO = new RebateSettleRuleDTO();
        ruleDTO.setCondition(Lists.newArrayList());
        rebateActivityRuleDTO.setRule(ruleDTO);

        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(CommonResponse.CODE_BIZ_ERROR);
        commonResponse.setMsg("Business Error");

        when(distributorActivityService.calculateOrderRebateAmount(any())).thenReturn(commonResponse);

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertFalse(response.isSuccess());
        assertEquals("Business Error", response.getMsg());
    }

    /**
     * 测试calculate方法，当CommonResponse返回成功但数据为空时
     */
    @Test
    public void testCalculateSuccessWithEmptyData() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleDTO = new RebateSettleRuleDTO();
        ruleDTO.setCondition(Lists.newArrayList());
        rebateActivityRuleDTO.setRule(ruleDTO);

        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(CommonResponse.CODE_SUCCESS);
        commonResponse.setData(Collections.emptyList());

        when(distributorActivityService.calculateOrderRebateAmount(any())).thenReturn(commonResponse);

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertTrue(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * 测试calculate方法，当团单不在楼层范围内时
     */
    @Test
    public void testCalculateDealItemNotInScope() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");
        request.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        request.setPayCent(1000);
        request.setProductId(123);

        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleConditionDTO conditionDTO = new RebateSettleConditionDTO();
        conditionDTO.setKey(RebateSettleConditionKey.DEAL_ITEM_ID.code);
        conditionDTO.setValue("456");
        rebateActivityRuleDTO.setRule(new RebateSettleRuleDTO());
        rebateActivityRuleDTO.getRule().setCondition(Collections.singletonList(conditionDTO));

        DataPoolResponse<Long> dataPoolResponse = new DataPoolResponse<>();
        dataPoolResponse.setData(0L);

        when(dataPoolImportQueryService.queryTargetDataCount(any())).thenReturn(dataPoolResponse);

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals("orderId", response.getData().getOrderId());
        assertEquals("团单不在楼层范围内", response.getData().getReason());
    }


}
