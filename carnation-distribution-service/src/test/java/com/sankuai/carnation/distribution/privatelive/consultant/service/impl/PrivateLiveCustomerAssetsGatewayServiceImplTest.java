package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.intention.PrivateLiveIntentionAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl.PrivateLiveCustomerAssetsGatewayServiceImpl;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalInfo;
import com.sankuai.dzusergrowth.growth.privatelive.api.response.PrivateLiveConsultantCommDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Maps;

import javax.validation.constraints.AssertTrue;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试PrivateLiveCustomerAssetsGatewayServiceImpl的pageList方法
 */
public class PrivateLiveCustomerAssetsGatewayServiceImplTest {

    @InjectMocks
    private PrivateLiveCustomerAssetsGatewayServiceImpl privateLiveCustomerAssetsGatewayService;

    @Mock
    private UserAuthorizeUtil userAuthorizeUtil;

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;

    @Mock
    private PrivateLiveIntentionAclService privateLiveIntentionAclService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testIntentionCommNum() {
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("wxToken");
        request.setSearchKeyWord("");
        PageRemoteResponse<CustomerCapitalInfo> pageRemoteResponse = buildRemoteResponse();
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setConsultantId(1L);
        Map<Long, PrivateLiveConsultantCommDTO> commMap = Maps.newHashMap();
        commMap.put(1L, new PrivateLiveConsultantCommDTO());
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(dto);
        when(privateSphereUserAclService.queryCustomerCapitalListByTag(any())).thenReturn(CompletableFuture.completedFuture(pageRemoteResponse));
        when(privateLiveIntentionAclService.queryConsultantCommInfo(any())).thenReturn(CompletableFuture.completedFuture(commMap));
        PaginationRemoteResponse<CustomerAssetsDTO> result = privateLiveCustomerAssetsGatewayService.pageList(request);
        assertNotNull(result);
    }

    private PageRemoteResponse<CustomerCapitalInfo> buildRemoteResponse() {
        CustomerCapitalInfo customerCapitalInfo = new CustomerCapitalInfo();
        customerCapitalInfo.setMtUserId(1L);
        PageRemoteResponse<CustomerCapitalInfo> pageRemoteResponse = new PageRemoteResponse<>();
        pageRemoteResponse.setCode(200);
        pageRemoteResponse.setData(Lists.newArrayList(customerCapitalInfo));
        pageRemoteResponse.setTotalHit(1L);
        return pageRemoteResponse;

    }

    /**
     * 测试pageList方法当服务响应为null时
     *
     * @throws Throwable
     */
    @Test
    public void testPageListWhenResponseIsNull() throws Throwable {

        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("wxToken");
        request.setSearchKeyWord("keyword");
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(null);
        PaginationRemoteResponse<CustomerAssetsDTO> result = privateLiveCustomerAssetsGatewayService.pageList(request);
        assertNotNull(result);
    }

    @Test
    public void testTabListWhenAuthenticationIsNull() throws Throwable {
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(null);
        RemoteResponse<List<CommonFilterItemDTO>> res = privateLiveCustomerAssetsGatewayService.tabList(1L, "liveId", "wxToken");
        assertTrue(res.getData().isEmpty());
    }

    @Test
    public void testLiveMessageWhenAuthenticationIsNull() throws Throwable {
        UserPortraitRequest request = new UserPortraitRequest();
        request.setWxToken("keyword");
        request.setTaskId(1L);
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(null);
        RemoteResponse<List<LiveMessageDTO>> res = privateLiveCustomerAssetsGatewayService.liveMessage(request);
        assertNull(res.getData());
    }

    @Test
    public void testUserPortraitInfoWhenAuthenticationIsNull() throws Throwable {
        UserPortraitRequest request = new UserPortraitRequest();
        request.setWxToken("keyword");
        request.setTaskId(1L);
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(null);
        RemoteResponse<UserPortraitDTO> res = privateLiveCustomerAssetsGatewayService.userPortraitInfo(request);
        assertNull(res.getData());
    }

    /**
     * 测试pageList方法当用户鉴权信息不为空且搜索关键字为空时
     *
     * @throws Throwable
     */
    @Test
    public void testPageListWhenUserAuthNotNullAndSearchKeyWordIsEmpty() throws Throwable {
        // arrange
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("wxToken");
        request.setSearchKeyWord("");
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(dto);
        when(privateSphereUserAclService.queryCustomerCapitalListByTag(any())).thenReturn(CompletableFuture.completedFuture(null));
        PaginationRemoteResponse<CustomerAssetsDTO> result = privateLiveCustomerAssetsGatewayService.pageList(request);
        assertNull(result);
    }

    /**
     * 测试pageList方法当用户鉴权信息不为空且搜索关键字不为空时
     *
     * @throws Throwable
     */
    @Test
    public void testPageListWhenUserAuthNotNullAndSearchKeyWordIsNotEmpty() throws Throwable {
        // arrange
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("wxToken");
        request.setSearchKeyWord("keyword");
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(dto);

        // act
        PaginationRemoteResponse<CustomerAssetsDTO> result = privateLiveCustomerAssetsGatewayService.pageList(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试pageList方法当发生异常时
     * @throws Throwable
     */
    /*@Test
    public void testPageListWhenExceptionOccurs() throws Throwable {
        // arrange
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("wxToken");
        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenThrow(new RuntimeException("Test Exception"));
        // act
        PaginationRemoteResponse<CustomerAssetsDTO> customerAssetsDTOPaginationRemoteResponse = privateLiveCustomerAssetsGatewayService.pageList(request);
        assertNull(customerAssetsDTOPaginationRemoteResponse);
    }*/
}
