package com.sankuai.carnation.distribution.distributor.service.impl;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.appication.DistributorAppService;
import com.sankuai.carnation.distribution.distributor.assembler.DistributorVOAssembler;
import com.sankuai.carnation.distribution.distributor.dto.request.PageQueryDistributorRequest;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindModel;
import com.sankuai.carnation.distribution.distributor.vo.ApplyDistributorVO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class DistributorGroupQueryServiceImplTest {

    @InjectMocks
    private DistributorGroupQueryServiceImpl service;

    @Mock
    private DistributorAppService distributorAppService;

    @Mock
    private DistributorVOAssembler distributorVOAssembler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试团长ID为空的情况
     */
    @Test(expected = BizSceneException.class)
    public void testGetGroupMemberList_DistributorGroupIdIsNull() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(null, 1, 1, 10, false, "wxToken");
        service.getGroupMemberList(request);
    }

    /**
     * 测试页码为空或小于等于0的情况
     */
    @Test(expected = BizSceneException.class)
    public void testGetGroupMemberList_PageNumIsInvalid() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 0, 10, false, "wxToken");
        service.getGroupMemberList(request);
    }

    /**
     * 测试每页数量为空或小于等于0的情况
     */
    @Test(expected = BizSceneException.class)
    public void testGetGroupMemberList_PageSizeIsInvalid() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 1, 0, false, "wxToken");
        service.getGroupMemberList(request);
    }

    /**
     * 测试每页数量大于20的情况
     */
    @Test(expected = BizSceneException.class)
    public void testGetGroupMemberList_PageSizeIsGreaterThan20() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 1, 21, false, "wxToken");
        service.getGroupMemberList(request);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testGetGroupMemberList_Success() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 1, 10, false, "wxToken");
        PageDataDTO<DistributorBindModel> distributorVOPageDataDTO = new PageDataDTO<>();
        distributorVOPageDataDTO.setList(Lists.newArrayList(DistributorBindModel.builder().build()));
        when(distributorAppService.pageQueryDistributor(any(PageQueryDistributorRequest.class))).thenReturn(distributorVOPageDataDTO);
        when(distributorVOAssembler.toApplyDistributorVOList(anyList())).thenReturn(Lists.newArrayList(ApplyDistributorVO.builder()
                .groupId("1")
                .build()));

        RemoteResponse<PageDataDTO<ApplyDistributorVO>> response = service.getGroupMemberList(request);

        assertNotNull(response);
        assertNotNull(response.getData());
        assertNotNull(response.getData().getList());
        assertFalse(response.getData().getList().isEmpty());
        verify(distributorAppService, times(1)).pageQueryDistributor(any(PageQueryDistributorRequest.class));
        verify(distributorVOAssembler, times(1)).toApplyDistributorVOList(anyList());
    }
}
