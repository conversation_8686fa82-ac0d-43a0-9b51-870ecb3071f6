package com.sankuai.carnation.distribution.intention.domain.calculate.mq.order.rerun;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.carnation.distribution.intention.dto.OrderChannelRerunDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;

public class OrderChannelTaskRerunNotifyProducerTest {

    @InjectMocks
    private OrderChannelTaskRerunNotifyProducer orderChannelTaskRerunNotifyProducer;

    @Mock
    private IProducerProcessor delayProducer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试sendDelayMessage方法，边界情况
     */
    @Test
    public void testSendDelayMessageBoundary() throws Exception {
        // arrange
        OrderChannelRerunDTO orderChannelRerunDTO = null;
        long delayTime = 1000L;
        // act
        orderChannelTaskRerunNotifyProducer.sendDelayMessage(orderChannelRerunDTO, delayTime);
        // assert
        verify(delayProducer, times(1)).sendDelayMessage(anyString(), eq(delayTime));
    }
}
