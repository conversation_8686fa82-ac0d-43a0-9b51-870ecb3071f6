package com.sankuai.carnation.distribution.product.v2.repository.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceGetDeactivatedListTest {

    private ProductItemSaleUnitDataService productItemSaleUnitDataService = new ProductItemSaleUnitDataService();

    private Method getDeactivatedListMethod;

    @Before
    public void setUp() throws Exception {
        getDeactivatedListMethod = ProductItemSaleUnitDataService.class.getDeclaredMethod("getDeactivatedList", List.class, Map.class);
        getDeactivatedListMethod.setAccessible(true);
    }

    private ProductItemSaleUnit createMockPo() {
        ProductItemSaleUnit po = new ProductItemSaleUnit();
        // Assuming setting a unique ID is necessary for the business key
        po.setId(1L);
        po.setProductItemId(1L);
        po.setProductType(1);
        po.setProductId(1L);
        po.setSkuId(1L);
        po.setStatus(1);
        return po;
    }

    private List<ProductItemSaleUnit> invokePrivateMethod(String methodName, List<ProductItemSaleUnit> updateList, Map<String, ProductItemSaleUnit> formerMap) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod(methodName, List.class, Map.class);
        method.setAccessible(true);
        return (List<ProductItemSaleUnit>) method.invoke(productItemSaleUnitDataService, updateList, formerMap);
    }

    private String invokeBuildPoBusinessUniqueKey(ProductItemSaleUnit unit) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("buildPoBusinessUniqueKey", ProductItemSaleUnit.class);
        method.setAccessible(true);
        return (String) method.invoke(productItemSaleUnitDataService, unit);
    }

    private ProductItemSaleUnit createValidProductItemSaleUnit() {
        ProductItemSaleUnit unit = new ProductItemSaleUnit();
        unit.setStatus(1);
        unit.setId(1L);
        unit.setProductItemId(1L);
        unit.setProductType(1);
        unit.setProductId(1L);
        unit.setSkuId(1L);
        return unit;
    }

    @Test
    public void testGetDeactivatedListWhenUpdateListIsEmpty() throws Throwable {
        List<ProductItemSaleUnit> updateList = Arrays.asList();
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        List<ProductItemSaleUnit> result = (List<ProductItemSaleUnit>) getDeactivatedListMethod.invoke(productItemSaleUnitDataService, updateList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDeactivatedListWhenFormerMapIsEmpty() throws Throwable {
        ProductItemSaleUnit updatePo = createMockPo();
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        List<ProductItemSaleUnit> result = (List<ProductItemSaleUnit>) getDeactivatedListMethod.invoke(productItemSaleUnitDataService, Arrays.asList(updatePo), formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetDeactivatedListWhenUpdateListAndFormerMapAreNotEmptyButNoElementIsDeactivated() throws Throwable {
        ProductItemSaleUnit newPo = createMockPo();
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        // Correct key
        formerMap.put("1_1_1_1", newPo);
        ProductItemSaleUnit updatePo = createMockPo();
        List<ProductItemSaleUnit> result = (List<ProductItemSaleUnit>) getDeactivatedListMethod.invoke(productItemSaleUnitDataService, Arrays.asList(updatePo), formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetReactivatedListWithReactivation() throws Throwable {
        ProductItemSaleUnit newPo = createValidProductItemSaleUnit();
        List<ProductItemSaleUnit> updateList = Arrays.asList(newPo);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        ProductItemSaleUnit oldPo = createValidProductItemSaleUnit();
        oldPo.setStatus(2);
        // Ensure the key matches the key generated by buildPoBusinessUniqueKey
        String key = invokeBuildPoBusinessUniqueKey(oldPo);
        formerMap.put(key, oldPo);
        List<ProductItemSaleUnit> result = invokePrivateMethod("getReactivatedList", updateList, formerMap);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetReactivatedListBothEmpty() throws Throwable {
        List<ProductItemSaleUnit> updateList = Arrays.asList();
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        List<ProductItemSaleUnit> result = invokePrivateMethod("getReactivatedList", updateList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetReactivatedListUpdateEmpty() throws Throwable {
        List<ProductItemSaleUnit> updateList = Arrays.asList();
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        formerMap.put("key", createValidProductItemSaleUnit());
        List<ProductItemSaleUnit> result = invokePrivateMethod("getReactivatedList", updateList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetReactivatedListFormerMapEmpty() throws Throwable {
        ProductItemSaleUnit newPo = createValidProductItemSaleUnit();
        List<ProductItemSaleUnit> updateList = Arrays.asList(newPo);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        List<ProductItemSaleUnit> result = invokePrivateMethod("getReactivatedList", updateList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetReactivatedListNoReactivation() throws Throwable {
        ProductItemSaleUnit newPo = createValidProductItemSaleUnit();
        List<ProductItemSaleUnit> updateList = Arrays.asList(newPo);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        formerMap.put("key", createValidProductItemSaleUnit());
        List<ProductItemSaleUnit> result = invokePrivateMethod("getReactivatedList", updateList, formerMap);
        assertEquals(0, result.size());
    }
}
