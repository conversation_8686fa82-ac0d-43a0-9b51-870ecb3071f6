package com.sankuai.carnation.distribution.privatelive.distribution.eums;

import org.junit.Assert;
import org.junit.Test;

public class PrivateLiveAccountTypeEnumTest {

    /**
     * 测试getByCode方法，输入已知code 1，期望返回CONSULTANT
     */
    @Test
    public void testGetByCodeWithKnownCodeReturnsConsultant() {
        // arrange
        Integer code = 1;

        // act
        PrivateLiveAccountTypeEnum result = PrivateLiveAccountTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveAccountTypeEnum.CONSULTANT, result);
    }

    /**
     * 测试getByCode方法，输入已知code 2，期望返回DISTRIBUTOR_GROUP
     */
    @Test
    public void testGetByCodeWithKnownCodeReturnsDistributorGroup() {
        // arrange
        Integer code = 2;

        // act
        PrivateLiveAccountTypeEnum result = PrivateLiveAccountTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP, result);
    }

    /**
     * 测试getByCode方法，输入已知code 3，期望返回DISTRIBUTOR
     */
    @Test
    public void testGetByCodeWithKnownCodeReturnsDistributor() {
        // arrange
        Integer code = 3;

        // act
        PrivateLiveAccountTypeEnum result = PrivateLiveAccountTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveAccountTypeEnum.DISTRIBUTOR, result);
    }

    /**
     * 测试getByCode方法，输入未知code 4，期望返回UNKNOWN
     */
    @Test
    public void testGetByCodeWithUnknownCodeReturnsUnknown() {
        // arrange
        Integer code = 4;

        // act
        PrivateLiveAccountTypeEnum result = PrivateLiveAccountTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveAccountTypeEnum.UNKNOWN, result);
    }

    /**
     * 测试getByCode方法，输入null，期望返回UNKNOWN
     */
    @Test
    public void testGetByCodeWithNullCodeReturnsUnknown() {
        // arrange
        Integer code = null;

        // act
        PrivateLiveAccountTypeEnum result = PrivateLiveAccountTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveAccountTypeEnum.UNKNOWN, result);
    }
}
