package com.sankuai.carnation.distribution.distributor.domain;

import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.exception.DistributorException;
import com.sankuai.carnation.distribution.distributor.domain.utils.BizDistributorBeanTransfer;
import com.sankuai.carnation.distribution.distributor.dto.request.BizDistributorOperateRequest;
import com.sankuai.carnation.distribution.distributor.enums.DistributorBindUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.repository.dao.BizDistributorMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupBindMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorMapper;
import com.sankuai.carnation.distribution.distributor.repository.db.*;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DistributorRootServiceTest {

    @Mock
    private BizDistributorMapper bizDistributorMapper;

    @InjectMocks
    private DistributorRootService distributorRootService;

    private BizDistributorOperateRequest request;

    private BizDistributor bizDistributor;

    private BizDistributorBO expectedBO;

    @Mock
    private DistributorMapper distributorMapper;

    @Mock
    private DistributorGroupBindMapper groupBindMapper;

    @Before
    public void setUp() {
        request = new BizDistributorOperateRequest();
        bizDistributor = new BizDistributor(1L, 1, "bizId", "userId", "distributorCode", 1, null, 1);
        expectedBO = BizDistributorBeanTransfer.po2bo(bizDistributor);
    }

    /**
     * 测试getBizDistributor方法，当请求参数全为空时，应返回空列表
     */
    @Test
    public void testGetBizDistributorWithEmptyRequest() {
        // arrange
        request.setUserId("");
        request.setUserType(null);
        request.setDistributorCode("");
        request.setBizId("");
        // act
        List<BizDistributorBO> result = distributorRootService.getBizDistributor(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试getBizDistributor方法，当请求参数有效时，应返回非空列表
     */
    @Test
    public void testGetBizDistributorWithValidRequest() {
        // arrange
        request.setUserId("userId");
        request.setUserType(1);
        request.setDistributorCode("distributorCode");
        request.setBizId("bizId");
        when(bizDistributorMapper.selectByExample(any(BizDistributorExample.class))).thenReturn(Arrays.asList(bizDistributor));
        // act
        List<BizDistributorBO> result = distributorRootService.getBizDistributor(request);
        // assert
        assertFalse(result.isEmpty());
        assertEquals(expectedBO, result.get(0));
    }

    /**
     * 测试getBizDistributor方法，当数据库返回空列表时，应返回空列表
     */
    @Test
    public void testGetBizDistributorWithEmptyDBResponse() {
        // arrange
        request.setUserId("userId");
        when(bizDistributorMapper.selectByExample(any(BizDistributorExample.class))).thenReturn(Arrays.asList());
        // act
        List<BizDistributorBO> result = distributorRootService.getBizDistributor(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 getBizDistributorTotal 方法，当所有参数为空时应返回0
     */
    @Test
    public void testGetBizDistributorTotalAllParamsNull() {
        BizDistributorOperateRequest request = new BizDistributorOperateRequest();
        Long result = distributorRootService.getBizDistributorTotal(request);
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * 测试 getBizDistributorTotal 方法，当 userId 和 userType 非空，其他参数为空时
     */
    @Test
    public void testGetBizDistributorTotalWithUserIdAndUserType() {
        BizDistributorOperateRequest request = new BizDistributorOperateRequest();
        request.setUserId("testUserId");
        request.setUserType(1);
        when(bizDistributorMapper.countByExample(any(BizDistributorExample.class))).thenReturn(1L);
        Long result = distributorRootService.getBizDistributorTotal(request);
        assertEquals(Long.valueOf(1), result);
    }

    /**
     * 测试 getBizDistributorTotal 方法，当 distributorCode 非空时
     */
    @Test
    public void testGetBizDistributorTotalWithDistributorCode() {
        BizDistributorOperateRequest request = new BizDistributorOperateRequest();
        request.setDistributorCode("testCode");
        when(bizDistributorMapper.countByExample(any(BizDistributorExample.class))).thenReturn(1L);
        Long result = distributorRootService.getBizDistributorTotal(request);
        assertEquals(Long.valueOf(1), result);
    }





    /**
     * 测试有效分销商存在的情况
     */
    @Test
    public void testGetValidDistributor_DistributorExists() {
        // arrange
        int userType = 1;
        long userId = 100L;
        Distributor distributor = new Distributor();
        distributor.setId(1L);
        distributor.setUserType(userType);
        distributor.setUserId(userId);
        distributor.setStatus(DistributionStatusEnum.VALID.getCode());
        when(distributorMapper.selectByExample(any(DistributorCriteria.class))).thenReturn(Collections.singletonList(distributor));
        // act
        DistributorBO result = distributorRootService.getValidDistributor(userType, userId);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1L), result.getDistributorId());
    }

    /**
     * 测试用户类型未知的情况
     */
    @Test
    public void testGetValidDistributor_UnknownUserType() {
        // arrange
        int userType = DistributorBindUserTypeEnum.UNKNOWN.getCode();
        long userId = 100L;
        // act
        DistributorBO result = distributorRootService.getValidDistributor(userType, userId);
        // assert
        assertNull(result);
    }

    /**
     * 测试用户ID无效的情况
     */
    @Test
    public void testGetValidDistributor_InvalidUserId() {
        // arrange
        int userType = 1;
        long userId = -1L;
        // act
        DistributorBO result = distributorRootService.getValidDistributor(userType, userId);
        // assert
        assertNull(result);
    }

    /**
     * 测试分销商不存在的情况
     */
    @Test
    public void testGetValidDistributor_DistributorNotExists() {
        // arrange
        int userType = 1;
        long userId = 100L;
        when(distributorMapper.selectByExample(any(DistributorCriteria.class))).thenReturn(Collections.emptyList());
        // act
        DistributorBO result = distributorRootService.getValidDistributor(userType, userId);
        // assert
        assertNull(result);
    }

    /**
     * 测试setDistributorWithAudit方法，当BO为null时
     */
    @Test
    public void testSetDistributorWithAuditWhenBoIsNull() {
        long result = distributorRootService.setDistributorWithAudit(null, DistributionApproveStatusEnum.PASS);
        assertEquals("当BO为null时，应返回0", 0L, result);
    }

    /**
     * 测试setDistributorWithAudit方法，当BO的distributorId、distributorCode和userId都为空时
     */
    @Test(expected = DistributorException.class)
    public void testSetDistributorWithAuditWhenKeysAreMissing() {
        DistributorBO bo = new DistributorBO();
        distributorRootService.setDistributorWithAudit(bo, DistributionApproveStatusEnum.PASS);
    }

    /**
     * 测试setDistributorWithAudit方法，当BO的distributorId有效时
     */
    @Test
    public void testSetDistributorWithAuditWhenDistributorIdIsValid() {
        DistributorBO bo = new DistributorBO();
        bo.setDistributorId(1L);
        bo.setStatus(1);
        Distributor distributor = new Distributor();
        distributor.setId(1L);
        when(distributorMapper.updateByPrimaryKeySelective(any(Distributor.class))).thenReturn(1);
        long result = distributorRootService.setDistributorWithAudit(bo, DistributionApproveStatusEnum.PASS);
        assertNotEquals("当distributorId有效时，应返回非0值", 0L, result);
    }

    /**
     * 测试setDistributorWithAudit方法，当BO的distributorCode有效时
     */
    @Test
    public void testSetDistributorWithAuditWhenDistributorCodeIsValid() {
        DistributorBO bo = new DistributorBO();
        bo.setDistributorCode("validCode");
        bo.setStatus(1);
        Distributor distributor = new Distributor();
        distributor.setId(1L);
        distributor.setDistributorCode("validCode");
        when(distributorMapper.selectByExample(any())).thenReturn(java.util.Collections.singletonList(distributor));
        when(distributorMapper.updateByPrimaryKeySelective(any(Distributor.class))).thenReturn(1);
        long result = distributorRootService.setDistributorWithAudit(bo, DistributionApproveStatusEnum.PASS);
        assertNotEquals("当distributorCode有效时，应返回非0值", 0L, result);
    }

    /**
     * 测试setDistributorWithAudit方法，当BO的userType和userId有效时
     */
    @Test
    public void testSetDistributorWithAuditWhenUserTypeAndUserIdAreValid() {
        DistributorBO bo = new DistributorBO();
        bo.setUserType(1);
        bo.setUserId(1L);
        bo.setStatus(1);
        Distributor distributor = new Distributor();
        distributor.setId(1L);
        distributor.setUserType(1);
        distributor.setUserId(1L);
        when(distributorMapper.selectByExample(any())).thenReturn(java.util.Collections.singletonList(distributor));
        when(distributorMapper.updateByPrimaryKeySelective(any(Distributor.class))).thenReturn(1);
        long result = distributorRootService.setDistributorWithAudit(bo, DistributionApproveStatusEnum.PASS);
        assertNotEquals("当userType和userId有效时，应返回非0值", 0L, result);
    }

    /**
     * 测试分销员ID为null的情况
     */
    @Test
    public void testGetDistributorGroupBind_DistributorIdIsNull() {
        DistributorGroupBind result = distributorRootService.getDistributorGroupBind(null);
        assertNull("当分销员ID为null时，应返回null", result);
    }

    /**
     * 测试分销员ID小于等于0的情况
     */
    @Test
    public void testGetDistributorGroupBind_DistributorIdIsInvalid() {
        DistributorGroupBind result = distributorRootService.getDistributorGroupBind(-1L);
        assertNull("当分销员ID小于等于0时，应返回null", result);
    }

    /**
     * 测试分销员绑定分销商列表为空的情况
     */
    @Test
    public void testGetDistributorGroupBind_BindListIsEmpty() {
        when(groupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(Collections.emptyList());
        DistributorGroupBind result = distributorRootService.getDistributorGroupBind(1L);
        assertNull("当分销员绑定分销商列表为空时，应返回null", result);
    }

    /**
     * 测试分销员绑定分销商成功的情况
     */
    @Test
    public void testGetDistributorGroupBind_Success() {
        DistributorGroupBind expectedBind = new DistributorGroupBind(1L, 1, 1L, 1, null, null);
        when(groupBindMapper.selectByExample(any(DistributorGroupBindCriteria.class))).thenReturn(Arrays.asList(expectedBind));
        DistributorGroupBind result = distributorRootService.getDistributorGroupBind(1L);
        assertNotNull("当分销员绑定分销商成功时，不应返回null", result);
        assertEquals("返回的分销员绑定分销商信息应与预期相符", expectedBind, result);
    }
}
