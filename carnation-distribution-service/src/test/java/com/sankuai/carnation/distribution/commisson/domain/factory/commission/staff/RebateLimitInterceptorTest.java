package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleLimitRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.limit.Interceptor.RebateRuleLimitInterceptor;
import com.sankuai.carnation.distribution.commisson.limit.RebateRuleLimitInterface;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.sankuai.carnation.distribution.commisson.repository.RebateLimitRepository;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Iterator;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * @description : 用于测试返利规则限制
 * @date : 2024/7/31
 */
@RunWith(MockitoJUnitRunner.class)
public class RebateLimitInterceptorTest {
    @InjectMocks
    private RebateRuleLimitInterceptor rebateRuleLimitInterceptor;

    @Mock
    private DistributorActivityService distributorActivityService;

    @Mock
    private RebateLimitRepository rebateLimitRepository;

    @Mock
    private List<RebateRuleLimitInterface> limitInterfaceList;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试 RebateAmountResult 为 null 的情况
     */
    @Test
    public void testHandleRebateRuleLimit_RebateAmountResultIsNull() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();

        RebateAmountResult result = rebateRuleLimitInterceptor.handleRebateRuleLimit(request, null);

        assertNull("返利规则在处理空的返利金额数据的时候 应为 null", result);
    }

    /**
     * 测试 RebateActivityRuleDTO 为 null 的情况
     */
    @Test
    public void testHandleRebateRuleLimit_RebateActivityRuleIsNull() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setTechApplyRecordId(1L);
        RebateAmountResult rebateAmountResult = new RebateAmountResult();
        rebateAmountResult.setRebateAmount(100L);

        when(distributorActivityService.queryActivityRule(anyLong())).thenReturn(new CommonResponse<>());

        RebateAmountResult result = rebateRuleLimitInterceptor.handleRebateRuleLimit(request, rebateAmountResult);

        assertNotNull("RebateAmountResult 不应为 null", result);
        verify(distributorActivityService, times(1)).queryActivityRule(anyLong());
        assertEquals("返利金额应为 100", 100L, result.getRebateAmount());

    }

    /**
     * 测试 RebateLimitResult.isResult() 为 true 命中活动限制的情况
     */
    @Test
    public void testHandleRebateRuleLimit_RebateLimitResultIsTrue() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setTechApplyRecordId(1L);
        RebateAmountResult rebateAmountResult = new RebateAmountResult();
        rebateAmountResult.setRebateAmount(100L);

        RebateActivityRuleDTO ruleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rule.setRebateSettleLimitRule(new RebateSettleLimitRuleDTO());
        ruleDTO.setRule(rule);
        CommonResponse<RebateActivityRuleDTO> response = new CommonResponse<>();
        response.setData(ruleDTO);

        when(distributorActivityService.queryActivityRule(anyLong())).thenReturn(response);
        when(rebateLimitRepository.saveOrderInfo(any())).thenReturn(1);
        when(limitInterfaceList.iterator()).thenReturn(getExpectLimitHandler());  //返回一个命中返利规则的固定返利金额为50的处理类


        RebateAmountResult result = rebateRuleLimitInterceptor.handleRebateRuleLimit(request, rebateAmountResult);

        assertNotNull("RebateAmountResult 不应为 null", result);
        assertEquals("返利金额被应为 50", 50L, result.getRebateAmount());
        verify(distributorActivityService, times(1)).queryActivityRule(anyLong());
        verify(rebateLimitRepository, times(1)).saveOrderInfo(any());
    }

    @NotNull
    private static Iterator<RebateRuleLimitInterface> getExpectLimitHandler() {
        return new Iterator<RebateRuleLimitInterface>() {
            private int index = 0;

            @Override
            public boolean hasNext() {
                if (index == 0) {
                    index++;
                    return true;
                } else {
                    return false;
                }
            }

            @Override
            public RebateRuleLimitInterface next() {
                return new RebateRuleLimitInterface() {
                    @Override
                    public RebateLimitResult handleResult(CommissionVerifyCalculationRequest request, RebateAmountResult rebateAmountResult, RebateActivityRuleDTO rule) {
                        RebateLimitResult limitResult = new RebateLimitResult();
                        limitResult.setResult(true);
                        limitResult.setRebateAmount(50L);
                        return limitResult;
                    }

                    @Override
                    public void sendMessage(CommissionVerifyCalculationRequest request, RebateLimitResult result) {
                    }
                };
            }
        };
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testHandleRebateRuleLimit_Exception() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setTechApplyRecordId(1L);
        RebateAmountResult rebateAmountResult = new RebateAmountResult();

        when(distributorActivityService.queryActivityRule(anyLong())).thenThrow(new RuntimeException());

        RebateAmountResult result = rebateRuleLimitInterceptor.handleRebateRuleLimit(request, rebateAmountResult);

        assertNotNull("RebateAmountResult 不应为 null", result);
        verify(distributorActivityService, times(1)).queryActivityRule(anyLong());
    }
}
