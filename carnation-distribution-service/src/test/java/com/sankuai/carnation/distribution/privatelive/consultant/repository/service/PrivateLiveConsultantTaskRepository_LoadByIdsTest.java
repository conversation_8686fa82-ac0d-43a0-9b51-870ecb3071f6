package com.sankuai.carnation.distribution.privatelive.consultant.repository.service;

import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantTaskExample;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantTaskMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantTaskRepository_LoadByIdsTest {

    @InjectMocks
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private PrivateLiveConsultantTaskMapper privateLiveConsultantTaskMapper;

    private List<PrivateLiveConsultantTask> privateLiveConsultantTasks;

    @Before
    public void setUp() {
        privateLiveConsultantTasks = Arrays.asList(new PrivateLiveConsultantTask(), new PrivateLiveConsultantTask());
    }

    /**
     * 测试 loadByIds 方法，当 consultantTaskIds 为空的情况
     */
    @Test
    public void testLoadByIds_WhenConsultantTaskIdsIsEmpty() {
        // arrange
        List<Long> consultantTaskIds = Collections.emptyList();
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.loadByIds(consultantTaskIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 loadByIds 方法，当 consultantTaskIds 不为空，但数据库中没有对应的记录的情况
     */
    @Test
    public void testLoadByIds_WhenConsultantTaskIdsIsNotEmptyButNoRecordInDB() {
        // arrange
        List<Long> consultantTaskIds = Arrays.asList(1L, 2L);
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(Collections.emptyList());
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.loadByIds(consultantTaskIds);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 loadByIds 方法，当 consultantTaskIds 不为空，数据库中有对应的记录的情况
     */
    @Test
    public void testLoadByIds_WhenConsultantTaskIdsIsNotEmptyAndRecordInDB() {
        // arrange
        List<Long> consultantTaskIds = Arrays.asList(1L, 2L);
        when(privateLiveConsultantTaskMapper.selectByExample(any(PrivateLiveConsultantTaskExample.class))).thenReturn(privateLiveConsultantTasks);
        // act
        List<PrivateLiveConsultantTask> result = privateLiveConsultantTaskRepository.loadByIds(consultantTaskIds);
        // assert
        assertEquals(privateLiveConsultantTasks, result);
    }
}
