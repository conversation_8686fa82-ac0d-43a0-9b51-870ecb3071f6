package com.sankuai.carnation.distribution.aunt.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.aunt.domain.AuntDistributionProductDomainService;
import com.sankuai.carnation.distribution.aunt.dto.AuntDistributionInfoDTO;
import com.sankuai.carnation.distribution.aunt.dto.AuntDistributionProductInfoDTO;
import com.sankuai.carnation.distribution.aunt.exception.AuntDistributionException;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService;
import com.sankuai.carnation.distribution.product.v2.domain.ProductItemDomainService;
import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.privatelive.ProductDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.request.CommonProductRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AuntDistributionProductServiceImplTest {

    @InjectMocks
    private AuntDistributionProductServiceImpl auntDistributionProductService;

    @Mock
    private AuntDistributionProductDomainService productDomainService;

    @Mock
    private ProductItemDomainService itemDomainService;

    @Mock
    private ProductSelectifyAclService selectifyAclService;

    private Long dpShopId = 1L;

    private AuntDistributionInfoDTO distributionInfoDTO = new AuntDistributionInfoDTO();

    private List<AuntDistributionProductInfoDTO> productInfoList = Arrays.asList(new AuntDistributionProductInfoDTO(), new AuntDistributionProductInfoDTO());

    private List<Integer> productTypeList = Arrays.asList(1, 2);

    private List<ProductDTO> productDTOS = Arrays.asList(new ProductDTO(), new ProductDTO());

    @Before
    public void setUp() {
        when(productDomainService.queryDistributionProduct(anyLong())).thenReturn(new AuntDistributionInfoDTO());
    }

    private void setUpCommon() throws Exception {
        for (AuntDistributionProductInfoDTO productInfoDTO : productInfoList) {
            productInfoDTO.setOrderNum(1);
        }
        distributionInfoDTO.setProductInfoList(productInfoList);
        when(selectifyAclService.queryProductList(any(CommonProductRequest.class))).thenReturn(productDTOS);
    }

    private void invokePrivateMethod(String methodName, Long dpShopId, AuntDistributionInfoDTO distributionInfoDTO) throws Exception {
        Method method = AuntDistributionProductServiceImpl.class.getDeclaredMethod(methodName, Long.class, AuntDistributionInfoDTO.class);
        method.setAccessible(true);
        method.invoke(auntDistributionProductService, dpShopId, distributionInfoDTO);
    }

    @Test
    public void testQueryDistributionProductDpShopIdIsNull() {
        RemoteResponse<AuntDistributionInfoDTO> response = auntDistributionProductService.queryDistributionProduct(null);
        assertEquals("门店id不合法", response.getMsg());
    }

    @Test
    public void testQueryDistributionProductDpShopIdIsLessThanOrEqualToZero() {
        RemoteResponse<AuntDistributionInfoDTO> response = auntDistributionProductService.queryDistributionProduct(0L);
        assertEquals("门店id不合法", response.getMsg());
    }

    @Test
    public void testQueryDistributionProductNormal() {
        RemoteResponse<AuntDistributionInfoDTO> response = auntDistributionProductService.queryDistributionProduct(1L);
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testBatchQueryDistributionProductDpShopIdListIsNull() throws Throwable {
        List<Long> dpShopIdList = null;
        RemoteResponse<List<AuntDistributionInfoDTO>> response = auntDistributionProductService.batchQueryDistributionProduct(dpShopIdList);
        assertEquals(Collections.emptyList(), response.getData());
    }

    @Test
    public void testBatchQueryDistributionProductDpShopIdListIsNotEmptyButReturnIsEmpty() throws Throwable {
        List<Long> dpShopIdList = Arrays.asList(1L, 2L, 3L);
        when(productDomainService.batchQueryDistributionProduct(dpShopIdList)).thenReturn(Collections.emptyList());
        RemoteResponse<List<AuntDistributionInfoDTO>> response = auntDistributionProductService.batchQueryDistributionProduct(dpShopIdList);
        assertEquals(Collections.emptyList(), response.getData());
    }

    @Test
    public void testBatchQueryDistributionProductDpShopIdListIsNotEmptyAndReturnIsNotEmptyButGetAndSetOrderNumThrowsException() throws Throwable {
        List<Long> dpShopIdList = Arrays.asList(1L, 2L, 3L);
        when(productDomainService.batchQueryDistributionProduct(dpShopIdList)).thenThrow(new AuntDistributionException("查询分销商品异常"));
        RemoteResponse<List<AuntDistributionInfoDTO>> response = auntDistributionProductService.batchQueryDistributionProduct(dpShopIdList);
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("查询分销商品异常", response.getMsg());
    }

    @Test
    public void testQueryCommissionDpShopIdIsNull() throws Throwable {
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(null, 1, 1L);
        assertEquals("门店id不合法", response.getMsg());
    }

    @Test
    public void testQueryCommissionDpShopIdIsLessThanOrEqualToZero() throws Throwable {
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(0L, 1, 1L);
        assertEquals("门店id不合法", response.getMsg());
    }

    @Test
    public void testQueryCommissionProductTypeIsUnknown() throws Throwable {
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(1L, 0, 1L);
        assertEquals("商品信息不合法", response.getMsg());
    }

    @Test
    public void testQueryCommissionProductIdIsNull() throws Throwable {
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(1L, 1, null);
        assertEquals("商品信息不合法", response.getMsg());
    }

    @Test
    public void testQueryCommissionProductIdIsLessThanOrEqualToZero() throws Throwable {
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(1L, 1, 0L);
        assertEquals("商品信息不合法", response.getMsg());
    }

    @Test
    public void testQueryCommissionThrowsAuntDistributionException() throws Throwable {
        when(productDomainService.queryCommission(anyLong(), anyInt(), anyLong())).thenThrow(new AuntDistributionException("查询分销商品佣金异常"));
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(1L, 1, 1L);
        assertEquals("查询分销商品佣金异常", response.getMsg());
    }

    @Test
    public void testQueryCommissionThrowsException() throws Throwable {
        when(productDomainService.queryCommission(anyLong(), anyInt(), anyLong())).thenThrow(new RuntimeException("查询分销商品佣金异常"));
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(1L, 1, 1L);
        assertEquals("查询分销商品佣金异常", response.getMsg());
    }

    @Test
    public void testQueryCommissionNormal() throws Throwable {
        when(productDomainService.queryCommission(anyLong(), anyInt(), anyLong())).thenReturn(100L);
        RemoteResponse<Long> response = auntDistributionProductService.queryCommission(1L, 1, 1L);
        assertEquals(100L, response.getData().longValue());
    }

    /**
     * Test case for general exception scenario
     * Should return fail response with error message
     */
    @Test
    public void testBatchQueryDistributionProduct_WhenGeneralExceptionOccurs() {
        // arrange
        List<Long> dpShopIdList = Arrays.asList(1L, 2L);
        when(productDomainService.batchQueryDistributionProduct(anyList())).thenThrow(new RuntimeException("Unexpected error"));
        // act
        RemoteResponse<List<AuntDistributionInfoDTO>> response = auntDistributionProductService.batchQueryDistributionProduct(dpShopIdList);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("查询分销商品异常", response.getMsg());
    }

    /**
     * Test case for empty input list
     * Should return success response with empty list
     */
    @Test
    public void testBatchQueryDistributionProduct_WhenEmptyInput() {
        // arrange
        List<Long> emptyList = Lists.newArrayList();
        // act
        RemoteResponse<List<AuntDistributionInfoDTO>> response = auntDistributionProductService.batchQueryDistributionProduct(emptyList);
        // assert
        assertEquals(true, response.isSuccess());
        assertEquals(0, response.getData().size());
    }

    /**
     * Test case for null input
     * Should return success response with empty list
     */
    @Test
    public void testBatchQueryDistributionProduct_WhenNullInput() {
        // act
        RemoteResponse<List<AuntDistributionInfoDTO>> response = auntDistributionProductService.batchQueryDistributionProduct(null);
        // assert
        assertEquals(true, response.isSuccess());
        assertEquals(0, response.getData().size());
    }

}
