package com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.proxy.impl;

import com.dianping.technician.common.api.domain.TechnicianResp;
import com.sankuai.technician.trade.api.settle.dto.PrivateLiveSettleCountDTO;
import com.sankuai.technician.trade.api.settle.dto.PrivateLiveSettleCountResult;
import com.sankuai.technician.trade.api.settle.req.PrivateLiveSettleCountRequest;
import com.sankuai.technician.trade.api.settle.solution.privatelive.PrivateLiveSettleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveSettleServiceProxyImplTest {

    @Mock
    private PrivateLiveSettleService privateLiveSettleService;

    @InjectMocks
    private PrivateLiveSettleServiceProxyImpl privateLiveSettleServiceProxyImpl;

    private PrivateLiveSettleCountRequest request;
    private TechnicianResp<PrivateLiveSettleCountResult> successResponse;
    private TechnicianResp<PrivateLiveSettleCountResult> failureResponse;

    @Before
    public void setUp() {
        request = new PrivateLiveSettleCountRequest();
        request.setLiveId("live123");
        request.setDistributorGroupIds(Arrays.asList("group1", "group2"));
        request.setSceneList(Arrays.asList(1, 2));

        PrivateLiveSettleCountDTO dto = new PrivateLiveSettleCountDTO();
        PrivateLiveSettleCountResult result = new PrivateLiveSettleCountResult();
        result.setSettleCountDTOList(Arrays.asList(dto));

        successResponse = new TechnicianResp<>(TechnicianResp.SUCCESS, "OK", result);
        failureResponse = new TechnicianResp<>(TechnicianResp.FAIL, "Error");
    }

    /**
     * 测试queryCountCommission方法，当远程调用成功时
     */
    @Test
    public void testQueryCountCommissionSuccess() throws Throwable {
        // arrange
        when(privateLiveSettleService.countCommission(any(PrivateLiveSettleCountRequest.class))).thenReturn(successResponse);

        // act
        CompletableFuture<List<PrivateLiveSettleCountDTO>> future = privateLiveSettleServiceProxyImpl.queryCountCommission(request);
        List<PrivateLiveSettleCountDTO> result = future.get();

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试queryCountCommission方法，当远程调用失败时
     */
    @Test
    public void testQueryCountCommissionFailure() throws Throwable {
        // arrange
        when(privateLiveSettleService.countCommission(any(PrivateLiveSettleCountRequest.class))).thenReturn(failureResponse);

        // act
        CompletableFuture<List<PrivateLiveSettleCountDTO>> future = privateLiveSettleServiceProxyImpl.queryCountCommission(request);
        List<PrivateLiveSettleCountDTO> result = future.get();

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryCountCommission方法
     */
    @Test
    public void testQueryCountCommissionException() throws Throwable {
        // arrange
        when(privateLiveSettleService.countCommission(any(PrivateLiveSettleCountRequest.class))).thenThrow(new RuntimeException("Remote call exception"));

        // act
        CompletableFuture<List<PrivateLiveSettleCountDTO>> future = privateLiveSettleServiceProxyImpl.queryCountCommission(request);
        future.get(); // This should throw an ExecutionException

        // assert is handled by the expected exception
    }
}
