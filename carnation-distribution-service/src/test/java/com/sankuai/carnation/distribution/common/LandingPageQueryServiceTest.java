package com.sankuai.carnation.distribution.common;

import com.alibaba.fastjson.JSONObject;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.LandingPageConfigDTO;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.sankuai.carnation.distribution.common.service.impl.LandingPageQueryServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LandingPageQueryServiceTest {

    @InjectMocks
    private LandingPageQueryServiceImpl landingPageQueryService;

    @Mock
    private HaimaClient haimaClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试bizType或moduleType为空时，返回空的结果
     */
    @Test
    public void testGetLandingPageConfigEmptyParams() {
        // arrange
        String bizType = "";
        String moduleType = "";

        // act
        RemoteResponse<LandingPageConfigDTO> result = landingPageQueryService.getLandingPageConfig(bizType, moduleType);

        // assert
        assertNull(result.getData());
    }

    /**
     * 测试haimaClient.query方法返回失败时，返回空的结果
     */
    @Test
    public void testGetLandingPageConfigQueryFailed() {
        // arrange
        String bizType = "testBizType";
        String moduleType = "testModuleType";
        HaimaResponse haimaResponse = new HaimaResponse(false, null, "error message");
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(haimaResponse);

        // act
        RemoteResponse<LandingPageConfigDTO> result = landingPageQueryService.getLandingPageConfig(bizType, moduleType);

        // assert
        assertNull(result.getData());
    }

    /**
     * 测试haimaClient.query方法返回成功，但没有符合条件的配置时，返回空的结果
     */
    @Test
    public void testGetLandingPageConfigNoMatchedConfig() {
        // arrange
        String bizType = "testBizType";
        String moduleType = "testModuleType";
        HaimaResponse haimaResponse = new HaimaResponse(true, Collections.emptyList(), "");
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(haimaResponse);

        // act
        RemoteResponse<LandingPageConfigDTO> result = landingPageQueryService.getLandingPageConfig(bizType, moduleType);

        // assert
        assertNull(result.getData());
    }

    /**
     * 测试haimaClient.query方法返回成功，且有符合条件的配置时，返回正确的结果
     */
    @Test
    public void testGetLandingPageConfigSuccess() {
        // arrange
        String bizType = "medicalCosmetology";
        String moduleType = "avoidGuide";
        //        HaimaConfig haimaConfig = new HaimaConfig();

        //        haimaConfig.setExtJson("{\"bizType\":\"testBizType\",\"moduleType\":\"testModuleType\"}");
        HaimaResponse haimaResponse = JSONObject.parseObject(mockData, HaimaResponse.class);
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(haimaResponse);

        // act
        RemoteResponse<LandingPageConfigDTO> result = landingPageQueryService.getLandingPageConfig(bizType, moduleType);

        // assert
        assertEquals(bizType, result.getData().getBizType());
        assertEquals(moduleType, result.getData().getModuleType());
    }

    private String mockData = "{\n" + "  \"statusCode\": 200,\n" + "  \"message\": null,\n" + "  \"data\": [\n" + "    {\n" + "      \"scene\": \"daozong_distribution_landing_page\",\n" + "      \"configId\": 88184,\n" + "      \"extJson\": \"{\\\"bizType\\\":\\\"medicalCosmetology\\\",\\\"moduleType\\\":\\\"avoidGuide\\\",\\\"nameText\\\":\\\"避坑指南\\\",\\\"titleText\\\":\\\"安心变美小帮手 | 公立三甲医生三审三校\\\",\\\"subTitleText\\\":\\\"\\\",\\\"moduleName\\\":\\\"避坑指南\\\"}\",\n" + "      \"contents\": [\n" + "        {\n" + "          \"configId\": 88184,\n" + "          \"contentId\": 572308,\n" + "          \"createTime\": 1685966128000,\n" + "          \"updateTime\": 1685966128000,\n" + "          \"content\": \"{\\\"contentPic\\\":\\\"https://img.meituan.net/dpmobile/b9deab7d5baaf1e18017ee5c4f8a816c1373855.png.webp\\\",\\\"city\\\":\\\"\\\",\\\"contentText\\\":\\\"\\\",\\\"contentThumbnail\\\":\\\"https://img.meituan.net/dpmobile/b9deab7d5baaf1e18017ee5c4f8a816c1373855.png.webp\\\",\\\"order\\\":0}\"\n" + "        },\n" + "        {\n" + "          \"configId\": 88184,\n" + "          \"contentId\": 572019,\n" + "          \"createTime\": 1685600575000,\n" + "          \"updateTime\": 1685966128000,\n" + "          \"content\": \"{\\\"contentPic\\\":\\\"https://img.meituan.net/dpmobile/b162a794e9407422ddda4d731372f54d1720524.png.webp\\\",\\\"city\\\":\\\"\\\",\\\"contentText\\\":\\\"test\\\",\\\"contentThumbnail\\\":\\\"https://img.meituan.net/dpmobile/b162a794e9407422ddda4d731372f54d1720524.png.webp\\\",\\\"order\\\":1}\"\n" + "        }\n" + "      ],\n" + "      \"createTime\": 1685600575000,\n" + "      \"updateTime\": 1686123240000\n" + "    },\n" + "    {\n" + "      \"scene\": \"daozong_distribution_landing_page\",\n" + "      \"configId\": 88186,\n" + "      \"extJson\": \"{\\\"bizType\\\":\\\"medicalCosmetology\\\",\\\"moduleType\\\":\\\"weChat\\\",\\\"titleText\\\":\\\"不知道哪个项目适合你？官方医生免费咨询\\\",\\\"titlePic\\\":\\\"https://img.meituan.net/dpmobile/8e1d8ce802808702a8448256a00028bc45660.png.webp\\\",\\\"nameText\\\":\\\"官方咨询\\\",\\\"namePic\\\":\\\"\\\",\\\"moduleName\\\":\\\"官方咨询\\\"}\",\n" + "      \"contents\": [\n" + "        {\n" + "          \"configId\": 88186,\n" + "          \"contentId\": 572027,\n" + "          \"createTime\": 1685605503000,\n" + "          \"updateTime\": 1685605503000,\n" + "          \"content\": \"{\\\"contentPic\\\":\\\"https://img.meituan.net/dpmobile/d9e029c30105a55ba3d3be6fac74413a2688.png.webp\\\",\\\"city\\\":\\\"\\\",\\\"contentText\\\":\\\"保存或长按二维码可添加官方医生微信\\\",\\\"order\\\":0}\"\n" + "        }\n" + "      ],\n" + "      \"createTime\": 1685605503000,\n" + "      \"updateTime\": 1686123240000\n" + "    },\n" + "    {\n" + "      \"scene\": \"daozong_distribution_landing_page\",\n" + "      \"configId\": 88270,\n" + "      \"extJson\": \"{\\\"bizType\\\":\\\"medicalCosmetology\\\",\\\"moduleType\\\":\\\"share\\\",\\\"titleText\\\":\\\"\\\",\\\"moduleName\\\":\\\"分享\\\",\\\"titlePic\\\":\\\"\\\"}\",\n" + "      \"contents\": [\n" + "        {\n" + "          \"configId\": 88270,\n" + "          \"contentId\": 572407,\n" + "          \"createTime\": 1686123240000,\n" + "          \"updateTime\": 1686123240000,\n" + "          \"content\": \"{\\\"contentPic\\\":\\\"https://img.meituan.net/dpmobile/37dea369fb7d723a0c47fc061b63d83a166128.png\\\",\\\"city\\\":\\\"\\\",\\\"contentText\\\":\\\"【美团医美】特价内购 | 正品保障 | 4.5星合作机构\\\",\\\"contSubText\\\":\\\"\\\",\\\"contentThumbnail\\\":\\\"https://img.meituan.net/dpmobile/37dea369fb7d723a0c47fc061b63d83a166128.png.webp\\\",\\\"contentType\\\":\\\"miniApp\\\",\\\"order\\\":0}\"\n" + "        },\n" + "        {\n" + "          \"configId\": 88270,\n" + "          \"contentId\": 572410,\n" + "          \"createTime\": 1686123240000,\n" + "          \"updateTime\": 1686123240000,\n" + "          \"content\": \"{\\\"contentPic\\\":\\\"https://img.meituan.net/dpmobile/b017f162a7ee9d5d489a1edf234a4e5c207102.png\\\",\\\"city\\\":\\\"\\\",\\\"contentText\\\":\\\"【美团医美】特价内购 | 正品保障 | 4.5星合作机构\\\",\\\"contSubText\\\":\\\"特价内购！赶紧来看看吧！\\\",\\\"contentThumbnail\\\":\\\"https://img.meituan.net/dpmobile/b017f162a7ee9d5d489a1edf234a4e5c207102.png.webp\\\",\\\"contentType\\\":\\\"h5\\\",\\\"order\\\":0}\"\n" + "        }\n" + "      ],\n" + "      \"createTime\": 1686123240000,\n" + "      \"updateTime\": 1686278759000\n" + "    }\n" + "  ],\n" + "  \"extraInfo\": {\n" + "    \"isNewProcess\": true,\n" + "    \"pkgUpdateTime\": 1686278759000,\n" + "    \"packageId\": 384174,\n" + "    \"originPackageId\": 384174,\n" + "    \"CacheType\": \"RemoteCache\"\n" + "  },\n" + "  \"success\": true\n" + "}";
}
