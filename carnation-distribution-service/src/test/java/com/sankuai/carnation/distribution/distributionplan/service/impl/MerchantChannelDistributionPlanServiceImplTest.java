package com.sankuai.carnation.distribution.distributionplan.service.impl;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.sankuai.carnation.distribution.distributionplan.acl.DealProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.GeneralDealGroupSearchAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.MerchantAuthAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductCommissionBasisAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealProductInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralDealGroupSearchResult;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisInfo;
import com.sankuai.carnation.distribution.distributionplan.enums.CommissionBasisTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.DistributionProductPageQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanCreateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanOperateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanPageQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.MerchantChannelDistributionPlanServiceImpl;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.MerchantChannelDistributionPlanOperateStrategyFactory;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.ModifyDistributionPlanCommissionStrategy;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.RecreateDistributionPlanStrategy;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.TerminateDistributionPlanStrategy;
import com.sankuai.carnation.distribution.distributionplan.service.model.ProductCommissionRange;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.CreateMerchantChannelDistributionPlanVO;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.DistributionPlanProductVO;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.MerchantChannelDistributionPlanVO;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.OperateMerchantChannelDistributionPlanVO;
import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanSubjectDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionCommissionTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionPlanSceneCodeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionPlanStatusEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionPlanSubjectTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanQueryRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanCreator;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@RunWith(MockitoJUnitRunner.class)
public class MerchantChannelDistributionPlanServiceImplTest {
    @InjectMocks
    private MerchantChannelDistributionPlanServiceImpl service;

    @Mock
    private MerchantAuthAcl merchantAuthAcl;

    @Mock
    private DistributionPlanQueryService distributionPlanQueryService;

    @Mock
    private DealProductAcl dealProductAcl;

    @Mock
    private ProductCommissionBasisAcl productCommissionBasisAcl;

    @Mock
    private GeneralDealGroupSearchAcl generalDealGroupSearchAcl;

    @Mock
    private DistributionPlanCommandService distributionPlanCommandService;

    @Mock
    private MerchantChannelDistributionPlanOperateStrategyFactory merchantChannelDistributionPlanOperateStrategyFactory;

    @Mock
    private ModifyDistributionPlanCommissionStrategy modifyDistributionPlanCommissionStrategy;

    @Mock
    private RecreateDistributionPlanStrategy recreateDistributionPlanStrategy;

    @Mock
    private TerminateDistributionPlanStrategy terminateDistributionPlanStrategy;


    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试场景：正常情况下分页查询分销计划
     */
    @Test
    public void testPageQueryDistributionPlanSuccess() {
        // arrange
        MerchantChannelDistributionPlanPageQueryRequest request = new MerchantChannelDistributionPlanPageQueryRequest();
        request.setAccountId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setProductId(10L);
        request.setStatus(1);

        when(merchantAuthAcl.getCustomerIdByAccountId(anyLong())).thenReturn(Optional.of(2L));

        DistributionPlanDTO distributionPlanDTO = new DistributionPlanDTO();
        distributionPlanDTO.setPlanId(1L);
        distributionPlanDTO.setBeginTime(new Date(1727193600000L));
        distributionPlanDTO.setEndTime(new Date(4095219606000L));
        distributionPlanDTO.setStatus(DistributionPlanStatusEnum.VALID.getCode());


        Map<Integer, BigDecimal> map = Maps.newHashMap();
        map.put(DistributionCommissionTypeEnum.MERCHANT_CHANNEL_COMMISSION.getCode(), new BigDecimal("30"));
        distributionPlanDTO.setCommissionRateMap(map);

        DistributionPlanSubjectDTO distributionPlanSubjectDTO = new DistributionPlanSubjectDTO();
        distributionPlanSubjectDTO.setSubjectType(DistributionPlanSubjectTypeEnum.PRODUCT.getCode());
        distributionPlanSubjectDTO.setField1("1234");
        distributionPlanDTO.setSubject(distributionPlanSubjectDTO);

        PageResult<DistributionPlanDTO> pageResult = new PageResult<>(Collections.singletonList(distributionPlanDTO), 1, 1, 10);
        Response<PageResult<DistributionPlanDTO>> response = Response.buildSuccess(pageResult);

        when(distributionPlanQueryService.pageQueryDistributionPlan(any())).thenReturn(response);

        DealProductInfo dealProductInfo = new DealProductInfo();
        dealProductInfo.setDpProductId(1234L);
        dealProductInfo.setMtProductId(1L);
        dealProductInfo.setCategoryId(1L);
        dealProductInfo.setLeafCategoryId(1L);
        dealProductInfo.setProductName("Product");
        dealProductInfo.setOnlineStatus(true);
        dealProductInfo.setApplyMtShopIds(Collections.emptyList());
        dealProductInfo.setTradeType(3);
        dealProductInfo.setEndSaleDate("2023-12-31");
        dealProductInfo.setSalePrice("100");
        dealProductInfo.setMarketPrice("150");
        dealProductInfo.setPicPath("111");
        dealProductInfo.setPlatformCustomerId(1L);

        when(dealProductAcl.queryMtDealProductToMap(any())).thenReturn(Collections.singletonMap(1234L, dealProductInfo));

        // act
        PaginationRemoteResponse<MerchantChannelDistributionPlanVO> result = service.pageQueryDistributionPlan(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试场景：正常情况下分页查询分销商品
     */
    @Test
    public void testPageQueryDistributionProductNormal() {
        // arrange
        DistributionProductPageQueryRequest request = new DistributionProductPageQueryRequest();
        request.setAccountId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setProductId(1L);
        request.setProductName("测试");
        request.setDpShopId(1L);

        when(merchantAuthAcl.getCustomerIdByAccountId(anyLong())).thenReturn(Optional.of(1L));

        GeneralDealGroupSearchResult generalDealGroupSearchResult = new GeneralDealGroupSearchResult();
        generalDealGroupSearchResult.setProductIdList(Lists.newArrayList(1L));
        generalDealGroupSearchResult.setTotalHits(1L);
        when(generalDealGroupSearchAcl.searchDpDealGroups(any())).thenReturn(generalDealGroupSearchResult);

        DealProductInfo dealProductInfo = new DealProductInfo();
        dealProductInfo.setMtProductId(2L);
        dealProductInfo.setDpProductId(1L);
        dealProductInfo.setProductName("测试");
        dealProductInfo.setSalePrice("1500");
        dealProductInfo.setPicPath("http://123.com");
        when(dealProductAcl.queryDpDealProductByIds(anyList())).thenReturn(Lists.newArrayList(dealProductInfo));

        ProductCommissionBasisInfo dealGroupCommissionResult = new ProductCommissionBasisInfo();
        dealGroupCommissionResult.setDpProductId(1L);
        dealGroupCommissionResult.setCommissionBasisType(CommissionBasisTypeEnum.COMMISSION_GROUP);
        when(productCommissionBasisAcl.batchQueryProductCommissionBasis(anyList())).thenReturn(Lists.newArrayList(dealGroupCommissionResult));
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            ProductCommissionRange commissionBasis = new ProductCommissionRange();
            commissionBasis.setMinCommissionValue(0);
            commissionBasis.setMaxCommissionValue(60);
            Map<String, ProductCommissionRange> commissionBasisMap = Maps.newHashMap();
            commissionBasisMap.put("1", commissionBasis);
            mockedStatic.when(() -> Lion.getMap(anyString(), eq("merchant.channel.distribution.plan.commission.basis.type.to.commission.range.map"), any())).thenReturn(commissionBasisMap);

            DistributionPlanDTO distributionPlanDTO = new DistributionPlanDTO();
            distributionPlanDTO.setStatus(DistributionPlanStatusEnum.VALID.getCode());
            distributionPlanDTO.setSubject(new DistributionPlanSubjectDTO(1L, 3, "2", "1", "1"));
            Response<List<DistributionPlanDTO>> listResponse = Response.buildSuccess(Lists.newArrayList(distributionPlanDTO));
            when(distributionPlanQueryService.queryLatestDistributionPlan(any(DistributionPlanQueryRequest.class))).thenReturn(listResponse);
            // act
            PaginationRemoteResponse<DistributionPlanProductVO> response = service.pageQueryDistributionProduct(request);
            // assert
            assertNotNull(response);
           /* assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
            assertNotNull(response.getData());

            assertEquals(1, response.getTotalHit());

            List<DistributionPlanProductVO> productList = response.getData();
            assertNotNull(productList);
            assertEquals(1, productList.size());
            DistributionPlanProductVO product = productList.get(0);
            assertEquals(1L, product.getProductId().longValue());
            assertEquals("测试", product.getProductName());
            assertEquals("1500", product.getPriceText());
            assertEquals("http://123.com", product.getPriceText());*/
        }
    }

    @Test
    public void testCreateDistributionPlanSuccess() {
        // arrange
        MerchantChannelDistributionPlanCreateRequest request = new MerchantChannelDistributionPlanCreateRequest();
        request.setAccountId(1L);
        request.setProductId(1L);
        request.setProductType(1);
        request.setCommissionRate("30");

        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            ProductCommissionRange commissionBasis = new ProductCommissionRange();
            commissionBasis.setMinCommissionValue(0);
            commissionBasis.setMaxCommissionValue(60);
            Map<String, ProductCommissionRange> commissionBasisMap = Maps.newHashMap();
            commissionBasisMap.put("1", commissionBasis);
            mockedStatic.when(() -> Lion.getMap(anyString(), eq("merchant.channel.distribution.plan.commission.basis.type.to.commission.range.map"), any())).thenReturn(commissionBasisMap);

            when(merchantAuthAcl.getCustomerIdByAccountId(request.getAccountId())).thenReturn(Optional.of(1L));

            ProductCommissionBasisInfo dealGroupCommissionResult = ProductCommissionBasisInfo.builder()
                    .dpProductId(1L)
                    .commissionBasisType(CommissionBasisTypeEnum.COMMISSION_GROUP)
                    .build();
            when(productCommissionBasisAcl.batchQueryProductCommissionBasis(anyList())).thenReturn(Lists.newArrayList(dealGroupCommissionResult));

            DealProductInfo dealProductInfo = new DealProductInfo();
            dealProductInfo.setOnlineStatus(true);
            dealProductInfo.setEndSaleDate("2999-01-01 23:59:59");
            dealProductInfo.setMtProductId(1L);
            dealProductInfo.setPlatformCustomerId(1L);
            when(dealProductAcl.queryDpDealProductByIds(any())).thenReturn(Lists.newArrayList(dealProductInfo));
            when(distributionPlanCommandService.createDistributionPlan(any())).thenReturn(Response.buildSuccess(null));

            // act
            RemoteResponse<CreateMerchantChannelDistributionPlanVO> response = service.createDistributionPlan(request);

            // assert
            // todo redisLockService.tryLock 方法mock不住返回值
            assertEquals(ResponseEnum.FAILURE.code, response.getCode());
        }
    }

    @Test
    public void testOperateDistributionPlan_Modify() {
        // arrange
        MerchantChannelDistributionPlanOperateRequest request = new MerchantChannelDistributionPlanOperateRequest();
        request.setAccountId(1L);
        request.setPlanId(1L);
        request.setOperateType(MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION.getType());
        request.setCommissionRate("20");

        DistributionPlanDTO distributionPlanDTO = new DistributionPlanDTO();
        distributionPlanDTO.setPlanId(1L);
        DistributionPlanCreator creator = DistributionPlanCreator.builder().creatorId("1").build();
        distributionPlanDTO.setCreator(creator);
        distributionPlanDTO.setSceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode());
        distributionPlanDTO.setStatus(DistributionPlanStatusEnum.VALID.getCode());
        distributionPlanDTO.setEndTime(new Date(new Date().getTime() + 100000L));
        when(merchantAuthAcl.getCustomerIdByAccountId(anyLong())).thenReturn(Optional.of(1L));

        when(distributionPlanQueryService.queryDistributionPlanByIds(anyList()))
                .thenReturn(new Response<>(Response.SUCCESS_CODE, "成功", Collections.singletonList(distributionPlanDTO)));
        when(merchantChannelDistributionPlanOperateStrategyFactory.getStrategyByOperateType(any(MerchantChannelDistributionPlanOperateTypeEnum.class)))
                .thenReturn(modifyDistributionPlanCommissionStrategy);
        when(distributionPlanCommandService.operateDistributionPlan(any())).thenReturn(Response.buildSuccess(null));

        // act
        RemoteResponse<OperateMerchantChannelDistributionPlanVO> response = service.operateDistributionPlan(request);

        // assert
        assertTrue(response.isSuccess());
    }

    @Test
    public void testOperateDistributionPlan_Recreate() {
        // arrange
        MerchantChannelDistributionPlanOperateRequest request = new MerchantChannelDistributionPlanOperateRequest();
        request.setAccountId(1L);
        request.setPlanId(1L);
        request.setOperateType(MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION.getType());
        request.setCommissionRate("20");

        DistributionPlanDTO distributionPlanDTO = new DistributionPlanDTO();
        distributionPlanDTO.setPlanId(1L);
        DistributionPlanCreator creator = DistributionPlanCreator.builder().creatorId("1").build();
        distributionPlanDTO.setCreator(creator);
        distributionPlanDTO.setSceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode());
        distributionPlanDTO.setStatus(DistributionPlanStatusEnum.VALID.getCode());
        distributionPlanDTO.setEndTime(new Date(new Date().getTime() + 100000L));
        distributionPlanDTO.setSceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode());
        distributionPlanDTO.setChannel("111");
        DistributionPlanSubjectDTO distributionPlanSubjectDTO = new DistributionPlanSubjectDTO();
        distributionPlanSubjectDTO.setSubjectId(1L);
        distributionPlanSubjectDTO.setSubjectType(1);
        distributionPlanSubjectDTO.setField1("1");
        distributionPlanSubjectDTO.setField2("2");
        distributionPlanSubjectDTO.setField3("3");
        distributionPlanDTO.setSubject(distributionPlanSubjectDTO);
        Map<Integer, BigDecimal> commissionRateMap = Maps.newHashMap();
        distributionPlanDTO.setCommissionRateMap(commissionRateMap);
        when(merchantAuthAcl.getCustomerIdByAccountId(anyLong())).thenReturn(Optional.of(1L));

        when(distributionPlanQueryService.queryDistributionPlanByIds(anyList()))
                .thenReturn(new Response<>(Response.SUCCESS_CODE, "成功", Collections.singletonList(distributionPlanDTO)));
        when(merchantChannelDistributionPlanOperateStrategyFactory.getStrategyByOperateType(any(MerchantChannelDistributionPlanOperateTypeEnum.class)))
                .thenReturn(recreateDistributionPlanStrategy);
        when(distributionPlanCommandService.operateDistributionPlan(any())).thenReturn(Response.buildSuccess(null));

        // act
        RemoteResponse<OperateMerchantChannelDistributionPlanVO> response = service.operateDistributionPlan(request);

        // assert
        assertTrue(response.isSuccess());
    }

    @Test
    public void testOperateDistributionPlan_Terminate() {
        // arrange
        MerchantChannelDistributionPlanOperateRequest request = new MerchantChannelDistributionPlanOperateRequest();
        request.setAccountId(1L);
        request.setPlanId(1L);
        request.setOperateType(MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION.getType());
        request.setCommissionRate("20");

        DistributionPlanDTO distributionPlanDTO = new DistributionPlanDTO();
        distributionPlanDTO.setPlanId(1L);
        DistributionPlanCreator creator = DistributionPlanCreator.builder().creatorId("1").build();
        distributionPlanDTO.setCreator(creator);
        distributionPlanDTO.setSceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode());
        distributionPlanDTO.setStatus(DistributionPlanStatusEnum.VALID.getCode());
        distributionPlanDTO.setEndTime(new Date(new Date().getTime() + 1000000L));
        distributionPlanDTO.setSceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode());
        distributionPlanDTO.setChannel("111");
        DistributionPlanSubjectDTO distributionPlanSubjectDTO = new DistributionPlanSubjectDTO();
        distributionPlanSubjectDTO.setSubjectId(1L);
        distributionPlanSubjectDTO.setSubjectType(1);
        distributionPlanSubjectDTO.setField1("1");
        distributionPlanSubjectDTO.setField2("2");
        distributionPlanSubjectDTO.setField3("3");
        distributionPlanDTO.setSubject(distributionPlanSubjectDTO);
        Map<Integer, BigDecimal> commissionRateMap = Maps.newHashMap();
        distributionPlanDTO.setCommissionRateMap(commissionRateMap);
        distributionPlanDTO.setBeginTime(new Date(new Date().getTime() - 1000000L));
        when(merchantAuthAcl.getCustomerIdByAccountId(anyLong())).thenReturn(Optional.of(1L));

        when(distributionPlanQueryService.queryDistributionPlanByIds(anyList()))
                .thenReturn(new Response<>(Response.SUCCESS_CODE, "成功", Collections.singletonList(distributionPlanDTO)));
        when(merchantChannelDistributionPlanOperateStrategyFactory.getStrategyByOperateType(any(MerchantChannelDistributionPlanOperateTypeEnum.class)))
                .thenReturn(terminateDistributionPlanStrategy);
        when(distributionPlanCommandService.operateDistributionPlan(any())).thenReturn(Response.buildSuccess(null));

        // act
        RemoteResponse<OperateMerchantChannelDistributionPlanVO> response = service.operateDistributionPlan(request);

        // assert
        assertTrue(response.isSuccess());
    }
}