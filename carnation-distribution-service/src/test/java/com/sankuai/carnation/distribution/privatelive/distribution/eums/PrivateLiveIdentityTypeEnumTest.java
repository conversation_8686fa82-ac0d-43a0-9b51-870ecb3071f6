package com.sankuai.carnation.distribution.privatelive.distribution.eums;

import org.junit.Assert;
import org.junit.Test;
import static org.mockito.Mockito.*;

/**
 * 测试 PrivateLiveIdentityTypeEnum 的 getByCode 方法
 */
public class PrivateLiveIdentityTypeEnumTest {

    /**
     * 测试 getByCode 方法，当 code 为 null 时应返回 UNKNOWN
     */
    @Test
    public void testGetByCodeWithNull() {
        // arrange
        Integer code = null;

        // act
        PrivateLiveIdentityTypeEnum result = PrivateLiveIdentityTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveIdentityTypeEnum.UNKNOWN, result);
    }

    /**
     * 测试 getByCode 方法，当 code 为未知值时应返回 UNKNOWN
     */
    @Test
    public void testGetByCodeWithUnknownValue() {
        // arrange
        Integer code = 999;

        // act
        PrivateLiveIdentityTypeEnum result = PrivateLiveIdentityTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveIdentityTypeEnum.UNKNOWN, result);
    }

    /**
     * 测试 getByCode 方法，当 code 为 0 时应返回 UNKNOWN
     */
    @Test
    public void testGetByCodeWith0() {
        // arrange
        Integer code = 0;

        // act
        PrivateLiveIdentityTypeEnum result = PrivateLiveIdentityTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveIdentityTypeEnum.UNKNOWN, result);
    }

    /**
     * 测试 getByCode 方法，当 code 为 1 时应返回 CONSULTANT
     */
    @Test
    public void testGetByCodeWith1() {
        // arrange
        Integer code = 1;

        // act
        PrivateLiveIdentityTypeEnum result = PrivateLiveIdentityTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveIdentityTypeEnum.CONSULTANT, result);
    }

    /**
     * 测试 getByCode 方法，当 code 为 2 时应返回 DISTRIBUTOR_GROUP
     */
    @Test
    public void testGetByCodeWith2() {
        // arrange
        Integer code = 2;

        // act
        PrivateLiveIdentityTypeEnum result = PrivateLiveIdentityTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveIdentityTypeEnum.DISTRIBUTOR_GROUP, result);
    }

    /**
     * 测试 getByCode 方法，当 code 为 3 时应返回 DISTRIBUTOR
     */
    @Test
    public void testGetByCodeWith3() {
        // arrange
        Integer code = 3;

        // act
        PrivateLiveIdentityTypeEnum result = PrivateLiveIdentityTypeEnum.getByCode(code);

        // assert
        Assert.assertEquals(PrivateLiveIdentityTypeEnum.DISTRIBUTOR, result);
    }
}
