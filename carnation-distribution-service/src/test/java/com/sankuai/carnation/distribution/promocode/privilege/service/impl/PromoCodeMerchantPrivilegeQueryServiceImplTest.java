package com.sankuai.carnation.distribution.promocode.privilege.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.promocode.privilege.dto.MerchantPrivilegeRecordDTO;
import com.sankuai.carnation.distribution.promocode.privilege.enums.PrivilegeCodeEnum;
import com.sankuai.carnation.distribution.promocode.privilege.service.PromoCodeMerchantPrivilegeDeliveryRecordLocalService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PromoCodeMerchantPrivilegeQueryServiceImplTest {

    @InjectMocks
    private PromoCodeMerchantPrivilegeQueryServiceImpl service;

    @Mock
    private PromoCodeMerchantPrivilegeDeliveryRecordLocalService privilegeDeliveryRecordLocalService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试batchQueryEffectiveTag方法，当shopIdList为空时
     */
    @Test
    public void testBatchQueryEffectiveTagEmptyShopIdList() {
        RemoteResponse<Map<Long, List<MerchantPrivilegeRecordDTO>>> response = service.batchQueryEffectiveTag(Collections.emptyList(), 1);
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isEmpty());
    }

    /**
     * 测试batchQueryEffectiveTag方法，当返回的权益列表为空时
     */
    @Test
    public void testBatchQueryEffectiveTagEmptyPrivilegeList() throws Exception {
        when(privilegeDeliveryRecordLocalService.batchQueryEffectivePrivilegeByShopId(anyList(), anyInt()))
                .thenReturn(Collections.emptyMap());

        RemoteResponse<Map<Long, List<MerchantPrivilegeRecordDTO>>> response = service.batchQueryEffectiveTag(Arrays.asList(1L, 2L), 1);
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isEmpty());
    }

    /**
     * 测试batchQueryEffectiveTag方法，当返回的权益列表不为空，但不包含POPULAR_SHOP权益时
     */
    @Test
    public void testBatchQueryEffectiveTagNonPopularShopPrivilege() throws Exception {
        MerchantPrivilegeRecordDTO recordDTO = new MerchantPrivilegeRecordDTO();
        recordDTO.setPrivilegeCode("NON_POPULAR_SHOP");
        Map<Long, List<MerchantPrivilegeRecordDTO>> privilegeMap = new HashMap<>();
        privilegeMap.put(1L, Collections.singletonList(recordDTO));

        when(privilegeDeliveryRecordLocalService.batchQueryEffectivePrivilegeByShopId(anyList(), anyInt()))
                .thenReturn(privilegeMap);

        RemoteResponse<Map<Long, List<MerchantPrivilegeRecordDTO>>> response = service.batchQueryEffectiveTag(Arrays.asList(1L), 1);
        assertTrue(response.isSuccess());
        assertTrue(response.getData().get(1L).isEmpty());
    }

    /**
     * 测试batchQueryEffectiveTag方法，当返回的权益列表包含POPULAR_SHOP权益时
     */
    @Test
    public void testBatchQueryEffectiveTagWithPopularShopPrivilege() throws Exception {
        MerchantPrivilegeRecordDTO popularShopRecord = new MerchantPrivilegeRecordDTO();
        popularShopRecord.setPrivilegeCode(PrivilegeCodeEnum.POPULAR_SHOP.getCode());
        MerchantPrivilegeRecordDTO nonPopularShopRecord = new MerchantPrivilegeRecordDTO();
        nonPopularShopRecord.setPrivilegeCode("NON_POPULAR_SHOP");
        Map<Long, List<MerchantPrivilegeRecordDTO>> privilegeMap = new HashMap<>();
        privilegeMap.put(1L, Arrays.asList(popularShopRecord, nonPopularShopRecord));

        when(privilegeDeliveryRecordLocalService.batchQueryEffectivePrivilegeByShopId(anyList(), anyInt()))
                .thenReturn(privilegeMap);

        RemoteResponse<Map<Long, List<MerchantPrivilegeRecordDTO>>> response = service.batchQueryEffectiveTag(Arrays.asList(1L), 1);
        assertTrue(response.isSuccess());
        assertEquals(1, response.getData().get(1L).size());
        assertEquals(PrivilegeCodeEnum.POPULAR_SHOP.getCode(), response.getData().get(1L).get(0).getPrivilegeCode());
    }

    /**
     * 测试batchQueryEffectiveTag方法，当内部方法抛出异常时
     */
    @Test
    public void testBatchQueryEffectiveTagThrowsException() throws Exception {
        when(privilegeDeliveryRecordLocalService.batchQueryEffectivePrivilegeByShopId(anyList(), anyInt()))
                .thenThrow(new RuntimeException("Mock Exception"));

        RemoteResponse<Map<Long, List<MerchantPrivilegeRecordDTO>>> response = service.batchQueryEffectiveTag(Arrays.asList(1L), 1);
        assertFalse(response.isSuccess());
        assertEquals("Mock Exception", response.getMsg());
    }
}
