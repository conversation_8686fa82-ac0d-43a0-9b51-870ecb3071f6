package com.sankuai.carnation.distribution.distributionplan.service.impl.strategy;

import com.sankuai.carnation.distribution.distributionplan.acl.DealProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductCommissionBasisAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealProductInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisInfo;
import com.sankuai.carnation.distribution.distributionplan.enums.CommissionBasisTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.MerchantChannelDistributionPlanServiceImpl;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.ModifyDistributionPlanCommissionStrategy;
import com.sankuai.carnation.distribution.distributionplan.service.model.MerchantChannelDistributionPlanOperateContext;
import com.sankuai.carnation.distribution.distributionplan.utils.DateUtils;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanSubjectDTO;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ModifyDistributionPlanCommissionStrategyTest {

    @Mock
    private DealProductAcl dealProductAcl;

    @Mock
    private ProductCommissionBasisAcl productCommissionBasisAcl;

    @InjectMocks
    private ModifyDistributionPlanCommissionStrategy strategy;

    private MerchantChannelDistributionPlanOperateContext context;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        DistributionPlanDTO distributionPlan = new DistributionPlanDTO();
        distributionPlan.setSubject(new DistributionPlanSubjectDTO(1L, 1, "100", null, null));
        context = MerchantChannelDistributionPlanOperateContext.builder()
                .distributionPlan(distributionPlan)
                .operateDate(new Date())
                .commissionRate(new BigDecimal("0.1"))
                .build();
    }

    /**
     * 测试结束时间早于开始时间的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testValidatePlanCanOperate_EndTimeBeforeBeginTime() {
        context.getDistributionPlan().setEndTime(new Date(System.currentTimeMillis() - 10000));
        context.getDistributionPlan().setBeginTime(new Date());

        strategy.validatePlanCanOperate(context);
    }

    /**
     * 测试结束时间等于操作时间的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testValidatePlanCanOperate_EndTimeEqualsOperateTime() {
        Date now = new Date();
        context.getDistributionPlan().setEndTime(now);
        context.getDistributionPlan().setBeginTime(new Date(now.getTime() - 10000));
        context.setOperateDate(now);

        strategy.validatePlanCanOperate(context);
    }

    /**
     * 测试结束时间早于操作时间的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testValidatePlanCanOperate_EndTimeBeforeOperateTime() {
        Date now = new Date();
        context.getDistributionPlan().setEndTime(DateUtils.addDay(now, -1));
        context.getDistributionPlan().setBeginTime(new Date(now.getTime() - 10000));
        context.setOperateDate(now);

        strategy.validatePlanCanOperate(context);
    }

    /**
     * 测试商品信息不存在的异常情况
     */
    @Test(expected = DistributionPlanException.class)
    public void testValidatePlanCanOperate_DealProductInfoNotFound() {
        context.getDistributionPlan().setEndTime(DateUtils.addDay(new Date(), 2));
        context.getDistributionPlan().setBeginTime(new Date());

        when(dealProductAcl.queryMtDealProductToMap(any())).thenReturn(new HashMap<>());

        strategy.validatePlanCanOperate(context);
    }

    /**
     * 测试佣金基础信息不存在的异常情况
     */
    @Test(expected = DistributionPlanException.class)
    public void testValidatePlanCanOperate_CommissionBasisInfoNotFound() {
        context.getDistributionPlan().setEndTime(DateUtils.addDay(new Date(), 2));
        context.getDistributionPlan().setBeginTime(new Date());

        Map<Long, DealProductInfo> dealProductInfoMap = new HashMap<>();
        dealProductInfoMap.put(1L, new DealProductInfo());
        when(dealProductAcl.queryMtDealProductToMap(any())).thenReturn(dealProductInfoMap);
        when(productCommissionBasisAcl.batchQueryProductCommissionBasis(any())).thenReturn(Collections.emptyList());

        strategy.validatePlanCanOperate(context);
    }

    /**
     * 测试佣金率超出规定范围的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testValidatePlanCanOperate_CommissionRateOutOfRange() {
        context.getDistributionPlan().setEndTime(DateUtils.addDay(new Date(), 2));
        context.getDistributionPlan().setBeginTime(new Date());
        context.setCommissionRate(new BigDecimal("61")); // 假设超出范围

        Map<Long, DealProductInfo> dealProductInfoMap = new HashMap<>();
        DealProductInfo dealProductInfo = new DealProductInfo();
        dealProductInfo.setMtProductId(100L);
        dealProductInfo.setDpProductId(20L);
        dealProductInfoMap.put(100L, dealProductInfo);
        when(dealProductAcl.queryMtDealProductToMap(any())).thenReturn(dealProductInfoMap);

        List<ProductCommissionBasisInfo> commissionBasisInfoList = Lists.newArrayList(
                new ProductCommissionBasisInfo(20L, CommissionBasisTypeEnum.COMMISSION_GROUP)
        );
        when(productCommissionBasisAcl.batchQueryProductCommissionBasis(any())).thenReturn(commissionBasisInfoList);
        strategy.validatePlanCanOperate(context);
    }

    /**
     * 测试正常情况下的成功验证
     */
    @Test
    public void testValidatePlanCanOperate_Success() {
        // 设置未来的结束时间
        context.getDistributionPlan().setEndTime(DateUtils.addDay(new Date(), 7));
        context.getDistributionPlan().setBeginTime(new Date());
        context.setCommissionRate(new BigDecimal("50")); // 假设这是允许范围内的佣金率

        // 模拟DealProductAcl返回结果
        Map<Long, DealProductInfo> dealProductInfoMap = new HashMap<>();
        dealProductInfoMap.put(100L, new DealProductInfo());
        when(dealProductAcl.queryMtDealProductToMap(any())).thenReturn(dealProductInfoMap);

        // 模拟ProductCommissionBasisAcl返回结果
        List<ProductCommissionBasisInfo> commissionBasisInfoList = Lists.newArrayList(
                new ProductCommissionBasisInfo(100L, CommissionBasisTypeEnum.COMMISSION_GROUP)
        );
        when(productCommissionBasisAcl.batchQueryProductCommissionBasis(any())).thenReturn(commissionBasisInfoList);

        // 模拟isCommissionRateOutOfRange方法返回false
        Mockito.mockStatic(MerchantChannelDistributionPlanServiceImpl.class);
        when(MerchantChannelDistributionPlanServiceImpl.isCommissionRateOutOfRange(any(), any())).thenReturn(false);

        // 执行验证
        strategy.validatePlanCanOperate(context);
        // 如果没有抛出异常，则测试通过
    }
}
