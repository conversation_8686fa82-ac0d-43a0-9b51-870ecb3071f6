package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff;

import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.handler.RebateAmountCalcHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class StaffRebateAmountCalcServiceTest {

    @InjectMocks
    private StaffRebateAmountCalcService service;

    @Mock
    private DistributorActivityService distributorActivityService;

    @Mock
    private List<RebateAmountCalcHandler> rebateAmountCalcHandlers;


    /**
     * 测试查询活动规则失败的场景
     */
    @Test
    public void testCalculateQueryActivityRuleFail() {
        // arrange
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.bizError("查询失败");
//        when(distributorActivityService.queryActivityRule(any())).thenReturn(commonResponse);

        // act
        RemoteResponse<RebateAmountResult> result = service.calculate(new RebateAmountCalcRequest());

        // assert
        assertTrue(result.isSuccess());
        assertEquals("下单时无有效报名记录！", result.getData().getReason());
    }

    /**
     * 测试找不到对应处理器的场景
     */
    @Test
    public void testCalculateFindHandlerFail() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setTechApplyRecordId(1L);
        CommonResponse commonResponse = new CommonResponse();
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rule.setType(0);
        rebateActivityRuleDTO.setRule(rule);
        commonResponse.success(rebateActivityRuleDTO);
        when(distributorActivityService.queryActivityRule(any())).thenReturn(commonResponse);


        // act
        RemoteResponse<RebateAmountResult> result = service.calculate(request);

        // assert
        assertTrue(result.isSuccess());
        assertEquals("不支持对应职人返利规则", result.getData().getReason());
    }

    /**
     * 测试正常处理的场景
     */
    @Test
    public void testCalculateSuccess() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setTechApplyRecordId(1L);

        CommonResponse commonResponse = new CommonResponse();
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rule.setType(1);
        rebateActivityRuleDTO.setRule(rule);
        commonResponse.success(rebateActivityRuleDTO);

//        when(distributorActivityService.queryActivityRule(any())).thenReturn(commonResponse);

        int channel = 1;
        RebateAmountCalcHandler mockHandler = Mockito.mock(RebateAmountCalcHandler.class);
//        Mockito.when(mockHandler.supportType(channel)).thenReturn(true);

        RemoteResponse<RebateAmountResult> remoteResponse = RemoteResponse.success(new RebateAmountResult());

//        Mockito.when(mockHandler.process(any(),any())).thenReturn(remoteResponse);

//        Mockito.when(rebateAmountCalcHandlers.stream()).thenReturn(Stream.of(mockHandler));


        // act
        RemoteResponse<RebateAmountResult> result = service.calculate(new RebateAmountCalcRequest());

        // assert
        assertTrue(result.isSuccess());
    }

    @Test
    public void testFindHandler() {
        // 假设channel为1时，有一个handler支持处理
        int channel = 1;
        RebateAmountCalcHandler mockHandler = Mockito.mock(RebateAmountCalcHandler.class);
        Mockito.when(mockHandler.supportType(channel)).thenReturn(true);

        // 模拟rebateAmountCalcHandlers只有一个支持channel为1的handler
        Mockito.when(rebateAmountCalcHandlers.stream()).thenReturn(Stream.of(mockHandler));

        // 调用待测试的方法
        RebateAmountCalcHandler resultHandler = service.findHandler(channel);

        // 验证结果
        assertNotNull(resultHandler);
        assertTrue(resultHandler.supportType(channel));
    }

}
