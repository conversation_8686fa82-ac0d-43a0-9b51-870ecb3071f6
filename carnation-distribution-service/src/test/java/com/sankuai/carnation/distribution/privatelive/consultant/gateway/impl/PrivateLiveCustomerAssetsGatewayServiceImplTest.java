package com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl;

import com.dianping.tp.deal.data.dto.DealGroupQueryDTO;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.product.ProductAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.converter.IntentionProductDTOConverter;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.testng.AssertJUnit.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveCustomerAssetsGatewayServiceImplTest {

    @InjectMocks
    private PrivateLiveCustomerAssetsGatewayServiceImpl privateLiveCustomerAssetsGatewayService;

    @Mock
    private ProductAclService productAclService;

    @Mock
    private PrivateLiveConsultantSummaryService privateLiveConsultantSummaryService;

    @Mock
    private UserAuthorizeUtil userAuthorizeUtil;

    /**
     * Test the pageList method when the response from the service is null.
     *
     * @throws Throwable
     */
    @Test
    public void testPageListWhenResponseIsNull() throws Throwable {
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        PaginationRemoteResponse<CustomerAssetsDTO> result = privateLiveCustomerAssetsGatewayService.pageList(request);
        assertNotNull(result);
    }


    /**
     * 测试buildProductBasicsInfo方法，当传入的UserPortraitDTO中IntentionProduct的ProductDetails包含团购商品信息时
     */
    @Test
    public void testBuildProductBasicsInfoWithGroupBuyProductDetails() throws Throwable {
        // arrange
        ProductDetailsDTO productDetailsDTO = ProductDetailsDTO.builder().productType(ProductTypeEnum.GROUP_BUY.getValue()).productId(22222L).interviewTime("2024-09-04 08:56:33").actionType(5).viewProductTimes(10).intentionTypeTag("浏览5次").build();
        List<ProductDetailsDTO> productDetails = Lists.newArrayList(productDetailsDTO);
        IntentionProductDTO intentionProductDTO = IntentionProductDTO.builder().productDetails(productDetails).build();
        UserPortraitDTO userPortraitDTO = new UserPortraitDTO();
        userPortraitDTO.setIntentionProduct(intentionProductDTO);
        DealGroupQueryDTO dealGroupQueryDTO = new DealGroupQueryDTO();
        dealGroupQueryDTO.setId(22222);
        List<DealGroupQueryDTO> dealGroupQueryDTOs = Lists.newArrayList(dealGroupQueryDTO);
        when(productAclService.queryByDealGroupByDealGroupIds(any())).thenReturn(dealGroupQueryDTOs);
        try (MockedStatic<IntentionProductDTOConverter> mockedStatic = mockStatic(IntentionProductDTOConverter.class)) {
            // 配置静态方法的期望行为
            mockedStatic.when(() -> IntentionProductDTOConverter.converterByGroupBuy(ArgumentMatchers.<DealGroupQueryDTO>anyList(), ArgumentMatchers.<Long, String>anyMap())).thenReturn(productDetails);
            // act
            UserPortraitDTO result = privateLiveCustomerAssetsGatewayService.buildProductBasicsInfo(userPortraitDTO);
            // assert
            assertNotNull(result);
        }
    }

    /**
     * 测试pageList方法当用户鉴权信息为空时的情况。
     */
    @Test
    public void testPageListWhenUserAuthInfoIsNull() throws Throwable {
        // arrange
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("testToken");

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenReturn(null);

        // act
        PaginationRemoteResponse<CustomerAssetsDTO> result = privateLiveCustomerAssetsGatewayService.pageList(request);

        // assert
        assertNotNull(result);
        assertEquals(0, result.getTotalHit());
    }


    /**
     * 测试pageList方法当发生异常时的情况。
     */
    @Test
    public void testPageListWhenExceptionOccurs() throws Throwable {
        // arrange
        CustomerAssetsRequest request = new CustomerAssetsRequest();
        request.setTaskId(1L);
        request.setWxToken("testToken");

        when(userAuthorizeUtil.authentication(any(Long.class), any(String.class))).thenThrow(new RuntimeException("Test Exception"));

        // act
        PaginationRemoteResponse<CustomerAssetsDTO> response = privateLiveCustomerAssetsGatewayService.pageList(request);

        assertNotNull(response);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
}
