package com.sankuai.carnation.distribution.privatelive.consultant.acl.service;

import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.baseinfo.RemoteResponse;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveproductshelf.ProductLinkUrlDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.livetrade.ProductUniqueDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.request.shelf.LiveProductQueryRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveproductshelf.LiveProductShelfRpcService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class PrivateLiveWxAclServiceTest {

    @InjectMocks
    private PrivateLiveWxAclService privateLiveWxAclService;

    @Mock
    private LiveProductShelfRpcService liveProductShelfRpcService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试参数为空的场景
     */
    @Test
    public void testQueryProductLinkUrlWithNullParams() {
        ProductLinkUrlDTO result = privateLiveWxAclService.queryProductLinkUrl(null, null, null, null);
        assertNull("应当返回null，因为参数为空", result);
    }

    /**
     * 测试RPC服务响应为空的场景
     */
    @Test
    public void testQueryProductLinkUrlWithEmptyResponse() {
        when(liveProductShelfRpcService.batchQueryProductLinkUrl(any(LiveProductQueryRequest.class))).thenReturn(null);
        ProductLinkUrlDTO result = privateLiveWxAclService.queryProductLinkUrl("liveId", "distributionParam", 1, 1L);
        assertNull("应当返回null，因为RPC响应为空", result);
    }

    /**
     * 测试RPC服务响应失败的场景
     */
    @Test
    public void testQueryProductLinkUrlWithFailedResponse() {
        RemoteResponse<Map<ProductUniqueDTO, ProductLinkUrlDTO>> resp = RemoteResponse.fail(500, "error", null);
        when(liveProductShelfRpcService.batchQueryProductLinkUrl(any(LiveProductQueryRequest.class))).thenReturn(resp);
        ProductLinkUrlDTO result = privateLiveWxAclService.queryProductLinkUrl("liveId", "distributionParam", 1, 1L);
        assertNull("应当返回null，因为RPC响应失败", result);
    }

    /**
     * 测试RPC服务响应成功但数据为空的场景
     */
    @Test
    public void testQueryProductLinkUrlWithEmptyData() {
        RemoteResponse<Map<ProductUniqueDTO, ProductLinkUrlDTO>> resp = RemoteResponse.success(Collections.emptyMap());
        when(liveProductShelfRpcService.batchQueryProductLinkUrl(any(LiveProductQueryRequest.class))).thenReturn(resp);
        ProductLinkUrlDTO result = privateLiveWxAclService.queryProductLinkUrl("liveId", "distributionParam", 1, 1L);
        assertNull("应当返回null，因为RPC响应成功但数据为空", result);
    }

    /**
     * 测试RPC服务响应成功且数据非空的正常场景
     */
    @Test
    public void testQueryProductLinkUrlWithSuccess() {
        Map<ProductUniqueDTO, ProductLinkUrlDTO> data = new HashMap<>();
        ProductLinkUrlDTO expected = new ProductLinkUrlDTO();
        expected.setDetailJumpUrl("http://example.com");
        data.put(new ProductUniqueDTO(), expected);
        RemoteResponse<Map<ProductUniqueDTO, ProductLinkUrlDTO>> resp = RemoteResponse.success(data);
        when(liveProductShelfRpcService.batchQueryProductLinkUrl(any(LiveProductQueryRequest.class))).thenReturn(resp);
        ProductLinkUrlDTO result = privateLiveWxAclService.queryProductLinkUrl("liveId", "distributionParam", 1, 1L);
        assertNotNull("应当返回非null，因为RPC响应成功且数据非空", result);
        assertEquals("返回的ProductLinkUrlDTO对象应当与预期相符", expected, result);
    }
}
