package com.sankuai.carnation.distribution.distributionplan.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryDistributorGroupByPlanResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLivePlanDistributorGroupAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.service.PrivateLivePlanDistributorGroupServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class PrivateLivePlanDistributorGroupServiceImplTest {

    @Mock
    private PrivateLivePlanDistributorGroupAppService privateLivePlanDistributorGroupAppService;

    @InjectMocks
    private PrivateLivePlanDistributorGroupServiceImpl privateLivePlanDistributorGroupServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当返回空列表时
     */
    @Test
    public void testQueryPrivateLiveDistributorGroupListReturnsEmptyList() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        QueryDistributorGroupByPlanResponse mockResponse = new QueryDistributorGroupByPlanResponse(0L, new ArrayList<>());

        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenReturn(mockResponse);

        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);

        assertNotNull(response);
        assertTrue(response.getData().getDistributorGroupList().isEmpty());

        verify(privateLivePlanDistributorGroupAppService, times(1)).queryDistributorGroupByPlan(any());
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当返回非空列表时
     */
    @Test
    public void testQueryPrivateLiveDistributorGroupListReturnsNonEmptyList() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");
        List<QueryDistributorGroupByPlanResponse.DistributorGroup> distributorGroups = new ArrayList<>();
        distributorGroups.add(new QueryDistributorGroupByPlanResponse.DistributorGroup());
        QueryDistributorGroupByPlanResponse mockResponse = new QueryDistributorGroupByPlanResponse(1L, distributorGroups);

        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenReturn(mockResponse);

        RemoteResponse<QueryDistributorGroupByPlanResponse> response = privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);

        assertNotNull(response);
        assertFalse(response.getData().getDistributorGroupList().isEmpty());
        assertEquals(1, response.getData().getDistributorGroupList().size());

        verify(privateLivePlanDistributorGroupAppService, atLeastOnce()).queryDistributorGroupByPlan(any());
    }

    /**
     * 测试queryPrivateLiveDistributorGroupList方法，当AppService抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryPrivateLiveDistributorGroupListThrowsException() {
        PrivateLiveDistributorGroupRequest request = new PrivateLiveDistributorGroupRequest("liveId");

        when(privateLivePlanDistributorGroupAppService.queryDistributorGroupByPlan(any())).thenThrow(new RuntimeException());

        privateLivePlanDistributorGroupServiceImpl.queryPrivateLiveDistributorGroupList(request);
    }
}
