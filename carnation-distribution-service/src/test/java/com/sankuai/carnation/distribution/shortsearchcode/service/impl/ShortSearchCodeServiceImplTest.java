package com.sankuai.carnation.distribution.shortsearchcode.service.impl;

import com.dianping.gmkt.event.api.promoqrcode.dto.SubCodeRelationDTO;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.enums.PromoQRCodeResCode;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.shortsearchcode.dto.request.MiniCodeDetailRequest;
import com.sankuai.carnation.distribution.minicode.domain.bo.MiniCodeBO;
import com.sankuai.carnation.distribution.minicode.domain.tech.promocode.TechPromoCodeMiniCodeDomainService;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeForCService;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Test cases for ShortSearchCodeServiceImpl.getCodeJumpUrl method
 */
@RunWith(MockitoJUnitRunner.class)
public class ShortSearchCodeServiceImplTest {

    @InjectMocks
    private ShortSearchCodeServiceImpl shortSearchCodeService;

    @Mock
    private TechPromoCodeMiniCodeDomainService techPromoCodeMiniCodeDomainService;

    @Mock
    private PromoQRCodeCService promoQRCodeCService;

    @Mock
    private ShopMapperService shopMapperService;

    @Mock
    private PromoCodeForCService promoCodeForCService;

    @Mock
    private UniversalQRCodeGeneratorService universalQRCodeGeneratorService;

    /**
     * Test successful case with valid request
     */
    @Test
    public void testGetCodeJumpUrl_ValidRequest_Success() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("validShortCode");
        request.setPlatform(1);
        request.setCityId(1);
        MiniCodeBO miniCodeBO = new MiniCodeBO();
        miniCodeBO.setBindUserId(123L);
        when(techPromoCodeMiniCodeDomainService.loadByCodeAndCity("validShortCode", 1, 1)).thenReturn(Collections.singletonList(miniCodeBO));
        SubCodeRelationDTO subCodeRelationDTO = new SubCodeRelationDTO();
        subCodeRelationDTO.setSubCodeId(456L);
        List<SubCodeRelationDTO> subCodeRelationDTOs = Collections.singletonList(subCodeRelationDTO);
        when(promoQRCodeCService.getSubCodeRelationByExternal(1, "123")).thenReturn(new PromoQRCodeResponse<>(PromoQRCodeResCode.SUCCESS, subCodeRelationDTOs));
        StaffCodeDTO staffCodeDTO = new StaffCodeDTO();
        staffCodeDTO.setId(456L);
        staffCodeDTO.setDpShopIdLong(123L);
        staffCodeDTO.setStatus(1);
        when(promoQRCodeCService.getStaffCodeDTOByCodeId(456L)).thenReturn(new PromoQRCodeResponse<>(PromoQRCodeResCode.SUCCESS, staffCodeDTO));
        when(shopMapperService.dp2mt(123L)).thenReturn(789L);
        QRCodeConfigDTO qrCodeConfig = new QRCodeConfigDTO();
        qrCodeConfig.setSecretKey("testKey");
        when(universalQRCodeGeneratorService.queryQRCode(456L, "456", BizTypeEnum.PROMO_STAFF_CODE.getType())).thenReturn(RemoteResponse.success(qrCodeConfig));
        PromoCodeLandingPageRequest landingPageRequest = new PromoCodeLandingPageRequest();
        landingPageRequest.setCodeKey("testKey");
        landingPageRequest.setPoiId(789L);
        landingPageRequest.setStaffCodeId(456L);
        when(promoCodeForCService.queryPromoCodeLandingPageUrl(any(PromoCodeLandingPageRequest.class))).thenReturn(RemoteResponse.success("http://example.com"));
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("http://example.com", response.getData());
    }

    /**
     * Test case for null request
     */
    @Test
    public void testGetCodeJumpUrl_NullRequest() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = null;
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("参数非法", response.getData());
    }

    /**
     * Test case for blank short code
     */
    @Test
    public void testGetCodeJumpUrl_BlankShortCode() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("");
        request.setPlatform(1);
        request.setCityId(1);
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("参数非法", response.getData());
    }

    /**
     * Test case for invalid platform
     */
    @Test
    public void testGetCodeJumpUrl_InvalidPlatform() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("validShortCode");
        request.setPlatform(0);
        request.setCityId(1);
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("参数非法", response.getData());
    }

    /**
     * Test case for invalid city ID
     */
    @Test
    public void testGetCodeJumpUrl_InvalidCityId() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("validShortCode");
        request.setPlatform(1);
        request.setCityId(0);
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("参数非法", response.getData());
    }

    /**
     * Test case for no mini code found
     */
    @Test
    public void testGetCodeJumpUrl_NoMiniCodeFound() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("validShortCode");
        request.setPlatform(1);
        request.setCityId(1);
        when(techPromoCodeMiniCodeDomainService.loadByCodeAndCity("validShortCode", 1, 1)).thenReturn(Collections.emptyList());
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("未找到对应的口令码信息", response.getMsg());
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testGetCodeJumpUrl_ExceptionHandling() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("validShortCode");
        request.setPlatform(1);
        request.setCityId(1);
        when(techPromoCodeMiniCodeDomainService.loadByCodeAndCity("validShortCode", 1, 1)).thenThrow(new RuntimeException("Test Exception"));
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("服务器错误", response.getMsg());
    }

    /**
     * Test case for no MT shop ID found
     */
    @Test
    public void testGetCodeJumpUrl_NoMTShopIdFound() throws Throwable {
        // arrange
        MiniCodeDetailRequest request = new MiniCodeDetailRequest();
        request.setShortCode("validShortCode");
        request.setPlatform(1);
        request.setCityId(1);
        MiniCodeBO miniCodeBO = new MiniCodeBO();
        miniCodeBO.setBindUserId(123L);
        when(techPromoCodeMiniCodeDomainService.loadByCodeAndCity("validShortCode", 1, 1)).thenReturn(Collections.singletonList(miniCodeBO));
        SubCodeRelationDTO subCodeRelationDTO = new SubCodeRelationDTO();
        subCodeRelationDTO.setSubCodeId(456L);
        List<SubCodeRelationDTO> subCodeRelationDTOs = Collections.singletonList(subCodeRelationDTO);
        when(promoQRCodeCService.getSubCodeRelationByExternal(1, "123")).thenReturn(new PromoQRCodeResponse<>(PromoQRCodeResCode.SUCCESS, subCodeRelationDTOs));
        StaffCodeDTO staffCodeDTO = new StaffCodeDTO();
        staffCodeDTO.setId(456L);
        staffCodeDTO.setDpShopIdLong(123L);
        staffCodeDTO.setStatus(1);
        when(promoQRCodeCService.getStaffCodeDTOByCodeId(456L)).thenReturn(new PromoQRCodeResponse<>(PromoQRCodeResCode.SUCCESS, staffCodeDTO));
        when(shopMapperService.dp2mt(123L)).thenReturn(null);
        // act
        RemoteResponse<String> response = shortSearchCodeService.getCodeJumpUrl(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("未找到对应的MT门店ID", response.getMsg());
    }
}
