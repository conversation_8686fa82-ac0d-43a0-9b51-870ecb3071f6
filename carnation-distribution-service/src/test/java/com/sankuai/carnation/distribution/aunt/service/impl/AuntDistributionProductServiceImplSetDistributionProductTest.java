package com.sankuai.carnation.distribution.aunt.service.impl;

import com.sankuai.carnation.distribution.aunt.domain.AuntDistributionProductDomainService;
import com.sankuai.carnation.distribution.aunt.dto.AuntDistributionInfoDTO;
import com.sankuai.carnation.distribution.aunt.dto.AuntDistributionProductInfoDTO;
import com.sankuai.carnation.distribution.aunt.exception.AuntDistributionException;
import com.sankuai.carnation.distribution.product.v2.domain.ProductItemDomainService;
import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AuntDistributionProductServiceImplSetDistributionProductTest {

    @InjectMocks
    private AuntDistributionProductServiceImpl auntDistributionProductService;

    @Mock
    private AuntDistributionProductDomainService productDomainService;

    @Mock
    private ProductItemDomainService itemDomainService;

    private AuntDistributionInfoDTO distributionInfoDTO;

    private ProductItemBO productItemBO;

    @Before
    public void setUp() {
        distributionInfoDTO = new AuntDistributionInfoDTO();
        productItemBO = new ProductItemBO();
    }

    @Test
    public void testSetDistributionProductNullInput() throws Throwable {
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(null);
        assertFalse(response.isSuccess());
        assertEquals("设置分销商品为空", response.getMsg());
    }

    @Test
    public void testSetDistributionProductEmptyProductInfoList() throws Throwable {
        when(itemDomainService.getValidProductItemByBiz(anyString(), anyString())).thenReturn(null);
        distributionInfoDTO.setProductInfoList(Collections.emptyList());
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(distributionInfoDTO);
        assertTrue(response.isSuccess());
        assertTrue(response.getData());
    }

    @Test
    public void testSetDistributionProductNonEmptyProductInfoList() throws Throwable {
        when(itemDomainService.getValidProductItemByBiz(anyString(), anyString())).thenReturn(null);
        AuntDistributionProductInfoDTO productInfoDTO = new AuntDistributionProductInfoDTO();
        distributionInfoDTO.setProductInfoList(Collections.singletonList(productInfoDTO));
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(distributionInfoDTO);
        // Assuming the expected behavior is to fail due to the non-empty product info list without valid product item
        assertFalse(response.isSuccess());
    }

    @Test
    public void testSetDistributionProductSuccess() throws Throwable {
        when(itemDomainService.getValidProductItemByBiz(anyString(), anyString())).thenReturn(productItemBO);
        when(productDomainService.setDistributionProduct(distributionInfoDTO)).thenReturn(1L);
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(distributionInfoDTO);
        assertTrue(response.isSuccess());
        assertTrue(response.getData());
    }

    @Test
    public void testSetDistributionProductFail() throws Throwable {
        when(itemDomainService.getValidProductItemByBiz(anyString(), anyString())).thenReturn(productItemBO);
        when(productDomainService.setDistributionProduct(distributionInfoDTO)).thenReturn(0L);
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(distributionInfoDTO);
        assertFalse(response.isSuccess());
        assertEquals("设置分销商品失败", response.getMsg());
    }

    @Test
    public void testSetDistributionProductAuntDistributionException() throws Throwable {
        when(itemDomainService.getValidProductItemByBiz(anyString(), anyString())).thenReturn(productItemBO);
        when(productDomainService.setDistributionProduct(distributionInfoDTO)).thenThrow(new AuntDistributionException("AuntDistributionException"));
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(distributionInfoDTO);
        assertFalse(response.isSuccess());
        assertEquals("AuntDistributionException", response.getMsg());
    }

    @Test
    public void testSetDistributionProductOtherException() throws Throwable {
        when(itemDomainService.getValidProductItemByBiz(anyString(), anyString())).thenReturn(productItemBO);
        when(productDomainService.setDistributionProduct(distributionInfoDTO)).thenThrow(new RuntimeException());
        RemoteResponse<Boolean> response = auntDistributionProductService.setDistributionProduct(distributionInfoDTO);
        assertFalse(response.isSuccess());
        assertEquals("设置分销商品失败", response.getMsg());
    }
}
