package com.sankuai.carnation.distribution.distributor.appication.impl;

import com.sankuai.carnation.distribution.distributor.assembler.DistributorGroupVOAssembler;
import com.sankuai.carnation.distribution.distributor.converter.DistributorGroupAttrConverter;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.*;
import com.sankuai.carnation.distribution.distributor.domain.service.DistributorGroupDomainService;
import com.sankuai.carnation.distribution.distributor.enums.DistributorBindUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindRequest;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindResponse;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.repository.DistributorRepository;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.MobileHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PageQueryDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.PageQueryDistributorGroupResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DistributorGroupAppServiceImplTest {

    @Mock
    private DistributorGroupRootService distributorGroupRootService;

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private DistributorGroupDomainService distributorGroupDomainService;

    @InjectMocks
    private DistributorGroupAppServiceImpl distributorGroupAppService;

    private DistributorBindRequest request;

    private DistributorGroupBO distributorGroupBO;

    private DistributorBO existingDistributorBO;

    private DistributorGroupBind distributorGroupBind;

    @Mock
    private DistributorGroupAttrConverter distributorGroupAttrConverter;

    @Mock
    private DistributorGroupVOAssembler distributorGroupVOAssembler;

    private final Long validGroupId = 1L;

    private final Long invalidGroupId = -1L;

    @InjectMocks
    private DistributorGroupAppServiceImpl service;

    @Mock
    private DistributorRepository distributorRepository;

    private List<Long> accountIdList;

    private List<DistributorBindBO> distributorBOList;

    private List<DistributorGroupBO> distributorGroupBOList;

    private List<DistributorGroupModel> distributorGroupModelList;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;

    @Mock
    private MobileHelper mobileHelper;

    @Before
    public void setUp() {
        request = DistributorBindRequest.builder().distributorGroupId(1L).userId(1L).build();
        distributorGroupBO = DistributorGroupBO.builder().groupId(1).build();
        existingDistributorBO = new DistributorBO();
        existingDistributorBO.setDistributorId(1L);
        distributorGroupBind = DistributorGroupBind.builder().status(DistributionApproveStatusEnum.PASS.getCode()).build();
    }

    /**
     * 测试分销商团队不存在的情况
     */
    @Test(expected = BizSceneException.class)
    public void testBindDistributor_GroupNotExist() {
        when(distributorGroupRootService.getDistributorGroup(any())).thenReturn(null);
        distributorGroupAppService.bindDistributor(request);
    }

    /**
     * 测试分销商已存在且正在审核中的情况
     */
    @Test(expected = BizSceneException.class)
    public void testBindDistributor_DistributorExistsAndWaiting() {
        when(distributorGroupRootService.getDistributorGroup(any())).thenReturn(distributorGroupBO);
        when(distributorRootService.getValidDistributor(anyInt(), anyLong())).thenReturn(existingDistributorBO);
        distributorGroupBind.setStatus(DistributionApproveStatusEnum.WAITING.getCode());
        when(distributorRootService.getDistributorGroupBind(anyLong())).thenReturn(distributorGroupBind);
        distributorGroupAppService.bindDistributor(request);
    }

    /**
     * 测试分销商已存在且已通过审核的情况
     */
    @Test(expected = BizSceneException.class)
    public void testBindDistributor_DistributorExistsAndApproved() {
        when(distributorGroupRootService.getDistributorGroup(any())).thenReturn(distributorGroupBO);
        when(distributorRootService.getValidDistributor(anyInt(), anyLong())).thenReturn(existingDistributorBO);
        when(distributorRootService.getDistributorGroupBind(anyLong())).thenReturn(distributorGroupBind);
        distributorGroupAppService.bindDistributor(request);
    }



    /**
     * 测试getDistributorGroup方法，当传入无效的distributorGroupId时
     */
    @Test(expected = BizSceneException.class)
    public void testGetDistributorGroupWithInvalidId() {
        // arrange
        when(distributorGroupRootService.getDistributorGroup(any(Integer.class))).thenReturn(null);
        // act
        distributorGroupAppService.getDistributorGroup(invalidGroupId);
        // assert
        // Expected exception
    }


    /**
     * 测试getDistributorGroup方法，当DistributorGroupBO包含空的distributorGroupAttrList时
     */
    @Test
    public void testGetDistributorGroupWithEmptyAttrList() {
        // arrange
        Long distributorGroupId = 1L;
        DistributorGroupBO mockDistributorGroupBO = new DistributorGroupBO();
        mockDistributorGroupBO.setGroupId(1);
        mockDistributorGroupBO.setGroupName("Test Group");
        mockDistributorGroupBO.setDistributorGroupAttrList(Collections.emptyList());
        when(distributorGroupRootService.getDistributorGroup(any(Integer.class))).thenReturn(mockDistributorGroupBO);
        when(distributorGroupAttrConverter.toEntity(any())).thenCallRealMethod();
        when(distributorGroupVOAssembler.buildDistributorGroupModel(any())).thenCallRealMethod();
        // act
        DistributorGroupModel result = distributorGroupAppService.getDistributorGroup(distributorGroupId);
        // assert
        assertNotNull(result);
        //        assertTrue(result.getDistributorGroupAttrList().isEmpty());
    }

    /**
     * 测试getDistributorGroup方法，当DistributorGroupBO的distributorGroupAttrList包含null元素时
     */
    @Test
    public void testGetDistributorGroupWithNullInAttrList() {
        // arrange
        Long distributorGroupId = 1L;
        DistributorGroupBO mockDistributorGroupBO = new DistributorGroupBO();
        mockDistributorGroupBO.setGroupId(1);
        mockDistributorGroupBO.setGroupName("Test Group");
        List<DistributorGroupAttrBO> attrList = new ArrayList<>();
        attrList.add(null);
        mockDistributorGroupBO.setDistributorGroupAttrList(attrList);
        when(distributorGroupRootService.getDistributorGroup(any(Integer.class))).thenReturn(mockDistributorGroupBO);
        when(distributorGroupVOAssembler.buildDistributorGroupModel(any())).thenCallRealMethod();
        // act
        DistributorGroupModel result = distributorGroupAppService.getDistributorGroup(distributorGroupId);
        // assert
        assertNotNull(result);
        //        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试 getDistributorGroupByAccountId 方法，当 distributorGroupRootService 返回 null 时
     */
    @Test
    public void testGetDistributorGroupByAccountId_ReturnNull() {
        Long accountId = 1L;
        //        when(distributorGroupRootService.getValidDistributorGroup(anyString(), eq(accountId))).thenReturn(null);
        DistributorGroupModel result = distributorGroupAppService.getDistributorGroupByAccountId(accountId);
        assertNull(result);
        //        verify(distributorGroupRootService, times(1)).getValidDistributorGroup(anyString(), eq(accountId));
        verify(distributorGroupVOAssembler, never()).buildDistributorGroupModel(any(DistributorGroupBO.class));
    }

    /**
     * 测试 getDistributorGroupByAccountId 方法，当 distributorGroupRootService 返回有效对象时
     */
    @Test
    public void testGetDistributorGroupByAccountId_ReturnValidObject() {
        Long accountId = 1L;
        DistributorGroupBO distributorGroupBO = mock(DistributorGroupBO.class);
        DistributorGroupModel expectedModel = mock(DistributorGroupModel.class);
        when(distributorGroupRootService.getValidDistributorGroup(anyInt(), eq(accountId))).thenReturn(distributorGroupBO);
        when(distributorGroupVOAssembler.buildDistributorGroupModel(distributorGroupBO)).thenReturn(expectedModel);
        DistributorGroupModel result = distributorGroupAppService.getDistributorGroupByAccountId(accountId);
        assertNotNull(result);
        assertEquals(expectedModel, result);
        verify(distributorGroupRootService, times(1)).getValidDistributorGroup(anyInt(), eq(accountId));
        verify(distributorGroupVOAssembler, times(1)).buildDistributorGroupModel(distributorGroupBO);
    }





    /**
     * 测试 passAudit 方法，当分销团信息不存在时抛出 BizSceneException 异常
     */
    @Test(expected = BizSceneException.class)
    public void testPassAuditWhenDistributorGroupNotFound() {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        when(distributorGroupRootService.getDistributorGroup(anyInt())).thenReturn(null);
        // act
        service.passAudit(distributorGroupBO);
        // assert is handled by the expected exception
    }



    /**
     * 测试 passAudit 方法，当分销团属性列表为空时，应正常处理并返回分销团模型
     */
    @Test
    public void testPassAuditWithEmptyDistributorGroupAttrList() {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        distributorGroupBO.setDistributorGroupAttrList(Collections.emptyList());
        DistributorGroupModel expectedModel = new DistributorGroupModel();
        when(distributorGroupRootService.getDistributorGroup(anyInt())).thenReturn(new DistributorGroupBO());
        when(distributorGroupVOAssembler.buildDistributorGroupModel(any(DistributorGroupBO.class))).thenReturn(expectedModel);
        // act
        DistributorGroupModel result = service.passAudit(distributorGroupBO);
        // assert
        verify(distributorGroupRootService, never()).addConsultantGroupAttr(any(DistributorGroupAttr.class));
        assert result == expectedModel;
    }

    /**
     * 测试 passAudit 方法，当分销团属性列表为 null 时，应正常处理并返回分销团模型
     */
    @Test
    public void testPassAuditWithNullDistributorGroupAttrList() {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        distributorGroupBO.setDistributorGroupAttrList(null);
        DistributorGroupModel expectedModel = new DistributorGroupModel();
        when(distributorGroupRootService.getDistributorGroup(anyInt())).thenReturn(new DistributorGroupBO());
        when(distributorGroupVOAssembler.buildDistributorGroupModel(any(DistributorGroupBO.class))).thenReturn(expectedModel);
        // act
        DistributorGroupModel result = service.passAudit(distributorGroupBO);
        // assert
        verify(distributorGroupRootService, never()).addConsultantGroupAttr(any(DistributorGroupAttr.class));
        assert result == expectedModel;
    }



    /**
     * 测试分页查询分销商组，正常情况
     */
    @Test
    public void testPageQueryDistributorGroup_Normal() {
        PageQueryDistributorGroupRequest request = new PageQueryDistributorGroupRequest(1L, 1, 1, 10);
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO(1, "groupName", 1, "groupCode", 1, 1L, 1, 1, "principal", "principalMobile", "contract", "companyName", "creditCode", "license", 1, Collections.emptyList(), Collections.emptyList(), null, null, Collections.emptyList(), 1);
        PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO = new PrivateLiveAnchorDistributorGroupBO(1L, distributorGroupBO, DistributionApproveStatusEnum.PASS, "statusDesc");
        PageDataDTO<PrivateLiveAnchorDistributorGroupBO> pageDataDTO = new PageDataDTO<>(new PageInfoDTO(1, 10, 1, 1), Collections.singletonList(privateLiveAnchorDistributorGroupBO));
        when(privateLiveAnchorDistributorGroupRepository.pageQueryDistributorGroup(request)).thenReturn(pageDataDTO);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setGroupId(1L);
        distributorGroupModel.setGroupName("groupName");
        distributorGroupModel.setCompanyName("companyName");
        distributorGroupModel.setPrincipalMobile("principalMobile");
        when(distributorGroupVOAssembler.buildDistributorGroupModel(any(DistributorGroupBO.class))).thenReturn(distributorGroupModel);
        when(distributorGroupRootService.getGroupMemberCount(anyLong())).thenReturn(10);
        when(mobileHelper.getMobileMask("principalMobile")).thenReturn("maskedMobile");
        PageDataDTO<PageQueryDistributorGroupResponse.DistributorGroup> result = distributorGroupAppService.pageQueryDistributorGroup(request);
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        PageQueryDistributorGroupResponse.DistributorGroup distributorGroup = result.getList().get(0);
        assertEquals(Long.valueOf(1), distributorGroup.getDistributorGroupId());
        assertEquals("groupName", distributorGroup.getDistributorGroupName());
        assertEquals("companyName", distributorGroup.getCompanyName());
        assertEquals(Integer.valueOf(10), distributorGroup.getDistributorCount());
        assertEquals("maskedMobile", distributorGroup.getPhoneNumber());
        verify(privateLiveAnchorDistributorGroupRepository, times(1)).pageQueryDistributorGroup(request);
        verify(distributorGroupVOAssembler, times(1)).buildDistributorGroupModel(any(DistributorGroupBO.class));
        verify(distributorGroupRootService, times(1)).getGroupMemberCount(anyLong());
        verify(mobileHelper, times(1)).getMobileMask("principalMobile");
    }

    /**
     * 测试分页查询分销商组，当没有分销商组时
     */
    @Test
    public void testPageQueryDistributorGroup_NoGroups() {
        PageQueryDistributorGroupRequest request = new PageQueryDistributorGroupRequest(1L, 1, 1, 10);
        PageDataDTO<PrivateLiveAnchorDistributorGroupBO> pageDataDTO = new PageDataDTO<>(new PageInfoDTO(1, 10, 0, 0), Collections.emptyList());
        when(privateLiveAnchorDistributorGroupRepository.pageQueryDistributorGroup(request)).thenReturn(pageDataDTO);
        PageDataDTO<PageQueryDistributorGroupResponse.DistributorGroup> result = distributorGroupAppService.pageQueryDistributorGroup(request);
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        verify(privateLiveAnchorDistributorGroupRepository, times(1)).pageQueryDistributorGroup(request);
        verify(distributorGroupVOAssembler, never()).buildDistributorGroupModel(any(DistributorGroupBO.class));
        verify(distributorGroupRootService, never()).getGroupMemberCount(anyLong());
        verify(mobileHelper, never()).getMobileMask(anyString());
    }

    /**
     * 测试createDistributorAttrList方法，当请求包含所有属性时
     */
    @Test
    public void testCreateDistributorAttrListWithAllAttributes() {
        // arrange
        DistributorBindRequest request = new DistributorBindRequest(1L, 1L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", "wechatNumber");
        when(mobileHelper.getMobileToken("phoneNumber")).thenReturn("encryptedPhoneNumber");
        // act
        List<DistributorAttrBO> result = service.createDistributorAttrList(request);
        // assert
        assertNotNull(result);
        assertEquals(6, result.size());
        assertTrue(result.stream().anyMatch(attr -> "encryptedPhoneNumber".equals(attr.getAttrValue()) && "phoneNumber".equals(attr.getAttrName())));
        verify(mobileHelper, times(1)).getMobileToken("phoneNumber");
    }



}
