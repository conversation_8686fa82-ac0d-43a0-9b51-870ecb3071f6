package com.sankuai.carnation.distribution.intention.domain.calculate.crane;

import com.alibaba.fastjson.JSON;
import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.service.BeautiBotUserService;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelTaskStatusEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.OrderChannelCalculateMerger;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.OrderChannelResultDomainService;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalTaskMapper;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalTaskWithBLOBs;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/11/13
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderChannelTaskRerunTaskTest {

    @InjectMocks
    private OrderChannelTaskRerun orderChannelTaskRerun;

    @Mock
    private DistributionOrderChannelCalTaskMapper taskMapper;

    @Mock
    private BeautiBotUserService beautiBotUserService;

    @Mock
    private OrderInfoAdaptor orderInfoAdaptor;

    @Mock
    private OrderChannelCalculateMerger calculateMerger;

    @Mock
    private OrderChannelResultDomainService resultDomainService;

    private static final String RERUN_ORDER_ID = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.rerun.order-id";

    private static final String RERUN_WRITE_DB = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.rerun.write-db";

    private static final String RERUN_SWITCH = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.param-check.switch";

    private static final String RERUN_TIME_OUT = "com.sankuai.medicalcosmetology.distribution.service.intention.order.channel.cal.task.time.out.rerun";

    private List<String> orderIdList = Lists.newArrayList();

    private List<DistributionOrderChannelCalTaskWithBLOBs> taskList = Lists.newArrayList();

    @Before
    public void setUp() {
        orderIdList.add("1");
    }


    /**
     * 测试重计算主任务，且任务为null
     */
    @Test
    public void testRerunTaskNull() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);

            taskList.add(null);
            when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);
                // act
                orderChannelTaskRerun.rerunTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }

    /**
     * 测试重计算主任务，且任务已超时，允许重计算
     */
    @Test
    public void testRerunTaskTimeOut() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);
            mockLionFactory.when(() -> Lion.getBoolean(anyString(), eq(RERUN_TIME_OUT))).thenReturn(true);

            DistributionOrderChannelCalTaskWithBLOBs task = new DistributionOrderChannelCalTaskWithBLOBs();
            task.setStatus(OrderChannelTaskStatusEnum.TIME_OUT.getCode());
            task.setOrderType(1);
            task.setOrderId("1");
            IntentionCalculateTaskParamBO formerTaskParam = new IntentionCalculateTaskParamBO();
            formerTaskParam.setOrderType(task.getOrderType());
            formerTaskParam.setOrderId(task.getOrderId());
            formerTaskParam.setDistributionCode("code");
            task.setParams(JSON.toJSONString(formerTaskParam));
            task.setId(1L);
            taskList.add(task);
            when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);

            when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(new OrderInfoBO());

            OrderChannelTaskResultBO rerunResult = new OrderChannelTaskResultBO();
            rerunResult.setBusinessChannel("channel");
            when(calculateMerger.merge(anyLong(), anyBoolean())).thenReturn(rerunResult);

            mockLionFactory.when(() -> Lion.getBoolean(anyString(), eq(RERUN_WRITE_DB))).thenReturn(true);
            doReturn(1).when(taskMapper).updateByPrimaryKeySelective(any());
            doNothing().when(resultDomainService).insertOrUpdateCalResult(anyLong(), any());

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);

                // act
                orderChannelTaskRerun.rerunTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }

    /**
     * 测试重计算主任务，且任务运行中
     */
    @Test
    public void testRerunTaskRunning() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);

            DistributionOrderChannelCalTaskWithBLOBs task = new DistributionOrderChannelCalTaskWithBLOBs();
            task.setStatus(OrderChannelTaskStatusEnum.RUNNING.getCode());
            taskList.add(task);
            when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);

                // act
                orderChannelTaskRerun.rerunTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }

    /**
     * 测试重计算主任务，且任务已结束
     */
    @Test
    public void testRerunTaskEnd() {
        try (MockedStatic<Lion> mockLionFactory = mockStatic(Lion.class)) {
            mockLionFactory.when(() -> Lion.getList(anyString(), eq(RERUN_ORDER_ID), eq(String.class))).thenReturn(orderIdList);

            DistributionOrderChannelCalTaskWithBLOBs task = new DistributionOrderChannelCalTaskWithBLOBs();
            task.setStatus(OrderChannelTaskStatusEnum.ENDED.getCode());
            task.setOrderType(1);
            task.setOrderId("1");
            IntentionCalculateTaskParamBO formerTaskParam = new IntentionCalculateTaskParamBO();
            formerTaskParam.setOrderType(task.getOrderType());
            formerTaskParam.setOrderId(task.getOrderId());
            formerTaskParam.setDistributionCode("code");
            task.setParams(JSON.toJSONString(formerTaskParam));
            task.setId(1L);
            OrderChannelTaskResultBO formerResult = new OrderChannelTaskResultBO();
            formerResult.setCalculateResult(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode());
            formerResult.setBusinessChannel(DistributionBusinessChannelEnum.UNKNOWN.getCode());
            task.setResult(JSON.toJSONString(formerResult));
            taskList.add(task);
            when(taskMapper.selectByExampleWithBLOBs(any())).thenReturn(taskList);

            when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(new OrderInfoBO());

            OrderChannelTaskResultBO rerunResult = new OrderChannelTaskResultBO();
            rerunResult.setBusinessChannel("channel");
            when(calculateMerger.merge(anyLong(), anyBoolean())).thenReturn(rerunResult);

            mockLionFactory.when(() -> Lion.getBoolean(anyString(), eq(RERUN_WRITE_DB))).thenReturn(true);
            doReturn(1).when(taskMapper).updateByPrimaryKeySelective(any());
            doNothing().when(resultDomainService).insertOrUpdateCalResult(anyLong(), any());

            try (MockedStatic<FileBodyBuilder> mockFileBodyBuilderFactory = mockStatic(FileBodyBuilder.class)) {
                FileBody mockFileBody = mock(FileBody.class);
                mockFileBodyBuilderFactory.when(() -> FileBodyBuilder.buildCsvFileBody(anyString(), any())).thenReturn(mockFileBody);

                // act
                orderChannelTaskRerun.rerunTask();

                // assert
                verify(beautiBotUserService, times(1)).pushMessage(any(), anyList());
            }
        }
    }
}
