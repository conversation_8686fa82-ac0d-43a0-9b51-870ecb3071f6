package com.sankuai.carnation.distribution.distributor.dto.request;

import org.junit.Test;
import static org.junit.Assert.*;

public class PageQueryDistributorRequestTest {

    /**
     * 测试构造函数和 Getter 方法
     */
    @Test
    public void testConstructorAndGetters() {
        Long distributorGroupId = 1L;
        Integer status = 0;
        Integer pageNum = 1;
        Integer pageSize = 10;
        boolean requireStatistics = true;
        String wxToken = "token";

        PageQueryDistributorRequest request = new PageQueryDistributorRequest(distributorGroupId, status, pageNum, pageSize, requireStatistics, wxToken);

        assertEquals(distributorGroupId, request.getDistributorGroupId());
        assertEquals(status, request.getStatus());
        assertEquals(pageNum, request.getPageNum());
        assertEquals(pageSize, request.getPageSize());
        assertTrue(request.isRequireStatistics());
        assertEquals(wxToken, request.getWxToken());
    }

    /**
     * 测试 Setter 方法和 Getter 方法
     */
    @Test
    public void testSettersAndGetters() {
        PageQueryDistributorRequest request = new PageQueryDistributorRequest();

        Long distributorGroupId = 2L;
        Integer status = 1;
        Integer pageNum = 2;
        Integer pageSize = 20;
        boolean requireStatistics = false;
        String wxToken = "newToken";

        request.setDistributorGroupId(distributorGroupId);
        request.setStatus(status);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setRequireStatistics(requireStatistics);
        request.setWxToken(wxToken);

        assertEquals(distributorGroupId, request.getDistributorGroupId());
        assertEquals(status, request.getStatus());
        assertEquals(pageNum, request.getPageNum());
        assertEquals(pageSize, request.getPageSize());
        assertFalse(request.isRequireStatistics());
        assertEquals(wxToken, request.getWxToken());
    }

    /**
     * 测试 Builder 模式
     */
    @Test
    public void testBuilder() {
        Long distributorGroupId = 3L;
        Integer status = 2;
        Integer pageNum = 3;
        Integer pageSize = 30;
        boolean requireStatistics = true;
        String wxToken = "builderToken";

        PageQueryDistributorRequest request = PageQueryDistributorRequest.builder()
                .distributorGroupId(distributorGroupId)
                .status(status)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .requireStatistics(requireStatistics)
                .wxToken(wxToken)
                .build();

        assertEquals(distributorGroupId, request.getDistributorGroupId());
        assertEquals(status, request.getStatus());
        assertEquals(pageNum, request.getPageNum());
        assertEquals(pageSize, request.getPageSize());
        assertTrue(request.isRequireStatistics());
        assertEquals(wxToken, request.getWxToken());
    }
}
