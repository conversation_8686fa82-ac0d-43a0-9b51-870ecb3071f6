package com.sankuai.carnation.distribution.product.v2.repository.service;

import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceGetUpdateListTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductItemSaleUnitMapper mapper;

    @Mock
    private ProductSelectifyAclService productSelectifyAclService;

    private Method getUpdateListMethod;

    @Before
    public void setUp() throws Exception {
        getUpdateListMethod = ProductItemSaleUnitDataService.class.getDeclaredMethod("getUpdateList", List.class, Map.class);
        getUpdateListMethod.setAccessible(true);
    }

    private List<ProductItemSaleUnit> invokeGetUpdateList(List<ProductItemSaleUnit> targetList, Map<String, ProductItemSaleUnit> formerMap) throws Exception {
        return (List<ProductItemSaleUnit>) getUpdateListMethod.invoke(productItemSaleUnitDataService, targetList, formerMap);
    }

    @Test
    public void testGetUpdateList_ExceptionInBuildPoBusinessUniqueKey() throws Throwable {
        ProductItemSaleUnit po1 = new ProductItemSaleUnit();
        po1.setProductItemId(null);
        po1.setProductType(1);
        po1.setProductId(100L);
        List<ProductItemSaleUnit> targetList = Arrays.asList(po1);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        try {
            invokeGetUpdateList(targetList, formerMap);
            fail("Expected ProductItemException was not thrown");
        } catch (InvocationTargetException e) {
            if (e.getCause() instanceof ProductItemException) {
                // Expected exception caught
                return;
            }
            throw new RuntimeException("Unexpected exception", e);
        } catch (Exception e) {
            throw new RuntimeException("Unexpected exception", e);
        }
    }

    @Test
    public void testGetUpdateList_EmptyTargetList() throws Throwable {
        List<ProductItemSaleUnit> targetList = Arrays.asList();
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        List<ProductItemSaleUnit> result = invokeGetUpdateList(targetList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetUpdateList_NoMatchingKeyInFormerMap() throws Throwable {
        ProductItemSaleUnit po1 = new ProductItemSaleUnit();
        po1.setProductItemId(1L);
        po1.setProductType(1);
        po1.setProductId(100L);
        List<ProductItemSaleUnit> targetList = Arrays.asList(po1);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        List<ProductItemSaleUnit> result = invokeGetUpdateList(targetList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetUpdateList_NoNeedToUpdate() throws Throwable {
        ProductItemSaleUnit po1 = new ProductItemSaleUnit();
        po1.setProductItemId(1L);
        po1.setProductType(1);
        po1.setProductId(100L);
        po1.setAmount(10);
        po1.setStatus(1);
        ProductItemSaleUnit formerPo1 = new ProductItemSaleUnit();
        formerPo1.setProductItemId(1L);
        formerPo1.setProductType(1);
        formerPo1.setProductId(100L);
        formerPo1.setAmount(10);
        formerPo1.setStatus(1);
        List<ProductItemSaleUnit> targetList = Arrays.asList(po1);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        formerMap.put("1_1_100", formerPo1);
        List<ProductItemSaleUnit> result = invokeGetUpdateList(targetList, formerMap);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetUpdateList_NeedToUpdate() throws Throwable {
        ProductItemSaleUnit po1 = new ProductItemSaleUnit();
        po1.setProductItemId(1L);
        po1.setProductType(1);
        po1.setProductId(100L);
        po1.setAmount(20);
        po1.setStatus(1);
        ProductItemSaleUnit formerPo1 = new ProductItemSaleUnit();
        formerPo1.setProductItemId(1L);
        formerPo1.setProductType(1);
        formerPo1.setProductId(100L);
        formerPo1.setAmount(10);
        formerPo1.setStatus(1);
        List<ProductItemSaleUnit> targetList = Arrays.asList(po1);
        Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();
        formerMap.put("1_1_100", formerPo1);
        List<ProductItemSaleUnit> result = invokeGetUpdateList(targetList, formerMap);
        assertEquals(1, result.size());
        assertEquals(po1, result.get(0));
    }
}
