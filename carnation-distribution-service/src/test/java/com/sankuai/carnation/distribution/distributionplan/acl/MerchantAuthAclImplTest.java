package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.distributionplan.acl.impl.MerchantAuthAclImpl;
import com.sankuai.nibmp.infra.amp.attribute.lib.IAccountService;
import com.sankuai.nibmp.infra.amp.attribute.lib.result.CustomerIdResult;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.Assert;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@RunWith(MockitoJUnitRunner.class)
public class MerchantAuthAclImplTest {

    @InjectMocks
    private MerchantAuthAclImpl merchantAuthAcl;

    @Mock
    private IAccountService iAccountService;


    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试dpAccountId为null时抛出IllegalArgumentException
     */
    @Test
    public void testGetCustomerIdByAccountIdWithNullAccountId() {
        Optional<Long> result = merchantAuthAcl.getCustomerIdByAccountId(null);
        Assert.assertEquals(result, Optional.empty());
    }

    /**
     * 测试dpAccountId小于等于0时抛出IllegalArgumentException
     */
    @Test
    public void testGetCustomerIdByAccountIdWithInvalidAccountId() {
        Optional<Long> result = merchantAuthAcl.getCustomerIdByAccountId(0L);
        Assert.assertEquals(result, Optional.empty());
    }

    /**
     * 测试根据账号ID查询客户ID接口返回为空时抛出DistributionPlanException
     */
    @Test
    public void testGetCustomerIdByAccountIdWithEmptyResponse() throws TException {
        when(iAccountService.getCustomerIdByAccountId(any())).thenReturn(new CustomerIdResult());
        Optional<Long> result = merchantAuthAcl.getCustomerIdByAccountId(1L);
        Assert.assertEquals(result, Optional.empty());
    }

    /**
     * 测试方法正常执行并返回正确的客户ID
     */
    @Test
    public void testGetCustomerIdByAccountIdWithSuccess() throws TException {
        CustomerIdResult result1 = new CustomerIdResult();
        result1.setCustomerId(2L);
        when(iAccountService.getCustomerIdByAccountId(any())).thenReturn(result1);
        Optional<Long> result = merchantAuthAcl.getCustomerIdByAccountId(1L);
        Assert.assertTrue(result.isPresent());
    }
}
