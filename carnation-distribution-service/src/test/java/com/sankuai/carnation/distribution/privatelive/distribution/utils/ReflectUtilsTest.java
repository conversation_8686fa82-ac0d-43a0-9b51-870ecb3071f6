package com.sankuai.carnation.distribution.privatelive.distribution.utils;

import lombok.Getter;
import lombok.Setter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.BeanWrapperImpl;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.doReturn;

public class ReflectUtilsTest {
    private Object targetWithAttributes;
    private Object targetWithoutAttributes;
    @Setter
    @Getter
    public class SimpleBean {
        private String name;
        private int age;

        // 省略getter和setter方法
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 直接初始化类成员变量
        this.targetWithAttributes = new SimpleBean();
        ((SimpleBean)this.targetWithAttributes).setName("John");
        ((SimpleBean)this.targetWithAttributes).setAge(30);
        BeanWrapperImpl beanWrapperWithAttributes = Mockito.spy(new BeanWrapperImpl(this.targetWithAttributes));
        doReturn(new String[] { "name", "age" }).when(beanWrapperWithAttributes).getPropertyDescriptors();
        doReturn("John").when(beanWrapperWithAttributes).getPropertyValue("name");
        doReturn(30).when(beanWrapperWithAttributes).getPropertyValue("age");
        // 对于不带属性的对象，可以保持不变或者使用相同的方式处理
    }




}
