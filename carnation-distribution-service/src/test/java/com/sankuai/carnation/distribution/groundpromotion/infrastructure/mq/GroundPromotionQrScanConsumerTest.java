package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.CommissionRateGateRuleGroupBO;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.RedisAcl;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPTQrScanBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPartTimeQrLogRepository;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.UserScanRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.service.UserScanRecordService;
import org.junit.After;
import org.junit.Before;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/11/11
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionQrScanConsumerTest {

    @InjectMocks
    private GroundPromotionQrScanConsumer consumer;

    @Mock
    private GroundPartTimeQrLogRepository groundPartTimeQrLogRepository;

    @Mock
    private UserScanRecordService userScanRecordServiceV2;

    @Mock
    private RedisAcl redisAcl;

    private static MockedStatic<Lion> mockLionFactory = Mockito.mockStatic(Lion.class);

    private static final String QR_LOG_STORE_CONFIG_KEY = "com.sankuai.medicalcosmetology.distribution.service.ground.promotion.part.time.qr.log.biz.type";

    private static final String GD_PROMO_CODE_CONFIG_KEY = "com.sankuai.medicalcosmetology.distribution.service.ground.promotion.promo.code.biz";

    @After
    public void closeStatic() {
        mockLionFactory.close();
    }

    private ConsumeStatus invokeHandle(MafkaMessage message, MessagetContext context) throws Exception {
        Method handleMethod = GroundPromotionQrScanConsumer.class.getDeclaredMethod("handle", MafkaMessage.class,
                MessagetContext.class);
        handleMethod.setAccessible(true);
        return (ConsumeStatus) handleMethod.invoke(consumer, message, context);
    }

    @Test
    public void testHandleBizTypeInConfigListDbOperationSuccess() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        GroundPromotionPTQrScanBO ptQRScanBO = new GroundPromotionPTQrScanBO();
        ptQRScanBO.setBizType("inList");
        ptQRScanBO.setUserType(0);
        ptQRScanBO.setScanTime(new Date().getTime());
        ptQRScanBO.setUserTypeTags(Lists.newArrayList());
        ptQRScanBO.setPoiId(1L);
        when(message.getBody()).thenReturn(JSON.toJSONString(ptQRScanBO));

        // 模拟配置列表包含该业务类型
        List<String> qrLogBizTypeList = Collections.singletonList("inList");
        // 假设这是获取配置列表的方法，需要根据实际情况调整
        mockLionFactory.when(() -> Lion.getList(eq(QR_LOG_STORE_CONFIG_KEY), eq(String.class))).thenReturn(qrLogBizTypeList);

        // 模拟数据库操作成功
        doReturn(1).when(groundPartTimeQrLogRepository).insert(any(GroundPartTimeQrLog.class));
        doNothing().when(redisAcl).setGroundPartTimeQrLogCache(any(GroundPartTimeQrLog.class), anyInt());

        // 模拟配置列表包含该业务类型
        Map<String, String> promoCodeBizMap = Maps.newHashMap();
        promoCodeBizMap.put("1", "inList");
        // 假设这是获取配置列表的方法，需要根据实际情况调整
        mockLionFactory.when(() -> Lion.getMap(eq(GD_PROMO_CODE_CONFIG_KEY), eq(String.class))).thenReturn(promoCodeBizMap);
        doReturn(RemoteResponse.success(true)).when(userScanRecordServiceV2).addUserScanRecord(any(UserScanRecordDTO.class));

        // act
        ConsumeStatus result = this.invokeHandle(message, new MessagetContext());

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
