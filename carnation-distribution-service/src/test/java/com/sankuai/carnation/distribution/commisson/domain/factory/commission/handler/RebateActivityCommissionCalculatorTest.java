package com.sankuai.carnation.distribution.commisson.domain.factory.commission.handler;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.rebate.dto.RebateRuleVerifyCheckResult;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleConditionDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.dianping.gmkt.event.api.rebate.enums.RebateSettleConditionKey;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.domain.enums.ProductEnum;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.commission.dto.CalculateResult;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commission.enums.RebateEventSourceEnum;
import com.sankuai.carnation.distribution.commission.enums.RebateStatusEnum;
import com.sankuai.carnation.distribution.commission.settle.enums.OrderVerifyRebateSourceEnum;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.CommissionRateGateRuleBO;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.CommissionRateGateRuleGroupBO;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.StaffRebateAmountCalcService;
import com.sankuai.carnation.distribution.commisson.limit.Interceptor.RebateRuleLimitInterceptor;
import com.sankuai.carnation.distribution.commisson.settle.rule.ShopCommissionRuleDomainService;
import com.sankuai.carnation.distribution.commisson.settle.rule.bo.ShopPredictEffectCommissionRuleBO;
import com.sankuai.carnation.distribution.common.acl.ShopCategoryAclService;
import com.sankuai.carnation.distribution.common.enums.DistributionTradeTypeEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderSkuBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.ProductDetailAclService;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductDetailBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductSkuBO;
import com.sankuai.carnation.distribution.promocode.acl.ShopApolloBuAclService;
import com.sankuai.carnation.distribution.promocode.acl.TechCateAndIndustryAclService;
import com.sankuai.carnation.distribution.promocode.acl.enums.BusinessDepartmentIdEnum;
import com.sankuai.carnation.distribution.promocode.rebate.bo.RbOrderVerifyRebateCommissionBO;
import com.sankuai.carnation.distribution.promocode.rebate.domain.TechPromoCodeRebateDomainService;
import com.sankuai.carnation.distribution.promocode.rebate.enums.PromoCodeRebateBizTypeEnum;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.utils.DateUtils;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletBindAccountTypeEnum;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.technician.category.enums.IndustryEnum;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/9/26
 **/
@RunWith(MockitoJUnitRunner.class)
public class RebateActivityCommissionCalculatorTest {

    private static final MockedStatic<Lion> mockLionFactory = Mockito.mockStatic(Lion.class);

    private final long mockDpShopId = 111L;

    private final long mockMtShopId = 222L;

    private final long mockDpUserId = 123L;

    private final long mockMtUserId = 321L;

    private final long mockProductId = 666L;

    private final long mockSkuId = 777L;

    private final long mockCouponVerifyPayAmount = 111L;

    private final String mockOrderId = "orderId";

    private final String couponVerifyId = "verifyId";

    private final int mockOrderType = DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode();

    private final long mockOrderPayCent = 10000L;

    private final int mockTechId = 998;

    private final long mockTechApplyRecordId = 1100000001L;

    @Mock
    private OrderInfoAdaptor orderInfoAdaptor;

    @Mock
    private OrderReceiptQueryService orderReceiptQueryService;

    @Mock
    private ProductDetailAclService productDetailAclService;

    @Mock
    private UserScanRecordService userScanRecordService;

    @Mock
    private ShopMapperService shopMapperService;

    @Mock
    private RebateRuleLimitInterceptor rebateRuleLimitInterceptor;

    @Mock
    private ShopAcl shopAcl;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rebateCommissionDataService;

    @Mock
    private TechPromoCodeRebateDomainService techPromoCodeRebateDomainService;

    @Mock
    private TechCateAndIndustryAclService techCateAndIndustryAclService;

    @Mock
    private ShopApolloBuAclService shopApolloBuAclService;

    @Mock
    private ShopCommissionRuleDomainService shopCommissionRuleDomainService;

    @Mock
    private ShopCategoryAclService shopCategoryAclService;

    @Mock
    private StaffRebateAmountCalcService staffRebateAmountCalcService;

    @Mock
    private DistributorActivityService distributorActivityService;

    @InjectMocks
    private RebateActivityCommissionCalculator calculator;

    @BeforeClass
    public static void mockStatic() {
        mockLionFactory.when(() -> Lion.getList(eq(Environment.getAppName()), eq("promo.code.tech.predict.rebate.commission.rule-list"), eq(CommissionRateGateRuleGroupBO.class))).thenReturn(generateMockRuleGroupList());
    }

    @AfterClass
    public static void closeStatic() {
        mockLionFactory.close();
    }
    
    @Test
    public void testCalculate() {
        boolean isMt = false;
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -1);
        ProductTypeEnum productType = ProductTypeEnum.TUAN_DEAL;
        DistributionTradeTypeEnum distributionTradeType = DistributionTradeTypeEnum.GROUPBUY_PAY;

        mockOrder(isMt, DateUtils.parse("2024-08-16 00:00:00"), productType, distributionTradeType, 2);
        mockShopBusiness(mockDpShopId, BusinessDepartmentIdEnum.LI_REN_YI_MEI);
        calendar.add(Calendar.MINUTE, -15);
        mockShopFrameRule(OrderVerifyRebateSourceEnum.ANNUAL_FRAME, new BigDecimal("0.1"));
        mockTechSdOrder(true);
        mockRebateCommissionData();

        List<CalculateResult> resultList = calculator.calculateVerify(generateRequest(true, DistributionBusinessChannelEnum.PROMO_CODE, productType, calendar.getTime()));

        Assert.assertNotNull(resultList);
        Assert.assertFalse(resultList.isEmpty());
        Assert.assertEquals(1, resultList.size());
        Assert.assertEquals(0L, resultList.get(0).getCommissionCent());
        verify(rebateCommissionDataService, times(1)).storeFinalRebateCommission(any());
    }

    /**
     * 测试场景：不符合类目限制，命中无类目规则
     */
    @Test
    public void testCalculateCommissionRateRuleGroupWithOutCategoryLimit() {
        boolean isMt = false;
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -1);
        ProductTypeEnum productType = ProductTypeEnum.TUAN_DEAL;

        mockOrder(isMt, DateUtils.parse("2024-08-16 00:00:00"), productType, 2);
        mockShopBusiness(mockDpShopId, BusinessDepartmentIdEnum.ZONG_FA);
        mockShopCategory(10);
        calendar.add(Calendar.MINUTE, -15);

        CommissionRateGateRuleGroupBO result = calculator.calculateCommissionRateRuleGroup(generateRequest(true, DistributionBusinessChannelEnum.PROMO_CODE, productType, calendar.getTime()));

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.getShopDepartmentList().size());
        Assert.assertEquals(BusinessDepartmentIdEnum.ZONG_FA.getBuId(), result.getShopDepartmentList().get(0));
        Assert.assertEquals(0.015, result.getRuleList().get(0).getRangeUpper(), 0.0001);
    }

    /**
     * 测试场景：符合类目限制，同时满足两种规则，由于优先级命中有类目规则
     */
    @Test
    public void testCalculateCommissionRateRuleGroupWithAllConditionsMatched() {
        boolean isMt = false;
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -1);
        ProductTypeEnum productType = ProductTypeEnum.TUAN_DEAL;

        mockOrder(isMt, DateUtils.parse("2024-08-16 00:00:00"), productType, 2);
        mockShopBusiness(mockDpShopId, BusinessDepartmentIdEnum.ZONG_FA);
        mockShopCategory(1);
        calendar.add(Calendar.MINUTE, -15);

        CommissionRateGateRuleGroupBO result = calculator.calculateCommissionRateRuleGroup(generateRequest(true, DistributionBusinessChannelEnum.PROMO_CODE, productType, calendar.getTime()));

        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.getShopDepartmentList().size());
        Assert.assertEquals(BusinessDepartmentIdEnum.ZONG_FA.getBuId(), result.getShopDepartmentList().get(0));
        Assert.assertEquals(0.01, result.getRuleList().get(0).getRangeUpper(), 0.0001);
    }

    private CommissionVerifyCalculationRequest generateRequest(boolean isMt, DistributionBusinessChannelEnum channel, ProductTypeEnum productType, Date verifyTime) {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderType(mockOrderType);
        request.setOrderId(mockOrderId);
        request.setChannel(channel.getCode());
        request.setProductType(productType.getCode());
        request.setProductId(mockProductId);
        request.setCouponVerifyId(couponVerifyId);
        request.setCouponVerifyCode(couponVerifyId);
        request.setVerifyTime(verifyTime);
        request.setDpShopId(mockDpShopId);
        request.setTechId(mockTechId);
        request.setDistributorId(mockTechId);
        request.setDistributorGroupId(0);
        request.setDistributeProductId(0L);
        request.setStaffCodeId(0L);
        request.setQrCodeId(0L);
        request.setEventSource(RebateEventSourceEnum.PROMO_CODE_REBATE_EVENT.getCode());
        request.setTechApplyRecordId(mockTechApplyRecordId);
        request.setUesrId(isMt ? mockMtUserId : mockDpUserId);
        request.setPlatform(isMt ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
        request.setCouponVerifyPayAmount(mockCouponVerifyPayAmount);
        return request;
    }

    private void mockTechSdOrder(boolean isTechSdOrder) {
        when(techPromoCodeRebateDomainService.isTechSdOrder(eq(mockOrderId), eq(couponVerifyId), eq(mockTechId))).thenReturn(isTechSdOrder);
    }

    private void mockRebateCommissionData() {
        RbOrderVerifyRebateCommissionBO bo = new RbOrderVerifyRebateCommissionBO();
        bo.setId(1L);
        bo.setOrderType(mockOrderType);
        bo.setOrderId(mockOrderId);
        bo.setCouponId(couponVerifyId);
        bo.setRebateBizType(PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode());
        bo.setRebateBizId("bizId");
        bo.setRebateUserType(WalletBindAccountTypeEnum.TECHNICIAN.getCode());
        bo.setRebateUserId(String.valueOf(mockTechId));
        bo.setPredictRebateCent(100L);
        bo.setFinalRebateCent(null);
        bo.setRebateDesc("");
        bo.setExtInfo(null);
        bo.setStatus(RebateStatusEnum.REBATE.getCode());
        bo.setActivityRecordId(mockTechApplyRecordId);
        bo.setPlatformUser("");
        bo.setGroupNewUser("");
        bo.setAddTime(new Date());
        bo.setUpdateTime(new Date());

        when(rebateCommissionDataService.queryByBiz(eq(PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode()), anyString(), any(), anyBoolean())).thenReturn(bo);
    }

    private void mockShopFrameRule(OrderVerifyRebateSourceEnum ruleSource, BigDecimal ruleCommission) {
        ShopPredictEffectCommissionRuleBO rule = new ShopPredictEffectCommissionRuleBO();
        rule.setDpShopId(mockDpShopId);
        rule.setMtShopId(mockMtShopId);
        rule.setRuleSource(ruleSource.getCode());
        rule.setSyncRuleIdList(Lists.newArrayList());
        rule.setFinalCommission(ruleCommission);

        when(shopCommissionRuleDomainService.getLowestCommissionRule(eq(mockDpShopId), anyLong(), any(), any(), anyList())).thenReturn(rule);
    }

    private void mockShopBusiness(long dpShopId, BusinessDepartmentIdEnum department) {
        when(shopApolloBuAclService.getBusinessDepartment(eq(dpShopId))).thenReturn(department);
    }

    private void mockRebateVerifyCheckSwitch(boolean switchConfig) {
        mockLionFactory.when(() -> Lion.getBoolean(eq("gmkt-event-manage-service"), eq("promoCode.rebateVerifyCheckSwitch"), anyBoolean())).thenReturn(switchConfig);
    }

    private void mockRebateLimitHour(int rebateLimitHour) {
        mockRebateActivityRule(rebateLimitHour);
        mockRebateLimitHourByCategory(rebateLimitHour);
    }

    private void mockRebateActivityRule(int rebateLimitHour) {
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rebateSettleRuleDTO = new RebateSettleRuleDTO();
        RebateSettleConditionDTO conditionDTO = new RebateSettleConditionDTO();
        conditionDTO.setKey(RebateSettleConditionKey.SCAN_TIME.code);
        conditionDTO.setValue(String.valueOf(rebateLimitHour));
        rebateSettleRuleDTO.setCondition(Lists.newArrayList(conditionDTO));
        rebateActivityRuleDTO.setRule(rebateSettleRuleDTO);
        CommonResponse<RebateActivityRuleDTO> response = new CommonResponse<>();
        response.success(rebateActivityRuleDTO);
        when(distributorActivityService.queryActivityRule(anyLong())).thenReturn(response);
    }

    private void mockRebateLimitHourByCategory(int rebateLimitHour) {
        mockLionFactory.when(() -> Lion.getMap(eq(Environment.getAppName()), eq("com.sankuai.medicalcosmetology.distribution.service.promo.code.rebate.limit.hour.category"),
                any(), anyMap())).thenReturn(ImmutableMap.of("-1", rebateLimitHour));
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Lists.newArrayList());

    }

    private void mockTechIndustry(int techId, int industry) {
        when(techCateAndIndustryAclService.getTechIndustry(eq(techId))).thenReturn(industry);
    }

    private void mockShopMapper(Long dpShopId, Long mtShopId) {
        if (dpShopId != null) {
            when(shopMapperService.dp2mt(eq(dpShopId))).thenReturn(mtShopId);
        }
        if (mtShopId != null) {
            when(shopMapperService.mt2dp(eq(dpShopId))).thenReturn(mtShopId);
        }
    }

    private void mockOrder(boolean isMt, Date orderTime, ProductTypeEnum productType, int verifyCount) {
        when(orderInfoAdaptor.getOrder(eq(mockOrderType), eq(mockOrderId))).thenReturn(generateOrderInfoBO(isMt, orderTime, productType, verifyCount));
    }

    private void mockOrder(boolean isMt, Date orderTime, ProductTypeEnum productType, DistributionTradeTypeEnum distributionTradeType, int verifyCount) {
        when(orderInfoAdaptor.getOrder(eq(mockOrderType), eq(mockOrderId))).thenReturn(generateOrderInfoBO(isMt, orderTime, productType, distributionTradeType, verifyCount));
    }

    private void mockProductVerifyTimes(int times) {
        ProductDetailBO productDetailBO = new ProductDetailBO();
        productDetailBO.setSkuList(Lists.newArrayList(generateProductSkuBO(times)));
        Map<Long, ProductDetailBO> map = Maps.newHashMap();
        map.put(mockProductId, productDetailBO);

        when(productDetailAclService.batchGetProduct(any())).thenReturn(map);
    }

    private void mockScanRecord(boolean passScanCheck) {
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = PromoQRCodeResponse.success(passScanCheck ? Lists.newArrayList(new UserScanRecordDTO()) : Lists.newArrayList());
        when(userScanRecordService.queryLatestScanRecord(any())).thenReturn(response);
    }

    private OrderInfoBO generateOrderInfoBO(boolean isMt, Date orderTime, ProductTypeEnum productType, int verifyCount) {
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderType(mockOrderType);
        orderInfoBO.setOrderId(mockOrderId);
        orderInfoBO.setLongOrderId(888L);
        orderInfoBO.setPlatform(isMt ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
        orderInfoBO.setUserId(isMt ? mockMtUserId : mockDpUserId);
        orderInfoBO.setCityId(1);
        orderInfoBO.setShopId(isMt ? mockMtShopId : mockDpShopId);
        orderInfoBO.setOrderTime(orderTime);
        orderInfoBO.setPayTime(orderTime);
        orderInfoBO.setDistributionCode("");
        orderInfoBO.setProductType(productType.getCode());
        orderInfoBO.setOrderBizType(ProductEnum.commonGroupon.value);
        orderInfoBO.setProductId(mockProductId);
        orderInfoBO.setDpProductId(mockProductId);
        orderInfoBO.setTotalAmount(BigDecimal.ONE);
        orderInfoBO.setPayPlatform(PayPlatform.dp_android_native);
        orderInfoBO.setBuySuccessTime(Calendar.getInstance().getTime());
        orderInfoBO.setDiscountList(Lists.newArrayList());
        orderInfoBO.setPaymentDetailList(Lists.newArrayList());
        orderInfoBO.setSkuList(Lists.newArrayList(generateOrderSkuBO(verifyCount)));
        orderInfoBO.setExtDistributionInfo(Maps.newHashMap());
        orderInfoBO.setTechExtParam(null);
        orderInfoBO.setLiveParam(null);
        orderInfoBO.setTradePlatform(0);
        return orderInfoBO;
    }

    private OrderInfoBO generateOrderInfoBO(boolean isMt, Date orderTime, ProductTypeEnum productType, DistributionTradeTypeEnum distributionTradeType,int verifyCount) {
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderType(mockOrderType);
        orderInfoBO.setOrderId(mockOrderId);
        orderInfoBO.setLongOrderId(888L);
        orderInfoBO.setPlatform(isMt ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
        orderInfoBO.setUserId(isMt ? mockMtUserId : mockDpUserId);
        orderInfoBO.setCityId(1);
        orderInfoBO.setShopId(isMt ? mockMtShopId : mockDpShopId);
        orderInfoBO.setOrderTime(orderTime);
        orderInfoBO.setPayTime(orderTime);
        orderInfoBO.setDistributionCode("");
        orderInfoBO.setProductType(productType.getCode());
        orderInfoBO.setOrderBizType(ProductEnum.commonGroupon.value);
        orderInfoBO.setProductId(mockProductId);
        orderInfoBO.setDpProductId(mockProductId);
        orderInfoBO.setTotalAmount(BigDecimal.ONE);
        orderInfoBO.setPayPlatform(PayPlatform.dp_android_native);
        orderInfoBO.setBuySuccessTime(Calendar.getInstance().getTime());
        orderInfoBO.setDiscountList(Lists.newArrayList());
        orderInfoBO.setPaymentDetailList(Lists.newArrayList());
        orderInfoBO.setSkuList(Lists.newArrayList(generateOrderSkuBO(verifyCount)));
        orderInfoBO.setExtDistributionInfo(Maps.newHashMap());
        orderInfoBO.setTechExtParam(null);
        orderInfoBO.setLiveParam(null);
        orderInfoBO.setTradePlatform(0);
        orderInfoBO.setDistributionTradeType(distributionTradeType.getCode());
        return orderInfoBO;
    }

    private OrderSkuBO generateOrderSkuBO(int verifyCount) {
        OrderSkuBO skuBO = new OrderSkuBO();
        skuBO.setSkuId(mockSkuId);
        skuBO.setProductId(mockProductId);
        skuBO.setTotalVerifyCount(verifyCount);
        return skuBO;
    }

    private ProductSkuBO generateProductSkuBO(int times) {
        ProductSkuBO skuBO = new ProductSkuBO();
        skuBO.setSkuId(mockSkuId);
        skuBO.setProductId(mockProductId);
        skuBO.setTimes(times);
        return skuBO;
    }

    private static List<CommissionRateGateRuleGroupBO> generateMockRuleGroupList() {
        List<CommissionRateGateRuleGroupBO> ruleGroupList = Lists.newArrayList();
        // 丽人规则
        ruleGroupList.addAll(generateBeautyRule());

        // 综发规则
        ruleGroupList.add(generateMockRuleGroup(Lists.newArrayList(generateMockRangeRule(0.015D, null, 0.0D)), null, null, Lists.newArrayList(BusinessDepartmentIdEnum.ZONG_FA.getBuId()), Lists.newArrayList(), Lists.newArrayList(), 1));

        // 增加类目限制规则
        ruleGroupList.addAll(generateCategoryRule());

        return ruleGroupList;
    }

    /**
     * 丽人职人规则
     */
    private static List<CommissionRateGateRuleGroupBO> generateBeautyRule() {
        List<CommissionRateGateRuleGroupBO> ruleGroupList = Lists.newArrayList();
        // 阶梯规则
        CommissionRateGateRuleBO range1 = generateMockRangeRule(null, 0.025D, 0.02D);
        CommissionRateGateRuleBO range2 = generateMockRangeRule(0.025D, 0.02D, 0.01D);
        CommissionRateGateRuleBO range3 = generateMockRangeRule(0.02D, null, 0.0D);
        List<CommissionRateGateRuleBO> rateList = Lists.newArrayList(range1, range2, range3);
        ruleGroupList.add(generateMockRuleGroup(rateList, DateUtils.parse("2024-08-01", "yyyy-MM-dd"), DateUtils.parse("2024-09-01", "yyyy-MM-dd"), Lists.newArrayList(BusinessDepartmentIdEnum.LI_REN_YI_MEI.getBuId()), Lists.newArrayList(), Lists.newArrayList(IndustryEnum.BEAUTY.getValue()), 1));

        // 优先级规则
        rateList = Lists.newArrayList(generateMockRangeRule(0.015D, null, 0.0D));
        ruleGroupList.add(generateMockRuleGroup(rateList, DateUtils.parse("2024-08-15", "yyyy-MM-dd"), null, Lists.newArrayList(BusinessDepartmentIdEnum.LI_REN_YI_MEI.getBuId()), Lists.newArrayList(), Lists.newArrayList(IndustryEnum.BEAUTY.getValue()), 2));

        return ruleGroupList;
    }

    /**
     * 综发规则
     */
    private static List<CommissionRateGateRuleGroupBO> generateZongFaRule() {
        List<CommissionRateGateRuleGroupBO> ruleGroupList = Lists.newArrayList();

        List<CommissionRateGateRuleBO> range = Lists.newArrayList(generateMockRangeRule(0.015D, null, 0.0D));
        ruleGroupList.add(generateMockRuleGroup(range, null, null, Lists.newArrayList(BusinessDepartmentIdEnum.ZONG_FA.getBuId()), Lists.newArrayList(), Lists.newArrayList(), 1));

        return ruleGroupList;
    }

    /**
     * 类目规则
     */
    private static List<CommissionRateGateRuleGroupBO> generateCategoryRule() {
        List<CommissionRateGateRuleGroupBO> ruleGroupList = Lists.newArrayList();

        CommissionRateGateRuleBO range1 = generateMockRangeRule(0.01D, null, 0.0D);
        CommissionRateGateRuleBO range2 = generateMockRangeRule(0.0D, null, 0.0D);

        CommissionRateGateRuleGroupBO ruleGroup1 = generateMockRuleGroup(Lists.newArrayList(range1), null, null, Lists.newArrayList(BusinessDepartmentIdEnum.ZONG_FA.getBuId()), Lists.newArrayList(), Lists.newArrayList(), 2);
        CommissionRateGateRuleGroupBO ruleGroup2 = generateMockRuleGroup(Lists.newArrayList(range2), null, null, Lists.newArrayList(BusinessDepartmentIdEnum.ZONG_FA.getBuId()), Lists.newArrayList(), Lists.newArrayList(), 2);
        ruleGroup1.setSecondCategoryIdList(Lists.newArrayList(1, 2, 3));
        ruleGroup2.setSecondCategoryIdList(Lists.newArrayList(4, 5, 6));
        ruleGroupList.add(ruleGroup1);
        ruleGroupList.add(ruleGroup2);

        return ruleGroupList;
    }

    private static CommissionRateGateRuleGroupBO generateMockRuleGroup(List<CommissionRateGateRuleBO> rateList, Date startTime, Date endTime,
                                                                       List<Integer> shopDepartmentList, List<Integer> techIdList, List<Integer> techIndustryList, int priority) {
        CommissionRateGateRuleGroupBO ruleGroup = new CommissionRateGateRuleGroupBO();
        ruleGroup.setRuleList(rateList);
        ruleGroup.setStartTime(startTime);
        ruleGroup.setEndTime(endTime);
        ruleGroup.setShopDepartmentList(shopDepartmentList);
        ruleGroup.setTechIdList(techIdList);
        ruleGroup.setTechIndustryList(techIndustryList);
        ruleGroup.setPriority(priority);
        return ruleGroup;
    }

    private static CommissionRateGateRuleBO generateMockRangeRule(Double upper, Double lower, Double rate) {
        CommissionRateGateRuleBO rule = new CommissionRateGateRuleBO();
        rule.setRangeUpper(upper);
        rule.setRangeLower(lower);
        rule.setMaxRebateRate(rate);
        return rule;
    }

    private void mockShopCategory(int categoryId) {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO categoryDTO = new DpPoiBackCategoryDTO();
        categoryDTO.setCategoryId(categoryId);
        dpPoiDTO.setBackMainCategoryPath(Lists.newArrayList(categoryDTO));
        when(shopAcl.getDpPoiDTOList(any())).thenReturn(Lists.newArrayList(dpPoiDTO));
    }
}
