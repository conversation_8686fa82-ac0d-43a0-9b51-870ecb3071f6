package com.sankuai.carnation.distribution.privatelive.distribution.startar.service;

/**
 * @Author: jinjianxia
 * @CreateTime: 2024/9/5 16:04
 * @Description:
 */

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.request.BecomeDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.BecomeDistributorGroupResponse;

import org.junit.Before;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveAnchorDistributorGroupAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.request.JoinDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.JoinDistributorGroupResponse;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

public class PrivateLiveAnchorDistributorGroupServiceImplTest {

    @InjectMocks
    private PrivateLiveAnchorDistributorGroupServiceImpl service;

    @Mock
    private PrivateLiveAnchorDistributorGroupAppService privateLiveAnchorDistributorGroupAppService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }



    /**
     * 测试joinDistributorGroup方法，当wxToken为空时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinDistributorGroupWxTokenIsNull() {
        // arrange
        JoinDistributorGroupRequest request = JoinDistributorGroupRequest.builder()
                .wxToken(null)
                .distributorGroupId(1L)
                .anchorId(1L)
                .avatarUrl("avatarUrl")
                .nickname("nickname")
                .phoneNumber("12345678901")
                .shareName("shareName")
                .actualName("actualName")
                .build();

        // act
        service.joinDistributorGroup(request);

        // assert
        // Expected IllegalArgumentException
    }

    /**
     * 测试joinDistributorGroup方法，当手机号不合法时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinDistributorGroupInvalidPhoneNumber() {
        // arrange
        JoinDistributorGroupRequest request = JoinDistributorGroupRequest.builder()
                .wxToken("wxToken")
                .distributorGroupId(1L)
                .anchorId(1L)
                .avatarUrl("avatarUrl")
                .nickname("nickname")
                .phoneNumber("invalidPhoneNumber")
                .shareName("shareName")
                .actualName("actualName")
                .build();

        // act
        service.joinDistributorGroup(request);

        // assert
        // Expected IllegalArgumentException
    }

    /**
     * 测试becomeDistributorGroup方法，当请求参数完全有效时
     */
    @Test
    public void testBecomeDistributorGroupValidRequest() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, 2L, "groupName", "12345678901", "companyName", "wxToken");
        BecomeDistributorGroupResponse expectedResponse = new BecomeDistributorGroupResponse(2L, "groupName", 1, "成功");
        when(privateLiveAnchorDistributorGroupAppService.becomeDistributorGroup(any(BecomeDistributorGroupRequest.class))).thenReturn(expectedResponse);

        // act
        RemoteResponse<BecomeDistributorGroupResponse> response = service.becomeDistributorGroup(request);

        // assert
        verify(privateLiveAnchorDistributorGroupAppService, times(1)).becomeDistributorGroup(request);
        assert response.getData().equals(expectedResponse);
    }

    /**
     * 测试becomeDistributorGroup方法，当请求参数中wxToken为null时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBecomeDistributorGroupWithNullWxToken() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, 2L, "groupName", "12345678901", "companyName", null);

        // act
        service.becomeDistributorGroup(request);

        // assert
        // 预期抛出IllegalArgumentException
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBecomeDistributorGroupWithNullAnchorId() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(null, 2L, "groupName", "12345678901", "companyName", "wxToken");

        // act
        service.becomeDistributorGroup(request);

        // assert
        // 预期抛出IllegalArgumentException
    }



    /**
     * 测试becomeDistributorGroup方法，当请求参数中phoneNumber为空字符串时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBecomeDistributorGroupWithEmptyPhoneNumber() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, 2L, "groupName", "", "companyName", "wxToken");

        // act
        service.becomeDistributorGroup(request);

        // assert
        // 预期抛出IllegalArgumentException
    }

    /**
     * 测试becomeDistributorGroup方法，当请求参数中companyName为空字符串时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBecomeDistributorGroupWithEmptyCompanyName() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, 2L, "groupName", "12345678901", "", "wxToken");

        // act
        service.becomeDistributorGroup(request);

        // assert
        // 预期抛出IllegalArgumentException
    }

    /**
     * 测试becomeDistributorGroup方法，当请求参数中distributorGroupName为空字符串时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBecomeDistributorGroupWithEmptyDistributorGroupName() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, 2L, "", "12345678901", "companyName", "wxToken");

        // act
        service.becomeDistributorGroup(request);

        // assert
        // 预期抛出IllegalArgumentException
    }
}
