package com.sankuai.carnation.distribution.distributor.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.utils.BizDistributorBeanTransfer;
import com.sankuai.carnation.distribution.distributor.dto.operate.BizDistributorOperateDTO;
import com.sankuai.carnation.distribution.utils.ShortCodeUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BizDistributorServiceImplTest {

    @InjectMocks
    private BizDistributorServiceImpl bizDistributorService;

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private BizDistributorBeanTransfer bizDistributorBeanTransfer;

    /**
     * 测试createCommunityDistributor方法，正常情况
     */
    @Test
    public void testCreateCommunityDistributorSuccess() throws Throwable {
        // arrange
        BizDistributorOperateDTO dto = new BizDistributorOperateDTO();
        dto.setBizId("testBizId");
        dto.setBizType(DistributorBizTypeEnum.COMMUNITY.getCode());
        dto.setUserId("testUserId");
        when(distributorRootService.setBizDistributor(any(BizDistributorBO.class))).thenReturn(1L);

        // act
        RemoteResponse<Long> response = bizDistributorService.createCommunityDistributor(dto);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(Long.valueOf(1L), response.getData());
    }

}
