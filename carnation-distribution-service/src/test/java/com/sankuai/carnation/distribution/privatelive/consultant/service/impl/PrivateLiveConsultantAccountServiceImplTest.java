package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.account.ConsultantAccountDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantAccountDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantTaskDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantTaskSearchDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageSortRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.utils.MobileTokenUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveConsultantAccountService;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

public class PrivateLiveConsultantAccountServiceImplTest {

    @InjectMocks
    private PrivateLiveConsultantAccountServiceImpl service;

    @Mock
    private ConsultantAccountDomainService consultantAccountDomainService;

    @Mock
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @Mock
    private DzPrivateLiveConsultantAccountService serviceNew;

    @Mock
    private MobileTokenUtil mobileTokenUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 registerConsultantTask 方法，当 accountDTO 和 taskDTO 都不为 null 时，应成功注册咨询师任务。
     */
    @Test
    public void testRegisterConsultantTaskSuccess() throws Exception {
        // arrange
        PrivateLiveConsultantAccountDTO accountDTO = new PrivateLiveConsultantAccountDTO();
        accountDTO.setId(1L);
        accountDTO.setUnionId("unionId");
        accountDTO.setOpenId("openId");
        accountDTO.setStatus(1);
        PrivateLiveConsultantTaskDTO taskDTO = new PrivateLiveConsultantTaskDTO();
        taskDTO.setId(1L);
        taskDTO.setConsultantId(1L);
        taskDTO.setGroupId("groupId");
        taskDTO.setLiveId("liveId");
        taskDTO.setAnchorId(1L);
        taskDTO.setNickname("nickname");
        taskDTO.setAvatarUrl("avatarUrl");
        taskDTO.setShareName("shareName");
        taskDTO.setActualName("actualName");
        taskDTO.setPhoneNumber("phoneNumber");
        taskDTO.setShopId(1L);
        taskDTO.setWechatNumber("wechatNumber");
        taskDTO.setStatus(1);
        doNothing().when(consultantAccountDomainService).registerTransactional(any(PrivateLiveConsultantAccount.class), any(PrivateLiveConsultantTask.class));
        // act
        RemoteResponse<Boolean> response = service.registerConsultantTask(accountDTO, taskDTO);
        // assert
        assertTrue(response.getData());
    }

    /**
     * 测试loadByTaskId方法，当taskId对应的任务不存在时
     */
    @Test
    public void testLoadByTaskId_TaskNotFound() {
        // arrange
        long taskId = 1L;
        when(privateLiveConsultantTaskRepository.loadById(taskId)).thenReturn(null);
        // act
        RemoteResponse<PrivateLiveConsultantTaskDTO> result = service.loadByTaskId(taskId);
        // assert
        assertNotNull(result);
        assertNull(result.getData());
    }

    /**
     * 测试loadByTaskId方法，当taskId对应的任务存在，但相关联的顾问账户不存在时
     */
    @Test
    public void testLoadByTaskId_TaskFoundAccountNotFound() {
        // arrange
        long taskId = 1L;
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setId(taskId);
        task.setConsultantId(2L);
        when(privateLiveConsultantTaskRepository.loadById(taskId)).thenReturn(task);
        when(privateLiveConsultantAccountRepository.loadById(task.getConsultantId())).thenReturn(null);
        // act
        RemoteResponse<PrivateLiveConsultantTaskDTO> result = service.loadByTaskId(taskId);
        // assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(taskId, result.getData().getId().longValue());
        assertNull(result.getData().getUnionId());
    }

    /**
     * 测试loadByTaskId方法，当taskId对应的任务和相关联的顾问账户都存在时
     */
    @Test
    public void testLoadByTaskId_TaskAndAccountFound() {
        // arrange
        long taskId = 1L;
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setId(taskId);
        task.setConsultantId(2L);
        when(privateLiveConsultantTaskRepository.loadById(taskId)).thenReturn(task);
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setId(task.getConsultantId());
        account.setUnionId("unionId");
        when(privateLiveConsultantAccountRepository.loadById(task.getConsultantId())).thenReturn(account);
        // act
        RemoteResponse<PrivateLiveConsultantTaskDTO> result = service.loadByTaskId(taskId);
        // assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(taskId, result.getData().getId().longValue());
        assertEquals("unionId", result.getData().getUnionId());
    }

    /**
     * 测试loadByTaskIds方法，当传入的taskIds为空的场景
     */
    @Test
    public void testLoadByTaskIdsWithEmptyTaskIds() {
        // arrange
        List<Long> taskIds = Collections.emptyList();
        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByTaskIds(taskIds);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
    }

    /**
     * 测试loadByTaskIds方法，当传入的taskIds不为空，但数据库中没有对应数据的场景
     */
    @Test
    public void testLoadByTaskIdsWithNonEmptyTaskIdsButNoData() {
        // arrange
        List<Long> taskIds = Arrays.asList(1L, 2L);
        when(privateLiveConsultantTaskRepository.loadByIds(taskIds)).thenReturn(Collections.emptyList());
        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByTaskIds(taskIds);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
        verify(privateLiveConsultantTaskRepository, times(1)).loadByIds(taskIds);
    }

    /**
     * 测试loadByTaskIds方法，当传入的taskIds不为空，并且数据库中有对应数据的场景
     */
    @Test
    public void testLoadByTaskIdsWithNonEmptyTaskIdsAndData() {
        // arrange
        List<Long> taskIds = Arrays.asList(1L, 2L);
        List<PrivateLiveConsultantTask> tasks = Arrays.asList(new PrivateLiveConsultantTask(1L, 1L, "group1", "live1", 1L, "nickname1", "avatarUrl1", "shareName1", "actualName1", "phoneNumber1", 1L, "wechatNumber1", 1, null, null, 1), new PrivateLiveConsultantTask(2L, 2L, "group2", "live2", 2L, "nickname2", "avatarUrl2", "shareName2", "actualName2", "phoneNumber2", 2L, "wechatNumber2", 2, null, null, 2));
        when(privateLiveConsultantTaskRepository.loadByIds(taskIds)).thenReturn(tasks);
        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByTaskIds(taskIds);
        // assert
        assertNotNull(response);
        assertFalse(response.getData().isEmpty());
        assertEquals(2, response.getData().size());
        verify(privateLiveConsultantTaskRepository, times(1)).loadByIds(taskIds);
    }

    /**
     * 测试loadByConsultantIdAndLiveId方法，当根据consultantId和liveId能够找到对应的任务时
     */
    @Test
    public void testLoadByConsultantIdAndLiveIdFound() {
        long consultantId = 1L;
        String liveId = "live123";
        PrivateLiveConsultantTask task = mock(PrivateLiveConsultantTask.class);
        when(privateLiveConsultantTaskRepository.loadByConsultantIdAndLiveId(consultantId, liveId)).thenReturn(task);
        when(task.getId()).thenReturn(1L);
        RemoteResponse<PrivateLiveConsultantTaskDTO> response = service.loadByConsultantIdAndLiveId(consultantId, liveId);
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(Long.valueOf(1L), response.getData().getId());
        verify(privateLiveConsultantTaskRepository, times(1)).loadByConsultantIdAndLiveId(consultantId, liveId);
    }

    /**
     * 测试loadByConsultantIdAndLiveId方法，当根据consultantId和liveId找不到对应的任务时
     */
    @Test
    public void testLoadByConsultantIdAndLiveIdNotFound() {
        long consultantId = 1L;
        String liveId = "live123";
        when(privateLiveConsultantTaskRepository.loadByConsultantIdAndLiveId(consultantId, liveId)).thenReturn(null);
        RemoteResponse<PrivateLiveConsultantTaskDTO> response = service.loadByConsultantIdAndLiveId(consultantId, liveId);
        assertNotNull(response);
        assertNull(response.getData());
        verify(privateLiveConsultantTaskRepository, times(1)).loadByConsultantIdAndLiveId(consultantId, liveId);
    }

    /**
     * 测试loadByConsultantId方法，当传入的consultantId对应的任务列表为空时
     */
    @Test
    public void testLoadByConsultantIdWithEmptyTaskList() {
        // arrange
        long consultantId = 1L;
        when(privateLiveConsultantTaskRepository.loadByConsultantId(consultantId)).thenReturn(new ArrayList<>());
        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByConsultantId(consultantId);
        // assert
        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertTrue(response.getData().isEmpty());
        verify(privateLiveConsultantTaskRepository, times(1)).loadByConsultantId(consultantId);
    }

    /**
     * 测试loadByConsultantId方法，当repository抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testLoadByConsultantIdWhenRepositoryThrowsException() {
        // arrange
        long consultantId = 1L;
        when(privateLiveConsultantTaskRepository.loadByConsultantId(consultantId)).thenThrow(new RuntimeException());
        // act
        service.loadByConsultantId(consultantId);
        // assert is handled by the expected exception
    }

    /**
     * 测试loadConsultantTasksByLiveId方法，当liveId有效但数据库中无数据时
     */
    @Test
    public void testLoadConsultantTasksByLiveIdWithValidLiveIdAndNoData() {
        // arrange
        String liveId = "validLiveId";
        int pageNo = 1;
        int pageSize = 10;
        when(privateLiveConsultantTaskRepository.pageLoadByLiveId(liveId, 0, pageSize)).thenReturn(new ArrayList<>());
        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadConsultantTasksByLiveId(liveId, pageNo, pageSize);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
        verify(privateLiveConsultantTaskRepository, times(1)).pageLoadByLiveId(liveId, 0, pageSize);
    }

    /**
     * 测试loadConsultantTasksPage方法，当pageSortRequest为null时
     */
    @Test
    public void testLoadConsultantTasksPage_PageSortRequestIsNull() {
        // arrange
        PageSortRequest pageSortRequest = null;
        // act
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> result = service.loadConsultantTasksPage(pageSortRequest);
        // assert
        assertNotNull(result);
        assertEquals("参数不正确", result.getMsg());
    }

    /**
     * 测试loadConsultantTasksPage方法，当pageNum小于等于0时
     */
    @Test
    public void testLoadConsultantTasksPage_PageNumIsInvalid() {
        // arrange
        PageSortRequest pageSortRequest = new PageSortRequest();
        pageSortRequest.setPageNum(0);
        pageSortRequest.setPageSize(10);
        // act
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> result = service.loadConsultantTasksPage(pageSortRequest);
        // assert
        assertNotNull(result);
        assertEquals("参数不正确", result.getMsg());
    }

    /**
     * 测试loadConsultantTasksPage方法，当pageSize小于等于0时
     */
    @Test
    public void testLoadConsultantTasksPage_PageSizeIsInvalid() {
        // arrange
        PageSortRequest pageSortRequest = new PageSortRequest();
        pageSortRequest.setPageNum(1);
        pageSortRequest.setPageSize(0);
        // act
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> result = service.loadConsultantTasksPage(pageSortRequest);
        // assert
        assertNotNull(result);
        assertEquals("参数不正确", result.getMsg());
    }

    /**
     * 测试loadByOpenId方法，当传入的openId对应的账户存在时
     */
    @Test
    public void testLoadByOpenIdWhenAccountExists() {
        // arrange
        String openId = "testOpenId";
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount(1L, "testUnionId", openId, 2L, 1, null, null);
        when(privateLiveConsultantAccountRepository.loadByUnionId(openId)).thenReturn(account);
        // act
        RemoteResponse<PrivateLiveConsultantAccountDTO> response = service.loadByOpenId(openId);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(Long.valueOf(1), response.getData().getId());
        assertEquals("testUnionId", response.getData().getUnionId());
        assertEquals(openId, response.getData().getOpenId());
        assertEquals(Integer.valueOf(1), response.getData().getStatus());
        verify(privateLiveConsultantAccountRepository, times(1)).loadByUnionId(openId);
    }

    /**
     * 测试loadByOpenId方法，当传入的openId对应的账户不存在时
     */
    @Test
    public void testLoadByOpenIdWhenAccountDoesNotExist() {
        // arrange
        String openId = "nonExistingOpenId";
        when(privateLiveConsultantAccountRepository.loadByUnionId(openId)).thenReturn(null);
        // act
        RemoteResponse<PrivateLiveConsultantAccountDTO> response = service.loadByOpenId(openId);
        // assert
        assertNotNull(response);
        assertNull(response.getData());
        verify(privateLiveConsultantAccountRepository, times(1)).loadByUnionId(openId);
    }

    /**
     * 测试loadByOpenId方法，当传入的openId为null时
     */
    @Test
    public void testLoadByOpenIdWhenOpenIdIsNull() {
        // arrange
        String openId = null;
        // act
        RemoteResponse<PrivateLiveConsultantAccountDTO> response = service.loadByOpenId(openId);
        // assert
        assertNotNull(response);
        assertNull(response.getData());
        verify(privateLiveConsultantAccountRepository, never()).loadByUnionId(anyString());
    }

    /**
     * 测试loadByOpenId方法，当repository抛出异常时
     */
    @Test(expected = RuntimeException.class)
    public void testLoadByOpenIdWhenRepositoryThrowsException() {
        // arrange
        String openId = "testOpenId";
        when(privateLiveConsultantAccountRepository.loadByUnionId(openId)).thenThrow(new RuntimeException());
        // act
        service.loadByOpenId(openId);
        // assert
        // Expected exception
    }





    @Test
    public void testRegisterConsultantTaskOld_Success() {
        PrivateLiveConsultantAccountDTO accountDTO = new PrivateLiveConsultantAccountDTO();
        PrivateLiveConsultantTaskDTO taskDTO = new PrivateLiveConsultantTaskDTO();

        RemoteResponse<Boolean> response = service.registerConsultantTaskOld(accountDTO, taskDTO);

        assertTrue(response.isSuccess());
        assertTrue(response.getData());
        verify(consultantAccountDomainService).registerTransactional(any(PrivateLiveConsultantAccount.class), any(PrivateLiveConsultantTask.class));
    }


    @Test
    public void testRegisterConsultantTaskNew_Success() throws Throwable {
        // arrange
        PrivateLiveConsultantAccountDTO accountDTO = new PrivateLiveConsultantAccountDTO();
        PrivateLiveConsultantTaskDTO taskDTO = new PrivateLiveConsultantTaskDTO();
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(serviceNew.registerConsultantTask(any(PrivateLiveConsultantAccountDTO.class), any(PrivateLiveConsultantTaskDTO.class))).thenReturn(success);
        // act
        RemoteResponse<Boolean> response = service.registerConsultantTaskNew(accountDTO, taskDTO);
        // assert
        assertTrue("应该成功注册顾问任务", response.getData());
    }

    @Test
    public void testLoadByTaskIdOld_Success() {
        // 准备测试数据
        long taskId = 1L;
        PrivateLiveConsultantTask mockTask = new PrivateLiveConsultantTask();
        mockTask.setId(taskId);
        mockTask.setConsultantId(100L);
        mockTask.setLiveId("live123");
        mockTask.setNickname("Test Consultant");

        // 模拟repository的行为
        when(privateLiveConsultantTaskRepository.loadById(taskId)).thenReturn(mockTask);

        // 调用被测试的方法
        RemoteResponse<PrivateLiveConsultantTaskDTO> response = service.loadByTaskIdOld(taskId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        PrivateLiveConsultantTaskDTO resultDTO = response.getData();
        assertEquals(taskId, resultDTO.getId().longValue());
        assertEquals(100L, resultDTO.getConsultantId().longValue());
        assertEquals("live123", resultDTO.getLiveId());
        assertEquals("Test Consultant", resultDTO.getNickname());

        // 验证repository方法被调用
        verify(privateLiveConsultantTaskRepository).loadById(taskId);
    }

    @Test
    public void testLoadByTaskIdNew_Success() {
        // arrange
        long taskId = 1L;
        PrivateLiveConsultantTaskDTO mockTaskDTO = new PrivateLiveConsultantTaskDTO();
        mockTaskDTO.setId(taskId);
        mockTaskDTO.setConsultantId(100L);
        mockTaskDTO.setLiveId("live123");
        mockTaskDTO.setNickname("Test Consultant");
        RemoteResponse<PrivateLiveConsultantTaskDTO> mockResponse = RemoteResponse.success(mockTaskDTO);

        when(serviceNew.loadByTaskId(taskId)).thenReturn(mockResponse);

        // act
        RemoteResponse<PrivateLiveConsultantTaskDTO> response = service.loadByTaskIdNew(taskId);

        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        PrivateLiveConsultantTaskDTO resultDTO = response.getData();
        assertEquals(taskId, resultDTO.getId().longValue());
        assertEquals(100L, resultDTO.getConsultantId().longValue());
        assertEquals("live123", resultDTO.getLiveId());
        assertEquals("Test Consultant", resultDTO.getNickname());

        // verify
        verify(serviceNew).loadByTaskId(taskId);
    }

    private PrivateLiveConsultantTask createMockTask(Long id, Long consultantId, String liveId) {
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setId(id);
        task.setConsultantId(consultantId);
        task.setLiveId(liveId);
        // 设置其他必要的字段...
        return task;
    }

    @Test
    public void testLoadByTaskIdsOld_Success() {
        // 准备测试数据
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);
        List<PrivateLiveConsultantTask> mockTasks = Arrays.asList(
                createMockTask(1L, 100L, "live1"),
                createMockTask(2L, 101L, "live2"),
                createMockTask(3L, 102L, "live3")
        );

        // 模拟repository的行为
        when(privateLiveConsultantTaskRepository.loadByIds(taskIds)).thenReturn(mockTasks);

        // 调用被测试的方法
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByTaskIdsOld(taskIds);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(3, response.getData().size());

        // 验证DTO转换是否正确
        PrivateLiveConsultantTaskDTO firstDto = response.getData().get(0);
        assertEquals(1L, firstDto.getId().longValue());
        assertEquals(100L, firstDto.getConsultantId().longValue());
        assertEquals("live1", firstDto.getLiveId());

        // 验证repository方法被调用
        verify(privateLiveConsultantTaskRepository).loadByIds(taskIds);
    }

    @Test
    public void testLoadByTaskIdsNew_Success() throws Throwable {
        // arrange
        List<Long> taskIds = Arrays.asList(1L, 2L, 3L);
        List<PrivateLiveConsultantTaskDTO> mockTaskDTOs = Arrays.asList(
                new PrivateLiveConsultantTaskDTO(), new PrivateLiveConsultantTaskDTO(), new PrivateLiveConsultantTaskDTO()
        );
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> expectedResponse = RemoteResponse.success(mockTaskDTOs);
        when(serviceNew.loadByTaskIds(taskIds)).thenReturn(expectedResponse);

        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByTaskIdsNew(taskIds);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(3, response.getData().size());
        verify(serviceNew).loadByTaskIds(taskIds);
    }

    @Test
    public void testLoadByConsultantIdAndLiveIdOld_Success() {
        // 准备测试数据
        long consultantId = 1L;
        String liveId = "live123";
        PrivateLiveConsultantTask mockTask = new PrivateLiveConsultantTask();
        mockTask.setId(1L);
        mockTask.setConsultantId(consultantId);
        mockTask.setLiveId(liveId);
        mockTask.setNickname("Test Consultant");

        // 模拟repository的行为
        when(privateLiveConsultantTaskRepository.loadByConsultantIdAndLiveId(consultantId, liveId)).thenReturn(mockTask);

        // 调用被测试的方法
        RemoteResponse<PrivateLiveConsultantTaskDTO> response = service.loadByConsultantIdAndLiveIdOld(consultantId, liveId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        PrivateLiveConsultantTaskDTO resultDTO = response.getData();
        assertEquals(1L, resultDTO.getId().longValue());
        assertEquals(consultantId, resultDTO.getConsultantId().longValue());
        assertEquals(liveId, resultDTO.getLiveId());
        assertEquals("Test Consultant", resultDTO.getNickname());

        // 验证repository方法被调用
        verify(privateLiveConsultantTaskRepository).loadByConsultantIdAndLiveId(consultantId, liveId);
    }

    @Test
    public void testLoadByConsultantIdAndLiveIdNew_Success() {
        // arrange
        long consultantId = 1L;
        String liveId = "live123";
        PrivateLiveConsultantTaskDTO mockTaskDTO = new PrivateLiveConsultantTaskDTO();
        mockTaskDTO.setId(1L);
        mockTaskDTO.setConsultantId(consultantId);
        mockTaskDTO.setLiveId(liveId);
        mockTaskDTO.setNickname("Test Consultant");
        RemoteResponse<PrivateLiveConsultantTaskDTO> mockResponse = RemoteResponse.success(mockTaskDTO);

        when(serviceNew.loadByConsultantIdAndLiveId(consultantId, liveId)).thenReturn(mockResponse);

        // act
        RemoteResponse<PrivateLiveConsultantTaskDTO> response = service.loadByConsultantIdAndLiveIdNew(consultantId, liveId);

        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        PrivateLiveConsultantTaskDTO resultDTO = response.getData();
        assertEquals(1L, resultDTO.getId().longValue());
        assertEquals(consultantId, resultDTO.getConsultantId().longValue());
        assertEquals(liveId, resultDTO.getLiveId());
        assertEquals("Test Consultant", resultDTO.getNickname());

        // verify
        verify(serviceNew).loadByConsultantIdAndLiveId(consultantId, liveId);
    }

    @Test
    public void testLoadByConsultantIdOld_Success() {
        // 准备测试数据
        long consultantId = 1L;
        List<PrivateLiveConsultantTask> mockTasks = Arrays.asList(
                createMockTask(1L, consultantId, "live1"),
                createMockTask(2L, consultantId, "live2")
        );

        // 模拟repository的行为
        when(privateLiveConsultantTaskRepository.loadByConsultantId(consultantId)).thenReturn(mockTasks);

        // 调用被测试的方法
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByConsultantIdOld(consultantId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());

        // 验证DTO转换是否正确
        PrivateLiveConsultantTaskDTO firstDto = response.getData().get(0);
        assertEquals(1L, firstDto.getId().longValue());
        assertEquals(consultantId, firstDto.getConsultantId().longValue());
        assertEquals("live1", firstDto.getLiveId());

        PrivateLiveConsultantTaskDTO secondDto = response.getData().get(1);
        assertEquals(2L, secondDto.getId().longValue());
        assertEquals(consultantId, secondDto.getConsultantId().longValue());
        assertEquals("live2", secondDto.getLiveId());

        // 验证repository方法被调用
        verify(privateLiveConsultantTaskRepository).loadByConsultantId(consultantId);
    }

    @Test
    public void testLoadByConsultantIdNew_Success() throws Throwable {
        // arrange
        long consultantId = 1L;
        List<PrivateLiveConsultantTaskDTO> mockTaskDTOs = Arrays.asList(
                new PrivateLiveConsultantTaskDTO(),
                new PrivateLiveConsultantTaskDTO()
        );
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> expectedResponse = RemoteResponse.success(mockTaskDTOs);
        when(serviceNew.loadByConsultantId(consultantId)).thenReturn(expectedResponse);

        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadByConsultantIdNew(consultantId);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(2, response.getData().size());
        verify(serviceNew).loadByConsultantId(consultantId);
    }

    @Test
    public void testLoadConsultantTasksByLiveIdOld_Success() {
        // 准备测试数据
        String liveId = "live123";
        int pageNo = 1;
        int pageSize = 10;
        List<PrivateLiveConsultantTask> mockTasks = Arrays.asList(
                createMockTask(1L, 100L, liveId),
                createMockTask(2L, 101L, liveId)
        );

        // 模拟repository的行为
        when(privateLiveConsultantTaskRepository.pageLoadByLiveId(liveId, 0, pageSize)).thenReturn(mockTasks);

        // 调用被测试的方法
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadConsultantTasksByLiveIdOld(liveId, pageNo, pageSize);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());

        // 验证DTO转换是否正确
        PrivateLiveConsultantTaskDTO firstDto = response.getData().get(0);
        assertEquals(1L, firstDto.getId().longValue());
        assertEquals(100L, firstDto.getConsultantId().longValue());
        assertEquals(liveId, firstDto.getLiveId());

        PrivateLiveConsultantTaskDTO secondDto = response.getData().get(1);
        assertEquals(2L, secondDto.getId().longValue());
        assertEquals(101L, secondDto.getConsultantId().longValue());
        assertEquals(liveId, secondDto.getLiveId());

        // 验证repository方法被调用
        verify(privateLiveConsultantTaskRepository).pageLoadByLiveId(liveId, 0, pageSize);
    }


    @Test
    public void testLoadConsultantTasksByLiveIdNew_Success() {
        // arrange
        String liveId = "live123";
        int pageNo = 1;
        int pageSize = 10;
        List<PrivateLiveConsultantTaskDTO> mockTasks = Arrays.asList(
                new PrivateLiveConsultantTaskDTO(),
                new PrivateLiveConsultantTaskDTO()
        );
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> expectedResponse = RemoteResponse.success(mockTasks);
        when(serviceNew.loadConsultantTasksByLiveId(liveId, pageNo, pageSize)).thenReturn(expectedResponse);

        // act
        RemoteResponse<List<PrivateLiveConsultantTaskDTO>> response = service.loadConsultantTasksByLiveIdNew(liveId, pageNo, pageSize);

        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().size());

        // verify
        verify(serviceNew).loadConsultantTasksByLiveId(liveId, pageNo, pageSize);
    }

    @Test
    public void testLoadConsultantTasksPageOld_Success() throws Exception {
        // arrange
        PageSortRequest pageSortRequest = new PageSortRequest();
        pageSortRequest.setPageNum(1);
        pageSortRequest.setPageSize(10);
        pageSortRequest.setCondition(new PrivateLiveConsultantTaskSearchDTO());
        pageSortRequest.setSortMap(new HashMap<>());

        List<PrivateLiveConsultantTask> mockTasks = Arrays.asList(
                new PrivateLiveConsultantTask(1L, 100L, "group1", "live1", 200L, "nickname1", "avatarUrl1", "shareName1", "actualName1", "phoneNumber1", 300L, "wechatNumber1", 1, null, null, 1),
                new PrivateLiveConsultantTask(2L, 101L, "group2", "live2", 201L, "nickname2", "avatarUrl2", "shareName2", "actualName2", "phoneNumber2", 301L, "wechatNumber2", 2, null, null, 2)
        );

        when(privateLiveConsultantTaskRepository.pageLoadConditionalSorted(any(), any(), any(), any(), any(), anyInt(), anyInt())).thenReturn(mockTasks);
        when(privateLiveConsultantTaskRepository.countConditional(any(), any(), any(), any())).thenReturn(2);
        when(mobileTokenUtil.getMobileMask(anyString())).thenReturn("masked_number");
        when(privateLiveConsultantAccountRepository.batchLoadById(anyList())).thenReturn(Arrays.asList(
                new PrivateLiveConsultantAccount(100L, "unionId1", "openId1", 400L, 1, null, null),
                new PrivateLiveConsultantAccount(101L, "unionId2", "openId2", 401L, 2, null, null)
        ));

        // act
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> response = service.loadConsultantTasksPageOld(pageSortRequest);

        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(2, response.getData().getList().size());
        verify(privateLiveConsultantTaskRepository).pageLoadConditionalSorted(any(), any(), any(), any(), any(), anyInt(), anyInt());
        verify(privateLiveConsultantTaskRepository).countConditional(any(), any(), any(), any());
        verify(privateLiveConsultantAccountRepository).batchLoadById(anyList());
        verify(mobileTokenUtil, times(2)).getMobileMask(anyString());
    }

    @Test
    public void testLoadConsultantTasksPageNew_Success() {
        // arrange
        PageSortRequest pageSortRequest = new PageSortRequest();
        pageSortRequest.setPageNum(1);
        pageSortRequest.setPageSize(10);
        PageDataDTO<PrivateLiveConsultantTaskDTO> pageDataDTO = new PageDataDTO<>();
        pageDataDTO.setList(Arrays.asList(new PrivateLiveConsultantTaskDTO(), new PrivateLiveConsultantTaskDTO()));
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> expectedResponse = RemoteResponse.success(pageDataDTO);
        when(serviceNew.loadConsultantTasksPage(pageSortRequest)).thenReturn(expectedResponse);

        // act
        RemoteResponse<PageDataDTO<PrivateLiveConsultantTaskDTO>> response = service.loadConsultantTasksPageNew(pageSortRequest);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(2, response.getData().getList().size());

        // verify
        verify(serviceNew, times(1)).loadConsultantTasksPage(pageSortRequest);
    }

    private PrivateLiveConsultantAccount createMockAccount(Long id, String unionId) {
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setId(id);
        account.setUnionId(unionId);
        // 设置其他必要的字段...
        return account;
    }

    @Test
    public void testLoadByOpenIdOld_Success() {
        // 准备测试数据
        String openId = "testOpenId";
        PrivateLiveConsultantAccount mockAccount = new PrivateLiveConsultantAccount();
        mockAccount.setId(1L);
        mockAccount.setUnionId(openId);
        mockAccount.setOpenId(openId);
        mockAccount.setStatus(1);

        // 模拟repository的行为
        when(privateLiveConsultantAccountRepository.loadByUnionId(openId)).thenReturn(mockAccount);

        // 调用被测试的方法
        RemoteResponse<PrivateLiveConsultantAccountDTO> response = service.loadByOpenIdOld(openId);

        // 验证结果
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        PrivateLiveConsultantAccountDTO resultDTO = response.getData();
        assertEquals(1L, resultDTO.getId().longValue());
        assertEquals(openId, resultDTO.getUnionId());
        assertEquals(openId, resultDTO.getOpenId());
        assertEquals(1, resultDTO.getStatus().intValue());

        // 验证repository方法被调用
        verify(privateLiveConsultantAccountRepository).loadByUnionId(openId);
    }

    @Test
    public void testLoadByOpenIdNew_Success() throws Throwable {
        // arrange
        String openId = "testOpenId";
        PrivateLiveConsultantAccountDTO mockAccountDTO = new PrivateLiveConsultantAccountDTO();
        mockAccountDTO.setId(1L);
        mockAccountDTO.setUnionId(openId);
        mockAccountDTO.setOpenId(openId);
        mockAccountDTO.setStatus(1);
        RemoteResponse<PrivateLiveConsultantAccountDTO> success = RemoteResponse.success(mockAccountDTO);


        when(serviceNew.loadByOpenId(openId)).thenReturn(success);

        // act
        RemoteResponse<PrivateLiveConsultantAccountDTO> response = service.loadByOpenIdNew(openId);

        // assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(Long.valueOf(1L), response.getData().getId());
        assertEquals(openId, response.getData().getUnionId());
        assertEquals(openId, response.getData().getOpenId());
        assertEquals(Integer.valueOf(1), response.getData().getStatus());

        verify(serviceNew).loadByOpenId(openId);
    }

}
