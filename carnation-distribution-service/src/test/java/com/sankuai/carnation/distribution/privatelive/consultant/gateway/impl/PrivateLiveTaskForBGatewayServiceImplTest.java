package com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveTaskManageOperateRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.dzrtc.privatelive.operation.api.common.ResponseDTO;
import com.sankuai.dzrtc.privatelive.operation.api.enums.AuthCodeEnum;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

/**
 * 测试PrivateLiveTaskForBGatewayServiceImpl中manageConsultantTaskApply方法对AnchorAuthUtils.checkAuth调用的逻辑
 */
public class PrivateLiveTaskForBGatewayServiceImplTest {

    @InjectMocks
    private PrivateLiveTaskForBGatewayServiceImpl service;

    @Mock
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试当AnchorAuthUtils.checkAuth返回null时，应返回"登陆主播id无权限"
     */
    @Test
    public void testManageConsultantTaskApply_AuthResponseIsNull() {
        PrivateLiveTaskManageOperateRequest request = new PrivateLiveTaskManageOperateRequest();
        request.setId(1L);
        request.setOperateType(1);
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setLiveId("liveId");
        task.setTaskType(1);
        Mockito.when(taskRepository.loadById(any(Long.class))).thenReturn(task);

        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(() -> AnchorAuthUtils.checkAuth(eq(AuthCodeEnum.DEFAULT_CODE.getCode()), eq("liveId"))).thenReturn(null);

            RemoteResponse<Boolean> result = service.manageConsultantTaskApply(request);

            assertFalse(result.isSuccess());
            assertEquals("登陆主播id无权限", result.getMsg());
        }
    }

    /**
     * 测试当AnchorAuthUtils.checkAuth返回的ResponseDTO.isSuccess为false时，应返回"登陆主播id无权限"
     */
    @Test
    public void testManageConsultantTaskApply_AuthResponseIsNotSuccess() {
        PrivateLiveTaskManageOperateRequest request = new PrivateLiveTaskManageOperateRequest();
        request.setId(1L);
        request.setOperateType(1);
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setLiveId("liveId");
        task.setTaskType(1);
        Mockito.when(taskRepository.loadById(any(Long.class))).thenReturn(task);

        ResponseDTO<Boolean> responseDTO = new ResponseDTO<>();
        responseDTO.setData(false);

        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(() -> AnchorAuthUtils.checkAuth(eq(AuthCodeEnum.DEFAULT_CODE.getCode()), eq("liveId"))).thenReturn(responseDTO);

            RemoteResponse<Boolean> result = service.manageConsultantTaskApply(request);

            assertFalse(result.isSuccess());
            assertEquals("登陆主播id无权限", result.getMsg());
        }
    }

    /**
     * 测试当AnchorAuthUtils.checkAuth返回的ResponseDTO.getData为false时，应返回"登陆主播id无权限"
     */
    @Test
    public void testManageConsultantTaskApply_AuthResponseDataIsFalse() {
        PrivateLiveTaskManageOperateRequest request = new PrivateLiveTaskManageOperateRequest();
        request.setId(1L);
        request.setOperateType(1);
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setLiveId("liveId");
        task.setTaskType(1);
        Mockito.when(taskRepository.loadById(any(Long.class))).thenReturn(task);

        ResponseDTO<Boolean> responseDTO = new ResponseDTO<>();
        responseDTO.setData(false);

        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(() -> AnchorAuthUtils.checkAuth(eq(AuthCodeEnum.DEFAULT_CODE.getCode()), eq("liveId"))).thenReturn(responseDTO);

            RemoteResponse<Boolean> result = service.manageConsultantTaskApply(request);

            assertFalse(result.isSuccess());
            assertEquals("登陆主播id无权限", result.getMsg());
        }
    }
}
