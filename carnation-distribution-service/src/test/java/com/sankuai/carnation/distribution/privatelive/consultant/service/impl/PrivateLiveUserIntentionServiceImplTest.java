package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import static org.junit.Assert.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.appication.DistributorAppService;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.repository.DistributorRepository;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.wechat.WechatUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.bo.ConsultantUserStatisticBO;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.group.GroupUserInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.intention.modify.PrivateLiveIntentionModifyDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveIntentionModifyRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveUserBindTaskRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.QueryUserIntentionTypeByUserIdsRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.QueryUserIntentionTypeByUserIdsResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.sourceboard.*;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.GroupUserTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.PrivateLiveIntentionException;
import com.sankuai.carnation.distribution.privatelive.consultant.migrate.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantSummaryRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveIntentionExpireTimeRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteRelationInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteWxUserInfo;
import com.sankuai.dz.srcm.privatelive.community.dto.CommunityCustomerDTO;
import com.sankuai.dz.srcm.privatelive.community.dto.CommunityLiveUserTagDetailDTO;
import com.sankuai.dz.srcm.privatelive.community.dto.CustomerListByTagRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.miniprogram.WechatUserInfoDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.saasconfig.TraceAbilityTypeEnum;
import java.util.*;

import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveUserIntentionService;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveUserIntentionServiceImplTest {

    @InjectMocks
    private PrivateLiveUserIntentionServiceImpl privateLiveUserIntentionService;

    @Mock
    private PrivateLiveUserIntentionDomainService userIntentionDomainService;

    @Mock
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Mock
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private DistributorRepository distributorRepository;

    @Mock
    private DistributorAppService distributorAppService;

    @Mock
    private DistributorGroupAppService distributorGroupAppService;

    @Mock
    private PrivateLiveConsultantSummaryRepository privateLiveConsultantSummaryRepository;

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;

    @Mock
    private PrivateLiveIntentionExpireTimeRepository intentionExpireTimeRepository;

    @Mock
    private PrivateLiveConsultantVerifyService privateLiveConsultantVerifyService;

    @Mock
    private PrivateLiveIntentionModifyDomainService modifyDomainService;

    @Mock
    private PrivateLiveAclService privateLiveAclService;

    @Mock
    private PrivateLiveDistributorCodeService distributorCodeService;

    @Mock
    private WechatUserAclService wechatUserAclService;

    @Mock
    private PrivateLiveIntentionExpireServiceImpl privateLiveIntentionExpireService;

    @InjectMocks
    private PrivateLiveUserIntentionServiceImpl service;

    private QueryUserIntentionTypeByUserIdsRequest request;

    @Mock
    private DzPrivateLiveUserIntentionService dzPrivateLiveUserIntentionService;

    @Test
    public void testUserStateChangeGroupUserInfoIsNull() throws Throwable {
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.userStateChange(null);
        assertEquals(RemoteResponse.fail(null).getCode(), response.getCode());
    }

    @Test
    public void testUserStateChangeAllIdsAreEmpty() throws Throwable {
        GroupUserInfo groupUserInfo = new GroupUserInfo();
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.userStateChange(groupUserInfo);
        assertEquals(RemoteResponse.success(true).getCode(), response.getCode());
        verify(userIntentionDomainService, never()).updateIntentionResultAndLog(any(), any(), any());
        verify(userIntentionDomainService, never()).updateIntentionStatusAndLog(any(), anyInt(), any());
    }

    @Test
    public void testUserStateChangeNoUserIntentionResult() throws Throwable {
        GroupUserInfo groupUserInfo = new GroupUserInfo();
        groupUserInfo.setWxId("wxId");
        when(userIntentionResultRepository.queryByWxId(anyString(), anyList())).thenReturn(Collections.emptyList());
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.userStateChange(groupUserInfo);
        assertEquals(RemoteResponse.success(true).getCode(), response.getCode());
        verify(userIntentionDomainService, never()).updateIntentionResultAndLog(any(), any(), any());
        verify(userIntentionDomainService, never()).updateIntentionStatusAndLog(any(), anyInt(), any());
    }

    @Test
    public void testUserStateChangeException() throws Throwable {
        GroupUserInfo groupUserInfo = new GroupUserInfo();
        groupUserInfo.setWxId("wxId");
        PrivateLiveUserIntentionResult userIntentionResult = new PrivateLiveUserIntentionResult();
        userIntentionResult.setLiveId("liveId");
        when(userIntentionResultRepository.queryByWxId(anyString(), anyList())).thenReturn(Collections.singletonList(userIntentionResult));
        doThrow(new RuntimeException()).when(userIntentionDomainService).updateIntentionResultAndLog(any(), any(), any());
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.userStateChange(groupUserInfo);
        assertEquals(RemoteResponse.fail(null).getCode(), response.getCode());
        verify(userIntentionDomainService).updateIntentionResultAndLog(any(), any(), any());
    }

    @Test
    public void testUserStateChangeOneLiveId() throws Throwable {
        GroupUserInfo groupUserInfo = new GroupUserInfo();
        groupUserInfo.setWxId("wxId");
        PrivateLiveUserIntentionResult userIntentionResult = new PrivateLiveUserIntentionResult();
        userIntentionResult.setLiveId("liveId");
        when(userIntentionResultRepository.queryByWxId(anyString(), anyList())).thenReturn(Collections.singletonList(userIntentionResult));
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.userStateChange(groupUserInfo);
        assertEquals(RemoteResponse.success(true).getCode(), response.getCode());
        verify(userIntentionResultRepository).queryByWxId(anyString(), anyList());
        verify(userIntentionDomainService).updateIntentionResultAndLog(any(), any(), any());
        verify(userIntentionDomainService, never()).updateIntentionStatusAndLog(any(), anyInt(), any());
    }

    @Test
    public void testQueryUserBindTaskInvalidInput() {
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        RemoteResponse<Long> response = privateLiveUserIntentionService.queryUserBindTask(request);
        assertNotNull(response);
        assertTrue(response.getMsg().contains("输入参数不合法"));
    }

    @Test
    public void testQueryUserBindTaskInvalidInputLiveId() {
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        request.setWxId("wxId");
        RemoteResponse<Long> response = privateLiveUserIntentionService.queryUserBindTask(request);
        assertNotNull(response);
        assertTrue(response.getMsg().contains("输入参数不合法"));
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryUserBindTaskEmptyResult() {
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        request.setWxId("wxId");
        request.setLiveId("liveId");
        when(userIntentionResultRepository.forceQuery(any())).thenReturn(Collections.emptyList());
        RemoteResponse<Long> response = privateLiveUserIntentionService.queryUserBindTask(request);
        assertNotNull(response);
        assertNull(response.getData());
    }

    /**
     * 测试查询结果不为空的情况
     */
    @Test
    public void testQueryUserBindTaskWithResult() {
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        request.setWxId("wxId");
        request.setLiveId("liveId");
        PrivateLiveUserIntentionResult result = new PrivateLiveUserIntentionResult();
        result.setConsultantTaskId(1L);
        when(userIntentionResultRepository.forceQuery(any())).thenReturn(Collections.singletonList(result));
        RemoteResponse<Long> response = privateLiveUserIntentionService.queryUserBindTask(request);
        assertNotNull(response);
        assertEquals(Long.valueOf(1L), response.getData());
    }

    /**
     * 测试方法执行异常的情况
     */
    @Test
    public void testQueryUserBindTaskException() {
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        request.setWxId("wxId");
        request.setLiveId("liveId");
        when(userIntentionResultRepository.forceQuery(any())).thenThrow(new RuntimeException("Database error"));
        RemoteResponse<Long> response = privateLiveUserIntentionService.queryUserBindTask(request);
        assertNotNull(response);
        assertTrue(response.getMsg().contains("Database error"));
    }

    @Test
    public void testPageQuerySourceBoardConsultList_invalid() {
    }

    /**
     * 测试场景：当存在有效的查询结果时
     */
//    @Test
//    public void testPageQuerySourceBoardConsultList_ValidResults() throws TException {
//        // arrange
//        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
//        request.setLiveId("liveId");
//        request.setPageNo(1);
//        request.setPageSize(10);
//        request.setTaskType(1);
//        request.setDistributorGroupIds(Lists.newArrayList(1L));
//        List<PrivateLiveConsultantTask> commontaskList = Lists.newArrayList();
//        PrivateLiveConsultantTask commonTask = new PrivateLiveConsultantTask();
//        commonTask.setId(1L);
//        commonTask.setConsultantId(1L);
//        commontaskList.add(commonTask);
//        List<PrivateLiveConsultantTask> distributortaskList = Lists.newArrayList();
//        PrivateLiveConsultantTask distributorTask = new PrivateLiveConsultantTask();
//        distributorTask.setId(2L);
//        distributorTask.setConsultantId(2L);
//        distributortaskList.add(distributorTask);
//        List<DistributorModel> distributorBOs = Lists.newArrayList();
//        DistributorModel distributorModel = new DistributorModel();
//        distributorModel.setDistributorId(1L);
//        distributorModel.setAccountId(2L);
//        distributorBOs.add(distributorModel);
//        when(privateLiveConsultantTaskRepository.loadByTypeAndConsultantId("liveId", 1, Lists.newArrayList())).thenReturn(commontaskList);
//        when(distributorAppService.queryAllByDistributorGroupIds(Lists.newArrayList(1L))).thenReturn(distributorBOs);
//        when(privateLiveConsultantTaskRepository.loadByTypeAndConsultantId("liveId", 2, Lists.newArrayList(2L))).thenReturn(distributortaskList);
//        when(privateLiveConsultantTaskRepository.countByCondition("liveId", null, null, Lists.newArrayList(1L, 2L))).thenReturn(1L);
//        List<PrivateLiveConsultantTask> consultantTasks = new ArrayList<>();
//        PrivateLiveConsultantTask consultantTask = new PrivateLiveConsultantTask();
//        consultantTask.setId(2L);
//        consultantTask.setConsultantId(2L);
//        consultantTask.setTaskType(2);
//        consultantTasks.add(consultantTask);
//        when(privateLiveConsultantTaskRepository.pageLoadByCondition(anyString(), any(), any(), anyList(), anyInt(), anyInt())).thenReturn(consultantTasks);
//        List<DistributorGroupModel> distributorGroupModels = new ArrayList<>();
//        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
//        distributorGroupModel.setAccountId(2L);
//        distributorGroupModels.add(distributorGroupModel);
////        when(distributorGroupAppService.getRelationDistributorGroupList(anyList())).thenReturn(distributorGroupModels);
//        // act
//        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> response = privateLiveUserIntentionService.pageQuerySourceBoardConsultList(request);
//        // assert
//        assertNotNull(response);
//        assertTrue(response.isSuccess());
//        assertNotNull(response.getData().getPageInfoDTO());
//        assertNotNull(response.getData().getList());
//        verify(privateLiveConsultantTaskRepository, times(1)).countByCondition("liveId", null, null, Lists.newArrayList(1L, 2L));
//    }

//    @Test
//    public void testPageQuerySourceBoardConsultUserList_ValidResults() throws TException {
//        // arrange
//        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
//        request.setLiveId("liveId");
//        request.setPageNo(1);
//        request.setPageSize(10);
//        request.setTaskType(1);
//        request.setDistributorGroupIds(Lists.newArrayList(1L));
//        List<PrivateLiveConsultantTask> commontaskList = Lists.newArrayList();
//        PrivateLiveConsultantTask commonTask = new PrivateLiveConsultantTask();
//        commonTask.setId(1L);
//        commonTask.setConsultantId(1L);
//        commontaskList.add(commonTask);
//        List<PrivateLiveConsultantTask> distributortaskList = Lists.newArrayList();
//        PrivateLiveConsultantTask distributorTask = new PrivateLiveConsultantTask();
//        distributorTask.setId(2L);
//        distributorTask.setConsultantId(2L);
//        distributortaskList.add(distributorTask);
//        List<DistributorModel> distributorBOs = Lists.newArrayList();
//        DistributorModel distributorModel = new DistributorModel();
//        distributorModel.setDistributorId(1L);
//        distributorModel.setAccountId(2L);
//        distributorBOs.add(distributorModel);
//        when(privateLiveConsultantTaskRepository.loadByTypeAndConsultantId("liveId", 1, Lists.newArrayList())).thenReturn(commontaskList);
//        when(distributorAppService.queryAllByDistributorGroupIds(Lists.newArrayList(1L))).thenReturn(distributorBOs);
//        when(privateLiveConsultantTaskRepository.loadByTypeAndConsultantId("liveId", 2, Lists.newArrayList(2L))).thenReturn(distributortaskList);
//        when(privateLiveConsultantTaskRepository.countByCondition("liveId", null, null, Lists.newArrayList(1L, 2L))).thenReturn(1L);
//        List<PrivateLiveConsultantTask> consultantTasks = new ArrayList<>();
//        PrivateLiveConsultantTask consultantTask = new PrivateLiveConsultantTask();
//        consultantTask.setId(2L);
//        consultantTask.setConsultantId(2L);
//        consultantTask.setTaskType(2);
//        consultantTasks.add(consultantTask);
//        when(privateLiveConsultantTaskRepository.pageLoadByCondition(anyString(), any(), any(), anyList(), anyInt(), anyInt())).thenReturn(consultantTasks);
//        List<DistributorGroupModel> distributorGroupModels = new ArrayList<>();
//        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
//        distributorGroupModel.setAccountId(2L);
//        distributorGroupModels.add(distributorGroupModel);
//        when(distributorGroupAppService.getRelationDistributorGroupList(anyList())).thenReturn(distributorGroupModels);
//        // act
//        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> response = privateLiveUserIntentionService.pageQuerySourceBoardConsultUserList(request);
//        // assert
//        assertNotNull(response);
//        assertTrue(response.isSuccess());
//        assertNotNull(response.getData().getPageInfoDTO());
//        assertNotNull(response.getData().getList());
//        verify(privateLiveConsultantTaskRepository, times(1)).countByCondition("liveId", null, null, Lists.newArrayList(1L, 2L));
//    }

    @Test
    public void testQuerySourceBoardConsultListSuccess() {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("live1");
        request.setConsultName("consultName");
        request.setConsultPhone("phoneNumber");
        request.setTaskType(1);
        request.setDistributorGroupIds(Arrays.asList(1L, 2L));
        // act
        RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> response = privateLiveUserIntentionService.querySourceBoardConsultList(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    @Test
    public void testQuerySourceBoardConsultUserListSuccess() {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("live1");
        request.setConsultName("consultName");
        request.setConsultPhone("phoneNumber");
        request.setTaskType(1);
        request.setDistributorGroupIds(Arrays.asList(1L, 2L));
        // act
        RemoteResponse<List<PrivateLiveSourceBoardConsultUserStatisticsDTO>> response = privateLiveUserIntentionService.querySourceBoardConsultUserList(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    @Test
    public void testQueryUserInfoByLiveIdWithNormalCase() {
        // arrange
        String liveId = "testLiveId";
        List<PrivateLiveUserTypeBaseCount> userTypeCountList = Lists.newArrayList();
        PrivateLiveUserTypeBaseCount seed = new PrivateLiveUserTypeBaseCount();
        seed.setCount(1);
        seed.setType(GroupUserTypeEnum.SEED_USER.getCode());
        PrivateLiveUserTypeBaseCount fission = new PrivateLiveUserTypeBaseCount();
        fission.setCount(1);
        fission.setType(GroupUserTypeEnum.FISSION_USER.getCode());
        PrivateLiveUserTypeBaseCount link = new PrivateLiveUserTypeBaseCount();
        link.setCount(1);
        link.setType(GroupUserTypeEnum.LINK_USER.getCode());
        PrivateLiveUserTypeBaseCount unEnter = new PrivateLiveUserTypeBaseCount();
        unEnter.setCount(1);
        unEnter.setType(GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode());
        userTypeCountList.add(seed);
        userTypeCountList.add(fission);
        userTypeCountList.add(link);
        userTypeCountList.add(unEnter);
        when(userIntentionResultRepository.countUserTypeByLiveId(anyString())).thenReturn(userTypeCountList);
        // act
        RemoteResponse<PrivateLiveSourceBoardStatisticDTO> response = privateLiveUserIntentionService.queryUserInfoByLiveId(liveId);
        // assert
        assertEquals(4, (int) response.getData().getAllUserCnt());
        assertEquals(1, (int) response.getData().getSeedUserCnt());
    }

    @Test
    public void testModifyUserAndOrderIntention_ExpireTimePassed() {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setLiveId("liveId");
        request.setWxId("wxId");
        request.setOriginConsultantTaskId(1L);
        request.setNewConsultantTaskId(2L);
        request.setOperatorId(1L);
        PrivateLiveIntentionExpireTime expiredTime = new PrivateLiveIntentionExpireTime();
        expiredTime.setStatus(0);
        // 设置为过去的时间
        expiredTime.setExpireTime(new Date(System.currentTimeMillis() - 10000));
        when(intentionExpireTimeRepository.queryByLiveId(anyString())).thenReturn(expiredTime);
        // act
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.modifyUserAndOrderIntention(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("归因有效期已过期", response.getMsg());
    }

    @Test
    public void testModifyUserIntention_DecorateUserType_LINK() throws PrivateLiveIntentionException {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setWxId("");
        request.setMtUserId(1L);
        request.setLiveId("liveId");
        request.setOriginConsultantTaskId(1L);
        request.setNewConsultantTaskId(2L);
        request.setOperatorId(1L);
        PrivateLiveIntentionExpireTime expiredTime = new PrivateLiveIntentionExpireTime();
        expiredTime.setStatus(DistributionStatusEnum.VALID.getCode());
        expiredTime.setExpireTime(DateUtils.addDay(new Date(), 1));
        when(intentionExpireTimeRepository.queryByLiveId(anyString())).thenReturn(expiredTime);
        when(distributorCodeService.loadCodeByLiveId(anyString())).thenReturn(RemoteResponse.success("code"));
        PrivateLiveUserIntentionResult userIntentionResult = new PrivateLiveUserIntentionResult();
        userIntentionResult.setWxId("");
        when(userIntentionResultRepository.forceGetByUserIdAndLiveId(anyLong(), anyString())).thenReturn(userIntentionResult);
        doNothing().when(modifyDomainService).modifyIntention(any(), anyInt());
        when(privateLiveAclService.queryLiveTraceConfig(anyString())).thenReturn(TraceAbilityTypeEnum.GROUP);
        // act
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.modifyUserAndOrderIntention(request);
        // assert
        assertEquals(true, response.getData());
    }

    @Test
    public void testModifyUserIntention_DecorateUserType_SEED() throws PrivateLiveIntentionException {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setWxId("wxId");
        request.setLiveId("liveId");
        request.setOriginConsultantTaskId(1L);
        request.setNewConsultantTaskId(2L);
        request.setOperatorId(1L);
        PrivateLiveIntentionExpireTime expiredTime = new PrivateLiveIntentionExpireTime();
        expiredTime.setStatus(DistributionStatusEnum.VALID.getCode());
        expiredTime.setExpireTime(DateUtils.addDay(new Date(), 1));
        when(intentionExpireTimeRepository.queryByLiveId(anyString())).thenReturn(expiredTime);
        when(distributorCodeService.loadCodeByLiveId(anyString())).thenReturn(RemoteResponse.success("code"));
        MemberInviteWxUserInfo memberInviteWxUserInfo = new MemberInviteWxUserInfo();
        memberInviteWxUserInfo.setUnionId("unionId");
        memberInviteWxUserInfo.setDistance(1);
        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = new MemberInviteRelationInfoDTO();
        memberInviteRelationInfoDTO.setUpInviteUserInfoList(Lists.newArrayList(memberInviteWxUserInfo));
        when(privateSphereUserAclService.queryInviteRelation(anyString(), anyString(), anyInt())).thenReturn(memberInviteRelationInfoDTO);
        when(privateLiveConsultantVerifyService.checkByUnionIdAndTaskId(anyString(), anyLong())).thenReturn(RemoteResponse.success(true));
        doNothing().when(modifyDomainService).modifyIntention(any(), anyInt());
        when(privateLiveAclService.queryLiveTraceConfig(anyString())).thenReturn(TraceAbilityTypeEnum.GROUP);
        // act
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.modifyUserAndOrderIntention(request);
        // assert
        assertEquals(true, response.getData());
    }

    @Test
    public void testModifyUserIntention_DecorateUserType_FISSION() throws PrivateLiveIntentionException {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setWxId("wxId");
        request.setLiveId("liveId");
        request.setOriginConsultantTaskId(1L);
        request.setNewConsultantTaskId(2L);
        request.setOperatorId(1L);
        PrivateLiveIntentionExpireTime expiredTime = new PrivateLiveIntentionExpireTime();
        expiredTime.setStatus(DistributionStatusEnum.VALID.getCode());
        expiredTime.setExpireTime(DateUtils.addDay(new Date(), 1));
        when(intentionExpireTimeRepository.queryByLiveId(anyString())).thenReturn(expiredTime);
        when(distributorCodeService.loadCodeByLiveId(anyString())).thenReturn(RemoteResponse.success("code"));
        MemberInviteWxUserInfo memberInviteWxUserInfo = new MemberInviteWxUserInfo();
        memberInviteWxUserInfo.setUnionId("unionId");
        memberInviteWxUserInfo.setDistance(1);
        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = new MemberInviteRelationInfoDTO();
        memberInviteRelationInfoDTO.setUpInviteUserInfoList(Lists.newArrayList(memberInviteWxUserInfo));
        when(privateSphereUserAclService.queryInviteRelation(anyString(), anyString(), anyInt())).thenReturn(memberInviteRelationInfoDTO);
        when(privateLiveConsultantVerifyService.checkByUnionIdAndTaskId(anyString(), anyLong())).thenReturn(RemoteResponse.success(false));
        doNothing().when(modifyDomainService).modifyIntention(any(), anyInt());
        when(privateLiveAclService.queryLiveTraceConfig(anyString())).thenReturn(TraceAbilityTypeEnum.GROUP);
        // act
        RemoteResponse<Boolean> response = privateLiveUserIntentionService.modifyUserAndOrderIntention(request);
        // assert
        assertEquals(true, response.getData());
    }

    @Before
    public void setUp() {
        request = new QueryUserIntentionTypeByUserIdsRequest("liveId", Arrays.asList(1L, 2L));
    }

    /**
     * 测试用户意向查询，正常情况
     */
    @Test
    public void testQueryUserIntentionTypeByUserIdsSuccess() {
        when(userIntentionResultRepository.batchQueryByUserId(anyString(), any())).thenReturn(Collections.singletonList(new PrivateLiveUserIntentionResult()));
        when(wechatUserAclService.queryMtUnionIdByUserId(anyLong())).thenReturn("unionId");
        when(privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(anyString(), anyString())).thenReturn(RemoteResponse.success(1L));
        RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> response = service.queryUserIntentionTypeByUserIds(request);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertFalse(response.getData().isEmpty());
    }

    /**
     * 测试用户意向查询，用户ID列表为空
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryUserIntentionTypeByUserIdsWithEmptyUserIds() {
        request.setUserIds(Collections.emptyList());
        service.queryUserIntentionTypeByUserIds(request);
    }

    /**
     * 测试用户意向查询，liveId为空
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryUserIntentionTypeByUserIdsWithEmptyLiveId() {
        request.setLiveId("");
        service.queryUserIntentionTypeByUserIds(request);
    }


    /**
     * 测试用户意向查询，查询结果为空
     */
    @Test
    public void testQueryUserIntentionTypeByUserIdsWithNoResults() {
        when(userIntentionResultRepository.batchQueryByUserId(anyString(), any())).thenReturn(Collections.emptyList());
        RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> response = service.queryUserIntentionTypeByUserIds(request);
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isEmpty());
    }

    @Test
    public void testInitFromOrderOrFromIntentionCode_ValidInputs() {
        // arrange
        WechatUserInfoDTO wechatUserInfoDTO = new WechatUserInfoDTO();
        wechatUserInfoDTO.setNickName("nickname");
        when(wechatUserAclService.queryMtUnionIdByUserId(1L)).thenReturn("unionId");
        when(wechatUserAclService.queryWechatUserByUserId(1L)).thenReturn(wechatUserInfoDTO);

        // act
        service.initFromOrderOrFromIntentionCode(1L, "distributorCode", "liveId", 1, 1, "remark");

        // assert
        verify(wechatUserAclService, times(1)).queryMtUnionIdByUserId(1L);
        verify(wechatUserAclService, times(1)).queryWechatUserByUserId(1L);
        verify(userIntentionDomainService, times(1)).insertIntentionResultAndLog(any(PrivateLiveUserIntentionResult.class), eq("remark"));
    }

    @Test
    public void testInitFromOrderOrFromIntentionCode_NullWechatUserInfo() {

        PrivateLiveOrderUpdateInfo orderUpdateInfo = new PrivateLiveOrderUpdateInfo();
        // arrange
        when(wechatUserAclService.queryMtUnionIdByUserId(1L)).thenReturn("unionId");
        when(wechatUserAclService.queryWechatUserByUserId(1L)).thenReturn(null);

        // act
        service.initFromOrderOrFromIntentionCode(1L, "remark", "remark", 1L, 1, "remark");

        // assert
        verify(wechatUserAclService, times(1)).queryMtUnionIdByUserId(1L);
        verify(wechatUserAclService, times(1)).queryWechatUserByUserId(1L);
        verify(userIntentionDomainService, times(1)).insertIntentionResultAndLog(any(PrivateLiveUserIntentionResult.class), eq("remark"));
    }

    @Test
    public void testQueryUserBindTaskOld_Exception() {
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        request.setWxId("wxId");
        request.setLiveId("liveId");

        when(userIntentionResultRepository.forceQuery(any())).thenThrow(new RuntimeException("Database error"));

        RemoteResponse<Long> response = service.queryUserBindTaskOld(request);

        assertNotNull(response);
        assertTrue(response.getMsg().contains("Database error"));
    }

    @Test
    public void testQueryUserBindTaskNew_Success() throws Throwable {
        // arrange
        PrivateLiveUserBindTaskRequest request = new PrivateLiveUserBindTaskRequest();
        request.setWxId("wx123");
        request.setUnionId("union123");
        request.setMtUserId(123L);
        request.setLiveId("live123");
        RemoteResponse<Long> success = RemoteResponse.success(1L);
        when(dzPrivateLiveUserIntentionService.queryUserBindTask(any(PrivateLiveUserBindTaskRequest.class))).thenReturn(success);
        // act
        RemoteResponse<Long> actualResponse = service.queryUserBindTaskNew(request);
        // assert
        assertNotNull(actualResponse);
    }

    @Test
    public void testModifyUserIntentionOld_Success() {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setWxId("wx123");
        request.setNewConsultantTaskId(1L);
        request.setLiveId("live123");
        request.setOperatorId(1L);

        PrivateLiveIntentionExpireTime expireTime = new PrivateLiveIntentionExpireTime();
        expireTime.setExpireTime(new Date(System.currentTimeMillis() + 10000)); // 设置为未来的时间

        when(intentionExpireTimeRepository.queryByLiveId(anyString())).thenReturn(expireTime);
        when(distributorCodeService.loadCodeByLiveId(anyString())).thenReturn(RemoteResponse.success("code123"));
        when(privateSphereUserAclService.queryInviteRelation(anyString(), anyString(), any())).thenReturn(new MemberInviteRelationInfoDTO());
        when(privateLiveIntentionExpireService.init(anyString(), any())).thenReturn(RemoteResponse.success(true));

        // act
        RemoteResponse<Boolean> response = service.modifyUserIntentionOld(request);
        assertNotNull(response);
    }

    @Test
    public void testModifyUserIntentionNew_Success() throws Throwable {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveUserIntentionService.modifyUserIntention(any(PrivateLiveIntentionModifyRequest.class))).thenReturn(success);
        // act
        RemoteResponse<Boolean> actualResponse = service.modifyUserIntentionNew(request);
        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
    }

    @Test
    public void testModifyUserAndOrderIntentionOld_Success() throws Throwable {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        request.setWxId("wx123");
        request.setNewConsultantTaskId(1L);
        request.setLiveId("live123");
        request.setOperatorId(1L);

        PrivateLiveIntentionExpireTime expireTime = new PrivateLiveIntentionExpireTime();
        expireTime.setExpireTime(new Date(System.currentTimeMillis() + 10000)); // 设置为未来的时间

        when(intentionExpireTimeRepository.queryByLiveId(anyString())).thenReturn(expireTime);
        when(distributorCodeService.loadCodeByLiveId(anyString())).thenReturn(RemoteResponse.success("code123"));

        // act
        RemoteResponse<Boolean> result = service.modifyUserAndOrderIntentionOld(request);
        assertNotNull(result);

    }

    @Test
    public void testModifyUserAndOrderIntentionNew_Success() throws Throwable {
        // arrange
        PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveUserIntentionService.modifyUserAndOrderIntention(request)).thenReturn(success);
        // act
        RemoteResponse<Boolean> actualResponse = service.modifyUserAndOrderIntentionNew(request);
        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveUserIntentionService, times(1)).modifyUserAndOrderIntention(request);
    }

    @Test
    public void testQueryUserInfoByLiveIdOld_Normal() {
        // arrange
        String liveId = "testLiveId";
        PrivateLiveUserTypeBaseCount privateLiveUserTypeBaseCount = new PrivateLiveUserTypeBaseCount();
        privateLiveUserTypeBaseCount.setType(1);
        privateLiveUserTypeBaseCount.setCount(10);
        List<PrivateLiveUserTypeBaseCount> counts = Arrays.asList(
                privateLiveUserTypeBaseCount
        );
        when(userIntentionResultRepository.countUserTypeByLiveId(anyString())).thenReturn(counts);

        // act
        RemoteResponse<PrivateLiveSourceBoardStatisticDTO> response = service.queryUserInfoByLiveIdOld(liveId);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    @Test
    public void testQueryUserInfoByLiveIdNewSuccess() {
        // arrange
        PrivateLiveSourceBoardStatisticDTO statisticDTO = new PrivateLiveSourceBoardStatisticDTO();
        statisticDTO.setLiveId("liveId123");
        statisticDTO.setAllUserCnt(100);
        statisticDTO.setFissionUserCnt(50);
        statisticDTO.setSeedUserCnt(30);
        statisticDTO.setUnEnterGroupUserCnt(20);
        RemoteResponse<PrivateLiveSourceBoardStatisticDTO> expectedResponse = RemoteResponse.success(new PrivateLiveSourceBoardStatisticDTO());
        when(dzPrivateLiveUserIntentionService.queryUserInfoByLiveId(anyString())).thenReturn(expectedResponse);

        // act
        RemoteResponse<PrivateLiveSourceBoardStatisticDTO> actualResponse = service.queryUserInfoByLiveIdNew("liveId123");

        // assert
        assertNotNull(actualResponse);
    }

    @Test
    public void testPageQuerySourceBoardConsultListOld_Normal() throws Exception {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("liveId");
        request.setPageNo(1);
        request.setPageSize(10);

        when(privateLiveConsultantTaskRepository.countByCondition(anyString(), any(), any(), any())).thenReturn(1L);

        // act
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> response = service.pageQuerySourceBoardConsultListOld(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getList());
    }

    @Test
    public void testPageQuerySourceBoardConsultListNew_Normal() {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = mock(PrivateLiveSourceBoardConsultRequest.class);
        PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO> pageDataDTO = new PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>();
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> expectedResponse = RemoteResponse.success(pageDataDTO);
        when(dzPrivateLiveUserIntentionService.pageQuerySourceBoardConsultList(request)).thenReturn(expectedResponse);
        // act
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> actualResponse = service.pageQuerySourceBoardConsultListNew(request);
        // assert
        verify(dzPrivateLiveUserIntentionService, times(1)).pageQuerySourceBoardConsultList(request);
        assertSame(expectedResponse, actualResponse);
    }

    @Test
    public void testPageQuerySourceBoardConsultUserListOld_Success() throws TException {

        IntentionCalcParamParser intentionCalcParamParser = new IntentionCalcParamParser();
        IntentionCalcByGroupMsgParamParser intentionCalcByGroupMsgParamParser = new IntentionCalcByGroupMsgParamParser();
        CheckByUnionIdAndLiveIdParamParser checkByUnionIdAndLiveIdParamParser = new CheckByUnionIdAndLiveIdParamParser();
        CheckByUnionIdAndTaskIdParamParser checkByUnionIdAndTaskIdParamParser = new CheckByUnionIdAndTaskIdParamParser();
        CheckConsultantValidParamParser checkConsultantValidParamParser = new CheckConsultantValidParamParser();
        GetUnionIdByTaskIdParamParser getUnionIdByTaskIdParamParser = new GetUnionIdByTaskIdParamParser();
        GetUnionIdsByLiveIdParamParser getUnionIdsByLiveIdParamParser = new GetUnionIdsByLiveIdParamParser();
        InsertOrUpdateSummaryDataParamParser insertOrUpdateSummaryDataParamParser = new InsertOrUpdateSummaryDataParamParser();
        IntentionCalcByGroupMsgParamParser intentionCalcByGroupMsgParamParser1 = new IntentionCalcByGroupMsgParamParser();
        LoadByConsultantIdAndLiveIdParamParser loadByConsultantIdAndLiveIdParamParser = new LoadByConsultantIdAndLiveIdParamParser();
        LoadByConsultantIdParamParser loadByConsultantIdParamParser = new LoadByConsultantIdParamParser();
        LoadByOpenIdParamParser loadByOpenIdParamParser = new LoadByOpenIdParamParser();
        LoadByTaskIdParamParser loadByTaskIdParamParser = new LoadByTaskIdParamParser();
        LoadByTaskIdsParamParser loadByTaskIdsParamParser = new LoadByTaskIdsParamParser();
        LoadConsultantTasksByLiveIdParamParser loadConsultantTasksByLiveIdParamParser = new LoadConsultantTasksByLiveIdParamParser();
        LoadConsultantTasksPageParamParser loadConsultantTasksPageParamParser = new LoadConsultantTasksPageParamParser();
        ManualFixOrderParamParser manualFixOrderParamParser = new ManualFixOrderParamParser();
        ManualFixUserOrderSummaryParamParser manualFixUserOrderSummaryParamParser = new ManualFixUserOrderSummaryParamParser();
        ModifyUserAndOrderIntentionParamParser modifyUserAndOrderIntentionParamParser = new ModifyUserAndOrderIntentionParamParser();
        ModifyUserIntentionParamParser modifyUserIntentionParamParser = new ModifyUserIntentionParamParser();
        PageQuerySourceBoardConsultListParamParser pageQuerySourceBoardConsultListParamParser = new PageQuerySourceBoardConsultListParamParser();
        PageQuerySourceBoardConsultUserListParamParser pageQuerySourceBoardConsultUserListParamParser = new PageQuerySourceBoardConsultUserListParamParser();
        PageQuerySourceBoardCustomerListParamParser pageQuerySourceBoardCustomerListParamParser = new PageQuerySourceBoardCustomerListParamParser();
        PrivateLiveIntentionExpireInitParamParser privateLiveIntentionExpireInitParamParser = new PrivateLiveIntentionExpireInitParamParser();
        QueryBossDataByLiveIdParamParser queryBossDataByLiveIdParamParser = new QueryBossDataByLiveIdParamParser();
        QueryByLiveIdParamParser queryByLiveIdParamParser = new QueryByLiveIdParamParser();
        QueryByOrderIdParamParser queryByOrderIdParamParser = new QueryByOrderIdParamParser();
        QueryConsultantCustomerRankListParamParser queryConsultantCustomerRankListParamParser = new QueryConsultantCustomerRankListParamParser();
        QueryConsultantFollowRankListParamParser queryConsultantFollowRankListParamParser = new QueryConsultantFollowRankListParamParser();
        QueryCustomerTradeRankListParamParser queryCustomerTradeRankListParamParser = new QueryCustomerTradeRankListParamParser();
        QueryHotProductByLiveIdParamParser  queryHotProductByLiveIdParamParser = new QueryHotProductByLiveIdParamParser();
        QueryRankByConditionParamParser queryRankByConditionParamParser = new QueryRankByConditionParamParser();
        QueryRankListParamParser queryRankListParamParser = new QueryRankListParamParser();
        QuerySourceBoardConsultListParamParser querySourceBoardConsultListParamParser = new QuerySourceBoardConsultListParamParser();
        QuerySourceBoardConsultUserListParamParser querySourceBoardConsultUserListParamParser = new QuerySourceBoardConsultUserListParamParser();
        QuerySummaryByConsultantLiveIdParamParser querySummaryByConsultantLiveIdParamParser = new QuerySummaryByConsultantLiveIdParamParser();
        QuerySummaryByConsultantTaskIdAndUserIdParamParser  querySummaryByConsultantTaskIdAndUserIdParamParser = new QuerySummaryByConsultantTaskIdAndUserIdParamParser();
        QuerySummaryByConsultantTaskIdParamParser querySummaryByConsultantTaskIdParamParser = new QuerySummaryByConsultantTaskIdParamParser();
        QuerySummaryByConsultantTaskIdsParamParser querySummaryByConsultantTaskIdsParamParser = new QuerySummaryByConsultantTaskIdsParamParser();
        // 初始化请求对象
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("testLiveId");
        request.setPageNo(1);
        request.setPageSize(10);

        // Mock PrivateLiveConsultantTask
        PrivateLiveConsultantTask mockTask = Mockito.mock(PrivateLiveConsultantTask.class);
        when(mockTask.getId()).thenReturn(1L);
        when(mockTask.getConsultantId()).thenReturn(1L);

        // Mock repository 方法
        when(privateLiveConsultantTaskRepository.countByCondition(anyString(), anyString(), anyString(), anyList())).thenReturn(1L);
        when(privateLiveConsultantTaskRepository.pageLoadByCondition(anyString(), anyString(), anyString(), anyList(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList(mockTask));

        // 执行测试方法
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> response =
                service.pageQuerySourceBoardConsultUserListOld(request);

        // 断言
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    /**
     * 测试pageQuerySourceBoardConsultUserListNew方法，当请求参数正常时
     */
    @Test
    public void testPageQuerySourceBoardConsultUserListNew_RequestIsValid() {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = mock(PrivateLiveSourceBoardConsultRequest.class);
        PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO> pageDataDTO = new PageDataDTO<>();
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> success = RemoteResponse.success(pageDataDTO);
        when(dzPrivateLiveUserIntentionService.pageQuerySourceBoardConsultUserList(request)).thenReturn(success);
        // act
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> actualResponse = service.pageQuerySourceBoardConsultUserListNew(request);
        // assert
        verify(dzPrivateLiveUserIntentionService, times(1)).pageQuerySourceBoardConsultUserList(request);
        assertSame(success, actualResponse);
    }

    @Test
    public void testPageQuerySourceBoardCustomerListOld_TagIdsButNoData() {
        PrivateLiveSourceBoardCustomerRequest request = new PrivateLiveSourceBoardCustomerRequest();
        request.setLiveId("liveId");
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTagIds(Collections.singletonList(1));

        PageRemoteResponse<CommunityCustomerDTO> pageRemoteResponse = new PageRemoteResponse<>();
        pageRemoteResponse.setCode(200);
        pageRemoteResponse.setData(new ArrayList<>());
        when(privateSphereUserAclService.queryCustomerListByTag(any(CustomerListByTagRequest.class)))
                .thenReturn(pageRemoteResponse);

        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> response = service.pageQuerySourceBoardCustomerListOld(request);

        Assert.assertTrue(response.isSuccess());
        Assert.assertEquals(0, response.getData().getPageInfoDTO().getTotalCount().intValue());
    }

    @Test
    public void testPageQuerySourceBoardCustomerListNew_Success() {
        // arrange
        PrivateLiveSourceBoardCustomerRequest request = new PrivateLiveSourceBoardCustomerRequest();
        request.setLiveId("live123");
        request.setPageNo(1);
        request.setPageSize(10);

        PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO> expectedResponseData = new PageDataDTO<>();
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> expectedResponse = RemoteResponse.success(expectedResponseData);

        when(dzPrivateLiveUserIntentionService.pageQuerySourceBoardCustomerList(request)).thenReturn(expectedResponse);

        // act
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> actualResponse = service.pageQuerySourceBoardCustomerListNew(request);

        // assert
        verify(dzPrivateLiveUserIntentionService, times(1)).pageQuerySourceBoardCustomerList(request);
        assertSame(expectedResponse, actualResponse);
    }

    @Test
    public void testQuerySourceBoardConsultListOld_Normal() throws Exception {
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("live123");
        when(privateLiveConsultantTaskRepository.loadByCondition(anyString(), any(), any(), any()))
                .thenReturn(Collections.singletonList(new PrivateLiveConsultantTask()));
        RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> response = privateLiveUserIntentionService.querySourceBoardConsultListOld(request);
        assertNotNull(response);
    }

    @Test
    public void testQuerySourceBoardConsultListNew_Normal() {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("live123");
        List<PrivateLiveSourceBoardConsultStatisticsDTO> expectedResponseData = new ArrayList<>();
        RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> expectedResponse = RemoteResponse.success(expectedResponseData);
        when(dzPrivateLiveUserIntentionService.querySourceBoardConsultList(any(PrivateLiveSourceBoardConsultRequest.class))).thenReturn(expectedResponse);

        // act
        RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> actualResponse = service.querySourceBoardConsultListNew(request);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponseData, actualResponse.getData());
        verify(dzPrivateLiveUserIntentionService, times(1)).querySourceBoardConsultList(any(PrivateLiveSourceBoardConsultRequest.class));
    }

    @Test
    public void testQueryTradeInfoByLiveIdOld_Normal() throws Exception {
        // arrange
        String liveId = "testLiveId";
        List<ConsultantUserStatisticBO> userStatisticBOS = Arrays.asList(
                new ConsultantUserStatisticBO() {{
                    setMtUserId(1L);
                    setConsultantTaskId(1L);
                    setUserType(1);
                    setGmvAmt(100L);
                    setPayOrderCnt(1);
                }}
        );
        when(privateLiveConsultantSummaryRepository.queryLiveUserStatistic(anyString())).thenReturn(userStatisticBOS);
        when(userIntentionResultRepository.queryUserIntentionType(anyString(), any())).thenReturn(Collections.singletonMap(1L, 1));

        // act
        RemoteResponse<PrivateLiveSourceBoardTradeStatisticDTO> response = service.queryTradeInfoByLiveIdOld(liveId);

        // assert
        assertTrue("应该返回成功", response.isSuccess());
        assertNotNull("返回的数据不应为空", response.getData());
    }

    @Test
    public void testQueryTradeInfoByLiveIdNew_Success() {
        // arrange
        String liveId = "validLiveId";
        PrivateLiveSourceBoardTradeStatisticDTO expectedDto = new PrivateLiveSourceBoardTradeStatisticDTO();
        RemoteResponse<PrivateLiveSourceBoardTradeStatisticDTO> expectedResponse = RemoteResponse.success(expectedDto);
        when(dzPrivateLiveUserIntentionService.queryTradeInfoByLiveId(liveId)).thenReturn(expectedResponse);

        // act
        RemoteResponse<PrivateLiveSourceBoardTradeStatisticDTO> actualResponse = service.queryTradeInfoByLiveIdNew(liveId);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        verify(dzPrivateLiveUserIntentionService, times(1)).queryTradeInfoByLiveId(liveId);
    }

    @Test
    public void testQueryTagUserDetailByTaskIdOld_Success() throws Exception {
        // arrange
        PrivateLiveSourceBoardTagUserRequest request = new PrivateLiveSourceBoardTagUserRequest();
        request.setLiveId("liveId");
        request.setConsultantTaskId(1L);
        request.setTagId(1);

        List<PrivateLiveUserIntentionResult> intentionResults = new ArrayList<>();
        intentionResults.add(new PrivateLiveUserIntentionResult());

        List<PrivateLiveConsultantUserStatistics> userStatistics = new ArrayList<>();
        userStatistics.add(new PrivateLiveConsultantUserStatistics());

        List<CommunityLiveUserTagDetailDTO> tagDetails = new ArrayList<>();
        tagDetails.add(new CommunityLiveUserTagDetailDTO());

        when(privateLiveConsultantSummaryRepository.queryLoadByConsultantAndMtUserIds(any(), any(), any())).thenReturn(userStatistics);
        when(userIntentionResultRepository.queryByWxIdOrUsers(any(), any(), any(), any())).thenReturn(intentionResults);

        // act
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardTagUserDTO>> response = service.queryTagUserDetailByTaskIdOld(request);

        // assert
        assertNotNull(response);
    }

    @Test
    public void testQueryTagUserDetailByTaskIdNew_Normal() {
        // arrange
        PrivateLiveSourceBoardTagUserRequest request = new PrivateLiveSourceBoardTagUserRequest();
        request.setLiveId("liveId");
        request.setConsultantTaskId(1L);
        request.setTagId(1);

        PageDataDTO<PrivateLiveSourceBoardTagUserDTO> expectedResponseData = new PageDataDTO<>();
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardTagUserDTO>> expectedResponse = RemoteResponse.success(expectedResponseData);

        when(dzPrivateLiveUserIntentionService.queryTagUserDetailByTaskId(request)).thenReturn(expectedResponse);

        // act
        RemoteResponse<PageDataDTO<PrivateLiveSourceBoardTagUserDTO>> actualResponse = service.queryTagUserDetailByTaskIdNew(request);

        // assert
        verify(dzPrivateLiveUserIntentionService, times(1)).queryTagUserDetailByTaskId(request);
        assert expectedResponse.equals(actualResponse);
    }

    @Test
    public void testQuerySourceBoardConsultUserListOld_WithValidRequest() throws TException {
        PrivateLiveSourceBoardConsultRequest request = new PrivateLiveSourceBoardConsultRequest();
        request.setLiveId("live123");
        List<PrivateLiveConsultantTask> tasks = new ArrayList<>();
        tasks.add(new PrivateLiveConsultantTask());
        when(privateLiveConsultantTaskRepository.loadByCondition(anyString(), any(), any(), any())).thenReturn(tasks);

        // Mock其他依赖方法的返回值
        // 由于方法内部调用了多个异步方法和其他服务，需要根据实际情况进行Mock

        RemoteResponse<List<PrivateLiveSourceBoardConsultUserStatisticsDTO>> response = privateLiveUserIntentionService.querySourceBoardConsultUserListOld(request);

        assertNotNull(response);
    }

    @Test
    public void testQuerySourceBoardConsultUserListNew_Success() {
        // arrange
        PrivateLiveSourceBoardConsultRequest request = mock(PrivateLiveSourceBoardConsultRequest.class);
        List<PrivateLiveSourceBoardConsultUserStatisticsDTO> expectedResponse = new ArrayList<>();
        when(dzPrivateLiveUserIntentionService.querySourceBoardConsultUserList(request)).thenReturn(RemoteResponse.success(expectedResponse));

        // act
        RemoteResponse<List<PrivateLiveSourceBoardConsultUserStatisticsDTO>> actualResponse = privateLiveUserIntentionService.querySourceBoardConsultUserListNew(request);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.isSuccess());
        assertEquals(expectedResponse, actualResponse.getData());
    }

    @Test
    public void testQueryUserIntentionTypeByUserIdsOld_WhenUserIntentionResultsAreNotEmpty() {
        List<PrivateLiveUserIntentionResult> results = Arrays.asList(
                new PrivateLiveUserIntentionResult(1L, "wxId1", "unionId1", 1L, "wxNickName1", "distributorCode1", "liveId", 1L, 1, 1, null, null, 1),
                new PrivateLiveUserIntentionResult(2L, "wxId2", "unionId2", 2L, "wxNickName2", "distributorCode2", "liveId", 2L, 1, 1, null, null, 1)
        );
        when(userIntentionResultRepository.batchQueryByUserId(anyString(), any())).thenReturn(results);
        when(privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(anyString(), anyString())).thenReturn(RemoteResponse.success(1L));

        RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> response = service.queryUserIntentionTypeByUserIdsOld(request);

        assertTrue(response.isSuccess());
        assertEquals(2, response.getData().size());
    }

    @Test
    public void testQueryUserIntentionTypeByUserIdsNew_ReturnsExpectedResult() {
        // arrange
        QueryUserIntentionTypeByUserIdsRequest request = new QueryUserIntentionTypeByUserIdsRequest("liveId", Arrays.asList(1L, 2L));
        List<QueryUserIntentionTypeByUserIdsResponse> expectedResponseList = Arrays.asList(
                new QueryUserIntentionTypeByUserIdsResponse("liveId", 1L, 1),
                new QueryUserIntentionTypeByUserIdsResponse("liveId", 2L, 2)
        );
        RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> expectedResponse = RemoteResponse.success(expectedResponseList);
        when(dzPrivateLiveUserIntentionService.queryUserIntentionTypeByUserIds(request)).thenReturn(expectedResponse);

        // act
        RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> actualResponse = service.queryUserIntentionTypeByUserIdsNew(request);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse.getData().size(), actualResponse.getData().size());
        assertEquals(expectedResponse.getData().get(0).getUserId(), actualResponse.getData().get(0).getUserId());
        verify(dzPrivateLiveUserIntentionService, times(1)).queryUserIntentionTypeByUserIds(request);
    }
}
