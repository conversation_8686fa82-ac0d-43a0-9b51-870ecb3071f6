package com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.impl;

import com.github.pagehelper.Page;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.converter.PrivateLivePlanDistributorGroupConverter;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLivePlanDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PageQueryForDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.example.PrivateLivePlanDistributorGroupExample;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.mapper.PrivateLivePlanDistributorGroupMapper;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.po.PrivateLivePlanDistributorGroup;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class PrivateLivePlanDistributorGroupRepositoryImplTest {
    @InjectMocks
    private PrivateLivePlanDistributorGroupRepositoryImpl repositoryImpl;

    @InjectMocks
    private PrivateLivePlanDistributorGroupRepositoryImpl repository;

    @Mock
    private PrivateLivePlanDistributorGroupMapper privateLivePlanDistributorGroupMapper;
    @Mock
    private PrivateLivePlanDistributorGroupBO privateLivePlanDistributorGroupBO;

    @Mock
    private PrivateLivePlanDistributorGroupConverter privateLivePlanDistributorGroupConverter;

    private final String testLiveId = "testLiveId";

    private final Long distributorGroupId = 1L;

    String liveId = "testLiveId";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @InjectMocks
    private PrivateLivePlanDistributorGroupRepositoryImpl privateLivePlanDistributorGroupRepositoryImpl;



    /**
     * 测试场景：当数据库中没有数据时的处理
     */
    @Test
    public void testPageQueryPrivateLiveDistributorGroupEmpty() {
        PageQueryForDistributorGroupRequest request = new PageQueryForDistributorGroupRequest("liveId", 1, 1, 10);
        Page<PrivateLivePlanDistributorGroup> page = new Page<>();
        page.setTotal(0);
        when(privateLivePlanDistributorGroupMapper.selectByExample(any())).thenReturn(page);
        PageDataDTO<PrivateLivePlanDistributorGroupBO> result = repository.pageQueryPrivateLiveDistributorGroup(request);
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        //        assertEquals(0, result.getPageInfoDTO().getTotalCount());
        verify(privateLivePlanDistributorGroupMapper, times(1)).selectByExample(any());
        verify(privateLivePlanDistributorGroupConverter, never()).toEntity(any());
    }

    /**
     * 测试场景：当请求参数为null时的处理
     */
    @Test(expected = NullPointerException.class)
    public void testPageQueryPrivateLiveDistributorGroupNullRequest() {
        repository.pageQueryPrivateLiveDistributorGroup(null);
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryByLiveIdAndDistributorGroupId_ResultEmpty() {
        when(privateLivePlanDistributorGroupMapper.selectByExample(any(PrivateLivePlanDistributorGroupExample.class))).thenReturn(Collections.emptyList());
        PrivateLivePlanDistributorGroupBO result = privateLivePlanDistributorGroupRepositoryImpl.queryByLiveIdAndDistributorGroupId(liveId, distributorGroupId);
        assertNull(result);
    }




    /**
     * 测试 countDistributorGroupByPlanId 方法，当 planId 有效时返回正确的计数
     */
    @Test
    public void testCountDistributorGroupByPlanId_ValidPlanId() {
        // arrange
        String  planId = "1";
        long expectedCount = 5L;
        PrivateLivePlanDistributorGroupExample example = new PrivateLivePlanDistributorGroupExample();
        example.createCriteria().andLiveIdEqualTo(String.valueOf(planId));
        when(privateLivePlanDistributorGroupMapper.countByExample(any(PrivateLivePlanDistributorGroupExample.class))).thenReturn(expectedCount);
        // act
        long actualCount = repository.countValidDistributorGroup(planId);
        // assert
        assertEquals(expectedCount, actualCount);
        verify(privateLivePlanDistributorGroupMapper, times(1)).countByExample(any(PrivateLivePlanDistributorGroupExample.class));
    }


    /**
     * 测试 countDistributorGroupByPlanId 方法，当数据库中没有对应 planId 的数据时返回 0
     */
    @Test
    public void testCountDistributorGroupByPlanId_NoDataFound() {
        // arrange
        String planId = "2";
        long expectedCount = 0L;
        when(privateLivePlanDistributorGroupMapper.countByExample(any(PrivateLivePlanDistributorGroupExample.class))).thenReturn(expectedCount);
        // act
        long actualCount = repository.countValidDistributorGroup(planId);
        // assert
        assertEquals(expectedCount, actualCount);
        verify(privateLivePlanDistributorGroupMapper, times(1)).countByExample(any(PrivateLivePlanDistributorGroupExample.class));
    }


    /**
     * 测试 countDistributorGroupByPlanId 方法，当数据库操作抛出异常时的处理逻辑
     */
    @Test(expected = RuntimeException.class)
    public void testCountDistributorGroupByPlanId_DatabaseException() {
        // arrange
        String planId = "3";
        when(privateLivePlanDistributorGroupMapper.countByExample(any(PrivateLivePlanDistributorGroupExample.class))).thenThrow(new RuntimeException("Database error"));
        // act
        repository.countValidDistributorGroup(planId);
        // assert 期望方法抛出 RuntimeException
    }

    /**
     * 测试 liveId 为空时的场景
     */
    @Test
    public void testCountDistributorGroupByLiveIdAndDistributorGroupIdLiveIdIsNull() {
        long result = repository.countDistributorGroupByLiveIdAndDistributorGroupId(null, 1L);
        assertEquals(0, result);
    }

    /**
     * 测试 distributorGroupId 为 null 时的场景
     */
    @Test
    public void testCountDistributorGroupByLiveIdAndDistributorGroupIdDistributorGroupIdIsNull() {
        long result = repository.countDistributorGroupByLiveIdAndDistributorGroupId("liveId", null);
        assertEquals(0, result);
    }

    /**
     * 测试 liveId 和 distributorGroupId 都有效时的场景
     */
    @Test
    public void testCountDistributorGroupByLiveIdAndDistributorGroupIdValidParams() {
        when(privateLivePlanDistributorGroupMapper.countByExample(any())).thenReturn(5L);
        long result = repository.countDistributorGroupByLiveIdAndDistributorGroupId("liveId", 1L);
        assertEquals(5, result);
        verify(privateLivePlanDistributorGroupMapper, times(1)).countByExample(any());
    }

    /**
     * 测试createPoExample方法，当BO中的version和id都不为null时
     */
    @Test
    public void testCreatePoExampleWithNonNullVersionAndId() {
        // arrange
        when(privateLivePlanDistributorGroupBO.getVersion()).thenReturn(1);
        when(privateLivePlanDistributorGroupBO.getId()).thenReturn(100L);

        // act
        PrivateLivePlanDistributorGroupExample result = repositoryImpl.createPoExample(privateLivePlanDistributorGroupBO);

        // assert
        assertEquals(1, result.getOredCriteria().size());
        assertEquals(1, (int) result.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
        assertEquals(100L, result.getOredCriteria().get(0).getAllCriteria().get(1).getValue());
    }



}
