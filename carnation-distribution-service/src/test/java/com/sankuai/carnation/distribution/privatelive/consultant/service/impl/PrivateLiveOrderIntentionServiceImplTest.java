package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.dianping.dztrade.enums.RelatedOrderTypeEnum;
import com.dianping.pay.order.common.enums.AmountType;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderPaymentDetailDTO;
import com.dianping.receipt.query.dto.ReceiptDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.carnation.distribution.common.acl.UnifiedOrderAclService;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributor.dto.request.PrivateLiveConsultantRequest;
import com.sankuai.carnation.distribution.distributor.dto.response.ConsultantDistributorResponse;
import com.sankuai.carnation.distribution.distributor.dto.response.DistributorGroupResponse;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.common.RelateOrderBO;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.group.GroupUserInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderProducer;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderOperateEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.migrate.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.*;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveOrderIntentionService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveOrderIntentionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveOrderIntentionServiceImplTest {

    @InjectMocks
    private PrivateLiveOrderIntentionServiceImpl service;

    @Mock
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    @Mock
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock
    private OrderReceiptAcl orderReceiptAcl;

    @Mock
    private PrivateLiveOrderProducer privateLiveOrderProducer;

    @Mock
    private IProducerProcessor producer;

    @Mock
    private PrivateLiveConsultantTaskRepository consultantTaskRepository;

    @Mock
    private PrivateLiveConsultantSummaryService consultantSummaryService;

    @Mock
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;
    @Mock
    private PrivateLiveIntentionExpireTimeRepository intentionExpireTimeRepository;

    @Mock
    private PrivateLiveConsultantSummaryRepository privateLiveConsultantSummaryRepository;

    @Mock
    private RemoteResponse<Boolean> mockRemoteResponse;
    @Mock
    private DzPrivateLiveOrderIntentionService dzPrivateLiveOrderIntentionService;
    @Mock
    private RedisStoreClient redisStoreClient;
    @Mock
    private PrivateLiveDistributorCodeService privateLiveDistributorCodeService;
    @Mock
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testUpdateIntentionResult_PaySuccess() {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("123").operateType(OrderOperateEnum.PAY_SUCCESS.getCode()).build();
        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildUnifiedOrderWithId();

        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("支付成功应返回成功", response.getData());
    }

    @Test
    public void testUpdateIntentionResult_SubOrderPaySuccess() {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("124").operateType(OrderOperateEnum.PAY_SUCCESS.getCode()).build();
        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildSubOrderUnifiedOrderWithId();

        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("支付成功应返回成功", response.getData());
    }

    /**
     * 测试核销更新订单信息
     */
    @Test
    public void testUpdateIntentionResult_Verify() throws Exception {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("123").operateType(OrderOperateEnum.VERIFY.getCode()).build();

        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildUnifiedOrderWithId();

        PrivateLiveConsultantTask task = PrivateLiveConsultantTask.builder().avatarUrl("1234").shareName("zzz").build();
        when(consultantTaskRepository.loadById(anyLong())).thenReturn(task);

        when(producer.sendMessage(anyString(), anyString())).thenReturn(new ProducerResult(ProducerStatus.SEND_OK));
        when(consultantSummaryService.insertOrUpdateSummaryData(anyLong(), anyLong(), anyString())).thenReturn(RemoteResponse.success(1));
        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderReceiptAcl.getTotalUsedReceiptMoney(anyString())).thenReturn(new BigDecimal(1000));
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("核销应返回成功", response.getData());
    }

    @Test
    public void testUpdateIntentionResult_SubVrify() throws Exception {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("123").operateType(OrderOperateEnum.VERIFY.getCode()).build();

        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildSubOrderUnifiedOrderWithId();

        PrivateLiveConsultantTask task = PrivateLiveConsultantTask.builder().avatarUrl("1234").shareName("zzz").build();
        when(consultantTaskRepository.loadById(anyLong())).thenReturn(task);

        when(producer.sendMessage(anyString(), anyString())).thenReturn(new ProducerResult(ProducerStatus.SEND_OK));
        when(consultantSummaryService.insertOrUpdateSummaryData(anyLong(), anyLong(), anyString())).thenReturn(RemoteResponse.success(1));
        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderReceiptAcl.getTotalUsedReceiptMoney(anyString())).thenReturn(new BigDecimal(1000));
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("核销应返回成功", response.getData());
    }

    /**
     * 测试退款成功更新订单信息
     */
    @Test
    public void testUpdateIntentionResult_RefundSuccess() throws Exception {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("123").operateType(OrderOperateEnum.REFUND_SUCCESS.getCode()).build();

        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildUnifiedOrderWithId();

        when(producer.sendMessage(anyString(), anyString())).thenReturn(new ProducerResult(ProducerStatus.SEND_OK));
        when(consultantSummaryService.insertOrUpdateSummaryData(anyLong(), anyLong(), anyString())).thenReturn(RemoteResponse.success(1));
        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderReceiptAcl.getTotalUsedReceiptMoney(anyString())).thenReturn(new BigDecimal(1000));
        when(orderReceiptAcl.getTotalRefundReceiptMoney(anyString())).thenReturn(new BigDecimal(1000));
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("退款成功应返回成功", response.getData());
    }

    @Test
    public void testUpdateIntentionResult_SubRefundSuccess() throws Exception {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("124").operateType(OrderOperateEnum.REFUND_SUCCESS.getCode()).build();

        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildSubOrderUnifiedOrderWithId();

        when(producer.sendMessage(anyString(), anyString())).thenReturn(new ProducerResult(ProducerStatus.SEND_OK));
        when(consultantSummaryService.insertOrUpdateSummaryData(anyLong(), anyLong(), anyString())).thenReturn(RemoteResponse.success(1));
        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderReceiptAcl.getTotalUsedReceiptMoney(anyString())).thenReturn(new BigDecimal(1000));
        when(orderReceiptAcl.getTotalRefundReceiptMoney(anyString())).thenReturn(new BigDecimal(1000));
        ReceiptDTO receiptDTO = new ReceiptDTO();
        receiptDTO.setReceiptID(333333333L);
        when(orderReceiptAcl.getRefundReceipt(anyString())).thenReturn(Lists.newArrayList(receiptDTO));
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("退款成功应返回成功", response.getData());
    }

    @Test
    public void testUpdateIntentionResult_SubPay() {
        // arrange
        PrivateLiveOrderUpdateInfo updateInfo = PrivateLiveOrderUpdateInfo.builder().orderId("123").subOrderPayFlag(true)
                .operateType(OrderOperateEnum.PAY_SUCCESS.getCode()).build();
        PrivateLiveOrderIntentionResult orderIntentionResult = buildOrderIntentionResult();

        UnifiedOrderWithId unifiedOrderWithId = buildUnifiedOrderWithId();

        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(unifiedOrderWithId);
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(updateInfo);

        // assert
        assertTrue("支付成功应返回成功", response.getData());
    }

    private PrivateLiveOrderIntentionResult buildOrderIntentionResult() {
        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");
        orderIntentionResult.setPayMoney(100L);
        orderIntentionResult.setTotalMoney(500L);
        orderIntentionResult.setVerifyMoney(100L);
        orderIntentionResult.setRefundMoney(100L);
        orderIntentionResult.setInRefundMoney(0L);
        orderIntentionResult.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        orderIntentionResult.setStatus(2);
        return orderIntentionResult;
    }

    private UnifiedOrderWithId buildUnifiedOrderWithId() {
        UnifiedOrderWithId unifiedOrderWithId = new UnifiedOrderWithId();

        UnifiedOrderPaymentDetailDTO unifiedOrderPaymentDetailDTO = new UnifiedOrderPaymentDetailDTO();
        unifiedOrderPaymentDetailDTO.setAmount(new BigDecimal(1000));
        unifiedOrderPaymentDetailDTO.setAmountType(AmountType.MEITUANPAY.value);
        unifiedOrderWithId.setPaymentDetails(Lists.newArrayList(unifiedOrderPaymentDetailDTO));
        unifiedOrderWithId.setPaySuccessTime(new Date());

        Map<String, String> map = Maps.newHashMap();
        RelateOrderBO relateOrderBO = new RelateOrderBO();
        relateOrderBO.setOrderId("123");
        relateOrderBO.setOrderType(String.valueOf(RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId()));
        map.put("general.ext.relatedOrderList", JsonUtil.toJson(Lists.newArrayList(relateOrderBO)));
        map.put("general.ext.extraOrderType", "14");
        unifiedOrderWithId.setNibExtraFields(map);

        return unifiedOrderWithId;
    }

    private UnifiedOrderWithId buildSubOrderUnifiedOrderWithId() {
        UnifiedOrderWithId unifiedOrderWithId = new UnifiedOrderWithId();

        UnifiedOrderPaymentDetailDTO unifiedOrderPaymentDetailDTO = new UnifiedOrderPaymentDetailDTO();
        unifiedOrderPaymentDetailDTO.setAmount(new BigDecimal(1000));
        unifiedOrderPaymentDetailDTO.setAmountType(AmountType.MEITUANPAY.value);
        unifiedOrderWithId.setPaymentDetails(Lists.newArrayList(unifiedOrderPaymentDetailDTO));
        unifiedOrderWithId.setPaySuccessTime(new Date());

        Map<String, String> map = Maps.newHashMap();
        RelateOrderBO relateOrderBO = new RelateOrderBO();
        relateOrderBO.setOrderId("123");
        relateOrderBO.setOrderType(String.valueOf(RelatedOrderTypeEnum.DEAL_GROUP_BALANCE_PAYMENT_ORDER.getId()));
        map.put("general.ext.relatedOrderList", JsonUtil.toJson(Lists.newArrayList(relateOrderBO)));
        unifiedOrderWithId.setNibExtraFields(map);

        return unifiedOrderWithId;
    }

    /**
     * 测试手动修复用户订单汇总，操作类型为1，正常情况
     */
    @Test
    public void testManualFixUserOrderSummary_OperationType1_Success() throws Throwable {
        // arrange
        UserOrderSummaryFixRequest request = new UserOrderSummaryFixRequest();
        UserOrderSummaryFixDTO dto = new UserOrderSummaryFixDTO(1L, "liveId", 1L, 1);
        request.setUserOrderSummaryFixDTOList(Collections.singletonList(dto));
        // act
        RemoteResponse<Boolean> response = service.manualFixUserOrderSummary(request);
        // assert
        verify(consultantSummaryService, times(1)).insertOrUpdateSummaryData(anyLong(), anyLong(), anyString());
        assert response.getData();
    }

    /**
     * 测试手动修复用户订单汇总，操作类型为3，归因明细缺失情况
     */
    @Test
    public void testManualFixUserOrderSummary_OperationType3_MissingDetails() throws Throwable {
        // arrange
        UserOrderSummaryFixRequest request = new UserOrderSummaryFixRequest();
        UserOrderSummaryFixDTO dto = new UserOrderSummaryFixDTO(1L, "liveId", 1L, 3);
        request.setUserOrderSummaryFixDTOList(Collections.singletonList(dto));
        List<PrivateLiveConsultantUserStatistics> emptyList = Collections.emptyList();
        when(orderIntentionResultRepository.queryConsultantUserStatistics(anyLong(), anyLong(), anyString())).thenReturn(emptyList);
        PrivateLiveConsultantUserStatistics statistics = new PrivateLiveConsultantUserStatistics();
        statistics.setAddTime(new Date());
        when(privateLiveConsultantSummaryRepository.queryLoadByConsultantAndMtUserIds(anyString(), anyLong(), anyList())).thenReturn(Collections.singletonList(statistics));
        // act
        RemoteResponse<Boolean> response = service.manualFixUserOrderSummary(request);
        assert response.getData();
    }

    @Test
    public void testIntentionCalc_ExistingOrderIntentionResult() throws Throwable {
        PrivateLiveConsultantRequest request1 = new PrivateLiveConsultantRequest();
        PrivateLiveOrderUpdateInfo updateInfo = new PrivateLiveOrderUpdateInfo();
        DistributorGroupResponse response1 = new DistributorGroupResponse();
        ConsultantDistributorResponse consultantDistributorResponse = new ConsultantDistributorResponse();
        // arrange
        PrivateLiveOrderInfoRequest request = new PrivateLiveOrderInfoRequest();
        request.setOrderId("123");
        PrivateLiveOrderIntentionResult existingResult = new PrivateLiveOrderIntentionResult();
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(existingResult);

        // act
        RemoteResponse<Boolean> response = service.intentionCalc(request);

        // assert
        assertTrue(response.getData());
        verify(orderIntentionResultRepository, times(1)).forceGetByOrderId(anyString());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUpdateIntentionResultRequestIsNull() throws Throwable {
        PrivateLiveConsultantRequest request1 = new PrivateLiveConsultantRequest();
        PrivateLiveOrderUpdateInfo updateInfo = new PrivateLiveOrderUpdateInfo();
        DistributorGroupResponse response = new DistributorGroupResponse();
        ConsultantDistributorResponse consultantDistributorResponse = new ConsultantDistributorResponse();
        // arrange
        PrivateLiveOrderUpdateInfoRequest request = null;
        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(new UnifiedOrderWithId());
        // act
        service.updateIntentionResult(request);

        // assert
        // IllegalArgumentException is expected
    }

    /**
     * 测试updateIntentionResult方法，当请求参数有效且更新成功时的场景
     */
    @Test
    public void testUpdateIntentionResultSuccess() throws Throwable {
        PrivateLiveConsultantRequest request1 = new PrivateLiveConsultantRequest();
        PrivateLiveOrderUpdateInfo updateInfo = new PrivateLiveOrderUpdateInfo();
        DistributorGroupResponse response1 = new DistributorGroupResponse();
        ConsultantDistributorResponse consultantDistributorResponse = new ConsultantDistributorResponse();
        // arrange
        PrivateLiveOrderUpdateInfoRequest request = new PrivateLiveOrderUpdateInfoRequest();
        request.setOrderId("123");
        request.setOperateType(1);
        when(mockRemoteResponse.getData()).thenReturn(true);
        when(unifiedOrderAclService.forceGetOrder(anyString())).thenReturn(new UnifiedOrderWithId());
        // act
        RemoteResponse<Boolean> response = service.updateIntentionResult(request);

        // assert
        assertNotNull(response);
    }

    @Test
    public void testIntentionCalc_OrderAlreadyAttributed() {
        // arrange
        PrivateLiveOrderInfo orderInfo = new PrivateLiveOrderInfo();
        orderInfo.setOrderId("testOrderId");
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(new PrivateLiveOrderIntentionResult());

        // act
        RemoteResponse<Boolean> result = service.intentionCalc(orderInfo);

        // assert
        assertTrue(result.getData());
        verify(orderIntentionResultRepository, times(1)).forceGetByOrderId(anyString());
    }


    @Test
    public void testQueryByOrderIdOld_WithValidOrderId() {
        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("testOrderId");
        orderIntentionResult.setPayMoney(1000L);
        // arrange
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        RemoteResponse<PrivateLiveOrderIntentionResultDTO> response = service.queryByOrderIdOld("testOrderId");

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("testOrderId", response.getData().getOrderId());
        assertEquals(Long.valueOf(1000), response.getData().getPayMoney());
    }

    @Test
    public void testQueryByOrderIdNew_ReturnsSuccess() {
        // arrange
        String orderId = "validOrderId";
        PrivateLiveOrderIntentionResultDTO expectedResultDTO = new PrivateLiveOrderIntentionResultDTO();
        expectedResultDTO.setOrderId(orderId);
        RemoteResponse<PrivateLiveOrderIntentionResultDTO> expectedResponse = RemoteResponse.success(expectedResultDTO);
        when(dzPrivateLiveOrderIntentionService.queryByOrderId(orderId)).thenReturn(expectedResponse);

        // act
        RemoteResponse<PrivateLiveOrderIntentionResultDTO> actualResponse = service.queryByOrderIdNew(orderId);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.isSuccess());
        assertEquals(orderId, actualResponse.getData().getOrderId());
        verify(dzPrivateLiveOrderIntentionService, times(1)).queryByOrderId(orderId);
    }

    @Test
    public void testIntentionCalcByGroupMsgOld_Success() {
        // arrange
        String msg = "{\"eventCode\":\"order_create\", \"bizCode\":\"groupon\", \"orderBaseInfo\":{\"extraMap\":{\"distributionParam\":\"syzb\"}}, \"unifiedOrderId\":\"123\"}";
        when(redisStoreClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(true);

        // act
        RemoteResponse<Boolean> response = service.intentionCalcByGroupMsgOld(msg);

        // assert
        assertNotNull(response);
    }

    @Test
    public void testIntentionCalcByGroupMsgNew_ReturnsSuccess() {
        // arrange
        String msg = "test message";
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveOrderIntentionService.intentionCalcByGroupMsg(msg)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = service.intentionCalcByGroupMsgNew(msg);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveOrderIntentionService, times(1)).intentionCalcByGroupMsg(msg);
    }

    @Test
    public void testManualFixOrderOld_OrderNotAttributed_LiveIdValid_IntentionCalcSuccess() throws Exception {
        String orderId = "testOrderId";
        String liveId = "testLiveId";
        when(orderIntentionResultRepository.forceGetByOrderId(orderId)).thenReturn(null);
        when(privateLiveDistributorCodeService.loadCodeByLiveId(liveId)).thenReturn(RemoteResponse.success("distributorCode"));
        when(privateLiveOrderIntentionService.intentionCalc(new PrivateLiveOrderInfo())).thenReturn(RemoteResponse.success(true));
        RemoteResponse<Boolean> result = privateLiveOrderIntentionService.manualFixOrderOld(orderId, liveId);
        assertNull(result);
    }

    @Test
    public void testManualFixOrderNewSuccess() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        String liveId = "testLiveId";
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveOrderIntentionService.manualFixOrder(orderId, liveId)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = service.manualFixOrderNew(orderId, liveId);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveOrderIntentionService, times(1)).manualFixOrder(orderId, liveId);
    }


    @Test
    public void testIntentionCalcNew_Success() throws Throwable {
        // arrange
        PrivateLiveOrderInfoRequest request = new PrivateLiveOrderInfoRequest();
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveOrderIntentionService.intentionCalc(request)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = service.intentionCalcNew(request);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveOrderIntentionService, times(1)).intentionCalc(request);
    }

    @Test
    public void testUpdateIntentionResultOld_Success() {
        PrivateLiveOrderUpdateInfoRequest request = new PrivateLiveOrderUpdateInfoRequest();
        request.setOrderId("validOrderId");
        request.setOperateType(OrderOperateEnum.PAY_SUCCESS.getCode());
        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        RemoteResponse<Boolean> response = service.updateIntentionResultOld(request);
        assertNotNull(response);
    }

    @Test
    public void testUpdateIntentionResultNewSuccess() {
        // arrange
        PrivateLiveOrderUpdateInfoRequest request = new PrivateLiveOrderUpdateInfoRequest();
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveOrderIntentionService.updateIntentionResult(request)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = service.updateIntentionResultNew(request);

        // assert
        verify(dzPrivateLiveOrderIntentionService).updateIntentionResult(request);
        assert actualResponse.getData().equals(success.getData());
    }

}
