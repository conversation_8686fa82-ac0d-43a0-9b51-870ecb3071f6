package com.sankuai.carnation.distribution.distributor.domain;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.enums.DistributorBindUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupBindMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupChannelMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupMapper;
import com.sankuai.carnation.distribution.distributor.repository.db.*;
import com.sankuai.carnation.distribution.empty.code.bind.repository.dao.EmptyCodeBindInfoMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DistributorGroupRootServiceTest {

    @Mock
    private DistributorGroupMapper groupMapper;

    @InjectMocks
    private DistributorGroupRootService distributorGroupRootService;

    private DistributorGroup distributorGroup;
    @Mock
    private
    DistributorGroupChannelMapper groupChannelMapper;
    @Mock
    private DistributorChannelRootService channelRootService
            ;

    @Before
    public void setUp() {
        distributorGroup = new DistributorGroup();
        distributorGroup.setId(1);
        distributorGroup.setName("Test Group");
    }

    /**
     * 测试用户类型未知且用户ID无效时返回null
     */
    @Test
    public void testGetValidDistributorGroup_UnknownUserTypeAndInvalidUserId() {
        DistributorGroupBO result = distributorGroupRootService.getValidDistributorGroup(DistributorBindUserTypeEnum.UNKNOWN.getCode(), -1);
        assertNull(result);
    }

    /**
     * 测试用户类型有效且用户ID有效但未找到分销商时返回null
     */
    @Test
    public void testGetValidDistributorGroup_ValidUserTypeAndUserIdButNoDistributorFound() {
        Mockito.when(groupMapper.selectByExample(any(DistributorGroupCriteria.class))).thenReturn(Collections.emptyList());
        DistributorGroupBO result = distributorGroupRootService.getValidDistributorGroup(DistributorBindUserTypeEnum.MT_USER.getCode(), 1L);
        assertNull(result);
    }

    /**
     * 测试用户类型有效且用户ID有效且找到分销商时返回非null
     */
    @Test
    public void testGetValidDistributorGroup_ValidUserTypeAndUserIdAndDistributorFound() {
        Mockito.when(groupMapper.selectByExample(any(DistributorGroupCriteria.class))).thenReturn(Arrays.asList(distributorGroup));
        when(channelRootService.getChannel(any())).thenReturn(Lists.newArrayList(DistributionChannelBO.builder()

                .build()));
        DistributorGroupBO result = distributorGroupRootService.getValidDistributorGroup(DistributorBindUserTypeEnum.MT_USER.getCode(), 1L);
        assertNotNull(result);
        assertEquals("Test Group", result.getGroupName());
    }

    /**
     * 测试批量查询分销组列表，输入为空列表
     */
    @Test
    public void testBatchQueryDistributorGroupMapWithEmptyInput() {
        Map<Long, DistributorGroupBO> result = distributorGroupRootService.batchQueryDistributorGroupMap(Collections.emptyList());
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试批量查询分销组列表，正常情况
     */
    @Test
    public void testBatchQueryDistributorGroupMapWithNormalInput() {
        List<Long> distributorGroupIds = Lists.newArrayList(1L, 2L);
        DistributorGroup group1 = new DistributorGroup();
        group1.setId(1);
        DistributorGroup group2 = new DistributorGroup();
        group2.setId(2);
        List<DistributorGroup> distributorGroups = Lists.newArrayList(group1, group2);
        when(groupMapper.selectByExample(any(DistributorGroupCriteria.class))).thenReturn(distributorGroups);
        Map<Long, DistributorGroupBO> result = distributorGroupRootService.batchQueryDistributorGroupMap(distributorGroupIds);
        assertFalse("结果不应为空", result.isEmpty());
        assertEquals("结果大小应为2", 2, result.size());
        assertTrue("应包含ID为1的分销组", result.containsKey(1L));
        assertTrue("应包含ID为2的分销组", result.containsKey(2L));
    }

    /**
     * 测试批量查询分销组列表，数据库返回空
     */
    @Test
    public void testBatchQueryDistributorGroupMapWithNullReturnFromDB() {
        List<Long> distributorGroupIds = Lists.newArrayList(1L, 2L);
        when(groupMapper.selectByExample(any(DistributorGroupCriteria.class))).thenReturn(null);
        Map<Long, DistributorGroupBO> result = distributorGroupRootService.batchQueryDistributorGroupMap(distributorGroupIds);
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试空的分销商ID列表
     */
    @Test
    public void testBatchQueryDistributorGroupListWithEmptyIdList() {
        // arrange
        List<Long> distributorGroupId = Collections.emptyList();

        // act
        List<DistributorGroupBO> result = distributorGroupRootService.batchQueryDistributorGroupList(distributorGroupId);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分销商ID列表不为空，但数据库中没有对应的分销商
     */
    @Test
    public void testBatchQueryDistributorGroupListWithNonEmptyIdListButNoDistributors() {
        // arrange
        List<Long> distributorGroupId = Arrays.asList(1L, 2L);
        when(groupMapper.selectByExample(any(DistributorGroupCriteria.class))).thenReturn(Collections.emptyList());

        // act
        List<DistributorGroupBO> result = distributorGroupRootService.batchQueryDistributorGroupList(distributorGroupId);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分销商ID列表不为空，且数据库中有对应的分销商
     */
    @Test
    public void testBatchQueryDistributorGroupListWithNonEmptyIdListAndDistributors() {
        // arrange
        List<Long> distributorGroupId = Arrays.asList(1L, 2L);
        DistributorGroupChannel distributorGroup1 = new DistributorGroupChannel();
        distributorGroup1.setId(1);
//        distributorGroup1.setName("Distributor 1");
        distributorGroup1.setChannel(1);
        distributorGroup1.setDistributorGroupId(1);

        DistributorGroupChannel distributorGroup2 = new DistributorGroupChannel();
        distributorGroup2.setId(2);
        distributorGroup2.setDistributorGroupId(2);
        distributorGroup1.setChannel(2);
        DistributorGroup distributorGroup11 = new DistributorGroup();
        distributorGroup11.setId(1);
        distributorGroup11.setName("Distributor 11");
//        distributorGroup1.setChannel(1);

        DistributorGroup distributorGroup22 = new DistributorGroup();
        distributorGroup22.setId(2);
        distributorGroup22.setName("Distributor 22");
        when(groupMapper.selectByExample(any(DistributorGroupCriteria.class))).thenReturn(Arrays.asList(distributorGroup11, distributorGroup22));
        when(groupChannelMapper.selectByExample(any(DistributorGroupChannelCriteria.class))).thenReturn(Arrays.asList(distributorGroup1, distributorGroup2));
        // act
        List<DistributorGroupBO> result = distributorGroupRootService.batchQueryDistributorGroupList(distributorGroupId);

        // assert
        assertFalse(result.isEmpty());
        assertEquals(2, result.size());
        assertEquals("Distributor 11", result.get(0).getGroupName());
        assertEquals("Distributor 22", result.get(1).getGroupName());
    }

    /**
     * 测试getGroupMemberCount方法，当groupId为负数时应返回0
     */
    @Test
    public void testGetGroupMemberCount_WithNegativeGroupId() {
        long groupId = -1L;
        int expected = 0;
        int actual = distributorGroupRootService.getGroupMemberCount(groupId);
        assertEquals(expected, actual);
    }

    /**
     * 测试getGroupMemberCount方法，当groupId为0时应返回0
     */
    @Test
    public void testGetGroupMemberCount_WithZeroGroupId() {
        long groupId = 0L;
        int expected = 0;
        int actual = distributorGroupRootService.getGroupMemberCount(groupId);
        assertEquals(expected, actual);
    }

    /**
     * 测试getGroupMemberCount方法，正常情况
     */
    @Test
    public void testGetGroupMemberCount_Normal() {
        Long groupId = 1L;
        int expectedCount = 10;
        DistributorGroupBindCriteria criteria = new DistributorGroupBindCriteria();
        criteria.createCriteria()
                .andDistributorGroupEqualTo(groupId.intValue())
                .andStatusEqualTo(DistributionStatusEnum.VALID.getCode());
        when(groupBindMapper.countByExample(any())).thenReturn((long) expectedCount);
        int actualCount = distributorGroupRootService.getGroupMemberCount(groupId);
        assertEquals(expectedCount, actualCount);
    }

    @Mock
    private DistributorGroupBindMapper groupBindMapper;

}
