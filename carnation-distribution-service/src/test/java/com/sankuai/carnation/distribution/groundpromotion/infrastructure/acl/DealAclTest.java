package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.groundpromotion.dto.DealProductDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryPoiAndGoodsReq;
import com.sankuai.carnation.distribution.groundpromotion.enums.ClientTypeEnum;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/11/11
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class DealAclTest {

    @InjectMocks
    private DealAcl dealAcl;

    @Mock
    private DealProductService dealProductService;

    @Mock
    private ShopMapperService shopMapperService;

    @Mock
    private DealIdMapperService dealIdMapperService;

    /**
     * 测试loadDealProduct方法，当dealProductService.query返回空列表时
     */
    @Test
    public void testLoadDealProductWhenDealProductServiceReturnsEmptyList() throws Throwable {
        // arrange
        QueryPoiAndGoodsReq queryPoiAndGoodsReq = new QueryPoiAndGoodsReq();
        queryPoiAndGoodsReq.setDealIdList(new ArrayList<>());
        queryPoiAndGoodsReq.setDpShopId(1L);

        DealProductRequest dealProductRequest = new DealProductRequest();
        List<DealProductRequest> dealProductRequestList = new ArrayList<>();
        dealProductRequestList.add(dealProductRequest);

        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(new ArrayList<>());

        when(dealProductService.query(any(DealProductRequest.class))).thenReturn(dealProductResult);

        // act
        Map<Long, List<DealProductDTO>> result = dealAcl.loadDealProduct(queryPoiAndGoodsReq);

        // assert
        assertTrue("结果应为空列表", result.get(1L).isEmpty());
    }

    /**
     * 测试loadDealProduct方法，当dealProductService.query抛出异常时
     */
    @Test
    public void testLoadDealProductWhenDealProductServiceThrowsException() throws Throwable {
        // arrange
        QueryPoiAndGoodsReq queryPoiAndGoodsReq = new QueryPoiAndGoodsReq();
        queryPoiAndGoodsReq.setDealIdList(new ArrayList<>());
        queryPoiAndGoodsReq.setDpShopId(1L);

        when(dealProductService.query(any(DealProductRequest.class))).thenThrow(new RuntimeException("服务异常"));

        // act
        Map<Long, List<DealProductDTO>> result = dealAcl.loadDealProduct(queryPoiAndGoodsReq);

        // assert
        assertTrue("结果应为空列表", result.get(1L).isEmpty());
    }

    /**
     * 测试loadDealProduct方法，正常情况
     */
    @Test
    public void testLoadDealProductNormalCase() throws Throwable {
        // arrange
        QueryPoiAndGoodsReq queryPoiAndGoodsReq = new QueryPoiAndGoodsReq();
        List<Long> dealIdList = new ArrayList<>();
        dealIdList.add(1L);
        queryPoiAndGoodsReq.setDealIdList(dealIdList);
        queryPoiAndGoodsReq.setDpShopId(1L);
        queryPoiAndGoodsReq.setPlatform(1);
        queryPoiAndGoodsReq.setClientType(ClientTypeEnum.NATIVE.code);
        queryPoiAndGoodsReq.setUserId(1L);

        DealProductRequest dealProductRequest = new DealProductRequest();
        List<DealProductRequest> dealProductRequestList = new ArrayList<>();
        dealProductRequestList.add(dealProductRequest);

        DealProductResult dealProductResult = new DealProductResult();
        List<com.sankuai.dztheme.deal.dto.DealProductDTO> dealProductDTOList = new ArrayList<>();
        com.sankuai.dztheme.deal.dto.DealProductDTO dealProductDTO = new com.sankuai.dztheme.deal.dto.DealProductDTO();
        List<DealProductAttrDTO> attrDTOList = Lists.newArrayList();
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setName("dealStatusAttr");
        attrDTO.setValue("true");
        attrDTOList.add(attrDTO);
        dealProductDTO.setAttrs(attrDTOList);
        dealProductDTO.setPromoPrices(Lists.newArrayList());
        dealProductDTO.setDetailUr("");
        dealProductDTOList.add(dealProductDTO);
        dealProductResult.setDeals(dealProductDTOList);

        when(dealProductService.query(any(DealProductRequest.class))).thenReturn(dealProductResult);

        // act
        Map<Long, List<DealProductDTO>> result = dealAcl.loadDealProduct(queryPoiAndGoodsReq);

        // assert
        assertFalse("结果不应为空列表", result.get(1L).isEmpty());
        assertEquals("结果列表大小应为1", 1, result.get(1L).size());
    }

    /**
     * 测试loadDealProduct方法，正常情况
     */
    @Test
    public void testLoadDealProductNormalCaseAndMt() throws Throwable {
        // arrange
        QueryPoiAndGoodsReq queryPoiAndGoodsReq = new QueryPoiAndGoodsReq();
        List<Long> dealIdList = new ArrayList<>();
        dealIdList.add(1L);
        queryPoiAndGoodsReq.setDealIdList(dealIdList);
        queryPoiAndGoodsReq.setDpShopId(1L);
        queryPoiAndGoodsReq.setPlatform(2);
        queryPoiAndGoodsReq.setClientType(ClientTypeEnum.NATIVE.code);
        queryPoiAndGoodsReq.setUserId(1L);

        DealProductRequest dealProductRequest = new DealProductRequest();
        List<DealProductRequest> dealProductRequestList = new ArrayList<>();
        dealProductRequestList.add(dealProductRequest);

        DealProductResult dealProductResult = new DealProductResult();
        List<com.sankuai.dztheme.deal.dto.DealProductDTO> dealProductDTOList = new ArrayList<>();
        com.sankuai.dztheme.deal.dto.DealProductDTO dealProductDTO = new com.sankuai.dztheme.deal.dto.DealProductDTO();
        List<DealProductAttrDTO> attrDTOList = Lists.newArrayList();
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setName("dealStatusAttr");
        attrDTO.setValue("true");
        attrDTOList.add(attrDTO);
        dealProductDTO.setAttrs(attrDTOList);
        dealProductDTO.setPromoPrices(Lists.newArrayList());
        dealProductDTO.setDetailUr("");
        dealProductDTOList.add(dealProductDTO);
        dealProductResult.setDeals(dealProductDTOList);

        List<IdMapper> idMappers = Lists.newArrayList();
        IdMapper idMapper = new IdMapper();
        idMapper.setMtDealGroupID(1);
        idMappers.add(idMapper);
        when(dealIdMapperService.queryByDpDealGroupIds(anyList())).thenReturn(idMappers);
        when(dealProductService.query(any(DealProductRequest.class))).thenReturn(dealProductResult);
        when(shopMapperService.dp2mt(anyLong())).thenReturn(1L);

        // act
        Map<Long, List<DealProductDTO>> result = dealAcl.loadDealProduct(queryPoiAndGoodsReq);

        // assert
        assertFalse("结果不应为空列表", result.get(1L).isEmpty());
        assertEquals("结果列表大小应为1", 1, result.get(1L).size());
    }
}
