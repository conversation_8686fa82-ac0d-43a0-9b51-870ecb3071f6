package com.sankuai.carnation.distribution.distributionplan.acl;

import com.dianping.zsts.commission.unify.api.UnifiedCommissionQueryService;
import com.dianping.zsts.commission.unify.dto.DealGroupCommissionDto;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.acl.impl.ProductCommissionBasisAclImpl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisQueryRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductCommissionBasisAclImplTest {

    @InjectMocks
    private ProductCommissionBasisAclImpl productCommissionBasisAcl;

    @Mock
    private UnifiedCommissionQueryService unifiedCommissionQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试空的产品佣金列表
     */
    @Test
    public void testBatchQueryDealGroupCommissionEmptyList() {
        // arrange
        List<ProductCommissionBasisQueryRequest> productCommissionList = Lists.newArrayList();

        // act
        List<ProductCommissionBasisInfo> resultList = productCommissionBasisAcl.batchQueryProductCommissionBasis(productCommissionList);

        // assert
        Assert.assertEquals(resultList, Lists.newArrayList());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBatchQueryDealGroupCommissionNormal() throws Throwable {
        // arrange
        List<ProductCommissionBasisQueryRequest> productCommissionList = Arrays.asList(
                new ProductCommissionBasisQueryRequest(1L, "100"),
                new ProductCommissionBasisQueryRequest(2L, "200")
        );
        DealGroupCommissionDto dealGroupCommissionDto1 = new DealGroupCommissionDto();
        dealGroupCommissionDto1.setDealGroupId(1L);
        dealGroupCommissionDto1.setChangeBaseCategoryFlag(true);
        DealGroupCommissionDto dealGroupCommissionDto2 = new DealGroupCommissionDto();
        dealGroupCommissionDto2.setDealGroupId(2L);
        dealGroupCommissionDto2.setChangeBaseCategoryFlag(false);
        Map<Long, DealGroupCommissionDto> response = new HashMap<>();
        response.put(1L, dealGroupCommissionDto1);
        response.put(2L, dealGroupCommissionDto2);

        when(unifiedCommissionQueryService.batchQueryDealGroupCommission(anyList())).thenReturn(response);

        // act
        List<ProductCommissionBasisInfo> results = productCommissionBasisAcl.batchQueryProductCommissionBasis(productCommissionList);

        // assert
        assertNotNull(results);
    }

    /**
     * 测试查询服务返回null
     */
    @Test
    public void testBatchQueryDealGroupCommissionServiceReturnsNull() throws Throwable {
        // arrange
        List<ProductCommissionBasisQueryRequest> productCommissionList = Lists.newArrayList(new ProductCommissionBasisQueryRequest(1L, "100"));

        when(unifiedCommissionQueryService.batchQueryDealGroupCommission(anyList())).thenReturn(null);

        // act
        List<ProductCommissionBasisInfo> resultList = productCommissionBasisAcl.batchQueryProductCommissionBasis(productCommissionList);

        // assert
        Assert.assertEquals(resultList, Lists.newArrayList());
    }

    /**
     * 测试查询结果中部分产品ID没有对应的佣金信息
     */
    @Test
    public void testBatchQueryDealGroupCommissionMissingCommissionInfo() throws Throwable {
        // arrange
        List<ProductCommissionBasisQueryRequest> productCommissionList = Arrays.asList(
                new ProductCommissionBasisQueryRequest(1L, "100"),
                new ProductCommissionBasisQueryRequest(2L, "200")
        );
        DealGroupCommissionDto dealGroupCommissionDto1 = new DealGroupCommissionDto();
        dealGroupCommissionDto1.setDealGroupId(1L);
        dealGroupCommissionDto1.setChangeBaseCategoryFlag(true);
        Map<Long, DealGroupCommissionDto> response = new HashMap<>();
        response.put(1L, dealGroupCommissionDto1);

        when(unifiedCommissionQueryService.batchQueryDealGroupCommission(anyList())).thenReturn(response);

        // act
        List<ProductCommissionBasisInfo> results = productCommissionBasisAcl.batchQueryProductCommissionBasis(productCommissionList);

        // assert
        assertNotNull(results);
    }
}