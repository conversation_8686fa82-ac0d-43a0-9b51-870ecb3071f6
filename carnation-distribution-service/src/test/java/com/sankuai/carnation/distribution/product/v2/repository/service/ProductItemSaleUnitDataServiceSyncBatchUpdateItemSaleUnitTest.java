package com.sankuai.carnation.distribution.product.v2.repository.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService;
import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceSyncBatchUpdateItemSaleUnitTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductItemSaleUnitMapper mapper;

    private ProductItemSaleUnit productItemSaleUnit;

    @Mock
    private ProductSelectifyAclService productSelectifyAclService;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Before
    public void setUp() {
        productItemSaleUnit = new ProductItemSaleUnit();
        productItemSaleUnit.setId(1L);
        productItemSaleUnit.setAmount(10);
        productItemSaleUnit.setStatus(1);
    }

    private boolean invokeSyncBatchUpdateItemSaleUnit(Object... args) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("syncBatchUpdateItemSaleUnit", java.util.List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(productItemSaleUnitDataService, args);
    }

    private ProductItemSaleUnit createProductItemSaleUnit() {
        return new ProductItemSaleUnit();
    }

    private boolean invokeBatchUpdateItemSaleUnit(List<ProductItemSaleUnit> list) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("batchUpdateItemSaleUnit", List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(productItemSaleUnitDataService, list);
    }

    @Test
    public void testSyncBatchUpdateItemSaleUnitWithEmptyList() throws Throwable {
        assertTrue(invokeSyncBatchUpdateItemSaleUnit(Collections.emptyList()));
    }

    @Test
    public void testSyncBatchUpdateItemSaleUnitWithAllUpdateFail() throws Throwable {
        when(mapper.updateByPrimaryKeySelective(any(ProductItemSaleUnit.class))).thenReturn(0);
        assertFalse(invokeSyncBatchUpdateItemSaleUnit(Arrays.asList(productItemSaleUnit)));
    }

    @Test
    public void testSyncBatchUpdateItemSaleUnitWithOneUpdateSuccess() throws Throwable {
        when(mapper.updateByPrimaryKeySelective(any(ProductItemSaleUnit.class))).thenReturn(1);
        assertTrue(invokeSyncBatchUpdateItemSaleUnit(Arrays.asList(productItemSaleUnit)));
    }

    @Test(expected = ProductItemException.class)
    public void testSyncBatchUpdateItemSaleUnitWithException() throws Throwable {
        when(mapper.updateByPrimaryKeySelective(any(ProductItemSaleUnit.class))).thenThrow(new RuntimeException());
        try {
            invokeSyncBatchUpdateItemSaleUnit(Arrays.asList(productItemSaleUnit));
        } catch (Exception e) {
            if (e.getCause() instanceof ProductItemException) {
                throw (ProductItemException) e.getCause();
            } else {
                throw e;
            }
        }
    }

    @Test
    public void testBatchUpdateItemSaleUnitEmptyList() throws Throwable {
        List<ProductItemSaleUnit> list = Collections.emptyList();
        boolean result = invokeBatchUpdateItemSaleUnit(list);
        assertTrue(result);
    }

    @Test
    public void testBatchUpdateItemSaleUnitAllNullElements() throws Throwable {
        List<ProductItemSaleUnit> list = Arrays.asList(null, null, null);
        boolean result = invokeBatchUpdateItemSaleUnit(list);
        assertTrue(result);
    }

    @Test
    public void testBatchUpdateItemSaleUnitExistElementFail() throws Throwable {
        ProductItemSaleUnit productItemSaleUnit = createProductItemSaleUnit();
        List<ProductItemSaleUnit> list = Arrays.asList(productItemSaleUnit, productItemSaleUnit, productItemSaleUnit);
        boolean result = invokeBatchUpdateItemSaleUnit(list);
        assertFalse(result);
    }
}
