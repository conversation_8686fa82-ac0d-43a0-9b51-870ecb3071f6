package com.sankuai.carnation.distribution.privatelive.consultant.acl.intention;

import com.sankuai.dzusergrowth.growth.privatelive.api.common.dto.Response;
import com.sankuai.dzusergrowth.growth.privatelive.api.request.PrivateLiveConsultantCommRequest;
import com.sankuai.dzusergrowth.growth.privatelive.api.response.PrivateLiveConsultantCommDTO;
import com.sankuai.dzusergrowth.growth.privatelive.api.service.CommRecordService;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.BizException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveIntentionAclServiceTest {

    @Mock
    private CommRecordService commRecordService;

    @InjectMocks
    private PrivateLiveIntentionAclService privateLiveIntentionAclService;

    private PrivateLiveConsultantCommRequest request;
    private Response<Map<Long, PrivateLiveConsultantCommDTO>> successResponse;
    private Response<Map<Long, PrivateLiveConsultantCommDTO>> failureResponse;

    @Before
    public void setUp() {
        request = new PrivateLiveConsultantCommRequest(1L, 2L, null);
        Map<Long, PrivateLiveConsultantCommDTO> data = new HashMap<>();
        data.put(1L, new PrivateLiveConsultantCommDTO(1, 2, 3));
        successResponse = new Response<>();
        successResponse.setData(data);

        failureResponse = new Response<>();
    }

    /**
     * 测试查询咨询师沟通记录成功的情况
     */
    @Test
    public void testQueryConsultantCommInfoSuccess() throws Throwable {
        // arrange
        when(commRecordService.queryConsultantCommInfo(any(PrivateLiveConsultantCommRequest.class))).thenReturn(successResponse);

        // act
        CompletableFuture<Map<Long, PrivateLiveConsultantCommDTO>> result = privateLiveIntentionAclService.queryConsultantCommInfo(request);

        // assert
        assertNotNull(result.get());
    }

    /**
     * 测试查询咨询师沟通记录失败抛出BizException的情况
     */
    @Test
    public void testQueryConsultantCommInfoFailure() throws Throwable {
        // arrange
        when(commRecordService.queryConsultantCommInfo(any(PrivateLiveConsultantCommRequest.class))).thenReturn(failureResponse);

        // act
        CompletableFuture<Map<Long, PrivateLiveConsultantCommDTO>> result = privateLiveIntentionAclService.queryConsultantCommInfo(request);

        // assert
        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试查询咨询师沟通记录时服务异常返回空Map的情况
     */
    @Test(expected = RuntimeException.class)
    public void testQueryConsultantCommInfoException() throws Throwable {
        // arrange
        when(commRecordService.queryConsultantCommInfo(any(PrivateLiveConsultantCommRequest.class))).thenThrow(new RuntimeException("Service exception"));

        // act
        CompletableFuture<Map<Long, PrivateLiveConsultantCommDTO>> result = privateLiveIntentionAclService.queryConsultantCommInfo(request);

        // assert
        assertTrue(result.get().isEmpty());
    }
}
