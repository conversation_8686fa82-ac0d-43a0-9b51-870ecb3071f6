package com.sankuai.carnation.distribution.distributor.vo;

import org.junit.Assert;
import org.junit.Test;

public class DistributorGroupVOTest {

    /**
     * 测试设置和获取分销商ID
     */
    @Test
    public void testSetAndGetGroupId() {
        DistributorGroupVO distributorGroupVO = new DistributorGroupVO();
        Long expected = 12345L;
        distributorGroupVO.setGroupId(expected);
        Long actual = distributorGroupVO.getGroupId();
        Assert.assertEquals(expected, actual);
    }

    /**
     * 测试设置和获取分销商名称
     */
    @Test
    public void testSetAndGetGroupName() {
        DistributorGroupVO distributorGroupVO = new DistributorGroupVO();
        String expected = "Test Group";
        distributorGroupVO.setGroupName(expected);
        String actual = distributorGroupVO.getGroupName();
        Assert.assertEquals(expected, actual);
    }

    /**
     * 测试设置和获取分销商手机号
     */
    @Test
    public void testSetAndGetMobile() {
        DistributorGroupVO distributorGroupVO = new DistributorGroupVO();
        String expected = "1234567890";
        distributorGroupVO.setMobile(expected);
        String actual = distributorGroupVO.getMobile();
        Assert.assertEquals(expected, actual);
    }

    // 更多属性的测试可以按照上述模式添加
}
