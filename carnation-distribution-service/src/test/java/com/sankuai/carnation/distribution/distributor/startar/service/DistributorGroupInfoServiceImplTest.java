package com.sankuai.carnation.distribution.distributor.startar.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.distributor.assembler.DistributorGroupVOAssembler;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.dto.request.QueryDistributorGroupRequest;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.vo.AnchorDistributorGroupVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorGroupVO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DistributorGroupInfoServiceImplTest {

    @InjectMocks
    private DistributorGroupInfoServiceImpl distributorGroupInfoService;

    @Mock
    private DistributorGroupAppService distributorGroupAppService;

    @Mock
    private DistributorGroupVOAssembler distributorGroupVOAssembler;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;

    @InjectMocks
    private DistributorGroupInfoServiceImpl service;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试queryByDistributorGroupId方法，当distributorGroupId为null时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryByDistributorGroupIdWithNullId() throws Throwable {
        // arrange
        Long distributorGroupId = null;
        // act
        distributorGroupInfoService.queryByDistributorGroupId(distributorGroupId);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryByDistributorGroupId方法，当distributorGroupId有效时返回成功的RemoteResponse
     */
    @Test
    public void testQueryByDistributorGroupIdWithValidId() throws Throwable {
        // arrange
        Long distributorGroupId = 1L;
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        DistributorGroupVO distributorGroupVO = new DistributorGroupVO();
        when(distributorGroupAppService.getDistributorGroup(distributorGroupId)).thenReturn(distributorGroupModel);
        when(distributorGroupVOAssembler.buildDistributorVO(distributorGroupModel)).thenReturn(distributorGroupVO);
        // act
        RemoteResponse<DistributorGroupVO> response = distributorGroupInfoService.queryByDistributorGroupId(distributorGroupId);
        // assert
        assertNotNull(response);
        assertEquals(distributorGroupVO, response.getData());
    }

    /**
     * 测试queryByDistributorGroupId方法，当distributorGroupId为负数时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryByDistributorGroupIdWithNegativeId() throws Throwable {
        // arrange
        Long distributorGroupId = -1L;
        // act
        distributorGroupInfoService.queryByDistributorGroupId(distributorGroupId);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryByDistributorGroupId方法，当distributorGroupId有效但未找到对应分销商信息时返回空的RemoteResponse
     */
    @Test
    public void testQueryByDistributorGroupIdWithValidIdButNoDataFound() throws Throwable {
        // arrange
        Long distributorGroupId = 2L;
        when(distributorGroupAppService.getDistributorGroup(distributorGroupId)).thenReturn(null);
        when(distributorGroupVOAssembler.buildDistributorVO(any(DistributorGroupBO.class))).thenReturn(null);
        // act
        RemoteResponse<DistributorGroupVO> response = distributorGroupInfoService.queryByDistributorGroupId(distributorGroupId);
        // assert
        assertNotNull(response);
        assertNull(response.getData());
    }

    /**
     * 测试queryByDistributorGroupId方法，当distributorGroupId有效且找到对应分销商信息但VO转换失败时返回空的RemoteResponse
     */
    @Test
    public void testQueryByDistributorGroupIdWithValidIdButVOConversionFails() throws Throwable {
        // arrange
        Long distributorGroupId = 3L;
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        when(distributorGroupAppService.getDistributorGroup(distributorGroupId)).thenReturn(distributorGroupModel);
        when(distributorGroupVOAssembler.buildDistributorVO(distributorGroupModel)).thenReturn(null);
        // act
        RemoteResponse<DistributorGroupVO> response = distributorGroupInfoService.queryByDistributorGroupId(distributorGroupId);
        // assert
        assertNotNull(response);
        assertNull(response.getData());
    }

    @Test
    public void testQueryApplyDistributorGroupInfo_ValidRequestAndGroupExists() {
        QueryDistributorGroupRequest request = new QueryDistributorGroupRequest(1L, "wxToken", 1L);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setGroupId(1L);
        PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO = new PrivateLiveAnchorDistributorGroupBO();
        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
        AnchorDistributorGroupVO expectedVO = new AnchorDistributorGroupVO();
        when(distributorGroupAppService.getDistributorGroup(anyLong())).thenReturn(distributorGroupModel);
        when(privateLiveAnchorDistributorGroupRepository.queryByDistributorGroupId(eq(1L), anyList())).thenReturn(privateLiveAnchorDistributorGroupBO);
        when(distributorGroupVOAssembler.buildAnchorDistributorGroupVO(eq(distributorGroupModel), eq(privateLiveAnchorDistributorGroupBO))).thenReturn(expectedVO);
        RemoteResponse<AnchorDistributorGroupVO> response = distributorGroupInfoService.queryApplyDistributorGroupInfo(request);
        assertNotNull(response);
        assertEquals(expectedVO, response.getData());
        verify(distributorGroupAppService, times(1)).getDistributorGroup(anyLong());
        verify(privateLiveAnchorDistributorGroupRepository, times(1)).queryByDistributorGroupId(eq(1L), anyList());
        verify(distributorGroupVOAssembler, times(1)).buildAnchorDistributorGroupVO(eq(distributorGroupModel), eq(privateLiveAnchorDistributorGroupBO));
    }

    /**
     * 测试查询分销组信息，当请求参数有效但分销组不存在时
     */
    @Test
    public void testQueryApplyDistributorGroupInfo_ValidRequestAndGroupNotExists() {
        QueryDistributorGroupRequest request = new QueryDistributorGroupRequest(1L, "wxToken", 1L);
        when(distributorGroupAppService.getDistributorGroup(anyLong())).thenReturn(null);
        RemoteResponse<AnchorDistributorGroupVO> response = distributorGroupInfoService.queryApplyDistributorGroupInfo(request);
        assertNotNull(response);
        assertNull(response.getData());
        verify(distributorGroupAppService, times(1)).getDistributorGroup(anyLong());
        verify(privateLiveAnchorDistributorGroupRepository, never()).queryByDistributorGroupId(anyLong(), anyList());
        verify(distributorGroupVOAssembler, never()).buildAnchorDistributorGroupVO(any(DistributorGroupModel.class), any(PrivateLiveAnchorDistributorGroupBO.class));
    }

    /**
     * 测试查询分销组信息，当请求参数无效时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryApplyDistributorGroupInfo_InvalidRequest() {
        QueryDistributorGroupRequest request = new QueryDistributorGroupRequest(null, null, null);
        try {
            distributorGroupInfoService.queryApplyDistributorGroupInfo(request);
        } finally {
            verify(distributorGroupAppService, never()).getDistributorGroup(anyLong());
            verify(privateLiveAnchorDistributorGroupRepository, never()).queryByDistributorGroupId(anyLong(), anyList());
            verify(distributorGroupVOAssembler, never()).buildAnchorDistributorGroupVO(any(DistributorGroupModel.class), any(PrivateLiveAnchorDistributorGroupBO.class));
        }
    }

    /**
     * 测试输入为空列表时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testGetRelationDistributorGroupsEmptyList() throws Throwable {
        // arrange
        List<Long> accountIdList = Arrays.asList();
        // act
        service.getRelationDistributorGroups(accountIdList);
        // assert is handled by the expected exception
    }

    /**
     * 测试输入列表项超过50时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testGetRelationDistributorGroupsExceedLimit() throws Throwable {
        // arrange
        List<Long> accountIdList = Arrays.asList(new Long[51]);
        // act
        service.getRelationDistributorGroups(accountIdList);
        // assert is handled by the expected exception
    }

    /**
     * 测试正常情况下的返回值
     */
    @Test
    public void testGetRelationDistributorGroupsNormal() throws Throwable {
        // arrange
        List<Long> accountIdList = Arrays.asList(1L, 2L);
        Map<Long, DistributorGroupModel> modelMap = new HashMap<>();
        DistributorGroupModel model = new DistributorGroupModel();
        model.setAccountId(1L);
        modelMap.put(1L, model);
        when(distributorGroupAppService.getRelationDistributorGroupMap(accountIdList)).thenReturn(modelMap);
        DistributorGroupVO vo = new DistributorGroupVO();
        vo.setAccountId(1L);
        when(distributorGroupVOAssembler.buildDistributorVO(any(DistributorGroupModel.class))).thenReturn(vo);
        // act
        RemoteResponse<Map<Long, DistributorGroupVO>> response = service.getRelationDistributorGroups(accountIdList);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().containsKey(1L));
        assertEquals(Long.valueOf(1), response.getData().get(1L).getAccountId());
        verify(distributorGroupAppService, times(1)).getRelationDistributorGroupMap(accountIdList);
        verify(distributorGroupVOAssembler, times(1)).buildDistributorVO(any(DistributorGroupModel.class));
    }

    /**
     * 测试getRelationDistributorGroupList方法，当accountIdList为空时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testGetRelationDistributorGroupListWithEmptyAccountIdList() throws Throwable {
        // arrange
        List<Long> accountIdList = Collections.emptyList();
        // act
        distributorGroupInfoService.getRelationDistributorGroupList(accountIdList);
        // assert 由于期望抛出异常，所以不需要额外的断言
    }

    /**
     * 测试getRelationDistributorGroupList方法，当accountIdList的大小超过50时抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testGetRelationDistributorGroupListWithAccountIdListSizeOver50() throws Throwable {
        // arrange
        List<Long> accountIdList = Collections.nCopies(51, 1L);
        // act
        distributorGroupInfoService.getRelationDistributorGroupList(accountIdList);
        // assert 由于期望抛出异常，所以不需要额外的断言
    }

    /**
     * 测试getRelationDistributorGroupList方法，当accountIdList有效且不超过50时，返回成功
     */
    @Test
    public void testGetRelationDistributorGroupListWithValidAccountIdList() throws Throwable {
        // arrange
        List<Long> accountIdList = Arrays.asList(1L, 2L);
        List<DistributorGroupVO> expectedVOList = Collections.emptyList();
        when(distributorGroupVOAssembler.toDistributorVOListWithModel(anyList())).thenReturn(expectedVOList);
        // act
        RemoteResponse<List<DistributorGroupVO>> response = distributorGroupInfoService.getRelationDistributorGroupList(accountIdList);
        // assert
        verify(distributorGroupVOAssembler, times(1)).toDistributorVOListWithModel(anyList());
        assert response.getData().equals(expectedVOList);
    }
}
