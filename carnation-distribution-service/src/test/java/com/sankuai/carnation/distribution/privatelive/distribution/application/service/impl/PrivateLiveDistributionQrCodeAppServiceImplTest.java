package com.sankuai.carnation.distribution.privatelive.distribution.application.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.carnation.distribution.privatelive.distribution.application.model.DistributionQrCodeInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

public class PrivateLiveDistributionQrCodeAppServiceImplTest {

    @Mock
    private UniversalQRCodeGeneratorService universalQRCodeGeneratorService;


    @InjectMocks
    private PrivateLiveDistributionQrCodeAppServiceImpl privateLiveDistributionQrCodeAppService;


    private final Long anchorId = 1L;
    private final Long distributorGroupId = 2L;
    private final String bizId = "2";
    private final Integer bizType = BizTypeEnum.DISTRIBUTOR_CODE.getType();


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getOrCreateRegisterDistributorGroupQrCode方法，当请求有效且主播ID匹配时返回成功
     */
    @Test
    public void testGetOrCreateRegisterDistributorGroupQrCode_ValidRequest_Success() {
        // arrange
        Long anchorId = 1L;
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setCodeUrl("codeUrl");
        qrCodeConfigDTO.setImageUrl("imageUrl");
        RemoteResponse response = RemoteResponse.success(qrCodeConfigDTO);
        when(universalQRCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(response);


        // act
        DistributionQrCodeInfo result = privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId);

        // assert
        assertNotNull(result);
        assertEquals("codeUrl", result.getCodeUrl());
        assertEquals("imageUrl", result.getImageUrl());
    }

    /**
     * 测试getOrCreateRegisterDistributorGroupQrCode方法，当主播ID为null时抛出IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterDistributorGroupQrCode_NullAnchorId_ThrowsIllegalArgumentException() {
        // arrange
        Long anchorId = null;

        // act
        privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId);

        // assert is handled by the expected exception
    }

    /**
     * 测试成功获取分销员二维码的情况
     */
    @Test
    public void testGetOrCreateRegisterDistributorQrCode_Success() {
        // arrange
        Long anchorId = 1L;
        Long distributorGroupId = 1L;
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setCodeUrl("codeUrl");
        qrCodeConfigDTO.setImageUrl("imageUrl");
        RemoteResponse response = RemoteResponse.success(qrCodeConfigDTO);
        when(universalQRCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(response);
        // 模拟方法返回预期结果
        // act
        DistributionQrCodeInfo result = privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert
        assertNotNull(result);
        assertEquals("codeUrl", result.getCodeUrl());
        assertEquals("imageUrl", result.getImageUrl());
    }

    /**
     * 测试当anchorId或distributorGroupId为null时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterDistributorQrCode_NullParameters() {
        // arrange
        Long anchorId = null;
        Long distributorGroupId = null;

        // act
        privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert 通过注解expected = IllegalArgumentException.class来验证预期的异常
    }

    /**
     * 测试当anchorId为null时的异常情况
     */
    @Test
    public void testGetOrCreateRegisterDistributorQrCode_NullAnchorId() {
        // arrange
        Long anchorId = null;
        Long distributorGroupId = 1L;

        // act
        privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert 通过注解expected = IllegalArgumentException.class来验证预期的异常
    }

    /**
     * 测试当distributorGroupId为null时的异常情况
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterDistributorQrCode_NullDistributorGroupId() {
        // arrange
        Long anchorId = 1L;
        Long distributorGroupId = null;

        // act
        privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert 通过注解expected = IllegalArgumentException.class来验证预期的异常
    }

    /**
     * 测试当方法返回null时的情况
     */
    @Test
    public void testGetOrCreateRegisterDistributorQrCode_ReturnNull() {
        // arrange
        Long anchorId = 1L;
        Long distributorGroupId = 1L;

        // 模拟方法返回null
        when(privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId)).thenReturn(null);

        // act
        DistributionQrCodeInfo result = privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert
        assertNull(result);
    }

    /**
     * 测试当方法抛出异常时的情况
     */
    @Test(expected = RuntimeException.class)
    public void testGetOrCreateRegisterDistributorQrCode_ThrowsException() {
        // arrange
        Long anchorId = 1L;
        Long distributorGroupId = 1L;

        // 模拟方法抛出异常
        when(privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId)).thenThrow(new RuntimeException());

        // act
        privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert 通过注解expected = RuntimeException.class来验证预期的异常
    }

    /**
     * 测试getOrCreateRegisterDistributorQrCode方法，当远程服务返回有效的QRCodeConfigDTO时
     */
    @Test
    public void testGetOrCreateRegisterDistributorQrCodeWithValidResponse() {
        // arrange
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setCodeUrl("testUrl");
        RemoteResponse response = RemoteResponse.success(qrCodeConfigDTO);

        when(universalQRCodeGeneratorService.queryQRCode(any(String.class), any(Integer.class))).thenReturn(response);

        // act
        DistributionQrCodeInfo result = privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert
        assertNotNull(result);
        assertEquals("testUrl", result.getCodeUrl());
    }

    /**
     * 测试getOrCreateRegisterDistributorQrCode方法，当远程服务返回null时
     */
    @Test
    public void testGetOrCreateRegisterDistributorQrCodeWithNullResponse() {
        // arrange
        when(universalQRCodeGeneratorService.queryQRCode(any(String.class), any(Integer.class))).thenReturn(null);

        // act
        DistributionQrCodeInfo result = privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert
        assertNull(result);
    }

    /**
     * 测试getOrCreateRegisterDistributorQrCode方法，当远程服务返回的QRCodeConfigDTO数据为null时
     */
    @Test
    public void testGetOrCreateRegisterDistributorQrCodeWithNullDataResponse() {
        // arrange
        RemoteResponse<QRCodeConfigDTO> response =RemoteResponse.success(null) ;

        when(universalQRCodeGeneratorService.queryQRCode(any(String.class), any(Integer.class))).thenReturn(response);

        // act
        DistributionQrCodeInfo result = privateLiveDistributionQrCodeAppService.getOrCreateRegisterDistributorQrCode(anchorId, distributorGroupId);

        // assert
        assertNull(result);
    }
}