package com.sankuai.carnation.distribution.privatelive.distribution.converter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.repository.DistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.po.PrivateLiveAnchorDistributorGroup;

import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class PrivateLiveDistributorGroupConverterTest {

    @InjectMocks
    private PrivateLiveDistributorGroupConverter converter;

    @Mock
    private DistributorGroupBO mockDistributorGroupBO;

    @Mock
    private DistributorGroupRepository distributorGroupRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试toPoJo方法，当DistributorGroupBO和DistributionApproveStatusEnum都不为空时
     */
    @Test
    public void testToPoJo_WithNonNullDistributorGroupAndStatus() {
        // arrange
        PrivateLiveAnchorDistributorGroupBO bo = new PrivateLiveAnchorDistributorGroupBO();
        bo.setDistributorGroup(DistributorGroupBO.builder().groupId(1).build());
        bo.setStatus(DistributionApproveStatusEnum.PASS);
        // act
        PrivateLiveAnchorDistributorGroup result = converter.toPoJo(bo);
        // assert
        assertEquals(java.util.Optional.ofNullable(1L), java.util.Optional.ofNullable(result.getDistributorGroupId()));
        assertEquals(java.util.Optional.ofNullable(DistributionApproveStatusEnum.PASS.getCode()), java.util.Optional.ofNullable(result.getStatus()));
    }

    /**
     * 测试toPoJo方法，当DistributorGroupBO为空时
     */
    @Test
    public void testToPoJo_WithNullDistributorGroup() {
        // arrange
        PrivateLiveAnchorDistributorGroupBO bo = new PrivateLiveAnchorDistributorGroupBO();
        bo.setStatus(DistributionApproveStatusEnum.PASS);
        // act
        PrivateLiveAnchorDistributorGroup result = converter.toPoJo(bo);
        // assert
        assertEquals(null, result.getDistributorGroupId());
        assertEquals(java.util.Optional.ofNullable(DistributionApproveStatusEnum.PASS.getCode()), java.util.Optional.ofNullable(result.getStatus()));
    }

    /**
     * 测试toPoJo方法，当DistributionApproveStatusEnum为空时
     */
    @Test
    public void testToPoJo_WithNullStatus() {
        // arrange
        PrivateLiveAnchorDistributorGroupBO bo = new PrivateLiveAnchorDistributorGroupBO();
        bo.setDistributorGroup(DistributorGroupBO.builder().groupId(1).build());
        // act
        PrivateLiveAnchorDistributorGroup result = converter.toPoJo(bo);
        // assert
        assertEquals(java.util.Optional.ofNullable(1L), java.util.Optional.ofNullable(result.getDistributorGroupId()));
        assertEquals(null, result.getStatus());
    }

    /**
     * 测试toPoJo方法，当DistributorGroupBO和DistributionApproveStatusEnum都为空时
     */
    @Test
    public void testToPoJo_WithNullDistributorGroupAndStatus() {
        // arrange
        PrivateLiveAnchorDistributorGroupBO bo = new PrivateLiveAnchorDistributorGroupBO();
        // act
        PrivateLiveAnchorDistributorGroup result = converter.toPoJo(bo);
        // assert
        assertEquals(null, result.getDistributorGroupId());
        assertEquals(null, result.getStatus());
    }

    /**
     * 测试toEntity方法，当distributorGroupId为null时
     */
    @Test
    public void testToEntityDistributorGroupIdIsNull() {
        PrivateLiveAnchorDistributorGroup input = new PrivateLiveAnchorDistributorGroup();
        input.setStatus(1);
        input.setVersion(1);
        PrivateLiveAnchorDistributorGroupBO result = converter.toEntity(input);
        assertNull(result.getDistributorGroup());
        assertEquals(DistributionApproveStatusEnum.PASS, result.getStatus());
    }

    /**
     * 测试toEntity方法，当distributorGroupId不为null，且能在repository中找到对应的DistributorGroupBO
     */
    @Test
    public void testToEntityDistributorGroupIdIsNotNullAndFound() {
        PrivateLiveAnchorDistributorGroup input = new PrivateLiveAnchorDistributorGroup();
        input.setDistributorGroupId(1L);
        input.setStatus(1);
        input.setVersion(1);
        DistributorGroupBO expectedGroup = new DistributorGroupBO();
        when(distributorGroupRepository.queryByGroupId(1L)).thenReturn(expectedGroup);
        PrivateLiveAnchorDistributorGroupBO result = converter.toEntity(input);
        assertEquals(expectedGroup, result.getDistributorGroup());
        assertEquals(DistributionApproveStatusEnum.PASS, result.getStatus());
    }

    /**
     * 测试toEntity方法，当distributorGroupId不为null，但在repository中找不到对应的DistributorGroupBO
     */
    @Test
    public void testToEntityDistributorGroupIdIsNotNullAndNotFound() {
        PrivateLiveAnchorDistributorGroup input = new PrivateLiveAnchorDistributorGroup();
        input.setDistributorGroupId(1L);
        input.setStatus(1);
        input.setVersion(1);
        when(distributorGroupRepository.queryByGroupId(1L)).thenReturn(null);
        PrivateLiveAnchorDistributorGroupBO result = converter.toEntity(input);
        assertNull(result.getDistributorGroup());
        assertEquals(DistributionApproveStatusEnum.PASS, result.getStatus());
    }

    /**
     * 测试toEntity方法，当status为null时
     */
    @Test
    public void testToEntityStatusIsNull() {
        PrivateLiveAnchorDistributorGroup input = new PrivateLiveAnchorDistributorGroup();
        input.setDistributorGroupId(1L);
        input.setVersion(1);
//        input.setStatus(1);
        DistributorGroupBO expectedGroup = new DistributorGroupBO();
        when(distributorGroupRepository.queryByGroupId(1L)).thenReturn(expectedGroup);
        PrivateLiveAnchorDistributorGroupBO result = converter.toEntity(input);
        assertEquals(expectedGroup, result.getDistributorGroup());
        assertNull(result.getStatus());
    }
}
