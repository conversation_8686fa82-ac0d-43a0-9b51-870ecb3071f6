package com.sankuai.carnation.distribution.privatelive.distribution.eums;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class DistributionApproveStatusEnumTest {

    /**
     * 测试操作类型为1时，返回审核通过的状态码
     */
    @Test
    public void testGetAfterCodeByOperateTypePass() {
        // arrange
        Integer operateType = 1;
        // act
        Integer result = DistributionApproveStatusEnum.getAfterCodeByOperateType(operateType);
        // assert
        Assert.assertEquals(Integer.valueOf(DistributionApproveStatusEnum.PASS.getCode()), result);
    }

    /**
     * 测试操作类型为2时，返回已驳回的状态码
     */
    @Test
    public void testGetAfterCodeByOperateTypeReject() {
        // arrange
        Integer operateType = 2;
        // act
        Integer result = DistributionApproveStatusEnum.getAfterCodeByOperateType(operateType);
        // assert
        Assert.assertEquals(Integer.valueOf(DistributionApproveStatusEnum.REJECT.getCode()), result);
    }

    /**
     * 测试操作类型为3时，返回已取消的状态码
     */
    @Test
    public void testGetAfterCodeByOperateTypeCanceled() {
        // arrange
        Integer operateType = 3;
        // act
        Integer result = DistributionApproveStatusEnum.getAfterCodeByOperateType(operateType);
        // assert
        Assert.assertEquals(Integer.valueOf(DistributionApproveStatusEnum.CANCELED.getCode()), result);
    }

    /**
     * 测试操作类型为默认情况时，返回审核中的状态码
     */
    @Test
    public void testGetAfterCodeByOperateTypeDefault() {
        // arrange
        // 任意不为1、2、3的值
        Integer operateType = 5;
        // act
        Integer result = DistributionApproveStatusEnum.getAfterCodeByOperateType(operateType);
        // assert
        Assert.assertEquals(Integer.valueOf(DistributionApproveStatusEnum.WAITING.getCode()), result);
    }

    /**
     * 测试操作类型为null时，应返回审核中的状态码
     */
    @Test
    public void testGetAfterCodeByOperateTypeNull() {
        // arrange
        Integer operateType = null;
        // act
        Integer result = DistributionApproveStatusEnum.getAfterCodeByOperateType(operateType);
        // assert
        Assert.assertEquals(DistributionApproveStatusEnum.WAITING.getCode(), result);
    }

    /**
     * 测试doesShow方法，当code为1时应返回true
     */
    @Test
    public void testDoesShowWhenCodeIs1() {
        // arrange
        Integer code = 1;
        // act
        boolean result = DistributionApproveStatusEnum.doesShow(code);
        // assert
        Assert.assertTrue("当code为1时，应返回true", result);
    }

    /**
     * 测试doesShow方法，当code为2时应返回true
     */
    @Test
    public void testDoesShowWhenCodeIs2() {
        // arrange
        Integer code = 2;
        // act
        boolean result = DistributionApproveStatusEnum.doesShow(code);
        // assert
        Assert.assertTrue("当code为2时，应返回true", result);
    }

    /**
     * 测试doesShow方法，当code为0时应返回false
     */
    @Test
    public void testDoesShowWhenCodeIs0() {
        // arrange
        Integer code = 0;
        // act
        boolean result = DistributionApproveStatusEnum.doesShow(code);
        // assert
        Assert.assertFalse("当code为0时，应返回false", result);
    }

    /**
     * 测试doesShow方法，当code为3时应返回false
     */
    @Test
    public void testDoesShowWhenCodeIs3() {
        // arrange
        Integer code = 3;
        // act
        boolean result = DistributionApproveStatusEnum.doesShow(code);
        // assert
        Assert.assertFalse("当code为3时，应返回false", result);
    }

    /**
     * 测试doesShow方法，当code为4时应返回false
     */
    @Test
    public void testDoesShowWhenCodeIs4() {
        // arrange
        Integer code = 4;
        // act
        boolean result = DistributionApproveStatusEnum.doesShow(code);
        // assert
        Assert.assertFalse("当code为4时，应返回false", result);
    }

    /**
     * 测试doesShow方法，当code为null时应如何处理，此处假设应抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testDoesShowWhenCodeIsNull() {
        // arrange
        Integer code = null;
        // act
        DistributionApproveStatusEnum.doesShow(code);
        // assert 是通过expected异常来实现的
    }
}
