package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.dztrade.dto.tradeEvent.GeneralEventNotifyDTO;
import com.dianping.dztrade.dto.tradeEvent.OrderBaseInfo;
import com.dianping.dztrade.dto.tradeEvent.RefundBaseInfo;
import com.dianping.dztrade.enums.BizCodeEnum;
import com.dianping.dztrade.enums.TradeEventEnum;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.order.domain.enums.ProductEnum;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderSKUDTO;
import com.dianping.receipt.query.dto.ReceiptDTO;
import com.dianping.refund.platform.api.model.enums.RefundProcessTemplate;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.common.PrepayInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelResultDataService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveOrderIntentionServiceImpl;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/18
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionGroupOrderConsumerTest {

    @InjectMocks
    private GroundPromotionGroupOrderConsumer consumer;

    @Mock
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    @Mock
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Mock
    private OrderChannelResultDataService resultDataService;

    @Mock
    private RedisStoreClient redisStoreClient;

    @Mock
    private DistributorRootService distributorRootService;

    private RemoteResponse<Boolean> successResponse;

    private static final MockedStatic<Lion> mockLionFactory = Mockito.mockStatic(Lion.class);


    @BeforeClass
    public static void mockStatic() {
        mockLionFactory.when(() -> Lion.getBoolean(eq(Environment.getAppName()), eq("privatelive.trade.order.refund.handle.switch"), eq(true))).thenReturn(true);
    }

    @AfterClass
    public static void closeStatic() {
        mockLionFactory.close();
    }

    @Before
    public void setUp() {
        successResponse = RemoteResponse.success(true);
    }

    /**
     * 使用反射调用私有方法
     */
    private ConsumeStatus invokeHandle(MafkaMessage message, MessagetContext context) throws Exception {
        Method handleMethod = GroundPromotionGroupOrderConsumer.class.getDeclaredMethod("handle", MafkaMessage.class,
                MessagetContext.class);
        handleMethod.setAccessible(true);
        return (ConsumeStatus) handleMethod.invoke(consumer, message, context);
    }

    @Test
    public void testPrivateLiveCreateHandle() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setBizCode(BizCodeEnum.groupbuy.getBizCode());
        generalEventNotifyDTO.setEventCode(TradeEventEnum.REFUND_FUND_COMPLETE.getEventCode());
        RefundBaseInfo refundBaseInfo = new RefundBaseInfo();
        refundBaseInfo.setRefundTemplateId(RefundProcessTemplate.PRIVATEBOOKING_COMMON_REFUND.getTemplateID());
        generalEventNotifyDTO.setRefundBaseInfo(refundBaseInfo);
        generalEventNotifyDTO.setUnifiedOrderId("12344");
        generalEventNotifyDTO.setReferIds(Lists.newArrayList("123"));
        generalEventNotifyDTO.setEventTime(new Date().getTime());
        String messageBody = JSON.toJSONString(generalEventNotifyDTO);

        Mockito.when(message.getBody()).thenReturn(messageBody);
        Mockito.when(resultDataService.getCalResultByOrder(anyInt(), anyString())).thenReturn(null);

        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("12344");
        orderIntentionResult.setStatus(OrderStatusEnum.CREATE.getCode());
        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult((PrivateLiveOrderUpdateInfo) any())).thenReturn(successResponse);

        // act
        ConsumeStatus status = this.invokeHandle(message, context);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
    }

    @Test
    public void testPrivateLiveCancelIntentionResultNotNullHandle() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setBizCode(BizCodeEnum.groupbuy.getBizCode());
        generalEventNotifyDTO.setEventCode(TradeEventEnum.ORDER_CANCEL.getEventCode());
        generalEventNotifyDTO.setUnifiedOrderId("12344");
        generalEventNotifyDTO.setReferIds(Lists.newArrayList("123"));
        generalEventNotifyDTO.setEventTime(new Date().getTime());
        String messageBody = JSON.toJSONString(generalEventNotifyDTO);

        Mockito.when(message.getBody()).thenReturn(messageBody);

        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("12344");
        orderIntentionResult.setStatus(OrderStatusEnum.CREATE.getCode());
        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult((PrivateLiveOrderUpdateInfo) any())).thenReturn(successResponse);

        // act
        ConsumeStatus status = this.invokeHandle(message, context);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
    }

    @Test
    public void testPrivateLiveCancelIntentionResultIsNullHandle() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setBizCode(BizCodeEnum.groupbuy.getBizCode());
        generalEventNotifyDTO.setEventCode(TradeEventEnum.ORDER_CANCEL.getEventCode());
        generalEventNotifyDTO.setUnifiedOrderId("12344");
        generalEventNotifyDTO.setReferIds(Lists.newArrayList("123"));
        generalEventNotifyDTO.setEventTime(new Date().getTime());
        String messageBody = JSON.toJSONString(generalEventNotifyDTO);

        Mockito.when(message.getBody()).thenReturn(messageBody);

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(null);

        // act
        ConsumeStatus status = this.invokeHandle(message, context);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
    }

    private PrivateLiveOrderInfo invokeInitPrivateLiveOrderInfo(GeneralEventNotifyDTO generalEventNotifyDTO, String orderId) throws Exception {
        Method handleMethod = GroundPromotionGroupOrderConsumer.class.getDeclaredMethod("initPrivateLiveOrderInfo", GeneralEventNotifyDTO.class,
                String.class);
        handleMethod.setAccessible(true);
        return (PrivateLiveOrderInfo) handleMethod.invoke(consumer, generalEventNotifyDTO, orderId);
    }

    @Test
    public void testInitPrivateLiveOrderInfo() throws Exception {

        PrepayInfoBO prepayInfoBO = new PrepayInfoBO();
        prepayInfoBO.setTotalAmount(new BigDecimal("4444"));
        prepayInfoBO.setPrepayAmount(new BigDecimal("22"));
        prepayInfoBO.setBalancePaymentAmount(new BigDecimal("4422"));

        Map<String, String> map = Maps.newHashMap();
        map.put("general.ext.prepayInfo", JsonUtil.toJson(prepayInfoBO));
        map.put("general.ext.extraOrderType", "14");

        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        orderBaseInfo.setOrderId("2222");
        orderBaseInfo.setUnifiedOrderId("2222");
        orderBaseInfo.setPlatform(1);
        orderBaseInfo.setMtUserId("12");
        orderBaseInfo.setDpUserId("23");
        orderBaseInfo.setMtShopId("34");
        orderBaseInfo.setDpShopId("44");
        orderBaseInfo.setMtCityId(0);
        orderBaseInfo.setDpCityId(0);
        orderBaseInfo.setCustomerId("123");
        orderBaseInfo.setTotalAmount(new BigDecimal("33"));
        orderBaseInfo.setActualAmount(new BigDecimal("34"));
        orderBaseInfo.setExtraMap(map);
        orderBaseInfo.setCreateTime(System.currentTimeMillis());
        orderBaseInfo.setOrderProductBaseInfoList(Lists.newArrayList());

        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setBizCode("1234");
        generalEventNotifyDTO.setEventCode("2222");
        generalEventNotifyDTO.setUnifiedOrderId("2222");
        generalEventNotifyDTO.setReferIds(new ArrayList<>());
        generalEventNotifyDTO.setOrderBaseInfo(orderBaseInfo);
        // act
        PrivateLiveOrderInfo privateLiveOrderInfo = this.invokeInitPrivateLiveOrderInfo(generalEventNotifyDTO, "1234");

        // assert
        assertNotNull(privateLiveOrderInfo);
    }

    private void invokeRetryProcessCancelOrder(String orderId, GeneralEventNotifyDTO generalEventNotifyDTO, Long expirationTime) throws Exception {
        Method handleMethod = GroundPromotionGroupOrderConsumer.class.getDeclaredMethod("retryProcessCancelOrder", String.class, GeneralEventNotifyDTO.class
                , Long.class);
        handleMethod.setAccessible(true);
        handleMethod.invoke(consumer, orderId, generalEventNotifyDTO, expirationTime);
    }

    @Test
    public void testRetryProcessCancelOrderResultIsNull() throws Exception {
        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setEventCode("2222");
        generalEventNotifyDTO.setEventTime(System.currentTimeMillis());

        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", "1234" + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getEventTime().toString()
                , OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(null);

        long expirationTime = System.currentTimeMillis() + 8000L;
        // act
        this.invokeRetryProcessCancelOrder("1234", generalEventNotifyDTO, expirationTime);

        // assert
        verify(redisStoreClient, times(1)).delete(storeKey);

    }

    @Test
    public void testRetryProcessCancelOrderResultNotNull() throws Exception {
        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setEventCode("2222");
        generalEventNotifyDTO.setEventTime(System.currentTimeMillis());

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(new PrivateLiveOrderIntentionResult());
        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult(any(PrivateLiveOrderUpdateInfo.class))).thenReturn(successResponse);

        long expirationTime = System.currentTimeMillis() + 8000L;
        // act
        this.invokeRetryProcessCancelOrder("1234", generalEventNotifyDTO, expirationTime);

        // assert
        verify(privateLiveOrderIntentionService, times(1)).updateIntentionResult(any(PrivateLiveOrderUpdateInfo.class));

    }

    @Test
    public void testGroundPromotionRefund() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        GeneralEventNotifyDTO generalEventNotifyDTO = new GeneralEventNotifyDTO();
        generalEventNotifyDTO.setBizCode(BizCodeEnum.groupbuy.getBizCode());
        generalEventNotifyDTO.setEventCode(TradeEventEnum.REFUND_FUND_COMPLETE.getEventCode());
        RefundBaseInfo refundBaseInfo = new RefundBaseInfo();
        refundBaseInfo.setRefundTemplateId(RefundProcessTemplate.PRIVATEBOOKING_COMMON_REFUND.getTemplateID());
        refundBaseInfo.setReceiptIdList(Lists.newArrayList("123"));
        generalEventNotifyDTO.setRefundBaseInfo(refundBaseInfo);
        generalEventNotifyDTO.setUnifiedOrderId("12344");
        generalEventNotifyDTO.setReferIds(Lists.newArrayList("123"));
        generalEventNotifyDTO.setEventTime(new Date().getTime());
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        orderBaseInfo.setDpShopId("1");
        generalEventNotifyDTO.setOrderBaseInfo(orderBaseInfo);
        String messageBody = JSON.toJSONString(generalEventNotifyDTO);

        Mockito.when(message.getBody()).thenReturn(messageBody);

        DistributionOrderChannelCalResult result = new DistributionOrderChannelCalResult();
        result.setChannel(DistributionBusinessChannelEnum.GROUND_PROMOTION.getCode());
        result.setDistributionCode("dt5645$$1");
        Mockito.when(resultDataService.getCalResultByOrder(anyInt(), anyString())).thenReturn(result);

        BizDistributorBO bizDistributorBO = new BizDistributorBO();
        bizDistributorBO.setBizType(DistributorBizTypeEnum.GROUND_PART_TIME_PROMOTION.getCode());
        Mockito.when(distributorRootService.getBizDistributor(anyString())).thenReturn(bizDistributorBO);

        UnifiedOrderWithId unifiedOrder = new UnifiedOrderWithId();
        unifiedOrder.setBizType(ProductEnum.mtGroupon.value);
        unifiedOrder.setPlatform(70050000);
        unifiedOrder.setLongMtShopId(1L);
        UnifiedOrderSKUDTO unifiedOrderSKUDTO = new UnifiedOrderSKUDTO();
        unifiedOrderSKUDTO.setSpugId("1");
        unifiedOrder.setSkus(Lists.newArrayList(unifiedOrderSKUDTO));
        unifiedOrder.setLongOrderId(1L);
        unifiedOrder.setUserId(1L);
        unifiedOrder.setMtUserId(1L);
        unifiedOrder.setTotalAmount(new BigDecimal(1.1));
        unifiedOrder.setAddTime(new Date());
        unifiedOrder.setPaySuccessTime(new Date());

        Map<Long, ReceiptDTO> receiptDTOMap = new HashMap<>();
        ReceiptDTO receiptDTO = new ReceiptDTO();
        receiptDTO.setSerialNumber("11");
        receiptDTOMap.put(123L, receiptDTO);

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(null);

        // act
        ConsumeStatus status = this.invokeHandle(message, context);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
    }
}
