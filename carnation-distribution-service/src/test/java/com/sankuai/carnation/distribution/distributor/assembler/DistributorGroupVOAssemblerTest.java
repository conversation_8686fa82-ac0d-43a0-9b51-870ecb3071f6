package com.sankuai.carnation.distribution.distributor.assembler;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupAttrBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.domain.enums.DistributorGroupAttrEnum;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.vo.AnchorDistributorGroupVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorGroupVO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.MobileHelper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DistributorGroupVOAssemblerTest {

    @InjectMocks
    private DistributorGroupVOAssembler distributorGroupVOAssembler;

    @Mock
    private MobileHelper mobileHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试buildDistributorGroupModel方法，当输入参数为null时
     */
    @Test
    public void testBuildDistributorGroupModelWithNullInput() {
        // arrange
        DistributorGroupBO distributorGroupBO = null;
        // act
        DistributorGroupModel result = distributorGroupVOAssembler.buildDistributorGroupModel(distributorGroupBO);
        // assert
        assertNull(result);
    }

    /**
     * 测试buildDistributorGroupModel方法，当DistributorGroupBO包含空的distributorGroupAttrList时
     */
    @Test
    public void testBuildDistributorGroupModelWithEmptyAttrList() {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        distributorGroupBO.setGroupName("Test Group");
        distributorGroupBO.setUserId(123L);
        distributorGroupBO.setDistributorGroupAttrList(Collections.emptyList());
        // act
        DistributorGroupModel result = distributorGroupVOAssembler.buildDistributorGroupModel(distributorGroupBO);
        // assert
        assertNotNull(result);
        assertEquals("Test Group", result.getGroupName());
        assertEquals(Long.valueOf(1), result.getGroupId());
        assertEquals(Long.valueOf(123), result.getAccountId());
        assertNull(result.getCustomerId());
        assertNull(result.getCustomerName());
    }


    /**
     * 测试buildDistributorVO方法，当输入参数为null时应返回null
     */
    @Test
    public void testBuildDistributorVOWithNullInput() {
        // arrange
        // act
        DistributorGroupVO result = distributorGroupVOAssembler.buildDistributorVO((DistributorGroupBO) null);
        // assert
        assertNull(result);
    }

    /**
     * 测试buildDistributorVO方法，当输入参数有效且包含所有必要信息时应正确返回DistributorGroupVO
     */
    @Test
    public void testBuildDistributorVOWithValidInput() {
        // arrange
        DistributorGroupBO distributorGroupBO = DistributorGroupBO.builder().groupId(1).userId(123L).principalMobile("*********").build();
        when(mobileHelper.getMobileMask("*********")).thenReturn("123***789");
        // act
        DistributorGroupVO result = distributorGroupVOAssembler.buildDistributorVO(distributorGroupBO);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(123), result.getAccountId());
        assertEquals(Long.valueOf(1), result.getGroupId());
        assertEquals("123***789", result.getMobile());
    }

    /**
     * 测试buildDistributorVO方法，当输入参数有效但手机号为空时应正确处理并返回DistributorGroupVO
     */
    @Test
    public void testBuildDistributorVOWithValidInputButNullMobile() {
        // arrange
        DistributorGroupBO distributorGroupBO = DistributorGroupBO.builder().groupId(1).userId(123L).principalMobile(null).build();
        // act
        DistributorGroupVO result = distributorGroupVOAssembler.buildDistributorVO(distributorGroupBO);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(123), result.getAccountId());
        assertEquals(Long.valueOf(1), result.getGroupId());
        assertNull(result.getMobile());
    }

    /**
     * 测试buildDistributorVO方法，当输入参数有效且principalMobile为null时
     */
    @Test
    public void testBuildDistributorVOWithValidInputAndNullMobile() {
        // arrange
        DistributorGroupModel distributor = new DistributorGroupModel();
        distributor.setAccountId(1L);
        distributor.setGroupId(2L);
        distributor.setGroupName("Test Group");
        distributor.setCompanyName("Test Company");
        distributor.setPrincipalMobile(null);
        distributor.setGroupCode("TG123");
        distributor.setCustomerId("C123");
        distributor.setCustomerName("Customer");
        distributor.setStatus(1);
        // act
        DistributorGroupVO result = distributorGroupVOAssembler.buildDistributorVO(distributor);
        // assert
        assertNotNull(result);
        assertEquals(distributor.getAccountId(), result.getAccountId());
        assertEquals(distributor.getGroupId(), result.getGroupId());
        assertEquals(distributor.getGroupName(), result.getGroupName());
        assertEquals(distributor.getCompanyName(), result.getCompanyName());
        assertNull(result.getMobile());
        assertEquals(distributor.getGroupCode(), result.getGroupCode());
        assertEquals(distributor.getCustomerId(), result.getCustomerId());
        assertEquals(distributor.getCustomerName(), result.getCustomerName());
        assertEquals(distributor.getStatus(), result.getStatus());
    }

    /**
     * 测试buildDistributorVO方法，当输入参数有效且principalMobile非null时
     */
    @Test
    public void testBuildDistributorVOWithValidInputAndNonNullMobile() {
        // arrange
        DistributorGroupModel distributor = new DistributorGroupModel();
        distributor.setAccountId(1L);
        distributor.setGroupId(2L);
        distributor.setGroupName("Test Group");
        distributor.setCompanyName("Test Company");
        distributor.setPrincipalMobile("*********");
        distributor.setGroupCode("TG123");
        distributor.setCustomerId("C123");
        distributor.setCustomerName("Customer");
        distributor.setStatus(1);
        when(mobileHelper.getMobileMask("*********")).thenReturn("123***789");
        // act
        DistributorGroupVO result = distributorGroupVOAssembler.buildDistributorVO(distributor);
        // assert
        assertNotNull(result);
        assertEquals(distributor.getAccountId(), result.getAccountId());
        assertEquals(distributor.getGroupId(), result.getGroupId());
        assertEquals(distributor.getGroupName(), result.getGroupName());
        assertEquals(distributor.getCompanyName(), result.getCompanyName());
        assertEquals("123***789", result.getMobile());
        assertEquals(distributor.getGroupCode(), result.getGroupCode());
        assertEquals(distributor.getCustomerId(), result.getCustomerId());
        assertEquals(distributor.getCustomerName(), result.getCustomerName());
        assertEquals(distributor.getStatus(), result.getStatus());
        verify(mobileHelper, times(1)).getMobileMask("*********");
    }

    /**
     * 测试 toDistributorVOListWithModel 方法，输入为空
     */
    @Test
    public void testToDistributorVOListWithModelWithEmptyInput() {
        // arrange
        List<DistributorGroupModel> distributorModelList = Collections.emptyList();
        // act
        List<DistributorGroupVO> result = distributorGroupVOAssembler.toDistributorVOListWithModel(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 toDistributorVOListWithModel 方法，输入为空列表
     */
    @Test
    public void testToDistributorVOListWithModelWithEmptyList() {
        // arrange
        List<DistributorGroupModel> distributorModelList = Collections.emptyList();
        DistributorGroupVOAssembler distributorGroupVOAssembler = new DistributorGroupVOAssembler();
        // act
        List<DistributorGroupVO> result = distributorGroupVOAssembler.toDistributorVOListWithModel(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 toDistributorVOListWithModel 方法，输入为null
     */
    @Test
    public void testToDistributorVOListWithModelWithNullInput() {
        // arrange
        List<DistributorGroupModel> distributorModelList = null;
        DistributorGroupVOAssembler distributorGroupVOAssembler = new DistributorGroupVOAssembler();
        // act
        List<DistributorGroupVO> result = distributorGroupVOAssembler.toDistributorVOListWithModel(distributorModelList);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }



    /**
     * 测试 toDistributorVOList 方法，当输入列表为空时
     */
    @Test
    public void testToDistributorVOListWhenListIsEmpty() {
        // arrange
        List<DistributorGroupBO> distributorModelList = Lists.newArrayList();
        // act
        List<DistributorGroupVO> result = distributorGroupVOAssembler.toDistributorVOList(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 toDistributorVOList 方法，当输入列表为null时
     */
    @Test
    public void testToDistributorVOListWhenListIsNull() {
        // arrange
        List<DistributorGroupBO> distributorModelList = null;
        // act
        List<DistributorGroupVO> result = distributorGroupVOAssembler.toDistributorVOList(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 toDistributorVOList 方法，当输入列表包含元素时
     */
    @Test
    public void testToDistributorVOListWhenListHasElements() {
        // arrange
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        distributorGroupBO.setGroupName("Test Group");
        List<DistributorGroupBO> distributorModelList = Arrays.asList(distributorGroupBO);
        // act
        List<DistributorGroupVO> result = distributorGroupVOAssembler.toDistributorVOList(distributorModelList);
        // assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        DistributorGroupVO distributorGroupVO = result.get(0);
        assertEquals(Long.valueOf(distributorGroupBO.getGroupId()), distributorGroupVO.getGroupId());
        assertEquals(distributorGroupBO.getGroupName(), distributorGroupVO.getGroupName());
    }

    /**
     * 测试当PrivateLiveAnchorDistributorGroupBO为null时，返回的状态码为UN_APPLY
     */
    @Test
    public void testBuildAnchorDistributorGroupVO_WithNullPrivateLiveAnchorDistributorGroupBO() {
        // arrange
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO = null;
        // act
        AnchorDistributorGroupVO result = distributorGroupVOAssembler.buildAnchorDistributorGroupVO(distributorGroupModel, privateLiveAnchorDistributorGroupBO);
        // assert
        assertEquals(DistributionApproveStatusEnum.UN_APPLY.getCode(), result.getStatus());
    }

    /**
     * 测试当PrivateLiveAnchorDistributorGroupBO不为null时，正确设置状态和anchorId
     */
    @Test
    public void testBuildAnchorDistributorGroupVO_WithNonNullPrivateLiveAnchorDistributorGroupBO() {
        // arrange
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO = new PrivateLiveAnchorDistributorGroupBO();
        privateLiveAnchorDistributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
        privateLiveAnchorDistributorGroupBO.setAnchorId(1L);
        // act
        AnchorDistributorGroupVO result = distributorGroupVOAssembler.buildAnchorDistributorGroupVO(distributorGroupModel, privateLiveAnchorDistributorGroupBO);
        // assert
        assertEquals(DistributionApproveStatusEnum.PASS.getCode(), result.getStatus());
        assertEquals(Long.valueOf(1), result.getAnchorId());
    }

    /**
     * 测试当DistributorGroupModel的principalMobile不为空时，mobile应通过MobileHelper进行脱敏处理
     */
    @Test
    public void testBuildAnchorDistributorGroupVO_WithNonNullPrincipalMobile() {
        // arrange
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setPrincipalMobile("*********01");
        PrivateLiveAnchorDistributorGroupBO privateLiveAnchorDistributorGroupBO = null;
        when(mobileHelper.getMobileMask("*********01")).thenReturn("123****8901");
        // act
        AnchorDistributorGroupVO result = distributorGroupVOAssembler.buildAnchorDistributorGroupVO(distributorGroupModel, privateLiveAnchorDistributorGroupBO);
        // assert
        assertEquals("123****8901", result.getMobile());
    }
}
