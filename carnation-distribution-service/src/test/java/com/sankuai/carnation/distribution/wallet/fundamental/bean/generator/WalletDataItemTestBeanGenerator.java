package com.sankuai.carnation.distribution.wallet.fundamental.bean.generator;

import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletDataItemStatusEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateTypeEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/6
 **/
public class WalletDataItemTestBeanGenerator {

    public static WalletDataItem generateDataItemPo(long dataItemId, long amount, WalletDataItemStatusEnum status, WalletOperateTypeEnum operateType) {
        WalletDataItem dataItem = new WalletDataItem();
        dataItem.setId(dataItemId);
        dataItem.setBizCode(WalletTestConstants.mockBizCode);
        dataItem.setWalletAccountId(WalletTestConstants.mockWalletAccountId);
        dataItem.setWalletActivityAccountId(WalletTestConstants.mockActivityAccountId);
        dataItem.setBizId(WalletTestConstants.mockDefaultString);
        dataItem.setUnifiedOrderId(WalletTestConstants.mockDefaultString);
        dataItem.setSearchId(WalletTestConstants.mockDefaultString);
        dataItem.setSecondSearchId(WalletTestConstants.mockDefaultString);
        dataItem.setAmount(amount);
        dataItem.setOperateType(operateType.getCode());
        dataItem.setStatus(status.getCode());
        return dataItem;
    }
}
