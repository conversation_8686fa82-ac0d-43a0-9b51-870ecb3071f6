package com.sankuai.carnation.distribution.wallet.fundamental.repository.service;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletOperateFlowMapper;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletOperateFlowExample;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WalletOperateFlowDataServiceTest {

    @InjectMocks
    private WalletOperateFlowDataService walletOperateFlowDataService;

    @Mock
    private WalletOperateFlowMapper flowMapper;

    /**
     * 测试 dataItemId 小于等于0 的情况
     */
    @Test
    public void testQueryFlowByItemIdDataItemIdLessThanOrEqualToZero() throws Throwable {
        // arrange
        long dataItemId = 0L;
        int operateType = 1;
        int flowType = 1;
        // act
        List<WalletOperateFlow> result = walletOperateFlowDataService.queryFlowByItemId(dataItemId, operateType, flowType);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 dataItemId 大于0，且数据库中存在符合条件的记录的情况
     */
    @Test
    public void testQueryFlowByItemIdDataItemIdGreaterThanZeroAndRecordExists() throws Throwable {
        // arrange
        long dataItemId = 1L;
        int operateType = 1;
        int flowType = 1;
        List<WalletOperateFlow> expected = Collections.singletonList(new WalletOperateFlow());
        when(flowMapper.selectByExample(any(WalletOperateFlowExample.class))).thenReturn(expected);
        // act
        List<WalletOperateFlow> result = walletOperateFlowDataService.queryFlowByItemId(dataItemId, operateType, flowType);
        // assert
        assertEquals(expected, result);
    }

    /**
     * 测试 dataItemId 大于0，但数据库中不存在符合条件的记录的情况
     */
    @Test
    public void testQueryFlowByItemIdDataItemIdGreaterThanZeroAndRecordNotExists() throws Throwable {
        // arrange
        long dataItemId = 1L;
        int operateType = 1;
        int flowType = 1;
        when(flowMapper.selectByExample(any(WalletOperateFlowExample.class))).thenReturn(Collections.emptyList());
        // act
        List<WalletOperateFlow> result = walletOperateFlowDataService.queryFlowByItemId(dataItemId, operateType, flowType);
        // assert
        assertEquals(Collections.emptyList(), result);
    }
}