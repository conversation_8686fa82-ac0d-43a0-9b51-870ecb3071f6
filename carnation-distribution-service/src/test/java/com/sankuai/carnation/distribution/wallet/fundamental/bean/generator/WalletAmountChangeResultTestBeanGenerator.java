package com.sankuai.carnation.distribution.wallet.fundamental.bean.generator;

import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountAmountChangeResultBO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateTypeEnum;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/6
 **/
public class WalletAmountChangeResultTestBeanGenerator {

    public static WalletActivityAccountAmountChangeResultBO generateChangeResult(boolean success, long formerAmount, long currentAmount, WalletOperateTypeEnum operateType, long changeAmount, String failReason) {
        WalletActivityAccountAmountChangeResultBO changeResultBO = new WalletActivityAccountAmountChangeResultBO();
        changeResultBO.setSuccess(success);
        changeResultBO.setActivityAccountId(WalletTestConstants.mockActivityAccountId);
        changeResultBO.setFormerAmount(formerAmount);
        changeResultBO.setCurrentAmount(currentAmount);
        changeResultBO.setOperateType(operateType.getCode());
        changeResultBO.setChangeAmount(changeAmount);
        changeResultBO.setFailReason(failReason);
        return changeResultBO;
    }
}
