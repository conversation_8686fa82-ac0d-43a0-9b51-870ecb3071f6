package com.sankuai.carnation.distribution.promocode.privilege.service;

import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.dianping.tp.search.dto.DealGroupInfoDTO;
import com.dianping.tp.search.dto.MerchantListSearchConditionDTO;
import com.dianping.tp.search.dto.PageModel;
import com.dianping.tp.search.service.MerchantSearchService;
import com.sankuai.carnation.distribution.promocode.privilege.dto.PageResponseDTO;
import com.sankuai.carnation.distribution.promocode.privilege.dto.QROfflineDealGroupDTO;
import com.sankuai.carnation.distribution.promocode.privilege.dto.QROfflineDealGroupInfoDTO;
import com.sankuai.carnation.distribution.promocode.privilege.enums.ResponseCodeEnum;
import com.sankuai.carnation.distribution.promocode.privilege.repository.service.QROfflineDealGroupService;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.privatelive.ProductDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.response.BaseResponse;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonProductQueryService;
import com.sankuai.nibmp.infra.amp.attribute.lib.IAccountService;
import com.sankuai.nibmp.infra.amp.attribute.lib.dto.AccountInfoDTO;
import com.sankuai.nibmp.infra.amp.attribute.lib.result.AccountInfoResult;
import org.apache.thrift.TException;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * 测试场景分类：
 * 1、页面中全为绑定团单
 * 2、页面中部分为绑定团单，部分为未绑定团单
 * 3、页面中全为未绑定团单
 * 4、全部未绑定时前一页末尾团单id不存在此次查询结果中(正好是货架中前一页的结尾)
 * 5、全部未绑定时前一页末尾团单id存在此次查询结果中
 *
 */
@RunWith(MockitoJUnitRunner.class)
public class QROfflineDealGroupApplicationServiceTest {

    @InjectMocks
    private QROfflineDealGroupApplicationServiceImpl service;

    @Mock
    private QROfflineDealGroupService qrOfflineDealGroupService;

    @Mock
    private IAccountService newAccountService;

    @Mock
    private MerchantSearchService merchantSearchService;

    @Mock
    private CommonProductQueryService commonProductQueryService;

    @Mock
    private PromoQRCodeCService promoQRCodeCService;

    @Mock
    private DealGroupQueryService dealGroupQueryService;

    @Mock
    private ConfigRepository config;

    // 其他需要mock的服务可以继续添加
    long dpShopId = 123L;
    long dpAccountId = 456L;
    long userId = 789L;
    private MerchantListSearchConditionDTO dto;
    private MerchantListSearchConditionDTO dto1;
    private MerchantListSearchConditionDTO dto2;
    private MerchantListSearchConditionDTO dto3;
    private static final MockedStatic<Lion> mockLionFactory = Mockito.mockStatic(Lion.class);

    @BeforeClass
    public static void mockStatic() {
        mockLionFactory.when(() -> Lion.getBooleanValue("com.sankuai.medicalcosmetology.distribution.service.new.dealgroup.manage",false)).thenReturn(true);
        mockLionFactory.when(() -> Lion.get(any(), any())).thenReturn("1");
    }
    @Before
    public void setUp() throws TException {
        MockitoAnnotations.initMocks(this);

        dto = new MerchantListSearchConditionDTO();
        dto1 = new MerchantListSearchConditionDTO();
        dto2 = new MerchantListSearchConditionDTO();
        dto3 = new MerchantListSearchConditionDTO();
        buildConditionDTO(dto);
        buildConditionDTO(dto1);
        buildConditionDTO(dto2);
        buildConditionDTO(dto3);
        when(newAccountService.getAccountById(any())).thenReturn(mockaccountInfoResult());
        when(promoQRCodeCService.getDpShopCodeList(dpShopId)).thenReturn(mockPromoResponse());
        when(commonProductQueryService.getBoundedProductList(any())).thenReturn(mockboundedProductList());
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(mockBoundedDealGroupInfo());
    }

    private void buildConditionDTO(MerchantListSearchConditionDTO dto) {
        dto.setPageSize(20);
        dto.setCustomerId(1000);
        dto.setMerchantSearchStatus(3);
        dto.setDealGroupLongShopIds(123L);
        dto.setShopAccountId(0);
    }

    @AfterClass
    public static void closeMockStatic() {
        mockLionFactory.close();;
    }
    /**
     * 访问全是绑定团单的分页结果
     * 
     * @throws TException
     */
    @Test
    public void testPaginateDealGroupListForAllBoundedPage() throws TException {
        // 假设的输入参数
        MerchantListSearchConditionDTO conditionDTO = new MerchantListSearchConditionDTO();

        long lastDpDealGroupId = 0L;
        int pageNo = 1;
        int pageSize = 20;

        conditionDTO.setPageSize(pageSize);
        conditionDTO.setPageIndex(pageNo);
        // 模拟方法调用的返回值

        when(merchantSearchService.searchListByConditionV2(any())).thenReturn(mockPageModel(conditionDTO));
        // 调用方法
        PageResponseDTO<List<QROfflineDealGroupInfoDTO>> result = service.paginateDealGroupList(dpShopId, dpAccountId,
                userId, lastDpDealGroupId, pageNo, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals("查询成功", result.getMsg());
        assertEquals(result.getCode(), ResponseCodeEnum.OK.getCode());
        assertFalse(result.getData().isEmpty());
        assertEquals(result.getData().size(), 20);
    }

    /**
     * 访问部分绑定团单，部分未绑定的分页结果
     * 
     * @throws TException
     */
    @Test
    public void testPaginateDealGroupListForBoundedWithUnBounded() throws TException {
        // 假设的输入参数

        long lastDpDealGroupId = 19L;
        int pageNo = 2;
        int pageSize = 20;

        dto.setPageIndex(1);
        dto1.setPageIndex(2);
        dto2.setPageIndex(3);
        dto3.setPageIndex(4);

        when(merchantSearchService.searchListByConditionV2(any())).thenReturn(mockPageModel(dto));

        // 调用方法
        PageResponseDTO<List<QROfflineDealGroupInfoDTO>> result = service.paginateDealGroupList(dpShopId, dpAccountId,
                userId, lastDpDealGroupId, pageNo, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals("查询成功", result.getMsg());
        assertEquals(result.getCode(), ResponseCodeEnum.OK.getCode());
        assertFalse(result.getData().isEmpty());
        assertEquals(61, result.getTotalCount());
        assertEquals(4, result.getPageCount());
        assertEquals(20, result.getData().size());

    }

    /**
     * 绑定团单数不是pageSize的倍数，查询全是未绑定团单(前一页末尾团单为某一未绑定团单的id)
     * 
     * @throws TException
     */
    @Test
    public void testPaginateDealGroupListForAllUnBounded_pageSizeUnsuitBoundedSize() throws TException {
        // 假设的输入参数
        long lastDpDealGroupId = 36L;
        int pageNo = 3;
        int pageSize = 20;

        dto.setPageIndex(1);
        when(merchantSearchService.searchListByConditionV2(any())).thenReturn(mockPageModel(dto));

        // 调用方法
        PageResponseDTO<List<QROfflineDealGroupInfoDTO>> result = service.paginateDealGroupList(dpShopId, dpAccountId,
                userId, lastDpDealGroupId, pageNo, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals("查询成功", result.getMsg());
        assertEquals(result.getCode(), ResponseCodeEnum.OK.getCode());
        assertFalse(result.getData().isEmpty());
        assertEquals(61, result.getTotalCount());
        assertEquals(4, result.getPageCount());
        assertEquals(37L, result.getData().get(0).getDpDealGroupId());
        assertEquals(20, result.getData().size());
    }

    /**
     * 绑定团单数是pageSize的倍数，查询全是未绑定团单(前一页末尾团单为某一绑定团单的id)
     * 
     * @throws TException
     */
    @Test
    public void testPaginateDealGroupListForAllUnBounded_pageSizesuitBoundedSize() throws TException {
        // 假设的输入参数
        long lastDpDealGroupId = 39L;
        int pageNo = 2;
        int pageSize = 20;
        when(commonProductQueryService.getBoundedProductList(any())).thenReturn(mockBoundedListForPageSize());
        dto.setPageIndex(1);


        when(merchantSearchService.searchListByConditionV2(any())).thenReturn(mockPageModel(dto));

        // 调用方法
        PageResponseDTO<List<QROfflineDealGroupInfoDTO>> result = service.paginateDealGroupList(dpShopId, dpAccountId,
                userId, lastDpDealGroupId, pageNo, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals("查询成功", result.getMsg());
        assertEquals(result.getCode(), ResponseCodeEnum.OK.getCode());
        assertFalse(result.getData().isEmpty());
        assertEquals(59, result.getTotalCount());
        assertEquals(3, result.getPageCount());
        assertEquals(0L, result.getData().get(0).getDpDealGroupId());
        assertEquals(20, result.getData().size());
    }

    /**
     * 绑定团单数是pageSize的倍数，查询全是未绑定团单，且门店返回上线团单数量超过单批次查询数量
     *
     * @throws TException
     */
    @Test
    public void testPaginateDealGroupListForAllUnBounded_pageSizesuitBoundedSize_exceedBatchQuery() throws TException {
        // 假设的输入参数
        long lastDpDealGroupId = 39L;
        int pageNo = 4;
        int pageSize = 20;
        when(commonProductQueryService.getBoundedProductList(any())).thenReturn(mockBoundedListForPageSize());



        when(merchantSearchService.searchListByConditionV2(any())).thenReturn(mockPageModel(dto));

        // 调用方法
        PageResponseDTO<List<QROfflineDealGroupInfoDTO>> result = service.paginateDealGroupList(dpShopId, dpAccountId,
                userId, lastDpDealGroupId, pageNo, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals("查询成功", result.getMsg());
        assertEquals(result.getCode(), ResponseCodeEnum.OK.getCode());
        assertNull(result.getData()); // 绑定数量为20，，上线数量一共58，能绑定的39（第20个的值为-1，所以-19），，总共能展示的只有59个
        assertEquals(59, result.getTotalCount());
        assertEquals(3, result.getPageCount());
    }
    /**
     * 最后一页数据少于pageSize的团单页
     * 
     * @throws TException
     */
    @Test
    public void testPaginateDealGroupListLastUnFillPage() throws TException {
        // 假设的输入参数
        long lastDpDealGroupId = 56L;
        int pageNo = 4;
        int pageSize = 20;
        int onlineDealGroupNum = 100;

        dto.setPageIndex(2);
        when(merchantSearchService.searchListByConditionV2(any())).thenReturn(mockExceedBatchNumPageModel(onlineDealGroupNum),mockExceedBatchNumPageModel2(onlineDealGroupNum));

        // 调用方法
        PageResponseDTO<List<QROfflineDealGroupInfoDTO>> result = service.paginateDealGroupList(dpShopId, dpAccountId,
                userId, lastDpDealGroupId, pageNo, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals("查询成功", result.getMsg());
        assertEquals(result.getCode(), ResponseCodeEnum.OK.getCode());
        assertFalse(result.getData().isEmpty());
        assertEquals(onlineDealGroupNum+3, result.getTotalCount());
        assertEquals(6, result.getPageCount());
        // 返回数据中有三个负数的一定是负数未绑定的, 即上线的数中有83个，前3个为负数，从20开始，第37个位置，即57
        assertEquals(57L, result.getData().get(0).getDpDealGroupId());
        assertEquals(20, result.getData().size());
    }

    private BaseResponse<List<ProductDTO>> mockBoundedListForPageSize() {
        List<ProductDTO> list = new ArrayList<>();
        for (int i = 20; i < 40; i++) {
            ProductDTO productDTO = new ProductDTO();
            productDTO.setProductId((long)i);
            list.add(productDTO);
        }
        return BaseResponse.success(list);
    }

    private PageModel mockPageModel(MerchantListSearchConditionDTO merchantListSearchConditionDTO) {
        PageModel<DealGroupInfoDTO> pageModel = new PageModel<>();
        pageModel.setRecordCount(58);
        List<DealGroupInfoDTO> list = new ArrayList<>();
        for (int i = 0; i < 58; i++) {
            DealGroupInfoDTO dealGroupInfoDTO = new DealGroupInfoDTO();
            dealGroupInfoDTO.setDpDealGroupId(i);
            if (dealGroupInfoDTO.getDpDealGroupId() == 15)
                dealGroupInfoDTO.setDpDealGroupId(-1);
            if (dealGroupInfoDTO.getDpDealGroupId() == 17)
                dealGroupInfoDTO.setDpDealGroupId(-2);
            if (dealGroupInfoDTO.getDpDealGroupId() == 20)
                dealGroupInfoDTO.setDpDealGroupId(-3);
            list.add(dealGroupInfoDTO);

        }
        pageModel.setList(list);
        return pageModel;
    }
    private PageModel mockExceedBatchNumPageModel(int length) {
        PageModel<DealGroupInfoDTO> pageModel = new PageModel<>();
        pageModel.setRecordCount(length);
        List<DealGroupInfoDTO> list = new ArrayList<>();
        for (int i = 0; i < 60; i++) {
            DealGroupInfoDTO dealGroupInfoDTO = new DealGroupInfoDTO();
            dealGroupInfoDTO.setDpDealGroupId(i);
            if (dealGroupInfoDTO.getDpDealGroupId() == 15)
                dealGroupInfoDTO.setDpDealGroupId(-1);
            if (dealGroupInfoDTO.getDpDealGroupId() == 17)
                dealGroupInfoDTO.setDpDealGroupId(-2);
            if (dealGroupInfoDTO.getDpDealGroupId() == 20)
                dealGroupInfoDTO.setDpDealGroupId(-3);
            list.add(dealGroupInfoDTO);

        }
        pageModel.setList(list);
        return pageModel;
    }
    private PageModel mockExceedBatchNumPageModel2(int length) {
        PageModel<DealGroupInfoDTO> pageModel = new PageModel<>();
        pageModel.setRecordCount(length);
        List<DealGroupInfoDTO> list = new ArrayList<>();
        for (int i = 60; i < length; i++) {
            DealGroupInfoDTO dealGroupInfoDTO = new DealGroupInfoDTO();
            dealGroupInfoDTO.setDpDealGroupId(i);
            if (dealGroupInfoDTO.getDpDealGroupId() == 15)
                dealGroupInfoDTO.setDpDealGroupId(-1);
            if (dealGroupInfoDTO.getDpDealGroupId() == 17)
                dealGroupInfoDTO.setDpDealGroupId(-2);
            if (dealGroupInfoDTO.getDpDealGroupId() == 20)
                dealGroupInfoDTO.setDpDealGroupId(-3);
            list.add(dealGroupInfoDTO);

        }
        pageModel.setList(list);
        return pageModel;
    }
    private QueryDealGroupListResponse mockBoundedDealGroupInfo() {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        List<DealGroupDTO> list = new ArrayList<>();
        List<Long> offlineIds = new ArrayList<>();
        offlineIds.add(20L);
        offlineIds.add(17L);
        offlineIds.add(15L);
        for (int i = -3; i < 100; i++) {
            DealGroupDTO productDTO = new DealGroupDTO();
            productDTO.setDpDealGroupId((long)i);
            DealGroupBasicDTO dealGroupBasicDTO = new DealGroupBasicDTO();
            if (offlineIds.contains((long)i))
                dealGroupBasicDTO.setStatus(0);
            else {
                dealGroupBasicDTO.setStatus(1);
            }
            dealGroupBasicDTO.setTitle("mock");
            dealGroupBasicDTO.setEndSaleDate("2025-08-15 14:30:20");
            productDTO.setBasic(dealGroupBasicDTO);
            list.add(productDTO);
        }

        result.setList(list);
        response.setData(result);
        return response;

    }

    private BaseResponse<List<ProductDTO>> mockboundedProductList() {
        List<ProductDTO> list = new ArrayList<>();
        for (int i = 0; i < 23; i++) {
            ProductDTO productDTO = new ProductDTO();
            productDTO.setProductId((long)i);
            list.add(productDTO);
        }
        return BaseResponse.success(list);
    }

    private AccountInfoResult mockaccountInfoResult() {
        AccountInfoDTO mockAccountInfo = new AccountInfoDTO();
        mockAccountInfo.setOriginCustomerId(1000L);
        AccountInfoResult accountInfoResult = new AccountInfoResult();
        accountInfoResult.setAccountInfo(mockAccountInfo);
        return accountInfoResult;
    }

    private PromoQRCodeResponse<List<PromoQRCodeDTO>> mockPromoResponse() {
        PromoQRCodeResponse<List<PromoQRCodeDTO>> mockPromoResponse = new PromoQRCodeResponse<>();
        mockPromoResponse.setData(Collections.singletonList(new PromoQRCodeDTO()));
        return mockPromoResponse;
    }

    private List<QROfflineDealGroupDTO> mockQROfflineDealGroupDTOList() {
        // 根据实际情况构造假数据
        List<QROfflineDealGroupDTO> list = new ArrayList<>();
        QROfflineDealGroupDTO dto = new QROfflineDealGroupDTO();
        // 设置dto的属性
        list.add(dto);
        return list;
    }
}
