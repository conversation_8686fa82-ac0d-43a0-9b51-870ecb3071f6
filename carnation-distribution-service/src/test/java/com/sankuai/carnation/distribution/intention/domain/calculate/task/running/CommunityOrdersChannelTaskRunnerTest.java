package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.pay.order.common.enums.AmountType;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.dto.BizDistributorDTO;
import com.sankuai.carnation.distribution.distributor.dto.request.BizDistributorOperateRequest;
import com.sankuai.carnation.distribution.distributor.service.BizDistributorService;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderDiscountBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.running.CommunityOrdersChannelTaskRunner;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.dz.srcm.automatedmanagement.request.coupon.attribution.ScrmCouponAttributionRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.coupon.attribution.ScrmCouponAttributionResponse;
import com.sankuai.dz.srcm.automatedmanagement.service.ScrmCouponAttributionService;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CommunityOrdersChannelTaskRunnerTest {

    @InjectMocks
    private CommunityOrdersChannelTaskRunner communityOrdersChannelTaskRunner;

    @Mock
    private ScrmCouponAttributionService scrmCouponAttributionService;

    @Mock
    private com.meituan.beauty.idmapper.service.UserMapperService userMapperService;

    private OrderInfoBO orderInfoBO;

    private OrderDiscountBO orderDiscountBO;

    @Mock
    private BizDistributorService bizDistributorService;

    @Before
    public void setUp() {
        orderInfoBO = new OrderInfoBO();
        orderDiscountBO = new OrderDiscountBO();
        orderDiscountBO.setDiscountId("123");
        orderDiscountBO.setType(AmountType.COUPON.value);
        // Set a non-null userId
        orderInfoBO.setUserId(123L);
        // Set a non-null platform
        orderInfoBO.setPlatform(com.sankuai.carnation.distribution.intention.enums.PlatformEnum.MT.getCode());
        orderInfoBO.setDiscountList(Lists.newArrayList(orderDiscountBO));
    }

    // Corrected method to create RemoteResponse with data using Builder pattern
    private RemoteResponse<List<BizDistributorDTO>> createRemoteResponseWithData(List<BizDistributorDTO> data) {
        return // Assuming 0 is success code
        RemoteResponse.<List<BizDistributorDTO>>custom().setCode(0).setMsg("success").setData(data).build();
    }

    @Test
    public void testCalculateByDiscountWhenFilteredDiscountListIsEmpty() throws Throwable {
        // arrange
        // act
        OrderChannelTaskRunningResultBO result = communityOrdersChannelTaskRunner.calculateByDiscount(Collections.emptyList(), orderInfoBO, null);
        // assert
        assertEquals(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
    }

    /**
     * 测试异常路径：extDistributionInfo 不包含 community 字段
     */
    @Test
    public void testCalculateByCode_NoCommunityField() throws Throwable {
        // arrange
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        Map<String, String> extDistributionInfo = new HashMap<>();
        // Ensure community field is present
        extDistributionInfo.put("community", "{\"distributionCode\":\"12345\"}");
        orderInfoBO.setExtDistributionInfo(extDistributionInfo);
        // act
        OrderChannelTaskRunningResultBO result = communityOrdersChannelTaskRunner.calculateByCode(orderInfoBO, task);
        // assert
        assertEquals(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.UNKNOWN.getCode(), result.getBusinessChannel());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
    }

    /**
     * 测试异常路径：解析后的 Map 不包含 distributionCode 字段
     */
    @Test
    public void testCalculateByCode_NoDistributionCodeField() throws Throwable {
        // arrange
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        Map<String, String> extDistributionInfo = new HashMap<>();
        extDistributionInfo.put("community", "{}");
        orderInfoBO.setExtDistributionInfo(extDistributionInfo);
        // act
        OrderChannelTaskRunningResultBO result = communityOrdersChannelTaskRunner.calculateByCode(orderInfoBO, task);
        // assert
        assertEquals(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.UNKNOWN.getCode(), result.getBusinessChannel());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
    }

    /**
     * 测试异常路径：bizDistributorService 返回 null
     */
    @Test
    public void testCalculateByCode_BizDistributorServiceReturnsNull() throws Throwable {
        // arrange
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        Map<String, String> extDistributionInfo = new HashMap<>();
        extDistributionInfo.put("community", "{\"distributionCode\":\"12345\"}");
        orderInfoBO.setExtDistributionInfo(extDistributionInfo);
        when(bizDistributorService.queryBizDistributor(any(BizDistributorOperateRequest.class))).thenReturn(null);
        // act
        OrderChannelTaskRunningResultBO result = communityOrdersChannelTaskRunner.calculateByCode(orderInfoBO, task);
        // assert
        assertEquals(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.UNKNOWN.getCode(), result.getBusinessChannel());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
    }

    /**
     * 测试异常路径：bizDistributorService 返回空列表
     */
    @Test
    public void testCalculateByCode_BizDistributorServiceReturnsEmptyList() throws Throwable {
        // arrange
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        Map<String, String> extDistributionInfo = new HashMap<>();
        extDistributionInfo.put("community", "{\"distributionCode\":\"12345\"}");
        orderInfoBO.setExtDistributionInfo(extDistributionInfo);
        RemoteResponse<List<BizDistributorDTO>> distributorResponse = createRemoteResponseWithData(Collections.emptyList());
        when(bizDistributorService.queryBizDistributor(any(BizDistributorOperateRequest.class))).thenReturn(distributorResponse);
        // act
        OrderChannelTaskRunningResultBO result = communityOrdersChannelTaskRunner.calculateByCode(orderInfoBO, task);
        // assert
        assertEquals(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.UNKNOWN.getCode(), result.getBusinessChannel());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
    }

    /**
     * 测试正常路径：extDistributionInfo 包含 community 字段，且解析后的 Map 包含 distributionCode 字段
     */
    @Test
    public void testCalculateByCode_NormalPath() throws Throwable {
        // arrange
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        Map<String, String> extDistributionInfo = new HashMap<>();
        extDistributionInfo.put("community", "{\"distributionCode\":\"12345\"}");
        orderInfoBO.setExtDistributionInfo(extDistributionInfo);
        BizDistributorDTO bizDistributorDTO = new BizDistributorDTO();
        bizDistributorDTO.setId(123L);
        RemoteResponse<List<BizDistributorDTO>> distributorResponse = createRemoteResponseWithData(Collections.singletonList(bizDistributorDTO));
        when(bizDistributorService.queryBizDistributor(any(BizDistributorOperateRequest.class))).thenReturn(distributorResponse);
        // act
        OrderChannelTaskRunningResultBO result = communityOrdersChannelTaskRunner.calculateByCode(orderInfoBO, task);
        // assert
        assertEquals(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.COMMUNITY.getCode(), result.getBusinessChannel());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
        assertEquals(123L, result.getDistributor().getDistributorId());
    }

    /**
     * 测试场景：订单信息中的扩展分销信息包含社区渠道
     */
    @Test
    public void testIsOrderHitTask_ExtDistributionInfoContainsCommunity() {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfo = new OrderInfoBO();
        HashMap<String, String> extDistributionInfo = new HashMap<>();
        extDistributionInfo.put("community", "true");
        orderInfo.setExtDistributionInfo(extDistributionInfo);
        // act
        boolean result = communityOrdersChannelTaskRunner.isOrderHitTask(taskParam, orderInfo);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：用户记录是社区渠道
     */
    @Test
    public void testIsOrderHitTask_UserRecordIsCommunity() {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        ScrmCouponAttributionResponse responseData = new ScrmCouponAttributionResponse("", true);
        RemoteResponse<ScrmCouponAttributionResponse> response = RemoteResponse.success(responseData);
        when(scrmCouponAttributionService.queryCouponAttributionOnlyByUserId(any(ScrmCouponAttributionRequest.class))).thenReturn(response);
        // act
        boolean result = communityOrdersChannelTaskRunner.isOrderHitTask(taskParam, orderInfoBO);
        // assert
        assertTrue(result);
    }
}
