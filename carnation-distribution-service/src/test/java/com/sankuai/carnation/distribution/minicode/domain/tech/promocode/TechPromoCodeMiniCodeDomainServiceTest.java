package com.sankuai.carnation.distribution.minicode.domain.tech.promocode;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.technician.biz.dto.AuthInfoDTO;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.CityMapperService;
import com.meituan.beauty.user.service.DpUserService;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.minicode.domain.bo.MiniCodeBO;
import com.sankuai.carnation.distribution.minicode.enums.MiniCodeBindUserTypeEnum;
import com.sankuai.carnation.distribution.minicode.repository.service.MiniCodeDataService;
import com.sankuai.carnation.distribution.promocode.acl.TechShopAclService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.technician.account.service.TechAccountAuthQueryService;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Calendar;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/14
 **/
@RunWith(MockitoJUnitRunner.class)
public class TechPromoCodeMiniCodeDomainServiceTest {

    @InjectMocks
    private TechPromoCodeMiniCodeDomainService miniCodeDomainService;

    private static final MockedStatic<Lion> mockLionFactory = Mockito.mockStatic(Lion.class);

    @Mock
    private MiniCodeDataService dataService;

    @Mock
    private DpUserService dpUserService;

    @Mock
    private ShopAcl shopAcl;

    @Mock
    private CityMapperService cityMapperService;

    @Mock
    private TechPromoCodeMiniCodeTransService miniCodeTransService;

    @Mock
    private TechShopAclService techShopAclService;

    @Mock
    private TechAccountAuthQueryService techAccountAuthQueryService;

    private static final List<String> prefixList = Lists.newArrayList("16", "18", "19");

    private static final String techSuffix = "1234";

    private static final int dpCityId = 1;

    private static final int mtCityId = 2;

    private static final int mockTechId = 1;

    @BeforeClass
    public static void mockStatic() {
        mockLionFactory.when(() -> Lion.getList(eq(Environment.getAppName()), eq("com.sankuai.medicalcosmetology.distribution.service.minicode.tech.promocode.prefix"), eq(String.class))).thenReturn(prefixList);
    }

    @AfterClass
    public static void closeStatic() {
        mockLionFactory.close();
    }

    @Test
    public void testGenerateMiniCode_codeExists() {
        String shortCode = "164321";
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockExistsTechCode(shortCode);
        mockAcceptShortCode(shortCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, shortCode);
    }

    @Test
    public void testGenerateMiniCode_conflictPartPrefix() {
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockNoExistsTechCode();
        mockRecommendCodeListPartExists();
        String legalCode = prefixList.get(1) + techSuffix;
        mockAcceptShortCode(legalCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, legalCode);
    }

    @Test
    public void testGenerateMiniCode_mtExistsDpNotExists() {
        String shortCode = "9876";
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockOnlyMtExistsTechCode(shortCode);
        mockAcceptShortCode(shortCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, shortCode);
    }

    @Test
    public void testGenerateMiniCode_conflictAllPrefix() {
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockNoExistsTechCode();
        mockRecommendCodeListAllExists();
        mockCityNoBeginningCode();
        String legalCode = prefixList.get(0) + "0000";
        mockAcceptShortCode(legalCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, legalCode);
    }

    @Test
    public void testGenerateMiniCode_calculateCityCode() {
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockNoExistsTechCode();
        mockRecommendCodeListAllExists();
        mockCityConflictCode();
        String legalCode = prefixList.get(0) + "0001";
        mockAcceptShortCode(legalCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, legalCode);
    }

    @Test
    public void testGenerateMiniCode_conflictDpCity() {
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockNoExistsTechCode();
        mockRecommendCodeListDpPartExists();
        String legalCode = prefixList.get(1) + techSuffix;
        mockAcceptShortCode(legalCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, legalCode);
    }

    @Test
    public void testGenerateMiniCode_conflictMtCity() {
        mockTechAuthInfo();
        mockUserMobile();
        mockTechCity();
        mockNoExistsTechCode();
        mockRecommendCodeListMtPartExists();
        String legalCode = prefixList.get(1) + techSuffix;
        mockAcceptShortCode(legalCode);
        String result = miniCodeDomainService.generateMiniCode(mockTechId);
        assertEquals(result, legalCode);
    }

    private void mockTechAuthInfo() {
        AuthInfoDTO authInfoDTO = new AuthInfoDTO();
        authInfoDTO.setUserIDLong(2L);
        authInfoDTO.setID(mockTechId);
        List<AuthInfoDTO> list = Lists.newArrayList(authInfoDTO);
        TechnicianResp<List<AuthInfoDTO>> resp = TechnicianResp.success(list);
        when(techAccountAuthQueryService.searchAuthInfo(any())).thenReturn(resp);
    }

    private void mockUserMobile() {
        when(dpUserService.loadUserMobile(anyLong())).thenReturn("1809876" + techSuffix);
    }

    private void mockTechCity() {
        when(techShopAclService.getTechShop(anyInt())).thenReturn(3L);
        List<DpPoiDTO> poiList = Lists.newArrayList();;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setCityId(dpCityId);
        dpPoiDTO.setShopId(3L);
        poiList.add(dpPoiDTO);
        when(shopAcl.getDpPoiDTOList(anyList(), anyList())).thenReturn(poiList);
        when(cityMapperService.dp2mt(anyInt())).thenReturn(mtCityId);
    }

    private void mockAcceptShortCode(String shortCode) {
        when(miniCodeTransService.registerTechShortCode(eq(shortCode), anyInt(), anyInt(), anyInt())).thenReturn(shortCode);
        ArgumentMatcher<String> matcher = argument -> !argument.equalsIgnoreCase(shortCode);
    }

    private void mockNoExistsTechCode() {
        List<MiniCodeBO> list = Lists.newArrayList();
        when(dataService.loadByBindUser(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(MiniCodeBindUserTypeEnum.TECHNICIAN.getCode()), anyLong())).thenReturn(list);
    }

    private void mockOnlyMtExistsTechCode(String shortCode) {
        List<MiniCodeBO> list = Lists.newArrayList();
        list.add(mockMiniCodeBO(shortCode, PlatformEnum.MT.getCode(), mtCityId));
        when(dataService.loadByBindUser(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(MiniCodeBindUserTypeEnum.TECHNICIAN.getCode()), anyLong())).thenReturn(list);
    }

    private void mockExistsTechCode(String shortCode) {
        List<MiniCodeBO> list = Lists.newArrayList();
        list.add(mockMiniCodeBO(shortCode, PlatformEnum.DP.getCode(), dpCityId));
        list.add(mockMiniCodeBO(shortCode, PlatformEnum.MT.getCode(), mtCityId));
        when(dataService.loadByBindUser(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(MiniCodeBindUserTypeEnum.TECHNICIAN.getCode()), anyLong())).thenReturn(list);
    }

    private void mockRecommendCodeListPartExists() {
        List<MiniCodeBO> dpList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.DP.getCode(), dpCityId));
        List<MiniCodeBO> mtList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.MT.getCode(), mtCityId));
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.DP.getCode()), eq(1), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(dpList);
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.MT.getCode()), eq(2), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(mtList);
    }

    private void mockRecommendCodeListDpPartExists() {
        List<MiniCodeBO> dpList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.DP.getCode(), dpCityId));
        List<MiniCodeBO> mtList = Lists.newArrayList();
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.DP.getCode()), eq(dpCityId), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(dpList);
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.MT.getCode()), eq(mtCityId), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(mtList);
    }

    private void mockRecommendCodeListMtPartExists() {
        List<MiniCodeBO> dpList = Lists.newArrayList();
        List<MiniCodeBO> mtList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.MT.getCode(), mtCityId));
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.DP.getCode()), eq(dpCityId), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(dpList);
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.MT.getCode()), eq(mtCityId), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(mtList);
    }

    private void mockRecommendCodeListAllExists() {
        List<MiniCodeBO> dpList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.DP.getCode(), dpCityId));
        List<MiniCodeBO> mtList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.MT.getCode(), mtCityId));
        prefixList.forEach(prefix -> {
            dpList.add(mockMiniCodeBO(prefix + techSuffix, PlatformEnum.DP.getCode(), dpCityId));
            dpList.add(mockMiniCodeBO(prefix + techSuffix, PlatformEnum.MT.getCode(), mtCityId));
        });
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.DP.getCode()), eq(dpCityId), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(dpList);
        when(dataService.loadCityMiniCode(anyList(), eq(PlatformEnum.MT.getCode()), eq(mtCityId), eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE))).thenReturn(mtList);
    }

    private void mockCityNoBeginningCode() {
        List<MiniCodeBO> dpList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.DP.getCode(), dpCityId));
        List<MiniCodeBO> mtList = Lists.newArrayList(mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.MT.getCode(), mtCityId));
        when(dataService.loadBizCityCode(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(PlatformEnum.DP.getCode()), eq(dpCityId))).thenReturn(dpList);
        when(dataService.loadBizCityCode(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(PlatformEnum.MT.getCode()), eq(mtCityId))).thenReturn(mtList);
    }

    private void mockCityConflictCode() {
        List<MiniCodeBO> dpList = Lists.newArrayList(
                mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.DP.getCode(), dpCityId),
                mockMiniCodeBO(prefixList.get(0) + "0000", PlatformEnum.DP.getCode(), dpCityId)
        );
        List<MiniCodeBO> mtList = Lists.newArrayList(
                mockMiniCodeBO(prefixList.get(0) + techSuffix, PlatformEnum.MT.getCode(), mtCityId),
                mockMiniCodeBO(prefixList.get(0) + "0000", PlatformEnum.MT.getCode(), mtCityId)
        );
        when(dataService.loadBizCityCode(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(PlatformEnum.DP.getCode()), eq(dpCityId))).thenReturn(dpList);
        when(dataService.loadBizCityCode(eq(TechPromoCodeMiniCodeDomainService.BIZ_CODE), eq(PlatformEnum.MT.getCode()), eq(mtCityId))).thenReturn(mtList);
    }

    private MiniCodeBO mockMiniCodeBO(String shortCode, int platform, int cityId) {
        MiniCodeBO bo = new MiniCodeBO();
        bo.setBizCode(TechPromoCodeMiniCodeDomainService.BIZ_CODE);
        bo.setBindUserType(MiniCodeBindUserTypeEnum.TECHNICIAN.getCode());
        bo.setBindUserId((long) mockTechId);
        bo.setShortCode(shortCode);
        bo.setPlatform(platform);
        bo.setCityId(cityId);
        bo.setBizId(String.format("%s_%s_%s", bo.getBindUserId(), bo.getPlatform(), bo.getCityId()));
        bo.setRedirectLink(null);
        bo.setStatus(DistributionStatusEnum.VALID.getCode());
        bo.setExtInfo(null);
        bo.setAddTime(Calendar.getInstance().getTime());
        bo.setUpdateTime(Calendar.getInstance().getTime());
        return bo;
    }
}
