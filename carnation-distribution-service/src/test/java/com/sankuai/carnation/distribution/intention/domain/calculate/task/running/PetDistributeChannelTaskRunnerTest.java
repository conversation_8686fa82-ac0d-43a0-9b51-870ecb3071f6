package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertFalse;

/**
 * 测试PetDistributeChannelTaskRunner的isOrderHitTask方法
 */
public class PetDistributeChannelTaskRunnerTest {

    @InjectMocks
    private PetDistributeChannelTaskRunner petDistributeChannelTaskRunner;

    @Mock
    private IntentionCalculateTaskParamBO mockTaskParam;

    @Mock
    private OrderInfoBO mockOrderInfo;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试isOrderHitTask方法，期望返回false
     */
    @Test
    public void testIsOrderHitTask_ExpectFalse() throws Throwable {
        // arrange
        // 由于isOrderHitTask方法的实现直接返回false，不需要设置mock对象的行为

        // act
        boolean result = petDistributeChannelTaskRunner.isOrderHitTask(mockTaskParam, mockOrderInfo);

        // assert
        assertFalse("期望返回false", result);
    }
}
