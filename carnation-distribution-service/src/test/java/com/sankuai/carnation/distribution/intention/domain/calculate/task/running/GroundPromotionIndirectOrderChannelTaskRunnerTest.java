package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.dianping.cat.Cat;
import com.dianping.dz.coupon.base.dto.CouponVerifyRecordDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.tuangou.receipt.record.dto.ReceiptVerifySuccessDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibtp.trade.client.buy.enums.OrderExtraFieldEnum;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLog;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.DistributionOrderReceiptLogRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPartTimeQrLogRepository;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.foodtrade.rebate.qrcode.api.response.ShelfPoiRes;
import com.sankuai.foodtrade.rebate.qrcode.api.service.QRCodeShelfService;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.junit.runner.RunWith;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.OrderChannelResultDomainService;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionIndirectOrderChannelTaskRunnerTest {

    @InjectMocks
    private GroundPromotionIndirectOrderChannelTaskRunner taskRunner;

    @Mock
    private RedisStoreClient redisStoreClient;

    @Mock
    private OrderReceiptAcl orderReceiptAcl;

    @Mock
    private OrderCouponAcl orderCouponAcl;

    @Mock
    private OrderChannelResultDomainService channelResultDomainService;

    @Mock
    private ShopMapperService shopMapperService;

    @Mock
    private QRCodeShelfService qrCodeShelfService;

    @Mock
    private GroundPartTimeQrLogRepository groundPartTimeQrLogRepository;

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private OrderReceiptQueryService orderReceiptQueryService;

    @Mock
    private DistributionOrderReceiptLogRepository distributionOrderReceiptLogRepository;

    private IntentionCalculateTaskParamBO taskParam;

    private OrderInfoBO orderInfoBO;

    private DistributionOrderChannelCalRunningTaskWithBLOBs task;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        taskParam = new IntentionCalculateTaskParamBO();
        orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderId("testOrderId");
        task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        task.setPredictEndTime(new Date(System.currentTimeMillis() + 10000));
        // Default mock for successful verification
        when(orderReceiptAcl.getTotalUsedReceiptNum(anyString())).thenReturn(1);
        when(orderCouponAcl.getTotalUsedCouponNum(anyString(), anyLong())).thenReturn(1);
        // Default mock for successful result
        DistributionOrderChannelCalResult calResult = new DistributionOrderChannelCalResult();
        calResult.setCalResult(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode());
        when(channelResultDomainService.getCalResultByOrder(anyInt(), anyString())).thenReturn(calResult);
    }

    /**
     * Test for timeout scenario
     */
    @Test
    public void testResumeCalculateTimeout() throws Throwable {
        task.setPredictEndTime(new Date(System.currentTimeMillis() - 1000));
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.NOT_DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
    }

    /**
     * Test for receipt verification failure
     */
    @Test
    public void testResumeCalculateReceiptVerificationFailure() throws Throwable {
        orderInfoBO.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        when(orderReceiptAcl.getTotalUsedReceiptNum(orderInfoBO.getOrderId())).thenReturn(0);
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.WAIT_FOR_CONFIRM.getCode(), result.getCalculateResult());
    }

    /**
     * Test for coupon verification failure
     */
    @Test
    public void testResumeCalculateCouponVerificationFailure() throws Throwable {
        orderInfoBO.setProductType(ProductTypeEnum.STANDARD_PRODUCT.getCode());
        when(orderCouponAcl.getTotalUsedCouponNum(orderInfoBO.getOrderId(), orderInfoBO.getUserId())).thenReturn(0);
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.WAIT_FOR_CONFIRM.getCode(), result.getCalculateResult());
    }

    /**
     * Test for successful calculation result retrieval
     */
    @Test
    public void testResumeCalculateSuccessfulResultRetrieval() throws Throwable {
        orderInfoBO.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        when(orderReceiptAcl.getTotalUsedReceiptNum(orderInfoBO.getOrderId())).thenReturn(1);
        DistributionOrderChannelCalResult calResult = new DistributionOrderChannelCalResult();
        calResult.setCalResult(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode());
        when(channelResultDomainService.getCalResultByOrder(anyInt(), anyString())).thenReturn(calResult);
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
    }

    /**
     * Test for missing shop ID and successful retrieval from receipt
     */
    @Test
    public void testResumeCalculateMissingShopIdFromReceipt() throws Throwable {
        orderInfoBO.setShopId(0L);
        orderInfoBO.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        when(orderReceiptAcl.getTotalUsedReceiptNum(orderInfoBO.getOrderId())).thenReturn(1);
        ReceiptVerifySuccessDTO receiptDTO = new ReceiptVerifySuccessDTO();
        receiptDTO.setShopID(123L);
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
    }

    /**
     * Test for missing shop ID and successful retrieval from coupon
     */
    @Test
    public void testResumeCalculateMissingShopIdFromCoupon() throws Throwable {
        orderInfoBO.setShopId(0L);
        orderInfoBO.setProductType(ProductTypeEnum.STANDARD_PRODUCT.getCode());
        when(orderCouponAcl.getTotalUsedCouponNum(orderInfoBO.getOrderId(), orderInfoBO.getUserId())).thenReturn(1);
        CouponVerifyRecordDTO couponDTO = new CouponVerifyRecordDTO();
        couponDTO.setShopId(123L);
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
    }

    /**
     * Test for final result generation
     */
    @Test
    public void testResumeCalculateFinalResultGeneration() throws Throwable {
        orderInfoBO.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);
        assertEquals(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode(), result.getCalculateResult());
    }

    @Test
    public void testResumeCalculateWeak() throws Throwable {
        orderInfoBO.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        when(channelResultDomainService.getCalResultByOrder(anyInt(), anyString())).thenReturn(null);

        orderInfoBO.setShopId(1L);
        orderInfoBO.setPlatform(2);
        when(qrCodeShelfService.queryShelfByPoi(any())).thenReturn(ShelfPoiRes.success(Lists.newArrayList(1L)));
        List<GroundPartTimeQrLog> qrLogList = Lists.newArrayList();
        GroundPartTimeQrLog qrLog = new GroundPartTimeQrLog();
        qrLog.setCode("code");
        qrLog.setScanTime(new Date());
        qrLogList.add(qrLog);
        when(groundPartTimeQrLogRepository.query(any())).thenReturn(qrLogList);

        List<BizDistributorBO> bizDistributorBOList = Lists.newArrayList();
        BizDistributorBO bizDistributorBO = new BizDistributorBO();
        bizDistributorBO.setDistributorCode("code");
        bizDistributorBO.setDistributorId(1L);
        bizDistributorBOList.add(bizDistributorBO);
        when(distributorRootService.getBizDistributorByBizIdAndUserId(anyString(), anyString(), anyInt())).thenReturn(bizDistributorBOList);

        orderInfoBO.setProductId(1L);
        when(orderReceiptQueryService.queryOrderReceipt(anyString(), anyInt())).thenReturn(TechnicianResp.success(new OrderReceiptDto()));

        orderInfoBO.setUserId(1L);
        when(orderReceiptAcl.getTotalReceiptNum(anyString())).thenReturn(1);
        orderInfoBO.setLongOrderId(1L);
        orderInfoBO.setTotalAmount(new BigDecimal(1));
        Map<String, String> extMagicMemberCouponInfo = Maps.newHashMap();
        extMagicMemberCouponInfo.put(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey(), "true");
        orderInfoBO.setExtMagicMemberCouponInfo(extMagicMemberCouponInfo);

        doReturn(1).when(distributionOrderReceiptLogRepository).insert(any(DistributionOrderReceiptLog.class));

        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);

        assertEquals(IntentionTypeEnum.WEAK_INTENTION.getCode(), result.getIntentionType());
    }

    @Test
    public void testResumeCalculateStrong() throws Throwable {
        orderInfoBO.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
        when(channelResultDomainService.getCalResultByOrder(anyInt(), anyString())).thenReturn(null);

        taskParam.setDistributionCode("code");
        orderInfoBO.setShopId(1L);
        orderInfoBO.setPlatform(2);
        BizDistributorBO bizDistributorBO = new BizDistributorBO();
        bizDistributorBO.setUserId("userid");
        bizDistributorBO.setDistributorId(1L);
        when(distributorRootService.getBizDistributor(anyString())).thenReturn(bizDistributorBO);
        List<GroundPartTimeQrLog> qrLogList = Lists.newArrayList();
        GroundPartTimeQrLog qrLog = new GroundPartTimeQrLog();
        qrLog.setCode("code");
        qrLog.setScanTime(new Date());
        qrLogList.add(qrLog);
        when(groundPartTimeQrLogRepository.query(any())).thenReturn(qrLogList);

        orderInfoBO.setProductId(1L);
        when(orderReceiptQueryService.queryOrderReceipt(anyString(), anyInt())).thenReturn(TechnicianResp.success(new OrderReceiptDto()));

        orderInfoBO.setUserId(1L);
        when(orderReceiptAcl.getTotalReceiptNum(anyString())).thenReturn(1);
        orderInfoBO.setLongOrderId(1L);
        orderInfoBO.setTotalAmount(new BigDecimal(1));
        doReturn(1).when(distributionOrderReceiptLogRepository).insert(any(DistributionOrderReceiptLog.class));

        OrderChannelTaskRunningResultBO result = taskRunner.resumeCalculate(taskParam, orderInfoBO, task);

        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), result.getIntentionType());
    }

    @Test
    public void testBizTypeNotPartTime() {
        taskParam.setDistributionCode("dtcode");

        BizDistributorBO distributor = new BizDistributorBO();
        distributor.setBizType(DistributorBizTypeEnum.AUNT_TECHNICIAN.getCode());
        when(distributorRootService.getBizDistributor(anyString())).thenReturn(distributor);

        boolean isOrderHitTask = taskRunner.isOrderHitTask(taskParam, orderInfoBO);

        assertEquals(false, isOrderHitTask);
    }

    @Test
    public void testShopIdNotMatch() {
        taskParam.setDistributionCode("dtcode");

        BizDistributorBO distributor = new BizDistributorBO();
        distributor.setBizType(DistributorBizTypeEnum.AUNT_TECHNICIAN.getCode());
        distributor.setBizId("123");

        orderInfoBO.setShopId(456L);
        orderInfoBO.setPlatform(PlatformEnum.DP.getCode());
        when(distributorRootService.getBizDistributor(anyString())).thenReturn(distributor);

        boolean isOrderHitTask = taskRunner.isOrderHitTask(taskParam, orderInfoBO);

        assertEquals(false, isOrderHitTask);
    }
}
