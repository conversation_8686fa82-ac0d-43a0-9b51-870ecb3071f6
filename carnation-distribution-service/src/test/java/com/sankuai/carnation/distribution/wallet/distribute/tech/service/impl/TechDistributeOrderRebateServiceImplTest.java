package com.sankuai.carnation.distribution.wallet.distribute.tech.service.impl;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.wallet.distribute.tech.dto.DistributionOrderVerifyRebateCommissionInfo;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TechDistributeOrderRebateServiceImplTest {
    @InjectMocks
    private TechDistributeOrderRebateServiceImpl service;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;

    @Before
    public void setUp() throws Exception {
    }

    /**
     * 测试查询返佣信息，当订单ID对应的返佣信息为空时
     */
    @Test
    public void testQueryVerifyRebateCommissionInfoWithEmptyResult() {
        when(rbOrderVerifyRebateCommissionDataService.selectByUnifiedOrderId(anyString())).thenReturn(Lists.newArrayList());

        RemoteResponse<List<DistributionOrderVerifyRebateCommissionInfo>> response = service.queryVerifyRebateCommissionInfo("orderId");

        assertTrue(response.isSuccess());
        assertTrue(CollectionUtils.isEmpty(response.getData()));
    }

    /**
     * 测试查询返佣信息，当订单ID对应的返佣信息不为空时
     */
    @Test
    public void testQueryVerifyRebateCommissionInfoWithNonEmptyResult() {
        RbOrderVerifyRebateCommission commission = new RbOrderVerifyRebateCommission();
        commission.setOrderId("orderId");
        commission.setPredictRebateCent(100L);

        when(rbOrderVerifyRebateCommissionDataService.selectByUnifiedOrderId(anyString())).thenReturn(Arrays.asList(commission));

        RemoteResponse<List<DistributionOrderVerifyRebateCommissionInfo>> response = service.queryVerifyRebateCommissionInfo("orderId");

        assertTrue(response.isSuccess());
        assertFalse(CollectionUtils.isEmpty(response.getData()));
        assertEquals(1, response.getData().size());
        assertEquals(Long.valueOf(100), response.getData().get(0).getPredictRebateCent());
    }

    /**
     * 测试查询返佣信息，当方法执行过程中发生异常时
     */
    @Test
    public void testQueryVerifyRebateCommissionInfoWithException() {
        when(rbOrderVerifyRebateCommissionDataService.selectByUnifiedOrderId(anyString())).thenThrow(new RuntimeException("Mock Exception"));

        RemoteResponse<List<DistributionOrderVerifyRebateCommissionInfo>> response = service.queryVerifyRebateCommissionInfo("orderId");

        assertFalse(response.isSuccess());
        assertNotNull(response.getMsg());
    }
}