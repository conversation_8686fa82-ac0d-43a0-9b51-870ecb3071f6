package com.sankuai.carnation.distribution.privatelive.distribution.domain.model;

import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveBecomeDistributorGroupCmdTest {

    /**
     * 测试 privateLiveWechatAccount 为 null 时，应该返回 true
     */
    @Test
    public void testRequireRegisterAccountWhenAccountIsNull() {
        // arrange
        PrivateLiveBecomeDistributorGroupCmd cmd = new PrivateLiveBecomeDistributorGroupCmd();
        cmd.setPrivateLiveWechatAccount(null);

        // act
        boolean result = cmd.requireRegisterAccount();

        // assert
        assertTrue("当 privateLiveWechatAccount 为 null 时，应该需要注册账户", result);
    }

    /**
     * 测试 privateLiveWechatAccount 不为 null 但 id 为 null 时，应该返回 true
     */
    @Test
    public void testRequireRegisterAccountWhenAccountIdIsNull() {
        // arrange
        PrivateLiveAccountBO account = new PrivateLiveAccountBO();
        account.setId(null);
        PrivateLiveBecomeDistributorGroupCmd cmd = new PrivateLiveBecomeDistributorGroupCmd();
        cmd.setPrivateLiveWechatAccount(account);

        // act
        boolean result = cmd.requireRegisterAccount();

        // assert
        assertTrue("当 privateLiveWechatAccount 不为 null 但 id 为 null 时，应该需要注册账户", result);
    }

    /**
     * 测试 privateLiveWechatAccount 和 id 都不为 null 时，应该返回 false
     */
    @Test
    public void testRequireRegisterAccountWhenAccountAndIdAreNotNull() {
        // arrange
        PrivateLiveAccountBO account = new PrivateLiveAccountBO();
        account.setId(1L);
        PrivateLiveBecomeDistributorGroupCmd cmd = new PrivateLiveBecomeDistributorGroupCmd();
        cmd.setPrivateLiveWechatAccount(account);

        // act
        boolean result = cmd.requireRegisterAccount();

        // assert
        assertFalse("当 privateLiveWechatAccount 和 id 都不为 null 时，不应该需要注册账户", result);
    }
}
