package com.sankuai.carnation.distribution.distributionplan.acl;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.acl.impl.DealProductAclImpl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealProductInfo;
import com.sankuai.general.product.query.center.client.dto.*;
import com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@RunWith(MockitoJUnitRunner.class)
public class DealProductAclImplTest {
    @Mock
    private DealGroupQueryService dealGroupQueryService;

    @InjectMocks
    private DealProductAclImpl dealProductAcl;

    private QueryDealGroupListResponse successResponse;
    private QueryDealGroupListResponse failureResponse;

    @Before
    public void setUp() throws Exception {
        // 创建DealGroupDto并设置所有相关字段
        DealGroupDTO dealGroupDto = new DealGroupDTO();
        dealGroupDto.setMtDealGroupId(123L);

        DealGroupBasicDTO basicInfo = new DealGroupBasicDTO();
        basicInfo.setTitle("测试产品");
        basicInfo.setStatus(DealGroupStatusEnum.VISIBLE_ONLINE.getCode()); // 设置为在线状态
        basicInfo.setTradeType(3);
        basicInfo.setEndSaleDate("2024-10-30 20:30:28");
        dealGroupDto.setBasic(basicInfo);

        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(456L);
        category.setServiceTypeId(789L);
        dealGroupDto.setCategory(category);

        DealGroupDisplayShopDTO displayShopInfo = new DealGroupDisplayShopDTO();
        displayShopInfo.setMtDisplayShopIds(Arrays.asList(101L, 102L));
        dealGroupDto.setDisplayShopInfo(displayShopInfo);

        PriceDTO price = new PriceDTO();
        price.setSalePrice("99.99");
        price.setMarketPrice("199.99");
        dealGroupDto.setPrice(price);

        DealGroupImageDTO image = new DealGroupImageDTO();
        image.setDefaultPicPath("http://example.com/pic.jpg");
        dealGroupDto.setImage(image);

        // 创建QueryDealGroupListResult并添加DealGroupDto
        QueryDealGroupListResult dealGroupListResult = new QueryDealGroupListResult();
        dealGroupListResult.setList(Lists.newArrayList(dealGroupDto));

        successResponse = new QueryDealGroupListResponse();
        successResponse.setCode(ResponseCodeEnum.SUCCESS.getCode());
        successResponse.setData(dealGroupListResult);

        failureResponse = new QueryDealGroupListResponse();
        failureResponse.setCode(ResponseCodeEnum.INVALID_PARAM.getCode());
    }


    /**
     * 测试查询成功的场景
     */
    @Test
    public void testQueryMtDealProductByIdsSuccess() throws Exception {
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(successResponse);

        List<Long> mtProductIdList = Arrays.asList(1L, 2L);
        List<DealProductInfo> result = dealProductAcl.queryMtDealProductByIds(mtProductIdList);

        assertNotNull(result);
        // 由于mock的response没有具体的数据，所以这里只能验证返回非null
    }


    /**
     * 测试空的产品ID列表
     */
    @Test
    public void testQueryMtDealProductByIdsEmptyList() {
        List<DealProductInfo> result = dealProductAcl.queryMtDealProductByIds(Lists.newArrayList());
        assertTrue(result.isEmpty());
    }

    /**
     * 测试查询服务抛出异常的场景
     */
    @Test
    public void testQueryMtDealProductByIdsServiceThrowsException() throws Exception {
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new RuntimeException("Service exception"));

        List<Long> mtProductIdList = Arrays.asList(1L, 2L);
        List<DealProductInfo> result = dealProductAcl.queryMtDealProductByIds(mtProductIdList);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试查询失败的场景
     */
    @Test
    public void testQueryMtDealProductByIdsFailure() throws Exception {
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(failureResponse);

        List<Long> mtProductIdList = Arrays.asList(1L, 2L);
        List<DealProductInfo> result = dealProductAcl.queryMtDealProductByIds(mtProductIdList);

        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryDpDealProductByIds方法，当输入为空列表时
     */
    @Test
    public void testQueryDpDealProductByIdsEmptyInput() {
        // arrange
        List<Long> dpProductIdList = Collections.emptyList();

        // act
        List<DealProductInfo> result = dealProductAcl.queryDpDealProductByIds(dpProductIdList);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryDpDealProductByIds方法，当服务返回成功且有数据时
     */
    @Test
    public void testQueryDpDealProductByIdsSuccessWithData() throws Throwable {
        // arrange
        List<Long> dpProductIdList = Arrays.asList(1L, 2L);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(successResponse);

        // act
        List<DealProductInfo> result = dealProductAcl.queryDpDealProductByIds(dpProductIdList);

        // assert
        assertNotNull(result);
        verify(dealGroupQueryService, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试queryDpDealProductByIds方法，当服务返回失败时
     */
    @Test
    public void testQueryDpDealProductByIdsServiceFailure() throws Throwable {
        // arrange
        List<Long> dpProductIdList = Arrays.asList(1L, 2L);
        QueryDealGroupListResponse response = mock(QueryDealGroupListResponse.class);
        when(response.getCode()).thenReturn(ResponseCodeEnum.INVALID_PARAM.getCode());
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);

        // act
        List<DealProductInfo> result = dealProductAcl.queryDpDealProductByIds(dpProductIdList);

        // assert
        assertTrue(result.isEmpty());
        verify(dealGroupQueryService, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试queryDpDealProductByIds方法，当服务抛出异常时
     */
    @Test
    public void testQueryDpDealProductByIdsThrowsException() throws Throwable {
        // arrange
        List<Long> dpProductIdList = Arrays.asList(1L, 2L);
        when(dealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new RuntimeException());

        // act
        List<DealProductInfo> result = dealProductAcl.queryDpDealProductByIds(dpProductIdList);

        // assert
        assertTrue(result.isEmpty());
        verify(dealGroupQueryService, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试queryMtDealProductToMap方法，当输入列表为空时
     */
    @Test
    public void testQueryMtDealProductToMapEmptyInput() {
        // arrange
        List<Long> mtProductIdList = Collections.emptyList();

        // act
        Map<Long, DealProductInfo> result = dealProductAcl.queryMtDealProductToMap(mtProductIdList);

        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试queryMtDealProductToMap方法，当服务返回成功且有数据时
     */
    @Test
    public void testQueryMtDealProductToMapSuccessWithData() throws Exception {
        // arrange
        List<Long> mtProductIdList = Arrays.asList(1L, 2L);
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(successResponse);

        // act
        Map<Long, DealProductInfo> result = dealProductAcl.queryMtDealProductToMap(mtProductIdList);

        // assert
        assertNotNull("结果不应为空", result);
        verify(dealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }

    /**
     * 测试queryMtDealProductToMap方法，当服务返回失败时
     */
    @Test
    public void testQueryMtDealProductToMapServiceFailure() throws Exception {
        // arrange
        List<Long> mtProductIdList = Arrays.asList(1L, 2L);
        QueryDealGroupListResponse response = mock(QueryDealGroupListResponse.class);
        when(response.getCode()).thenReturn(ResponseCodeEnum.INVALID_PARAM.getCode());
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);

        // act
        Map<Long, DealProductInfo> result = dealProductAcl.queryMtDealProductToMap(mtProductIdList);

        // assert
        assertTrue("结果应为空", result.isEmpty());
        verify(dealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }

    /**
     * 测试queryMtDealProductToMap方法，当服务抛出异常时
     */
    @Test
    public void testQueryMtDealProductToMapServiceThrowsException() throws Exception {
        // arrange
        List<Long> mtProductIdList = Arrays.asList(1L, 2L);
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenThrow(new RuntimeException());

        // act
        Map<Long, DealProductInfo> result = dealProductAcl.queryMtDealProductToMap(mtProductIdList);

        // assert
        assertTrue("结果应为空", result.isEmpty());
        verify(dealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }
}