package com.sankuai.carnation.distribution.distributor.assembler;

import static org.junit.Assert.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBindBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.domain.enums.DistributorAttrEnum;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.vo.ApplyDistributorVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorGroupVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorVO;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.MobileHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.MobileHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.utils.NullableUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Before;
import org.junit.Before;
import org.junit.Test;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ObjectUtils;

public class DistributorVOAssemblerTest {

    @InjectMocks
    private DistributorVOAssembler distributorVOAssembler;

    @Mock
    private DistributorGroupVOAssembler distributorGroupVOAssembler;

    @Mock
    private MobileHelper mobileHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 buildDistributorModel 方法，当传入的 DistributorBO 为 null 时
     */
    @Test
    public void testBuildDistributorModelWhenDistributorBOIsNull() {
        // arrange
        DistributorBO distributorBO = null;
        // act
        DistributorModel result = distributorVOAssembler.buildDistributorModel(distributorBO);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildDistributorModel 方法，当传入的 DistributorBO 不为 null 且包含完整信息时
     */
    @Test
    public void testBuildDistributorModelWhenDistributorBOIsNotNull() {
        // arrange
        DistributorBO distributorBO = mock(DistributorBO.class);
        DistributorGroupBO distributorGroupBO = mock(DistributorGroupBO.class);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        when(distributorBO.getDistributorGroup()).thenReturn(distributorGroupBO);
        when(distributorGroupVOAssembler.buildDistributorGroupModel(distributorGroupBO)).thenReturn(distributorGroupModel);
        when(distributorBO.getDistributorAttrValue(DistributorAttrEnum.NICK_NAME.getAttrName())).thenReturn("NickName");
        // act
        DistributorModel result = distributorVOAssembler.buildDistributorModel(distributorBO);
        // assert
        assertNotNull(result);
        assertEquals("NickName", result.getNickname());
        verify(distributorGroupVOAssembler).buildDistributorGroupModel(distributorGroupBO);
    }

    /**
     * 测试 buildDistributorModel 方法，当传入的 DistributorBO 不为 null 但 DistributorGroupBO 为 null 时
     */
    @Test
    public void testBuildDistributorModelWhenDistributorGroupBOIsNull() {
        // arrange
        DistributorBO distributorBO = mock(DistributorBO.class);
        when(distributorBO.getDistributorGroup()).thenReturn(null);
        // act
        DistributorModel result = distributorVOAssembler.buildDistributorModel(distributorBO);
        // assert
        assertNotNull(result);
        assertNull(result.getDistributorGroupModel());
    }

    /**
     * 测试buildDistributorBindModel方法，当传入的distributor为null时
     */
    @Test
    public void testBuildDistributorBindModelWithNullDistributor() {
        // arrange
        DistributorBindBO distributor = null;
        // act
        DistributorBindModel result = distributorVOAssembler.buildDistributorBindModel(distributor);
        // assert
        assertNull("结果应为null", result);
    }



    /**
     * 测试buildDistributorBindModel方法，当传入的DistributorBindBO为null时
     */
    @Test
    public void testBuildDistributorBindModelWithNullDistributorBindBO() {
        // arrange
        DistributorVOAssembler distributorVOAssembler = new DistributorVOAssembler();
        // act
        DistributorBindModel result = distributorVOAssembler.buildDistributorBindModel(null);
        // assert
        assertNull("当传入的DistributorBindBO为null时，结果应为null", result);
    }




    /**
     * 测试当传入的DistributorBO不为null，且包含完整信息时，能正确构建DistributorVO
     */
    @Test
    public void testBuildDistributorVOWithFullInfo() {
        // arrange
        DistributorBO distributor = new DistributorBO();
        distributor.setUserId(1L);
        // Simplified for the example
        distributor.setDistributorAttrList(null);
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributor.setDistributorGroup(distributorGroupBO);
        DistributorGroupVO distributorGroupVO = new DistributorGroupVO();
        when(distributorGroupVOAssembler.buildDistributorVO(any(DistributorGroupBO.class))).thenReturn(distributorGroupVO);
        when(mobileHelper.getMobileMask(anyString())).thenReturn("maskedMobile");
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributor);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getAccountId());
        assertEquals(distributorGroupVO, result.getDistributorGroup());
    }

    /**
     * 测试当传入的DistributorBO不为null，但DistributorGroupBO为null时，DistributorVO中的DistributorGroupVO也应为null
     */
    @Test
    public void testBuildDistributorVOWithNullDistributorGroup() {
        // arrange
        DistributorBO distributor = new DistributorBO();
        distributor.setUserId(1L);
        distributor.setDistributorGroup(null);
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributor);
        // assert
        assertNotNull(result);
        assertNull(result.getDistributorGroup());
    }

    /**
     * 测试buildDistributorVO方法，当传入的DistributorModel为null时
     */
    @Test
    public void testBuildDistributorVONullDistributor() {
        // arrange
        DistributorModel distributor = null;
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributor);
        // assert
        assertNull(result);
    }

    /**
     * 测试buildDistributorVO方法，正常情况
     */
    @Test
    public void testBuildDistributorVOSuccess() {
        // arrange
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setAccountId(1L);
        distributorModel.setDistributorId(2L);
        distributorModel.setGroupId("G1");
        distributorModel.setNickname("Nick");
        distributorModel.setAvatarUrl("http://avatar.url");
        distributorModel.setShareName("ShareName");
        distributorModel.setPhoneNumber("*********");
        distributorModel.setWechatNumber("wechat123");
        distributorModel.setStatus(1);
        distributorModel.setActualName("ActualName");
        distributorModel.setDistributorGroupName("GroupName");
        when(mobileHelper.getMobileMask("*********")).thenReturn("maskedNumber");
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributorModel);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getAccountId());
        assertEquals(Long.valueOf(2), result.getDistributorId());
        assertEquals("G1", result.getGroupId());
        assertEquals("Nick", result.getNickname());
        assertEquals("http://avatar.url", result.getAvatarUrl());
        assertEquals("ShareName", result.getShareName());
        assertEquals("maskedNumber", result.getMobile());
        assertEquals("wechat123", result.getWechatNumber());
        assertEquals(Integer.valueOf(1), result.getStatus());
        assertEquals("ActualName", result.getActualName());
        assertNull(result.getDistributorGroup());
        verify(mobileHelper, times(1)).getMobileMask("*********");
    }

    /**
     * 测试buildDistributorVO方法，当传入的DistributorModel为null时
     */
    @Test
    public void testBuildDistributorVONullDistributorModel() {
        // arrange
        DistributorModel distributorModel = null;
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributorModel);
        // assert
        assertNull(result);
    }

    /**
     * 测试buildDistributorVO方法，当传入的DistributorModel非null且phoneNumber为null时
     */
    @Test
    public void testBuildDistributorVONonNullDistributorModelWithNullPhoneNumber() {
        // arrange
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setAccountId(1L);
        distributorModel.setDistributorId(2L);
        distributorModel.setGroupId("G1");
        distributorModel.setNickname("Nick");
        distributorModel.setAvatarUrl("http://avatar.url");
        distributorModel.setShareName("ShareName");
        // phoneNumber为null
        distributorModel.setPhoneNumber(null);
        distributorModel.setWechatNumber("wechat123");
        distributorModel.setStatus(1);
        distributorModel.setActualName("ActualName");
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributorModel);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getAccountId());
        assertEquals(Long.valueOf(2), result.getDistributorId());
        assertEquals("G1", result.getGroupId());
        assertEquals("Nick", result.getNickname());
        assertEquals("http://avatar.url", result.getAvatarUrl());
        assertEquals("ShareName", result.getShareName());
        // 验证phoneNumber为null时，mobile也应为null
        assertNull(result.getMobile());
        assertEquals("wechat123", result.getWechatNumber());
        assertEquals(Integer.valueOf(1), result.getStatus());
        assertEquals("ActualName", result.getActualName());
        assertNull(result.getDistributorGroup());
    }

    /**
     * 测试buildDistributorVO方法，当传入的DistributorModel非null且phoneNumber非null时
     */
    @Test
    public void testBuildDistributorVONonNullDistributorModelWithNonNullPhoneNumber() {
        // arrange
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setAccountId(1L);
        distributorModel.setDistributorId(2L);
        distributorModel.setGroupId("G1");
        distributorModel.setNickname("Nick");
        distributorModel.setAvatarUrl("http://avatar.url");
        distributorModel.setShareName("ShareName");
        // phoneNumber非null
        distributorModel.setPhoneNumber("*********");
        distributorModel.setWechatNumber("wechat123");
        distributorModel.setStatus(1);
        distributorModel.setActualName("ActualName");
        when(mobileHelper.getMobileMask("*********")).thenReturn("maskedNumber");
        // act
        DistributorVO result = distributorVOAssembler.buildDistributorVO(distributorModel);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getAccountId());
        assertEquals(Long.valueOf(2), result.getDistributorId());
        assertEquals("G1", result.getGroupId());
        assertEquals("Nick", result.getNickname());
        assertEquals("http://avatar.url", result.getAvatarUrl());
        assertEquals("ShareName", result.getShareName());
        // 验证phoneNumber非null时，mobile被正确处理
        assertEquals("maskedNumber", result.getMobile());
        assertEquals("wechat123", result.getWechatNumber());
        assertEquals(Integer.valueOf(1), result.getStatus());
        assertEquals("ActualName", result.getActualName());
        assertNull(result.getDistributorGroup());
        verify(mobileHelper, times(1)).getMobileMask("*********");
    }

    /**
     * 测试toDistributorVOList方法，当输入列表为空时
     */
    @Test
    public void testToDistributorVOListWhenListIsEmpty() {
        // arrange
        List<DistributorModel> distributorModelList = Lists.newArrayList();
        // act
        List<DistributorVO> result = distributorVOAssembler.toDistributorVOList(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试toDistributorVOList方法，当输入列表为null时
     */
    @Test
    public void testToDistributorVOListWhenListIsNull() {
        // arrange
        List<DistributorModel> distributorModelList = null;
        // act
        List<DistributorVO> result = distributorVOAssembler.toDistributorVOList(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }


    /**
     * 测试toApplyDistributorVOList方法，当传入的distributorModelList为空时，应返回空列表
     */
    @Test
    public void testToApplyDistributorVOListWithEmptyList() {
        // arrange
        List<DistributorBindModel> distributorModelList = new ArrayList<>();
        // act
        List<ApplyDistributorVO> result = distributorVOAssembler.toApplyDistributorVOList(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }



    /**
     * 测试toApplyDistributorVOList方法，当传入的distributorModelList包含null元素时，应过滤掉null并返回非空列表
     */
    @Test
    public void testToApplyDistributorVOListWithNullElements() {
        // arrange
        List<DistributorBindModel> distributorModelList = new ArrayList<>();
        distributorModelList.add(null);
        // act
        List<ApplyDistributorVO> result = distributorVOAssembler.toApplyDistributorVOList(distributorModelList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 buildApplyDistributorVO 方法，当 distributorBindModel 为 null 时
     */
    @Test
    public void testBuildApplyDistributorVO_WithNullDistributorBindModel() {
        // arrange
        DistributorBindModel distributorBindModel = null;
        // act
        ApplyDistributorVO result = distributorVOAssembler.buildApplyDistributorVO(distributorBindModel);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildApplyDistributorVO 方法，当 distributorBindModel 不为 null 但 distributorGroupModel 为 null 时
     */
    @Test
    public void testBuildApplyDistributorVO_WithNonNullDistributorBindModelAndNullDistributorGroupModel() {
        // arrange
        DistributorBindModel distributorBindModel = new DistributorBindModel();
        distributorBindModel.setDistributorModel(new DistributorModel());
        when(mobileHelper.getMobileMask(anyString())).thenReturn("maskedMobile");
        // act
        ApplyDistributorVO result = distributorVOAssembler.buildApplyDistributorVO(distributorBindModel);
        // assert
        assertNotNull(result);
        assertEquals(DistributionApproveStatusEnum.UN_APPLY.getCode(), result.getStatus());
    }


}
