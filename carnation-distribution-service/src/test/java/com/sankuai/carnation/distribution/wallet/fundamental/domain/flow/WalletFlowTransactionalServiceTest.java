package com.sankuai.carnation.distribution.wallet.fundamental.domain.flow;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.wallet.fundamental.acl.salary.SalarySettleAclService;
import com.sankuai.carnation.distribution.wallet.fundamental.acl.salary.bo.SalarySettleResultBO;
import com.sankuai.carnation.distribution.wallet.fundamental.acl.salary.enums.SalaryPayResultEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.bean.generator.WalletAccountTestBeanGenerator;
import com.sankuai.carnation.distribution.wallet.fundamental.bean.generator.WalletAmountChangeResultTestBeanGenerator;
import com.sankuai.carnation.distribution.wallet.fundamental.bean.generator.WalletDataItemTestBeanGenerator;
import com.sankuai.carnation.distribution.wallet.fundamental.bean.generator.WalletTestConstants;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountAmountChangeResultBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountBO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletDataItemStatusEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateTypeEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.exceptions.WalletFlowException;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletActivityAccountDataService;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletDataItemDataService;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletFlowItemDataService;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletOperateFlowDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/5
 **/
@RunWith(MockitoJUnitRunner.class)
public class WalletFlowTransactionalServiceTest {

    @Mock
    private WalletDataItemDataService itemDataService;

    @Mock
    private WalletOperateFlowDataService flowDataService;

    @Mock
    private WalletFlowItemDataService flowItemDataService;

    @Mock
    private WalletActivityAccountDataService activityAccountDataService;

    @Mock
    private SalarySettleAclService salarySettleAclService;

    @InjectMocks
    private WalletFlowTransactionalService transactionalService;

    @Test(expected = WalletFlowException.class)
    public void testStartWithdrawFlow_EmptyDataItemList() {
        transactionalService.startWithdrawFlow(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L), Lists.newArrayList(), WalletTestConstants.mockOperator, 10L);
    }

    /**
     * 测试提款流程开始时，账户信息为空的情况
     */
    @Test(expected = WalletFlowException.class)
    public void testStartWithdrawFlow_NullAccountBO() {
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L);
        WalletDataItem dataItem = WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION);
        account.setWalletAccountBO(null);
        transactionalService.startWithdrawFlow(account, Lists.newArrayList(dataItem), WalletTestConstants.mockOperator, 10L);
    }

    /**
     * 测试提款流程开始时，账户支付策略未设置的情况
     */
    @Test(expected = WalletFlowException.class)
    public void testStartWithdrawFlow_NullPayStrategy() {
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L);
        WalletDataItem dataItem = WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION);
        account.getPayStrategy().setMtUserId(null);
        transactionalService.startWithdrawFlow(account, Lists.newArrayList(dataItem), WalletTestConstants.mockOperator, 10L);
    }

    /**
     * 测试提款流程开始时，正常情况
     */
    @Test
    public void testStartWithdrawFlow_Normal() {
        long mockFlowId = 1L;
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L);
        List<WalletDataItem> itemList = Lists.newArrayList();
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(3L, 10L, WalletDataItemStatusEnum.WAIT_FOR_GET_BACK, WalletOperateTypeEnum.ADDITION));
        when(itemDataService.batchUpdateItemStatus(anyList(), anyInt(), anyInt(), any())).thenReturn(1);
        when(flowDataService.addFlow(any())).thenReturn(mockFlowId);
        when(flowItemDataService.batchInsertItemFlow(anyLong(), any())).thenReturn(2L);
        when(flowDataService.processing(anyLong(), any())).thenReturn(true);

        // act
        long flowId = transactionalService.startWithdrawFlow(account, itemList, WalletTestConstants.mockOperator, 10L);

        assertEquals(flowId, mockFlowId);
    }

    /**
     * 测试提款流程开始时，更新数据项数量与预期不符
     */
    @Test(expected = WalletFlowException.class)
    public void testStartWithdrawFlow_ItemUpdateNotMatch() {
        long mockFlowId = 1L;
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L);
        List<WalletDataItem> itemList = Lists.newArrayList();
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(2L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(3L, 10L, WalletDataItemStatusEnum.WAIT_FOR_GET_BACK, WalletOperateTypeEnum.ADDITION));
        when(itemDataService.batchUpdateItemStatus(anyList(), anyInt(), anyInt(), any())).thenReturn(1);

        // act
        long flowId = transactionalService.startWithdrawFlow(account, itemList, WalletTestConstants.mockOperator, 10L);
    }

    /**
     * 测试提款流程开始时，插入流程-数据项数量与预期不符
     */
    @Test(expected = WalletFlowException.class)
    public void testStartWithdrawFlow_FlowItemCountNotMatch() {
        long mockFlowId = 1L;
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L);
        List<WalletDataItem> itemList = Lists.newArrayList();
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(3L, 10L, WalletDataItemStatusEnum.WAIT_FOR_GET_BACK, WalletOperateTypeEnum.ADDITION));
        when(itemDataService.batchUpdateItemStatus(anyList(), anyInt(), anyInt(), any())).thenReturn(1);
        when(flowDataService.addFlow(any())).thenReturn(mockFlowId);
        when(flowItemDataService.batchInsertItemFlow(anyLong(), any())).thenReturn(3L);

        // act
        long flowId = transactionalService.startWithdrawFlow(account, itemList, WalletTestConstants.mockOperator, 10L);
    }

    /**
     * 测试对私提现，数据项更新量不匹配
     */
    @Test(expected = WalletFlowException.class)
    public void testWithdrawPrivacyMoney_ItemUpdateCountNotMatch() {
        long mockFlowId = 1L;
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(20L);
        WalletActivityAccountAmountChangeResultBO mockChangeResult = WalletAmountChangeResultTestBeanGenerator.generateChangeResult(true, 20L, 0L, WalletOperateTypeEnum.SUBTRACT, 20L, null);
        List<WalletDataItem> itemList = Lists.newArrayList();
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(2L, 20L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(3L, 10L, WalletDataItemStatusEnum.WAIT_FOR_GET_BACK, WalletOperateTypeEnum.ADDITION));
        when(activityAccountDataService.changeMoney(anyLong(), anyInt(), anyLong())).thenReturn(mockChangeResult);
        when(itemDataService.batchUpdateItemStatus(anyList(), anyInt(), anyInt(), any())).thenReturn(1);

        // act
        WalletActivityAccountAmountChangeResultBO changeResult = transactionalService.withdrawPrivacyMoney(mockFlowId, WalletTestConstants.mockPayer, account, itemList, 10L);
    }

    /**
     * 测试对私提现，打款失败
     */
    @Test
    public void testWithdrawPrivacyMoney_SettleFail() {
        long mockFlowId = 1L;
        String settleFailMsg = "打款异常";
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(20L);
        WalletActivityAccountAmountChangeResultBO mockChangeResult = WalletAmountChangeResultTestBeanGenerator.generateChangeResult(true, 20L, 0L, WalletOperateTypeEnum.SUBTRACT, 20L, null);
        List<WalletDataItem> itemList = Lists.newArrayList();
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 30L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(3L, 10L, WalletDataItemStatusEnum.WAIT_FOR_GET_BACK, WalletOperateTypeEnum.ADDITION));
        when(activityAccountDataService.changeMoney(anyLong(), anyInt(), anyLong())).thenReturn(mockChangeResult);
        when(itemDataService.batchUpdateItemStatus(anyList(), anyInt(), anyInt(), any())).thenReturn(1);
        when(salarySettleAclService.settle(anyString(), anyLong(), anyString(), anyLong(), anyString())).thenReturn(mockSettleResult(SalaryPayResultEnum.FAIL, settleFailMsg));
        when(flowDataService.fail(anyLong(), any(), any())).thenReturn(true);

        // act
        WalletActivityAccountAmountChangeResultBO changeResult = transactionalService.withdrawPrivacyMoney(mockFlowId, WalletTestConstants.mockPayer, account, itemList, 10L);
        assertWalletActivityAccountAmountChangeResultFail(changeResult, settleFailMsg);
    }

    /**
     * 测试对私提现，成功
     */
    @Test
    public void testWithdrawPrivacyMoney_Success() {
        long mockFlowId = 1L;
        WalletActivityAccountBO account = WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(20L);
        WalletActivityAccountAmountChangeResultBO mockChangeResult = WalletAmountChangeResultTestBeanGenerator.generateChangeResult(true, 20L, 0L, WalletOperateTypeEnum.SUBTRACT, 20L, null);
        List<WalletDataItem> itemList = Lists.newArrayList();
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 30L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
        itemList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(3L, 10L, WalletDataItemStatusEnum.WAIT_FOR_GET_BACK, WalletOperateTypeEnum.ADDITION));
        when(activityAccountDataService.changeMoney(anyLong(), anyInt(), anyLong())).thenReturn(mockChangeResult);
        when(itemDataService.batchUpdateItemStatus(anyList(), anyInt(), anyInt(), any())).thenReturn(1);
        when(salarySettleAclService.settle(anyString(), anyLong(), anyString(), anyLong(), anyString())).thenReturn(mockSettleResult(SalaryPayResultEnum.SUCCESS, null));
        when(flowDataService.success(anyLong(), anyLong(), anyLong(), anyString(), any())).thenReturn(true);

        // act
        WalletActivityAccountAmountChangeResultBO changeResult = transactionalService.withdrawPrivacyMoney(mockFlowId, WalletTestConstants.mockPayer, account, itemList, 10L);
        assertWalletActivityAccountAmountChangeResult(changeResult, 20L);
    }

    private SalarySettleResultBO mockSettleResult(SalaryPayResultEnum payResult, String message) {
        SalarySettleResultBO salarySettleResultBO = new SalarySettleResultBO();
        salarySettleResultBO.setResult(payResult.getCode());
        salarySettleResultBO.setMessage(message);
        return salarySettleResultBO;
    }

    private void assertWalletActivityAccountAmountChangeResult(WalletActivityAccountAmountChangeResultBO result, long amount) {
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(amount, result.getChangeAmount());
    }

    private void assertWalletActivityAccountAmountChangeResultFail(WalletActivityAccountAmountChangeResultBO result, String message) {
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(message, result.getFailReason());
    }
}
