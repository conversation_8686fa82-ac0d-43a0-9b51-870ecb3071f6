package com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.beauty.arch.fundamental.threadpool.ResizableCapacityThreadPoolExecutorFactory;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAnchorAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAnchorAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.constant.CacheCategoryConstant;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveTaskBaseInfoDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PrivateLiveConsultantBaseRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.ConsultantTaskSummaryDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.RankConditionDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.RankDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.LiveStatusMapEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.TaskDataDetailModuleEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.ConsultantUserInvalidException;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.WeChatUserInvalidException;
import com.sankuai.carnation.distribution.privatelive.consultant.gateway.PrivateLiveTaskForCGatewayService;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserTypeCount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import com.sankuai.dz.srcm.pchat.service.ScrmLiveManageService;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTagStatDTO;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTagStatRequest;
import com.sankuai.dz.srcm.user.dto.PrivateSphereCustomerCapitalOverview;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomsQueryRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.live.LiveStatusEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.live.LiveStatusEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.saasconfig.TraceAbilityTypeEnum;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginCustomerInfo;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginUserInfo;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginUserInfo;
import java.lang.reflect.Method;
import java.util.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

public class PrivateLiveTaskForCGatewayServiceImplTest {

    @Mock
    private WeChatUserService weChatUserService;

    @Mock
    private RedisStoreClient redisStoreClient;

    @Mock
    private PrivateLiveAnchorAclService privateLiveAnchorAclService;

    @Mock
    private PrivateLiveAclService privateLiveAclService;

    @InjectMocks
    private PrivateLiveTaskForCGatewayServiceImpl service;

    @Mock
    private ScrmLiveManageService scrmLiveManageService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private int invokePrivateMethod(String methodName, String liveId) throws Exception {
        Method method = PrivateLiveTaskForCGatewayServiceImpl.class.getDeclaredMethod(methodName, String.class);
        method.setAccessible(true);
        return (int) method.invoke(service, liveId);
    }

    /**
     * 测试queryBaseInfo方法，当请求参数为null时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryBaseInfoRequestIsNull() {
        service.queryBaseInfo(null, "wxToken");
    }

    /**
     * 测试queryBaseInfo方法，当wxToken为空时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryBaseInfoWxTokenIsEmpty() {
        PrivateLiveConsultantBaseRequest request = new PrivateLiveConsultantBaseRequest();
        request.setAnchorId(1L);
        service.queryBaseInfo(request, "");
    }

    /**
     * 测试queryBaseInfo方法，当通过wxToken无法获取用户信息时返回失败
     */
    @Test
    public void testQueryBaseInfoWeChatUserInfoIsNull() {
        PrivateLiveConsultantBaseRequest request = new PrivateLiveConsultantBaseRequest();
        request.setAnchorId(1L);
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(null);
        RemoteResponse<PrivateLiveTaskBaseInfoDTO> response = service.queryBaseInfo(request, "wxToken");
        verify(weChatUserService).getWeChatUserInfoByToken("wxToken");
        assert "获取用户信息失败".equals(response.getMsg());
    }

    @Test
    public void testQueryPrivateLiveWxTypeLiveIdIsNull() throws Throwable {
        int result = invokePrivateMethod("queryPrivateLiveWxType", null);
        assertEquals(1, result);
    }

    @Test
    public void testQueryPrivateLiveWxTypeResponseIsNull() throws Throwable {
        when(scrmLiveManageService.queryLiveWxType(anyString())).thenReturn(null);
        int result = invokePrivateMethod("queryPrivateLiveWxType", "test");
        assertEquals(1, result);
    }

    @Test
    public void testQueryPrivateLiveWxTypeResponseIsFailure() throws Throwable {
        RemoteResponse<Integer> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(false);
        when(scrmLiveManageService.queryLiveWxType(anyString())).thenReturn(response);
        int result = invokePrivateMethod("queryPrivateLiveWxType", "test");
        assertEquals(1, result);
    }

    @Test
    public void testQueryPrivateLiveWxTypeResponseDataIsNull() throws Throwable {
        RemoteResponse<Integer> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(null);
        when(scrmLiveManageService.queryLiveWxType(anyString())).thenReturn(response);
        int result = invokePrivateMethod("queryPrivateLiveWxType", "test");
        assertEquals(1, result);
    }

    @Test
    public void testQueryPrivateLiveWxTypeResponseDataIsNotNull() throws Throwable {
        RemoteResponse<Integer> response = mock(RemoteResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(2);
        when(scrmLiveManageService.queryLiveWxType(anyString())).thenReturn(response);
        int result = invokePrivateMethod("queryPrivateLiveWxType", "test");
        assertEquals(2, result);
    }

    @Test
    public void testQueryPrivateLiveWxTypeExceptionOccurred() throws Throwable {
        when(scrmLiveManageService.queryLiveWxType(anyString())).thenThrow(new RuntimeException());
        int result = invokePrivateMethod("queryPrivateLiveWxType", "test");
        assertEquals(1, result);
    }
}
