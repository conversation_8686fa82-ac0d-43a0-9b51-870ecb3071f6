package com.sankuai.carnation.distribution.distributor.startar.service;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.appication.DistributorAppService;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.distributor.assembler.DistributorVOAssembler;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.dto.DistributorInfoDTO;
import com.sankuai.carnation.distribution.distributor.dto.PrivateLiveConsultantDTO;
import com.sankuai.carnation.distribution.distributor.dto.request.DistributorApplyHandleRequest;
import com.sankuai.carnation.distribution.distributor.dto.request.PageQueryDistributorRequest;
import com.sankuai.carnation.distribution.distributor.dto.request.PrivateLiveConsultantRequest;
import com.sankuai.carnation.distribution.distributor.dto.request.QueryDistributorRequest;
import com.sankuai.carnation.distribution.distributor.dto.response.ConsultantDistributorResponse;
import com.sankuai.carnation.distribution.distributor.dto.response.DistributorApplyHandleResponse;
import com.sankuai.carnation.distribution.distributor.dto.response.DistributorGroupResponse;
import com.sankuai.carnation.distribution.distributor.dto.response.PageQueryDistributorResponse;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind;
import com.sankuai.carnation.distribution.distributor.vo.ApplyDistributorVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorVO;
import com.sankuai.carnation.distribution.privatelive.account.application.service.PrivateLiveWechatAccountAppService;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveWechatAccountRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ManageOperateTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.PrivateLiveAccountTypeEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.MobileHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试DistributorInfoServiceImpl
 */
public class DistributorInfoServiceImplTest {

    @InjectMocks
    private DistributorInfoServiceImpl distributorInfoServiceImpl;

    @Mock
    private MobileHelper mobileHelper;

    @Mock
    private DistributorAppService distributorAppService;

    @Mock
    private DistributorVOAssembler distributorVOAssembler;

    @Mock
    private PrivateLiveWechatAccountAppService privateLiveWechatAccountAppService;

    @InjectMocks
    private DistributorInfoServiceImpl distributorInfoService;

    @Mock
    private DistributorGroupAppService distributorGroupAppService;

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private UserAuthorizeUtil userAuthorizeUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试pageQueryDistributor方法，正常情况
     */
    @Test
    public void testPageQueryDistributorSuccess() throws Exception {
        // arrange
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 1, 10, true, "wxToken");
        PageDataDTO<DistributorBindModel> pageDataDTO = new PageDataDTO<>(PageInfoDTO.builder().currentPageNum(1).pageSize(10).totalCount(1).totalPageCount(1).build(), Collections.singletonList(new DistributorBindModel()));
        when(distributorAppService.pageQueryDistributor(any(PageQueryDistributorRequest.class))).thenReturn(pageDataDTO);
        // act
        RemoteResponse<PageQueryDistributorResponse> result = distributorInfoServiceImpl.pageQueryDistributor(request);
        // assert
        assertEquals(Integer.valueOf(1), result.getData().getTotalNum());
        //        verify(distributorAppService, times(1)).pageQueryDistributor(any(PageQueryDistributorRequest.class));
    }

    /**
     * 测试pageQueryDistributor方法，请求参数为null
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorRequestNull() throws Exception {
        // act
        distributorInfoServiceImpl.pageQueryDistributor(null);
    }

    /**
     * 测试pageQueryDistributor方法，页码小于等于0
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorPageNumInvalid() throws Exception {
        // arrange
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 0, 10, true, "wxToken");
        // act
        distributorInfoServiceImpl.pageQueryDistributor(request);
    }

    /**
     * 测试pageQueryDistributor方法，每页数量小于等于0或大于20
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorPageSizeInvalid() throws Exception {
        // arrange
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(1L, 1, 1, 21, true, "wxToken");
        // act
        distributorInfoServiceImpl.pageQueryDistributor(request);
    }

    /**
     * 测试pageQueryDistributor方法，分销商(团长)ID为null
     */
    @Test(expected = IllegalArgumentException.class)
    public void testPageQueryDistributorDistributorGroupIdNull() throws Exception {
        // arrange
        PageQueryDistributorRequest request = new PageQueryDistributorRequest(null, 1, 1, 10, true, "wxToken");
        // act
        distributorInfoServiceImpl.pageQueryDistributor(request);
    }

    /**
     * 测试queryAllByDistributorGroupId方法，当传入有效的distributorGroupId时，应返回非空的ApplyDistributorVO列表
     */
    @Test
    public void testQueryAllByDistributorGroupIdWithValidId() {
        // arrange
        Long distributorGroupId = 1L;
        List<ApplyDistributorVO> mockVOList = new ArrayList<>();
        mockVOList.add(new ApplyDistributorVO());
        when(distributorAppService.queryAllByDistributorGroupId(distributorGroupId)).thenReturn(new ArrayList<>());
        when(distributorVOAssembler.toApplyDistributorVOList(anyList())).thenReturn(mockVOList);
        // act
        RemoteResponse<List<ApplyDistributorVO>> response = distributorInfoService.queryAllByDistributorGroupId(distributorGroupId);
        // assert
        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertFalse(response.getData().isEmpty());
        verify(distributorAppService, times(1)).queryAllByDistributorGroupId(distributorGroupId);
        verify(distributorVOAssembler, times(1)).toApplyDistributorVOList(anyList());
    }

    /**
     * 测试queryAllByDistributorGroupId方法，当传入的distributorGroupId为null时，应抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryAllByDistributorGroupIdWithNullId() {
        // arrange
        Long distributorGroupId = null;
        // act
        distributorInfoService.queryAllByDistributorGroupId(distributorGroupId);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryAllByDistributorGroupId方法，当distributorAppService返回空列表时，应返回空的ApplyDistributorVO列表
     */
    @Test
    public void testQueryAllByDistributorGroupIdWithEmptyResult() {
        // arrange
        Long distributorGroupId = 1L;
        when(distributorAppService.queryAllByDistributorGroupId(distributorGroupId)).thenReturn(new ArrayList<>());
        when(distributorVOAssembler.toApplyDistributorVOList(anyList())).thenReturn(new ArrayList<>());
        // act
        RemoteResponse<List<ApplyDistributorVO>> response = distributorInfoService.queryAllByDistributorGroupId(distributorGroupId);
        // assert
        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertTrue(response.getData().isEmpty());
        verify(distributorAppService, times(1)).queryAllByDistributorGroupId(distributorGroupId);
        verify(distributorVOAssembler, times(1)).toApplyDistributorVOList(anyList());
    }

    /**
     * 测试queryAllByDistributorGroupId方法，当distributorAppService抛出异常时，应正确处理异常
     */
    @Test(expected = RuntimeException.class)
    public void testQueryAllByDistributorGroupIdWithException() {
        // arrange
        Long distributorGroupId = 1L;
        when(distributorAppService.queryAllByDistributorGroupId(distributorGroupId)).thenThrow(new RuntimeException());
        // act
        distributorInfoService.queryAllByDistributorGroupId(distributorGroupId);
        // assert is handled by the expected exception
    }

    /**
     * 测试queryByDistributorId方法，当distributorId为null时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryByDistributorIdDistributorIdIsNull() {
        distributorInfoService.queryByDistributorId(null);
    }

    /**
     * 测试queryByDistributorId方法，正常情况
     */
    @Test
    public void testQueryByDistributorIdSuccess() {
        Long distributorId = 1L;
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setPhoneNumber("*********");
        DistributorVO distributorVO = new DistributorVO();
        distributorVO.setMobile("123-456-789");
        when(distributorAppService.queryDistributorById(distributorId)).thenReturn(distributorModel);
        when(mobileHelper.getMobileMask("*********")).thenReturn("123-456-789");
        when(distributorVOAssembler.buildDistributorVO(distributorModel)).thenReturn(distributorVO);
        RemoteResponse<DistributorVO> response = distributorInfoService.queryByDistributorId(distributorId);
        assertNotNull(response);
        assertEquals("123-456-789", response.getData().getMobile());
        verify(distributorAppService, times(1)).queryDistributorById(distributorId);
        verify(mobileHelper, times(1)).getMobileMask("*********");
        verify(distributorVOAssembler, times(1)).buildDistributorVO(distributorModel);
    }

    /**
     * 测试queryByDistributorId方法，当distributorAppService.queryDistributorById返回null时
     */
    @Test
    public void testQueryByDistributorIdDistributorModelIsNull() {
        Long distributorId = 1L;
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setPhoneNumber("123");
        when(distributorAppService.queryDistributorById(distributorId)).thenReturn(distributorModel);
        when(mobileHelper.getMobileMask(any())).thenReturn("123");
        RemoteResponse<DistributorVO> response = distributorInfoService.queryByDistributorId(distributorId);
        assertNotNull(response);
        assertNull(response.getData());
        verify(distributorAppService, times(1)).queryDistributorById(distributorId);
    }

    /**
     * 测试queryByDistributorId方法，当手机号码为空时
     */
    @Test
    public void testQueryByDistributorIdPhoneNumberIsNull() {
        Long distributorId = 1L;
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setPhoneNumber(null);
        DistributorVO distributorVO = new DistributorVO();
        when(distributorAppService.queryDistributorById(distributorId)).thenReturn(distributorModel);
        when(distributorVOAssembler.buildDistributorVO(distributorModel)).thenReturn(distributorVO);
        RemoteResponse<DistributorVO> response = distributorInfoService.queryByDistributorId(distributorId);
        assertNotNull(response);
        assertNull(response.getData().getMobile());
        //        verify(distributorAppService, times(1)).queryDistributorById(distributorId);
        //        verify(mobileHelper, never()).getMobileMask(null);
        //        verify(distributorVOAssembler, times(1)).buildDistributorVO(distributorModel);
    }

    /**
     * 测试queryByDistributorId方法，当手机号码遮罩处理失败时
     */
    @Test
    public void testQueryByDistributorIdMobileMaskFail() {
        Long distributorId = 1L;
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setPhoneNumber("*********");
        DistributorVO distributorVO = new DistributorVO();
        when(distributorAppService.queryDistributorById(distributorId)).thenReturn(distributorModel);
        // 模拟遮罩处理失败
        when(mobileHelper.getMobileMask("*********")).thenReturn(null);
        when(distributorVOAssembler.buildDistributorVO(distributorModel)).thenReturn(distributorVO);
        RemoteResponse<DistributorVO> response = distributorInfoService.queryByDistributorId(distributorId);
        assertNotNull(response);
        // 预期手机号码遮罩处理失败，返回的DistributorVO中mobile为null
        assertNull(response.getData().getMobile());
        verify(distributorAppService, times(1)).queryDistributorById(distributorId);
        verify(mobileHelper, times(1)).getMobileMask("*********");
        verify(distributorVOAssembler, times(1)).buildDistributorVO(distributorModel);
    }

    /**
     * 测试查询分销员信息，当分销员ID为空且微信Token对应的账户需要注册
     */
    @Test
    public void testQueryApplyDistributorInfo_WithWxTokenAndNeedRegister() {
        // arrange
        QueryDistributorRequest request = new QueryDistributorRequest(123L, "wxToken", null, 574L);
        PrivateLiveAccountBO privateLiveAccountBO = Mockito.mock(PrivateLiveAccountBO.class);
        when(privateLiveAccountBO.requireRegisterAccount()).thenReturn(true);
        when(privateLiveWechatAccountAppService.getPrivateLiveAccount(any())).thenReturn(privateLiveAccountBO);
        // act
        RemoteResponse<ApplyDistributorVO> response = distributorInfoService.queryApplyDistributorInfo(request);
        // assert
        assertNotNull(response);
        assertNull(response.getData());
    }

    /**
     * 测试查询分销员信息，当请求参数为空时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryApplyDistributorInfo_WithNullRequest() {
        // act
        distributorInfoService.queryApplyDistributorInfo(null);
    }

    /**
     * 测试查询分销员信息，当分销员ID和微信Token都为空时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testQueryApplyDistributorInfo_WithNullDistributorIdAndWxToken() {
        // arrange
        QueryDistributorRequest request = new QueryDistributorRequest(null, null, null, 574L);
        // act
        distributorInfoService.queryApplyDistributorInfo(request);
    }

    /**
     * 测试handleApply方法，当操作类型为AGREE时
     */
    @Test(expected = BizSceneException.class)
    public void testHandleApplyWhenOperationTypeIsAgree() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.AGREE.getCode(), 1L);
        DistributorApplyHandleResponse expectedResponse = new DistributorApplyHandleResponse(1L, 1);
        when(distributorAppService.passAudit(any(DistributorApplyHandleRequest.class))).thenReturn(expectedResponse);
        PrivateLiveAccountBO privateLiveAccountBO = new PrivateLiveAccountBO();
        privateLiveAccountBO.setId(1L);
        privateLiveAccountBO.setAccountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(privateLiveAccountBO);
        // act
        RemoteResponse<DistributorApplyHandleResponse> response = distributorInfoService.handleApply(request);
        // assert
        assertNotNull(response);
        assertEquals(expectedResponse, response.getData());
    }

    /**
     * 测试handleApply方法，当操作类型为REJECT时
     */
    @Test(expected = BizSceneException.class)
    public void testHandleApplyWhenOperationTypeIsReject() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.REJECT.getCode(), 1L);
        DistributorApplyHandleResponse expectedResponse = new DistributorApplyHandleResponse(1L, 0);
        when(distributorAppService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(expectedResponse);
        PrivateLiveAccountBO privateLiveAccountBO = new PrivateLiveAccountBO();
        privateLiveAccountBO.setId(1L);
        privateLiveAccountBO.setAccountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(privateLiveAccountBO);
        // act
        RemoteResponse<DistributorApplyHandleResponse> response = distributorInfoService.handleApply(request);
        // assert
        assertNotNull(response);
        assertEquals(expectedResponse, response.getData());
    }

    /**
     * 测试handleApply方法，当操作类型非法时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testHandleApplyWhenOperationTypeIsInvalid() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, 999, 1L);
        PrivateLiveAccountBO privateLiveAccountBO = new PrivateLiveAccountBO();
        privateLiveAccountBO.setId(1L);
        privateLiveAccountBO.setAccountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(privateLiveAccountBO);
        // act
        distributorInfoService.handleApply(request);
        // assert is done by the expected exception
    }

    /**
     * 测试handleApply方法，当请求参数为null时
     */
    @Test(expected = IllegalArgumentException.class)
    public void testHandleApplyWhenRequestIsNull() {
        // arrange
        DistributorApplyHandleRequest request = null;
        // act
        distributorInfoService.handleApply(request);
        // assert is done by the expected exception
    }

    /**
     * 测试handleApply方法，当anchorId为空且wxToken不为空时，应自动填充anchorId
     */
    @Test(expected = BizSceneException.class)
    public void testHandleApplyWhenAnchorIdIsNull() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", null, ManageOperateTypeEnum.AGREE.getCode(), 1L);
        DistributorApplyHandleResponse expectedResponse = new DistributorApplyHandleResponse(1L, 1);
        PrivateLiveAccountBO privateLiveAccountBO = new PrivateLiveAccountBO();
        privateLiveAccountBO.setId(1L);
        privateLiveAccountBO.setAccountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(privateLiveAccountBO);
        when(distributorAppService.passAudit(any(DistributorApplyHandleRequest.class))).thenReturn(expectedResponse);
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            // act
            RemoteResponse<DistributorApplyHandleResponse> response = distributorInfoService.handleApply(request);
            // assert
            assertNotNull(response);
            assertEquals(expectedResponse, response.getData());
            assertNotNull(request.getAnchorId());
        }
    }

    /**
     * 测试查询顾问分销商信息，输入为空列表
     */
    @Test
    public void testQueryConsultantDistributorWithEmptyList() {
        PrivateLiveConsultantDTO privateLiveConsultantDTO = new PrivateLiveConsultantDTO();
        // arrange
        List<PrivateLiveConsultantRequest> requests = Collections.emptyList();
        privateLiveConsultantDTO.setPrivateLiveConsultantRequests(requests);
        // act
        RemoteResponse<ConsultantDistributorResponse> response = distributorInfoService.queryConsultantDistributor(privateLiveConsultantDTO);
        // assert
        assertNotNull(response);
        assertNull(response.getData());
    }

    /**
     * 测试查询顾问分销商信息，正常情况
     */
    @Test
    public void testQueryConsultantDistributorWithValidInput() {
        // arrange
        List<PrivateLiveConsultantRequest> requests = Arrays.asList(new PrivateLiveConsultantRequest(1L, 2L));
        HashMap<Long, DistributorGroupModel> distributorGroupModelHashMap = new HashMap<>();
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setAccountId(2L);
        distributorGroupModel.setGroupId(3L);
        distributorGroupModelHashMap.put(2L, distributorGroupModel);
        when(distributorGroupAppService.getRelationDistributorGroupMap(anyList())).thenReturn(distributorGroupModelHashMap);
        PrivateLiveConsultantDTO privateLiveConsultantDTO = new PrivateLiveConsultantDTO();
        privateLiveConsultantDTO.setPrivateLiveConsultantRequests(requests);
        // act
        RemoteResponse<ConsultantDistributorResponse> response = distributorInfoService.queryConsultantDistributor(privateLiveConsultantDTO);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertFalse(response.getData().getDistributorGroupInfoMap().isEmpty());
        assertEquals(Long.valueOf(3), response.getData().getDistributorGroupInfoMap().get(1L).getGroupId());
        // verify
        verify(distributorGroupAppService, times(1)).getRelationDistributorGroupMap(anyList());
    }

    @Test
    public void testQueryAllByDistributorGroupIdsEmptyList() {
        // arrange
        List<Long> distributorGroupIds = Collections.emptyList();
        // act
        RemoteResponse<DistributorGroupResponse> response = distributorInfoService.queryAllByDistributorGroupIds(distributorGroupIds);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertTrue(response.getData().getDistributorInfoDTOList().isEmpty());
    }

    @Test
    public void testQueryAllByDistributorGroupIdsSuccess() {
        // arrange
        List<Long> distributorGroupIds = Arrays.asList(1L, 2L);
        DistributorModel distributorModel = new DistributorModel();
        distributorModel.setAccountId(1L);
        distributorModel.setDistributorId(1L);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setGroupId(1L);
        distributorModel.setDistributorGroupModel(distributorGroupModel);
        when(distributorAppService.queryAllByDistributorGroupIds(distributorGroupIds)).thenReturn(Collections.singletonList(distributorModel));
        // act
        RemoteResponse<DistributorGroupResponse> response = distributorInfoService.queryAllByDistributorGroupIds(distributorGroupIds);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertFalse(response.getData().getDistributorInfoDTOList().isEmpty());
        DistributorInfoDTO distributorInfoDTO = response.getData().getDistributorInfoDTOList().get(0);
        assertNotNull(distributorInfoDTO.getDistributorGroupInfoDTO());
        assertEquals(distributorModel.getAccountId(), distributorInfoDTO.getAccountId());
        verify(distributorAppService, times(1)).queryAllByDistributorGroupIds(distributorGroupIds);
    }

    /**
     * 测试handleApply方法，当操作类型为AGREE且权限验证通过时
     */
    @Test(expected = NullPointerException.class)
    public void testHandleApplyWhenOperationTypeIsAgreeAndAuthorized() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.AGREE.getCode(), 1L);
        DistributorApplyHandleResponse expectedResponse = new DistributorApplyHandleResponse(1L, 1);
        when(distributorAppService.passAudit(any(DistributorApplyHandleRequest.class))).thenReturn(expectedResponse);
        DistributorGroupBind distributorGroupBind = new DistributorGroupBind(1L, 1, 1L, 1, null, null);
        when(distributorRootService.getDistributorGroupBind(any(Long.class))).thenReturn(distributorGroupBind);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setGroupId(1L);
        when(userAuthorizeUtil.getDistributorGroupByWxToken(any(String.class))).thenReturn(distributorGroupModel);
        // act
        RemoteResponse<DistributorApplyHandleResponse> response = distributorInfoService.handleApply(request);
        // assert
        assertNotNull(response);
        assertEquals(expectedResponse, response.getData());
    }

    /**
     * 测试handleApply方法，当操作类型为REJECT且权限验证通过时
     */
    @Test(expected = NullPointerException.class)
    public void testHandleApplyWhenOperationTypeIsRejectAndAuthorized() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.REJECT.getCode(), 1L);
        DistributorApplyHandleResponse expectedResponse = new DistributorApplyHandleResponse(1L, 0);
        when(distributorAppService.rejectAudit(any(DistributorApplyHandleRequest.class))).thenReturn(expectedResponse);
        DistributorGroupBind distributorGroupBind = new DistributorGroupBind(1L, 1, 1L, 1, null, null);
        when(distributorRootService.getDistributorGroupBind(any(Long.class))).thenReturn(distributorGroupBind);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        distributorGroupModel.setGroupId(1L);
        when(userAuthorizeUtil.getDistributorGroupByWxToken(any(String.class))).thenReturn(distributorGroupModel);
        // act
        RemoteResponse<DistributorApplyHandleResponse> response = distributorInfoService.handleApply(request);
        // assert
        assertNotNull(response);
        assertEquals(expectedResponse, response.getData());
    }

    /**
     * 测试handleApply方法，当分销人(团员)未加入团队时
     */
    @Test(expected = NullPointerException.class)
    public void testHandleApplyWhenDistributorNotInGroup() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.AGREE.getCode(), 1L);
        when(distributorRootService.getDistributorGroupBind(any(Long.class))).thenReturn(null);
        // act
        distributorInfoService.handleApply(request);
    }

    /**
     * 测试handleApply方法，当团长无访问此团员权限时
     */
    @Test(expected = NullPointerException.class)
    public void testHandleApplyWhenLeaderHasNoAccess() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.AGREE.getCode(), 1L);
        DistributorGroupBind distributorGroupBind = new DistributorGroupBind(1L, 1, 1L, 1, null, null);
        when(distributorRootService.getDistributorGroupBind(any(Long.class))).thenReturn(distributorGroupBind);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        // Different group ID to simulate no access
        distributorGroupModel.setGroupId(2L);
        when(userAuthorizeUtil.getDistributorGroupByWxToken(any(String.class))).thenReturn(distributorGroupModel);
        // act
        distributorInfoService.handleApply(request);
    }

    /**
     * 测试handleApply方法，当团长无访问此团员权限时
     */
    @Test(expected = BizSceneException.class)
    public void testHandleApplyWhenLeaderHasNoAccessToDistributor() {
        // arrange
        DistributorApplyHandleRequest request = new DistributorApplyHandleRequest("wxToken", 1L, ManageOperateTypeEnum.AGREE.getCode(), 1L);
        DistributorGroupBind distributorGroupBind = new DistributorGroupBind(1L, 1, 1L, 1, null, null);
        when(distributorRootService.getDistributorGroupBind(any(Long.class))).thenReturn(distributorGroupBind);
        DistributorGroupModel distributorGroupModel = new DistributorGroupModel();
        // Different group ID to simulate no access
        distributorGroupModel.setGroupId(2L);
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class)))
                .thenReturn(PrivateLiveAccountBO.builder().id(1L).accountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR,
                        PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP,
                        PrivateLiveAccountTypeEnum.CONSULTANT)).build());
        when(userAuthorizeUtil.getDistributorGroupByWxToken(any(String.class))).thenReturn(distributorGroupModel);
        // act
        distributorInfoService.handleApply(request);
    }
}
