package com.sankuai.carnation.distribution.distributionplan.config;

import com.sankuai.carnation.distribution.config.OpenSdkConfig;
import com.sankuai.carnation.distribution.config.model.OrgConfig;
import com.sankuai.meituan.org.opensdk.client.RemoteServiceClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class OpenSdkConfigTest {

    private OpenSdkConfig openSdkConfigUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        openSdkConfigUnderTest = new OpenSdkConfig();
    }

    /**
     * 测试 remoteServiceClient 方法，当 Lion 返回的配置有效时
     */
    @Test
    public void testRemoteServiceClient_ValidConfig() throws Exception {

        // act
        RemoteServiceClient result = openSdkConfigUnderTest.remoteServiceClient();

        // assert
        assertNotNull(result);
    }

    /**
     * 测试 remoteServiceClient 方法，当 Lion 返回的配置无效时
     */
    @Test
    public void testRemoteServiceClient_InvalidConfig() throws Exception {
        // act
        RemoteServiceClient result = openSdkConfigUnderTest.remoteServiceClient();

        // assert
        assertNotNull(result);
    }

    @Test
    public void testNewInstance() throws Exception {
        // act
        OrgConfig build = OrgConfig.builder()
                .appSource("")
                .appTenantId(1)
                .remoteAppKey("")
                .clientAppKey("")
                .build();

        // assert
        assertNotNull(build);
    }
}
