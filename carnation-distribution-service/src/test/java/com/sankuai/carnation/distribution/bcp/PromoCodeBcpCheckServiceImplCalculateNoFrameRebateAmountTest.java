package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleNormalRuleDTO;
import com.dianping.gmkt.event.api.rebate.enums.RebateSettleRuleType;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import static org.mockito.ArgumentMatchers.*;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.dianping.technician.common.api.domain.TechnicianResp;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplCalculateNoFrameRebateAmountTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private OrderInfoBO orderInfoBO;

    @Mock
    private OrderReceiptDto orderReceiptDto;

    @Mock
    private RebateActivityRuleDTO rebateActivityRuleDTO;

    @Mock
    private RebateSettleRuleDTO rebateSettleRuleDTO;

    @Mock
    private RebateSettleNormalRuleDTO rebateSettleNormalRuleDTO;

    @Mock
    private OrderInfoAdaptor orderInfoAdaptor;

    @Mock
    private ShopAcl shopAcl;

    @Mock
    private UserScanRecordService userScanRecordService;

    @Mock
    private OrderReceiptQueryService orderReceiptQueryService;

    @Mock
    private DistributorActivityService distributorActivityService;

    private CommissionVerifyCalculationRequest request;

    @Before
    public void setUp() {
        request = new CommissionVerifyCalculationRequest();
        request.setOrderId("123");
        request.setCouponVerifyId("456");
        request.setTechApplyRecordId(789L);
        request.setVerifyTime(new Date());
        setupOrderInfoAdaptor();
        setupShopAcl();
        setupOrderReceiptQueryService();
        setupDistributorActivityService();
        setupOrderInfoBO();
        setupRebateActivityRule();
    }




    private void setupOrderInfoAdaptor() {
        when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(orderInfoBO);
    }

    private void setupShopAcl() {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(new DpPoiBackCategoryDTO()));
        List<DpPoiDTO> dpPoiDTOList = Arrays.asList(dpPoiDTO);
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(dpPoiDTOList);
    }

    private void setupOrderReceiptQueryService() {
        TechnicianResp<OrderReceiptDto> technicianResp = new TechnicianResp<>();
        technicianResp.setCode(TechnicianResp.SUCCESS);
        technicianResp.setData(orderReceiptDto);
        when(orderReceiptQueryService.queryOrderReceipt(anyString(), anyInt())).thenReturn(technicianResp);
    }

    private void setupDistributorActivityService() {
        CommonResponse<RebateActivityRuleDTO> commonResponse = new CommonResponse<>();
        commonResponse.setCode(CommonResponse.CODE_SUCCESS);
        commonResponse.setData(rebateActivityRuleDTO);
        when(distributorActivityService.queryActivityRule(any())).thenReturn(commonResponse);
    }

    private void setupOrderInfoBO() {
        when(orderInfoBO.getShopId()).thenReturn(1L);
        when(orderInfoBO.getPlatform()).thenReturn(1);
        when(orderInfoBO.getUserId()).thenReturn(1L);
        when(orderInfoBO.getBuySuccessTime()).thenReturn(new Date());
    }

    private void setupRebateActivityRule() {
        when(rebateActivityRuleDTO.getRule()).thenReturn(rebateSettleRuleDTO);
        when(rebateSettleRuleDTO.getType()).thenReturn(RebateSettleRuleType.NORMAL.code);
        when(rebateSettleRuleDTO.getNormalRule()).thenReturn(rebateSettleNormalRuleDTO);
        when(rebateSettleNormalRuleDTO.getRebateAmount()).thenReturn(100L);
    }

    /**
     * 测试正常获取普通返利佣金的情况
     */
    @Test
    public void testCalculateNoFrameRebateAmountNormal() throws Throwable {

        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        PromoQRCodeResponse<List<UserScanRecordDTO>> scanResponse = mock(PromoQRCodeResponse.class);
        when(scanResponse.isSuccess()).thenReturn(true);
        when(scanResponse.getData()).thenReturn(Arrays.asList(new UserScanRecordDTO()));
        when(userScanRecordService.queryLatestScanRecord(any())).thenReturn(scanResponse);
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(100L, result);
    }

    /**
     * 测试核销时间超过下单时间24h的情况
     */

    @Test
    public void testCalculateNoFrameRebateAmountOrderTimeExceed() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date(System.currentTimeMillis() - 1000000000L));
        PromoQRCodeResponse<List<UserScanRecordDTO>> scanResponse = mock(PromoQRCodeResponse.class);
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    /**
     *  测试扫码时间超过一周的情况
     */

    @Test
    public void testCalculateNoFrameRebateAmountUserNotScan() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        PromoQRCodeResponse<List<UserScanRecordDTO>> scanResponse = mock(PromoQRCodeResponse.class);
        when(scanResponse.isSuccess()).thenReturn(true);
        when(scanResponse.getData()).thenReturn(Arrays.asList());
        when(userScanRecordService.queryLatestScanRecord(any())).thenReturn(scanResponse);
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    /**
     *  测试无订单信息的情况
     */

    @Test
    public void testCalculateNoFrameRebateAmountOrderReceiptNull() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    /**
     *
    */
    @Test
    public void testCalculateNoFrameRebateAmountNoActivityRule() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountNotNormalRule() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountRebateAmountNull() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }

    @Test
    public void testCalculateNoFrameRebateAmountRebateAmountZero() throws Throwable {
        when(orderInfoBO.getOrderTime()).thenReturn(new Date());
        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(-1L, result);
    }
}
