//package com.sankuai.carnation.distribution.distributionplan.acl;
//
//
//import com.dianping.gb.audit.platform.biz.service.ProcessService;
//import com.dianping.tp.audit.dto.HistoryDTO;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.junit.Assert.*;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.anyLong;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class PlatformAuditAclImplTest {
//    @Mock
//    private ProcessService processService;
//
//    @InjectMocks
//    private PlatformAuditAcl platformAuditAcl;
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//    }
//
//    /**
//     * 测试subjectId为null时抛出RuntimeException
//     */
//    @Test
//    public void testFetchHistorySubjectIdIsNull() throws Throwable {
//        // arrange
//        Long subjectId = null;
//
//        // act
//        List<HistoryDTO> historyDTOS = platformAuditAcl.fetchHistory(subjectId);
//
//        // assert is handled by the expected exception
//        assertNull(historyDTOS);
//    }
//
//    /**
//     * 测试processService.fetchHistory返回空列表时抛出RuntimeException
//     */
//    @Test
//    public void testFetchHistoryResultIsEmpty() throws Throwable {
//        // arrange
//        Long subjectId = 1L;
//        when(processService.fetchHistory(anyInt(), anyLong())).thenReturn(new ArrayList<>());
//
//        // act
//        List<HistoryDTO> historyDTOS = platformAuditAcl.fetchHistory(subjectId);
//
//        // assert is handled by the expected exception
//        assertNull(historyDTOS);
//    }
//
//    /**
//     * 测试正常情况下能够获取审批历史
//     */
//    @Test
//    public void testFetchHistorySuccess() throws Throwable {
//        // arrange
//        Long subjectId = 1L;
//        List<HistoryDTO> expectedHistory = new ArrayList<>();
//        expectedHistory.add(new HistoryDTO());
//        when(processService.fetchHistory(anyInt(), anyLong())).thenReturn(expectedHistory);
//
//        // act
//        List<HistoryDTO> result = platformAuditAcl.fetchHistory(subjectId);
//
//        // assert
//        assertNotNull(result);
//        assertFalse(result.isEmpty());
//        assertEquals(expectedHistory, result);
//    }
//}
