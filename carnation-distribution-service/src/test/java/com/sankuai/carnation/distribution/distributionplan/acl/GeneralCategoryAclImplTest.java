package com.sankuai.carnation.distribution.distributionplan.acl;

import com.dianping.general.category.response.Response;
import com.dianping.general.category.service.GeneralCategoryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.distributionplan.acl.impl.GeneralCategoryAclImpl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralCategoryTree;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCategory;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GeneralCategoryAclImplTest {

    @Mock
    private GeneralCategoryService generalCategoryService;

    @InjectMocks
    private GeneralCategoryAclImpl generalCategoryAcl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    /**
     * 测试 tradeType 为 null 时抛出 RuntimeException
     */
    @Test
    public void testGetCategoryTreeByTradeTypeWithNullTradeType() {
        List<GeneralCategoryTree> categoryTreeList = generalCategoryAcl.getCategoryTreeByTradeType(null);
        Assert.assertEquals(categoryTreeList, Lists.newArrayList());
    }

    /**
     * 测试 response 为 null 时抛出 DistributionPlanException
     */
    @Test
    public void testGetCategoryTreeByTradeTypeWithNullResponse() {
        Integer tradeType = 1;
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(null);
        List<GeneralCategoryTree> categoryTreeList = generalCategoryAcl.getCategoryTreeByTradeType(tradeType);

        Assert.assertEquals(categoryTreeList, Lists.newArrayList());

    }

    /**
     * 测试 response.isFail() 为 true 时抛出 RuntimeException
     */
    @Test
    public void testGetCategoryTreeByTradeTypeWithResponseFail() {
        Integer tradeType = 1;
        Response<String> response = new Response<>();
        response.setSuccess(false);
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(response);
        List<GeneralCategoryTree> categoryTreeList = generalCategoryAcl.getCategoryTreeByTradeType(tradeType);
        Assert.assertEquals(categoryTreeList, Lists.newArrayList());

    }

    /**
     * 测试 response.getResult() 为 null 时抛出 RuntimeException
     */
    @Test
    public void testGetCategoryTreeByTradeTypeWithNullResult() {
        Integer tradeType = 1;
        Response<String> response = new Response<>();
        response.setSuccess(true);
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(response);
        List<GeneralCategoryTree> categoryTreeList = generalCategoryAcl.getCategoryTreeByTradeType(tradeType);
        Assert.assertEquals(categoryTreeList, Lists.newArrayList());

    }

    /**
     * 测试正常情况下返回正确的 GeneralCategoryTree 列表
     */
    @Test
    public void testGetCategoryTreeByTradeTypeWithValidResponse() {
        Integer tradeType = 1;
        Response<String> response = new Response<>();
        response.setSuccess(true);
        response.setResult("[{\"platformCategoryId\":1,\"categoryName\":\"Test\",\"level\":1,\"seq\":1,\"childList\":[],\"parentCategoryName\":\"Parent\",\"parentPlatformCategoryId\":0}]");
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(response);
        List<GeneralCategoryTree> result = generalCategoryAcl.getCategoryTreeByTradeType(tradeType);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }


    /**
     * 测试 tradeType 为 null 时抛出 IllegalArgumentException
     */
    @Test
    public void testGetCategoryMapWithNullTradeType() {
        Map<ProductCategory, List<ProductCategory>> categoryMap = generalCategoryAcl.getCategoryMap(null);

        Assert.assertEquals(categoryMap, Maps.newHashMap());

    }

    /**
     * 测试 generalCategoryService 返回 null 时抛出 DistributionPlanException
     */
    @Test
    public void testGetCategoryMapWithNullResponse() {
        Integer tradeType = 1;
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(null);
        Map<ProductCategory, List<ProductCategory>> categoryMap = generalCategoryAcl.getCategoryMap(tradeType);
        Assert.assertEquals(categoryMap, Maps.newHashMap());
    }

    /**
     * 测试 generalCategoryService 返回失败结果时抛出 RuntimeException
     */
    @Test
    public void testGetCategoryMapWithFailResponse() {
        Integer tradeType = 1;
        Response<String> response = new Response<>();
        response.setSuccess(false);
        response.setResultMessage("失败");
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(response);
        Map<ProductCategory, List<ProductCategory>> categoryMap = generalCategoryAcl.getCategoryMap(tradeType);
        Assert.assertEquals(categoryMap, Maps.newHashMap());
    }

    /**
     * 测试 generalCategoryService 返回的 result 为 null 时抛出 RuntimeException
     */
    @Test
    public void testGetCategoryMapWithNullResult() {
        Integer tradeType = 1;
        Response<String> response = new Response<>();
        response.setSuccess(true);
        response.setResult(null);
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(response);
        Map<ProductCategory, List<ProductCategory>> categoryMap = generalCategoryAcl.getCategoryMap(tradeType);
        Assert.assertEquals(categoryMap, Maps.newHashMap());
    }

    /**
     * 测试正常情况下方法返回正确的 Map 结构
     */
    @Test
    public void testGetCategoryMapWithValidResponse() {
        Integer tradeType = 1;
        Response<String> response = new Response<>();
        response.setSuccess(true);
        GeneralCategoryTree parent = new GeneralCategoryTree();
        parent.setPlatformCategoryId(1L);
        parent.setCategoryName("Parent");
        GeneralCategoryTree child = new GeneralCategoryTree();
        child.setPlatformCategoryId(2L);
        child.setCategoryName("Child");
        parent.setChildList(Lists.newArrayList(child));
        String json = JsonUtil.toJson(Lists.newArrayList(parent));
        response.setResult(json);
        when(generalCategoryService.getCategoryTree(tradeType)).thenReturn(response);

        Map<ProductCategory, List<ProductCategory>> result = generalCategoryAcl.getCategoryMap(tradeType);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(new ProductCategory(1L, "Parent")));
        assertEquals(1, result.get(new ProductCategory(1L, "Parent")).size());
        assertEquals(new ProductCategory(2L, "Child"), result.get(new ProductCategory(1L, "Parent")).get(0));
    }
}