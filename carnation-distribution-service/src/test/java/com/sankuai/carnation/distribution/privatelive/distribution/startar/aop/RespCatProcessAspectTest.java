package com.sankuai.carnation.distribution.privatelive.distribution.startar.aop;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.mtrace.ServerTracer;
import com.meituan.mtrace.Tracer;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class RespCatProcessAspectTest {

    @InjectMocks
    private RespCatProcessAspect respCatProcessAspect;

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private RespCatProcess respCatProcess;

    @Mock
    private MethodSignature methodSignature;

    @Mock
    private ServerTracer serverTracer;

    private RespCatProcessAspect aspect;

    private RespCatProcess process;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    /**
     * 测试BizSceneException异常情况
     */
    @Test
    public void testCatTransactionProcessWithBizSceneException() throws Throwable {
        // arrange
        when(joinPoint.proceed()).thenThrow(new BizSceneException("Biz error", 400));
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(respCatProcess.type()).thenReturn("");
        when(respCatProcess.name()).thenReturn("");
        when(methodSignature.getDeclaringType()).thenReturn((Class) this.getClass());
        when(methodSignature.getName()).thenReturn("testMethod");
        // act
        Object result = respCatProcessAspect.catTransactionProcess(joinPoint, respCatProcess);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof RemoteResponse);
        assertEquals(400, ((RemoteResponse) result).getCode());
    }

    /**
     * 测试IllegalArgumentException异常情况
     */
    @Test
    public void testCatTransactionProcessWithIllegalArgumentException() throws Throwable {
        // arrange
        when(joinPoint.proceed()).thenThrow(new IllegalArgumentException("Invalid argument"));
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(respCatProcess.type()).thenReturn("");
        when(respCatProcess.name()).thenReturn("");
        when(methodSignature.getDeclaringType()).thenReturn((Class) this.getClass());
        when(methodSignature.getName()).thenReturn("testMethod");
        // act
        Object result = respCatProcessAspect.catTransactionProcess(joinPoint, respCatProcess);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof RemoteResponse);
        assertEquals(ResponseEnum.INVALID_PARAM.code, ((RemoteResponse) result).getCode());
    }

    /**
     * 测试未知Throwable异常情况
     */
    @Test
    public void testCatTransactionProcessWithUnknownThrowable() throws Throwable {
        // arrange
        when(joinPoint.proceed()).thenThrow(new RuntimeException("Unknown error"));
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(respCatProcess.type()).thenReturn("");
        when(respCatProcess.name()).thenReturn("");
        when(methodSignature.getDeclaringType()).thenReturn((Class) this.getClass());
        when(methodSignature.getName()).thenReturn("testMethod");
        // act
        Object result = respCatProcessAspect.catTransactionProcess(joinPoint, respCatProcess);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof RemoteResponse);
        assertEquals("系统繁忙，请重试", ((RemoteResponse) result).getMsg());
    }


    @Test
    public void testAddTraceIdWithNonNullRespAndTraceId() {
//        RemoteResponse<Object> resp = new RemoteResponse<>();
        when(serverTracer.getTraceId()).thenReturn("traceId123");
//        RemoteResponse<Object> result = aspect.addTraceId(resp);
//        assertNotNull("当resp非null时，不应返回null", result);
//        assertEquals("traceId123", result.getTraceId());
    }

    /**
     * 测试当resp非null且Tracer.getServerTracer().getTraceId()返回null时
     */
    @Test
    public void testAddTraceIdWithNonNullRespAndNullTraceId() {
//        RemoteResponse<Object> resp = new RemoteResponse<>();
        when(serverTracer.getTraceId()).thenReturn(null);
//        RemoteResponse<Object> result = aspect.addTraceId(resp);
//        assertNotNull("当resp非null时，不应返回null", result);
//        assertNull("当Tracer.getServerTracer().getTraceId()返回null时，traceId应为null", result.getTraceId());
    }

    /**
     * 测试aspectNeedLog方法，当needLog返回true且logSampleRadio返回的值使得当前时间毫秒值的百分比小于等于logSampleRadio时
     */

    // 更多测试用例可以根据实际情况继续添加，例如测试不同的异常处理、边界情况等
}
