package com.sankuai.carnation.distribution.bcp;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.enums.PromoQRCodeResCode;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleLimitRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleNormalRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.limit.RebateRuleLimitInterface;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.sankuai.carnation.distribution.commisson.limit.rule.RebateLimitRule;
import com.sankuai.carnation.distribution.commisson.limit.rule.ShopAmountLimitRule;
import com.sankuai.carnation.distribution.commisson.limit.rule.TechnicianAmountLimitRule;
import com.sankuai.carnation.distribution.commisson.limit.rule.UserOrderCountLimitRule;
import com.sankuai.carnation.distribution.commisson.repository.RebateLimitRepository;
import com.sankuai.carnation.distribution.commisson.settle.rule.ShopCommissionRuleDomainService;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckRebateTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private OrderInfoAdaptor orderInfoAdaptor;

    @Mock
    private ShopCommissionRuleDomainService shopCommissionRuleDomainService;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;

    @Mock
    private UserScanRecordService userScanRecordService;

    @Mock
    private OrderReceiptQueryService orderReceiptQueryService;

    @Mock
    private ShopAcl shopAcl;

    @Mock
    ShopAmountLimitRule shopAmountLimitRule;

    @Mock
    DistributorActivityService distributorActivityService;

    @Mock
    RebateLimitRepository rebateLimitRepository;

    @Mock
    List<RebateRuleLimitInterface> limitInterfaceList;


    @Mock
    TechnicianAmountLimitRule technicianAmountLimitRule;

    @Mock
    UserOrderCountLimitRule userOrderCountLimitRule;

    OrderOperateNotifyBcpDTO orderOperateNotify;
    CommissionVerifyCalculationRequest request;

    OrderInfoBO orderInfoBO;
    List<RbOrderVerifyRebateCommission> rbOrderVerifyRebateCommissions;
    PromoQRCodeResponse<List<UserScanRecordDTO>> scanResponse;
    OrderReceiptDto orderReceiptDto;
    RebateActivityRuleDTO rebateActivityRuleDTO;
    RebateLimitResult rebateLimitResult;

    @Before
    public void build(){

        rebateLimitResult = new RebateLimitResult();
        rebateLimitResult.setRebateAmount(100L);
        rebateLimitResult.setResult(true);

        orderOperateNotify = new OrderOperateNotifyBcpDTO();
        orderOperateNotify.setOrderId("1");
        orderOperateNotify.setBizId("1");

        request = new CommissionVerifyCalculationRequest();
        request.setChannel("promo_code");
        request.setOrderId("1");
        request.setOrderType(1);
        request.setTechId(1);
        request.setCouponVerifyId("456");
        request.setTechApplyRecordId(789L);
        request.setVerifyTime(new Date());

        orderInfoBO = new OrderInfoBO();
        orderInfoBO.setShopId(1L);
        orderInfoBO.setPlatform(1);
        orderInfoBO.setUserId(1L);
        orderInfoBO.setBuySuccessTime(new Date());
        orderInfoBO.setOrderTime(new Date());
        orderInfoBO.setSkuList(new ArrayList<>());

        scanResponse = new PromoQRCodeResponse<>();
        scanResponse.setCode(PromoQRCodeResCode.SUCCESS.getCode());
        scanResponse.setData(Arrays.asList(new UserScanRecordDTO()));

        orderReceiptDto = new OrderReceiptDto();

        TechnicianResp<OrderReceiptDto> technicianResp = new TechnicianResp<>();
        technicianResp.setCode(TechnicianResp.SUCCESS);
        technicianResp.setData(orderReceiptDto);
        when(orderReceiptQueryService.queryOrderReceipt(anyString(), anyInt())).thenReturn(technicianResp);

        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(new DpPoiBackCategoryDTO()));
        List<DpPoiDTO> dpPoiDTOList = Arrays.asList(dpPoiDTO);
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(dpPoiDTOList);

        rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rebateSettleRuleDTO = new RebateSettleRuleDTO();
        RebateSettleNormalRuleDTO rebateSettleNormalRuleDTO = new RebateSettleNormalRuleDTO();
        rebateSettleNormalRuleDTO.setRebateAmount(100L);
        rebateSettleRuleDTO.setNormalRule(rebateSettleNormalRuleDTO);
        rebateSettleRuleDTO.setType(1);
        rebateActivityRuleDTO.setRule(rebateSettleRuleDTO);
        RebateSettleLimitRuleDTO rebateSettleLimitRuleDTO = new RebateSettleLimitRuleDTO();
        rebateSettleLimitRuleDTO.setShopAmountLimit(1000L);
        rebateSettleLimitRuleDTO.setDistributorAmountLimit(1000L);
        rebateSettleLimitRuleDTO.setUserOrderCountLimit(1000);
        rebateSettleRuleDTO.setRebateSettleLimitRule(rebateSettleLimitRuleDTO);


        CommonResponse<RebateActivityRuleDTO> commonResponse = new CommonResponse<>();
        commonResponse.setCode(CommonResponse.CODE_SUCCESS);
        commonResponse.setData(rebateActivityRuleDTO);
        when(distributorActivityService.queryActivityRule(any())).thenReturn(commonResponse);



    }


    /**
     * 测试普通返利佣金计算成功的情况
     */

    @Test
    public void testCalculateNoFrameRebateAmount(){

        when(rebateLimitRepository.queryOrderByUnifiedOrderId(anyString())).thenReturn(null);
        when(rebateLimitRepository.sumOrderRebateAmountByDpShopId(any(),any())).thenReturn(0L);
        when(userScanRecordService.queryLatestScanRecord(any())).thenReturn(scanResponse);
        when(orderInfoAdaptor.getOrder(anyInt(),anyString())).thenReturn(orderInfoBO);

        Iterator<RebateRuleLimitInterface> mockIterator = mock(Iterator.class);
        when(limitInterfaceList.iterator()).thenReturn(mockIterator);
        when(limitInterfaceList.isEmpty()).thenReturn(false);
        when(mockIterator.hasNext()).thenReturn(true,true,true,false);
        when(mockIterator.next()).thenReturn(shopAmountLimitRule,technicianAmountLimitRule,userOrderCountLimitRule);

        when(shopAmountLimitRule.handleResult(any(),any(),any())).thenReturn(rebateLimitResult);
        when(technicianAmountLimitRule.handleResult(any(),any(),any())).thenReturn(rebateLimitResult);
        when(userOrderCountLimitRule.handleResult(any(),any(),any())).thenReturn(rebateLimitResult);


        long result = promoCodeBcpCheckService.calculateNoFrameRebateAmount(request);
        assertEquals(100L, result);

    }

}
