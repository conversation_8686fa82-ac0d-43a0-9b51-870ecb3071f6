package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveDistributorCodeDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveIntentionExpireTimeDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionExpireTime;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveIntentionExpireTimeRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveIntentionExpireService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class PrivateLiveIntentionExpireServiceImplTest {

    @InjectMocks
    private PrivateLiveIntentionExpireServiceImpl service;

    @Mock
    private PrivateLiveIntentionExpireTimeRepository privateLiveIntentionExpireTimeRepository;

    @Mock
    private PrivateLiveDistributorCodeService privateLiveDistributorCodeService;

    @Mock
    private DzPrivateLiveIntentionExpireService dzPrivateLiveIntentionExpireService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：当distributorCode不为空，但loadLiveAndTaskByCode返回失败时，应返回null
     */
    @Test
    public void testQueryExpireTimeDTOByDistributorCode_LoadLiveAndTaskByCodeFails() {
        // arrange
        String distributorCode = "testCode";
        RemoteResponse<PrivateLiveDistributorCodeDTO> response = RemoteResponse.fail("");
        when(privateLiveDistributorCodeService.loadLiveAndTaskByCode(distributorCode)).thenReturn(response);

        // act
        PrivateLiveIntentionExpireTimeDTO result = service.queryExpireTimeDTOByDistributorCode(distributorCode);

        // assert
        assertNull(result);
    }



    /**
     * 测试场景：当distributorCode不为空，loadLiveAndTaskByCode成功，并找到对应的过期时间信息时，应正确返回DTO
     */
    @Test
    public void testQueryExpireTimeDTOByDistributorCode_Success() {
        // arrange
        String distributorCode = "testCode";
        PrivateLiveDistributorCodeDTO distributorCodeDTO = new PrivateLiveDistributorCodeDTO();
        distributorCodeDTO.setLiveId("liveId");
        RemoteResponse<PrivateLiveDistributorCodeDTO> response = RemoteResponse.success(distributorCodeDTO);
        when(privateLiveDistributorCodeService.loadLiveAndTaskByCode(distributorCode)).thenReturn(response);

        Date expireTime = new Date();
        PrivateLiveIntentionExpireTime privateLiveIntentionExpireTime = new PrivateLiveIntentionExpireTime(1L, "liveId", 1, expireTime, new Date(), new Date());
        when(privateLiveIntentionExpireTimeRepository.queryByLiveId("liveId")).thenReturn(privateLiveIntentionExpireTime);

        // act
        PrivateLiveIntentionExpireTimeDTO result = service.queryExpireTimeDTOByDistributorCode(distributorCode);

        // assert
        assertEquals("liveId", result.getLiveId());
        assertEquals(expireTime, result.getExpireTime());
    }

    @Test
    public void testQueryByLiveIdOld_WhenDataFound() {
        // arrange
        String liveId = "testLiveId";
        PrivateLiveIntentionExpireTime expireTime = new PrivateLiveIntentionExpireTime(1L, liveId, 1, new Date(), new Date(), new Date());
        when(privateLiveIntentionExpireTimeRepository.queryByLiveId(liveId)).thenReturn(expireTime);

        // act
        RemoteResponse<PrivateLiveIntentionExpireTimeDTO> response = service.queryByLiveIdOld(liveId);

        // assert
        assertTrue(response.isSuccess());
        assertEquals(liveId, response.getData().getLiveId());
        verify(privateLiveIntentionExpireTimeRepository, times(1)).queryByLiveId(liveId);
    }

    @Test
    public void testQueryByLiveIdNewReturnsSuccess() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        PrivateLiveIntentionExpireTimeDTO expectedDto = new PrivateLiveIntentionExpireTimeDTO();
        expectedDto.setLiveId(liveId);
        expectedDto.setExpireTime(new Date());
        RemoteResponse<PrivateLiveIntentionExpireTimeDTO> success = RemoteResponse.success(new PrivateLiveIntentionExpireTimeDTO());
        when(dzPrivateLiveIntentionExpireService.queryByLiveId(liveId)).thenReturn(success);
        // act
        RemoteResponse<PrivateLiveIntentionExpireTimeDTO> actualResponse = service.queryByLiveIdNew(liveId);
        // assert
        assertNotNull(actualResponse);
    }

    @Test
    public void testInitOld_ExistingExpireTime() throws Throwable {
        String liveId = "testLiveId";
        // arrange
        when(privateLiveIntentionExpireTimeRepository.queryByLiveId(liveId))
                .thenReturn(new PrivateLiveIntentionExpireTime());

        // act
        RemoteResponse<Boolean> result = service.initOld(liveId);

        // assert
        assertTrue(result.getData());
        verify(privateLiveIntentionExpireTimeRepository, times(1)).queryByLiveId(liveId);
        verify(privateLiveIntentionExpireTimeRepository, never()).insert(any(PrivateLiveIntentionExpireTime.class));
    }

    @Test
    public void testInitNew_ReturnsSuccess() throws Throwable {
        // arrange
        String liveId = "testLiveId";
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveIntentionExpireService.init(liveId)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = service.initNew(liveId);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveIntentionExpireService, times(1)).init(liveId);
    }
}
