package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.handler;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.rebate.dto.*;
import com.dianping.gmkt.event.api.rebate.service.RebateActivityManageService;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateRuleCheckResult;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.RebateNewOldCustomerService;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.RebateRuleCheckUtils;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class RebateAmountUserStepCalcHandlerTest {

    @InjectMocks
    private RebateAmountUserStepCalcHandler rebateAmountUserStepCalcHandler;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;
    @Mock
    private RebateNewOldCustomerService rebateNewOldCustomerService;
    @Mock
    private RebateActivityManageService rebateActivityManageService;
    @Mock
    private RedisStoreClient redisStoreClient;
    @Mock
    private RebateRuleCheckUtils rebateRuleCheckUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试新用户，无重复，满足阶梯规则
     */
    @Test
    public void testCalculateNewUserNoDuplicateMeetsStepRule() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = getRebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        CommonResponse commonResponse = new CommonResponse();
        RebateActivityConfigDTO rebateActivityConfigDTO = new RebateActivityConfigDTO();
        rebateActivityConfigDTO.setCategoryId(2);
        CommonResponse<RebateActivityConfigDTO> activityConfigResponse = commonResponse.success(rebateActivityConfigDTO);

        when(rebateActivityManageService.getActivityConfigById(any())).thenReturn(activityConfigResponse);
        when(rebateNewOldCustomerService.validNewCustomerStatus(any())).thenReturn(true);
        when(redisStoreClient.setnx(any(), any())).thenReturn(true);
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(anyLong(), anyLong(), anyInt(), any())).thenReturn(new ArrayList<>());
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndNewUser(anyLong(), anyString())).thenReturn(0L);
        when(rebateRuleCheckUtils.checkRuleByNewUser(any(), any(), anyBoolean())).thenReturn(RebateRuleCheckResult.valid());

        // act
        RemoteResponse<RebateAmountResult> response = rebateAmountUserStepCalcHandler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertTrue(response.isSuccess());
        assertEquals(100L, (long) response.getData().getRebateAmount());
    }

    /**
     * 测试新用户，存在重复，不计算返利
     */
    @Test
    public void testCalculateNewUserDuplicate() throws Throwable {
        RebateAmountCalcRequest request = getRebateAmountCalcRequest();
        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        CommonResponse commonResponse = new CommonResponse();
        RebateActivityConfigDTO rebateActivityConfigDTO = new RebateActivityConfigDTO();
        rebateActivityConfigDTO.setCategoryId(2);
        CommonResponse<RebateActivityConfigDTO> activityConfigResponse = commonResponse.success(rebateActivityConfigDTO);

        when(rebateActivityManageService.getActivityConfigById(any())).thenReturn(activityConfigResponse);
        when(rebateNewOldCustomerService.validNewCustomerStatus(any())).thenReturn(true);
        when(redisStoreClient.setnx(any(), any())).thenReturn(false);
        // act
        RemoteResponse<RebateAmountResult> response = rebateAmountUserStepCalcHandler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertTrue(response.isSuccess());
        assertEquals("用户存在重复", response.getData().getReason());
    }

    @NotNull
    private RebateAmountCalcRequest getRebateAmountCalcRequest() {
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("order1");
        request.setUserId(1);
        request.setPlatform(1);
        request.setPayCent(1000L);
        request.setTechApplyRecordId(1);
        request.setBuySuccessTime(new Date());
        return request;
    }

    private RebateActivityRuleDTO getRebateActivityRuleDTO() {
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleDTO = new RebateSettleRuleDTO();


        RebateSettleCustomerStepRuleDTO newCustomerStepRule = new RebateSettleCustomerStepRuleDTO();
        RebateSettleRuleStepConfigDTO rebateSettleRuleStepConfigDTO = new RebateSettleRuleStepConfigDTO();
        rebateSettleRuleStepConfigDTO.setStepIndex(1);
        rebateSettleRuleStepConfigDTO.setStepStart(1L);
        rebateSettleRuleStepConfigDTO.setStepEnd(10L);
        rebateSettleRuleStepConfigDTO.setRebateAmount(100L);
        newCustomerStepRule.setRuleStep(Lists.newArrayList(rebateSettleRuleStepConfigDTO));



        ruleDTO.setNewCustomerStepRule(newCustomerStepRule);
        rebateActivityRuleDTO.setRule(ruleDTO);
        return rebateActivityRuleDTO;
    }


}
