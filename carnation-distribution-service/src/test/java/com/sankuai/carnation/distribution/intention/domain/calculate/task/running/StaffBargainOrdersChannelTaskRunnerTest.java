package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.StaffCodeBargainBO;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelRunningTaskDataService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.ReceiveRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.service.staffbargain.StaffBargainService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class StaffBargainOrdersChannelTaskRunnerTest {

    @InjectMocks
    private StaffBargainOrdersChannelTaskRunner staffBargainOrdersChannelTaskRunner;

    @Mock
    private StaffBargainService staffBargainService;

    @Mock
    private OrderChannelRunningTaskDataService runningTaskDataService;

    @Test
    public void testCalculateOrderWithoutStaffCodeBargain() throws Throwable {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        orderInfoBO.setStaffCodeBargainBO(null);
        OrderChannelTaskRunningResultBO expectedResult = OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        OrderChannelTaskRunningResultBO actualResult = staffBargainOrdersChannelTaskRunner.calculate(taskParam, orderInfoBO, task);
        assertEquals(expectedResult.getCalculateResult(), actualResult.getCalculateResult());
        assertEquals(expectedResult.getBusinessChannel(), actualResult.getBusinessChannel());
        assertEquals(expectedResult.getIntentionType(), actualResult.getIntentionType());
    }

    @Test
    public void testCalculateOrderWithStaffCodeBargainButNoRunningTask() throws Throwable {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        StaffCodeBargainBO staffCodeBargainBO = new StaffCodeBargainBO();
        staffCodeBargainBO.setStaffBargainCodeId(1L);
        orderInfoBO.setStaffCodeBargainBO(staffCodeBargainBO);
        RemoteResponse<List<ReceiveRecordDTO>> response = RemoteResponse.<List<ReceiveRecordDTO>>custom().setData(Collections.<ReceiveRecordDTO>emptyList()).build();
        when(staffBargainService.queryReceiveRecord(any())).thenReturn(response);
        OrderChannelTaskRunningResultBO expectedResult = OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        OrderChannelTaskRunningResultBO actualResult = staffBargainOrdersChannelTaskRunner.calculate(taskParam, orderInfoBO, task);
        assertEquals(expectedResult.getCalculateResult(), actualResult.getCalculateResult());
        assertEquals(expectedResult.getBusinessChannel(), actualResult.getBusinessChannel());
        assertEquals(expectedResult.getIntentionType(), actualResult.getIntentionType());
    }

    @Test
    public void testCalculateOrderWithStaffCodeBargainAndRunningTaskButNotMatched() throws Throwable {
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        StaffCodeBargainBO staffCodeBargainBO = new StaffCodeBargainBO();
        staffCodeBargainBO.setStaffBargainCodeId(1L);
        orderInfoBO.setStaffCodeBargainBO(staffCodeBargainBO);
        orderInfoBO.setPlatform(1);
        orderInfoBO.setUserId(123L);
        List<ReceiveRecordDTO> records = new ArrayList<>();
        ReceiveRecordDTO record = new ReceiveRecordDTO();
        // Different platform
        record.setPlatform(2);
        record.setUserId(123L);
        records.add(record);
        RemoteResponse<List<ReceiveRecordDTO>> response = RemoteResponse.<List<ReceiveRecordDTO>>custom().setData(records).build();
        when(staffBargainService.queryReceiveRecord(any())).thenReturn(response);
        OrderChannelTaskRunningResultBO expectedResult = OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        OrderChannelTaskRunningResultBO actualResult = staffBargainOrdersChannelTaskRunner.calculate(taskParam, orderInfoBO, task);
        assertEquals(expectedResult.getCalculateResult(), actualResult.getCalculateResult());
        assertEquals(expectedResult.getBusinessChannel(), actualResult.getBusinessChannel());
        assertEquals(expectedResult.getIntentionType(), actualResult.getIntentionType());
    }

    @Test
    public void testCalculateOrderWithStaffCodeBargainAndRunningTaskAndMatched() throws Throwable {
        // Setup task param and order info
        IntentionCalculateTaskParamBO taskParam = new IntentionCalculateTaskParamBO();
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        // Setup staff code bargain
        StaffCodeBargainBO staffCodeBargainBO = new StaffCodeBargainBO();
        staffCodeBargainBO.setStaffBargainCodeId(1L);
        staffCodeBargainBO.setTechId(100);
        orderInfoBO.setStaffCodeBargainBO(staffCodeBargainBO);
        // Setup order info
        orderInfoBO.setPlatform(1);
        orderInfoBO.setUserId(123L);
        Date payTime = new Date();
        orderInfoBO.setPayTime(payTime);
        // Setup task
        DistributionOrderChannelCalRunningTaskWithBLOBs task = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        // Setup receive record
        List<ReceiveRecordDTO> records = new ArrayList<>();
        ReceiveRecordDTO record = new ReceiveRecordDTO();
        // Same platform as order
        record.setPlatform(1);
        // Same user as order
        record.setUserId(123L);
        records.add(record);
        // Setup mock responses
        RemoteResponse<List<ReceiveRecordDTO>> response = RemoteResponse.<List<ReceiveRecordDTO>>custom().setData(records).setMsg("success").setCode(200).build();
        when(staffBargainService.queryReceiveRecord(any())).thenReturn(response);
        // Execute
        OrderChannelTaskRunningResultBO actualResult = staffBargainOrdersChannelTaskRunner.calculate(taskParam, orderInfoBO, task);
        // Verify
        assertEquals(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode(), actualResult.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.STAFF_BARGAIN_MULTIPLE_ORDERS.getCode(), actualResult.getBusinessChannel());
        assertEquals(IntentionTypeEnum.STRONG_INTENTION.getCode(), actualResult.getIntentionType());
        assertNotNull(actualResult.getDistributor());
        assertEquals(100, actualResult.getDistributor().getDistributorId());
        assertEquals(1L, actualResult.getDistributor().getDistributionProductId());
        assertEquals(payTime, actualResult.getIntentionCreateTime());
    }
}
