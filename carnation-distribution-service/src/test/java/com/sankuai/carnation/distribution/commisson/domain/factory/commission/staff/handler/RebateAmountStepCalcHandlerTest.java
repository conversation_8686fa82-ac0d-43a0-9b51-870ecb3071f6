package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.handler;

import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleStepConfigDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleStepRuleDTO;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class RebateAmountStepCalcHandlerTest {

    @InjectMocks
    private RebateAmountStepCalcHandler rebateAmountStepCalcHandler;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;

    @Mock
    private RedisStoreClient redisStoreClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：Redis中不存在重复用户，数据库中不存在重复用户，满足阶梯规则
     */
    @Test
    public void testCalculate_UserNotExistsAndSatisfyStepRule() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");
        request.setTechApplyRecordId(1L);
        request.setUserId(1L);
        request.setPlatform(1);
        request.setPayCent(1000L);

        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        when(redisStoreClient.setnx(any(StoreKey.class), any())).thenReturn(true);
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(anyLong(), anyLong(), anyInt(), any())).thenReturn(new ArrayList<>());
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityId(anyLong())).thenReturn(1L);

        // act
        RemoteResponse<RebateAmountResult> response = rebateAmountStepCalcHandler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(100L, response.getData().getRebateAmount());
    }



    /**
     * 测试场景：Redis中存在重复用户
     */
    @Test
    public void testCalculate_UserExistsInRedis() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");

        when(redisStoreClient.setnx(any(StoreKey.class), any())).thenReturn(false);

        // act
        RemoteResponse<RebateAmountResult> response = rebateAmountStepCalcHandler.calculate(request, null);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("用户存在重复", response.getData().getReason());
    }

    /**
     * 测试场景：数据库中存在重复用户
     */
    @Test
    public void testCalculate_UserExistsInDatabase() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");
        request.setTechApplyRecordId(1L);
        request.setUserId(1L);
        request.setPlatform(1);

        List<RbOrderVerifyRebateCommission> existingUsers = new ArrayList<>();
        existingUsers.add(new RbOrderVerifyRebateCommission());

        when(redisStoreClient.setnx(any(StoreKey.class), any())).thenReturn(true);
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(anyLong(), anyLong(), anyInt(), any())).thenReturn(existingUsers);

        // act
        RemoteResponse<RebateAmountResult> response = rebateAmountStepCalcHandler.calculate(request, null);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("用户存在重复", response.getData().getReason());
    }

    /**
     * 测试场景：不满足任何阶梯规则
     */
    @Test
    public void testCalculate_NotSatisfyAnyStepRule() {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("orderId");
        request.setTechApplyRecordId(1L);
        request.setUserId(1L);
        request.setPlatform(1);
        request.setPayCent(1000L);

        RebateActivityRuleDTO rebateActivityRuleDTO = getRebateActivityRuleDTO();

        when(redisStoreClient.setnx(any(StoreKey.class), any())).thenReturn(true);
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(anyLong(), anyLong(), anyInt(), any())).thenReturn(new ArrayList<>());
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityId(anyLong())).thenReturn(2L);

        // act
        RemoteResponse<RebateAmountResult> response = rebateAmountStepCalcHandler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("不满足活动规则", response.getData().getReason());
    }

    @NotNull
    private RebateActivityRuleDTO getRebateActivityRuleDTO() {
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleDTO = new RebateSettleRuleDTO();

        RebateSettleStepRuleDTO stepRule = new RebateSettleStepRuleDTO();
        RebateSettleRuleStepConfigDTO rebateSettleRuleStepConfigDTO = new RebateSettleRuleStepConfigDTO();
        rebateSettleRuleStepConfigDTO.setStepIndex(1);
        rebateSettleRuleStepConfigDTO.setStepStart(1L);
        rebateSettleRuleStepConfigDTO.setStepEnd(2L);
        rebateSettleRuleStepConfigDTO.setRebateAmount(100L);

        stepRule.setRuleStep(Lists.newArrayList(rebateSettleRuleStepConfigDTO));
        ruleDTO.setStepRule(stepRule);
        rebateActivityRuleDTO.setRule(ruleDTO);
        return rebateActivityRuleDTO;
    }

}
