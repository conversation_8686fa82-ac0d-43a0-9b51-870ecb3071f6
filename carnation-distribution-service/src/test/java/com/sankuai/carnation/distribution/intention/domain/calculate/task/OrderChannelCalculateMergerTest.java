package com.sankuai.carnation.distribution.intention.domain.calculate.task;

import com.alibaba.fastjson.JSON;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelTaskStatusEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelRunningTaskDataService;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelTaskDataService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class OrderChannelCalculateMergerTest {

    @InjectMocks
    private OrderChannelCalculateMerger orderChannelCalculateMerger;

    @Mock
    private OrderChannelTaskDataService taskDataService;

    @Mock
    private OrderChannelRunningTaskDataService runningTaskDataService;

    @Mock
    private  OrderInfoAdaptor orderInfoAdaptor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    /**
     * 测试场景：当任务未结束或强制计算时，但没有运行中的任务
     */
    @Test
    public void testMerge_WithNoRunningTask() {
        // arrange
        DistributionOrderChannelCalTaskWithBLOBs task = new DistributionOrderChannelCalTaskWithBLOBs();
        task.setStatus(OrderChannelTaskStatusEnum.RUNNING.getCode());
        task.setOrderType(1);
        task.setOrderId("123");
        when(taskDataService.forceGetTaskByTaskId(anyLong())).thenReturn(task);
        when(runningTaskDataService.getRunningTaskByParentTaskIdFromMaster(anyLong())).thenReturn(Collections.emptyList());
        when(orderInfoAdaptor.getOrder(anyInt(),anyString())).thenReturn(new OrderInfoBO());

        // act
        OrderChannelTaskResultBO result = orderChannelCalculateMerger.merge(1L, true);

        // assert
        assertNotNull(result);
    }

    @Test
    public void testMerge_WithNonEmptyResultMap() {
        // Arrange
        long taskId = 1L;
        boolean forceCalculate = true;

        DistributionOrderChannelCalTaskWithBLOBs task = new DistributionOrderChannelCalTaskWithBLOBs();
        task.setStatus(OrderChannelTaskStatusEnum.RUNNING.getCode());
        task.setOrderType(1);
        task.setOrderId("123");
        when(taskDataService.forceGetTaskByTaskId(taskId)).thenReturn(task);

        List<DistributionOrderChannelCalRunningTaskWithBLOBs> runningTasks = new ArrayList<>();
        DistributionOrderChannelCalRunningTaskWithBLOBs runningTask1 = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        runningTask1.setStatus(OrderChannelTaskStatusEnum.ENDED.getCode());
        runningTask1.setTaskType(OrderChannelCalculateTaskTypeEnum.PROMO_CODE_INTENTION.getCode());
        runningTask1.setOrderType(1);
        runningTask1.setOrderId("123");
        runningTask1.setCalTaskId(123L);
        runningTask1.setId(123L);
        runningTask1.setResult("{\"calculateResult\":1,\"businessChannel\":\"CHANNEL_A\",\"runningTaskStatus\":2}");
        runningTasks.add(runningTask1);

        DistributionOrderChannelCalRunningTaskWithBLOBs runningTask2 = new DistributionOrderChannelCalRunningTaskWithBLOBs();
        runningTask2.setStatus(OrderChannelTaskStatusEnum.RUNNING.getCode());
        runningTask2.setTaskType(OrderChannelCalculateTaskTypeEnum.COMMUNITY.getCode());
        runningTask2.setResult("{\"calculateResult\":2,\"businessChannel\":\"CHANNEL_B\",\"runningTaskStatus\":1}");
        runningTask2.setOrderType(1);
        runningTask2.setOrderId("123");
        runningTask2.setCalTaskId(123L);
        runningTask2.setId(124L);
        runningTasks.add(runningTask2);

        when(runningTaskDataService.getRunningTaskByParentTaskIdFromMaster(taskId)).thenReturn(runningTasks);

        OrderInfoBO orderInfo = new OrderInfoBO();
        when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(orderInfo);

        // Act
        OrderChannelTaskResultBO result = orderChannelCalculateMerger.merge(taskId, forceCalculate);

        // Assert
        assertNotNull(result);
        assertEquals(IntentionCalculateResultEnum.WAIT_FOR_CONFIRM.getCode(), result.getCalculateResult());
        assertEquals(DistributionBusinessChannelEnum.UNKNOWN.getCode(), result.getBusinessChannel());

    }

}
