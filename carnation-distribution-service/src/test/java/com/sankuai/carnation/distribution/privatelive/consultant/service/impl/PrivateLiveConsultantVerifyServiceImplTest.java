package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveConsultantVerifyService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import org.junit.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantVerifyServiceImplTest {

    @Mock
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @InjectMocks
    private PrivateLiveConsultantVerifyServiceImpl privateLiveConsultantVerifyService;

    @Mock
    private DzPrivateLiveConsultantVerifyService dzPrivateLiveConsultantVerifyService;

    private final String unionId = "testUnionId";
    private final long consultantTaskId = 1L;
    private final long consultantId = 2L;

    @Before
    public void setUp() {
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setConsultantId(consultantId);

        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setUnionId(unionId);

        when(privateLiveConsultantTaskRepository.loadById(consultantTaskId)).thenReturn(task);

        when(privateLiveConsultantAccountRepository.loadById(consultantId)).thenReturn(account);
    }

    /**
     * 测试任务和账户都存在，且unionId匹配的情况
     */
    @Test
    public void testCheckByUnionIdAndTaskIdOld_Success() {
        // arrange

        // act
        RemoteResponse<Boolean> response = privateLiveConsultantVerifyService.checkByUnionIdAndTaskIdOld(unionId, consultantTaskId);

        // assert
        assertTrue(response.getData());
    }


    @Test
    public void testCheckByUnionIdAndTaskIdNew_ReturnsSuccess() {
        // arrange
        String unionId = "testUnionId";
        long consultantTaskId = 123L;
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveConsultantVerifyService.checkByUnionIdAndTaskId(unionId, consultantTaskId)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = privateLiveConsultantVerifyService.checkByUnionIdAndTaskIdNew(unionId, consultantTaskId);

        // assert
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveConsultantVerifyService, times(1)).checkByUnionIdAndTaskId(unionId, consultantTaskId);
    }

    @Test
    public void testGetUnionIdByTaskIdOld_Success() {
        // arrange
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setConsultantId(1L);
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setUnionId("unionId");
        when(privateLiveConsultantTaskRepository.loadById(anyLong())).thenReturn(task);
        when(privateLiveConsultantAccountRepository.loadById(anyLong())).thenReturn(account);

        // act
        RemoteResponse<String> response = privateLiveConsultantVerifyService.getUnionIdByTaskIdOld(1L);

        // assert
        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertEquals("unionId", response.getData());
    }

    @Test
    public void testGetUnionIdByTaskIdNewSuccess() throws Throwable {
        // arrange
        long consultantTaskId = 1L;
        RemoteResponse<String> unionId1 = RemoteResponse.success("unionId");
        when(dzPrivateLiveConsultantVerifyService.getUnionIdByTaskId(consultantTaskId)).thenReturn(unionId1);

        // act
        RemoteResponse<String> actualResponse = privateLiveConsultantVerifyService.getUnionIdByTaskIdNew(consultantTaskId);

        // assert
        assertEquals("unionId", actualResponse.getData());
    }

    @Test
    public void testGetUnionIdsByLiveIdOld_WhenAccountListIsEmpty() {
        // arrange
        String liveId = "testLiveId";
        List<PrivateLiveConsultantTask> tasks = Arrays.asList(
                new PrivateLiveConsultantTask(1L, 2L, "group1", liveId, 3L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", 4L, "wechatNumber", ConsultantTaskApproveStatusEnum.PASS.getCode(), null, null, 1)
        );
        when(privateLiveConsultantTaskRepository.pageLoadByLiveId(liveId, 0, 10000)).thenReturn(tasks);
        when(privateLiveConsultantAccountRepository.batchLoadById(anyList())).thenReturn(Collections.emptyList());

        // act
        RemoteResponse<List<String>> result = privateLiveConsultantVerifyService.getUnionIdsByLiveIdOld(liveId);

        // assert
        assertTrue(result.getData().isEmpty());
        verify(privateLiveConsultantTaskRepository, times(1)).pageLoadByLiveId(liveId, 0, 10000);
        verify(privateLiveConsultantAccountRepository, times(1)).batchLoadById(anyList());
    }

    @Test
    public void testGetUnionIdsByLiveIdNew_ReturnsNonEmptyList() {
        // arrange
        String liveId = "validLiveId";
        List<String> expectedUnionIds = Arrays.asList("unionId1", "unionId2");
        RemoteResponse<List<String>> expectedResponse = RemoteResponse.success(Collections.emptyList());
        when(dzPrivateLiveConsultantVerifyService.getUnionIdsByLiveId(liveId)).thenReturn(expectedResponse);

        // act
        RemoteResponse<List<String>> actualResponse = privateLiveConsultantVerifyService.getUnionIdsByLiveIdNew(liveId);

        // assert
        assertNotNull(actualResponse);
    }


    @Test
    public void testCheckByUnionIdAndLiveIdOldSuccess() {
        // arrange
        String unionId = "unionId";
        String liveId = "liveId";
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setId(1L);
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setId(2L);
        task.setStatus(ConsultantTaskApproveStatusEnum.PASS.getCode());
        when(privateLiveConsultantAccountRepository.loadByUnionId(unionId)).thenReturn(account);
        when(privateLiveConsultantTaskRepository.loadByConsultantIdAndLiveId(account.getId(), liveId)).thenReturn(task);

        // act
        RemoteResponse<Long> result = privateLiveConsultantVerifyService.checkByUnionIdAndLiveIdOld(unionId, liveId);

        // assert
        assertTrue(result.isSuccess());
        assertEquals(Long.valueOf(2L), result.getData());
    }

    @Test
    public void testCheckByUnionIdAndLiveIdNewSuccess() {
        // arrange
        String unionId = "testUnionId";
        String liveId = "testLiveId";
        RemoteResponse<Long> success = RemoteResponse.success(1L);
        when(dzPrivateLiveConsultantVerifyService.checkByUnionIdAndLiveId(unionId, liveId)).thenReturn(success);

        // act
        RemoteResponse<Long> actualResponse = privateLiveConsultantVerifyService.checkByUnionIdAndLiveIdNew(unionId, liveId);

        // assert
        assertEquals(success, actualResponse);
        verify(dzPrivateLiveConsultantVerifyService, times(1)).checkByUnionIdAndLiveId(unionId, liveId);
    }

    @Test
    public void testCheckConsultantValidOld_TaskStatusPass() throws Throwable {
        // arrange
        long consultantTaskId = 1L;
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setStatus(ConsultantTaskApproveStatusEnum.PASS.getCode());
        when(privateLiveConsultantTaskRepository.loadById(consultantTaskId)).thenReturn(task);

        // act
        RemoteResponse<Boolean> result = privateLiveConsultantVerifyService.checkConsultantValidOld(consultantTaskId);

        // assert
        assertTrue(result.getData());
    }

    @Test
    public void testCheckConsultantValidNew_ReturnsSuccess() {
        long consultantTaskId = 1L;
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveConsultantVerifyService.checkConsultantValid(consultantTaskId)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = privateLiveConsultantVerifyService.checkConsultantValidNew(consultantTaskId);

        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.getData());
        verify(dzPrivateLiveConsultantVerifyService, times(1)).checkConsultantValid(consultantTaskId);
    }

}