package com.sankuai.carnation.distribution.distributor.dto.response;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * DistributorRejectAuditResponse 类的单元测试
 */
public class DistributorRejectAuditResponseTest {

    /**
     * 测试无参构造函数
     */
    @Test
    public void testNoArgsConstructor() {
        DistributorRejectAuditResponse response = new DistributorRejectAuditResponse();
        assertNull(response.getDistributorId());
        assertNull(response.getStatus());
    }

    /**
     * 测试全参构造函数
     */
    @Test
    public void testAllArgsConstructor() {
        DistributorRejectAuditResponse response = new DistributorRejectAuditResponse(1L, 0);
        assertEquals(Long.valueOf(1), response.getDistributorId());
        assertEquals(Integer.valueOf(0), response.getStatus());
    }

    /**
     * 测试 Builder 模式
     */
    @Test
    public void testBuilder() {
        DistributorRejectAuditResponse response = DistributorRejectAuditResponse.builder()
                .distributorId(1L)
                .status(0)
                .build();
        assertEquals(Long.valueOf(1), response.getDistributorId());
        assertEquals(Integer.valueOf(0), response.getStatus());
    }

    /**
     * 测试 setter 和 getter 方法
     */
    @Test
    public void testSettersAndGetters() {
        DistributorRejectAuditResponse response = new DistributorRejectAuditResponse();
        response.setDistributorId(2L);
        response.setStatus(1);
        assertEquals(Long.valueOf(2), response.getDistributorId());
        assertEquals(Integer.valueOf(1), response.getStatus());
    }
}
