package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.event.api.enums.RebateRuleCategoryEnum;
import com.google.common.collect.Maps;
import com.meituan.data.uss.thrift.*;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateCustomerNewStatusRequest;
import com.sankuai.carnation.distribution.common.bo.UssReturnInfoItem;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RebateNewOldCustomerServiceTest {

    @Mock
    private USSServiceV2.Iface ussClient;

    @InjectMocks
    private RebateNewOldCustomerService service;

    /**
     * 测试请求有效，用户是新客户
     */
    @Test
    public void testValidNewCustomerStatus_ValidNewCustomer() throws Throwable {
        // arrange
        RebateCustomerNewStatusRequest request = createValidRequest();
        QueryByIdsResponse response = createValidResponse();
        when(ussClient.queryByIds(any(QueryByIdsRequest.class))).thenReturn(response);

        // act
        boolean result = service.validNewCustomerStatus(request);

        // assert
        assertTrue(result);
    }

    /**
     * 测试用户不是新客户
     */
    @Test
    public void testValidNewCustomerStatus_NotNewCustomer() throws Throwable {
        // arrange
        RebateCustomerNewStatusRequest request = createValidRequest();
        QueryByIdsResponse response = createResponseNotNewCustomer();
        when(ussClient.queryByIds(any(QueryByIdsRequest.class))).thenReturn(response);

        // act
        boolean result = service.validNewCustomerStatus(request);

        // assert
        assertFalse(result);
    }


    /**
     * 测试请求无效
     */
    @Test
    public void testValidNewCustomerStatus_InvalidRequest() throws Throwable {
        // arrange
        RebateCustomerNewStatusRequest request = new RebateCustomerNewStatusRequest();

        // act
        boolean result = service.validNewCustomerStatus(request);

        // assert
        assertFalse(result);
    }

    /**
     * 测试查询异常
     */
    @Test
    public void testValidNewCustomerStatus_QueryException() throws Throwable {
        // arrange
        RebateCustomerNewStatusRequest request = createValidRequest();
        when(ussClient.queryByIds(any(QueryByIdsRequest.class))).thenThrow(new RuntimeException());

        // act
        boolean result = service.validNewCustomerStatus(request);

        // assert
        assertFalse(result);
    }

    /**
     * 测试查询结果无效
     */
    @Test
    public void testValidNewCustomerStatus_InvalidQueryResult() throws Throwable {
        // arrange
        RebateCustomerNewStatusRequest request = createValidRequest();
        QueryByIdsResponse response = new QueryByIdsResponse();
        response.setStatusCode(ResponseStatus.BAD_REQUEST);
        when(ussClient.queryByIds(any(QueryByIdsRequest.class))).thenReturn(response);

        // act
        boolean result = service.validNewCustomerStatus(request);

        // assert
        assertFalse(result);
    }


    /**
     * 创建一个有效的 RebateCustomerNewStatusRequest 对象
     *
     * @return 有效的 RebateCustomerNewStatusRequest 实例
     */
    private RebateCustomerNewStatusRequest createValidRequest() {
        RebateCustomerNewStatusRequest request = new RebateCustomerNewStatusRequest();
        request.setUserId(12345L); // 假设的有效用户ID
        request.setPlatform(1);
        request.setCategoryEnum(RebateRuleCategoryEnum.LIREN);
        request.setBuySuccessTime(new Date());
        return request;
    }

    /**
     * 创建一个表示查询成功且用户为新客户的 QueryByIdsResponse 对象
     *
     * @return 查询成功且用户为新客户的 QueryByIdsResponse 实例
     */
    private QueryByIdsResponse createValidResponse() {
        QueryByIdsResponse response = new QueryByIdsResponse();
        response.setStatusCode(ResponseStatus.OK); // 假设0表示查询成功

        Map<QueryByIdsTag, String> value = Maps.newHashMap();
        QueryByIdsTag queryByIdsTag = new QueryByIdsTag();
        queryByIdsTag.setTagName(RebateRuleCategoryEnum.LIREN.dpDesc);

        UssReturnInfoItem ussReturnInfoItem = new UssReturnInfoItem();
        ussReturnInfoItem.setCnt(1);

        value.put(queryByIdsTag, JSON.toJSONString(ussReturnInfoItem));
        response.setValue(value); // 用户是新客户
        return response;
    }

    /**
     * 创建一个表示查询成功但用户不是新客户的 QueryByIdsResponse 对象
     *
     * @return 查询成功但用户不是新客户的 QueryByIdsResponse 实例
     */
    private QueryByIdsResponse createResponseNotNewCustomer() {
        QueryByIdsResponse response = new QueryByIdsResponse();
        response.setStatusCode(ResponseStatus.OK); // 假设0表示查询成功

        Map<QueryByIdsTag, String> value = Maps.newHashMap();
        QueryByIdsTag queryByIdsTag = new QueryByIdsTag();
        queryByIdsTag.setTagName(RebateRuleCategoryEnum.LIREN.dpDesc);

        UssReturnInfoItem ussReturnInfoItem = new UssReturnInfoItem();
        ussReturnInfoItem.setCnt(2);
        ussReturnInfoItem.setTs(System.currentTimeMillis() / 1000 - 100);

        value.put(queryByIdsTag, JSON.toJSONString(ussReturnInfoItem));
        response.setValue(value);  // 用户是老客户
        return response;
    }

}
