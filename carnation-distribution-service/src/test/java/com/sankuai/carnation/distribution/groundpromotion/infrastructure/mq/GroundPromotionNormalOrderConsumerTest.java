package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.order.common.beans.OrderStatusMessageDTO;
import com.dianping.pay.order.domain.enums.OrderStatusAction;
import com.dianping.pay.order.domain.enums.ProductEnum;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.swallow.common.message.Message;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveOrderIntentionServiceImpl;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2024/12/27
 */
@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionNormalOrderConsumerTest {

    @InjectMocks
    private GroundPromotionNormalOrderConsumer consumer;

    @Mock
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    @Mock
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Mock
    private RedisStoreClient redisStoreClient;

    private RemoteResponse<Boolean> successResponse;


    private static final MockedStatic<Lion> mockLionFactory = Mockito.mockStatic(Lion.class);


    @BeforeClass
    public static void mockStatic() {
        mockLionFactory.when(() -> Lion.getBoolean(eq(Environment.getAppName()), eq("privatelive.trade.order.refund.handle.switch"), eq(true))).thenReturn(true);
    }

    @AfterClass
    public static void closeStatic() {
        mockLionFactory.close();
    }

    @Before
    public void setUp() {
        successResponse = RemoteResponse.success(true);
    }

    /**
     * 使用反射调用私有方法
     */
    private void invokeProcess(Message message) throws Exception {
        Method handleMethod = GroundPromotionNormalOrderConsumer.class.getDeclaredMethod("process", Message.class);
        handleMethod.setAccessible(true);
        handleMethod.invoke(consumer, message);
    }

    @Test
    public void testPrivateLiveCancelIntentionResultNotNullHandle() throws Exception {
        // 模拟MafkaMessage
        Message message = mock(Message.class);

        // arrange
        OrderStatusMessageDTO orderStatusMessageDTO = new OrderStatusMessageDTO();
        orderStatusMessageDTO.setUnifiedOrderId("12344");
        orderStatusMessageDTO.setProductType(ProductEnum.insurance_prepay.productCode);
        orderStatusMessageDTO.setOrderStatusAction(OrderStatusAction.cancel.name());
        orderStatusMessageDTO.setActionTime(new Date());
        String messageBody = JSON.toJSONString(orderStatusMessageDTO);

        Mockito.when(message.getContent()).thenReturn(messageBody);

        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("12344");
        orderIntentionResult.setStatus(OrderStatusEnum.CREATE.getCode());
        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);

        // act
        this.invokeProcess(message);

        // assert
        verify(orderIntentionResultRepository, times(1)).forceGetByOrderId(anyString());
    }

    @Test
    public void testPrivateLiveCancelIntentionResultIsNullHandle() throws Exception {
        // 模拟MafkaMessage
        Message message = mock(Message.class);

        // arrange
        OrderStatusMessageDTO orderStatusMessageDTO = new OrderStatusMessageDTO();
        orderStatusMessageDTO.setUnifiedOrderId("12344");
        orderStatusMessageDTO.setProductType(ProductEnum.insurance_prepay.productCode);
        orderStatusMessageDTO.setOrderStatusAction(OrderStatusAction.cancel.name());
        orderStatusMessageDTO.setActionTime(new Date());
        String messageBody = JSON.toJSONString(orderStatusMessageDTO);

        Mockito.when(message.getContent()).thenReturn(messageBody);

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(null);

        // act
        this.invokeProcess(message);

        // assert
        verify(orderIntentionResultRepository, times(1)).forceGetByOrderId(anyString());
    }

    private void invokeRetryProcessCancelOrder(String orderId, OrderStatusMessageDTO orderStatusMessageDTO, Long expirationTime) throws Exception {
        Method handleMethod = GroundPromotionNormalOrderConsumer.class.getDeclaredMethod("retryProcessCancelOrder"
                , String.class, OrderStatusMessageDTO.class, Long.class);
        handleMethod.setAccessible(true);
        handleMethod.invoke(consumer, orderId, orderStatusMessageDTO, expirationTime);
    }

    @Test
    public void testRetryProcessCancelOrderResultIsNull() throws Exception {
        OrderStatusMessageDTO orderStatusMessageDTO = new OrderStatusMessageDTO();
        orderStatusMessageDTO.setUnifiedOrderId("12344");
        orderStatusMessageDTO.setProductType(ProductEnum.insurance_prepay.productCode);
        orderStatusMessageDTO.setOrderStatusAction(OrderStatusAction.cancel.name());
        orderStatusMessageDTO.setActionTime(new Date());

        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderStatusMessageDTO.getUnifiedOrderId() + orderStatusMessageDTO.getOrderStatusAction() + orderStatusMessageDTO.getActionTime().toString()
                , OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(null);

        long expirationTime = System.currentTimeMillis() + 8000L;
        // act
        this.invokeRetryProcessCancelOrder("1234", orderStatusMessageDTO, expirationTime);

        // assert
        verify(redisStoreClient, times(1)).delete(storeKey);

    }

    @Test
    public void testRetryProcessCancelOrderResultNotNull() throws Exception {
        OrderStatusMessageDTO orderStatusMessageDTO = new OrderStatusMessageDTO();
        orderStatusMessageDTO.setUnifiedOrderId("12344");
        orderStatusMessageDTO.setProductType(ProductEnum.insurance_prepay.productCode);
        orderStatusMessageDTO.setOrderStatusAction(OrderStatusAction.cancel.name());
        orderStatusMessageDTO.setActionTime(new Date());

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(new PrivateLiveOrderIntentionResult());
        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult(any(PrivateLiveOrderUpdateInfo.class))).thenReturn(successResponse);

        long expirationTime = System.currentTimeMillis() + 8000L;
        // act
        this.invokeRetryProcessCancelOrder("1234", orderStatusMessageDTO, expirationTime);

        // assert
        verify(privateLiveOrderIntentionService, times(1)).updateIntentionResult(any(PrivateLiveOrderUpdateInfo.class));
    }
}