package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.CommissionRateGateRuleGroupBO;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import java.util.Date;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.carnation.distribution.commisson.settle.rule.ShopCommissionRuleDomainService;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.promocode.acl.TechCateAndIndustryAclService;
import com.sankuai.carnation.distribution.promocode.acl.ShopApolloBuAclService;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.CommissionRateGateRuleBO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith;
import static org.mockito.Mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplCalculateCommissionRateRuleGroupTest {

    @Spy
    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private ShopCommissionRuleDomainService shopCommissionRuleDomainService;

    @Mock
    private OrderInfoAdaptor orderInfoAdaptor;

    @Mock
    private TechCateAndIndustryAclService techCateAndIndustryAclService;

    @Mock
    private ShopApolloBuAclService shopApolloBuAclService;

    @Test
    public void testCalculateCommissionRateRuleGroupWhenRuleListIsEmpty() throws Throwable {
        // Given
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        doReturn(Collections.emptyList()).when(promoCodeBcpCheckService).getCommissionRateRuleGroupList();
        // When
        CommissionRateGateRuleGroupBO result = promoCodeBcpCheckService.calculateCommissionRateRuleGroup(request);
        // Then
        assertNull("The result should be null when the rule list is empty", result);
    }

    @Test
    public void testCalculateCommissionRateRuleGroupWhenAllRulesDoNotMatch() throws Throwable {
        // Given
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("123");
        request.setTechId(1);
        request.setDpShopId(1L);
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderTime(new Date());
        when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(orderInfoBO);
        when(techCateAndIndustryAclService.getTechIndustry(anyInt())).thenReturn(2);
        when(shopApolloBuAclService.getBusinessDepartment(anyLong())).thenReturn(null);
        CommissionRateGateRuleGroupBO ruleGroupBO = new CommissionRateGateRuleGroupBO();
        ruleGroupBO.setTechIdList(Arrays.asList(2));
        ruleGroupBO.setTechIndustryList(Arrays.asList(1));
        ruleGroupBO.setShopDepartmentList(Collections.emptyList());
        ruleGroupBO.setPriority(1);
        doReturn(Arrays.asList(ruleGroupBO)).when(promoCodeBcpCheckService).getCommissionRateRuleGroupList();
        // When
        CommissionRateGateRuleGroupBO result = promoCodeBcpCheckService.calculateCommissionRateRuleGroup(request);
        // Then
        assertNull("The result should be null when all rules do not match", result);
    }

    @Test
    public void testCalculateCommissionRateRuleGroupWhenSomeRulesMatch() throws Throwable {
        // Given
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("123");
        request.setTechId(1);
        request.setDpShopId(1L);
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderTime(new Date());
        when(orderInfoAdaptor.getOrder(anyInt(), anyString())).thenReturn(orderInfoBO);
        when(techCateAndIndustryAclService.getTechIndustry(anyInt())).thenReturn(1);
        when(shopApolloBuAclService.getBusinessDepartment(anyLong())).thenReturn(null);
        CommissionRateGateRuleGroupBO ruleGroupBO = new CommissionRateGateRuleGroupBO();
        ruleGroupBO.setTechIdList(Arrays.asList(1));
        ruleGroupBO.setTechIndustryList(Arrays.asList(1));
        ruleGroupBO.setShopDepartmentList(Collections.emptyList());
        ruleGroupBO.setPriority(1);
        doReturn(Arrays.asList(ruleGroupBO)).when(promoCodeBcpCheckService).getCommissionRateRuleGroupList();
        // When
        CommissionRateGateRuleGroupBO result = promoCodeBcpCheckService.calculateCommissionRateRuleGroup(request);
        // Then
        assertNotNull("The result should not be null when some rules match", result);
    }
}
