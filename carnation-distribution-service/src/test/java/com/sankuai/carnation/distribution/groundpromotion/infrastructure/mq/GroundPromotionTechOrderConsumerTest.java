package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.meituan.hotel.dlm.service.IDistributedLockManager;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.carnation.distribution.common.acl.UnifiedOrderAclService;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveOrderIntentionServiceImpl;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import com.sankuai.technician.trade.api.order.enums.OrderLogBizTypeEnum;
import com.sankuai.technician.trade.api.order.enums.OrderLogOptTypeEnum;
import com.sankuai.technician.trade.api.order.model.orderreceiptext.PrepayOrderModel;
import com.sankuai.technician.trade.api.order.model.orderreceiptext.PrivateLiveModel;
import com.sankuai.technician.trade.types.enums.OrderDistributionTypeEnum;
import com.sankuai.technician.trade.types.topic.OrderOperateNotify;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/2
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class GroundPromotionTechOrderConsumerTest {

    @InjectMocks
    private GroundPromotionTechOrderConsumer consumer;

    @Mock
    private ShopMapperService shopMapperService;

    @Mock
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    @Mock
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Mock
    private RedisStoreClient redisStoreClient;

    @Mock
    private RedisLockService redisLockService;

    @Mock
    private IDistributedLockManager distributedLockManager;

    private RemoteResponse<Boolean> successResponse;

    @Before
    public void setUp() {
        successResponse = RemoteResponse.success(true);
    }

    /**
     * 使用反射调用私有方法
     */
    private ConsumeStatus invokeHandle(MafkaMessage message, MessagetContext context) throws Exception {
        Method handleMethod = GroundPromotionTechOrderConsumer.class.getDeclaredMethod("handle", MafkaMessage.class,
                MessagetContext.class);
        handleMethod.setAccessible(true);
        return (ConsumeStatus) handleMethod.invoke(consumer, message, context);
    }

    @Test
    public void testPrivateLiveCreateHandle() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        OrderOperateNotify orderOperateNotify = new OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.CREATE.getCode());
        String messageBody = JSON.toJSONString(orderOperateNotify);

        Mockito.when(message.getBody()).thenReturn(messageBody);
        Mockito.when(shopMapperService.mt2dp(anyLong())).thenReturn(123L);

        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");
        orderIntentionResult.setStatus(OrderStatusEnum.CREATE.getCode());

        // act
        ConsumeStatus status = this.invokeHandle(message, context);

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
    }

    @Test
    public void testPrivateLiveCancelHandle() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        OrderOperateNotify orderOperateNotify = new OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.CANCEL.getCode());
        String messageBody = JSON.toJSONString(orderOperateNotify);

        Mockito.when(message.getBody()).thenReturn(messageBody);
        Mockito.when(shopMapperService.mt2dp(anyLong())).thenReturn(123L);

        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            mockedStatic.when(() -> Lion.getBoolean(anyString(), eq("privatelive.trade.order.refund.handle.switch"), anyBoolean())).thenReturn(true);

            // act
            ConsumeStatus status = this.invokeHandle(message, context);

            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);

        }
    }

    @Test
    public void testPrivateLiveVerifyHandle() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        PrepayOrderModel prepayOrderModel = new PrepayOrderModel();
        prepayOrderModel.setMainOrderType(2);
        prepayOrderModel.setMainOrderId("123444");

        Map<String, String> map = Maps.newHashMap();
        map.put("prepayOrderModel", JsonUtil.toJson(prepayOrderModel));

        // arrange
        OrderOperateNotify orderOperateNotify = new OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(map);
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.VERIFY.getCode());
        String messageBody = JSON.toJSONString(orderOperateNotify);

        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");

        Mockito.when(message.getBody()).thenReturn(messageBody);
        Mockito.when(shopMapperService.mt2dp(anyLong())).thenReturn(123L);

        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            // 配置静态方法的期望行为
            mockedStatic.when(() -> Lion.getBoolean(eq(Environment.getAppName()), eq("privatelive.order.receipt.operate.handle.switch")
                    , eq(false))).thenReturn(true);

            // act
            ConsumeStatus status = this.invokeHandle(message, context);

            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
        }

    }

    @Test
    public void testPrivateLiveHandleV2() throws Exception {
        // 模拟MafkaMessage和MessagetContext
        MafkaMessage message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);

        // arrange
        OrderOperateNotify orderOperateNotify = new OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.CREATE.getCode());
        String messageBody = JSON.toJSONString(orderOperateNotify);

        Mockito.when(message.getBody()).thenReturn(messageBody);
        Mockito.when(shopMapperService.mt2dp(anyLong())).thenReturn(123L);

        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            // 配置静态方法的期望行为
            mockedStatic.when(() -> Lion.getBoolean(anyString(), eq("privatelive.order.receipt.operate.handle.switch"), anyBoolean())).thenReturn(true);

            // act
            ConsumeStatus status = this.invokeHandle(message, context);

            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, status);
        }

    }

    /**
     * 使用反射调用私有方法
     */
    private void invokeProcessOrderOperate(com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify
            , Boolean subOrderFlag, Boolean refundHandleSwitch) throws Exception {
        Method handleMethod = GroundPromotionTechOrderConsumer.class.getDeclaredMethod("processOrderOperate"
                , com.sankuai.technician.trade.api.order.message.OrderOperateNotify.class,
                Boolean.class, Boolean.class);
        handleMethod.setAccessible(true);
        handleMethod.invoke(consumer, orderOperateNotify, subOrderFlag, refundHandleSwitch);
    }

    /**
     * 使用反射调用私有方法
     */
    private void invokeExtInfoChangeProcess(String datetime, com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify) throws Exception {
        Method handleMethod = GroundPromotionTechOrderConsumer.class.getDeclaredMethod("extInfoChangeProcess",
                String.class, com.sankuai.technician.trade.api.order.message.OrderOperateNotify.class);
        handleMethod.setAccessible(true);
        handleMethod.invoke(consumer, datetime, orderOperateNotify);
    }


    @Test
    public void testPrivateLiveCreateHandleV2() throws Exception {
        // arrange
        com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify = new com.sankuai.technician.trade.api.order.message.OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.CREATE.getCode());


        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");
        orderIntentionResult.setStatus(OrderStatusEnum.CREATE.getCode());
        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult((PrivateLiveOrderUpdateInfo) any())).thenReturn(successResponse);

        // act
        this.invokeProcessOrderOperate(orderOperateNotify, true, true);


        // assert
        verify(privateLiveOrderIntentionService, times(1)).updateIntentionResult((PrivateLiveOrderUpdateInfo) any());

    }

    @Test
    public void testPrivateLiveVerifyHandleV2() throws Exception {
        // arrange
        com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify = new com.sankuai.technician.trade.api.order.message.OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.VERIFY.getCode());
        orderOperateNotify.setActionTime(new Date());


        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");

        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);
        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult((PrivateLiveOrderUpdateInfo) any())).thenReturn(successResponse);

        // act
        this.invokeProcessOrderOperate(orderOperateNotify, true, true);

        // assert
        verify(privateLiveOrderIntentionService, times(1)).updateIntentionResult((PrivateLiveOrderUpdateInfo) any());

    }

    @Test
    public void testPrivateLiveCancelHandleV2() throws Exception {
        // arrange
        com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify = new com.sankuai.technician.trade.api.order.message.OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.CANCEL.getCode());
        orderOperateNotify.setBizType(OrderLogBizTypeEnum.FUN_COUPON.getCode());
        orderOperateNotify.setActionTime(new Date());


        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");

        Mockito.when(redisStoreClient.setnx(any(StoreKey.class), anyBoolean(), anyInt())).thenReturn(true);
        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult((PrivateLiveOrderUpdateInfo) any())).thenReturn(successResponse);

        // act
        this.invokeProcessOrderOperate(orderOperateNotify, true, false);

        // assert
        verify(privateLiveOrderIntentionService, times(1)).updateIntentionResult((PrivateLiveOrderUpdateInfo) any());

    }

    @Test
    public void testPrivateLiveCancelHandleV3() throws Exception {
        // arrange
        PrivateLiveModel privateLiveModel = new PrivateLiveModel();
        privateLiveModel.setOrderGroupId("1234");
        privateLiveModel.setOrderGroupStatus("30");
        Map<String, String> map = Maps.newHashMap();
        map.put("privateLiveModel", JsonUtil.toJson(privateLiveModel));

        com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify = new com.sankuai.technician.trade.api.order.message.OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(map);
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.CANCEL.getCode());
        orderOperateNotify.setBizType(OrderLogBizTypeEnum.SD_ORDER.getCode());
        orderOperateNotify.setActionTime(new Date());


        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        this.invokeProcessOrderOperate(orderOperateNotify, true, false);

        // assert
        verify(orderIntentionResultRepository, times(1)).forceGetByOrderId(anyString());
    }

    @Test
    public void testPrivateLiveExtChangeHandle() throws Exception {
        // arrange
        PrivateLiveModel privateLiveModel = new PrivateLiveModel();
        privateLiveModel.setOrderGroupId("1234");
        privateLiveModel.setOrderGroupStatus("30");
        Map<String, String> map = Maps.newHashMap();
        map.put("privateLiveModel", JsonUtil.toJson(privateLiveModel));

        com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify = new com.sankuai.technician.trade.api.order.message.OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(map);
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.EXT_INFO_CHANGE.getCode());
        orderOperateNotify.setActionTime(new Date());


        PrivateLiveOrderIntentionResult orderIntentionResult = new PrivateLiveOrderIntentionResult();
        orderIntentionResult.setOrderId("123");

        Mockito.when(orderIntentionResultRepository.forceGetByOrderId(anyString())).thenReturn(orderIntentionResult);

        // act
        this.invokeProcessOrderOperate(orderOperateNotify, true, false);

        // assert
        verify(orderIntentionResultRepository, times(1)).forceGetByOrderId(anyString());
    }

    @Test
    public void testExtInfoChangeProcess() throws Exception {
        // arrange
        com.sankuai.technician.trade.api.order.message.OrderOperateNotify orderOperateNotify = new com.sankuai.technician.trade.api.order.message.OrderOperateNotify();
        orderOperateNotify.setOrderId("123");
        orderOperateNotify.setExtInfo(Maps.newHashMap());
        orderOperateNotify.setChannel(OrderDistributionTypeEnum.PRIVATE_LIVE_CONSULTANT.getCode());
        orderOperateNotify.setMtShopId(123L);
        orderOperateNotify.setOperateType(OrderLogOptTypeEnum.EXT_INFO_CHANGE.getCode());


        String datetime = "2025-01-01 11:22:00";
        Mockito.when(privateLiveOrderIntentionService.updateIntentionResult((PrivateLiveOrderUpdateInfo) any())).thenReturn(successResponse);

        // act
        this.invokeExtInfoChangeProcess(datetime, orderOperateNotify);


        // assert
        verify(privateLiveOrderIntentionService, times(1)).updateIntentionResult((PrivateLiveOrderUpdateInfo) any());

    }

}
