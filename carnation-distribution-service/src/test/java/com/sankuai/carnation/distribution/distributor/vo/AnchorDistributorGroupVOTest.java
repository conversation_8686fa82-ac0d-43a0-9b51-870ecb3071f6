package com.sankuai.carnation.distribution.distributor.vo;

import org.junit.Assert;
import org.junit.Test;

/**
 * 测试AnchorDistributorGroupVO的属性设置和获取
 */
public class AnchorDistributorGroupVOTest {

    /**
     * 测试设置和获取anchorId
     */
    @Test
    public void testSetAndGetAnchorId() {
        // arrange
        AnchorDistributorGroupVO vo = new AnchorDistributorGroupVO();
        Long expected = 123L;

        // act
        vo.setAnchorId(expected);
        Long actual = vo.getAnchorId();

        // assert
        Assert.assertEquals(expected, actual);
    }

    /**
     * 测试设置和获取groupName
     */
    @Test
    public void testSetAndGetGroupName() {
        // arrange
        AnchorDistributorGroupVO vo = new AnchorDistributorGroupVO();
        String expected = "Test Group";

        // act
        vo.setGroupName(expected);
        String actual = vo.getGroupName();

        // assert
        Assert.assertEquals(expected, actual);
    }

    /**
     * 测试设置和获取status
     */
    @Test
    public void testSetAndGetStatus() {
        // arrange
        AnchorDistributorGroupVO vo = new AnchorDistributorGroupVO();
        Integer expected = 1;

        // act
        vo.setStatus(expected);
        Integer actual = vo.getStatus();

        // assert
        Assert.assertEquals(expected, actual);
    }

    // 可以继续为其他属性编写类似的测试用例
}
