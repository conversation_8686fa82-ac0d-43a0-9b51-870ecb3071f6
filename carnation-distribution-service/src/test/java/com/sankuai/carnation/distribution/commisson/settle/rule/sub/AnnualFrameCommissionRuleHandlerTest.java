package com.sankuai.carnation.distribution.commisson.settle.rule.sub;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.carnation.distribution.commission.settle.enums.AnnualFrameRebateRuleTypeEnum;
import com.sankuai.carnation.distribution.commisson.repository.AnnualRebateRuleDataService;
import com.sankuai.carnation.distribution.commisson.settle.calculator.bo.annual.frame.ShopAnnualCommissionRebateBO;
import com.sankuai.carnation.distribution.commisson.settle.calculator.bo.annual.frame.ShopAnnualFramePromoBO;
import com.sankuai.carnation.distribution.commisson.settle.rule.bo.ShopPredictEffectCommissionRuleBO;
import com.sankuai.carnation.distribution.common.acl.ShopCategoryAclService;
import com.sankuai.carnation.distribution.common.bo.PoiCategoryBO;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AnnualFrameCommissionRuleHandlerTest {

    @InjectMocks
    private AnnualFrameCommissionRuleHandler annualFrameCommissionRuleHandler;

    @Mock
    private AnnualRebateRuleDataService dataService;

    private long mtShopId = 1L;

    private Date payTime = new Date();

    private Date verifyTime = new Date();

    @Mock
    private ShopCategoryAclService shopCategoryAclService;

    private long dpShopId = 1L;

    private Date startTime = new Date();

    private Date endTime = new Date();

    @Test
    public void testGetLowestCommissionRuleInvalidInput() throws Throwable {
        assertNull(annualFrameCommissionRuleHandler.getLowestCommissionRule(0L, mtShopId, payTime, verifyTime));
        assertNull(annualFrameCommissionRuleHandler.getLowestCommissionRule(mtShopId, 0L, payTime, verifyTime));
        assertNull(annualFrameCommissionRuleHandler.getLowestCommissionRule(mtShopId, mtShopId, null, verifyTime));
        assertNull(annualFrameCommissionRuleHandler.getLowestCommissionRule(mtShopId, mtShopId, payTime, null));
    }

    @Test
    public void testGetLowestCommissionRuleEmptyLists() throws Throwable {
        when(dataService.getCommissionRateRuleByShopAndTime(mtShopId, payTime, verifyTime)).thenReturn(Collections.emptyList());
        when(dataService.getRebateRuleByShopAndTime(mtShopId, verifyTime)).thenReturn(Collections.emptyList());
        assertNull(annualFrameCommissionRuleHandler.getLowestCommissionRule(mtShopId, mtShopId, payTime, verifyTime));
    }

    @Test
    public void testGetLowestCommissionRuleListBothMapsAreEmpty() throws Throwable {
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(new HashMap<>());
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(new HashMap<>());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListCommissionRateMapIsNotEmpty() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> commissionRateRuleMap = new HashMap<>();
        commissionRateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(commissionRateRuleMap);
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(new HashMap<>());
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListRebateMapIsNotEmpty() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> rebateRuleMap = new HashMap<>();
        rebateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(new HashMap<>());
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(rebateRuleMap);
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListBothMapsAreNotEmpty() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> commissionRateRuleMap = new HashMap<>();
        commissionRateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        Map<String, List<ShopAnnualFramePromoBO>> rebateRuleMap = new HashMap<>();
        rebateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(commissionRateRuleMap);
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(rebateRuleMap);
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListSameKeyInBothMaps() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> commissionRateRuleMap = new HashMap<>();
        commissionRateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        Map<String, List<ShopAnnualFramePromoBO>> rebateRuleMap = new HashMap<>();
        rebateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(commissionRateRuleMap);
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(rebateRuleMap);
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListGetLowestCommissionRuleByCommissionAndRebateReturnsNull() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> commissionRateRuleMap = new HashMap<>();
        commissionRateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        Map<String, List<ShopAnnualFramePromoBO>> rebateRuleMap = new HashMap<>();
        rebateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(commissionRateRuleMap);
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(rebateRuleMap);
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListGetLowestCommissionRuleByCommissionAndRebateReturnsNonNull() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> commissionRateRuleMap = new HashMap<>();
        commissionRateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        Map<String, List<ShopAnnualFramePromoBO>> rebateRuleMap = new HashMap<>();
        rebateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(commissionRateRuleMap);
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(rebateRuleMap);
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetLowestCommissionRuleListGetLowestCommissionRuleByCommissionAndRebateReturnsNonNull2() throws Throwable {
        Map<String, List<ShopAnnualFramePromoBO>> commissionRateRuleMap = new HashMap<>();
        commissionRateRuleMap.put("key", Collections.singletonList(new ShopAnnualFramePromoBO()));
        Map<String, List<ShopAnnualFramePromoBO>> rebateRuleMap = new HashMap<>();
        ShopAnnualFramePromoBO shopAnnualFramePromoBO = new ShopAnnualFramePromoBO();
        ShopAnnualCommissionRebateBO rule = new ShopAnnualCommissionRebateBO();
        rule.setRebateType(AnnualFrameRebateRuleTypeEnum.UNKNOWN.getCode());
        shopAnnualFramePromoBO.setCommissionRebate(rule);
        rebateRuleMap.put("key", Collections.singletonList(shopAnnualFramePromoBO));
        when(dataService.getCommissionRateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(commissionRateRuleMap);
        when(dataService.getRebateRuleBetweenStarTimeAndEndTime(any(Long.class), any(Date.class), any(Date.class))).thenReturn(rebateRuleMap);
        when(shopCategoryAclService.getPoiCategory(any(Integer.class), any(Long.class))).thenReturn(new PoiCategoryBO());
        List<ShopPredictEffectCommissionRuleBO> result = annualFrameCommissionRuleHandler.getLowestCommissionRuleList(dpShopId, mtShopId, startTime, endTime);
        assertEquals(1, result.size());
    }
}