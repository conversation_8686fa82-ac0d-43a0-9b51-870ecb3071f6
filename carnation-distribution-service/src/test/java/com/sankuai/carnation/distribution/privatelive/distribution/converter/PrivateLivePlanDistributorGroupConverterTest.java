package com.sankuai.carnation.distribution.privatelive.distribution.converter;

import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLivePlanDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.po.PrivateLivePlanDistributorGroup;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.repository.DistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.po.PrivateLivePlanDistributorGroup;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLivePlanDistributorGroupBO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PrivateLivePlanDistributorGroupConverterTest {

    @InjectMocks
    private PrivateLivePlanDistributorGroupConverter converter;

    @Mock
    private DistributorGroupBO distributorGroupBO;
    @Mock
    private DistributorGroupRepository distributorGroupRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试toPoJo方法，当distributorGroupBO不为null且groupId不为null时
     */
    @Test
    public void testToPoJoWithNonNullDistributorGroupAndGroupId() {
        // arrange
        PrivateLivePlanDistributorGroupBO bo = new PrivateLivePlanDistributorGroupBO();
        bo.setDistributorGroup(distributorGroupBO);
        when(distributorGroupBO.getGroupId()).thenReturn(1);

        // act
        PrivateLivePlanDistributorGroup result = converter.toPoJo(bo);

        // assert
        assertEquals(Long.valueOf(1), result.getDistributorGroupId());
    }

    /**
     * 测试toPoJo方法，当distributorGroupBO为null时
     */
    @Test
    public void testToPoJoWithNullDistributorGroup() {
        // arrange
        PrivateLivePlanDistributorGroupBO bo = new PrivateLivePlanDistributorGroupBO();
        bo.setDistributorGroup(null);

        // act
        PrivateLivePlanDistributorGroup result = converter.toPoJo(bo);

        // assert
        assertEquals(null, result.getDistributorGroupId());
    }

    /**
     * 测试toPoJo方法，当distributorGroupBO不为null但groupId为null时
     */
    @Test
    public void testToPoJoWithNonNullDistributorGroupAndNullGroupId() {
        // arrange
        PrivateLivePlanDistributorGroupBO bo = new PrivateLivePlanDistributorGroupBO();
        bo.setDistributorGroup(distributorGroupBO);
        when(distributorGroupBO.getGroupId()).thenReturn(null);

        // act
        PrivateLivePlanDistributorGroup result = converter.toPoJo(bo);

        // assert
        assertEquals(null, result.getDistributorGroupId());
    }

    /**
     * 测试 toEntity 方法，正常场景
     */
    @Test
    public void testToEntityNormalScenario() {
        // arrange
        Long distributorGroupId = 1L;
        PrivateLivePlanDistributorGroup input = new PrivateLivePlanDistributorGroup();
        input.setDistributorGroupId(distributorGroupId);
        input.setVersion(1);
        DistributorGroupBO expectedDistributorGroupBO = new DistributorGroupBO();
        when(distributorGroupRepository.queryByGroupId(distributorGroupId)).thenReturn(expectedDistributorGroupBO);

        // act
        PrivateLivePlanDistributorGroupBO result = converter.toEntity(input);

        // assert
        assertNotNull(result);
        assertEquals(expectedDistributorGroupBO, result.getDistributorGroup());
    }

    /**
     * 测试 toEntity 方法，异常场景：DistributorGroupId 为 null
     */
    @Test
    public void testToEntityExceptionScenarioDistributorGroupIdIsNull() {
        // arrange
        PrivateLivePlanDistributorGroup input = new PrivateLivePlanDistributorGroup();
        input.setDistributorGroupId(null);
        input.setVersion(1);
        // act
        PrivateLivePlanDistributorGroupBO result = converter.toEntity(input);

        // assert
        assertNotNull(result);
        assertNull(result.getDistributorGroup());
    }

    /**
     * 测试 toEntity 方法，异常场景：根据 DistributorGroupId 查询不到 DistributorGroupBO
     */
    @Test
    public void testToEntityExceptionScenarioDistributorGroupBONotFound() {
        // arrange
        Long distributorGroupId = 1L;
        PrivateLivePlanDistributorGroup input = new PrivateLivePlanDistributorGroup();
        input.setDistributorGroupId(distributorGroupId);input.setVersion(1);
        when(distributorGroupRepository.queryByGroupId(distributorGroupId)).thenReturn(null);

        // act
        PrivateLivePlanDistributorGroupBO result = converter.toEntity(input);

        // assert
        assertNotNull(result);
        assertNull(result.getDistributorGroup());
    }
}
