package com.sankuai.carnation.distribution.product.acl;

import com.sankuai.medicalcosmetology.product.selectify.api.dto.privatelive.ProductDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.request.CommonProductRequest;
import com.sankuai.medicalcosmetology.product.selectify.api.response.BaseResponse;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonProductQueryService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

public class ProductSelectifyAclServiceTest {

    @InjectMocks
    private ProductSelectifyAclService productSelectifyAclService;

    @Mock
    private CommonProductQueryService commonProductQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryProductListReturnNull() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenReturn(null);
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertNull(result);
    }

    @Test
    public void testQueryProductListResultNotSuccess() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        BaseResponse<List<ProductDTO>> response = new BaseResponse<>();
        response.setCode(500);
        response.setError("error");
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenReturn(response);
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertNull(result);
    }

    @Test
    public void testQueryProductListDataIsNull() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        BaseResponse<List<ProductDTO>> response = new BaseResponse<>();
        response.setCode(200);
        response.setError("success");
        response.setData(null);
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenReturn(response);
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertNull(result);
    }

    @Test
    public void testQueryProductListSuccess() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        BaseResponse<List<ProductDTO>> response = new BaseResponse<>();
        response.setCode(200);
        response.setError("success");
        response.setData(Collections.singletonList(new ProductDTO()));
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenReturn(response);
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertSame(response.getData(), result);
    }

    @Test
    public void testQueryProductListException() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenThrow(new RuntimeException());
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertNull(result);
    }

    @Test
    public void testQueryProductListMaxRetries() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenReturn(null);
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertNull(result);
    }

    @Test
    public void testQueryProductListInterrupted() throws Throwable {
        CommonProductRequest productQueryRequest = new CommonProductRequest();
        // Corrected to throw a RuntimeException instead of InterruptedException
        when(commonProductQueryService.getProductListWithCondition(productQueryRequest)).thenThrow(new RuntimeException());
        List<ProductDTO> result = productSelectifyAclService.queryProductList(productQueryRequest);
        assertNull(result);
    }
}
