package com.sankuai.carnation.distribution.bcp;

import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplPromoCodeFilterTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private OrderOperateNotifyBcpDTO orderOperateNotify;

    @Mock
    private BcpCheckRule bcpCheckRule;

    private Map<String, String> extInfo;

    @Before
    public void setUp() {
        extInfo = new HashMap<>();
        when(orderOperateNotify.getExtInfo()).thenReturn(extInfo);
    }

    @Test
    public void testPromoCodeFilterWhenTechIdIsZero() throws Throwable {
        // Setup conditions that would lead to the techId being 0
        extInfo.put("activityInfo", "{\"techId\":0}");
        // Assert the expected behavior
        assertFalse(promoCodeBcpCheckService.promoCodeFilter(orderOperateNotify));
    }

    @Test
    public void testPromoCodeFilterWhenEventSourceIsNotPromoCodeRebateEvent() throws Throwable {
        // Setup conditions that would lead to the event source not being a promo code rebate event
        extInfo.put("activityInfo", "{\"techId\":1,\"eventSource\":0}");
        // Assert the expected behavior
        assertFalse(promoCodeBcpCheckService.promoCodeFilter(orderOperateNotify));
    }

    @Test
    public void testPromoCodeFilterWhenValidCommandListIsNotEmptyAndNotContainsOne() throws Throwable {
        // Setup conditions that would lead to the valid command list not containing 1
        extInfo.put("activityInfo", "{\"techId\":1,\"eventSource\":1}");
        // Assert the expected behavior
        assertFalse(promoCodeBcpCheckService.promoCodeFilter(orderOperateNotify));
    }

    @Test
    public void testPromoCodeFilterWhenOperateTypeIsNotTwo() throws Throwable {
        // Setup conditions that would lead to the operate type not being 2
        extInfo.put("activityInfo", "{\"techId\":1,\"eventSource\":1}");
        when(orderOperateNotify.getOperateType()).thenReturn(1);
        // Assert the expected behavior
        assertFalse(promoCodeBcpCheckService.promoCodeFilter(orderOperateNotify));
    }

    @Test
    public void testPromoCodeFilterWhenAllConditionsAreMet() throws Throwable {
        // Setup conditions that would lead to all conditions being met for the method to return true
        extInfo.put("activityInfo", "{\"techId\":1,\"eventSource\":1}");
        when(orderOperateNotify.getOperateType()).thenReturn(2);
        // Assert the expected behavior
        assertTrue(promoCodeBcpCheckService.promoCodeFilter(orderOperateNotify));
    }
}
