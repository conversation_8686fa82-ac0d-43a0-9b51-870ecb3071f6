package com.sankuai.carnation.distribution.wallet.distribute.tech.service.impl;

import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.sankuai.carnation.distribution.wallet.distribute.tech.dto.TechWalletFlowDTO;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletFlowItemDataService;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletOperateFlowDataService;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class TechDistributeWalletFlowServiceImplTest {

    @InjectMocks
    private TechDistributeWalletFlowServiceImpl service;

    @Mock
    private WalletFlowItemDataService walletFlowItemDataService;

    @Mock
    private WalletOperateFlowDataService flowDataService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 dataItemIds 为 null 的情况
     */
    @Test
    public void testQueryWalletFlowByDateItemIdsWithNullDataItemIds() {
        // arrange
        List<Long> dataItemIds = null;

        // act
        PaginationRemoteResponse<TechWalletFlowDTO> result = service.queryWalletFlowByDateItemIds(dataItemIds);

        // assert
        assertEquals("dataItemId 非法", result.getMsg());
    }

    /**
     * 测试 dataItemIds 为空列表的情况
     */
    @Test
    public void testQueryWalletFlowByDateItemIdsWithEmptyDataItemIds() {
        // arrange
        List<Long> dataItemIds = Collections.emptyList();

        // act
        PaginationRemoteResponse<TechWalletFlowDTO> result = service.queryWalletFlowByDateItemIds(dataItemIds);

        // assert
        assertEquals("dataItemId 非法", result.getMsg());
    }

    /**
     * 测试 dataItemIds 包含非法值的情况
     */
    @Test
    public void testQueryWalletFlowByDateItemIdsWithInvalidDataItemIds() {
        // arrange
        List<Long> dataItemIds = Arrays.asList(-1L, 0L);

        // act
        PaginationRemoteResponse<TechWalletFlowDTO> result = service.queryWalletFlowByDateItemIds(dataItemIds);

        // assert
        assertEquals("dataItemId 非法", result.getMsg());
    }

    /**
     * 测试 dataItemIds 有效但未找到流水ID的情况
     */
    @Test
    public void testQueryWalletFlowByDateItemIdsWithNoFlowIdsFound() {
        // arrange
        List<Long> dataItemIds = Arrays.asList(1L, 2L);
        when(walletFlowItemDataService.getFlowIdByItemId(dataItemIds)).thenReturn(Collections.emptyList());

        // act
        PaginationRemoteResponse<TechWalletFlowDTO> result = service.queryWalletFlowByDateItemIds(dataItemIds);

        // assert
        assertEquals(0L, result.getTotalHit());
        verify(walletFlowItemDataService).getFlowIdByItemId(dataItemIds);
    }

    /**
     * 测试 dataItemIds 有效且找到流水ID但未找到流水信息的情况
     */
    @Test
    public void testQueryWalletFlowByDateItemIdsWithNoWalletOperateFlowsFound() {
        // arrange
        List<Long> dataItemIds = Arrays.asList(1L, 2L);
        List<Long> flowIds = Arrays.asList(3L, 4L);
        when(walletFlowItemDataService.getFlowIdByItemId(dataItemIds)).thenReturn(flowIds);
        when(flowDataService.queryByFlowIdList(flowIds)).thenReturn(Collections.emptyList());

        // act
        PaginationRemoteResponse<TechWalletFlowDTO> result = service.queryWalletFlowByDateItemIds(dataItemIds);

        // assert
        assertEquals(0L, result.getTotalHit());
        verify(walletFlowItemDataService).getFlowIdByItemId(dataItemIds);
        verify(flowDataService).queryByFlowIdList(flowIds);
    }

    /**
     * 测试 dataItemIds 有效且找到流水ID和流水信息的情况
     */
    @Test
    public void testQueryWalletFlowByDateItemIdsWithValidData() {
        // arrange
        List<Long> dataItemIds = Arrays.asList(1L, 2L);
        List<Long> flowIds = Arrays.asList(3L, 4L);
        WalletOperateFlow walletOperateFlow = buildWalletOperateFlow(1L, 1L, 1);
        WalletOperateFlow walletOperateFlow2 = buildWalletOperateFlow(2L, 2L, 1);
        List<WalletOperateFlow> walletOperateFlows = Arrays.asList(walletOperateFlow, walletOperateFlow2);
        when(walletFlowItemDataService.getFlowIdByItemId(dataItemIds)).thenReturn(flowIds);
        when(flowDataService.queryByFlowIdList(flowIds)).thenReturn(walletOperateFlows);

        // act
        PaginationRemoteResponse<TechWalletFlowDTO> result = service.queryWalletFlowByDateItemIds(dataItemIds);

        // assert
        assertEquals(walletOperateFlows.size(), result.getTotalHit());
        verify(walletFlowItemDataService).getFlowIdByItemId(dataItemIds);
        verify(flowDataService).queryByFlowIdList(flowIds);
    }

    @NotNull
    private static WalletOperateFlow buildWalletOperateFlow(long id, long dataItemId, int flowType) {
        WalletOperateFlow walletOperateFlow2 = new WalletOperateFlow();
        walletOperateFlow2.setId(id);
        walletOperateFlow2.setDataItemId(dataItemId);
        walletOperateFlow2.setWalletAccountId(0L);
        walletOperateFlow2.setWalletActivityAccountId(0L);
        walletOperateFlow2.setSearchId("");
        walletOperateFlow2.setSecondSearchId("");
        walletOperateFlow2.setOperator("");
        walletOperateFlow2.setOperateType(0);
        walletOperateFlow2.setFlowType(flowType);
        walletOperateFlow2.setAmount(0L);
        walletOperateFlow2.setFormerAmount(0L);
        walletOperateFlow2.setCurrentAmount(0L);
        walletOperateFlow2.setComment("");
        walletOperateFlow2.setStatus(0);
        walletOperateFlow2.setExtInfo("");
        walletOperateFlow2.setAddTime(new Date());
        walletOperateFlow2.setUpdateTime(new Date());
        return walletOperateFlow2;
    }
}