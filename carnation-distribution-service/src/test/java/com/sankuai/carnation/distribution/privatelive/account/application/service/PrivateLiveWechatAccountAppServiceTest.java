package com.sankuai.carnation.distribution.privatelive.account.application.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.pay.mwallet.proxy.thrift.MwalletProxyService;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.AuthTargetMaskInfoDataTo;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.AuthTargetMaskInfoResTo;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.AuthTargetMaskInfoSignResTo;
import com.meituan.pay.mwallet.proxy.thrift.entity.auth.ReqTo;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBindBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.repository.DistributorGroupRepository;
import com.sankuai.carnation.distribution.distributor.repository.DistributorRepository;
import com.sankuai.carnation.distribution.privatelive.account.model.MwalletAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveAccountRoleTaskRequest;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveWechatAccountRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.PrivateLiveAccountTypeEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ObjectUtils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveWechatAccountAppServiceTest {

    @Mock
    private MwalletProxyService.Iface mwalletProxyService;

    @InjectMocks
    private PrivateLiveWechatAccountAppService privateLiveWechatAccountAppService;

    @Mock
    private WeChatUserService weChatUserService;

    @Mock
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;

    @Mock
    private DistributorRepository distributorRepository;

    @Mock
    private DistributorGroupRepository distributorGroupRepository;

    @InjectMocks
    private PrivateLiveWechatAccountAppService service;

    private PrivateLiveAccountRoleTaskRequest request;

    private PrivateLiveConsultantTask consultantTask;

    private PrivateLiveConsultantAccount consultantAccount;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试微信账户不存在的场景
     */
    @Test
    public void testQueryPrivateLiveWechatAccount_WechatAccountNotExist() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest("wxToken", "unionId", "openId", null);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(anyString(), anyString())).thenReturn(null);
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom()
                .setCode(200)
                .setSuccess()
                .setData(WeChatLoginInfo.builder()
                        .openId("123")
                        .unionId("unionId")
                        .build())
                .build());
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(request);
        // assert
        assertNotNull(result);
        assertNull(result.getId());
    }

    /**
     * 测试微信账户存在，但没有任何关联账户类型的场景
     */
    @Test
    public void testQueryPrivateLiveWechatAccount_WechatAccountExistNoLinkedAccountType() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest("wxToken", "unionId", "openId", null);
        PrivateLiveConsultantAccount consultantAccount = new PrivateLiveConsultantAccount(1L, "unionId", "openId", 1L, 1, null, null);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(anyString(), anyString())).thenReturn(consultantAccount);
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom()
                .setCode(200)
                .setSuccess()
                .setData(WeChatLoginInfo.builder()
                        .openId("123")
                        .unionId("unionId")
                        .build())
                .build());
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(request);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getId());
        assertTrue(result.getAccountTypeList().isEmpty());
    }

    /**
     * 测试微信账户存在，并且是分销员的场景
     */
    @Test
    public void testQueryPrivateLiveWechatAccount_WechatAccountExistDistributor() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest("wxToken", "unionId", "openId", null);
        PrivateLiveConsultantAccount consultantAccount = new PrivateLiveConsultantAccount(1L, "unionId", "openId", 1L, 1, null, null);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(anyString(), anyString())).thenReturn(consultantAccount);
        DistributorBO distributorBO = new DistributorBO();
        DistributorBindBO distributorBindBO = new DistributorBindBO(distributorBO, 1L, 1L, new DistributorGroupBO(), DistributionApproveStatusEnum.PASS);
        when(distributorRepository.queryDistributorByAccountId(any(), anyLong())).thenReturn(distributorBindBO);

        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom()
                .setCode(200)
                .setSuccess()
                .setData(WeChatLoginInfo.builder()
                        .openId("123")
                        .unionId("unionId")

                        .build())
                .build());
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(request);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getId());
        assertTrue(result.getAccountTypeList().contains(PrivateLiveAccountTypeEnum.DISTRIBUTOR));
    }

    /**
     * 测试微信账户存在，但分销员查询返回null的场景
     */
    @Test
    public void testQueryPrivateLiveWechatAccount_WechatAccountExistDistributorQueryNull() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest("wxToken", "unionId", "openId", null);
        PrivateLiveConsultantAccount consultantAccount = new PrivateLiveConsultantAccount(1L, "unionId", "openId", 1L, 1, null, null);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(anyString(), anyString())).thenReturn(consultantAccount);
        when(distributorRepository.queryDistributorByAccountId(any(), anyLong())).thenReturn(null);
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom()
                .setCode(200)
                .setSuccess()
                .setData(WeChatLoginInfo.builder()
                        .openId("123")
                        .unionId("unionId")
                        .build())
                .build());
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(request);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getId());
        assertTrue(result.getAccountTypeList().isEmpty());
    }

    /**
     * 测试任务ID为空时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryPrivateLiveWechatAccountWithEmptyTaskId() {
        request = new PrivateLiveAccountRoleTaskRequest(null);
        service.queryPrivateLiveWechatAccount(request);
    }

    /**
     * 测试不存在该角色任务时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryPrivateLiveWechatAccountWithNonExistentTask() {
        request = new PrivateLiveAccountRoleTaskRequest(1L);
        when(taskRepository.loadById(any(Long.class))).thenReturn(null);
        service.queryPrivateLiveWechatAccount(request);
    }

    /**
     * 测试账户不存在时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryPrivateLiveWechatAccountWithNonExistentAccount() {
        request = new PrivateLiveAccountRoleTaskRequest(1L);
        when(taskRepository.loadById(any(Long.class))).thenReturn(consultantTask);
        service.queryPrivateLiveWechatAccount(request);
    }


    /**
     * 测试获取私域直播账户信息，当wxToken不为空且能正确查询到用户信息时
     */
    @Test
    public void testGetPrivateLiveAccount_WithValidWxToken() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest("validWxToken", null, null, null);
        WeChatLoginInfo weChatLoginInfo = new WeChatLoginInfo();
        weChatLoginInfo.setOpenId("openId");
        weChatLoginInfo.setUnionId("unionId");
        RemoteResponse<WeChatLoginInfo> weChatLoginInfoRemoteResponse = mock(RemoteResponse.class);

        weChatLoginInfoRemoteResponse.setCode(200);
//        weChatLoginInfoRemoteResponse.set(true);
        weChatLoginInfoRemoteResponse.setData(weChatLoginInfo);
        PrivateLiveConsultantAccount privateLiveConsultantAccount = new PrivateLiveConsultantAccount();
        privateLiveConsultantAccount.setId(1L);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId(anyString(), anyString())).thenReturn(privateLiveConsultantAccount);

        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom()
                .setCode(200)
                .setSuccess()
                .setData(WeChatLoginInfo.builder()
                        .openId("openId")
                        .unionId("unionId")
                        .build())
                .build());
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.getPrivateLiveAccount(request);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1L), result.getId());
        assertEquals("unionId", result.getUnionId());
        assertEquals("openId", result.getOpenId());
    }

    /**
     * 测试获取私域直播账户信息，当wxToken不为空但查询不到用户信息时
     */
    @Test(expected = BizSceneException.class)
    public void testGetPrivateLiveAccount_WithInvalidWxToken() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest("invalidWxToken", null, null, null);
        RemoteResponse<WeChatLoginInfo> weChatLoginInfoRemoteResponse = RemoteResponse.<WeChatLoginInfo>custom()
                .setCode(404)
                .setSuccess()
                .build();
//        weChatLoginInfoRemoteResponse.setCode(404);
//        weChatLoginInfoRemoteResponse.setSuccess();
        when(weChatUserService.getWeChatUserInfoByToken("invalidWxToken")).thenReturn(weChatLoginInfoRemoteResponse);
        // act
        privateLiveWechatAccountAppService.getPrivateLiveAccount(request);
        // assert is handled by the expected exception
    }

    /**
     * 测试获取私域直播账户信息，当wxToken为空且unionId和openId有效时
     */
    @Test
    public void testGetPrivateLiveAccount_WithValidUnionIdAndOpenId() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest(null, "unionId", "openId", null);
        PrivateLiveConsultantAccount privateLiveConsultantAccount = new PrivateLiveConsultantAccount();
        privateLiveConsultantAccount.setId(1L);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId("unionId", "openId")).thenReturn(privateLiveConsultantAccount);
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.getPrivateLiveAccount(request);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1L), result.getId());
        assertEquals("unionId", result.getUnionId());
        assertEquals("openId", result.getOpenId());
    }

    /**
     * 测试获取私域直播账户信息，当wxToken为空且unionId和openId无效时
     */
    @Test
    public void testGetPrivateLiveAccount_WithInvalidUnionIdAndOpenId() {
        // arrange
        PrivateLiveWechatAccountRequest request = new PrivateLiveWechatAccountRequest(null, "invalidUnionId", "invalidOpenId", null);
        when(privateLiveConsultantAccountRepository.loadByUnionIdAndOenId("invalidUnionId", "invalidOpenId")).thenReturn(null);
        // act
        PrivateLiveAccountBO result = privateLiveWechatAccountAppService.getPrivateLiveAccount(request);
        // assert
        assertNotNull(result);
        assertNull(result.getId());
        assertEquals("invalidUnionId", result.getUnionId());
        assertEquals("invalidOpenId", result.getOpenId());
    }

    /**
     * 测试 getMwalletAccount 方法，当 mtUserIdStr 为空时
     */
    @Test
    public void testGetMwalletAccountWithEmptyMtUserIdStr() {
        // arrange
        String mtUserIdStr = "";

        // act
        MwalletAccountBO result = service.getMwalletAccount(mtUserIdStr);

        // assert
        assert result == null;
    }

    /**
     * 测试 getMwalletAccount 方法，当 mwalletProxyService 返回 null 时
     */
    @Test
    public void testGetMwalletAccountWithNullResponse() throws Exception {
        // arrange
        String mtUserIdStr = "123";
        when(mwalletProxyService.queryAuthTargetMaskInfo(any(ReqTo.class))).thenReturn(null);

        // act
        MwalletAccountBO result = service.getMwalletAccount(mtUserIdStr);

        // assert
        assert result == null;
    }

    /**
     * 测试 getMwalletAccount 方法，当 mwalletProxyService 抛出异常时
     */
    @Test
    public void testGetMwalletAccountWithException() throws Exception {
        // arrange
        String mtUserIdStr = "123";
        when(mwalletProxyService.queryAuthTargetMaskInfo(any(ReqTo.class))).thenThrow(new RuntimeException());

        // act
        MwalletAccountBO mwalletAccount = service.getMwalletAccount(mtUserIdStr);

        // assert is handled by expected exception
        assertTrue(ObjectUtils.isEmpty(mwalletAccount));
    }

    /**
     * 测试 getMwalletAccount 方法，当 mwalletProxyService 返回成功响应时
     */
    @Test
    public void testGetMwalletAccountWithSuccessResponse() throws Exception {
        // arrange
        String mtUserIdStr = "123";
        AuthTargetMaskInfoSignResTo signResTo = new AuthTargetMaskInfoSignResTo();
        AuthTargetMaskInfoResTo resTo = new AuthTargetMaskInfoResTo();
        resTo.setStatus("success");
        AuthTargetMaskInfoDataTo dataTo = new AuthTargetMaskInfoDataTo();
        dataTo.setTargetName("John Doe");
        dataTo.setIdentifyIdMask("**********");
        resTo.setData(dataTo);
        signResTo.setPlain(resTo);
        when(mwalletProxyService.queryAuthTargetMaskInfo(any(ReqTo.class))).thenReturn(signResTo);

        // act
        MwalletAccountBO result = service.getMwalletAccount(mtUserIdStr);

        // assert
        assert !ObjectUtils.isEmpty(result);
        assert "John Doe".equals(result.getName());
        assert "**********".equals(result.getIdentifyMask());
    }
}
