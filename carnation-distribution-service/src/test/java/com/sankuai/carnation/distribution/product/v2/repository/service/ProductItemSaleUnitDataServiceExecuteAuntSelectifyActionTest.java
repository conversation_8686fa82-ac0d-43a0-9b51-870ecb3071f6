package com.sankuai.carnation.distribution.product.v2.repository.service;

import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.privatelive.ProductActionDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.enums.ProductActionTypeEnum;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceExecuteAuntSelectifyActionTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductSelectifyAclService productSelectifyAclService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private void invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod(methodName, ProductItemBO.class, List.class, ProductActionTypeEnum.class);
        method.setAccessible(true);
        method.invoke(productItemSaleUnitDataService, args);
    }

    /**
     * 测试正常路径：productSelectifyAclService.productAction 返回非 null 且 actionResult 为 true 的 ProductActionDTO
     */
    @Test
    public void testExecuteAuntSelectifyAction_NormalPath() throws Throwable {
        // arrange
        ProductItemBO productItemBO = new ProductItemBO();
        List<ProductItemSaleUnit> saleUnits = Collections.emptyList();
        ProductActionTypeEnum actionType = ProductActionTypeEnum.ADD;
        ProductActionDTO mockResult = new ProductActionDTO();
        mockResult.setActionResult(true);
        when(productSelectifyAclService.productAction(any())).thenReturn(mockResult);
        // act
        invokePrivateMethod("executeAuntSelectifyAction", productItemBO, saleUnits, actionType);
        // assert
        verify(productSelectifyAclService, times(1)).productAction(any());
    }

    /**
     * 测试异常路径：productSelectifyAclService.productAction 返回 null
     */
    @Test
    public void testExecuteAuntSelectifyAction_NullResult() throws Throwable {
        // arrange
        ProductItemBO productItemBO = new ProductItemBO();
        List<ProductItemSaleUnit> saleUnits = Collections.emptyList();
        ProductActionTypeEnum actionType = ProductActionTypeEnum.ADD;
        when(productSelectifyAclService.productAction(any())).thenReturn(null);
        // act & assert
        try {
            invokePrivateMethod("executeAuntSelectifyAction", productItemBO, saleUnits, actionType);
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ProductItemException);
        }
    }

    /**
     * 测试异常路径：productSelectifyAclService.productAction 返回 actionResult 为 false 的 ProductActionDTO
     */
    @Test
    public void testExecuteAuntSelectifyAction_FalseActionResult() throws Throwable {
        // arrange
        ProductItemBO productItemBO = new ProductItemBO();
        List<ProductItemSaleUnit> saleUnits = Collections.emptyList();
        ProductActionTypeEnum actionType = ProductActionTypeEnum.ADD;
        ProductActionDTO mockResult = new ProductActionDTO();
        mockResult.setActionResult(false);
        when(productSelectifyAclService.productAction(any())).thenReturn(mockResult);
        // act & assert
        try {
            invokePrivateMethod("executeAuntSelectifyAction", productItemBO, saleUnits, actionType);
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ProductItemException);
        }
    }
}
