package com.sankuai.carnation.distribution.promocode.task.repository.service.impl;

import com.sankuai.carnation.distribution.promocode.task.dto.PromoCodeShopPromoteTradeDataDTO;
import com.sankuai.carnation.distribution.promocode.task.repository.dao.QrShopPromoteTradeDataExtMapper;
import com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData;
import com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeDataExample;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeShopPromoteTradeDataDataServiceImplTest {

    @Mock
    private QrShopPromoteTradeDataExtMapper tradeDataMapper;

    @InjectMocks
    private PromoCodeShopPromoteTradeDataDataServiceImpl service;

    @Before
    public void setUp() {
        // 初始化操作，如有需要
    }

    /**
     * 测试输入参数partitionDate为空的情况
     */
    @Test
    public void testQueryShopTradeDataByPageAndShopId_PartitionDateIsEmpty() {
        // arrange
        String partitionDate = "";
        long lastShopId = 1L;
        int limitSize = 10;

        // act
        List<PromoCodeShopPromoteTradeDataDTO> result = service.queryShopTradeDataByPageAndShopId(partitionDate, lastShopId, limitSize);

        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试输入参数lastShopId小于0的情况
     */
    @Test
    public void testQueryShopTradeDataByPageAndShopId_LastShopIdIsNegative() {
        // arrange
        String partitionDate = "2023-01-01";
        long lastShopId = -1L;
        int limitSize = 10;

        // act
        List<PromoCodeShopPromoteTradeDataDTO> result = service.queryShopTradeDataByPageAndShopId(partitionDate, lastShopId, limitSize);

        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryShopTradeDataByPageAndShopId_Normal() {
        // arrange
        String partitionDate = "2023-01-01";
        long lastShopId = 1L;
        int limitSize = 10;
        QrShopPromoteTradeData po = new QrShopPromoteTradeData(); // 假设这是一个有效的PO对象
        when(tradeDataMapper.selectByExample(any(QrShopPromoteTradeDataExample.class))).thenReturn(Arrays.asList(po));

        // act
        List<PromoCodeShopPromoteTradeDataDTO> result = service.queryShopTradeDataByPageAndShopId(partitionDate, lastShopId, limitSize);

        // assert
        assertEquals(1, result.size()); // 假设convertToDTO方法总是返回非null的DTO对象
    }

}
