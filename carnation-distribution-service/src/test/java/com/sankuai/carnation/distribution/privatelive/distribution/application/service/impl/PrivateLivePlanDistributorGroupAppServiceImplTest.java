package com.sankuai.carnation.distribution.privatelive.distribution.application.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributionplan.service.PrivateLiveDistributionPlanService;
import com.sankuai.carnation.distribution.distributionplan.vo.PrivateLiveDistributionPlanVO;
import com.sankuai.carnation.distribution.distributor.domain.DistributorChannelRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
/**
 * @Author: jinjianxia
 * @CreateTime: 2024/9/5 17:01
 * @Description:
 */
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLivePlanDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PageQueryForDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLivePlanDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.service.PrivateLivePlanDistributorGroupDomainService;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.proxy.PrivateLiveSettleServiceProxy;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.proxy.StatementQueryServiceProxy;
import com.sankuai.carnation.distribution.privatelive.distribution.request.AddDistributorGroupForPlanRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryDistributorGroupByPlanRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryDistributorGroupForAnchorRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryPlanAndDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.AddDistributorGroupForPlanResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryDistributorGroupByPlanResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryDistributorGroupForAnchorResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryPlanAndDistributorGroupResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.technician.trade.api.settle.dto.PrivateLiveSettleCountDTO;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ObjectUtils;
import org.testng.collections.Lists;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLivePlanDistributorGroupAppServiceImplTest {

    @Mock
    private StatementQueryServiceProxy statementQueryServiceProxy;

    @Mock
    private PrivateLivePlanDistributorGroupRepository privateLivePlanDistributorGroupRepository;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;

    @Mock
    private DistributorRootService distributorRootService;

    @Mock
    private DistributorGroupRootService distributorGroupRootService;

    @Mock
    private PrivateLiveDistributionPlanService privateLiveDistributionPlanService;

    @Mock
    private PrivateLiveSettleServiceProxy privateLiveSettleServiceProxy;

    @Mock
    private PrivateLivePlanDistributorGroupDomainService privateLivePlanDistributorGroupDomainService;

    @Mock
    private DistributorChannelRootService distributorChannelRootService;

    @InjectMocks
    private PrivateLivePlanDistributorGroupAppServiceImpl service;

    private QueryDistributorGroupByPlanRequest request;

    private PageDataDTO<PrivateLivePlanDistributorGroupBO> pageDataDTO;

    @Before
    public void setUp() {
        request = new QueryDistributorGroupByPlanRequest("liveId", 1, 10, true, true,true);
        List<PrivateLivePlanDistributorGroupBO> list = new ArrayList<>();
        list.add(new PrivateLivePlanDistributorGroupBO("liveId", new DistributorGroupBO(), 1, 0));
        PageInfoDTO pageInfoDTO = new PageInfoDTO(1, 10, 1, 1);
        pageDataDTO = new PageDataDTO<>(pageInfoDTO, list);
    }

    /**
     * 测试查询分销组计划时，分销组列表为空的情况
     */
    @Test
    public void testQueryDistributorGroupByPlanEmptyList() {
        //        when(privateLivePlanDistributorGroupRepository.pageQueryPrivateLiveDistributorGroup(any(PageQueryForDistributorGroupRequest.class)))
        //                .thenReturn(new PageDataDTO<>(new PageInfoDTO(), Collections.emptyList()));
        PageDataDTO<PrivateLivePlanDistributorGroupBO> pageDataDTO = new PageDataDTO<>(PageInfoDTO.builder().totalCount(0).build(), Collections.emptyList());
        when(privateLivePlanDistributorGroupRepository.pageQueryPrivateLiveDistributorGroup(any(PageQueryForDistributorGroupRequest.class))).thenReturn(pageDataDTO);
        QueryDistributorGroupByPlanResponse response = service.queryDistributorGroupByPlan(request);
        assert response.getTotalNum() == 0;
        assert response.getDistributorGroupList().isEmpty();
    }

    /**
     * 测试查询分销组计划时，正常情况
     */
    @Test
    public void testQueryDistributorGroupByPlanNormal() {
        when(privateLivePlanDistributorGroupRepository.pageQueryPrivateLiveDistributorGroup(any(PageQueryForDistributorGroupRequest.class))).thenReturn(PageDataDTO.<PrivateLivePlanDistributorGroupBO>builder().list(Collections.singletonList(PrivateLivePlanDistributorGroupBO.builder().distributorGroup(DistributorGroupBO.builder().groupId(1).build()).build())).pageInfoDTO(PageInfoDTO.builder().totalCount(1).build()).build());
        when(distributorGroupRootService.batchQueryDistributorGroupMap(any(List.class))).thenReturn(Collections.singletonMap(1L, new DistributorGroupBO()));
        when(privateLiveDistributionPlanService.queryDistributionPlan(any())).thenReturn(RemoteResponse.success(Collections.singletonList(new PrivateLiveDistributionPlanVO(1L, "liveId", "1", new BigDecimal("10"), new Date(), 1))));
        try (MockedStatic<AnchorAuthUtils> mockedStatic = Mockito.mockStatic(AnchorAuthUtils.class)) {
            mockedStatic.when(AnchorAuthUtils::getMainAnchorId).thenReturn(1L);
            PrivateLiveSettleCountDTO privateLiveSettleCountDTO = new PrivateLiveSettleCountDTO();
            privateLiveSettleCountDTO.setDistributorGroupId("1");
            privateLiveSettleCountDTO.setTotalEstimatedCommission(new BigDecimal("100"));
            privateLiveSettleCountDTO.setTotalSettableCommission(new BigDecimal("50"));
            when(privateLiveSettleServiceProxy.queryCountCommission(any())).thenReturn(CompletableFuture.completedFuture(Collections.singletonList(privateLiveSettleCountDTO)));
            when(AnchorAuthUtils.getMainAnchorId()).thenReturn(1L);
            when(statementQueryServiceProxy.queryStatementTotal(any())).thenReturn(CompletableFuture.completedFuture(Lists.newArrayList()));
            QueryDistributorGroupByPlanResponse response = service.queryDistributorGroupByPlan(request);
            assert response.getTotalNum() == 1;
            assert !response.getDistributorGroupList().isEmpty();
            QueryDistributorGroupByPlanResponse.DistributorGroup distributorGroup = response.getDistributorGroupList().get(0);
            assert distributorGroup.getChannelStatus() == 1;
            assert "10".equals(distributorGroup.getCommissionRate());
            assert "50.00".equals(distributorGroup.getChannelSettleableCommission());
            assert "100.00".equals(distributorGroup.getChannelEstimatedCommission());
        }
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当请求参数正确时的情况
     */
    @Test
    public void testJoinPrivateLiveZoomSuccess() {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(1L, "liveId", 2L);
        PrivateLivePlanDistributorGroupBO bo = new PrivateLivePlanDistributorGroupBO("liveId", DistributorGroupBO.builder().groupId(1).build(), 1, 0);
        when(privateLivePlanDistributorGroupDomainService.joinPrivateLiveZoom(request)).thenReturn(bo);
        // act
        AddDistributorGroupForPlanResponse response = service.joinPrivateLiveZoom(request);
        // assert
        assertNotNull(response);
        assertEquals("liveId", response.getLiveId());
        verify(privateLivePlanDistributorGroupDomainService, times(1)).joinPrivateLiveZoom(request);
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当请求参数 liveId 为 null 时的情况
     */
    @Test(expected = BizSceneException.class)
    public void testJoinPrivateLiveZoomWithNullLiveId() {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(1L, null, 2L);
        // act
        service.joinPrivateLiveZoom(request);
        // assert 通过注解 expected = BizSceneException.class 检查异常
    }

    /**
     * 测试 joinPrivateLiveZoom 方法，当请求参数 distributorGroupId 为 null 时的情况
     */
    @Test(expected = BizSceneException.class)
    public void testJoinPrivateLiveZoomWithNullDistributorGroupId() {
        // arrange
        AddDistributorGroupForPlanRequest request = new AddDistributorGroupForPlanRequest(1L, "liveId", null);
        // act
        service.joinPrivateLiveZoom(request);
        // assert 通过注解 expected = BizSceneException.class 检查异常
    }

    /**
     * 测试查询主播团长列表，当主播团长列表为空时
     */
    @Test
    public void testQueryDistributorGroupForAnchorWhenDistributorGroupListIsEmpty() {
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(1L, "liveId", 1L, 10L);
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorId(anyLong(), anyLong(), anyLong())).thenReturn(null);
        QueryDistributorGroupForAnchorResponse response = service.queryDistributorGroupForAnchor(request);
        assert ObjectUtils.isEmpty(response.getDistributorGroupList());
        assert response.getTotalNum() == 0;
    }

    /**
     * 测试查询主播团长列表，当主播团长列表不为空，但团长信息为空时
     */
    @Test
    public void testQueryDistributorGroupForAnchorWhenDistributorGroupInfoIsEmpty() {
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(1L, "liveId", 1L, 10L);
        PageDataDTO<PrivateLiveAnchorDistributorGroupBO> pageDataDTO = new PageDataDTO<>();
        pageDataDTO.setList(Collections.singletonList(new PrivateLiveAnchorDistributorGroupBO()));
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorId(anyLong(), anyLong(), anyLong())).thenReturn(pageDataDTO);
        DistributionChannelBO channelBO = new DistributionChannelBO();
        //        when(distributorChannelRootService.getChannelByChannelCode(any())).thenReturn(Collections.singletonList(channelBO));
        QueryDistributorGroupForAnchorResponse response = service.queryDistributorGroupForAnchor(request);
        assert response.getDistributorGroupList().isEmpty();
    }

    /**
     * 测试查询主播团长列表，正常情况
     */
    @Test
    public void testQueryDistributorGroupForAnchorNormal() {
        QueryDistributorGroupForAnchorRequest request = new QueryDistributorGroupForAnchorRequest(1L, "liveId", 1L, 10L);
        PageDataDTO<PrivateLiveAnchorDistributorGroupBO> pageDataDTO = new PageDataDTO<>();
        PrivateLiveAnchorDistributorGroupBO bo = new PrivateLiveAnchorDistributorGroupBO();
        bo.setDistributorGroup(DistributorGroupBO.builder().groupId(1).build());
        bo.getDistributorGroup().setGroupId(1);
        pageDataDTO.setList(Collections.singletonList(bo));
        Map<Long, DistributorGroupBO> distributorGroupBOMap = new HashMap<>();
        distributorGroupBOMap.put(1L, DistributorGroupBO.builder().groupId(1).build());
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorId(anyLong(), anyLong(), anyLong())).thenReturn(pageDataDTO);
        //        when(distributorGroupRootService.batchQueryDistributorGroupMap(any())).thenReturn(Collections.singletonMap(1L, new DistributorGroupBO()));
        //        when(privateLivePlanDistributorGroupRepository.countDistributorGroupByLiveIdAndDistributorGroupId(any(), anyLong())).thenReturn(1L);
        QueryDistributorGroupForAnchorResponse response = service.queryDistributorGroupForAnchor(request);
        assert !response.getDistributorGroupList().isEmpty();
        assert !response.getDistributorGroupList().get(0).getBindStatus();
    }

    /**
     * 测试场景：查询计划和分销组成功返回结果
     */
    @Test
    public void testQueryPlanAndDistributorGroup_ReturnsResult() {
        // arrange
        String liveId = "live123";
        Long distributorGroupId = 1L;
        QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest(liveId, distributorGroupId);
        PrivateLivePlanDistributorGroupBO bo = new PrivateLivePlanDistributorGroupBO();
        bo.setLiveId(liveId);
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(distributorGroupId.intValue());
        bo.setDistributorGroup(distributorGroupBO);
        when(privateLivePlanDistributorGroupRepository.queryByLiveIdAndDistributorGroupId(liveId, distributorGroupId)).thenReturn(bo);
        // act
        QueryPlanAndDistributorGroupResponse response = service.queryPlanAndDistributorGroup(request);
        // assert
        assertNotNull(response);
        assertEquals(liveId, response.getLiveId());
        assertEquals(distributorGroupId, response.getDistributorGroupId());
    }

    /**
     * 测试场景：当查询结果为null时
     */
    @Test
    public void testQueryPlanAndDistributorGroup_ReturnsNull() {
        // arrange
        String liveId = "live123";
        Long distributorGroupId = 1L;
        QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest(liveId, distributorGroupId);
        when(privateLivePlanDistributorGroupRepository.queryByLiveIdAndDistributorGroupId(liveId, distributorGroupId)).thenReturn(null);
        // act
        QueryPlanAndDistributorGroupResponse response = service.queryPlanAndDistributorGroup(request);
        // assert
        assertNull(response);
    }

    /**
     * 测试场景：当查询结果的distributorGroup为null时
     */
    @Test
    public void testQueryPlanAndDistributorGroup_DistributorGroupIsNull() {
        // arrange
        String liveId = "live123";
        Long distributorGroupId = 1L;
        QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest(liveId, distributorGroupId);
        PrivateLivePlanDistributorGroupBO bo = new PrivateLivePlanDistributorGroupBO();
        bo.setLiveId(liveId);
        bo.setDistributorGroup(null);
        when(privateLivePlanDistributorGroupRepository.queryByLiveIdAndDistributorGroupId(liveId, distributorGroupId)).thenReturn(bo);
        // act
        QueryPlanAndDistributorGroupResponse response = service.queryPlanAndDistributorGroup(request);
        // assert
        assertNull(response);
    }

    /**
     * 测试queryDistributorGroupCountByLiveId方法，当传入的liveIds列表为空时
     */
    @Test
    public void testQueryDistributorGroupCountByLiveIdWithEmptyList() {
        // arrange
        List<String> liveIds = Arrays.asList();
        // act
        Map<String, Long> result = service.queryDistributorGroupCount(liveIds);
        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试queryDistributorGroupCountByLiveId方法，当传入的liveIds列表包含重复元素时
     */
    @Test
    public void testQueryDistributorGroupCountByLiveIdWithDuplicatedElements() {
        // arrange
        List<String> liveIds = Arrays.asList("liveId1", "liveId1");
        when(privateLivePlanDistributorGroupRepository.countValidDistributorGroup("liveId1")).thenReturn(2L);
        // act
        Map<String, Long> result = service.queryDistributorGroupCount(liveIds);
        // assert
        assertEquals("结果应包含一个元素", 1, result.size());
        assertEquals("liveId1的计数应为2", Long.valueOf(2), result.get("liveId1"));
    }

    /**
     * 测试queryDistributorGroupCountByLiveId方法，当传入的liveIds列表包含多个不同元素时
     */
    @Test
    public void testQueryDistributorGroupCountByLiveIdWithMultipleElements() {
        // arrange
        List<String> liveIds = Arrays.asList("liveId1", "liveId2");
        when(privateLivePlanDistributorGroupRepository.countValidDistributorGroup("liveId1")).thenReturn(2L);
        when(privateLivePlanDistributorGroupRepository.countValidDistributorGroup("liveId2")).thenReturn(3L);
        // act
        Map<String, Long> result = service.queryDistributorGroupCount(liveIds);
        // assert
        assertEquals("结果应包含两个元素", 2, result.size());
        assertEquals("liveId1的计数应为2", Long.valueOf(2), result.get("liveId1"));
        assertEquals("liveId2的计数应为3", Long.valueOf(3), result.get("liveId2"));
    }
}
