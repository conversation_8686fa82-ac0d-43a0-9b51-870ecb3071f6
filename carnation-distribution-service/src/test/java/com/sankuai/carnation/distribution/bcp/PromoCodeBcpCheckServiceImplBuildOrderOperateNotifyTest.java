package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.bcp.PromoCodeBcpCheckServiceImpl;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.sankuai.technician.trade.order.dto.model.OrderVerifyReceiptDto;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.Mock;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplBuildOrderOperateNotifyTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    private OrderReceiptDto orderReceiptDto;

    private OrderVerifyReceiptDto orderVerifyReceiptDto;

    @Before
    public void setUp() {
        orderReceiptDto = new OrderReceiptDto();
        orderVerifyReceiptDto = new OrderVerifyReceiptDto();
    }

    @Test
    public void testBuildOrderOperateNotify_StatusNotOrderVerified() throws Throwable {
        orderReceiptDto.setProductType(1);
        orderReceiptDto.setProductId(1L);
        orderReceiptDto.setOrderId("orderId");
        orderReceiptDto.setOrderType(1);
        orderVerifyReceiptDto.setStatus(2);
        orderVerifyReceiptDto.setCouponId("couponId");
        orderVerifyReceiptDto.setActionTime(new Date());
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("key", "value");
        orderReceiptDto.setExtInfo(extInfo);
        com.sankuai.carnation.distribution.bcp.OrderOperateNotifyBcpDTO result = promoCodeBcpCheckService.buildOrderOperateNotify(orderReceiptDto, orderVerifyReceiptDto);
        assertNotNull(result);
        assertEquals(orderReceiptDto.getProductType(), result.getProductType());
        assertEquals(orderReceiptDto.getProductId(), result.getProductId());
        assertEquals(orderReceiptDto.getOrderId(), result.getOrderId());
        assertEquals(orderReceiptDto.getOrderType(), result.getOrderType());
        assertNull(result.getOperateType());
        assertEquals(orderVerifyReceiptDto.getCouponId(), result.getBizId());
        assertEquals(orderReceiptDto.getChannel(), result.getChannel());
        assertEquals(Long.valueOf(orderVerifyReceiptDto.getActionTime().getTime()), result.getActionTime());
        assertEquals(orderReceiptDto.getExtInfo(), result.getExtInfo());
    }

    @Test(expected = NullPointerException.class)
    public void testBuildOrderOperateNotify_NullObject() throws Throwable {
        promoCodeBcpCheckService.buildOrderOperateNotify(null, null);
    }
}
