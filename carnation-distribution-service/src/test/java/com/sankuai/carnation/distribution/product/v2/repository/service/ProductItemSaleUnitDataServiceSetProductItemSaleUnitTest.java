package com.sankuai.carnation.distribution.product.v2.repository.service;

import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.PluralProductBO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.Future;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceSetProductItemSaleUnitTest {

    @Mock
    private ProductItemSaleUnitMapper mapper;

    @Mock
    private com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService productSelectifyAclService;

    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    private long productItemId = 1L;

    private ProductItemBO productItemBO;

    @Before
    public void setUp() throws Exception {
        productItemSaleUnitDataService = new ProductItemSaleUnitDataService();
        // 注入依赖
        injectField("mapper", mapper);
        injectField("productSelectifyAclService", productSelectifyAclService);
        // Mock线程池
        ThreadPoolExecutor mockUpdatePool = mock(ThreadPoolExecutor.class);
        ThreadPoolExecutor mockInsertPool = mock(ThreadPoolExecutor.class);
        // 配置线程池行为
        Future mockFuture = mock(Future.class);
        // 注入mock的线程池
        injectField("updateDataPool", mockUpdatePool);
        injectField("insertDataPool", mockInsertPool);
    }

    private void injectField(String fieldName, Object value) throws Exception {
        Field field = ProductItemSaleUnitDataService.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(productItemSaleUnitDataService, value);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitWithInvalidProductItemId() throws Throwable {
        productItemBO = new ProductItemBO();
        productItemBO.setProductItemId(productItemId);
        productItemBO.setRelateProductList(Arrays.asList(new PluralProductBO(1, 1L, 1, 1L, 1)));
        productItemSaleUnitDataService.setProductItemSaleUnit(0L, productItemBO);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitWithNullProductItemBO() throws Throwable {
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, (ProductItemBO) null);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitWithEmptyRelateProductList() throws Throwable {
        productItemBO = new ProductItemBO();
        productItemBO.setProductItemId(productItemId);
        productItemBO.setRelateProductList(Collections.emptyList());
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, productItemBO);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitWithDatabaseOperationFailure() throws Throwable {
        productItemBO = new ProductItemBO();
        productItemBO.setProductItemId(productItemId);
        productItemBO.setRelateProductList(Arrays.asList(new PluralProductBO(1, 1L, 1, 1L, 1)));
        when(mapper.selectByExample(any())).thenThrow(new ProductItemException("新增关联商品失败"));
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, productItemBO);
    }
}
