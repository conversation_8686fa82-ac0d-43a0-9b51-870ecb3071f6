package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.acl.wechat.WechatUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.ConsultantCustomerRankQueryRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.CustomerTradeRankDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantSummaryRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;

import static org.mockito.Mockito.when;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.miniprogram.WechatUserInfoDTO;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantSummaryServiceImpl_QueryCustomerTradeRankListTest {

    @InjectMocks
    private PrivateLiveConsultantSummaryServiceImpl privateLiveConsultantSummaryService;

    @Mock
    private PrivateLiveConsultantSummaryRepository privateLiveConsultantSummaryRepository;

    @Mock
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private WechatUserAclService wechatUserAclService;

    @Mock
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    private ConsultantCustomerRankQueryRequest createRequest() {
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("testLiveId");
        request.setRankNum(10);
        return request;
    }

    private Map<Long, Long> createConsultantMtUserIds() {
        Map<Long, Long> consultantMtUserIds = new HashMap<>();
        consultantMtUserIds.put(1L, 1L);
        return consultantMtUserIds;
    }

    private List<PrivateLiveConsultantUserStatistics> createUserStatistics() {
        return Arrays.asList(new PrivateLiveConsultantUserStatistics());
    }

    private List<Long> createUserIds() {
        return Arrays.asList(1L);
    }

    private Map<Long, PrivateLiveConsultantTask> createTaskId2DetailMap() {
        Map<Long, PrivateLiveConsultantTask> taskId2DetailMap = new HashMap<>();
        taskId2DetailMap.put(1L, new PrivateLiveConsultantTask());
        return taskId2DetailMap;
    }

    private Map<Long, WechatUserInfoDTO> createUserId2Info() {
        Map<Long, WechatUserInfoDTO> userId2Info = new HashMap<>();
        userId2Info.put(1L, new WechatUserInfoDTO());
        return userId2Info;
    }

    @Test
    public void testQueryCustomerTradeRankListException() throws Throwable {
        ConsultantCustomerRankQueryRequest request = createRequest();
        when(privateLiveConsultantAccountRepository.batchLoadConsultantUserId(any(String.class))).thenThrow(new RuntimeException());
        RemoteResponse<CustomerTradeRankDTO> response = privateLiveConsultantSummaryService.queryCustomerTradeRankList(request);
        assertEquals("客资交易排行查询失败", response.getMsg());
    }

    @Test
    public void testQueryCustomerTradeRankListEmptyResult() throws Throwable {
        ConsultantCustomerRankQueryRequest request = createRequest();
        Map<Long, Long> consultantMtUserIds = createConsultantMtUserIds();
        when(privateLiveConsultantAccountRepository.batchLoadConsultantUserId(any(String.class))).thenReturn(consultantMtUserIds);
        when(privateLiveConsultantSummaryRepository.queryCustomerStatisticRank(any(String.class), any(Integer.class), any(List.class))).thenReturn(null);
        RemoteResponse<CustomerTradeRankDTO> response = privateLiveConsultantSummaryService.queryCustomerTradeRankList(request);
        assertEquals("success", response.getMsg());
        assertEquals("testLiveId", response.getData().getLiveId());
    }
}
