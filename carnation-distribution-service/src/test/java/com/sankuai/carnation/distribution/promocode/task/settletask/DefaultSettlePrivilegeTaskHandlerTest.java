package com.sankuai.carnation.distribution.promocode.task.settletask;

import com.sankuai.carnation.distribution.promocode.task.dto.PrivilegeTaskItemDTO;
import com.sankuai.carnation.distribution.promocode.task.dto.PromoCodeShopPromoteTradeDataDTO;
import com.sankuai.carnation.distribution.promocode.task.model.PrivilegeTaskConfigBO;
import com.sankuai.carnation.distribution.promocode.task.model.PrivilegeTaskRequirementsBO;
import com.sankuai.carnation.distribution.promocode.task.settletask.DefaultSettlePrivilegeTaskHandler;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class DefaultSettlePrivilegeTaskHandlerTest {

    @InjectMocks
    private DefaultSettlePrivilegeTaskHandler handler;

    @Mock
    private PrivilegeTaskConfigBO taskConfig;

    @Mock
    private PrivilegeTaskRequirementsBO taskRequirements;

    @Mock
    private PromoCodeShopPromoteTradeDataDTO tradeData;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(taskConfig.getRequirements()).thenReturn(taskRequirements);
    }

    @Test
    public void testShopOrderAmountRequirementCompleteText() {
        // 设置测试数据
        Integer rankRequirement = 10;
        when(taskRequirements.getShopCodeOrderAmount()).thenReturn(BigDecimal.valueOf(rankRequirement));
        when(taskRequirements.getShopCodeOrderAmountCityRank()).thenReturn(rankRequirement);
        // 调用被测试的方法
        List<PrivilegeTaskItemDTO> result = handler.isTaskCompletedDetail(taskConfig, tradeData, null);

        // 验证结果
        assertFalse("结果列表不应为空", result.isEmpty());

        PrivilegeTaskItemDTO targetItem = null;
        for (PrivilegeTaskItemDTO item : result) {
            if (item.getRequirement().contains("城市优惠码金额排名前")) {
                targetItem = item;
                break;
            }
        }

        assertNotNull("应该存在包含 '城市优惠码金额排名前' 的项目", targetItem);
        assertEquals("要求文本应该匹配预期格式",
                "城市优惠码金额排名前" + rankRequirement,
                targetItem.getRequirement());
    }

    @Test
    public void testShopOrderAmountRequirementNotCompleteText() {
        // 设置测试数据
        Integer rankRequirement = 10;
        when(taskRequirements.getShopCodeOrderAmount()).thenReturn(BigDecimal.valueOf(rankRequirement));
        when(taskRequirements.getShopCodeOrderAmountCityRank()).thenReturn(rankRequirement);
        // 调用被测试的方法
        List<PrivilegeTaskItemDTO> result = handler.isTaskNotCompletedDetail(taskConfig, null);

        // 验证结果
        assertFalse("结果列表不应为空", result.isEmpty());

        PrivilegeTaskItemDTO targetItem = null;
        for (PrivilegeTaskItemDTO item : result) {
            if (item.getRequirement().contains("城市优惠码金额排名前")) {
                targetItem = item;
                break;
            }
        }

        assertNotNull("应该存在包含 '城市优惠码金额排名前' 的项目", targetItem);
        assertEquals("要求文本应该匹配预期格式",
                "城市优惠码金额排名前" + rankRequirement,
                targetItem.getRequirement());
    }
}
