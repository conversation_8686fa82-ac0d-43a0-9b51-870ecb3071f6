package com.sankuai.carnation.distribution.bcp;

import com.sankuai.technician.trade.api.settle.tradereturn.enums.ReturnBizLineEnum;
import com.dianping.lion.client.Lion;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BcpCheckRuleHelperTest {

    /**
     * Tests getRule method when bizLine is null.
     */
    @Test
    public void testGetRuleBizLineIsNull() throws Throwable {
        // Arrange
        ReturnBizLineEnum bizLine = null;
        // Act
        BcpCheckRule result = BcpCheckRuleHelper.getRule(bizLine);
        // Assert
        assertNull(result);
    }

    /**
     * Tests getRule method when bizLine is not null but no rule in Lion.
     */
    @Test
    public void testGetRuleBizLineIsNotNullButNoRuleInLion() throws Throwable {
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            // Arrange
            ReturnBizLineEnum bizLine = ReturnBizLineEnum.BEAUTY_GROUP_RETURN;
            mockedStatic.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn("{}");
            // Act
            BcpCheckRule result = BcpCheckRuleHelper.getRule(bizLine);
            // Assert
            assertNull(result);
        }
    }

    /**
     * Tests getRule method when bizLine is not null and there is a rule in Lion.
     */
    @Test
    public void testGetRuleBizLineIsNotNullAndRuleInLion() throws Throwable {
        try (MockedStatic<Lion> mockedStatic = mockStatic(Lion.class)) {
            // Arrange
            ReturnBizLineEnum bizLine = ReturnBizLineEnum.BEAUTY_GROUP_RETURN;
            String json = "{\"BEAUTY_GROUP_RETURN\": {\"billMode\": \"PERIOD_BATCH_MODE\",\"batchNoType\": \"MONTH_BATCH\",\"displayName\": \"丽人扫码返现\",\"useBillCommand\": true,\"validCommandList\": [1, 2, 3],\"needTradeRiskControl\": true,\"needAccountModule\": true}}";
            mockedStatic.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn(json);
            // Act
            BcpCheckRule result = BcpCheckRuleHelper.getRule(bizLine);
            // Assert
            assertNotNull(result);
            assertEquals("周期全量计费", result.getBillMode().getDesc());
            assertEquals("按月批次", result.getBatchNoType().getDesc());
            assertEquals("丽人扫码返现", result.getDisplayName());
            assertTrue(result.isUseBillCommand());
            assertEquals(3, result.getValidCommandList().size());
            assertTrue(result.isNeedTradeRiskControl());
            assertTrue(result.isNeedAccountModule());
        }
    }
}
