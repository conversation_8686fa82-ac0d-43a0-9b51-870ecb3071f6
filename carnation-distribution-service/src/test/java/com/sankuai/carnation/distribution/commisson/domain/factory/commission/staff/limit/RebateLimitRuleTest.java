package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.limit;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleLimitRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.sankuai.carnation.distribution.commisson.limit.rule.ShopAmountLimitRule;
import com.sankuai.carnation.distribution.commisson.repository.RebateLimitRepository;
import com.sankuai.carnation.distribution.commisson.repository.db.DistributorRebateLimitTab;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * @description :
 * @date : 2024/7/31
 */
@RunWith(MockitoJUnitRunner.class)
public class RebateLimitRuleTest {
    @InjectMocks
    private ShopAmountLimitRule rebateLimitRule; // 无法mock抽象类，使用子类来完成mock

    @Mock
    private RebateLimitRepository rebateLimitRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试handleResult方法，当getLimitRule返回null时
     */
    @Test
    public void testHandleResultWithNullLimitRule() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        RebateAmountResult rebateAmountResult = new RebateAmountResult();
        RebateActivityRuleDTO rule = new RebateActivityRuleDTO();

        RebateLimitResult result = rebateLimitRule.handleResult(request, rebateAmountResult, rule);

        assertFalse(result.isResult());
        assertEquals(0L, result.getRebateAmount().longValue());
        assertNull(result.getReason());

        verifyNoMoreInteractions(rebateLimitRepository);
    }

    /**
     * 测试handleResult方法，当checkOrderIsHave返回null时
     */
    @Test
    public void testHandleResultWithNullOrder() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        RebateAmountResult rebateAmountResult = new RebateAmountResult();
        rebateAmountResult.setOrderId("orderId");
        RebateActivityRuleDTO rule = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleData = new RebateSettleRuleDTO();
        RebateSettleLimitRuleDTO rebateSettleLimitRule = new RebateSettleLimitRuleDTO();
        rebateSettleLimitRule.setShopAmountLimit(100L);
        ruleData.setRebateSettleLimitRule(rebateSettleLimitRule);
        rule.setRule(ruleData);

        when(rebateLimitRepository.sumOrderRebateAmountByDpShopId(any(), any())).thenReturn(50L);
        when(rebateLimitRepository.queryOrderByUnifiedOrderId("orderId")).thenReturn(null);

        RebateLimitResult result = rebateLimitRule.handleResult(request, rebateAmountResult, rule);

        assertNotNull(result);
        verify(rebateLimitRepository, times(1)).queryOrderByUnifiedOrderId("orderId");
    }

    /**
     * 测试handleResult方法，当checkOrderIsHave返回非null且限额小于返利金额时
     */
    @Test
    public void testHandleResultWithOrderAndLimitLessThanRebate() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        RebateAmountResult rebateAmountResult = new RebateAmountResult();
        rebateAmountResult.setOrderId("orderId");
        rebateAmountResult.setRebateAmount(200L);
        RebateActivityRuleDTO rule = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleData = new RebateSettleRuleDTO();
        RebateSettleLimitRuleDTO rebateSettleLimitRule = new RebateSettleLimitRuleDTO();
        rebateSettleLimitRule.setShopAmountLimit(100L);  // 设置返利结算限额为100
        ruleData.setRebateSettleLimitRule(rebateSettleLimitRule);
        rule.setRule(ruleData);
        DistributorRebateLimitTab tab = new DistributorRebateLimitTab();
        tab.setRebateAmount(10L);
        tab.setExtInfo("limit");

        // 设置本次命中规则只能返利50
        when(rebateLimitRepository.sumOrderRebateAmountByDpShopId(any(), any())).thenReturn(50L);
        // 设置该笔订单已经返利100了
        when(rebateLimitRepository.queryOrderByUnifiedOrderId("orderId")).thenReturn(tab);

        RebateLimitResult result = rebateLimitRule.handleResult(request, rebateAmountResult, rule);

        assertTrue(result.isResult());
        assertEquals("该笔订单已经返利过10元，且命中返利规则中的返利金额大于10元",10, result.getRebateAmount().longValue());
        assertEquals("limit", result.getReason());

        verify(rebateLimitRepository, times(1)).queryOrderByUnifiedOrderId("orderId");
    }
}
