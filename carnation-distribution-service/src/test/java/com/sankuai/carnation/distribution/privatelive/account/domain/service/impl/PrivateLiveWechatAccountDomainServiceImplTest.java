package com.sankuai.carnation.distribution.privatelive.account.domain.service.impl;

import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountCmd;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.account.converter.PrivateLiveWechatAccountConverter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PrivateLiveWechatAccountDomainServiceImplTest {

    @InjectMocks
    private PrivateLiveWechatAccountDomainServiceImpl service;

    @Mock
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @Mock
    private PrivateLiveWechatAccountConverter privateLiveWechatAccountConverter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 registerWechatAccount 方法，正常情况
     */
    @Test
    public void testRegisterWechatAccountNormal() throws Throwable {
        // arrange
        PrivateLiveAccountCmd cmd = new PrivateLiveAccountCmd("unionId", "openId", true);
        PrivateLiveConsultantAccount account = new PrivateLiveConsultantAccount();
        account.setUnionId("unionId");
        account.setOpenId("openId");
        account.setStatus(DistributionStatusEnum.VALID.getCode());
        when(privateLiveConsultantAccountRepository.insert(any(PrivateLiveConsultantAccount.class))).thenReturn(1L);
        PrivateLiveAccountBO expectedBO = new PrivateLiveAccountBO();
        expectedBO.setId(1L);
        when(privateLiveWechatAccountConverter.toEntity(any(PrivateLiveConsultantAccount.class))).thenReturn(expectedBO);

        // act
        PrivateLiveAccountBO result = service.registerWechatAccount(cmd);

        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1L), result.getId());
        verify(privateLiveConsultantAccountRepository, times(1)).insert(any(PrivateLiveConsultantAccount.class));
        verify(privateLiveWechatAccountConverter, times(1)).toEntity(any(PrivateLiveConsultantAccount.class));
    }

    /**
     * 测试 registerWechatAccount 方法，当 privateLiveConsultantAccountRepository.insert 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testRegisterWechatAccountInsertThrowsException() throws Throwable {
        // arrange
        PrivateLiveAccountCmd cmd = new PrivateLiveAccountCmd("unionId", "openId", true);
        when(privateLiveConsultantAccountRepository.insert(any(PrivateLiveConsultantAccount.class))).thenThrow(new RuntimeException());

        // act
        service.registerWechatAccount(cmd);

        // assert 通过注解 @Test(expected = RuntimeException.class) 验证
    }
}
