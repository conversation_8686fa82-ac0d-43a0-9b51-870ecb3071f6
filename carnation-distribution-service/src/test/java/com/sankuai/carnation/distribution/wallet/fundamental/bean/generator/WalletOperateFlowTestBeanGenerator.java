package com.sankuai.carnation.distribution.wallet.fundamental.bean.generator;

import com.sankuai.carnation.distribution.wallet.fundamental.domain.flow.bo.WalletDataItemBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.flow.bo.WalletOperateFlowBO;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/13
 **/
public class WalletOperateFlowTestBeanGenerator {

    public static WalletOperateFlowBO buildWalletOperateFlowBO() {
        WalletOperateFlowBO operateFlow = new WalletOperateFlowBO();
        operateFlow.setAmount(100L);
        operateFlow.setFlowType(1);
        operateFlow.setOperateType(1);
        operateFlow.setOperator("operator");
        operateFlow.setSearchId("4962988985707657652");
        operateFlow.setSecondSearchId("**********");
        operateFlow.setWalletAccountId(312L);
        operateFlow.setWalletActivityAccountId(112L);

        WalletDataItemBO dataItem = new WalletDataItemBO();
        dataItem.setAmount(100L);
        dataItem.setBizCode("tech_distribution");
        dataItem.setBizId("1814231323410206791");
        dataItem.setOperateType(1);
        dataItem.setSearchId("4962988985707657652");
        dataItem.setSecondSearchId("**********");
        dataItem.setUnifiedOrderId("4962988985707657652");
        dataItem.setWalletAccountId(312L);
        dataItem.setWalletActivityAccountId(112L);

        operateFlow.setDataItem(dataItem);
        return operateFlow;
    }

}
