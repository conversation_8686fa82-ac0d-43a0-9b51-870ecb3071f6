package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.ConsultantCustomerRankQueryRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.ConsultantFollowRankDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantSummaryServiceImpl_QueryConsultantFollowRankListTest {

    @InjectMocks
    private PrivateLiveConsultantSummaryServiceImpl privateLiveConsultantSummaryService;

    @Mock
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;

    @Test
    public void testQueryConsultantFollowRankListNoConsultants() throws Throwable {
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("testLiveId");
        when(privateLiveConsultantTaskRepository.loadLiveAllConsultant(request.getLiveId())).thenReturn(Collections.emptyList());
        RemoteResponse<ConsultantFollowRankDTO> response = privateLiveConsultantSummaryService.queryConsultantFollowRankList(request);
        assertNotNull(response);
        assertNotNull(response.getData());
        assertTrue(response.getData().getConsultantFollowDTOList().isEmpty());
    }

    @Test
    public void testQueryConsultantFollowRankListNoFollowInfo() throws Throwable {
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("testLiveId");
        when(privateLiveConsultantTaskRepository.loadLiveAllConsultant(request.getLiveId())).thenReturn(Collections.singletonList(new PrivateLiveConsultantTask()));
        when(privateSphereUserAclService.queryConsultantFollowDataByLiveId(request.getLiveId())).thenReturn(null);
        RemoteResponse<ConsultantFollowRankDTO> response = privateLiveConsultantSummaryService.queryConsultantFollowRankList(request);
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getConsultantFollowDTOList().size());
    }
}
