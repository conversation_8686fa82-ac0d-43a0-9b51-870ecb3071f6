package com.sankuai.carnation.distribution.consultant;


import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.account.ConsultantAccountDomainService;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class ConsultantAccountDomainServiceTest {

    @InjectMocks
    private ConsultantAccountDomainService consultantAccountDomainService;

    @Mock
    private UniversalQRCodeGeneratorService universalQRCodeGeneratorService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 liveId 或 anchorId 为空的情况
     */
    @Test
    public void testQueryOrCreateRegisterConsultantQrCode_InvalidParams() {
        // arrange
        String liveId = "";
        Long anchorId = null;

        // act
        RemoteResponse<String> result = consultantAccountDomainService.queryOrCreateRegisterConsultantQrCode(liveId, anchorId);

        // assert
        assertNotNull(result);
        assertEquals("参数非法", result.getMsg());
    }

    /**
     * 测试成功查询到二维码配置的情况
     */
    @Test
    public void testQueryOrCreateRegisterConsultantQrCode_QuerySuccess() {
        // arrange
        String liveId = "live123";
        Long anchorId = 123L;
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setImageUrl("http://example.com/qrcode.png");
        RemoteResponse<QRCodeConfigDTO> qrCodeConfigDTORemoteResponse = RemoteResponse.success(qrCodeConfigDTO);

        when(universalQRCodeGeneratorService.queryQRCode(liveId, BizTypeEnum.PRIVATE_LIVE_CONSULTANT.getType())).thenReturn(qrCodeConfigDTORemoteResponse);

        // act
        RemoteResponse<String> result = consultantAccountDomainService.queryOrCreateRegisterConsultantQrCode(liveId, anchorId);

        // assert
        assertNotNull(result);
        assertEquals("http://example.com/qrcode.png", result.getData());
    }

    /**
     * 测试查询二维码配置失败，进而创建二维码配置的情况
     */
    @Test
    public void testQueryOrCreateRegisterConsultantQrCode_CreateSuccess() {
        // arrange
        String liveId = "live123";
        Long anchorId = 123L;
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setImageUrl("http://example.com/qrcode.png");
        RemoteResponse<QRCodeConfigDTO> qrCodeConfigDTORemoteResponse = RemoteResponse.success(qrCodeConfigDTO);

        when(universalQRCodeGeneratorService.queryQRCode(liveId, BizTypeEnum.PRIVATE_LIVE_CONSULTANT.getType())).thenReturn(null);
        when(universalQRCodeGeneratorService.insertQRCode(any(QRCodeConfigDTO.class))).thenReturn(qrCodeConfigDTORemoteResponse);

        // act
        RemoteResponse<String> result = consultantAccountDomainService.queryOrCreateRegisterConsultantQrCode(liveId, anchorId);

        // assert
        assertNotNull(result);
        assertEquals("http://example.com/qrcode.png", result.getData());
    }

}
