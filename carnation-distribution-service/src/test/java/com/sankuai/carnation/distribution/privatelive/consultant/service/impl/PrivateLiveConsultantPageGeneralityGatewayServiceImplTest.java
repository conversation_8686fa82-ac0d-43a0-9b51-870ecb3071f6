
package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;


import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveWxAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantApplicantDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.ProductShareLinkDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.UserIntentionStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl.PrivateLiveConsultantPageGeneralityGatewayServiceImpl;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveproductshelf.ProductLinkUrlDTO;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantPageGeneralityGatewayServiceImplTest {

    @Mock
    private UserAuthorizeUtil userAuthorizeUtil;

    @Mock
    private RpcUserRetrieveService.Iface userRetrieveService;

    @Mock
    private PrivateLiveUserIntentionResultRepository privateLiveUserIntentionResultRepository;

    @InjectMocks
    private PrivateLiveConsultantPageGeneralityGatewayServiceImpl service;

    @Mock
    private UniversalQRCodeGeneratorService generatorService;

    @Mock
    private PrivateLiveWxAclService privateLiveWxAclService;

    private final Long validMtUserId = 100L;
    private final Long validTaskId = 200L;
    private final String validWxToken = "validToken";
    private final String validMobile = "13800000000";

    @Before
    public void setUp() throws Exception {
        PrivateLiveConsultantApplicantDTO mockApplicantDTO = new PrivateLiveConsultantApplicantDTO();
        when(userAuthorizeUtil.authentication(eq(validTaskId), eq(validWxToken))).thenReturn(mockApplicantDTO);
        when(privateLiveUserIntentionResultRepository.queryByTaskIdAndUserId(eq(validMtUserId), eq(validTaskId), eq(UserIntentionStatusEnum.VALID_STATUS_LIST))).thenReturn(true);

        UserFields userFields = new UserFields();
        userFields.setMobile(true);
        UserModel userModel = new UserModel();
        userModel.setMobile(validMobile);
        UserRespMsg userRespMsg = new UserRespMsg(true);
        userRespMsg.setUser(userModel);
    }

    /**
     * 测试getUserRealPhone方法，当mtUserId为null时，应返回参数异常。
     */
    @Test
    public void testGetUserRealPhoneWithNullMtUserId() throws Throwable {
        // arrange

        // act
        RemoteResponse<String> result = service.getUserRealPhone(null, validTaskId, validWxToken);

        // assert
        assertNotNull(result);
    }


    /**
     * 测试getUserRealPhone方法，当用户鉴权信息为空时，应返回null。
     */
    @Test
    public void testGetUserRealPhoneWithNullAuthorization() throws Throwable {
        // arrange
        when(userAuthorizeUtil.authentication(eq(validTaskId), eq(validWxToken))).thenReturn(null);

        // act
        RemoteResponse<String> result = service.getUserRealPhone(validMtUserId, validTaskId, validWxToken);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试getUserRealPhone方法，当查询用户意向表信息失败时，应返回null。
     */
    @Test
    public void testGetUserRealPhoneWithQueryIntentionFail() throws Throwable {
        // arrange
        when(privateLiveUserIntentionResultRepository.queryByTaskIdAndUserId(eq(validMtUserId), eq(validTaskId), eq(UserIntentionStatusEnum.VALID_STATUS_LIST))).thenReturn(false);

        // act
        RemoteResponse<String> result = service.getUserRealPhone(validMtUserId, validTaskId, validWxToken);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试getUserRealPhone方法，当获取用户信息失败时，应返回获取用户手机号失败。
     */
    @Test
    public void testGetUserRealPhoneWithUserRetrieveFail() throws Throwable {
        // arrange
        UserRespMsg userRespMsg = new UserRespMsg(false);
        when(userRetrieveService.getUserByIdWithMsg(eq(validMtUserId), any(UserFields.class))).thenReturn(userRespMsg);
        // act
        RemoteResponse<String> result = service.getUserRealPhone(validMtUserId, validTaskId, validWxToken);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试参数异常情况
     */
    @Test
    public void testGetProductQrCodeNewParamError() throws Throwable {
        // arrange
        Long productId = null;
        Integer productType = null;
        Long taskId = 1L;
        String wxToken = "token";

        // act
        RemoteResponse<String> result = (RemoteResponse<String>) ReflectionTestUtils.invokeMethod(service, "getProductQrCodeNew", productId, productType, taskId, wxToken);

        // assert
        assert "参数异常".equals(result.getMsg());
    }

    /**
     * 测试查询二维码成功情况
     */
    @Test
    public void testGetProductQrCodeNewQueryQRCodeSuccess() throws Throwable {
        // arrange
        Long productId = 1L;
        Integer productType = ProductTypeEnum.PREPAID.getValue();
        Long taskId = 1L;
        String wxToken = "token";
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setDistributorCode("distributorCode");
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setImageUrl("imageUrl");
        RemoteResponse<QRCodeConfigDTO> qrCodeResponse = RemoteResponse.success(qrCodeConfigDTO);

        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(dto);
        when(generatorService.queryQRCode(anyString(), anyInt())).thenReturn(qrCodeResponse);

        // act
        RemoteResponse<String> result = (RemoteResponse<String>) ReflectionTestUtils.invokeMethod(service, "getProductQrCodeNew", productId, productType, taskId, wxToken);

        // assert
        assert "imageUrl".equals(result.getData());
    }

    /**
     * 测试获取商品链接失败情况
     */
    @Test
    public void testGetProductQrCodeNewQueryProductLinkUrlFail() throws Throwable {
        // arrange
        Long productId = 1L;
        Integer productType = ProductTypeEnum.PREPAID.getValue();
        Long taskId = 1L;
        String wxToken = "token";
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setDistributorCode("distributorCode");

        when(userAuthorizeUtil.authentication(anyLong(), anyString())).thenReturn(dto);
        when(generatorService.queryQRCode(anyString(), anyInt())).thenReturn(null);
        when(privateLiveWxAclService.queryProductLinkUrl(anyString(), anyString(), anyInt(), anyLong())).thenReturn(null);

        // act
        RemoteResponse<String> result = (RemoteResponse<String>) ReflectionTestUtils.invokeMethod(service, "getProductQrCodeNew", productId, productType, taskId, wxToken);

        // assert
        assert "获取商品链接失败".equals(result.getMsg());
    }

    /**
     * 参数异常测试
     */
    @Test
    public void testGetProductUrlNewParamError() throws Throwable {
        // arrange
        Long productId = null;
        Integer productType = null;
        Long taskId = null;
        String wxToken = null;

        // act
        RemoteResponse<ProductShareLinkDTO> result = (RemoteResponse<ProductShareLinkDTO>) ReflectionTestUtils.invokeMethod(service, "getProductUrlNew", productId, productType, taskId, wxToken);

        // assert
        assert "参数异常".equals(result.getMsg());
    }

    /**
     * 获取商品链接失败测试
     */
    @Test
    public void testGetProductUrlNewQueryProductLinkUrlIsNull() throws Throwable {
        // arrange
        Long productId = 1L;
        Integer productType = ProductTypeEnum.PREPAID.getValue();
        Long taskId = 1L;
        String wxToken = "token";
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setLiveId("liveId");
        dto.setDistributorCode("distributorCode");
        when(userAuthorizeUtil.authentication(anyLong(), any())).thenReturn(dto);
        when(privateLiveWxAclService.queryProductLinkUrl(any(), any(), anyInt(), anyLong())).thenReturn(null);

        // act
        RemoteResponse<ProductShareLinkDTO> result = (RemoteResponse<ProductShareLinkDTO>) ReflectionTestUtils.invokeMethod(service, "getProductUrlNew", productId, productType, taskId, wxToken);

        // assert
        assert "获取商品链接失败".equals(result.getMsg());
    }

    @Test
    public void testGetProductUrlNew() {
        Long productId = 1L;
        Integer productType = ProductTypeEnum.PREPAID.getValue();
        Long taskId = 1L;
        String wxToken = "token";
        PrivateLiveConsultantApplicantDTO dto = new PrivateLiveConsultantApplicantDTO();
        dto.setLiveId("liveId");
        dto.setDistributorCode("distributorCode");
        when(userAuthorizeUtil.authorize(anyLong(), any())).thenReturn(dto);
        ProductLinkUrlDTO productLinkUrlDTO = new ProductLinkUrlDTO();
        productLinkUrlDTO.setDetailJumpUrl("detailJumpUrl");
        productLinkUrlDTO.setDetailRelativeJumpUrl("detailRelativeJumpUrl");
        when(privateLiveWxAclService.queryProductLinkUrl(any(), any(), anyInt(), anyLong())).thenReturn(productLinkUrlDTO);
        when(privateLiveWxAclService.generateShotLink(any(), any(), any())).thenReturn(CompletableFuture.completedFuture("shortLink"));

        RemoteResponse<ProductShareLinkDTO> result = (RemoteResponse<ProductShareLinkDTO>) ReflectionTestUtils.invokeMethod(service, "getProductUrlNew", productId, productType, taskId, wxToken);

    }
}
