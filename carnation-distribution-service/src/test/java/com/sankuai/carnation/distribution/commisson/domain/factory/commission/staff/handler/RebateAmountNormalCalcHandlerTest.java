package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.handler;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleNormalRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

/**
 * 测试 RebateAmountNormalCalcHandler 的 calculate 方法
 */
public class RebateAmountNormalCalcHandlerTest {

    @InjectMocks
    private RebateAmountNormalCalcHandler handler;

    @Mock
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;

    @Mock
    private RedisStoreClient redisStoreClient;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常计算返利金额的场景
     */
    @Test
    public void testCalculateNormalScenario() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("123");
        request.setPayCent(1000L);
        request.setUserId(1L);
        request.setPlatform(1);

        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rebateActivityRuleDTO.setRule(rule);
        RebateSettleNormalRuleDTO normalRule = new RebateSettleNormalRuleDTO();
        rebateActivityRuleDTO.getRule().setNormalRule(normalRule);
        rebateActivityRuleDTO.getRule().getNormalRule().setRebateAmount(100L);

        when(redisStoreClient.setnx(any(), any())).thenReturn(true);
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(anyLong(), anyLong(), anyInt(), any())).thenReturn(Collections.emptyList());

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(100L, response.getData().getRebateAmount());
    }

    /**
     * 测试Redis中存在重复订单的场景
     */
    @Test
    public void testCalculateDuplicateOrderInRedis() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("123");

        when(redisStoreClient.setnx(any(), any())).thenReturn(false);

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, null);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("用户存在重复", response.getData().getReason());
    }

    /**
     * 测试数据库中存在重复返利记录的场景
     */
    @Test
    public void testCalculateDuplicateRebateRecordInDB() throws Throwable {
        // arrange
        RebateAmountCalcRequest request = new RebateAmountCalcRequest();
        request.setOrderId("123");

        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        RebateSettleRuleDTO rule = new RebateSettleRuleDTO();
        rebateActivityRuleDTO.setRule(rule);
        RebateSettleNormalRuleDTO normalRule = new RebateSettleNormalRuleDTO();
        rebateActivityRuleDTO.getRule().setNormalRule(normalRule);
        rebateActivityRuleDTO.getRule().getNormalRule().setRebateAmount(100L);


        when(redisStoreClient.setnx(any(), any())).thenReturn(true);
        RbOrderVerifyRebateCommission rbOrderVerifyRebateCommission = new RbOrderVerifyRebateCommission();
        when(rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(anyLong(), anyLong(), anyInt(), any())).thenReturn(Lists.newArrayList(rbOrderVerifyRebateCommission));

        // act
        RemoteResponse<RebateAmountResult> response = handler.calculate(request, rebateActivityRuleDTO);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("用户存在重复", response.getData().getReason());
    }
}
