package com.sankuai.carnation.distribution.distributor.domain.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.distributor.domain.bo.ConsultantAccountBO;
import com.sankuai.carnation.distribution.distributor.dto.consultant.ConsultantAccountDTO;
import com.sankuai.carnation.distribution.distributor.repository.db.ConsultantAccount;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

public class ConsultantAccountBeanTransferTest {

    /**
     * 测试 po2bo 方法，当传入的 ConsultantAccount 对象为 null 时，应返回 null
     */
    @Test
    public void testPo2boWhenConsultantAccountIsNull() {
        // arrange
        ConsultantAccount consultantAccount = null;
        // act
        ConsultantAccountBO result = ConsultantAccountBeanTransfer.po2bo(consultantAccount);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 po2bo 方法，当传入的 ConsultantAccount 对象不为 null 时，应返回一个新的 ConsultantAccountBO 对象，其属性值与 ConsultantAccount 对象的属性值相同
     */
    @Test
    public void testPo2boWhenConsultantAccountIsNotNull() {
        // arrange
        ConsultantAccount consultantAccount = new ConsultantAccount();
        consultantAccount.setId(1L);
        consultantAccount.setConsultantAccountName("test");
        consultantAccount.setCustomerId(1L);
        consultantAccount.setOriginCustomerId(1L);
        consultantAccount.setAccountId(1L);
        consultantAccount.setShopId(1L);
        consultantAccount.setDistributorId(1L);
        consultantAccount.setMobile("**********");
        consultantAccount.setStatus(1);
        consultantAccount.setCreateTime(new Date());
        consultantAccount.setUpdateTime(new Date());
        // act
        ConsultantAccountBO result = ConsultantAccountBeanTransfer.po2bo(consultantAccount);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(consultantAccount.getId(), result.getId());
        Assert.assertEquals(consultantAccount.getConsultantAccountName(), result.getConsultantAccountName());
        Assert.assertEquals(consultantAccount.getCustomerId(), result.getCustomerId());
        Assert.assertEquals(consultantAccount.getOriginCustomerId(), result.getOriginCustomerId());
        Assert.assertEquals(consultantAccount.getAccountId(), result.getAccountId());
        Assert.assertEquals(consultantAccount.getShopId(), result.getShopId());
        Assert.assertEquals(consultantAccount.getDistributorId(), result.getDistributorId());
        Assert.assertEquals(consultantAccount.getMobile(), result.getMobile());
        Assert.assertEquals(consultantAccount.getStatus(), result.getStatus());
        Assert.assertEquals(consultantAccount.getCreateTime(), result.getCreateTime());
        Assert.assertEquals(consultantAccount.getUpdateTime(), result.getUpdateTime());
    }

    /**
     * Tests the bo2dto method with null input, expecting a null return.
     */
    @Test
    public void testBo2DtoNullInput() throws Throwable {
        // Arrange
        ConsultantAccountBO bo = null;
        // Act
        ConsultantAccountDTO dto = ConsultantAccountBeanTransfer.bo2dto(bo);
        // Assert
        Assert.assertNull(dto);
    }

    /**
     * Tests the bo2dto method with non-null input, expecting a new ConsultantAccountDTO object with properties matching those of the input object.
     */
    @Test
    public void testBo2DtoNonNullInput() throws Throwable {
        // Arrange
        ConsultantAccountBO bo = new ConsultantAccountBO();
        bo.setId(1L);
        bo.setConsultantAccountName("test");
        bo.setCustomerId(2L);
        bo.setOriginCustomerId(3L);
        bo.setAccountId(4L);
        bo.setShopId(5L);
        bo.setDistributorId(6L);
        bo.setMobile("**********");
        bo.setCreateTime(new Date());
        bo.setUpdateTime(new Date());
        // Act
        ConsultantAccountDTO dto = ConsultantAccountBeanTransfer.bo2dto(bo);
        // Assert
        Assert.assertNotNull(dto);
        // Fix for the compilation exception: Cast to Long to avoid ambiguity
        Assert.assertEquals(Long.valueOf(bo.getId()), Long.valueOf(dto.getId()));
        Assert.assertEquals(bo.getConsultantAccountName(), dto.getConsultantAccountName());
        Assert.assertEquals(bo.getCustomerId(), dto.getCustomerId());
        Assert.assertEquals(bo.getOriginCustomerId(), dto.getOriginCustomerId());
        Assert.assertEquals(bo.getAccountId(), dto.getAccountId());
        Assert.assertEquals(bo.getShopId(), dto.getShopId());
        Assert.assertEquals(bo.getDistributorId(), dto.getDistributorId());
        Assert.assertEquals(bo.getMobile(), dto.getMobile());
        Assert.assertEquals(bo.getCreateTime(), dto.getCreateTime());
        Assert.assertEquals(bo.getUpdateTime(), dto.getUpdateTime());
    }
}
