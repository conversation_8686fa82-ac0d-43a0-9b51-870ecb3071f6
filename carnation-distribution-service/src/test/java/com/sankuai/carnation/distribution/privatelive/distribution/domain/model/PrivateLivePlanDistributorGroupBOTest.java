package com.sankuai.carnation.distribution.privatelive.distribution.domain.model;

import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.technician.ddd.aggregate.AggregateFactory;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLivePlanDistributorGroupRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * 测试PrivateLivePlanDistributorGroupBO的create方法
 */
public class PrivateLivePlanDistributorGroupBOTest {

    @Mock
    private PrivateLivePlanDistributorGroupRepository privateLivePlanDistributorGroupRepository;

    @InjectMocks
    private PrivateLivePlanDistributorGroupBO privateLivePlanDistributorGroupBO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试create方法正常情况
     */
    @Test
    public void testCreateNormal() {
        // arrange
        PrivateLivePlanDistributorGroupContext context = new PrivateLivePlanDistributorGroupContext(privateLivePlanDistributorGroupRepository);
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        privateLivePlanDistributorGroupBO.setDistributorGroup(distributorGroupBO);
        when(privateLivePlanDistributorGroupRepository.save(any())).thenReturn(privateLivePlanDistributorGroupBO);

        // act
        PrivateLivePlanDistributorGroupBO result = privateLivePlanDistributorGroupBO.create(context);

        // assert
        assert result != null;
    }

    /**
     * 测试create方法时，context为null的异常情况
     */
    @Test(expected = NullPointerException.class)
    public void testCreateContextNull() {
        // arrange
        PrivateLivePlanDistributorGroupContext context = null;

        // act
        privateLivePlanDistributorGroupBO.create(context);

        // assert
        // 期望抛出NullPointerException
    }
}
