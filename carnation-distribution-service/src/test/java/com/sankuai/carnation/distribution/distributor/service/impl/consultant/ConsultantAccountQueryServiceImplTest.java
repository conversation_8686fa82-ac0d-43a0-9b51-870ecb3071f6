package com.sankuai.carnation.distribution.distributor.service.impl.consultant;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.domain.ConsultantAccountService;
import com.sankuai.carnation.distribution.distributor.domain.bo.ConsultantAccountBO;
import com.sankuai.carnation.distribution.distributor.dto.consultant.ConsultantAccountDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConsultantAccountQueryServiceImplTest {

    @InjectMocks
    private ConsultantAccountQueryServiceImpl consultantAccountQueryService;

    @Mock
    private ConsultantAccountService consultantAccountService;

    private List<Long> ids;

    @Before
    public void setUp() {
        ids = Arrays.asList(1L, 2L, 3L);
    }

    /**
     * 测试id小于等于0的情况
     */
    @Test
    public void testGetConsultantAccountByIdLessThanOrEqualToZero() throws Throwable {
        RemoteResponse<ConsultantAccountDTO> response = consultantAccountQueryService.getConsultantAccountById(0L);
        assertNotNull(response);
        assertNull(response.getData());
    }

    /**
     * 测试id大于0的情况
     */
    @Test
    public void testGetConsultantAccountByIdGreaterThanZero() throws Throwable {
        ConsultantAccountBO consultantAccountBO = new ConsultantAccountBO();
        consultantAccountBO.setId(1L);
        when(consultantAccountService.selectById(anyLong())).thenReturn(consultantAccountBO);
        RemoteResponse<ConsultantAccountDTO> response = consultantAccountQueryService.getConsultantAccountById(1L);
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals((Long) consultantAccountBO.getId(), response.getData().getId());
    }

    /**
     * 测试consultantAccountService.selectById(id)方法抛出异常的情况
     */
    @Test
    public void testGetConsultantAccountByIdException() throws Throwable {
        when(consultantAccountService.selectById(anyLong())).thenThrow(new RuntimeException());
        RemoteResponse<ConsultantAccountDTO> response = consultantAccountQueryService.getConsultantAccountById(1L);
        assertNotNull(response);
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testGetConsultantAccountByIdsEmpty() {
        // arrange
        ids = Collections.emptyList();
        // act
        RemoteResponse<List<ConsultantAccountDTO>> response = consultantAccountQueryService.getConsultantAccountByIds(ids);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNull(response.getData());
    }

    @Test
    public void testGetConsultantAccountByIdsExceedMaxPageSize() {
        // arrange
        ids = Arrays.asList(new Long[1001]);
        // act
        RemoteResponse<List<ConsultantAccountDTO>> response = consultantAccountQueryService.getConsultantAccountByIds(ids);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("单次查询数量不能超过1000", response.getMsg());
    }

    @Test
    public void testGetConsultantAccountByIdsInvalidPageNumOrPageSize() {
        // arrange
        // act
        RemoteResponse<List<ConsultantAccountDTO>> response = consultantAccountQueryService.getConsultantAccountByIds(ids);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    @Test
    public void testGetConsultantAccountByIdsContainsNullOrNegative() {
        // arrange
        ids = Arrays.asList(1L, null, -1L);
        // act
        RemoteResponse<List<ConsultantAccountDTO>> response = consultantAccountQueryService.getConsultantAccountByIds(ids);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }

    @Test
    public void testGetConsultantAccountByIdsException() {
        // arrange
        when(consultantAccountService.selectByIds(anyList())).thenThrow(new RuntimeException());
        // act
        RemoteResponse<List<ConsultantAccountDTO>> response = consultantAccountQueryService.getConsultantAccountByIds(ids);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testGetConsultantAccountByIdsNormal() {
        // arrange
        List<ConsultantAccountBO> consultantAccountBOS = Arrays.asList(new ConsultantAccountBO(), new ConsultantAccountBO());
        when(consultantAccountService.selectByIds(anyList())).thenReturn(consultantAccountBOS);
        // act
        RemoteResponse<List<ConsultantAccountDTO>> response = consultantAccountQueryService.getConsultantAccountByIds(ids);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(consultantAccountBOS.size(), response.getData().size());
    }
}
