package com.sankuai.carnation.distribution.wallet.fundamental.domain.flow;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.wallet.fundamental.acl.salary.SalarySettleAclService;
import com.sankuai.carnation.distribution.wallet.fundamental.bean.generator.*;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletAccountBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.account.bo.WalletActivityAccountAmountChangeResultBO;
import com.sankuai.carnation.distribution.wallet.fundamental.domain.flow.bo.WalletOperateFlowBO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletDataItemStatusEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletOperateTypeEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.exceptions.WalletFlowException;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletAccountAttrDataService;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletDataItemDataService;
import com.sankuai.carnation.distribution.wallet.fundamental.repository.service.WalletOperateFlowDataService;
import com.sankuai.carnation.distribution.wallet.payer.factory.AcquirePayerFactory;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/8/5
 **/
@RunWith(MockitoJUnitRunner.class)
public class WalletFlowDomainServiceTest {

    @InjectMocks
    private WalletFlowDomainService flowDomainService;

    @Mock
    private WalletDataItemDataService itemDataService;

    @Mock
    private WalletOperateFlowDataService flowDataService;

    @Mock
    private WalletFlowTransactionalService transactionalService;

    @Mock
    private WalletAccountAttrDataService accountAttrDataService;

    @Mock
    private SalarySettleAclService salarySettleAclService;

    private static final MockedStatic<AcquirePayerFactory> mockAcquirePayerFactory = Mockito.mockStatic(AcquirePayerFactory.class);

    @BeforeClass
    public static void mockStatic() {
        ArgumentMatcher<WalletAccountBO> setPayerAccount = walletAccount -> walletAccount.getWalletId() == WalletTestConstants.mockWalletAccountId;
        ArgumentMatcher<WalletAccountBO> notSetPayerAccount = walletAccount -> walletAccount.getWalletId() != WalletTestConstants.mockWalletAccountId;
        mockAcquirePayerFactory.when(() -> AcquirePayerFactory.getPayerInfo(Mockito.argThat(setPayerAccount))).thenReturn(WalletPayerInfoTestBeanGenerator.generatePayerInfo());
        mockAcquirePayerFactory.when(() -> AcquirePayerFactory.getPayerInfo(Mockito.argThat(notSetPayerAccount))).thenReturn(null);
    }

    @AfterClass
    public static void closeMockStatic() {
        mockAcquirePayerFactory.close();;
    }

    // withdrawActivityAccountWithPrivacyWay 方法测试
    /**
     * 测试对私提现，支付账号未设置
     */
    @Test
    public void testWithdrawActivityAccountWithPrivacyWay_NoPayer() {
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(3L, 10L), WalletTestConstants.mockOperator);
        assertWalletActivityAccountAmountChangeResultFail(result, "支付账号未配置");
    }

    /**
     * 测试对私提现，无可提现金额
     */
    @Test
    public void testWithdrawActivityAccountWithPrivacyWay_NoAmount() {
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(0L), WalletTestConstants.mockOperator);
        assertWalletActivityAccountAmountChangeResultFail(result, "无可提现金额");
    }

    /**
     * 测试对私提现，打到最大提现额
     */
    @Test
    public void testWithdrawActivityAccountWithPrivacyWay_NoRemainAmount() {
        mockSalarySettleRemainAmount(0L);
        mockItemDataService(10L, 1, 0L);
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L), WalletTestConstants.mockOperator);
        assertWalletActivityAccountAmountChangeResultFail(result, "每月最大提现金额2.6万，本月提现已经到达额度，剩余金额可下月提现");
    }

    /**
     * 测试对私提现，提现余额不足
     */
    @Test
    public void testWithdrawActivityAccountWithPrivacyWay_NotEnoughRemainAmount() {
        mockSalarySettleRemainAmount(10L);
        mockItemDataService(10L, 2, 0L);
        mockTransactionWithdrawStartFlow();
        mockTransactionWithdrawProcessFlow();
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L), WalletTestConstants.mockOperator);
        assertWalletActivityAccountAmountChangeResult(result, 10L);
    }

    /**
     * 测试对私提现，开始流程异常
     */
    @Test(expected = WalletFlowException.class)
    public void testWithdrawActivityAccountWithPrivacyWay_StartFlowException() {
        mockSalarySettleRemainAmount(10L);
        mockItemDataService(10L, 1, 0L);
        mockTransactionWithdrawStartException();
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L), WalletTestConstants.mockOperator);
    }

    /**
     * 测试对私提现，执行流程异常
     */
    @Test
    public void testWithdrawActivityAccountWithPrivacyWay_WithdrawException() {
        mockSalarySettleRemainAmount(10L);
        mockItemDataService(10L, 1, 0L);
        mockTransactionWithdrawStartFlow();
        mockTransactionWithdrawProcessFlowException();
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L), WalletTestConstants.mockOperator);
        assertWalletActivityAccountAmountChangeResultFail(result, "执行异常");
    }

    /**
     * 测试对私提现，成功提现
     */
    @Test
    public void testWithdrawActivityAccountWithPrivacyWay_Success() {
        mockSalarySettleRemainAmount(10L);
        mockItemDataService(10L, 1, 0L);
        mockTransactionWithdrawStartFlow();
        mockTransactionWithdrawProcessFlow();
        WalletActivityAccountAmountChangeResultBO result = flowDomainService.withdrawActivityAccountWithPrivacyWay(WalletAccountTestBeanGenerator.generateWalletActivityAccountBO(10L), WalletTestConstants.mockOperator);
        assertWalletActivityAccountAmountChangeResult(result, 10L);
    }

    /**
     * 测试addOperateFlow方法，当操作流程正常且数据项不存在时
     */
    @Test
    public void testAddOperateFlowWithNewItemSuccess() throws Throwable {
        // arrange
        WalletOperateFlowBO operateFlowBO = WalletOperateFlowTestBeanGenerator.buildWalletOperateFlowBO();

        when(itemDataService.getItemByActivityAccountAndBizId(any(Long.class), any(String.class), any(Boolean.class))).thenReturn(null);
        when(transactionalService.initFlowForAdd(any(WalletOperateFlowBO.class))).thenReturn(1L);
        when(flowDataService.processing(any(Long.class), any())).thenReturn(true);
        WalletActivityAccountAmountChangeResultBO expectedBO = new WalletActivityAccountAmountChangeResultBO(true, 1L, 0L, 100L, 1, 100L, null);
        when(transactionalService.collect(any(WalletOperateFlowBO.class))).thenReturn(expectedBO);

        // act
        WalletActivityAccountAmountChangeResultBO resultBO = flowDomainService.addOperateFlow(operateFlowBO);

        // assert
        assertTrue(resultBO.isSuccess());
        assertEquals(100L, resultBO.getChangeAmount());
    }

    /**
     * 测试addOperateFlow方法，当操作流程正常但数据项已存在且状态为COLLECTED时
     */
    @Test
    public void testAddOperateFlowWithExistingCollectedItemSuccess() throws Throwable {
        // arrange
        WalletOperateFlowBO operateFlowBO = WalletOperateFlowTestBeanGenerator.buildWalletOperateFlowBO();

        WalletDataItem existingItem = WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION);
        when(itemDataService.getItemByActivityAccountAndBizId(any(Long.class), any(String.class), any(Boolean.class))).thenReturn(existingItem);
        List<WalletOperateFlow> itemFlowList = new ArrayList<>();
        WalletOperateFlow walletOperateFlow = new WalletOperateFlow();
        walletOperateFlow.setAmount(100L);
        walletOperateFlow.setOperateType(1);
        walletOperateFlow.setFormerAmount(10L);
        walletOperateFlow.setCurrentAmount(10L);
        walletOperateFlow.setWalletActivityAccountId(1L);
        itemFlowList.add(walletOperateFlow);
        when(flowDataService.queryCollectingFlowByItemId(any(Long.class))).thenReturn(itemFlowList);

        // act
        WalletActivityAccountAmountChangeResultBO resultBO = flowDomainService.addOperateFlow(operateFlowBO);

        // assert
        assertTrue(resultBO.isSuccess());
        assertEquals(100L, resultBO.getChangeAmount());
    }

    /**
     * 测试addOperateFlow方法，当操作流程正常但数据项已存在且状态为INITIAL时
     */
    @Test
    public void testAddOperateFlowWithExistingInitialItemSuccess() throws Throwable {
        // arrange
        WalletOperateFlowBO operateFlowBO = WalletOperateFlowTestBeanGenerator.buildWalletOperateFlowBO();

        WalletDataItem existingItem = WalletDataItemTestBeanGenerator.generateDataItemPo(1L, 10L, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION);
        existingItem.setStatus(WalletDataItemStatusEnum.INITIAL.getCode());
        when(itemDataService.getItemByActivityAccountAndBizId(any(Long.class), any(String.class), any(Boolean.class))).thenReturn(existingItem);
        when(transactionalService.initFlowWithItemForAdd(any(WalletOperateFlowBO.class))).thenReturn(1L);
        when(flowDataService.processing(any(Long.class), any())).thenReturn(true);
        WalletActivityAccountAmountChangeResultBO expectedBO = new WalletActivityAccountAmountChangeResultBO(true, 1L, 0L, 100L, 1, 100L, null);
        when(transactionalService.collect(any(WalletOperateFlowBO.class))).thenReturn(expectedBO);

        // act
        WalletActivityAccountAmountChangeResultBO resultBO = flowDomainService.addOperateFlow(operateFlowBO);

        // assert
        assertTrue(resultBO.isSuccess());
        assertEquals(100L, resultBO.getChangeAmount());
    }

    /**
     * 测试addOperateFlow方法，当操作流程抛出WalletFlowException异常时
     */
    @Test
    public void testAddOperateFlowThrowsWalletFlowException() throws Throwable {
        // arrange
        WalletOperateFlowBO operateFlowBO = new WalletOperateFlowBO();
        operateFlowBO.setAmount(-100L); // 设置一个无效的金额，以触发异常

        // act
        WalletActivityAccountAmountChangeResultBO resultBO  = flowDomainService.addOperateFlow(operateFlowBO);

        // assert
        assertFalse(resultBO.isSuccess());
        assertEquals("操作数据项不能为空", resultBO.getFailReason());
    }



    private void assertWalletActivityAccountAmountChangeResult(WalletActivityAccountAmountChangeResultBO result, long amount) {
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(amount, result.getChangeAmount());
    }

    private void assertWalletActivityAccountAmountChangeResultFail(WalletActivityAccountAmountChangeResultBO result, String message) {
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(message, result.getFailReason());
    }

    private void mockSalarySettleRemainAmount(long remainAmount) {
        when(salarySettleAclService.getRemainSettleAmount(anyString(), anyLong())).thenReturn(remainAmount);
    }

    private void mockTransactionWithdrawStartException() {
        when(transactionalService.startWithdrawFlow(any(), anyList(), anyString(), anyLong())).thenThrow(new WalletFlowException("创建异常"));
    }

    private void mockTransactionWithdrawStartFlow() {
        when(transactionalService.startWithdrawFlow(any(), anyList(), anyString(), anyLong())).thenReturn(1L);
    }

    private void mockTransactionWithdrawProcessFlowException() {
        when(transactionalService.withdrawPrivacyMoney(anyLong(), anyString(), any(), anyList(), anyLong())).thenThrow(new WalletFlowException("执行异常"));
    }

    private void mockTransactionWithdrawProcessFlow() {
        when(transactionalService.withdrawPrivacyMoney(anyLong(), anyString(), any(), anyList(), anyLong())).thenReturn(WalletAmountChangeResultTestBeanGenerator.generateChangeResult(true, 10L, 0L, WalletOperateTypeEnum.SUBTRACT, 10L, null));
    }

    private void mockItemDataService(long withdrawItemAmount, int withdrawItemCount, long rollbackAmount) {
        List<WalletDataItem> poList = Lists.newArrayList();
        if (withdrawItemAmount > 0L && withdrawItemCount > 0) {
            for (int i = 0; i < withdrawItemCount; i++) {
                poList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(i + 1, withdrawItemAmount, WalletDataItemStatusEnum.COLLECTED, WalletOperateTypeEnum.ADDITION));
            }
        }
        if (rollbackAmount > 0L) {
            poList.add(WalletDataItemTestBeanGenerator.generateDataItemPo(withdrawItemCount + 2L, rollbackAmount, WalletDataItemStatusEnum.HAS_DRAW, WalletOperateTypeEnum.ADDITION));
        }
        when(itemDataService.getItemByWalletAccount(anyLong(), anyList())).thenReturn(poList);
    }
}
