package com.sankuai.carnation.distribution.promocode.task.settletask;

import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.carnation.distribution.promocode.task.dto.PromoCodeShopPromoteTradeDataDTO;
import com.sankuai.carnation.distribution.promocode.task.model.PrivilegeTaskConfigBO;
import com.sankuai.carnation.distribution.promocode.task.repository.service.PromoCodeShopPromoteTradeDataDataService;
import com.sankuai.swan.udqs.api.PageInfo;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import com.sankuai.swan.udqs.api.SwanQuerier;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PromoCodeSettlePrivilegeTasksJobTest {

    @InjectMocks
    private PromoCodeSettlePrivilegeTasksJob promoCodeSettlePrivilegeTasksJob;

    @Mock
    private PromoCodeShopPromoteTradeDataDataService tradeDataDataService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getSettlePrivilegeSwanResults方法，当SwanQuerier返回null时
     */
    @Test
    public void testGetSettlePrivilegeSwanResultsWhenSwanQuerierReturnsNull() {
        // arrange
        PrivilegeTaskConfigBO privilegeTaskConfigBO = new PrivilegeTaskConfigBO();
        privilegeTaskConfigBO.setId(1L);
        MockedStatic<SwanQuerier> mockedStatic = Mockito.mockStatic(SwanQuerier.class);
        mockedStatic.when(() -> SwanQuerier.swanQuery(anyInt(), anyString(), any(SwanParam.class))).thenReturn(null);

        List<PromoCodeShopPromoteTradeDataDTO> list = Lists.newArrayList(new PromoCodeShopPromoteTradeDataDTO());
        when(tradeDataDataService.queryShopTradeDataByPage(any(), anyInt(), anyInt(),any())).thenReturn(list); // 假设PageResult是方法的返回类型


        // act
        List<Map<String, Object>> result = promoCodeSettlePrivilegeTasksJob.getSettlePrivilegeSwanResults(privilegeTaskConfigBO);

        // assert
        assertTrue(result.isEmpty());
        mockedStatic.close();

    }

    /**
     * 测试getSettlePrivilegeSwanResults方法，当SwanQuerier返回成功但结果集为空时
     */
    @Test
    public void testGetSettlePrivilegeSwanResultsWhenSwanQuerierReturnsSuccessButEmptyResultSet() {
        // arrange
        PrivilegeTaskConfigBO privilegeTaskConfigBO = new PrivilegeTaskConfigBO();
        privilegeTaskConfigBO.setId(1L);
        Result<QueryData> mockResult = mock(Result.class);
        QueryData mockQueryData = mock(QueryData.class);
        when(mockResult.isIfSuccess()).thenReturn(true);
        when(mockResult.getData()).thenReturn(mockQueryData);
        when(mockQueryData.getResultSet()).thenReturn(null);
        MockedStatic<SwanQuerier> mockedStatic = Mockito.mockStatic(SwanQuerier.class);
        mockedStatic.when(() -> SwanQuerier.swanQuery(anyInt(), anyString(), any(SwanParam.class))).thenReturn(mockResult);

        List<PromoCodeShopPromoteTradeDataDTO> list = Lists.newArrayList(new PromoCodeShopPromoteTradeDataDTO());
        when(tradeDataDataService.queryShopTradeDataByPage(any(), anyInt(), anyInt(),any())).thenReturn(list); // 假设PageResult是方法的返回类型


        // act
        List<Map<String, Object>> result = promoCodeSettlePrivilegeTasksJob.getSettlePrivilegeSwanResults(privilegeTaskConfigBO);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        mockedStatic.close();
    }

    /**
     * 测试getSettlePrivilegeSwanResults方法，当SwanQuerier返回成功且结果集非空时
     */
    @Test
    public void testGetSettlePrivilegeSwanResultsWhenSwanQuerierReturnsSuccessAndNonEmptyResultSet() {
        // arrange
        PrivilegeTaskConfigBO privilegeTaskConfigBO = new PrivilegeTaskConfigBO();
        privilegeTaskConfigBO.setId(1L);

        Result<QueryData> mockResult = mock(Result.class);
        QueryData mockQueryData = mock(QueryData.class);
        List<Map<String, Object>> mockResultSet = Lists.newArrayList(Maps.newHashMap());
        when(mockResult.isIfSuccess()).thenReturn(true);
        when(mockResult.getData()).thenReturn(mockQueryData);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalPage(1);
        when(mockResult.getData().getPageInfo()).thenReturn(pageInfo);
        when(mockQueryData.getResultSet()).thenReturn(mockResultSet);
        MockedStatic<SwanQuerier> mockedStatic = Mockito.mockStatic(SwanQuerier.class);
        mockedStatic.when(() -> SwanQuerier.swanQuery(anyInt(), anyString(), any(SwanParam.class))).thenReturn(mockResult);

        List<PromoCodeShopPromoteTradeDataDTO> list = Lists.newArrayList(new PromoCodeShopPromoteTradeDataDTO());
        when(tradeDataDataService.queryShopTradeDataByPage(any(), anyInt(), anyInt(),any())).thenReturn(list); // 假设PageResult是方法的返回类型


        // act
        List<Map<String, Object>> result = promoCodeSettlePrivilegeTasksJob.getSettlePrivilegeSwanResults(privilegeTaskConfigBO);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        mockedStatic.close();
    }

}
