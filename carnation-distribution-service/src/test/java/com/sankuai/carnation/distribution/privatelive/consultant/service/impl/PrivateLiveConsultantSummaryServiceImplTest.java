package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.dianping.tpfun.publish.api.sku.entity.ProductEntity;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.product.ProductAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.wechat.WechatUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantApplicantDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantRankinglistDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.*;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.SortFieldEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.SortingRulesEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.gateway.PrivateLiveRankingForCGatewayService;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.*;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.user.dto.LiveUserHandleStatisticsDTO;
import com.sankuai.dz.srcm.user.dto.PrivateSphereCustomerCapitalHandleSizeOverview;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.miniprogram.WechatUserInfoDTO;
import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveConsultantSummaryService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;

import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveConsultantSummaryServiceImplTest {

    @InjectMocks
    private PrivateLiveConsultantSummaryServiceImpl privateLiveConsultantSummaryService;

    @Mock
    private PrivateLiveConsultantSummaryRepository privateLiveConsultantSummaryRepository;

    @Mock
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Mock
    private PrivateSphereUserAclService privateSphereUserAclService;
    @Mock
    private PrivateLiveOrderIntentionResultRepository privateLiveOrderIntentionResultRepository;
    @Mock
    private PrivateLiveUserIntentionResultRepository privateLiveUserIntentionResultRepository;
    @Mock
    private DzPrivateLiveConsultantSummaryService dzPrivateLiveConsultantSummaryService;
    @Mock
    private ProductAclService productAclService;
    @Mock
    private PrivateLiveRankingForCGatewayService privateLiveRankingForCGatewayService;
    @Mock
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;
    @Mock
    private WechatUserAclService wechatUserAclService;

    private RankConditionDTO condition;

    private List<PrivateLiveConsultantUserStatistics> statisticsPO;

    private List<PrivateLiveConsultantTask> taskList;

    private PageRemoteResponse<PrivateSphereCustomerCapitalHandleSizeOverview> response;


    @Before
    public void setUp() {
        condition = new RankConditionDTO();
        condition.setLiveId("testLiveId");
        condition.setSortField(SortFieldEnum.GMV_AMT.getCode());
        condition.setSortOrder(SortingRulesEnum.DESC.getCode());
        condition.setNeedFollowData(true);
        condition.setLimit(5);
        statisticsPO = Arrays.asList(new PrivateLiveConsultantUserStatistics(), new PrivateLiveConsultantUserStatistics());
        taskList = Arrays.asList(new PrivateLiveConsultantTask(), new PrivateLiveConsultantTask());
        taskList.get(0).setId(1L);
        taskList.get(1).setId(2L);
        response = new PageRemoteResponse<>();
        // Ensure unique consultantTaskId to avoid duplicate key exception
        PrivateSphereCustomerCapitalHandleSizeOverview overview1 = new PrivateSphereCustomerCapitalHandleSizeOverview();
        overview1.setConsultantTaskId(1L);
        PrivateSphereCustomerCapitalHandleSizeOverview overview2 = new PrivateSphereCustomerCapitalHandleSizeOverview();
        overview2.setConsultantTaskId(2L);
        response.setData(Arrays.asList(overview1, overview2));
    }

    @Test
    public void testQueryRankByCondition_NullCondition() throws Throwable {
        RemoteResponse<RankDTO> result = privateLiveConsultantSummaryService.queryRankByCondition(null);
        assertEquals("直播id为空", result.getMsg());
    }

    @Test
    public void testQueryRankByCondition_EmptyLiveId() throws Throwable {
        condition.setLiveId("");
        RemoteResponse<RankDTO> result = privateLiveConsultantSummaryService.queryRankByCondition(condition);
        assertEquals("直播id为空", result.getMsg());
    }

    @Test
    public void testQueryRankByCondition_EmptyStatisticsPO() throws Throwable {
        when(privateLiveConsultantSummaryRepository.forceGetRankSummary(anyString(), anyString(), anyString())).thenReturn(Collections.emptyList());
        RemoteResponse<RankDTO> result = privateLiveConsultantSummaryService.queryRankByCondition(condition);
        assertEquals("统计数据为空", result.getMsg());
    }

    @Test
    public void testQueryRankByCondition_Success() throws Throwable {
        when(privateLiveConsultantSummaryRepository.forceGetRankSummary(anyString(), anyString(), anyString())).thenReturn(statisticsPO);
        when(privateSphereUserAclService.queryConsultantFollowDataByLiveId(anyString())).thenReturn(response);
        RemoteResponse<RankDTO> result = privateLiveConsultantSummaryService.queryRankByCondition(condition);
        assertEquals("success", result.getMsg());
    }

    @Test
    public void testQueryBossDataByLiveIdWithNullLiveId() {
        String liveId = null;
        RemoteResponse<BossToolDataDTO> result = privateLiveConsultantSummaryService.queryBossDataByLiveId(liveId);
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("参数错误", result.getMsg());
    }

    /**
     * 测试queryBossDataByLiveId方法，当liveId有效且数据正常返回时
     */
    @Test
    public void testQueryBossDataByLiveIdWithValidLiveId() {
        String liveId = "1000013070";
        BossToolDataDTO bossToolDataDTO = new BossToolDataDTO();
        bossToolDataDTO.setLiveId(liveId);
        when(privateLiveConsultantSummaryRepository.queryBossToolDataByLiveId(liveId)).thenReturn(bossToolDataDTO);
        when(privateLiveUserIntentionResultRepository.countUserTypeByLiveId(liveId, null)).thenReturn(10L);
        when(privateLiveUserIntentionResultRepository.countUserTypeByLiveId(liveId, 1)).thenReturn(5L);
        when(privateLiveUserIntentionResultRepository.countUserTypeByLiveId(liveId, 2)).thenReturn(5L);
        RemoteResponse<BossToolDataDTO> result = privateLiveConsultantSummaryService.queryBossDataByLiveId(liveId);
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(liveId, result.getData().getLiveId());
        assertEquals(Integer.valueOf(10), result.getData().getAllUserCnt());
        assertEquals(Integer.valueOf(5), result.getData().getSeedUserCnt());
        assertEquals(Integer.valueOf(5), result.getData().getFissionUserCnt());
    }

    @Test
    public void testQueryRankByConditionOld_Normal() {
        // arrange
        RankConditionDTO condition = new RankConditionDTO();
        condition.setLiveId("liveId");
        condition.setNeedFollowData(true);
        condition.setLimit(1);

        List<PrivateLiveConsultantUserStatistics> statisticsList = new ArrayList<>();
        PrivateLiveConsultantUserStatistics statistics = new PrivateLiveConsultantUserStatistics();
        statistics.setConsultantTaskId(1L);
        statisticsList.add(statistics);

        when(privateLiveConsultantSummaryRepository.forceGetRankSummary(any(), any(), any())).thenReturn(statisticsList);

        List<PrivateLiveConsultantTask> taskList = new ArrayList<>();
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setId(1L);
        task.setActualName("actualName");
        taskList.add(task);

        when(privateLiveConsultantTaskRepository.loadByIds(any())).thenReturn(taskList);

        PageRemoteResponse<PrivateSphereCustomerCapitalHandleSizeOverview> pageResponse = new PageRemoteResponse<>();
        List<PrivateSphereCustomerCapitalHandleSizeOverview> overviewList = new ArrayList<>();
        PrivateSphereCustomerCapitalHandleSizeOverview overview = new PrivateSphereCustomerCapitalHandleSizeOverview();
        overview.setConsultantTaskId(1L);
        overview.setHaveHandleSize(10L);
        overview.setTotalCount(20L);
        overviewList.add(overview);
        pageResponse.setData(overviewList);

        // act
        RemoteResponse<RankDTO> response = privateLiveConsultantSummaryService.queryRankByConditionOld(condition);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
    }


    @Test
    public void testQueryRankByConditionNew_ValidCondition() {
        // arrange
        RankConditionDTO condition = new RankConditionDTO();
        RankDTO expectedRankDTO = new RankDTO();
        RemoteResponse<RankDTO> success = RemoteResponse.success(expectedRankDTO);
        when(dzPrivateLiveConsultantSummaryService.queryRankByCondition(condition)).thenReturn(success);

        // act
        RemoteResponse<RankDTO> actualResponse = privateLiveConsultantSummaryService.queryRankByConditionNew(condition);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedRankDTO, actualResponse.getData());
    }

    @Test
    public void testQuerySummaryByConsultantTaskIdNew_ValidId() {
        // arrange
        Long consultantTaskId = 1L;
        RemoteResponse<ConsultantTaskSummaryDTO> success = RemoteResponse.success(new ConsultantTaskSummaryDTO());
        when(dzPrivateLiveConsultantSummaryService.querySummaryByConsultantTaskId(consultantTaskId)).thenReturn(success);
        // act
        RemoteResponse<ConsultantTaskSummaryDTO> actualResponse = privateLiveConsultantSummaryService.querySummaryByConsultantTaskIdNew(consultantTaskId);
        verify(dzPrivateLiveConsultantSummaryService, times(1)).querySummaryByConsultantTaskId(consultantTaskId);
    }

    @Test
    public void testQuerySummaryByConsultantLiveIdOld_StatisticsAndOverviewNotNull() {
        // arrange
        String liveId = "testLiveId";
        PrivateLiveConsultantUserStatistics statistics = new PrivateLiveConsultantUserStatistics();
        statistics.setLiveId(liveId);
        when(privateLiveConsultantSummaryRepository.forceGetSummaryByLiveId(liveId)).thenReturn(statistics);

        LiveUserHandleStatisticsDTO overview = new LiveUserHandleStatisticsDTO();
        overview.setHasHandleSize(10L);
        overview.setNotHandleSize(5L);
        when(privateSphereUserAclService.queryFollowDataByLiveId(liveId)).thenReturn(overview);

        // act
        RemoteResponse<ConsultantTaskSummaryDTO> response = privateLiveConsultantSummaryService.querySummaryByConsultantLiveIdOld(liveId);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        ConsultantTaskSummaryDTO summaryDTO = response.getData();
        assertNotNull(summaryDTO);
        assertEquals(Integer.valueOf(10), summaryDTO.getFollowUserCount());
        assertEquals(Integer.valueOf(15), summaryDTO.getAllUserCount());
        assertEquals(Integer.valueOf(66), summaryDTO.getFollowPercent()); // 因为整数除法的结果会向下取整
    }

    @Test
    public void testQuerySummaryByConsultantLiveIdNew_ValidLiveId_ReturnsSuccess() {
        // arrange
        String liveId = "validLiveId";
        ConsultantTaskSummaryDTO expectedDto = new ConsultantTaskSummaryDTO();
        RemoteResponse<ConsultantTaskSummaryDTO> success = RemoteResponse.success(expectedDto);
        when(dzPrivateLiveConsultantSummaryService.querySummaryByConsultantLiveId(liveId)).thenReturn(success);

        // act
        RemoteResponse<ConsultantTaskSummaryDTO> actualResponse = privateLiveConsultantSummaryService.querySummaryByConsultantLiveIdNew(liveId);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedDto, actualResponse.getData());
        verify(dzPrivateLiveConsultantSummaryService, times(1)).querySummaryByConsultantLiveId(liveId);
    }

    @Test
    public void testQuerySummaryByConsultantTaskIdsOld_WithValidResult() {
        when(privateLiveConsultantSummaryRepository.batchGetSummaryByConsultantTasks(anyList())).thenReturn(statisticsPO);
        List<Long> consultantTaskIdList = Arrays.asList(1L, 2L);
        RemoteResponse<List<ConsultantTaskSummaryDTO>> response = privateLiveConsultantSummaryService.querySummaryByConsultantTaskIdsOld(consultantTaskIdList);

        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertNotNull(response.getData());
    }

    @Test
    public void testQuerySummaryByConsultantTaskIdsNew_NonEmptyList() {
        // arrange
        List<Long> consultantTaskIdList = Arrays.asList(1L, 2L);
        RemoteResponse<List<ConsultantTaskSummaryDTO>> success = RemoteResponse.success(Collections.emptyList());
        when(dzPrivateLiveConsultantSummaryService.querySummaryByConsultantTaskIds(consultantTaskIdList)).thenReturn(success);
        // act
        RemoteResponse<List<ConsultantTaskSummaryDTO>> result = privateLiveConsultantSummaryService.querySummaryByConsultantTaskIdsNew(consultantTaskIdList);
        // assert
        assertNotNull(result);
        verify(dzPrivateLiveConsultantSummaryService, times(1)).querySummaryByConsultantTaskIds(consultantTaskIdList);
    }

    @Test
    public void testQuerySummaryByConsultantTaskIdAndUserIdNormal() {
        // arrange
        Long consultantTaskId = 1L;
        Long userId = 1L;
        PrivateLiveConsultantUserStatistics userStatistics = new PrivateLiveConsultantUserStatistics();
        userStatistics.setMtUserId(userId);
        userStatistics.setConsultantTaskId(consultantTaskId);
        when(privateLiveConsultantSummaryRepository.forceGetByUserSummary(anyLong(), anyLong())).thenReturn(userStatistics);

        // act
        RemoteResponse<UserTaskSummaryDTO> response = privateLiveConsultantSummaryService.querySummaryByConsultantTaskIdAndUserIdOld(consultantTaskId, userId);

        // assert
        assert response.getData().getUserIds().contains(userId);
    }

    @Test
    public void testQuerySummaryByConsultantTaskIdAndUserIdNew_ReturnsSuccess() {
        // arrange
        Long consultantTaskId = 1L;
        Long userId = 1L;
        UserTaskSummaryDTO expectedDto = new UserTaskSummaryDTO();
        RemoteResponse<UserTaskSummaryDTO> success = RemoteResponse.success(expectedDto);
        when(dzPrivateLiveConsultantSummaryService.querySummaryByConsultantTaskIdAndUserId(consultantTaskId, userId)).thenReturn(success);

        // act
        RemoteResponse<UserTaskSummaryDTO> actualResponse = privateLiveConsultantSummaryService.querySummaryByConsultantTaskIdAndUserIdNew(consultantTaskId, userId);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedDto, actualResponse.getData());
    }

    @Test
    public void testQuerySummaryByLiveIdAndUserIdListOld_Normal() {
        // arrange
        String liveId = "live123";
        List<Long> userIdList = Arrays.asList(1L, 2L);
        List<PrivateLiveConsultantUserStatistics> userStatisticsDatas = new ArrayList<>();
        PrivateLiveConsultantUserStatistics stats = new PrivateLiveConsultantUserStatistics();
        stats.setMtUserId(1L);
        stats.setGmvAmt(1000L);
        stats.setVerifyGmvAmt(500L);
        userStatisticsDatas.add(stats);
        when(privateLiveConsultantSummaryRepository.forceBatchGetUserSummaryByLiveId(anyList(), any())).thenReturn(userStatisticsDatas);

        // act
        RemoteResponse<List<UserTaskSummaryDTO>> response = privateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdListOld(liveId, userIdList);

        // assert
        assertTrue("应该返回成功", response.isSuccess());
        assertNotNull("结果不应该为空", response.getData());
        assertEquals("结果数量应该为1", 1, response.getData().size());
        UserTaskSummaryDTO result = response.getData().get(0);
        assertEquals("用户ID应该匹配", Lists.newArrayList(1L), result.getUserIds());
        assertEquals("GMV应该匹配", Long.valueOf(1000L), result.getDateSummaryDTO().getGmvAmt());
        assertEquals("核销GMV应该匹配", Long.valueOf(500L), result.getDateSummaryDTO().getVerifyGmvAmt());
    }
    @Test
    public void testQuerySummaryByLiveIdAndUserIdListNew_ValidInput() throws Throwable {
        // arrange
        String liveId = "live123";
        List<Long> userIdList = Arrays.asList(1L, 2L);
        RemoteResponse<List<UserTaskSummaryDTO>> success = RemoteResponse.success(Collections.emptyList());
        when(dzPrivateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdList(liveId, userIdList)).thenReturn(success);

        // act
        RemoteResponse<List<UserTaskSummaryDTO>> actualResponse = privateLiveConsultantSummaryService.querySummaryByLiveIdAndUserIdListNew(liveId, userIdList);

        // assert
        assertEquals(success, actualResponse);
        verify(dzPrivateLiveConsultantSummaryService, times(1)).querySummaryByLiveIdAndUserIdList(liveId, userIdList);
    }

    @Test
    public void testInsertOrUpdateSummaryDataOld_MultipleConsultants() {
        // arrange
        Long consultantTaskId = null; // 表示多个咨询师
        Long userId = 1L;
        String liveId = "liveId";
        PrivateLiveConsultantUserStatistics statistics1 = new PrivateLiveConsultantUserStatistics();
        statistics1.setConsultantTaskId(1L);
        PrivateLiveConsultantUserStatistics statistics2 = new PrivateLiveConsultantUserStatistics();
        statistics2.setConsultantTaskId(2L);
        when(privateLiveOrderIntentionResultRepository.queryConsultantUserStatistics(consultantTaskId, userId, liveId)).thenReturn(Arrays.asList(statistics1, statistics2));
        PrivateLiveConsultantTask task1 = new PrivateLiveConsultantTask();
        task1.setId(1L);
        task1.setAvatarUrl("avatarUrl1");
        task1.setShareName("shareName1");
        PrivateLiveConsultantTask task2 = new PrivateLiveConsultantTask();
        task2.setId(2L);
        task2.setAvatarUrl("avatarUrl2");
        task2.setShareName("shareName2");
        when(privateLiveConsultantTaskRepository.loadByIds(Arrays.asList(1L, 2L))).thenReturn(Arrays.asList(task1, task2));
        when(privateLiveConsultantSummaryRepository.batchInsertAndUpdate(any())).thenReturn(2);

        // act
        RemoteResponse<Integer> result = privateLiveConsultantSummaryService.insertOrUpdateSummaryDataOld(consultantTaskId, userId, liveId);

        // assert
        assertTrue("应该返回成功", result.isSuccess());
        assertEquals(Integer.valueOf(2), result.getData());
    }

    @Test
                         public void testInsertOrUpdateSummaryDataNewSuccess() throws Throwable {
        // arrange
        Long consultantTaskId = 1L;
        Long userId = 1L;
        String liveId = "live123";
        RemoteResponse<Integer> success = RemoteResponse.success(1);

        when(dzPrivateLiveConsultantSummaryService.insertOrUpdateSummaryData(consultantTaskId, userId, liveId)).thenReturn(success);

        // act
        RemoteResponse<Integer> actualResponse = privateLiveConsultantSummaryService.insertOrUpdateSummaryDataNew(consultantTaskId, userId, liveId);

        // assert
        assertNotNull(actualResponse);
        assertEquals(success.getData(), actualResponse.getData());
        verify(dzPrivateLiveConsultantSummaryService, times(1)).insertOrUpdateSummaryData(consultantTaskId, userId, liveId);
    }

    @Test
    public void testUpdateSummaryDataStatusOld_AllParamsAreValid() {
        // arrange
        Long consultantTaskId = 1L;
        Long userId = 1L;
        String liveId = "liveId";
        Integer status = 1;
        when(privateLiveConsultantSummaryRepository.updateStatus(consultantTaskId, userId, liveId, status)).thenReturn(true);
        // act
        RemoteResponse<Boolean> result = privateLiveConsultantSummaryService.updateSummaryDataStatusOld(consultantTaskId, userId, liveId, status);
        // assert
        assertTrue("应当返回成功", result.getData());
        verify(privateLiveConsultantSummaryRepository, times(1)).updateStatus(consultantTaskId, userId, liveId, status);
    }

    @Test
    public void testUpdateSummaryDataStatusNewSuccess() {
        // arrange
        Long consultantTaskId = 1L;
        Long userId = 1L;
        String liveId = "live123";
        Integer status = 1;
        RemoteResponse<Boolean> success = RemoteResponse.success(true);
        when(dzPrivateLiveConsultantSummaryService.updateSummaryDataStatus(consultantTaskId, userId, liveId, status)).thenReturn(success);

        // act
        RemoteResponse<Boolean> actualResponse = privateLiveConsultantSummaryService.updateSummaryDataStatusNew(consultantTaskId, userId, liveId, status);

        // assert
        verify(dzPrivateLiveConsultantSummaryService, times(1)).updateSummaryDataStatus(consultantTaskId, userId, liveId, status);
        assert actualResponse.getData().equals(success.getData());
    }

    @Test
    public void testQueryHotProductByLiveIdOld_PrepayWithData() {
        String liveId = "testLiveId";
        List<PrivateLiveHotProduct> hotProducts = Lists.newArrayList(new PrivateLiveHotProduct(1L, 2, 0, 0, 0L));
        when(privateLiveOrderIntentionResultRepository.queryHotProducts(liveId)).thenReturn(hotProducts);
        ProductEntity productEntity = new ProductEntity();
        productEntity.setName("Test Product");
        when(productAclService.getProducts(any())).thenReturn(Lists.newArrayList(productEntity));

        RemoteResponse<String> result = privateLiveConsultantSummaryService.queryHotProductByLiveIdOld(liveId);

        verify(privateLiveOrderIntentionResultRepository).queryHotProducts(liveId);
    }

    @Test
    public void testQueryHotProductByLiveIdNewSuccess() throws Throwable {
        // arrange
        String liveId = "validLiveId";
        RemoteResponse<String> successData = RemoteResponse.success("successData");

        when(dzPrivateLiveConsultantSummaryService.queryHotProductByLiveId(liveId)).thenReturn(successData);

        // act
        RemoteResponse<String> actualResponse = privateLiveConsultantSummaryService.queryHotProductByLiveIdNew(liveId);

        // assert
        assertEquals("successData", actualResponse.getData());
    }

    @Test
    public void testQueryBossDataByLiveIdOld_ValidLiveIdWithDataAndUserStatistics() {
        // arrange
        String liveId = "validLiveId";
        BossToolDataDTO bossToolDataDTO = new BossToolDataDTO();
        PrivateLiveConsultantUserStatistics statistics = new PrivateLiveConsultantUserStatistics();
        statistics.setTradeOrderCnt(10);
        statistics.setGmvAmt(1000L);

        when(privateLiveConsultantSummaryRepository.queryBossToolDataByLiveId(liveId)).thenReturn(bossToolDataDTO);
        when(privateLiveConsultantSummaryRepository.forceGetSummaryByLiveId(liveId)).thenReturn(statistics);

        // act
        RemoteResponse<BossToolDataDTO> result = privateLiveConsultantSummaryService.queryBossDataByLiveIdOld(liveId);

        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(Integer.valueOf(10), result.getData().getTotalOrderCnt());
        assertEquals(Long.valueOf(1000L), result.getData().getGmvAmt());
    }

    @Test
    public void testQueryBossDataByLiveIdNew_ReturnsSuccess() {
        // arrange
        String liveId = "validLiveId";
        BossToolDataDTO expectedData = new BossToolDataDTO();
        RemoteResponse<BossToolDataDTO> success = RemoteResponse.success(expectedData);
        when(dzPrivateLiveConsultantSummaryService.queryBossDataByLiveId(liveId)).thenReturn(success);

        // act
        RemoteResponse<BossToolDataDTO> actualResponse = privateLiveConsultantSummaryService.queryBossDataByLiveIdNew(liveId);

        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedData, actualResponse.getData());
    }

    @Test
    public void testQueryRankListOld_LiveIdIsNotNull() {
        // arrange
        String liveId = "testLiveId";
        Integer orderBy = 1;
        PrivateLiveConsultantApplicantDTO applicantDTO = new PrivateLiveConsultantApplicantDTO();
        applicantDTO.setLiveId(liveId);
        RemoteResponse<PrivateLiveConsultantRankinglistDTO> expectedResponse = RemoteResponse.success(new PrivateLiveConsultantRankinglistDTO());

        when(privateLiveRankingForCGatewayService.getPrivateLiveConsultantRankinglistDTORemoteResponse(anyInt(), any(PrivateLiveConsultantApplicantDTO.class)))
                .thenReturn(expectedResponse);

        // act
        RemoteResponse<PrivateLiveConsultantRankinglistDTO> result = privateLiveConsultantSummaryService.queryRankListOld(liveId, orderBy);

        // assert
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        verify(privateLiveRankingForCGatewayService, times(1)).getPrivateLiveConsultantRankinglistDTORemoteResponse(orderBy, applicantDTO);
    }

    @Test
    public void testQueryRankListNewReturnsSuccess() {
        // arrange
        String liveId = "testLiveId";
        Integer orderBy = 1;
        RemoteResponse<PrivateLiveConsultantRankinglistDTO> success = RemoteResponse.success(new PrivateLiveConsultantRankinglistDTO());
        when(dzPrivateLiveConsultantSummaryService.queryRankList(liveId, orderBy)).thenReturn(success);

        // act
        RemoteResponse<PrivateLiveConsultantRankinglistDTO> actualResponse = privateLiveConsultantSummaryService.queryRankListNew(liveId, orderBy);

        // assert
        assertEquals(success, actualResponse);
        verify(dzPrivateLiveConsultantSummaryService, times(1)).queryRankList(liveId, orderBy);
    }

    @Test
    public void testQueryConsultantCustomerRankListOld_Normal() {
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("liveId");
        request.setRankNum(2);

        List<PrivateLiveConsultantTask> consultantTasks = new ArrayList<>();
        consultantTasks.add(new PrivateLiveConsultantTask(1L, 2L, "groupId", "liveId", 3L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", 4L, "wechatNumber", 1, null, null, 1));
        when(privateLiveConsultantTaskRepository.loadLiveAllConsultant(anyString())).thenReturn(consultantTasks);

        List<PrivateLiveUserTypeCount> userTypeCounts = new ArrayList<>();
        PrivateLiveUserTypeCount privateLiveUserTypeCount = new PrivateLiveUserTypeCount();
        privateLiveUserTypeCount.setConsultantTaskId(1L);
        privateLiveUserTypeCount.setType(1);
        privateLiveUserTypeCount.setCount(10);
        userTypeCounts.add(privateLiveUserTypeCount);
        when(privateLiveUserIntentionResultRepository.countUserTypeByConsultantTaskId(anyString(), anyList())).thenReturn(userTypeCounts);

        RemoteResponse<ConsultantCustomerRankDTO> response = privateLiveConsultantSummaryService.queryConsultantCustomerRankListOld(request);

        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData().getConsultantCustomerDTOList());
        assertFalse(response.getData().getConsultantCustomerDTOList().isEmpty());
        assertEquals(1, response.getData().getConsultantCustomerDTOList().size());
        ConsultantCustomerDTO dto = response.getData().getConsultantCustomerDTOList().get(0);
        assertEquals(Long.valueOf(1), dto.getConsultantTaskId());
        assertEquals("shareName", dto.getConsultantTaskShareName());
        assertEquals("actualName", dto.getConsultantTaskActualName());
        assertEquals("avatarUrl", dto.getConsultantHeadPic());
        assertEquals(Long.valueOf(10), dto.getAllUserCnt());
    }

    @Test
    public void testQueryConsultantCustomerRankListNew_ReturnsSuccess() {
        // arrange
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("live123");
        request.setRankNum(10);
        RemoteResponse<ConsultantCustomerRankDTO> success = RemoteResponse.success(new ConsultantCustomerRankDTO());

        when(dzPrivateLiveConsultantSummaryService.queryConsultantCustomerRankList(request)).thenReturn(success);

        // act
        RemoteResponse<ConsultantCustomerRankDTO> actualResponse = privateLiveConsultantSummaryService.queryConsultantCustomerRankListNew(request);

        // assert
        assertNotNull(actualResponse);
        assertEquals(200, actualResponse.getCode());
        assertEquals("success", actualResponse.getMsg());
        verify(dzPrivateLiveConsultantSummaryService, times(1)).queryConsultantCustomerRankList(request);
    }

    @Test
    public void testQueryConsultantFollowRankListOld_SuccessWithData() {
        List<PrivateLiveConsultantTask> consultantTasks = Lists.newArrayList(new PrivateLiveConsultantTask(1L, 2L, "groupId", "liveId", 3L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", 4L, "wechatNumber", 1, null, null, 1));
        when(privateLiveConsultantTaskRepository.loadLiveAllConsultant(any(String.class))).thenReturn(consultantTasks);

        List<PrivateSphereCustomerCapitalHandleSizeOverview> followData = Lists.newArrayList(new PrivateSphereCustomerCapitalHandleSizeOverview());
        followData.get(0).setConsultantTaskId(1L);
        followData.get(0).setTotalCount(100L);
        followData.get(0).setHaveHandleSize(50L);
        followData.get(0).setNotHandleSize(50L);
        PageRemoteResponse<PrivateSphereCustomerCapitalHandleSizeOverview> pageRemoteResponse = new PageRemoteResponse<>(0, "success", followData, 100, false, null);
        when(privateSphereUserAclService.queryConsultantFollowDataByLiveId(any(String.class))).thenReturn(pageRemoteResponse);
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("liveId");
        request.setRankNum(5);
        RemoteResponse<ConsultantFollowRankDTO> response = privateLiveConsultantSummaryService.queryConsultantFollowRankListOld(request);

        assertFalse(response.getData().getConsultantFollowDTOList().isEmpty());
        assertEquals(50, response.getData().getConsultantFollowDTOList().get(0).getFollowUser().intValue());
    }

    @Test
    public void testQueryConsultantFollowRankListNewSuccess() {
        // arrange
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("live123");
        request.setRankNum(10);
        RemoteResponse<ConsultantFollowRankDTO> success = RemoteResponse.success(new ConsultantFollowRankDTO());
        when(dzPrivateLiveConsultantSummaryService.queryConsultantFollowRankList(request)).thenReturn(success);

        // act
        RemoteResponse<ConsultantFollowRankDTO> actualResponse = privateLiveConsultantSummaryService.queryConsultantFollowRankListNew(request);

        // assert
        verify(dzPrivateLiveConsultantSummaryService, times(1)).queryConsultantFollowRankList(request);
        assert actualResponse.getCode() == 200;
        assert "success".equals(actualResponse.getMsg());
    }

    @Test
    public void testQueryCustomerTradeRankListOld_Normal() {
        // arrange
        ConsultantCustomerRankQueryRequest request = new ConsultantCustomerRankQueryRequest();
        request.setLiveId("liveId");
        request.setRankNum(10);
        Map<Long, Long> consultantMtUserIds = new HashMap<>();
        consultantMtUserIds.put(1L, 2L);
        when(privateLiveConsultantAccountRepository.batchLoadConsultantUserId(anyString())).thenReturn(consultantMtUserIds);

        List<PrivateLiveConsultantUserStatistics> userStatistics = new ArrayList<>();
        PrivateLiveConsultantUserStatistics stats = new PrivateLiveConsultantUserStatistics();
        stats.setMtUserId(2L);
        stats.setConsultantTaskId(1L);
        userStatistics.add(stats);
        when(privateLiveConsultantSummaryRepository.queryCustomerStatisticRank(anyString(), anyInt(), anyList())).thenReturn(userStatistics);

        List<PrivateLiveConsultantTask> consultantTasks = new ArrayList<>();
        PrivateLiveConsultantTask task = new PrivateLiveConsultantTask();
        task.setId(1L);
        task.setActualName("TaskName");
        consultantTasks.add(task);
        when(privateLiveConsultantTaskRepository.loadByIds(anyList())).thenReturn(consultantTasks);

        Map<Long, WechatUserInfoDTO> userId2Info = new HashMap<>();
        WechatUserInfoDTO userInfoDTO = new WechatUserInfoDTO();
        userInfoDTO.setNickName("NickName");
        userInfoDTO.setAvatarUrl("AvatarUrl");
        userId2Info.put(2L, userInfoDTO);
        when(wechatUserAclService.batchQueryWechatUserByUserId(anyList())).thenReturn(userId2Info);

        // act
        RemoteResponse<CustomerTradeRankDTO> response = privateLiveConsultantSummaryService.queryCustomerTradeRankListOld(request);

    }

}
