//package com.sankuai.carnation.distribution.distribution;
//
//import base.BaseTest;
//import com.alibaba.fastjson.JSON;
//import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
//import com.sankuai.carnation.distribution.distributor.dto.request.PageQueryDistributorRequest;
//import com.sankuai.carnation.distribution.distributor.dto.response.PageQueryDistributorResponse;
//import com.sankuai.carnation.distribution.distributor.service.DistributorInfoService;
//import com.sankuai.carnation.distribution.privatelive.account.application.service.PrivateLiveWechatAccountAppService;
//import com.sankuai.carnation.distribution.privatelive.account.dto.PrivateLiveAccountDTO;
//import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveAccountRoleTaskRequest;
//import com.sankuai.carnation.distribution.privatelive.account.service.PrivateLiveWechatAccountService;
//import com.sankuai.carnation.distribution.privatelive.consultant.enums.ManageOperateTypeEnum;
//import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveTaskAppService;
//import com.sankuai.carnation.distribution.privatelive.distribution.gateway.PrivateLiveDistributorGroupForBGatewayService;
//import com.sankuai.carnation.distribution.privatelive.distribution.gateway.PrivateLivePlanDistributorGroupForBGatewayService;
//import com.sankuai.carnation.distribution.privatelive.distribution.request.AddDistributorGroupForPlanRequest;
//import com.sankuai.carnation.distribution.privatelive.distribution.request.BecomeDistributorGroupRequest;
//import com.sankuai.carnation.distribution.privatelive.distribution.request.DistributorGroupApplyHandleRequest;
//import com.sankuai.carnation.distribution.privatelive.distribution.request.JoinDistributorGroupRequest;
//import com.sankuai.carnation.distribution.privatelive.distribution.response.AddDistributorGroupForPlanResponse;
//import com.sankuai.carnation.distribution.privatelive.distribution.response.BecomeDistributorGroupResponse;
//import com.sankuai.carnation.distribution.privatelive.distribution.response.JoinDistributorGroupResponse;
//import com.sankuai.carnation.distribution.privatelive.distribution.service.PrivateLiveAnchorDistributorGroupService;
//import com.sankuai.carnation.distribution.privatelive.distribution.service.PrivateLivePlanDistributorGroupService;
//import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributorTaskRequest;
//import org.junit.Assert;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import javax.annotation.Resource;
//
///**
// * @Author: bianzhan
// * @CreateTime: 2024-08-20 20:56
// * @Description:
// */
//public class PrivateLiveAnchorDistributorGroupServiceTest extends BaseTest {
//
//    @Autowired
//    private PrivateLiveAnchorDistributorGroupService privateLiveAnchorDistributorGroupService;
//
//    @Autowired
//    private DistributorInfoService distributorInfoService;
//
//
//    @Autowired
//    private PrivateLivePlanDistributorGroupService privateLivePlanDistributorGroupService;
//
//    @Resource
//    private PrivateLiveTaskAppService privateLiveTaskAppService;
//    @Resource
//    private PrivateLivePlanDistributorGroupForBGatewayService privateLivePlanDistributorGroupForBGatewayService;
//    @Resource
//    private PrivateLiveDistributorGroupForBGatewayService privateLiveDistributorGroupForBGatewayService;
//
//    @Resource
//    private PrivateLiveWechatAccountService privateLiveWechatAccountService;
//
//    // 成为团长bind
//    @Test
//    public void testQueryDistributorGroup() {
//        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest();
//        request.setAnchorId(1L);
//        request.setCompanyName("上海艾维庭美容有限公司");
//        request.setPhoneNumber("***********");
//        request.setDistributorGroupName("艾维庭美容SPA");
//        request.setWxToken("45c7cfe87125c25e8c4d09ca780dcd58");
//        RemoteResponse<BecomeDistributorGroupResponse> response =  privateLiveAnchorDistributorGroupService.becomeDistributorGroup(request);
//        Assert.assertNotNull(response.getData());
//    }
//
//    @Test
//    public void testDistributorGroup_HandleApply_Agree() {
//        DistributorGroupApplyHandleRequest request = new DistributorGroupApplyHandleRequest();
//        request.setAnchorId(1L);
//        request.setDistributorGroupId(457L);
//        request.setOperationType(ManageOperateTypeEnum.AGREE.getCode());
//        privateLiveDistributorGroupForBGatewayService.handleApply(request);
//    }
//
//    @Test
//    public void testDistributorGroup_HandleApply_Reject() {
//        DistributorGroupApplyHandleRequest request = new DistributorGroupApplyHandleRequest();
//        request.setAnchorId(1L);
//        request.setDistributorGroupId(457L);
//        request.setOperationType(ManageOperateTypeEnum.REJECT.getCode());
//        privateLiveDistributorGroupForBGatewayService.handleApply(request);
//    }
//
//
//    @Test
//    public void testJoinDistributorGroup_success() {
//        JoinDistributorGroupRequest request = new JoinDistributorGroupRequest();
//        request.setDistributorGroupId(457L);
//        request.setAnchorId(1L);
//        request.setNickname("归途也还可爱");
//        request.setShareName("周兆明");
//        request.setActualName("周兆明");
//        request.setPhoneNumber("18119758738");
//        request.setAvatarUrl("https://p0.meituan.net/travelcube/46896677590e8a1c43e37dfbf2f86759314975.jpg");
//        request.setWxToken("5c702d52cf195770734678d1101fe957");
//        RemoteResponse<JoinDistributorGroupResponse> joinDistributorGroupResponseRemoteResponse = privateLiveAnchorDistributorGroupService.joinDistributorGroup(request);
//        Assert.assertNotNull(joinDistributorGroupResponseRemoteResponse);
//        System.out.println(JSON.toJSONString(joinDistributorGroupResponseRemoteResponse.getData()));
//    }
//
//    @Test
//    public void testPageQueryDistributorList() {
//        PageQueryDistributorRequest request = PageQueryDistributorRequest.builder()
//                .pageSize(20)
//                .pageNum(1)
//                .distributorGroupId(457L)
//                .status(3)
//                .requireStatistics(true)
//                .build();
//        RemoteResponse<PageQueryDistributorResponse> response = distributorInfoService.pageQueryDistributor(request) ;
//        Assert.assertNotNull(response);
//        System.out.println(JSON.toJSONString(response.getData()));
//    }
//
//    @Test
//    public void TestJoinPrivateLiveZoom() {
//        RemoteResponse<AddDistributorGroupForPlanResponse> response = privateLivePlanDistributorGroupForBGatewayService.joinPrivateLiveZoom(AddDistributorGroupForPlanRequest.builder()
//                        .distributorGroupId(457L)
//                        .liveId("1000007051")
//                .build()) ;
//        Assert.assertNotNull(response);
//        System.out.println(JSON.toJSONString(response.getData()));
//    }
//
//    @Test
//    public void testCreateDistributionTask() {
//        privateLiveTaskAppService.joinDistributorGroupTask(PrivateLiveDistributorTaskRequest.builder()
//                .liveId("**********")
//                    .distributorGroupId(457L)
//                    .anchorId(1L)
//                .build());
//
//
//    }
//    @Test
//    public void testAccountTaskType() {
//        RemoteResponse<PrivateLiveAccountDTO> response = privateLiveWechatAccountService.queryPrivateLiveWechatAccount(PrivateLiveAccountRoleTaskRequest.builder().taskId(47460L).build());
//        Assert.assertNotNull(response);
//        System.out.println(JSON.toJSONString(response.getData()));
//    }
//}
