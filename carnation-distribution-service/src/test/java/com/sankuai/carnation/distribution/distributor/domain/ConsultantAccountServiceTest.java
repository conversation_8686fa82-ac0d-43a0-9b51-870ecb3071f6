package com.sankuai.carnation.distribution.distributor.domain;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.carnation.distribution.distributor.domain.bo.ConsultantAccountBO;
import com.sankuai.carnation.distribution.distributor.repository.dao.ConsultantAccountMapper;
import com.sankuai.carnation.distribution.distributor.repository.db.ConsultantAccount;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ConsultantAccountServiceTest {

    @InjectMocks
    private ConsultantAccountService consultantAccountService;

    @Mock
    private ConsultantAccountMapper consultantAccountMapper;

    /**
     * Test selectById method when id is less than or equal to zero, it should return null.
     */
    @Test
    public void testSelectByIdWhenIdIsLessThanOrEqualToZero() throws Throwable {
        long id = 0;
        ConsultantAccountBO result = consultantAccountService.selectById(id);
        assertNull(result);
    }

    /**
     * Test selectById method when id is greater than zero, but there is no corresponding ConsultantAccount object in the database, it should return null.
     */
    @Test
    public void testSelectByIdWhenIdIsGreaterThanZeroButNoConsultantAccountInDB() throws Throwable {
        long id = 1;
        when(consultantAccountMapper.selectById(id)).thenReturn(null);
        ConsultantAccountBO result = consultantAccountService.selectById(id);
        assertNull(result);
    }

    /**
     * Test selectById method when id is greater than zero, and there is a corresponding ConsultantAccount object in the database, it should return a converted ConsultantAccountBO object.
     */
    @Test
    public void testSelectByIdWhenIdIsGreaterThanZeroAndConsultantAccountInDB() throws Throwable {
        long id = 1;
        ConsultantAccount consultantAccount = new ConsultantAccount();
        consultantAccount.setId(id);
        when(consultantAccountMapper.selectById(id)).thenReturn(consultantAccount);
        ConsultantAccountBO result = consultantAccountService.selectById(id);
        assertNotNull(result);
        assertEquals(id, result.getId());
    }

    @Test
    public void testSelectByIdsWithEmptyIds() throws Throwable {
        List<ConsultantAccountBO> result = consultantAccountService.selectByIds(Collections.emptyList());
        assertNull(result);
    }

    @Test
    public void testSelectByIdsWithInvalidPageNumAndPageSize() throws Throwable {
        // Mock setup to return a list of 10 ConsultantAccount objects
        List<ConsultantAccount> mockConsultantAccounts = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ConsultantAccount account = new ConsultantAccount();
            account.setId((long) i);
            mockConsultantAccounts.add(account);
        }
        when(consultantAccountMapper.selectByIds(Arrays.asList(1L, 2L, 3L))).thenReturn(mockConsultantAccounts);
        List<ConsultantAccountBO> result = consultantAccountService.selectByIds(Arrays.asList(1L, 2L, 3L));
        assertEquals(10, result.size());
    }

    @Test
    public void testSelectByIdsWithValidPageNumAndPageSize() throws Throwable {
        // Mock setup to return a list of 10 ConsultantAccount objects
        List<ConsultantAccount> mockConsultantAccounts = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ConsultantAccount account = new ConsultantAccount();
            account.setId((long) i);
            mockConsultantAccounts.add(account);
        }
        when(consultantAccountMapper.selectByIds(Arrays.asList(1L, 2L, 3L))).thenReturn(mockConsultantAccounts);
        List<ConsultantAccountBO> result = consultantAccountService.selectByIds(Arrays.asList(1L, 2L, 3L));
        assertEquals(10, result.size());
    }

    @Test
    public void testSelectByIdsWithEmptyConsultantAccountList() throws Throwable {
        when(consultantAccountMapper.selectByIds(Arrays.asList(1L, 2L, 3L))).thenReturn(Collections.emptyList());
        List<ConsultantAccountBO> result = consultantAccountService.selectByIds(Arrays.asList(1L, 2L, 3L));
        assertEquals(0, result.size());
    }

    @Test
    public void testSelectByIdsWithNonEmptyConsultantAccountList() throws Throwable {
        ConsultantAccount consultantAccount = new ConsultantAccount();
        consultantAccount.setId(1L);
        when(consultantAccountMapper.selectByIds(Arrays.asList(1L, 2L, 3L))).thenReturn(Arrays.asList(consultantAccount));
        List<ConsultantAccountBO> result = consultantAccountService.selectByIds(Arrays.asList(1L, 2L, 3L));
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
    }
}
