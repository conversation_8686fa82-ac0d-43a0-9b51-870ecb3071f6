package com.sankuai.carnation.distribution.privatelive.distribution.startar.service;

/**
 * @Author: jinjianxia
 * @CreateTime: 2024/9/5 16:23
 * @Description:
 */

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveTaskAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskBO;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributorTaskRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.PrivateLiveDistributorTaskResponse;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * 测试PrivateLiveTaskServiceImpl的joinDistributorGroupTask方法
 */
public class PrivateLiveTaskServiceImplTest {

    @InjectMocks
    private PrivateLiveTaskServiceImpl service;

    @Mock
    private PrivateLiveTaskAppService privateLiveTaskAppService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试joinDistributorGroupTask方法，当传入的请求导致返回空列表时
     */
    @Test
    public void testJoinDistributorGroupTaskReturnsEmptyList() {
        PrivateLiveDistributorTaskRequest request = new PrivateLiveDistributorTaskRequest();
        when(privateLiveTaskAppService.joinDistributorGroupTask(request)).thenReturn(Lists.newArrayList());

        RemoteResponse<List<PrivateLiveDistributorTaskResponse>> response = service.joinDistributorGroupTask(request);

        verify(privateLiveTaskAppService, times(1)).joinDistributorGroupTask(request);
        assertEquals("success", response.getMsg());
        assertEquals(0, response.getData().size());
    }

    /**
     * 测试joinDistributorGroupTask方法，当传入的请求导致返回非空列表时
     */
    @Test
    public void testJoinDistributorGroupTaskReturnsNonEmptyList() {
        PrivateLiveDistributorTaskRequest request = new PrivateLiveDistributorTaskRequest();
        PrivateLiveTaskBO bo = new PrivateLiveTaskBO(1L, 2L, "group1", "live1", 3L, "nickname", "avatarUrl", "shareName", "actualName", "phoneNumber", 4L, "wechatNumber", true, null, null, null);
        when(privateLiveTaskAppService.joinDistributorGroupTask(request)).thenReturn(Arrays.asList(bo));

        RemoteResponse<List<PrivateLiveDistributorTaskResponse>> response = service.joinDistributorGroupTask(request);

        verify(privateLiveTaskAppService, times(1)).joinDistributorGroupTask(request);
        assertEquals("success", response.getMsg());
        assertEquals(1, response.getData().size());
        PrivateLiveDistributorTaskResponse responseData = response.getData().get(0);
        assertEquals(Long.valueOf(1), responseData.getId());
        assertEquals("group1", responseData.getGroupId());
    }
}

