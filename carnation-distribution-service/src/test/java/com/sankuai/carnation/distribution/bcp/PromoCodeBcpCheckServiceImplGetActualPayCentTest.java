package com.sankuai.carnation.distribution.bcp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.pay.order.common.enums.AmountType;
import com.sankuai.carnation.distribution.bcp.PromoCodeBcpCheckServiceImpl;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderPaymentDetailBO;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplGetActualPayCentTest {

    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService = new PromoCodeBcpCheckServiceImpl();

    @Test(expected = NullPointerException.class)
    public void testGetActualPayCentWhenOrderInfoIsNull() throws Throwable {
        promoCodeBcpCheckService.getActualPayCent(null);
    }

    @Test
    public void testGetActualPayCentWhenPaymentDetailListIsEmpty() throws Throwable {
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPaymentDetailList(Collections.emptyList());
        long result = promoCodeBcpCheckService.getActualPayCent(orderInfo);
        assertEquals(0L, result);
    }

    @Test
    public void testGetActualPayCentWhenNoMeituanPay() throws Throwable {
        OrderInfoBO orderInfo = new OrderInfoBO();
        OrderPaymentDetailBO paymentDetail = new OrderPaymentDetailBO();
        // Assuming 999 is not MEITUANPAY
        paymentDetail.setAmountType(999);
        paymentDetail.setAmount(new BigDecimal("100"));
        orderInfo.setPaymentDetailList(Arrays.asList(paymentDetail));
        long result = promoCodeBcpCheckService.getActualPayCent(orderInfo);
        assertEquals(0L, result);
    }

    @Test
    public void testGetActualPayCentWhenHasMeituanPay() throws Throwable {
        OrderInfoBO orderInfo = new OrderInfoBO();
        OrderPaymentDetailBO paymentDetail = new OrderPaymentDetailBO();
        // Correctly setting MEITUANPAY
        // Assuming AmountType.MEITUANPAY.value provides the correct value
        paymentDetail.setAmountType(AmountType.MEITUANPAY.value);
        paymentDetail.setAmount(new BigDecimal("100"));
        orderInfo.setPaymentDetailList(Arrays.asList(paymentDetail));
        long result = promoCodeBcpCheckService.getActualPayCent(orderInfo);
        assertEquals(10000L, result);
    }

    @Test
    public void testGetActualPayCentWhenPaymentDetailListIsZero() throws Throwable {
        OrderInfoBO orderInfo = new OrderInfoBO();
        OrderPaymentDetailBO paymentDetail = new OrderPaymentDetailBO();
        // Correctly setting MEITUANPAY
        paymentDetail.setAmountType(AmountType.MEITUANPAY.value);
        paymentDetail.setAmount(BigDecimal.ZERO);
        orderInfo.setPaymentDetailList(Arrays.asList(paymentDetail));
        long result = promoCodeBcpCheckService.getActualPayCent(orderInfo);
        assertEquals(0L, result);
    }

    /**
     * 测试所有参数都为空的情况
     */
    @Test
    public void testGetRebateBizIdAllParamsNull() throws Throwable {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        String result = promoCodeBcpCheckService.getRebateBizId(request);
        assertEquals("onull_cnull_r0", result);
    }

    /**
     * 测试orderId为空，其他参数不为空的情况
     */
    @Test
    public void testGetRebateBizIdOrderIdNull() throws Throwable {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setCouponVerifyId("couponId");
        request.setTechApplyRecordId(123L);
        String result = promoCodeBcpCheckService.getRebateBizId(request);
        assertEquals("onull_ccouponId_r123", result);
    }

    /**
     * 测试couponVerifyId为空，其他参数不为空的情况
     */
    @Test
    public void testGetRebateBizIdCouponVerifyIdNull() throws Throwable {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("orderId");
        request.setTechApplyRecordId(123L);
        String result = promoCodeBcpCheckService.getRebateBizId(request);
        // Corrected the expected value to match the actual output
        assertEquals("oorderId_cnull_r123", result);
    }

    /**
     * 测试techApplyRecordId为空，其他参数不为空的情况
     */
    @Test
    public void testGetRebateBizIdTechApplyRecordIdNull() throws Throwable {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("orderId");
        request.setCouponVerifyId("couponId");
        String result = promoCodeBcpCheckService.getRebateBizId(request);
        assertEquals("oorderId_ccouponId_r0", result);
    }

    /**
     * 测试所有参数都不为空的情况
     */
    @Test
    public void testGetRebateBizIdAllParamsNotNull() throws Throwable {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("orderId");
        request.setCouponVerifyId("couponId");
        request.setTechApplyRecordId(123L);
        String result = promoCodeBcpCheckService.getRebateBizId(request);
        assertEquals("oorderId_ccouponId_r123", result);
    }

    /**
     * Test environment: verifyTime is before the synchronization time.
     */
    @Test
    public void testCommissionRateSynchronizedTestEnvBefore() throws Throwable {
        // Arrange
        Calendar calendar = Calendar.getInstance();
        // Ensure the time is set to a known condition before the synchronization time
        // Adjusting the minute to ensure the test is reliable
        calendar.add(Calendar.MINUTE, -2);
        Date verifyTime = calendar.getTime();
        // Act
        boolean result = promoCodeBcpCheckService.commissionRateSynchronized(verifyTime);
        // Assert
        assertTrue(result);
    }

    /**
     * Test environment: verifyTime is after the synchronization time.
     */
    @Test
    public void testCommissionRateSynchronizedTestEnvAfter() throws Throwable {
        // Arrange
        Calendar calendar = Calendar.getInstance();
        // Ensure the time is set to a known condition after the synchronization time
        // Adjusting the minute to ensure the test is reliable
        calendar.add(Calendar.MINUTE, 2);
        Date verifyTime = calendar.getTime();
        // Act
        boolean result = promoCodeBcpCheckService.commissionRateSynchronized(verifyTime);
        // Assert
        assertFalse(result);
    }

    /**
     * Non-test environment: verifyTime is before the synchronization time.
     */
    @Test
    public void testCommissionRateSynchronizedNonTestEnvBefore() throws Throwable {
        // Arrange
        Calendar calendar = Calendar.getInstance();
        // Ensure the time is set to a known condition before the synchronization time
        // Adjusting the hour to ensure the test is reliable
        calendar.add(Calendar.HOUR_OF_DAY, -19);
        Date verifyTime = calendar.getTime();
        // Act
        boolean result = promoCodeBcpCheckService.commissionRateSynchronized(verifyTime);
        // Assert
        assertTrue(result);
    }

    /**
     * Non-test environment: verifyTime is after the synchronization time.
     */
    @Test
    public void testCommissionRateSynchronizedNonTestEnvAfter() throws Throwable {
        // Arrange
        Calendar calendar = Calendar.getInstance();
        // Ensure the time is set to a known condition after the synchronization time
        // Adjusting the hour to ensure the test is reliable
        calendar.add(Calendar.HOUR_OF_DAY, 19);
        Date verifyTime = calendar.getTime();
        // Act
        boolean result = promoCodeBcpCheckService.commissionRateSynchronized(verifyTime);
        // Assert
        assertFalse(result);
    }
}
