package com.sankuai.carnation.distribution.privatelive.distribution.application.service.impl;

/**
 * @Author: jinjianxia
 * @CreateTime: 2024/9/5 16:27
 * @Description:
 */

import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindResponse;
import com.sankuai.carnation.distribution.privatelive.account.application.service.PrivateLiveWechatAccountAppService;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveWechatAccountRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.application.model.DistributionQrCodeInfo;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveBecomeDistributorGroupCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveJoinDistributorGroupCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.service.PrivateLiveAnchorDistributorGroupDomainService;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.PrivateLiveAccountTypeEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.request.BecomeDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.DistributorGroupPassAuditRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.JoinDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.BecomeDistributorGroupResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.DistributorGroupApplyHandleResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.response.JoinDistributorGroupResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveAnchorDistributorGroupAppServiceImplTest {

    @Mock
    private PrivateLiveAnchorDistributorGroupDomainService privateLiveAnchorDistributorGroupDomainService;
    @Mock
    private PrivateLiveWechatAccountAppService privateLiveWechatAccountAppService;
    @InjectMocks
    private PrivateLiveAnchorDistributorGroupAppServiceImpl privateLiveAnchorDistributorGroupAppService;
    @Mock
    private PrivateLiveDistributionQrCodeAppServiceImpl privateLiveDistributionQrCodeAppService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试用户已经是团员身份，不能重复申请的场景
     */
    @Test(expected = BizSceneException.class)
    public void testJoinDistributorGroup_UserAlreadyDistributor() {
        // arrange
        JoinDistributorGroupRequest request = new JoinDistributorGroupRequest();
        request.setWxToken("wxToken");
        request.setAnchorId(1L);

        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
//        accountBO.setDistributor(new DistributorBO()); // 用户已经是团员
        accountBO.setAccountTypeList(Arrays.asList(PrivateLiveAccountTypeEnum.DISTRIBUTOR));
        accountBO.setStatus(DistributionStatusEnum.VALID);
        accountBO.setId(123L);
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);

        //when(privateLiveAnchorDistributorGroupDomainService.joinDistributorGroup(any(PrivateLiveJoinDistributorGroupCmd.class))).thenReturn(new DistributorBindResponse());
        // act
        privateLiveAnchorDistributorGroupAppService.joinDistributorGroup(request);

        // assert
        // Expected BizSceneException
    }

    /**
     * 测试用户成功加入分销团队的场景
     */
    @Test
    public void testJoinDistributorGroup_Success() {
        // arrange
        JoinDistributorGroupRequest request = new JoinDistributorGroupRequest();
        request.setWxToken("wxToken");
        request.setAnchorId(1L);
        request.setDistributorGroupId(1L);

        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
//        accountBO.setDistributor(new DistributorBO()); // 用户不是团员
        accountBO.setStatus(DistributionStatusEnum.INVALID);
        DistributorBindResponse distributorBindResponse = new DistributorBindResponse(1L);

        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        when(privateLiveAnchorDistributorGroupDomainService.joinDistributorGroup(any(PrivateLiveJoinDistributorGroupCmd.class))).thenReturn(distributorBindResponse);

        // act
        JoinDistributorGroupResponse response = privateLiveAnchorDistributorGroupAppService.joinDistributorGroup(request);

        // assert
        assertNotNull(response);
        assertEquals(Long.valueOf(1L), response.getDistributorId());
        assertEquals(Long.valueOf(1L), response.getDistributorGroupId());
    }

    /**
     * 测试用户微信账号为空的场景
     */
    @Test(expected = BizSceneException.class)
    public void testJoinDistributorGroup_NullWechatAccount() {
        // arrange
        JoinDistributorGroupRequest request = new JoinDistributorGroupRequest();
        request.setWxToken(null);
        request.setAnchorId(1L);

        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(null);
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(new PrivateLiveAccountBO());
        when(privateLiveAnchorDistributorGroupDomainService.joinDistributorGroup(any(PrivateLiveJoinDistributorGroupCmd.class))).thenReturn(new DistributorBindResponse());
        // act
        privateLiveAnchorDistributorGroupAppService.joinDistributorGroup(request);

        // assert
        // Expected BizSceneException
    }

    /**
     * 测试用户已经是团长身份，不能重复申请的场景
     */
    @Test(expected = BizSceneException.class)
    public void testBecomeDistributorGroup_UserAlreadyDistributorGroup() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, null, "groupName", "phoneNumber", "companyName", "wxToken");
        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
        accountBO.setId(123L);
        DistributorGroupBO distributorGroupBO = new DistributorGroupBO();
        distributorGroupBO.setGroupId(1);
        accountBO.setDistributorGroup(distributorGroupBO);
        accountBO.setAccountTypeList(Arrays.asList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP));

        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);

        // act
        privateLiveAnchorDistributorGroupAppService.becomeDistributorGroup(request);

        // assert is handled by the expected exception
    }

    /**
     * 测试正常申请成为团长的场景
     */
    @Test
    public void testBecomeDistributorGroup_Normal() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, null, "groupName", "phoneNumber", "companyName", "wxToken");
        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
        PrivateLiveAnchorDistributorGroupBO distributorGroupBO = new PrivateLiveAnchorDistributorGroupBO();
        distributorGroupBO.setStatus(DistributionApproveStatusEnum.PASS);
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        when(privateLiveAnchorDistributorGroupDomainService.becomeDistributorGroup(any(PrivateLiveBecomeDistributorGroupCmd.class))).thenReturn(distributorGroupBO);

        // act
        BecomeDistributorGroupResponse response = privateLiveAnchorDistributorGroupAppService.becomeDistributorGroup(request);

        // assert
        assertNotNull(response);
        assertEquals(DistributionApproveStatusEnum.PASS.getCode(), response.getStatus());
    }

    /**
     * 测试查询微信账户为空的场景
     */
    @Test
    public void testBecomeDistributorGroup_WechatAccountIsNull() {
        // arrange
        BecomeDistributorGroupRequest request = new BecomeDistributorGroupRequest(1L, null, "groupName", "phoneNumber", "companyName", "wxToken");
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(null);
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(new PrivateLiveAccountBO());
        when(privateLiveAnchorDistributorGroupDomainService.becomeDistributorGroup(any(PrivateLiveBecomeDistributorGroupCmd.class))).thenReturn(new PrivateLiveAnchorDistributorGroupBO());
        // act
        BecomeDistributorGroupResponse response = privateLiveAnchorDistributorGroupAppService.becomeDistributorGroup(request);

        // assert
        assertNull(response.getDistributorGroupId());
    }

    /**
     * 测试 getOrCreateRegisterDistributorGroupQrCode 方法，当 anchorId 为 null 时的场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterDistributorGroupQrCodeWithNullAnchorId() throws Throwable {
        // arrange
        Long anchorId = null;

        // act
        privateLiveAnchorDistributorGroupAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId);

        // assert 由于预期抛出异常，所以不需要断言
    }

    /**
     * 测试 getOrCreateRegisterDistributorGroupQrCode 方法，当 anchorId 有效且二维码已存在时的场景
     */
    @Test
    public void testGetOrCreateRegisterDistributorGroupQrCodeWithValidAnchorIdAndQrCodeExists() throws Throwable {
        // arrange
        Long anchorId = 123L;
        DistributionQrCodeInfo expectedQrCodeInfo = new DistributionQrCodeInfo("codeUrl", "imageUrl");
        // 假设 service 已经有了某种方式来获取或创建二维码信息，这里用 mock 来模拟这个行为
        when(privateLiveAnchorDistributorGroupAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId)).thenReturn(expectedQrCodeInfo);

        // act
        DistributionQrCodeInfo actualQrCodeInfo = privateLiveAnchorDistributorGroupAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId);

        // assert
        assertNotNull(actualQrCodeInfo);
        assertEquals(expectedQrCodeInfo.getCodeUrl(), actualQrCodeInfo.getCodeUrl());
        assertEquals(expectedQrCodeInfo.getImageUrl(), actualQrCodeInfo.getImageUrl());
    }

    /**
     * 测试 getOrCreateRegisterDistributorGroupQrCode 方法，当 anchorId 有效但二维码不存在，需要创建时的场景
     */
    @Test
    public void testGetOrCreateRegisterDistributorGroupQrCodeWithValidAnchorIdAndQrCodeNotExists() throws Throwable {
        // arrange
        Long anchorId = 456L;
        DistributionQrCodeInfo expectedQrCodeInfo = new DistributionQrCodeInfo("newCodeUrl", "newImageUrl");
        // 假设 service 有某种方式来创建二维码信息，这里用 mock 来模拟这个行为
        when(privateLiveAnchorDistributorGroupAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId)).thenReturn(expectedQrCodeInfo);

        // act
        DistributionQrCodeInfo actualQrCodeInfo = privateLiveAnchorDistributorGroupAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId);

        // assert
        assertNotNull(actualQrCodeInfo);
        assertEquals(expectedQrCodeInfo.getCodeUrl(), actualQrCodeInfo.getCodeUrl());
        assertEquals(expectedQrCodeInfo.getImageUrl(), actualQrCodeInfo.getImageUrl());
    }
    /**
     * 测试 getOrCreateRegisterDistributorGroupQrCode 方法，当传入的 anchorId 为 null 时，应抛出 IllegalArgumentException 异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetOrCreateRegisterDistributorGroupQrCodeNullAnchorId() {
        // arrange
        Long anchorId = null;

        // act
        privateLiveAnchorDistributorGroupAppService.getOrCreateRegisterDistributorGroupQrCode(anchorId);

        // assert 由于预期抛出异常，所以不需要写断言
    }

    /**
     * 测试审核通过的情况
     */
    @Test
    public void testPassAuditSuccess() {
        // arrange
        DistributorGroupPassAuditRequest request = new DistributorGroupPassAuditRequest(1L, 2L, "groupName", "customerId", "customerName");
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO();
        mockBO.setStatus(DistributionApproveStatusEnum.PASS);
        when(privateLiveAnchorDistributorGroupDomainService.passAudit(any(DistributorGroupPassAuditRequest.class))).thenReturn(mockBO);

        // act
        DistributorGroupApplyHandleResponse response = privateLiveAnchorDistributorGroupAppService.passAudit(request);

        // assert
        assertNotNull(response);
        assertEquals(DistributionApproveStatusEnum.PASS.getCode(), response.getStatus());
        verify(privateLiveAnchorDistributorGroupDomainService, times(1)).passAudit(any(DistributorGroupPassAuditRequest.class));
    }

    /**
     * 测试审核通过但返回对象为null的情况
     */
    @Test
    public void testPassAuditSuccessButReturnNull() {
        // arrange
        DistributorGroupPassAuditRequest request = new DistributorGroupPassAuditRequest(1L, 2L, "groupName", "customerId", "customerName");
        when(privateLiveAnchorDistributorGroupDomainService.passAudit(any(DistributorGroupPassAuditRequest.class))).thenReturn(new PrivateLiveAnchorDistributorGroupBO());
        // act
        DistributorGroupApplyHandleResponse response = privateLiveAnchorDistributorGroupAppService.passAudit(request);

        // assert
        assertNotNull(response);
        assertNull(response.getStatus());
        verify(privateLiveAnchorDistributorGroupDomainService, times(1)).passAudit(any(DistributorGroupPassAuditRequest.class));
    }

    /**
     * 测试审核状态为等待审核的情况
     */
    @Test
    public void testPassAuditWaiting() {
        // arrange
        DistributorGroupPassAuditRequest request = new DistributorGroupPassAuditRequest(1L, 2L, "groupName", "customerId", "customerName");
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO();
        mockBO.setStatus(DistributionApproveStatusEnum.WAITING);
        when(privateLiveAnchorDistributorGroupDomainService.passAudit(any(DistributorGroupPassAuditRequest.class))).thenReturn(mockBO);

        // act
        DistributorGroupApplyHandleResponse response = privateLiveAnchorDistributorGroupAppService.passAudit(request);

        // assert
        assertNotNull(response);
        assertEquals(DistributionApproveStatusEnum.WAITING.getCode(), response.getStatus());
        verify(privateLiveAnchorDistributorGroupDomainService, times(1)).passAudit(any(DistributorGroupPassAuditRequest.class));
    }

    /**
     * 测试审核状态为已驳回的情况
     */
    @Test
    public void testPassAuditRejected() {
        // arrange
        DistributorGroupPassAuditRequest request = new DistributorGroupPassAuditRequest(1L, 2L, "groupName", "customerId", "customerName");
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO();
        mockBO.setStatus(DistributionApproveStatusEnum.REJECT);
        when(privateLiveAnchorDistributorGroupDomainService.passAudit(any(DistributorGroupPassAuditRequest.class))).thenReturn(mockBO);

        // act
        DistributorGroupApplyHandleResponse response = privateLiveAnchorDistributorGroupAppService.passAudit(request);

        // assert
        assertNotNull(response);
        assertEquals(DistributionApproveStatusEnum.REJECT.getCode(), response.getStatus());
        verify(privateLiveAnchorDistributorGroupDomainService, times(1)).passAudit(any(DistributorGroupPassAuditRequest.class));
    }

    /**
     * 测试审核状态为已取消的情况
     */
    @Test
    public void testPassAuditCanceled() {
        // arrange
        DistributorGroupPassAuditRequest request = new DistributorGroupPassAuditRequest(1L, 2L, "groupName", "customerId", "customerName");
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO();
        mockBO.setStatus(DistributionApproveStatusEnum.CANCELED);
        when(privateLiveAnchorDistributorGroupDomainService.passAudit(any(DistributorGroupPassAuditRequest.class))).thenReturn(mockBO);

        // act
        DistributorGroupApplyHandleResponse response = privateLiveAnchorDistributorGroupAppService.passAudit(request);

        // assert
        assertNotNull(response);
        assertEquals(DistributionApproveStatusEnum.CANCELED.getCode(), response.getStatus());
        verify(privateLiveAnchorDistributorGroupDomainService, times(1)).passAudit(any(DistributorGroupPassAuditRequest.class));
    }

    /**
     * 测试审核请求参数为null的情况
     */
    @Test(expected = NullPointerException.class)
    public void testPassAuditRequestNull() {
        // arrange
        DistributorGroupPassAuditRequest request = null;

        // act
        privateLiveAnchorDistributorGroupAppService.passAudit(request);

        // assert
        // Expected exception
    }
}
