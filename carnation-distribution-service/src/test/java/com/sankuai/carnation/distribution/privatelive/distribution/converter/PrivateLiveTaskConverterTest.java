package com.sankuai.carnation.distribution.privatelive.distribution.converter;

import com.sankuai.carnation.distribution.privatelive.account.enums.PrivateLiveTaskTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskBO;
import com.sankuai.carnation.distribution.privatelive.distribution.utils.NullableUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.Date;

import static org.junit.Assert.*;

public class PrivateLiveTaskConverterTest {

    private PrivateLiveTaskConverter converter;

    @Before
    public void setUp() {
        converter = new PrivateLiveTaskConverter();
    }

    /**
     * 测试 toPoJo 方法，当 PrivateLiveTaskBO 中的 status 和 taskType 都不为 null 时
     */
    @Test
    public void testToPoJoWithNonNullStatusAndTaskType() {
        // arrange
        PrivateLiveTaskBO privateLiveTaskBO = PrivateLiveTaskBO.builder()
                .accountId(1L)
                .status(ConsultantTaskApproveStatusEnum.PASS)
                .taskType(PrivateLiveTaskTypeEnum.CONSULTANT)
                .build();

        // act
        PrivateLiveConsultantTask result = converter.toPoJo(privateLiveTaskBO);

        // assert
        assertNotNull(result);
        assertEquals(ConsultantTaskApproveStatusEnum.PASS.getCode(), result.getStatus());
//        assertEquals(PrivateLiveTaskTypeEnum.CONSULTANT.getCode(), result.getTaskType());
        assertEquals(privateLiveTaskBO.getAccountId(), result.getConsultantId());
    }

    /**
     * 测试 toPoJo 方法，当 PrivateLiveTaskBO 中的 status 为 null 时
     */
    @Test
    public void testToPoJoWithNullStatus() {
        // arrange
        PrivateLiveTaskBO privateLiveTaskBO = PrivateLiveTaskBO.builder()
                .accountId(1L)
                .taskType(PrivateLiveTaskTypeEnum.CONSULTANT)
                .build();

        // act
        PrivateLiveConsultantTask result = converter.toPoJo(privateLiveTaskBO);

        // assert
        assertNotNull(result);
        assertEquals(null, result.getStatus());
//        assertEquals(PrivateLiveTaskTypeEnum.CONSULTANT.getCode(), result.getTaskType());
        assertEquals(privateLiveTaskBO.getAccountId(), result.getConsultantId());
    }

    /**
     * 测试 toPoJo 方法，当 PrivateLiveTaskBO 中的 taskType 为 null 时
     */
    @Test
    public void testToPoJoWithNullTaskType() {
        // arrange
        PrivateLiveTaskBO privateLiveTaskBO = PrivateLiveTaskBO.builder()
                .accountId(1L)
                .status(ConsultantTaskApproveStatusEnum.PASS)
                .build();

        // act
        PrivateLiveConsultantTask result = converter.toPoJo(privateLiveTaskBO);

        // assert
        assertNotNull(result);
        assertEquals(ConsultantTaskApproveStatusEnum.PASS.getCode(), result.getStatus());
        assertEquals(null, result.getTaskType());
        assertEquals(privateLiveTaskBO.getAccountId(), result.getConsultantId());
    }

    /**
     * 测试 toPoJo 方法，当 PrivateLiveTaskBO 中的 status 和 taskType 都为 null 时
     */
    @Test
    public void testToPoJoWithNullStatusAndTaskType() {
        // arrange
        PrivateLiveTaskBO privateLiveTaskBO = PrivateLiveTaskBO.builder()
                .accountId(1L)
                .build();

        // act
        PrivateLiveConsultantTask result = converter.toPoJo(privateLiveTaskBO);

        // assert
        assertNotNull(result);
        assertEquals(null, result.getStatus());
        assertEquals(null, result.getTaskType());
        assertEquals(privateLiveTaskBO.getAccountId(), result.getConsultantId());
    }

    /**
     * 测试toEntity方法，当PrivateLiveConsultantTask的所有字段都非空时
     */
    @Test
    public void testToEntityAllFieldsNonNull() {
        // arrange
        PrivateLiveConsultantTask privateLiveTask = PrivateLiveConsultantTask.builder()
                .id(1L)
                .consultantId(2L)
                .groupId("group1")
                .liveId("live1")
                .anchorId(3L)
                .nickname("nickname")
                .avatarUrl("avatarUrl")
                .shareName("shareName")
                .actualName("actualName")
                .phoneNumber("phoneNumber")
                .shopId(4L)
                .wechatNumber("wechatNumber")
                .status(1)
                .addTime(new Date())
                .updateTime(new Date())
                .taskType(1)
                .build();

        // act
        PrivateLiveTaskBO result = converter.toEntity(privateLiveTask);

        // assert
        assertNotNull(result);
        assertEquals(ConsultantTaskApproveStatusEnum.PASS, result.getStatus());
        assertEquals(PrivateLiveTaskTypeEnum.CONSULTANT, result.getTaskType());
        assertEquals(privateLiveTask.getConsultantId(), result.getAccountId());
    }

    /**
     * 测试toEntity方法，当PrivateLiveConsultantTask的status和taskType字段为null时
     */
    @Test
    public void testToEntityStatusAndTaskTypeNull() {
        // arrange
        PrivateLiveConsultantTask privateLiveTask = PrivateLiveConsultantTask.builder()
                .id(1L)
                .consultantId(2L)
                .taskType(null)
                .build();

        // act
        PrivateLiveTaskBO result = converter.toEntity(privateLiveTask);

        // assert
        assertNotNull(result);
        assertEquals(null, result.getStatus());
        assertEquals(null, result.getTaskType());
        assertEquals(privateLiveTask.getConsultantId(), result.getAccountId());
    }

    /**
     * 测试toEntity方法，当PrivateLiveConsultantTask的status为有效值，taskType为null时
     */
    @Test
    public void testToEntityStatusValidTaskTypeNull() {
        // arrange
        PrivateLiveConsultantTask privateLiveTask = PrivateLiveConsultantTask.builder()
                .id(1L)
                .consultantId(2L)
                .status(1) // PASS
                .build();
        // act
        PrivateLiveTaskBO result = converter.toEntity(privateLiveTask);
        // assert
        assertNotNull(result);
        assertEquals(ConsultantTaskApproveStatusEnum.PASS, result.getStatus());
        assertNull(result.getTaskType());
        assertEquals(privateLiveTask.getConsultantId(), result.getAccountId());
    }

    /**
     * 测试toEntity方法，当PrivateLiveConsultantTask的status为null，taskType为有效值时
     */
    @Test
    public void testToEntityStatusNullTaskTypeValid() {
        // arrange
        PrivateLiveConsultantTask privateLiveTask = PrivateLiveConsultantTask.builder()
                .id(1L)
                .consultantId(2L)
                .taskType(1) // CONSULTANT
                .build();
        // act
        PrivateLiveTaskBO result = converter.toEntity(privateLiveTask);
        // assert
        assertNotNull(result);
        assertEquals(null, result.getStatus());
        assertEquals(PrivateLiveTaskTypeEnum.CONSULTANT, result.getTaskType());
        assertEquals(privateLiveTask.getConsultantId(), result.getAccountId());
    }

    /**
     * 测试toEntity方法，当PrivateLiveConsultantTask的status和taskType字段均为有效值时
     */
    @Test
    public void testToEntityStatusAndTaskTypeValid() {
        // arrange
        PrivateLiveConsultantTask privateLiveTask = PrivateLiveConsultantTask.builder()
                .id(1L)
                .consultantId(2L)
                .status(1) // PASS
                .taskType(2) // DISTRIBUTOR
                .build();
        // act
        PrivateLiveTaskBO result = converter.toEntity(privateLiveTask);
        // assert
        assertNotNull(result);
        assertEquals(ConsultantTaskApproveStatusEnum.PASS, result.getStatus());
        assertEquals(PrivateLiveTaskTypeEnum.DISTRIBUTOR, result.getTaskType());
        assertEquals(privateLiveTask.getConsultantId(), result.getAccountId());
    }

}
