package com.sankuai.carnation.distribution.product.v2.repository.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.product.acl.ProductSelectifyAclService;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.PluralProductBO;
import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnitCriteria;
import com.sankuai.medicalcosmetology.product.selectify.api.enums.ProductActionTypeEnum;
import com.sankuai.medicalcosmetology.product.selectify.api.request.ProductActionRequest;
import com.sankuai.medicalcosmetology.product.selectify.api.request.ProductRequest;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductItemSaleUnitDataServiceTest {

    @InjectMocks
    private ProductItemSaleUnitDataService productItemSaleUnitDataService;

    @Mock
    private ProductItemSaleUnitMapper mapper;

    private long productItemId = 1L;

    private List<PluralProductBO> relateProductList = Arrays.asList(new PluralProductBO(1, 1L, 1, 1L));

    private ProductItemSaleUnit productItemSaleUnit;

    @Mock
    private ProductSelectifyAclService productSelectifyAclService;

    @Mock
    private ProductItemBO productItemBO;

    @Mock
    private ProductItemSaleUnit saleUnit;

    private Map<String, ProductItemSaleUnit> formerMap = new HashMap<>();

    @Before
    public void setUp() {
        productItemSaleUnit = new ProductItemSaleUnit();
        productItemSaleUnit.setProductType(1);
        productItemSaleUnit.setProductId(1L);
        productItemSaleUnit.setStatus(DistributionStatusEnum.VALID.getCode());
    }

    /**
     * Helper method to create sample ProductItemSaleUnit list
     */
    private List<ProductItemSaleUnit> createSampleProductItemSaleUnits(long productItemId) {
        ProductItemSaleUnit unit1 = new ProductItemSaleUnit();
        unit1.setId(1L);
        unit1.setProductItemId(productItemId);
        unit1.setProductType(1);
        unit1.setProductId(100L);
        unit1.setSkuId(1000L);
        unit1.setAmount(1);
        unit1.setStatus(1);
        unit1.setAddTime(new Date());
        unit1.setUpdateTime(new Date());
        ProductItemSaleUnit unit2 = new ProductItemSaleUnit();
        unit2.setId(2L);
        unit2.setProductItemId(productItemId);
        unit2.setProductType(1);
        unit2.setProductId(101L);
        unit2.setSkuId(1001L);
        unit2.setAmount(2);
        unit2.setStatus(1);
        unit2.setAddTime(new Date());
        unit2.setUpdateTime(new Date());
        return Arrays.asList(unit1, unit2);
    }

    // Utility method to invoke private methods using reflection
    private ProductActionRequest invokePrivateMethod(Object target, String methodName, Object... args) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, ProductItemBO.class, List.class, ProductActionTypeEnum.class);
        method.setAccessible(true);
        return (ProductActionRequest) method.invoke(target, args);
    }

    private List<ProductRequest> invokePrivateMethod(List<ProductItemSaleUnit> relateProductList) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("createProductRequestList", List.class);
        method.setAccessible(true);
        return (List<ProductRequest>) method.invoke(productItemSaleUnitDataService, relateProductList);
    }

    private ProductItemSaleUnit createProductItemSaleUnit() {
        ProductItemSaleUnit productItemSaleUnit = new ProductItemSaleUnit();
        productItemSaleUnit.setProductItemId(1L);
        return productItemSaleUnit;
    }

    private void invokeExecuteDatabaseOperations(Object... lists) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("executeDatabaseOperations", java.util.List.class, java.util.List.class, java.util.List.class);
        method.setAccessible(true);
        try {
            method.invoke(productItemSaleUnitDataService, lists);
        } catch (InvocationTargetException e) {
            if (e.getCause() instanceof ProductItemException) {
                throw (ProductItemException) e.getCause();
            }
            throw e;
        }
    }

    private boolean invokeIsDeactivated(ProductItemSaleUnit newPo, Map<String, ProductItemSaleUnit> formerMap) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("isDeactivated", ProductItemSaleUnit.class, Map.class);
        method.setAccessible(true);
        try {
            return (boolean) method.invoke(productItemSaleUnitDataService, saleUnit, formerMap);
        } catch (InvocationTargetException e) {
            if (e.getCause() instanceof ProductItemException) {
                throw (ProductItemException) e.getCause();
            }
            throw e;
        }
    }

    private ProductItemSaleUnit createSaleUnit() {
        ProductItemSaleUnit saleUnit = new ProductItemSaleUnit();
        saleUnit.setProductItemId(1L);
        saleUnit.setProductType(1);
        saleUnit.setProductId(1L);
        saleUnit.setSkuId(1L);
        saleUnit.setAmount(1);
        saleUnit.setStatus(1);
        return saleUnit;
    }

    private boolean invokeSyncBatchAddItemSaleUnit(List<ProductItemSaleUnit> list) throws Exception {
        Method method = ProductItemSaleUnitDataService.class.getDeclaredMethod("syncBatchAddItemSaleUnit", List.class);
        method.setAccessible(true);
        return (boolean) method.invoke(productItemSaleUnitDataService, list);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitProductItemIdInvalid() throws Throwable {
        productItemSaleUnitDataService.setProductItemSaleUnit(0L, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitRelateProductListEmpty() throws Throwable {
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, Collections.emptyList());
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitBatchAddFailed() throws Throwable {
        when(mapper.selectByExample(any())).thenReturn(Collections.emptyList());
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitBatchUpdateFailed() throws Throwable {
        List<ProductItemSaleUnit> existingUnits = Arrays.asList(new ProductItemSaleUnit());
        when(mapper.selectByExample(any())).thenReturn(existingUnits);
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitBatchRemoveFailed() throws Throwable {
        List<ProductItemSaleUnit> existingUnits = Arrays.asList(new ProductItemSaleUnit());
        when(mapper.selectByExample(any())).thenReturn(existingUnits);
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitBatchOperationFailed() throws Throwable {
        when(mapper.selectByExample(any())).thenReturn(Collections.emptyList());
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitPartialBatchOperationFailed() throws Throwable {
        when(mapper.selectByExample(any())).thenReturn(Collections.emptyList());
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitPartialBatchOperationSuccess() throws Throwable {
        List<ProductItemSaleUnit> existingUnits = Arrays.asList(new ProductItemSaleUnit());
        when(mapper.selectByExample(any())).thenReturn(existingUnits);
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, relateProductList);
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnitInvalidPluralProductBO() throws Throwable {
        List<PluralProductBO> invalidRelateProductList = Arrays.asList(new PluralProductBO(0, 0L, 0, 0L));
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, invalidRelateProductList);
    }

    @Test
    public void testGetValidSaleUnitByOriginProductId_InvalidInput() throws Throwable {
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnitByOriginProductId(-1, -1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetValidSaleUnitByOriginProductId_ValidInput_NoRecord() throws Throwable {
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Collections.emptyList());
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnitByOriginProductId(1, 1L);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetValidSaleUnitByOriginProductId_ValidInput_WithRecord() throws Throwable {
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Collections.singletonList(productItemSaleUnit));
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getValidSaleUnitByOriginProductId(1, 1L);
        assertEquals(Collections.singletonList(productItemSaleUnit), result);
    }

    /**
     * Test getSaleUnit method when productItemId is zero
     * Should return empty list
     */
    @Test
    public void testGetSaleUnit_WhenProductItemIdIsZero() {
        // arrange
        long productItemId = 0L;
        // act
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getSaleUnit(productItemId);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(Lists.newArrayList(), result);
        verify(mapper, never()).selectByExample(any());
    }

    /**
     * Test getSaleUnit method when productItemId is negative
     * Should return empty list
     */
    @Test
    public void testGetSaleUnit_WhenProductItemIdIsNegative() {
        // arrange
        long productItemId = -1L;
        // act
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getSaleUnit(productItemId);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(Lists.newArrayList(), result);
        verify(mapper, never()).selectByExample(any());
    }

    /**
     * Test getSaleUnit method when productItemId is valid
     * Should return list of ProductItemSaleUnit
     */
    @Test
    public void testGetSaleUnit_WhenProductItemIdIsValid() {
        // arrange
        long productItemId = 1L;
        List<ProductItemSaleUnit> expectedUnits = createSampleProductItemSaleUnits(productItemId);
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(expectedUnits);
        // act
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getSaleUnit(productItemId);
        // assert
        assertNotNull(result);
        assertEquals(expectedUnits.size(), result.size());
        assertEquals(expectedUnits, result);
        // verify criteria
        ArgumentCaptor<ProductItemSaleUnitCriteria> criteriaCaptor = ArgumentCaptor.forClass(ProductItemSaleUnitCriteria.class);
        verify(mapper).selectByExample(criteriaCaptor.capture());
        ProductItemSaleUnitCriteria capturedCriteria = criteriaCaptor.getValue();
        assertEquals(1, capturedCriteria.getOredCriteria().size());
        assertEquals(productItemId, capturedCriteria.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
    }

    /**
     * Test getSaleUnit method when mapper returns empty list
     * Should return empty list
     */
    @Test
    public void testGetSaleUnit_WhenMapperReturnsEmptyList() {
        // arrange
        long productItemId = 1L;
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Lists.newArrayList());
        // act
        List<ProductItemSaleUnit> result = productItemSaleUnitDataService.getSaleUnit(productItemId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mapper).selectByExample(any(ProductItemSaleUnitCriteria.class));
    }

    /**
     * Tests createProductActionRequest method under exception conditions.
     */
    @Test
    public void testCreateProductActionRequestException() throws Throwable {
        try {
            // Arrange
            ProductItemBO productItemBO = null;
            List<ProductItemSaleUnit> saleUnits = null;
            ProductActionTypeEnum actionType = null;
            // Act
            invokePrivateMethod(productItemSaleUnitDataService, "createProductActionRequest", productItemBO, saleUnits, actionType);
            fail("Expected an InvocationTargetException wrapping a NullPointerException");
        } catch (InvocationTargetException e) {
            // Assert
            assertTrue("Expected cause to be NullPointerException", e.getCause() instanceof NullPointerException);
        }
    }

    /**
     * Tests createProductActionRequest method under normal conditions.
     */
    @Test
    public void testCreateProductActionRequestNormal() throws Throwable {
        // Arrange
        when(productItemBO.getBizId()).thenReturn("bizId");
        List<ProductItemSaleUnit> saleUnits = Arrays.asList(saleUnit);
        ProductActionTypeEnum actionType = ProductActionTypeEnum.ADD;
        // Act
        ProductActionRequest result = invokePrivateMethod(productItemSaleUnitDataService, "createProductActionRequest", productItemBO, saleUnits, actionType);
        // Assert
        assertNotNull(result);
        assertEquals("bizId", result.getOutBizId());
        assertEquals((Integer) actionType.getType(), result.getActionType());
    }

    /**
     * Tests createProductActionRequest method under boundary conditions.
     */
    @Test
    public void testCreateProductActionRequestBoundary() throws Throwable {
        // Arrange
        when(productItemBO.getBizId()).thenReturn(null);
        List<ProductItemSaleUnit> saleUnits = Arrays.asList(saleUnit);
        ProductActionTypeEnum actionType = ProductActionTypeEnum.ADD;
        // Act
        ProductActionRequest result = invokePrivateMethod(productItemSaleUnitDataService, "createProductActionRequest", productItemBO, saleUnits, actionType);
        // Assert
        assertNotNull(result);
        assertNull(result.getOutBizId());
        assertEquals((Integer) actionType.getType(), result.getActionType());
    }

    /**
     * 测试createProductRequestList方法，当输入为空列表时，应返回空列表
     */
    @Test
    public void testCreateProductRequestListWithEmptyList() throws Throwable {
        // arrange
        List<ProductItemSaleUnit> relateProductList = Arrays.asList();
        // act
        List<ProductRequest> result = invokePrivateMethod(relateProductList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试createProductRequestList方法，当输入包含一个ProductItemSaleUnit对象时，应返回一个包含一个ProductRequest对象的列表
     */
    @Test
    public void testCreateProductRequestListWithOneElement() throws Throwable {
        // arrange
        ProductItemSaleUnit saleUnit = new ProductItemSaleUnit();
        saleUnit.setProductId(1L);
        saleUnit.setProductType(1);
        List<ProductItemSaleUnit> relateProductList = Arrays.asList(saleUnit);
        // act
        List<ProductRequest> result = invokePrivateMethod(relateProductList);
        // assert
        assertEquals(1, result.size());
        assertEquals(saleUnit.getProductId(), result.get(0).getProductId());
        assertEquals(saleUnit.getProductType(), result.get(0).getProductType());
    }

    /**
     * 测试createProductRequestList方法，当输入包含多个ProductItemSaleUnit对象时，应返回一个包含相同数量ProductRequest对象的列表
     */
    @Test
    public void testCreateProductRequestListWithMultipleElements() throws Throwable {
        // arrange
        ProductItemSaleUnit saleUnit1 = new ProductItemSaleUnit();
        saleUnit1.setProductId(1L);
        saleUnit1.setProductType(1);
        ProductItemSaleUnit saleUnit2 = new ProductItemSaleUnit();
        saleUnit2.setProductId(2L);
        saleUnit2.setProductType(2);
        List<ProductItemSaleUnit> relateProductList = Arrays.asList(saleUnit1, saleUnit2);
        // act
        List<ProductRequest> result = invokePrivateMethod(relateProductList);
        // assert
        assertEquals(2, result.size());
        assertEquals(saleUnit1.getProductId(), result.get(0).getProductId());
        assertEquals(saleUnit1.getProductType(), result.get(0).getProductType());
        assertEquals(saleUnit2.getProductId(), result.get(1).getProductId());
        assertEquals(saleUnit2.getProductType(), result.get(1).getProductType());
    }

    @Test
    public void testSetProductItemSaleUnit_UpdateWithDifferentAmountAndStatus() throws Throwable {
        long productItemId = 1L;
        ProductItemSaleUnit existingUnit = new ProductItemSaleUnit();
        existingUnit.setId(10L);
        existingUnit.setProductItemId(productItemId);
        existingUnit.setProductType(1);
        existingUnit.setProductId(100L);
        existingUnit.setAmount(1);
        existingUnit.setStatus(DistributionStatusEnum.VALID.getCode());
        PluralProductBO newProduct = new PluralProductBO(1, 100L, 2);
        when(mapper.selectByExample(any())).thenReturn(Arrays.asList(existingUnit));
        try {
            productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, Arrays.asList(newProduct));
        } catch (ProductItemException e) {
            assertEquals("更新关联商品失败", e.getMessage());
        }
    }

    @Test
    public void testSetProductItemSaleUnit_RemoveItems() throws Throwable {
        long productItemId = 1L;
        ProductItemSaleUnit existingUnit = new ProductItemSaleUnit();
        existingUnit.setId(1L);
        existingUnit.setProductItemId(productItemId);
        existingUnit.setProductType(1);
        existingUnit.setProductId(100L);
        existingUnit.setStatus(DistributionStatusEnum.VALID.getCode());
        PluralProductBO newProduct = new PluralProductBO(1, 200L, 1);
        when(mapper.selectByExample(any())).thenReturn(Arrays.asList(existingUnit));
        try {
            productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, Arrays.asList(newProduct));
        } catch (ProductItemException e) {
            // Corrected the expected message to match the actual failure message
            assertEquals("新增关联商品失败", e.getMessage());
        }
    }

    @Test(expected = ProductItemException.class)
    public void testSetProductItemSaleUnit_UpdateFailed() throws Throwable {
        long productItemId = 1L;
        ProductItemSaleUnit existingUnit = new ProductItemSaleUnit();
        existingUnit.setProductItemId(1L);
        existingUnit.setProductType(1);
        existingUnit.setProductId(100L);
        existingUnit.setAmount(1);
        PluralProductBO newProduct = new PluralProductBO(1, 100L, 2);
        when(mapper.selectByExample(any())).thenReturn(Arrays.asList(existingUnit));
        productItemSaleUnitDataService.setProductItemSaleUnit(productItemId, Arrays.asList(newProduct));
    }

    @Test
    public void testExecuteDatabaseOperationsAllEmpty() throws Throwable {
        invokeExecuteDatabaseOperations(Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
        verify(mapper, never()).insert(any());
        verify(mapper, never()).updateByPrimaryKey(any());
        verify(mapper, never()).deleteByPrimaryKey(anyLong());
    }

    @Test(expected = ProductItemException.class)
    public void testExecuteDatabaseOperationsAddOnly() throws Throwable {
        invokeExecuteDatabaseOperations(Arrays.asList(createProductItemSaleUnit()), Collections.emptyList(), Collections.emptyList());
    }

    @Test(expected = ProductItemException.class)
    public void testExecuteOnly() throws Throwable {
        invokeExecuteDatabaseOperations(Collections.emptyList(), Arrays.asList(createProductItemSaleUnit()), Collections.emptyList());
    }

    @Test(expected = ProductItemException.class)
    public void testExecuteDatabaseOperationsUpdateAndRemove() throws Throwable {
        invokeExecuteDatabaseOperations(Collections.emptyList(), Arrays.asList(createProductItemSaleUnit()), Arrays.asList(createProductItemSaleUnit()));
    }

    @Test(expected = ProductItemException.class)
    public void testExecuteDatabaseOperationsAllNotEmpty() throws Throwable {
        invokeExecuteDatabaseOperations(Arrays.asList(createProductItemSaleUnit()), Arrays.asList(createProductItemSaleUnit()), Arrays.asList(createProductItemSaleUnit()));
    }

    @Test
    public void testUpdateItemSaleUnitIdNotNullAndGreaterThanZero() throws Throwable {
        Method updateItemSaleUnitMethod = ProductItemSaleUnitDataService.class.getDeclaredMethod("updateItemSaleUnit", ProductItemSaleUnit.class);
        updateItemSaleUnitMethod.setAccessible(true);
        ProductItemSaleUnit productItemSaleUnit = new ProductItemSaleUnit();
        productItemSaleUnit.setId(1L);
        when(mapper.updateByPrimaryKeySelective(any(ProductItemSaleUnit.class))).thenReturn(1);
        boolean result = (boolean) updateItemSaleUnitMethod.invoke(productItemSaleUnitDataService, productItemSaleUnit);
        assertTrue(result);
    }

    @Test
    public void testUpdateItemSaleUnitProductItemIdNotNullAndGreaterThanZero() throws Throwable {
        Method updateItemSaleUnitMethod = ProductItemSaleUnitDataService.class.getDeclaredMethod("updateItemSaleUnit", ProductItemSaleUnit.class);
        updateItemSaleUnitMethod.setAccessible(true);
        ProductItemSaleUnit productItemSaleUnit = new ProductItemSaleUnit();
        productItemSaleUnit.setProductItemId(1L);
        productItemSaleUnit.setProductType(1);
        // Ensure productId is set
        productItemSaleUnit.setProductId(1L);
        when(mapper.updateByExampleSelective(any(ProductItemSaleUnit.class), any(ProductItemSaleUnitCriteria.class))).thenReturn(1);
        boolean result = (boolean) updateItemSaleUnitMethod.invoke(productItemSaleUnitDataService, productItemSaleUnit);
        assertTrue(result);
    }

    @Test
    public void testUpdateItemSaleUnitProductItemIdNullOrProductTypeUnknown() throws Throwable {
        Method updateItemSaleUnitMethod = ProductItemSaleUnitDataService.class.getDeclaredMethod("updateItemSaleUnit", ProductItemSaleUnit.class);
        updateItemSaleUnitMethod.setAccessible(true);
        ProductItemSaleUnit productItemSaleUnit = new ProductItemSaleUnit();
        productItemSaleUnit.setProductItemId(0L);
        productItemSaleUnit.setProductType(0);
        productItemSaleUnit.setProductId(0L);
        boolean result = (boolean) updateItemSaleUnitMethod.invoke(productItemSaleUnitDataService, productItemSaleUnit);
        assertFalse(result);
    }

    @Test(expected = ProductItemException.class)
    public void testIsDeactivatedNewPoIsNull() throws Throwable {
        invokeIsDeactivated(null, formerMap);
    }

    @Test(expected = ProductItemException.class)
    public void testIsDeactivatedProductItemIdIsInvalid() throws Throwable {
        when(saleUnit.getProductItemId()).thenReturn(-1L);
        invokeIsDeactivated(saleUnit, formerMap);
    }

    @Test(expected = ProductItemException.class)
    public void testIsDeactivatedProductTypeIsUnknown() throws Throwable {
        invokeIsDeactivated(saleUnit, formerMap);
    }

    @Test(expected = ProductItemException.class)
    public void testIsDeactivatedProductIdIsInvalid() throws Throwable {
        invokeIsDeactivated(saleUnit, formerMap);
    }

    @Test
    public void testIsDeactivatedOldPoNotInFormerMap() throws Throwable {
        when(saleUnit.getProductItemId()).thenReturn(1L);
        when(saleUnit.getProductType()).thenReturn(1);
        when(saleUnit.getProductId()).thenReturn(1L);
        assertFalse(invokeIsDeactivated(saleUnit, formerMap));
    }

    @Test
    public void testIsDeactivatedOldPoStatusNotValid() throws Throwable {
        when(saleUnit.getProductItemId()).thenReturn(1L);
        when(saleUnit.getProductType()).thenReturn(1);
        when(saleUnit.getProductId()).thenReturn(1L);
        assertFalse(invokeIsDeactivated(saleUnit, formerMap));
    }

    @Test
    public void testIsDeactivatedNewPoStatusNotInvalid() throws Throwable {
        when(saleUnit.getProductItemId()).thenReturn(1L);
        when(saleUnit.getProductType()).thenReturn(1);
        when(saleUnit.getProductId()).thenReturn(1L);
        assertFalse(invokeIsDeactivated(saleUnit, formerMap));
    }

    @Test
    public void testIsDeactivatedAllConditionsMet() throws Throwable {
        when(saleUnit.getProductItemId()).thenReturn(1L);
        when(saleUnit.getProductType()).thenReturn(1);
        when(saleUnit.getProductId()).thenReturn(1L);
        when(saleUnit.getStatus()).thenReturn(DistributionStatusEnum.INVALID.getCode());
        ProductItemSaleUnit oldPo = mock(ProductItemSaleUnit.class);
        when(oldPo.getStatus()).thenReturn(DistributionStatusEnum.VALID.getCode());
        formerMap.put("1_1_1", oldPo);
        assertTrue(invokeIsDeactivated(saleUnit, formerMap));
    }

    @Test
    public void testBatchGetValidSaleUnitWithEmptyList() throws Throwable {
        List<Long> productItemIdList = Collections.emptyList();
        Map<Long, List<ProductItemSaleUnit>> result = productItemSaleUnitDataService.batchGetValidSaleUnit(productItemIdList);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBatchGetValidSaleUnitWithInvalidIds() throws Throwable {
        List<Long> productItemIdList = Arrays.asList(-1L, 0L, null);
        Map<Long, List<ProductItemSaleUnit>> result = productItemSaleUnitDataService.batchGetValidSaleUnit(productItemIdList);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBatchGetValidSaleUnitWithValidIdsButNoRecords() throws Throwable {
        List<Long> productItemIdList = Arrays.asList(1L, 2L);
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Collections.emptyList());
        Map<Long, List<ProductItemSaleUnit>> result = productItemSaleUnitDataService.batchGetValidSaleUnit(productItemIdList);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBatchGetValidSaleUnitWithValidIdsAndRecordsButInvalidStatus() throws Throwable {
        List<Long> productItemIdList = Arrays.asList(1L, 2L);
        ProductItemSaleUnit record = new ProductItemSaleUnit();
        record.setProductItemId(1L);
        // Assuming DistributionStatusEnum.INVALID.getCode() is an invalid status code
        record.setStatus(com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum.INVALID.getCode());
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Arrays.asList(record));
        Map<Long, List<ProductItemSaleUnit>> result = productItemSaleUnitDataService.batchGetValidSaleUnit(productItemIdList);
        // Adjust the assertion based on the expected behavior
        // If the method is expected to filter out invalid statuses, then the result should not be empty
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));
        assertEquals(1, result.get(1L).size());
    }

    @Test
    public void testBatchGetValidSaleUnitWithValidIdsAndRecordsAndValidStatus() throws Throwable {
        List<Long> productItemIdList = Arrays.asList(1L, 2L);
        ProductItemSaleUnit record = new ProductItemSaleUnit();
        record.setProductItemId(1L);
        // Assuming 1 is a valid status code
        record.setStatus(1);
        when(mapper.selectByExample(any(ProductItemSaleUnitCriteria.class))).thenReturn(Arrays.asList(record));
        Map<Long, List<ProductItemSaleUnit>> result = productItemSaleUnitDataService.batchGetValidSaleUnit(productItemIdList);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));
        assertEquals(1, result.get(1L).size());
    }

    @Test
    public void testSyncBatchAddItemSaleUnitEmptyList() throws Throwable {
        assertTrue(invokeSyncBatchAddItemSaleUnit(Collections.emptyList()));
    }

    @Test
    public void testSyncBatchAddItemSaleUnitSuccess() throws Throwable {
        ProductItemSaleUnit saleUnit = createSaleUnit();
        when(mapper.insertSelective(any(ProductItemSaleUnit.class))).thenReturn(1);
        assertTrue(invokeSyncBatchAddItemSaleUnit(Arrays.asList(saleUnit)));
    }

    @Test
    public void testSyncBatchAddItemSaleUnitFailure() throws Throwable {
        ProductItemSaleUnit saleUnit = createSaleUnit();
        when(mapper.insertSelective(any(ProductItemSaleUnit.class))).thenReturn(0);
        assertFalse(invokeSyncBatchAddItemSaleUnit(Arrays.asList(saleUnit)));
    }

    @Test
    public void testSyncBatchAddItemSaleUnitException() throws Throwable {
        ProductItemSaleUnit saleUnit = createSaleUnit();
        when(mapper.insertSelective(any(ProductItemSaleUnit.class))).thenThrow(new RuntimeException());
        try {
            invokeSyncBatchAddItemSaleUnit(Arrays.asList(saleUnit));
            fail("Expected ProductItemException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof ProductItemException);
        }
    }
}
