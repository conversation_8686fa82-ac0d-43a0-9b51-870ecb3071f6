package com.sankuai.carnation.distribution.product.service;

import com.dianping.beauty.ibot.service.BeautiBotUserService;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.audit.application.DistributionProductPushAuditServiceImpl;
import com.sankuai.carnation.distribution.distributor.dto.DistributorDTO;
import com.sankuai.carnation.distribution.distributor.dto.DistributorGroupDTO;
import com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl;
import com.sankuai.carnation.distribution.distributor.service.impl.DistributorQueryServiceImpl;
import com.sankuai.carnation.distribution.notify.repository.XMNotifyUtils;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.ProductDetailAclService;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductDetailBO;
import com.sankuai.carnation.distribution.product.bo.DistributionBatchImportBO;
import com.sankuai.carnation.distribution.product.bo.DistributionProductInfoDo;
import com.sankuai.carnation.distribution.product.bo.DistributionShareMaterialDo;
import com.sankuai.carnation.distribution.product.bo.FixCommissionStrategyDo;
import com.sankuai.carnation.distribution.product.constant.DistributionProductConstant;
import com.sankuai.carnation.distribution.product.dto.*;
import com.sankuai.carnation.distribution.product.enums.DistributionProductStatusEnum;
import com.sankuai.carnation.distribution.product.service.base.DistributionProductBaseService;
import com.sankuai.carnation.distribution.product.service.base.DistributionShareMaterialBaseService;
import com.sankuai.carnation.distribution.product.service.base.FixCommissionStrategyBaseService;
import com.sankuai.carnation.distribution.utils.ProductPicUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import java.math.BigDecimal;
import java.util.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DistributionProductServiceImplTest {
    @InjectMocks
    private DistributionProductServiceImpl service;

    @Mock private DistributionProductBaseService distributionProductBaseService;
    @Mock private FixCommissionStrategyBaseService fixCommissionStrategyBaseService;
    @Mock private DistributionShareMaterialBaseService shareMaterialBaseService;
    @Mock private ProductInfoService productInfoService;
    @Mock private ProductPriceService productPriceService;
    @Mock private DistributionProductPushAuditServiceImpl distributionProductPushAuditService;
    @Mock private XMNotifyUtils xmNotifyUtils;
    @Mock private DistributorQueryServiceImpl distributorQueryService;
    @Mock private DistributorGroupQueryServiceImpl distributorGroupQueryService;
    @Mock private ProductPicUtils productPicUtils;
    @Mock private ShortUrlService shortUrlService;
    @Mock private OrderSizeQueryService orderSizeQueryService;
    @Mock private RedisStoreClient redisStoreClient;
    @Mock private ProductDetailAclService productDetailAclService;
    @Mock private BeautiBotUserService beautiBotUserService;

    @Before
    public void setUp() { MockitoAnnotations.initMocks(this); }

    @Test
    public void testQueryList_success() {
        DistributionProductQueryListParamDto param = new DistributionProductQueryListParamDto();
        param.setStartNum(0); param.setSize(1); param.setPlatform(1); param.setUserId(1L); param.setCityId(1);
        DistributorDTO distributorDTO = new DistributorDTO(); distributorDTO.setDistributorGroupId(1);
        when(distributorQueryService.getDistributor(anyInt(), anyLong())).thenReturn(RemoteResponse.success(distributorDTO));
        DistributorGroupDTO groupDTO = new DistributorGroupDTO(); groupDTO.setChannelList(Collections.singletonList(1));
        when(distributorGroupQueryService.getDistributorGroup(anyInt())).thenReturn(RemoteResponse.success(groupDTO));
        DistributionProductInfoDo infoDo = new DistributionProductInfoDo();
        infoDo.setRelateProductId(100L); infoDo.setProductType(1); infoDo.setCommissionId(10L);
        when(distributionProductBaseService.queryProductList(anyInt(), anyList(), anyInt(), anyInt())).thenReturn(Collections.singletonList(infoDo));
        ProductPriceInfo priceInfo = new ProductPriceInfo(); priceInfo.setProductId(100L); priceInfo.setBasePrice(BigDecimal.TEN); priceInfo.setLinePrice(BigDecimal.TEN);
        List<ProductRequestParam> productRequestParams = new ArrayList<>();
        ProductRequestParam prp = new ProductRequestParam(); prp.setProductId(100L); prp.setProductType(1); productRequestParams.add(prp);
        when(productPriceService.getPriceList(anyInt(), anyList(), anyString(), anyInt())).thenReturn(Collections.singletonList(priceInfo));
        ProductBaseInfo baseInfo = new ProductBaseInfo(); baseInfo.setProductId(100L); baseInfo.setAvailable(true);
        when(productInfoService.queryProductInfoList(anyList(), anyInt(), anyInt())).thenReturn(Collections.singletonList(baseInfo));
        FixCommissionStrategyDo commissionStrategy = new FixCommissionStrategyDo(); commissionStrategy.setCommissionValue(1.0);
        Map<Long, FixCommissionStrategyDo> commissionMap = new HashMap<>(); commissionMap.put(10L, commissionStrategy);
        when(fixCommissionStrategyBaseService.queryStrategyIdMap(anyList())).thenReturn(commissionMap);
        RemoteResponse<DistributionProductListResponseInfo> resp = service.queryList(param);
        assertTrue(resp.isSuccess());
        assertNotNull(resp.getData());
    }

    @Test
    public void testQueryProduct_success() {
        DistributionProductQueryParamDto param = new DistributionProductQueryParamDto();
        param.setDistributionProductId(1L); param.setUserId(1L); param.setPlatform(1); param.setCityId(1);
        DistributorDTO distributorDTO = new DistributorDTO(); distributorDTO.setDistributorGroupId(1); distributorDTO.setDistributorId(1L); distributorDTO.setDistributorCode("code");
        when(distributorQueryService.getDistributor(anyInt(), anyLong())).thenReturn(RemoteResponse.success(distributorDTO));
        DistributorGroupDTO groupDTO = new DistributorGroupDTO(); groupDTO.setChannelList(Collections.singletonList(1));
        when(distributorGroupQueryService.getDistributorGroup(anyInt())).thenReturn(RemoteResponse.success(groupDTO));
        DistributionProductInfoDo infoDo = new DistributionProductInfoDo();
        infoDo.setId(1L); infoDo.setRelateProductId(100L); infoDo.setProductType(1); infoDo.setStatus(DistributionProductStatusEnum.ON_SALE.getCode());
        infoDo.setStartTime(new Date(System.currentTimeMillis() - 1000)); infoDo.setEndTime(new Date(System.currentTimeMillis() + 100000));
        infoDo.setChannel(1); infoDo.setCommissionId(10L); infoDo.setDistributionShareId(20L);
        when(distributionProductBaseService.queryProduct(anyLong())).thenReturn(infoDo);
        ProductPriceInfo priceInfo = new ProductPriceInfo(); priceInfo.setProductId(100L); priceInfo.setBasePrice(BigDecimal.TEN); priceInfo.setLinePrice(BigDecimal.TEN);
        when(productPriceService.getPrice(anyInt(), any(), anyString(), anyInt())).thenReturn(priceInfo);
        ProductBaseInfo baseInfo = new ProductBaseInfo(); baseInfo.setProductId(100L); baseInfo.setAvailable(true);
        when(productInfoService.queryProductInfo(any(), anyInt(), anyInt())).thenReturn(baseInfo);
        FixCommissionStrategyDo commissionStrategy = new FixCommissionStrategyDo(); commissionStrategy.setCommissionValue(1.0);
        when(fixCommissionStrategyBaseService.queryStrategy(anyLong())).thenReturn(commissionStrategy);
        DistributionShareMaterialDo shareMaterialDo = new DistributionShareMaterialDo();
        when(shareMaterialBaseService.queryMaterial(anyLong())).thenReturn(shareMaterialDo);
        RemoteResponse<DistributionProductResponseInfo> resp = service.queryProduct(param);
        assertTrue(resp.isSuccess());
        assertNotNull(resp.getData());
    }

    @Test
    public void testInsert_success() {
        DistributionProductRequestInfo req = new DistributionProductRequestInfo();
        req.setProductId(1L); req.setProductType(1); req.setChannel(1); req.setBasicHotValue(1); req.setCommissionValue(1.0); req.setShopCommissionValue(1.0);
        req.setStartTime("2024-01-01 00:00:00"); req.setEndTime("2099-01-01 00:00:00"); req.setCityId(1); req.setVersion("v1"); req.setClientType(1);
        ProductBaseInfo baseInfo = new ProductBaseInfo(); baseInfo.setTitle("t"); baseInfo.setPicUrl("p");
        when(productInfoService.queryProductInfo(any(), anyInt(), anyInt())).thenReturn(baseInfo);
        ProductPriceInfo priceInfo = new ProductPriceInfo(); priceInfo.setBasePrice(BigDecimal.TEN); priceInfo.setLinePrice(BigDecimal.TEN);
        when(productPriceService.getPrice(anyInt(), any(), anyString(), anyInt())).thenReturn(priceInfo);
        when(fixCommissionStrategyBaseService.insertStrategy(any())).thenReturn(true);
        when(shareMaterialBaseService.insertMaterial(any())).thenReturn(true);
        when(distributionProductBaseService.insertProduct(any())).thenReturn(true);
        when(redisStoreClient.set(any(), any())).thenReturn(true);
        doNothing().when(distributionProductPushAuditService).pushAudit(anyLong());
        RemoteResponse<Boolean> resp = service.insert(req);
        assertTrue(resp.isSuccess());
        assertTrue(resp.getData());
    }

    @Test
    public void testImportByJson_blank() {
        RemoteResponse<Integer> resp = service.importByJson("", new ArrayList<>());
        assertFalse(resp.isSuccess());
    }

    @Test
    public void testDiscriminateProduct_success() {
        when(redisStoreClient.exists(any())).thenReturn(true);
        DistributionProductInfoDo infoDo = new DistributionProductInfoDo(); infoDo.setId(1L);
        when(distributionProductBaseService.queryByRelateProductId(anyLong(), anyInt(), anyInt())).thenReturn(infoDo);
        ProductDiscriminateResult result = service.discriminateProduct(1L, 1, 1);
        assertNotNull(result);
        assertEquals(1L, result.getDistributionProductId());
    }

    @Test
    public void testGetProductMaterialInfo_success() {
        DistributionProductInfoDo infoDo = new DistributionProductInfoDo();
        infoDo.setDistributionShareId(1L); infoDo.setProductPicList("[\"p1\"]"); infoDo.setProductName("n");
        when(distributionProductBaseService.queryProduct(anyLong())).thenReturn(infoDo);
        DistributionShareMaterialDo shareMaterialDo = new DistributionShareMaterialDo();
        shareMaterialDo.setShareContext("ctx"); shareMaterialDo.setSharePicList("[\"s1\"]"); shareMaterialDo.setSharePicWishQrCode("qr");
        when(shareMaterialBaseService.queryMaterial(anyLong())).thenReturn(shareMaterialDo);
        ProductMaterialInfo result = service.getProductMaterialInfo(1L);
        assertNotNull(result);
        assertEquals("n", result.getProductName());
        assertEquals("ctx", result.getShareContext());
    }

    @Test
    public void testProductMaterialAuditResultUpdate_auditReject() {
        DistributionProductInfoDo infoDo = new DistributionProductInfoDo();
        infoDo.setRelateProductId(1L); infoDo.setProductType(1); infoDo.setId(1L);
        when(distributionProductBaseService.queryProduct(anyLong())).thenReturn(infoDo);
        when(distributionProductBaseService.updateProductById(any())).thenReturn(true);
        service.productMaterialAuditResultUpdate(1L, false);
        verify(distributionProductBaseService, atLeastOnce()).updateProductById(any());
    }

    @Test
    public void testQueryProductListByIds_success() {
        DistributionProductInfoDo infoDo = new DistributionProductInfoDo();
        infoDo.setId(1L); infoDo.setProductName("n"); infoDo.setProductPicList("[\"p1\"]");
        when(distributionProductBaseService.queryProductListByIds(anyList())).thenReturn(Collections.singletonList(infoDo));
        RemoteResponse<Map<Long, DistributionProductResponseInfo>> resp = service.queryProductListByIds(Collections.singletonList(1L));
        assertTrue(resp.isSuccess());
        assertNotNull(resp.getData());
        assertTrue(resp.getData().containsKey(1L));
    }
} 