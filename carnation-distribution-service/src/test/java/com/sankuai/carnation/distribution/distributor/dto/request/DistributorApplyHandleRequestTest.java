package com.sankuai.carnation.distribution.distributor.dto.request;

import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

public class DistributorApplyHandleRequestTest {

    private DistributorApplyHandleRequest request;

    @Before
    public void setUp() throws Exception {
        request = new DistributorApplyHandleRequest();
    }

    /**
     * 测试 wxToken 属性设置和获取
     */
    @Test
    public void testWxTokenSetAndGet() {
        // arrange
        String expected = "testToken";
        request.setWxToken(expected);

        // act
        String actual = request.getWxToken();

        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 anchorId 属性设置和获取
     */
    @Test
    public void testAnchorIdSetAndGet() {
        // arrange
        Long expected = 12345L;
        request.setAnchorId(expected);

        // act
        Long actual = request.getAnchorId();

        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 operationType 属性设置和获取
     */
    @Test
    public void testOperationTypeSetAndGet() {
        // arrange
        Integer expected = 1;
        request.operationType = expected;

        // act
        Integer actual = request.operationType;

        // assert
        assertEquals(expected, actual);
    }

    /**
     * 测试 distributorId 属性设置和获取
     */
    @Test
    public void testDistributorIdSetAndGet() {
        // arrange
        Long expected = 67890L;
        request.setDistributorId(expected);

        // act
        Long actual = request.getDistributorId();

        // assert
        assertEquals(expected, actual);
    }
}
