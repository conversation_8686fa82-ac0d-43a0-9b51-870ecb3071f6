package com.sankuai.carnation.distribution.privatelive.distribution.application.service.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributor.appication.DistributorAppService;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.privatelive.distribution.acl.DistributionPlanQueryServiceAcl;
import com.sankuai.carnation.distribution.privatelive.distribution.acl.PrivateLiveDistributionPlan;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveDistributorTaskCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.service.PrivateLiveTaskDomainService;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveDistributorTaskRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.PrivateLiveTaskRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import java.util.Arrays;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.util.ObjectUtils;

public class PrivateLiveRoleTaskAppServiceImplTest {

    @InjectMocks
    private PrivateLiveRoleTaskAppServiceImpl service;

    @Mock
    private DistributorAppService distributorAppService;

    @Mock
    private DistributionPlanQueryServiceAcl distributionPlanQueryServiceAcl;

    @Mock
    private PrivateLiveTaskDomainService privateLiveTaskDomainService;

    @InjectMocks
    private PrivateLiveRoleTaskAppServiceImpl privateLiveRoleTaskAppServiceImpl;

    @Mock
    private DistributionPlanDoService distributionPlanDoService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：分销员组为空时返回空列表
     */
    @Test
    public void testJoinDistributorGroupTask_DistributorListEmpty() {
        PrivateLiveDistributorTaskRequest request = new PrivateLiveDistributorTaskRequest(null, 1L, "liveId", 1L);
        when(distributorAppService.queryAllByDistributorGroupId(anyLong())).thenReturn(Lists.newArrayList());
        List<PrivateLiveTaskBO> result = service.joinDistributorGroupTask(request);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：分销计划ID非空但分销计划不存在时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testJoinDistributorGroupTask_PlanIdNotNullButPlanNotFound() {
        PrivateLiveDistributorTaskRequest request = new PrivateLiveDistributorTaskRequest(1L, 1L, "liveId", 1L);
        when(distributorAppService.queryAllByDistributorGroupId(anyLong())).thenReturn(Arrays.asList(new DistributorBindModel()));
        when(distributionPlanQueryServiceAcl.queryDistributionPlan(anyLong())).thenReturn(null);
        service.joinDistributorGroupTask(request);
    }

    /**
     * 测试场景：正常情况下，分销员加入直播任务
     */
    @Test
    public void testJoinDistributorGroupTask_Normal() {
        PrivateLiveDistributorTaskRequest request = new PrivateLiveDistributorTaskRequest(null, 1L, "liveId", 1L);
        DistributorBindModel bindModel = new DistributorBindModel();
        bindModel.setStatus(DistributionApproveStatusEnum.PASS);
        bindModel.setDistributorModel(new DistributorModel());
        when(distributorAppService.queryAllByDistributorGroupId(anyLong())).thenReturn(Arrays.asList(bindModel));
        when(privateLiveTaskDomainService.batchJoinPrivateLiveTask(any(PrivateLiveDistributorTaskCmd.class))).thenReturn(Lists.newArrayList(new PrivateLiveTaskBO()));
        List<PrivateLiveTaskBO> result = service.joinDistributorGroupTask(request);
        assertFalse(result.isEmpty());
    }

    /**
     * 测试场景：分销计划ID非空且分销计划存在时，正确设置直播ID和主播ID
     */
    @Test
    public void testJoinDistributorGroupTask_PlanIdNotNullAndPlanFound() {
        PrivateLiveDistributorTaskRequest request = new PrivateLiveDistributorTaskRequest(1L, 1L, "liveId", 1L);
        DistributorBindModel bindModel = new DistributorBindModel();
        bindModel.setStatus(DistributionApproveStatusEnum.PASS);
        bindModel.setDistributorModel(new DistributorModel());
        PrivateLiveDistributionPlan plan = new PrivateLiveDistributionPlan("newLiveId", 2L);
        when(distributorAppService.queryAllByDistributorGroupId(anyLong())).thenReturn(Arrays.asList(bindModel));
        when(distributionPlanQueryServiceAcl.queryDistributionPlan(anyLong())).thenReturn(plan);
        when(privateLiveTaskDomainService.batchJoinPrivateLiveTask(any(PrivateLiveDistributorTaskCmd.class))).thenReturn(Lists.newArrayList(new PrivateLiveTaskBO()));
        List<PrivateLiveTaskBO> result = service.joinDistributorGroupTask(request);
        assertFalse(result.isEmpty());
        verify(distributionPlanQueryServiceAcl, times(1)).queryDistributionPlan(anyLong());
    }

    /**
     * 测试joinPrivateLiveTask方法，当请求参数为null时的场景
     */
    @Test(expected = IllegalArgumentException.class)
    public void testJoinPrivateLiveTaskRequestIsNull() throws Throwable {
        // arrange
        PrivateLiveTaskRequest request = null;
        // act
        privateLiveRoleTaskAppServiceImpl.joinPrivateLiveTask(request);
        // assert
        // 期望抛出IllegalArgumentException异常
    }

    /**
     * 测试joinPrivateLiveTask方法，正常情况下的场景
     */
    @Test
    public void testJoinPrivateLiveTaskNormal() throws Throwable {
        // arrange
        PrivateLiveTaskRequest request = new PrivateLiveTaskRequest();
        PrivateLiveTaskBO expected = new PrivateLiveTaskBO();
//        when(privateLiveTaskDomainService.createTask(any(PrivateLiveTaskRequest.class))).thenReturn(expected);
        // act
        PrivateLiveTaskBO result = privateLiveRoleTaskAppServiceImpl.joinPrivateLiveTask(request);
        // assert
//        verify(privateLiveTaskDomainService, times(1)).createTask(any(PrivateLiveTaskRequest.class));
//        assertSame("返回的对象与预期不符", expected, result);
    }
}
