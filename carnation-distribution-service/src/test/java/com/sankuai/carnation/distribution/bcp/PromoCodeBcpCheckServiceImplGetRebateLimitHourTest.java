package com.sankuai.carnation.distribution.bcp;

import com.sankuai.carnation.distribution.bcp.PromoCodeBcpCheckServiceImpl;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.promocode.rebate.domain.TechPromoCodeRebateDomainService;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import com.dianping.lion.Environment;
import com.sankuai.carnation.distribution.commission.enums.RebateValidTimeEnum;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplGetRebateLimitHourTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private ShopAcl shopAcl;

    @Mock
    private TechPromoCodeRebateDomainService techPromoCodeRebateDomainService;

    private CommissionVerifyCalculationRequest request;

    private OrderInfoBO orderInfo;

    @Before
    public void setUp() {
        request = new CommissionVerifyCalculationRequest();
        orderInfo = new OrderInfoBO();
    }

    @Test
    public void testGetRebateLimitHour_ShopIdNotNullAndNotZero_WithCategoryId() throws Throwable {
        orderInfo.setShopId(1L);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO.setCategoryId(1);
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(dpPoiBackCategoryDTO));
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Arrays.asList(dpPoiDTO));
        int result = promoCodeBcpCheckService.getRebateLimitHour(request, orderInfo);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_ShopIdNotNullAndNotZero_WithoutCategoryId() throws Throwable {
        orderInfo.setShopId(1L);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Collections.emptyList());
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Arrays.asList(dpPoiDTO));
        int result = promoCodeBcpCheckService.getRebateLimitHour(request, orderInfo);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_ShopIdNullOrZero_WithoutDpPoiDTO() throws Throwable {
        orderInfo.setShopId(0L);
        int result = promoCodeBcpCheckService.getRebateLimitHour(request, orderInfo);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_ShopIdNullOrZero_WithCategoryId() throws Throwable {
        orderInfo.setShopId(0L);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO.setCategoryId(1);
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(dpPoiBackCategoryDTO));
        int result = promoCodeBcpCheckService.getRebateLimitHour(request, orderInfo);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_ShopIdNullOrZero_WithoutCategoryId() throws Throwable {
        orderInfo.setShopId(0L);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Collections.emptyList());
        int result = promoCodeBcpCheckService.getRebateLimitHour(request, orderInfo);
        assertEquals(24, result);
    }
}
