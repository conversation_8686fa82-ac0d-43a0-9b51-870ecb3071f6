package com.sankuai.carnation.distribution.privatelive.consultant.utils;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantApplicantDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.BizException;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.WeChatUserInvalidException;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sankuai.carnation.distribution.privatelive.consultant.service.impl.WeChatUserServiceImpl.UNLOGIN_CODE;
import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UserAuthorizeUtilTest {

    @InjectMocks
    private UserAuthorizeUtil userAuthorizeUtil;

    @Mock
    private WeChatUserService weChatUserService;

    @Mock
    private PrivateLiveConsultantAccountRepository accountRepository;

    @Mock
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Mock
    private DistributorGroupAppService distributorGroupAppService;

    private final String wxToken = "validToken";

    private final Long distributorGroupId = 1L;

    private final String unionId = "validUnionId";

    private WeChatLoginInfo weChatLoginInfo = new WeChatLoginInfo("openId", "sessionKey", "unionId", "wxToken");

    private PrivateLiveConsultantAccount account = PrivateLiveConsultantAccount.builder().id(1L).build();

    private DistributorGroupModel distributorGroupModel = new DistributorGroupModel();

    private final Long accountId = 1L;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试wxToken为空时抛出IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testAuthenticationWxTokenIsNull() {
        userAuthorizeUtil.authentication(3625L, null);
    }

    /**
     * 测试taskId为空时抛出IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testAuthenticationTaskIdIsNull() {
        userAuthorizeUtil.authentication(null, "22222");
    }

    /**
     * 测试微信用户未登录时抛出WeChatUserInvalidException
     */
    @Test(expected = WeChatUserInvalidException.class)
    public void testAuthenticationWeChatUserNotLoggedIn() {
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom().setCode(UNLOGIN_CODE).setMsg("用户未登录").setData(null).build());
        userAuthorizeUtil.authentication(3625L, "22222");
    }

    /**
     * 测试校验并获取咨询师信息失败时抛出IllegalArgumentException
     */
    @Test(expected = IllegalArgumentException.class)
    public void testAuthenticationExceptionOccurs() {
        WeChatLoginInfo weChatLoginInfo = new WeChatLoginInfo();
        weChatLoginInfo.setUnionId("unionId");
        weChatLoginInfo.setOpenId("openId");
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.success(weChatLoginInfo));
        when(accountRepository.loadByUnionId(anyString())).thenThrow(new RuntimeException("数据库异常"));
        userAuthorizeUtil.authentication(3625L, "22222");
    }

    @Test
    public void testAccountDTOAndTaskDTOToPrivateLiveConsultantApplicantDTO() {
        PrivateLiveConsultantAccount accountDTO = PrivateLiveConsultantAccount.builder().id(1L).build();
        PrivateLiveConsultantTask taskDTO = PrivateLiveConsultantTask.builder().build();
        PrivateLiveConsultantApplicantDTO applicantDTO = userAuthorizeUtil.accountDTOAndTaskDTOToPrivateLiveConsultantApplicantDTO(accountDTO, taskDTO, null, null, null, null, null);
        Assert.assertNotNull(applicantDTO);
    }

    /**
     * 测试wxToken为空时抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetDistributorGroupByWxToken_WithEmptyWxToken() {
        userAuthorizeUtil.getDistributorGroupByWxToken("");
    }

    /**
     * 测试用户未登录时抛出WeChatUserInvalidException异常
     */
    @Test(expected = WeChatUserInvalidException.class)
    public void testGetDistributorGroupByWxToken_WithUserNotLoggedIn() {
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom().setCode(300).build());
        userAuthorizeUtil.getDistributorGroupByWxToken("validWxToken");
    }

    /**
     * 测试用户信息未查询到时抛出BizException异常
     */
    @Test(expected = BizException.class)
    public void testGetDistributorGroupByWxToken_WithUserInfoNotFound() {
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(null);
        userAuthorizeUtil.getDistributorGroupByWxToken("validWxToken");
    }

    /**
     * 测试用户未注册咨询师时抛出ConsultantUserInvalidException异常
     */
    @Test(expected = WeChatUserInvalidException.class)
    public void testGetDistributorGroupByWxToken_WithUserNotRegistered() {
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom().setCode(300).build());
        when(accountRepository.loadByUnionId(anyString())).thenReturn(null);
        userAuthorizeUtil.getDistributorGroupByWxToken("validWxToken");
    }
//
//    /**
//     * 测试正常情况下能够获取到DistributorGroupModel
//     */
//    @Test(expected = WeChatUserInvalidException.class)
//    public void testGetDistributorGroupByWxToken_WithValidWxToken() {
//        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.<WeChatLoginInfo>custom().setCode(200).build());
//        when(accountRepository.loadByUnionId(anyString())).thenReturn(new PrivateLiveConsultantAccount());
//        when(distributorGroupAppService.getDistributorGroupByAccountId(anyLong())).thenReturn(new DistributorGroupModel());
//        DistributorGroupModel result = userAuthorizeUtil.getDistributorGroupByWxToken("validWxToken");
//        assertNotNull(result);
//    }

//    @Test
//    public void test1() {
//        UserAuthorizeUtil userAuthorizeUtil = spy(new UserAuthorizeUtil());
//
//        // Mock内部方法authorizeWeChatInfo
//        doReturn(weChatLoginInfo).when(userAuthorizeUtil).authorizeWeChatInfo(anyString());
//
//        // Mock其他依赖
//        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenReturn(RemoteResponse.success(weChatLoginInfo));
//
//        when(accountRepository.loadByUnionId(anyString())).thenReturn(account);
//        DistributorGroupModel distributorGroupByWxToken = userAuthorizeUtil.getDistributorGroupByWxToken("22222");
//        assertNotNull(distributorGroupByWxToken);
//    }

    /**
     * 测试wxToken有效，但用户未登录时抛出WeChatUserInvalidException异常
     */
    @Test(expected = WeChatUserInvalidException.class)
    public void testGetDistributorGroupByWxToken_WithUnloggedInUser() {
        when(weChatUserService.getWeChatUserInfoByToken(anyString())).thenThrow(new WeChatUserInvalidException("用户未登录"));
        userAuthorizeUtil.getDistributorGroupByWxToken(wxToken);
    }

    /**
     * 测试wxToken有效，用户登录但未注册咨询师时抛出BizSceneException异常
     */
    @Test(expected = BizException.class)
    public void testGetDistributorGroupByWxToken_WithUnregisteredConsultant() {
        when(accountRepository.loadByUnionId(anyString())).thenReturn(null);
        userAuthorizeUtil.getDistributorGroupByWxToken(wxToken);
    }

    /**
     * 测试wxToken有效，用户登录且注册咨询师但未注册团长时抛出BizSceneException异常
     */
    @Test(expected = BizException.class)
    public void testGetDistributorGroupByWxToken_WithUnregisteredDistributorGroup() {
        when(distributorGroupAppService.getDistributorGroupByAccountId(accountId)).thenReturn(null);
        userAuthorizeUtil.getDistributorGroupByWxToken(wxToken);
    }

    /**
     * 测试wxToken有效，用户登录且注册咨询师且注册团长时正常返回DistributorGroupModel
     */
    @Test(expected = BizException.class)
    public void testGetDistributorGroupByWxToken_WithValidToken() {
        DistributorGroupModel result = userAuthorizeUtil.getDistributorGroupByWxToken(wxToken);
        assert result.getAccountId().equals(accountId);
    }
}
