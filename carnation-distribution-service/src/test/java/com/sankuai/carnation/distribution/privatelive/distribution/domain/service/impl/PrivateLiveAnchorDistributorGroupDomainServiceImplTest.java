package com.sankuai.carnation.distribution.privatelive.distribution.domain.service.impl;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorChannelRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindResponse;
import com.sankuai.carnation.distribution.distributor.repository.DistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.account.domain.service.PrivateLiveWechatAccountDomainService;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.application.service.PrivateLiveDistributionQrCodeAppService;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveAnchorDistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveBecomeDistributorGroupCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveJoinDistributorGroupCmd;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveAnchorDistributorGroupRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.request.DistributorGroupPassAuditRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.request.DistributorGroupRejectAuditRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import com.sankuai.carnation.distribution.utils.MobileTokenUtil;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveAnchorDistributorGroupDomainServiceImplTest {

    @Mock
    private RpcUserRetrieveService.Iface userRetrieveService;

    @Mock
    private MobileTokenUtil mobileTokenUtil;

    @Mock
    private DistributorGroupRepository distributorGroupRepository;

    @Mock
    private PrivateLiveAnchorDistributorGroupRepository privateLiveAnchorDistributorGroupRepository;

    @Mock
    private PrivateLiveWechatAccountDomainService privateLiveWechatAccountDomainService;

    @Mock
    private PrivateLiveDistributionQrCodeAppService privateLiveDistributionQrCodeAppService;

    @Mock
    private DistributorGroupAppService distributorGroupAppService;
    @Mock
    private DistributorChannelRootService distributorChannelRootService;
    @Mock
    private DistributorGroupRootService distributorGroupRootService;
    @InjectMocks
    private PrivateLiveAnchorDistributorGroupDomainServiceImpl privateLiveAnchorDistributorGroupDomainService;

    private PrivateLiveJoinDistributorGroupCmd privateLiveJoinDistributorGroupCmd;

    private PrivateLiveAccountBO accountBO;

    private PrivateLiveBecomeDistributorGroupCmd privateLiveBecomeDistributorGroupCmd = new PrivateLiveBecomeDistributorGroupCmd();

    private DistributorGroupBO distributorGroupBO = new DistributorGroupBO();

    private DistributorGroupPassAuditRequest request = new DistributorGroupPassAuditRequest();

    @Before
    public void setUp() {
        privateLiveJoinDistributorGroupCmd = PrivateLiveJoinDistributorGroupCmd.builder().anchorId(1L).distributorGroupId(1L).unionId("unionId").openId("openId").build();
        accountBO = PrivateLiveAccountBO.builder().id(1L).unionId("unionId").openId("openId").build();
    }

    /**
     * 测试joinDistributorGroup方法，当需要注册账户时
     */
    @Test
    public void testJoinDistributorGroupRequireRegisterAccount() {
        // arrange
        when(privateLiveWechatAccountDomainService.registerWechatAccount(any(PrivateLiveAccountCmd.class))).thenReturn(accountBO);
        // act
        DistributorBindResponse response = privateLiveAnchorDistributorGroupDomainService.joinDistributorGroup(privateLiveJoinDistributorGroupCmd);
        // assert
        Mockito.verify(privateLiveWechatAccountDomainService).registerWechatAccount(any(PrivateLiveAccountCmd.class));
    }

    /**
     * 测试joinDistributorGroup方法，当不需要注册账户时
     */
    @Test
    public void testJoinDistributorGroupNotRequireRegisterAccount() {
        // arrange
        // 设置已有账户，模拟不需要注册账户的情况
        privateLiveJoinDistributorGroupCmd.setPrivateLiveWechatAccount(accountBO);
        // act
        DistributorBindResponse response = privateLiveAnchorDistributorGroupDomainService.joinDistributorGroup(privateLiveJoinDistributorGroupCmd);
        // assert
        Mockito.verify(privateLiveWechatAccountDomainService, Mockito.never()).registerWechatAccount(any(PrivateLiveAccountCmd.class));
    }




    /**
     * 测试 passAudit 方法，当查询到的团长申请信息为空时，应抛出 BizSceneException 异常
     */
    @Test(expected = BizSceneException.class)
    public void testPassAuditWhenAnchorDistributorGroupIsNull() {
        // arrange
        // act
        privateLiveAnchorDistributorGroupDomainService.passAudit(request);
        // assert
        // Expected exception
    }



    /**
     * 测试 passAudit 方法，当传入的 request 中 distributorGroupId 为 null 时，应抛出 BizSceneException 异常
     */
    @Test(expected = BizSceneException.class)
    public void testPassAuditWhenDistributorGroupIdIsNull() {
        // arrange
        request.setDistributorGroupId(null);
        // act
        privateLiveAnchorDistributorGroupDomainService.passAudit(request);
        // assert
        // Expected exception
    }

    /**
     * 测试 passAudit 方法，当传入的 request 中 anchorId 为 null 时，应抛出 BizSceneException 异常
     */
    @Test(expected = BizSceneException.class)
    public void testPassAuditWhenAnchorIdIsNull() {
        // arrange
        request.setAnchorId(null);
        // act
        privateLiveAnchorDistributorGroupDomainService.passAudit(request);
        // assert
        // Expected exception
    }

    /**
     * 测试 passAudit 方法，当数据库操作异常时，应抛出相应的异常
     */
    @Test(expected = RuntimeException.class)
    public void testPassAuditWhenDatabaseOperationThrowsException() {
        // arrange
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorIdAndDistributorGroupId(anyLong(), anyLong())).thenThrow(new RuntimeException());
        // act
        privateLiveAnchorDistributorGroupDomainService.passAudit(request);
        // assert
        // Expected exception
    }

    /**
     * 测试rejectAudit方法，当查询到的团长申请信息为空时，应抛出BizSceneException异常
     */
    @Test(expected = BizSceneException.class)
    public void testRejectAuditWhenApplicationInfoIsNull() {
        // arrange
        DistributorGroupRejectAuditRequest request = new DistributorGroupRejectAuditRequest(1L, 1L);
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorIdAndDistributorGroupId(any(Long.class), any(Long.class))).thenReturn(null);
        // act
        privateLiveAnchorDistributorGroupDomainService.rejectAudit(request);
        // assert
        // BizSceneException expected
    }

    /**
     * 测试rejectAudit方法，当查询到的团长申请信息不为空时，应正常处理并返回结果
     */
    @Test
    public void testRejectAuditWhenApplicationInfoIsNotNull() {
        // arrange
        DistributorGroupRejectAuditRequest request = new DistributorGroupRejectAuditRequest(1L, 1L);
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO(1L, new DistributorGroupBO(), DistributionApproveStatusEnum.WAITING, "审核中");
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorIdAndDistributorGroupId(any(Long.class), any(Long.class))).thenReturn(mockBO);
        when(privateLiveAnchorDistributorGroupRepository.save(any())).thenReturn(mockBO);
        // act
        PrivateLiveAnchorDistributorGroupBO result = privateLiveAnchorDistributorGroupDomainService.rejectAudit(request);
        // assert
        assertNotNull(result);
        assertEquals(DistributionApproveStatusEnum.REJECT, result.getStatus());
    }

    /**
     * 测试rejectAudit方法，当查询到的团长申请信息不为空，且审核状态更新成功时
     */
    @Test
    public void testRejectAuditWhenApplicationInfoIsNotNullAndAuditUpdateSuccess() {
        // arrange
        DistributorGroupRejectAuditRequest request = new DistributorGroupRejectAuditRequest(1L, 1L);
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO(1L, new DistributorGroupBO(), DistributionApproveStatusEnum.WAITING, "审核中");
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorIdAndDistributorGroupId(any(Long.class), any(Long.class))).thenReturn(mockBO);
        when(privateLiveAnchorDistributorGroupRepository.save(any())).thenReturn(mockBO);
        // act
        PrivateLiveAnchorDistributorGroupBO result = privateLiveAnchorDistributorGroupDomainService.rejectAudit(request);
        // assert
        assertNotNull(result);
        assertEquals(DistributionApproveStatusEnum.REJECT, result.getStatus());
    }

    /**
     * 测试rejectAudit方法，当查询到的团长申请信息不为空，但审核状态更新失败时
     */
    @Test(expected = Exception.class)
    public void testRejectAuditWhenApplicationInfoIsNotNullButAuditUpdateFail() {
        // arrange
        DistributorGroupRejectAuditRequest request = new DistributorGroupRejectAuditRequest(1L, 1L);
        PrivateLiveAnchorDistributorGroupBO mockBO = new PrivateLiveAnchorDistributorGroupBO(1L, new DistributorGroupBO(), DistributionApproveStatusEnum.WAITING, "审核中");
        when(privateLiveAnchorDistributorGroupRepository.queryByAnchorIdAndDistributorGroupId(any(Long.class), any(Long.class))).thenReturn(mockBO);
        when(privateLiveAnchorDistributorGroupRepository.save(any())).thenThrow(new RuntimeException("数据库异常"));
        // act
        privateLiveAnchorDistributorGroupDomainService.rejectAudit(request);
        // assert
        // RuntimeException expected
    }


    /**
     * 测试成为分销团长，不需要注册账户，且之前已经是分销团长且状态为等待审核
     */
    @Test
    public void testBecomeDistributorGroup_NotRequireRegisterAccountAndExistingGroupWaiting() {
        // arrange
        accountBO = new PrivateLiveAccountBO();
        accountBO.setId(null);
        privateLiveBecomeDistributorGroupCmd = PrivateLiveBecomeDistributorGroupCmd.builder()
                .permitReRegister(false)
                .privateLiveWechatAccount(accountBO)
                .anchorId(1L)
                .build();
        PrivateLiveAnchorDistributorGroupBO existingGroup = PrivateLiveAnchorDistributorGroupBO.builder().anchorId(1L).status(DistributionApproveStatusEnum.WAITING).build();
        when(privateLiveAnchorDistributorGroupRepository.queryByDistributorGroupId(anyLong(), anyList())).thenReturn(existingGroup);
        List<DistributionChannelBO> distributionChannelBOList = Lists.newArrayList();
        distributionChannelBOList.add(DistributionChannelBO.builder().channelId(123).build());
        when(distributorChannelRootService.getChannelByChannelCode(anyString())).thenReturn(distributionChannelBOList);
        when(privateLiveWechatAccountDomainService.registerWechatAccount(any(PrivateLiveAccountCmd.class))).thenReturn(PrivateLiveAccountBO.builder().build());
//        when(privateLiveAnchorDistributorGroupCmd.getPrivateLiveWechatAccount())
        when(distributorGroupRepository.queryGroupAttrs(anyInt(), anyList())).thenReturn(Lists.newArrayList());
        try {
            when(mobileTokenUtil.getMobileToken(any())).thenReturn("token");
            UserModel userModel = new UserModel();
            userModel.setId(123L);
            UserRespMsg userRespMsg = new UserRespMsg();
            userRespMsg.setSuccess(true);
            userRespMsg.setUser(userModel);
            when(userRetrieveService.getUserByMobileWithMsg(any(), any(UserFields.class))).thenReturn(userRespMsg);
            when(distributorGroupRootService.setDistributorGroup(distributorGroupBO)).thenReturn(1);
        } catch (TException e) {
            throw new RuntimeException(e);
        }
        // act
        PrivateLiveAnchorDistributorGroupBO result = privateLiveAnchorDistributorGroupDomainService.becomeDistributorGroup(privateLiveBecomeDistributorGroupCmd);
        // assert
//        verify(privateLiveWechatAccountDomainService, never()).registerWechatAccount(any(PrivateLiveAccountCmd.class));
        verify(privateLiveAnchorDistributorGroupRepository, times(1)).queryByDistributorGroupId(anyLong(), anyList());
        verify(privateLiveDistributionQrCodeAppService, never()).getOrCreateRegisterDistributorGroupQrCode(anyLong());
        assertNotNull(result);
        assertEquals(DistributionApproveStatusEnum.WAITING, result.getStatus());
    }

    /**
     * 测试成为分销团长，不需要注册账户，且之前已经是分销团长但状态为审核通过
     */
    @Test(expected = BizSceneException.class)
    public void testBecomeDistributorGroup_NotRequireRegisterAccountAndExistingGroupPassed() {
        // arrange
        privateLiveBecomeDistributorGroupCmd = PrivateLiveBecomeDistributorGroupCmd.builder().permitReRegister(false).privateLiveWechatAccount(accountBO).build();
        PrivateLiveAnchorDistributorGroupBO existingGroup = PrivateLiveAnchorDistributorGroupBO.builder().anchorId(1L).status(DistributionApproveStatusEnum.PASS).build();

        // act
        privateLiveAnchorDistributorGroupDomainService.becomeDistributorGroup(privateLiveBecomeDistributorGroupCmd);
        // assert is handled by the expected exception
    }
}
