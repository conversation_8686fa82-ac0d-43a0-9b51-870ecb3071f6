package com.sankuai.carnation.distribution.empty.code.bind.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.empty.code.bind.repository.service.EmptyCodeBindInfoDataService;
import com.sankuai.carnation.distribution.empty.code.prebind.dto.EmptyCodeConfigInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class EmptyCodeGenerateServiceImplTest {

    @InjectMocks
    private EmptyCodeGenerateServiceImpl emptyCodeGenerateService;

    @Mock
    private EmptyCodeBindInfoDataService emptyCodeBindInfoDataService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试emptyCodeGenerate方法，当needLogo和needPadding都为null，且generateEmptyQrCodeByBizType返回null时
     */
    @Test
    public void testEmptyCodeGenerate_NeedLogoAndNeedPaddingAreNullAndGenerateEmptyQrCodeByBizTypeReturnsNull() {
        // arrange
        when(emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(anyInt(), anyBoolean(), anyBoolean())).thenReturn(null);
        // act
        RemoteResponse<EmptyCodeConfigInfoDTO> response = emptyCodeGenerateService.emptyCodeGenerate(1, null, null);
        // assert
        assertNotNull(response);
        assertEquals("emptyCodeGenerate error", response.getMsg());
    }

    /**
     * 测试emptyCodeGenerate方法，当needLogo和needPadding都为null，且generateEmptyQrCodeByBizType返回非null时
     */
    @Test
    public void testEmptyCodeGenerate_NeedLogoAndNeedPaddingAreNullAndGenerateEmptyQrCodeByBizTypeReturnsNonNull() {
        // arrange
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setCodeUrl("codeUrl");
        qrCodeConfigDTO.setImageUrl("imageUrl");
        when(emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(anyInt(), anyBoolean(), anyBoolean())).thenReturn(qrCodeConfigDTO);
        // act
        RemoteResponse<EmptyCodeConfigInfoDTO> response = emptyCodeGenerateService.emptyCodeGenerate(1, null, null);
        // assert
        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertEquals("codeUrl", response.getData().getOriginalUrl());
        assertEquals("imageUrl", response.getData().getQrCodeUrl());
    }

    /**
     * 测试emptyCodeGenerate方法，当needLogo和needPadding至少有一个不为null，且generateEmptyQrCodeByBizType返回null时
     */
    @Test
    public void testEmptyCodeGenerate_NeedLogoAndNeedPaddingAreNotNullAndGenerateEmptyQrCodeByBizTypeReturnsNull() {
        // arrange
        when(emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(anyInt(), anyBoolean(), anyBoolean())).thenReturn(null);
        // act
        RemoteResponse<EmptyCodeConfigInfoDTO> response = emptyCodeGenerateService.emptyCodeGenerate(1, true, true);
        // assert
        assertNotNull(response);
        assertEquals("emptyCodeGenerate error", response.getMsg());
    }

    /**
     * 测试emptyCodeGenerate方法，当needLogo和needPadding至少有一个不为null，且generateEmptyQrCodeByBizType返回非null时
     */
    @Test
    public void testEmptyCodeGenerate_NeedLogoAndNeedPaddingAreNotNullAndGenerateEmptyQrCodeByBizTypeReturnsNonNull() {
        // arrange
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setCodeUrl("codeUrl");
        qrCodeConfigDTO.setImageUrl("imageUrl");
        when(emptyCodeBindInfoDataService.generateEmptyQrCodeByBizType(anyInt(), anyBoolean(), anyBoolean())).thenReturn(qrCodeConfigDTO);
        // act
        RemoteResponse<EmptyCodeConfigInfoDTO> response = emptyCodeGenerateService.emptyCodeGenerate(1, true, true);
        // assert
        assertNotNull(response);
        assertEquals("success", response.getMsg());
        assertEquals("codeUrl", response.getData().getOriginalUrl());
        assertEquals("imageUrl", response.getData().getQrCodeUrl());
    }
}
