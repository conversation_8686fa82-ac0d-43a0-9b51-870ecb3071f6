package com.sankuai.carnation.distribution.bcp;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableMap;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplGetRebateLimitHour1Test {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private ShopAcl shopAcl;

    private MockedStatic<Lion> mockedLion;

    @Before
    public void setUp() {
        mockedLion = Mockito.mockStatic(Lion.class);
    }

    @After
    public void tearDown() {
        mockedLion.close();
    }

    private void setUpCommonMocks() {
        Map<String, Integer> categoryMap = ImmutableMap.of("1", 12, "2", 24, "3", 48);
        mockedLion.when(() -> Lion.getMap(anyString(), anyString(), any(), any())).thenReturn(categoryMap);
    }

    private String getRebateLimitHourKey() {
        try {
            java.lang.reflect.Field field = PromoCodeBcpCheckServiceImpl.class.getDeclaredField("REBATE_LIMIT_HOUR_KEY");
            field.setAccessible(true);
            return (String) field.get(null);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get REBATE_LIMIT_HOUR_KEY", e);
        }
    }

    @Test
    public void testGetRebateLimitHour_dpShopIdIsNull() throws Throwable {
        setUpCommonMocks();
        Long dpShopId = null;
        int result = promoCodeBcpCheckService.getRebateLimitHour(dpShopId);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_dpShopIdIsZero() throws Throwable {
        setUpCommonMocks();
        Long dpShopId = 0L;
        int result = promoCodeBcpCheckService.getRebateLimitHour(dpShopId);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_dpShopIdIsNotNullAndDpPoiDTOListIsEmpty() throws Throwable {
        setUpCommonMocks();
        Long dpShopId = 123L;
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Collections.emptyList());
        int result = promoCodeBcpCheckService.getRebateLimitHour(dpShopId);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_dpShopIdIsNotNullAndBackMainCategoryPathIsEmpty() throws Throwable {
        setUpCommonMocks();
        Long dpShopId = 123L;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Collections.emptyList());
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Collections.singletonList(dpPoiDTO));
        int result = promoCodeBcpCheckService.getRebateLimitHour(dpShopId);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_dpShopIdIsNotNullAndNoCategoryIdMatches() throws Throwable {
        setUpCommonMocks();
        Long dpShopId = 123L;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        List<DpPoiBackCategoryDTO> backCategoryPath = Collections.emptyList();
        dpPoiDTO.setBackMainCategoryPath(backCategoryPath);
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Collections.singletonList(dpPoiDTO));
        int result = promoCodeBcpCheckService.getRebateLimitHour(dpShopId);
        assertEquals(24, result);
    }

    @Test
    public void testGetRebateLimitHour_dpShopIdIsNotNullAndCategoryIdMatches() throws Throwable {
        setUpCommonMocks();
        Long dpShopId = 123L;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        List<DpPoiBackCategoryDTO> backCategoryPath = Collections.emptyList();
        dpPoiDTO.setBackMainCategoryPath(backCategoryPath);
        when(shopAcl.getDpPoiDTOList(anyList())).thenReturn(Collections.singletonList(dpPoiDTO));
        int result = promoCodeBcpCheckService.getRebateLimitHour(dpShopId);
        assertEquals(24, result);
    }
}
