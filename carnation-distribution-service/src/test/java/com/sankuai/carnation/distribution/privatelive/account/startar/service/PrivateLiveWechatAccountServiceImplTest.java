package com.sankuai.carnation.distribution.privatelive.account.startar.service;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributor.assembler.DistributorVOAssembler;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.vo.DistributorVO;
import com.sankuai.carnation.distribution.privatelive.account.application.service.PrivateLiveWechatAccountAppService;
import com.sankuai.carnation.distribution.privatelive.account.dto.PrivateLiveAccountDTO;
import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveAccountIdentityRequest;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveAccountRoleTaskRequest;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveAccountSwitchRequest;
import com.sankuai.carnation.distribution.privatelive.account.request.PrivateLiveWechatAccountRequest;
import com.sankuai.carnation.distribution.privatelive.account.response.PrivateLiveAccountIdentityResponse;
import com.sankuai.carnation.distribution.privatelive.account.response.PrivateLiveAccountSwitchResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.PrivateLiveAccountTypeEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.MobileHelper;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 测试 PrivateLiveWechatAccountServiceImpl
 */
@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveWechatAccountServiceImplTest {

    @InjectMocks
    private PrivateLiveWechatAccountServiceImpl privateLiveWechatAccountServiceImpl;

    @Mock
    private PrivateLiveWechatAccountAppService privateLiveWechatAccountAppService;

    @Mock
    private DistributorVOAssembler distributorVOAssembler;

    @Mock
    private MobileHelper mobileHelper;

    @InjectMocks
    private PrivateLiveWechatAccountServiceImpl privateLiveWechatAccountService;

    private PrivateLiveAccountRoleTaskRequest request;

    private PrivateLiveAccountBO privateLiveAccountBO;

    private PrivateLiveAccountDTO privateLiveAccountDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this); privateLiveAccountBO = new PrivateLiveAccountBO();
        privateLiveAccountDTO = new PrivateLiveAccountDTO();
    }

    /**
     * 测试 wxToken 为空时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryAccountIdentityInfoWxTokenEmpty() throws Throwable {
        PrivateLiveAccountIdentityRequest request = new PrivateLiveAccountIdentityRequest();
        request.setWxToken("");
        privateLiveWechatAccountServiceImpl.queryAccountIdentityInfo(request);
    }

    /**
     * 测试未查询到账号信息或账号需要注册时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testQueryAccountIdentityInfoAccountNotFoundOrNeedRegister() throws Throwable {
        PrivateLiveAccountIdentityRequest request = new PrivateLiveAccountIdentityRequest();
        request.setWxToken("validToken");
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(null);
        privateLiveWechatAccountServiceImpl.queryAccountIdentityInfo(request);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testQueryAccountIdentityInfoSuccess() throws Throwable {
        PrivateLiveAccountIdentityRequest request = new PrivateLiveAccountIdentityRequest();
        request.setWxToken("validToken");
        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
        accountBO.setId(1L);
        accountBO.setAccountTypeList(Arrays.asList(PrivateLiveAccountTypeEnum.DISTRIBUTOR));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        RemoteResponse<PrivateLiveAccountIdentityResponse> response = privateLiveWechatAccountServiceImpl.queryAccountIdentityInfo(request);
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(1, response.getData().getAccountTypeList().size());
        assertEquals(PrivateLiveAccountTypeEnum.DISTRIBUTOR.getCode(), response.getData().getAccountTypeList().get(0));
    }

    @Test(expected = BizSceneException.class)
    public void testSwitchAccountWithEmptyWxTokenThrowsException() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("", 1);
        // act
        privateLiveWechatAccountServiceImpl.switchAccount(request);
    }

    /**
     * 测试账号类型为空时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testSwitchAccountWithEmptyAccountTypeThrowsException() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("wxToken", null);
        // act
        privateLiveWechatAccountServiceImpl.switchAccount(request);
    }

    /**
     * 测试账号类型错误时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testSwitchAccountWithInvalidAccountTypeThrowsException() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("wxToken", 999);
        // act
        privateLiveWechatAccountServiceImpl.switchAccount(request);
    }

    /**
     * 测试未查询到账号信息时抛出异常
     */
    @Test(expected = BizSceneException.class)
    public void testSwitchAccountWithNoAccountFoundThrowsException() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("wxToken", PrivateLiveAccountTypeEnum.CONSULTANT.getCode());
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(null);
        // act
        privateLiveWechatAccountServiceImpl.switchAccount(request);
    }

    /**
     * 测试成功切换到咨询师账号
     */
    @Test
    public void testSwitchAccountToConsultantSuccess() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("wxToken", PrivateLiveAccountTypeEnum.CONSULTANT.getCode());
        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
        accountBO.setConsultant(new PrivateLiveConsultantTask());
        accountBO.setId(1L);
        accountBO.setAccountTypeList(Arrays.asList(PrivateLiveAccountTypeEnum.CONSULTANT));
        accountBO.setConsultant(PrivateLiveConsultantTask.builder().phoneNumber("***********").build());
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        when(mobileHelper.getMobileMask(anyString())).thenReturn("123");
        //        when(distributorVOAssembler.buildConsultantVO(any(PrivateLiveConsultantTask.class))).thenReturn(DistributorVO.builder()
        //                .mobile("123").build());
        // act
        RemoteResponse<PrivateLiveAccountSwitchResponse> response = privateLiveWechatAccountServiceImpl.switchAccount(request);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData().getConsultant());
    }

    /**
     * 测试成功切换到分销商账号
     */
    @Test
    public void testSwitchAccountToDistributorSuccess() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("wxToken", PrivateLiveAccountTypeEnum.DISTRIBUTOR.getCode());
        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
        DistributorBO distributorBO = new DistributorBO();
        distributorBO.setDistributorId(1L);
        distributorBO.setDistributorGroupId(1);
        accountBO.setDistributor(distributorBO);
        accountBO.setId(1L);
        accountBO.setAccountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        when(distributorVOAssembler.buildDistributorVO(any(DistributorBO.class))).thenReturn(new DistributorVO());
        // act
        RemoteResponse<PrivateLiveAccountSwitchResponse> response = privateLiveWechatAccountServiceImpl.switchAccount(request);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData().getDistributor());
    }

    /**
     * 测试成功切换到分销商团长账号
     */
    @Test
    public void testSwitchAccountToDistributorGroupSuccess() {
        // arrange
        PrivateLiveAccountSwitchRequest request = new PrivateLiveAccountSwitchRequest("wxToken", PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP.getCode());
        PrivateLiveAccountBO accountBO = new PrivateLiveAccountBO();
        accountBO.setDistributorGroup(new DistributorGroupBO());
        DistributorBO distributorBO = new DistributorBO();
        distributorBO.setDistributorId(1L);
        distributorBO.setDistributorGroupId(1);
        accountBO.setDistributor(distributorBO);
        accountBO.setId(1L);
        accountBO.setAccountTypeList(Lists.newArrayList(PrivateLiveAccountTypeEnum.DISTRIBUTOR_GROUP));
        when(privateLiveWechatAccountAppService.queryPrivateLiveWechatAccount(any(PrivateLiveWechatAccountRequest.class))).thenReturn(accountBO);
        // act
        RemoteResponse<PrivateLiveAccountSwitchResponse> response = privateLiveWechatAccountServiceImpl.switchAccount(request);
        // assert
        assertNotNull(response);
        assertNotNull(response.getData().getDistributorGroup());
    }








    /**
     * 测试查询微信账户信息，当返回结果为null时
     */
    @Test
    public void testQueryPrivateLiveWechatAccount_ReturnNull() {

        RemoteResponse<PrivateLiveAccountDTO> response = privateLiveWechatAccountService.queryPrivateLiveWechatAccount(request);

        assertNull("应当返回null的PrivateLiveAccountDTO", response.getData());
    }



}
