package com.sankuai.carnation.distribution.privatelive.distribution.domain.model;

import com.sankuai.carnation.distribution.privatelive.account.model.PrivateLiveAccountBO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import static org.junit.Assert.*;

public class PrivateLiveJoinDistributorGroupCmdTest {

    private PrivateLiveJoinDistributorGroupCmd cmd;
    private PrivateLiveAccountBO mockAccount;

    @Before
    public void setUp() {
        cmd = new PrivateLiveJoinDistributorGroupCmd();
        mockAccount = Mockito.mock(PrivateLiveAccountBO.class);
    }

    /**
     * 测试requireRegisterAccount方法，当privateLiveWechatAccount为null时
     */
    @Test
    public void testRequireRegisterAccountWhenAccountIsNull() {
        // arrange
        cmd.setPrivateLiveWechatAccount(null);

        // act
        boolean result = cmd.requireRegisterAccount();

        // assert
        assertTrue("当privateLiveWechatAccount为null时，应该需要注册账户", result);
    }

    /**
     * 测试requireRegisterAccount方法，当privateLiveWechatAccount不为null且需要注册账户时
     */
    @Test
    public void testRequireRegisterAccountWhenAccountIsNotNullAndRequiresRegistration() {
        // arrange
        Mockito.when(mockAccount.requireRegisterAccount()).thenReturn(true);
        cmd.setPrivateLiveWechatAccount(mockAccount);

        // act
        boolean result = cmd.requireRegisterAccount();

        // assert
        assertTrue("当privateLiveWechatAccount不为null且需要注册账户时，应该返回true", result);
    }

    /**
     * 测试requireRegisterAccount方法，当privateLiveWechatAccount不为null且不需要注册账户时
     */
    @Test
    public void testRequireRegisterAccountWhenAccountIsNotNullAndDoesNotRequireRegistration() {
        // arrange
        Mockito.when(mockAccount.requireRegisterAccount()).thenReturn(false);
        cmd.setPrivateLiveWechatAccount(mockAccount);

        // act
        boolean result = cmd.requireRegisterAccount();

        // assert
        assertFalse("当privateLiveWechatAccount不为null且不需要注册账户时，应该返回false", result);
    }
}
