package com.sankuai.carnation.distribution.commisson.domain.factory.commission.staff.limit.rule;

import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleLimitRuleDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateSettleRuleDTO;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.sankuai.carnation.distribution.commisson.limit.enums.RebateLimitReasonType;
import com.sankuai.carnation.distribution.commisson.limit.rule.TechnicianAmountLimitRule;
import com.sankuai.carnation.distribution.commisson.repository.RebateLimitRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @description :
 * @date : 2024/7/31
 */
@RunWith(MockitoJUnitRunner.class)
public class TechnicianAmountLimitRuleTest {

    @InjectMocks
    private TechnicianAmountLimitRule shopAmountLimitRule;

    @Mock
    private RebateLimitRepository rebateLimitRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private RebateActivityRuleDTO buildRebateActivityRuleDTO() {
        RebateActivityRuleDTO rule = new RebateActivityRuleDTO();
        RebateSettleRuleDTO ruleData = new RebateSettleRuleDTO();
        RebateSettleLimitRuleDTO rebateSettleLimitRule = new RebateSettleLimitRuleDTO();
        rebateSettleLimitRule.setDistributorAmountLimit((long) 1000);
        ruleData.setRebateSettleLimitRule(rebateSettleLimitRule);
        rule.setRule(ruleData);
        return rule;
    }

    private CommissionVerifyCalculationRequest defaultRequest() {
        CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
        request.setOrderId("orderId");
        return request;
    }

    private RebateAmountResult calculationResult(long rebateAmount) {
        RebateAmountResult rebateAmountOrder = new RebateAmountResult();
        rebateAmountOrder.setRebateAmount(rebateAmount);
        return rebateAmountOrder;
    }

    /**
     * 测试当总返利金额等于限制金额时
     */
    @Test
    public void testCalculatorResultWhenSumAllOrderResultEqualsLimitRule() {
        // arrange
        RebateActivityRuleDTO limitRule = buildRebateActivityRuleDTO(); //活动限制1000
        RebateAmountResult rebateAmountResult = calculationResult(100); // 订单计算返利100

        // 模拟rebateAmountOrder返回的返利金额，已返利1000L
        when(rebateLimitRepository.sumOrderRebateAmountByTechId(any(), any())).thenReturn(1000L);
        // act
        RebateLimitResult result = shopAmountLimitRule.handleResult(defaultRequest(), rebateAmountResult, limitRule);

        // assert
        assertTrue(result.isResult());
        assertEquals(Long.valueOf(0), result.getRebateAmount());
        assertEquals(RebateLimitReasonType.PROMOTION_LIMIT_REACHED_DISTRIBUTOR.getReason(), result.getReason());
    }

    /**
     * 测试当订单总和加上返利金额大于限制规则时的情况
     */
    @Test
    public void testCalculatorResultSumAllOrderResultPlusRebateAmountGreaterThanLimitRule() {
        // arrange
        RebateActivityRuleDTO limitRule = buildRebateActivityRuleDTO(); //活动限制1000
        RebateAmountResult rebateAmountResult = calculationResult(200); // 订单计算返利200

        // 模拟rebateAmountOrder返回的返利金额
        when(rebateLimitRepository.sumOrderRebateAmountByTechId(any(), any())).thenReturn(900L);

        // act
        RebateLimitResult result = shopAmountLimitRule.handleResult(defaultRequest(), rebateAmountResult, limitRule);

        // assert
        assertTrue(result.isResult());
        assertEquals(Long.valueOf(100), result.getRebateAmount());
        assertEquals(RebateLimitReasonType.CASHBACK_LIMIT_REACHED__DISTRIBUTOR.getReason(), result.getReason());
    }

    /**
     * 测试当订单总和加上返利金额不超过限制规则时的情况
     */
    @Test
    public void testCalculatorResultSumAllOrderResultPlusRebateAmountNotExceedLimitRule() {
        // arrange
        RebateActivityRuleDTO limitRule = buildRebateActivityRuleDTO(); //活动限制1000
        RebateAmountResult rebateAmountResult = calculationResult(100); // 订单计算返利100

        // 模拟rebateAmountOrder返回的返利金额
        when(rebateLimitRepository.sumOrderRebateAmountByTechId(any(), any())).thenReturn(800L);

        // act
        RebateLimitResult result = shopAmountLimitRule.handleResult(defaultRequest(), rebateAmountResult, limitRule);

        // assert
        assertFalse(result.isResult());
        assertEquals("不命中返利规则时候其rebateAmount应该为0", Long.valueOf(0), result.getRebateAmount());
        assertNull(result.getReason());
    }
}
