package com.sankuai.carnation.distribution.bcp;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.dianping.gmkt.event.api.scan.request.UserScanRequest;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commisson.exceptions.CommissionCalculateException;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.ProductDetailAclService;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductDetailBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.request.ProductDetailRequest;
import com.sankuai.technician.trade.common.enums.OrderTypeEnum;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.sankuai.technician.trade.order.dto.model.OrderVerifyReceiptDto;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PromoCodeBcpCheckServiceImplGetRebateActivityRuleTest {

    @InjectMocks
    private PromoCodeBcpCheckServiceImpl promoCodeBcpCheckService;

    @Mock
    private DistributorActivityService distributorActivityService;

    @Mock
    private ProductDetailAclService productDetailAclService;

    private final int productType = 1;

    private final long productId = 1L;

    private final int platform = 1;

    private final long userId = 1L;

    @Mock
    private OrderReceiptQueryService orderReceiptQueryService;

    @Mock
    private UserScanRecordService userScanRecordService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 applyRecordId 为 null 的情况
     */
    @Test
    public void testGetRebateActivityRuleApplyRecordIdIsNull() throws Throwable {
        assertNull(promoCodeBcpCheckService.getRebateActivityRule(null));
    }

    /**
     * 测试 applyRecordId 小于等于 0 的情况
     */
    @Test
    public void testGetRebateActivityRuleApplyRecordIdIsLessThanOrEqualToZero() throws Throwable {
        assertNull(promoCodeBcpCheckService.getRebateActivityRule(0L));
    }

    /**
     * 测试 distributorActivityService.queryActivityRule(applyRecordId) 返回的响应为 null 的情况
     */
    @Test
    public void testGetRebateActivityRuleResponseIsNull() throws Throwable {
        when(distributorActivityService.queryActivityRule(anyLong())).thenReturn(null);
        assertNull(promoCodeBcpCheckService.getRebateActivityRule(1L));
    }

    /**
     * 测试 distributorActivityService.queryActivityRule(applyRecordId) 返回的响应不为 null 的情况
     */
    @Test
    public void testGetRebateActivityRuleResponseIsNotNull() throws Throwable {
        RebateActivityRuleDTO rebateActivityRuleDTO = new RebateActivityRuleDTO();
        CommonResponse<RebateActivityRuleDTO> response = mock(CommonResponse.class);
        when(response.getData()).thenReturn(rebateActivityRuleDTO);
        when(distributorActivityService.queryActivityRule(anyLong())).thenReturn(response);
        assertSame(rebateActivityRuleDTO, promoCodeBcpCheckService.getRebateActivityRule(1L));
    }

    /**
     * Tests getOrderProduct method under normal conditions.
     */
    @Test
    public void testGetOrderProductNormal() throws Throwable {
        // Arrange
        ProductDetailBO productDetailBO = new ProductDetailBO();
        Map<Long, ProductDetailBO> productDetailMap = new HashMap<>();
        productDetailMap.put(productId, productDetailBO);
        when(productDetailAclService.batchGetProduct(any(ProductDetailRequest.class))).thenReturn(productDetailMap);
        // Act
        ProductDetailBO result = promoCodeBcpCheckService.getOrderProduct(productType, productId, platform, userId, 1);
        // Assert
        assertEquals(productDetailBO, result);
    }

    /**
     * Tests getOrderProduct method under retry conditions.
     */
    @Test
    public void testGetOrderProductRetry() throws Throwable {
        // Arrange
        ProductDetailBO productDetailBO = new ProductDetailBO();
        Map<Long, ProductDetailBO> productDetailMap = new HashMap<>();
        productDetailMap.put(productId, productDetailBO);
        when(productDetailAclService.batchGetProduct(any(ProductDetailRequest.class))).thenReturn(productDetailMap);
        // Act
        ProductDetailBO result = promoCodeBcpCheckService.getOrderProduct(productType, productId, platform, userId, 2);
        // Assert
        assertEquals(productDetailBO, result);
    }

    /**
     * Tests getOrderProduct method under failure conditions.
     */
    @Test
    public void testGetOrderProductFailure() throws Throwable {
        // Arrange
        when(productDetailAclService.batchGetProduct(any(ProductDetailRequest.class))).thenReturn(new HashMap<>());
        // Act
        ProductDetailBO result = promoCodeBcpCheckService.getOrderProduct(productType, productId, platform, userId, 1);
        // Assert
        assertNull(result);
    }

    /**
     * 测试 getOrderReceipt 方法，当 orderReceiptQueryService.queryOrderReceipt 返回的 TechnicianResp 对象的 code 字段为 200 时，应返回 TechnicianResp 对象中的 data 字段
     */
    @Test
    public void testGetOrderReceipt_Success() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        OrderReceiptDto orderReceiptDto = new OrderReceiptDto();
        when(orderReceiptQueryService.queryOrderReceipt(orderId, OrderTypeEnum.TRADE_ORDER.getId())).thenReturn(TechnicianResp.success(orderReceiptDto));
        // act
        OrderReceiptDto result = promoCodeBcpCheckService.getOrderReceipt(orderId);
        // assert
        assertEquals(orderReceiptDto, result);
    }

    /**
     * 测试 getOrderReceipt 方法，当 orderReceiptQueryService.queryOrderReceipt 返回的 TechnicianResp 对象的 code 字段不为 200 时，应返回 null
     */
    @Test
    public void testGetOrderReceipt_Fail() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        when(orderReceiptQueryService.queryOrderReceipt(orderId, OrderTypeEnum.TRADE_ORDER.getId())).thenReturn(TechnicianResp.fail("fail"));
        // act
        OrderReceiptDto result = promoCodeBcpCheckService.getOrderReceipt(orderId);
        // assert
        assertNull(result);
    }

    @Test
    public void testCheckUserScanTimeWhenVerifyTimeIsNull() throws Throwable {
        CommissionVerifyCalculationRequest verifyCalculationRequest = new CommissionVerifyCalculationRequest();
        verifyCalculationRequest.setVerifyTime(null);
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPlatform(1);
        orderInfo.setUserId(1L);
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = new PromoQRCodeResponse<>();
        response.setCode("000");
        response.setMsg("成功");
        response.setData(Collections.emptyList());
        when(userScanRecordService.queryLatestScanRecord(any(UserScanRequest.class))).thenReturn(response);
        boolean result = promoCodeBcpCheckService.checkUserScanTime(verifyCalculationRequest, orderInfo, 1);
        assertFalse(result);
    }

    @Test
    public void testCheckUserScanTimeWhenVerifyTimeIsNotNull() throws Throwable {
        CommissionVerifyCalculationRequest verifyCalculationRequest = new CommissionVerifyCalculationRequest();
        verifyCalculationRequest.setVerifyTime(new Date());
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPlatform(1);
        orderInfo.setUserId(1L);
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = new PromoQRCodeResponse<>();
        response.setCode("000");
        response.setMsg("成功");
        response.setData(Collections.emptyList());
        when(userScanRecordService.queryLatestScanRecord(any(UserScanRequest.class))).thenReturn(response);
        boolean result = promoCodeBcpCheckService.checkUserScanTime(verifyCalculationRequest, orderInfo, 1);
        assertFalse(result);
    }

    @Test(expected = CommissionCalculateException.class)
    public void testCheckUserScanTimeWhenResponseIsNull() throws Throwable {
        CommissionVerifyCalculationRequest verifyCalculationRequest = new CommissionVerifyCalculationRequest();
        verifyCalculationRequest.setVerifyTime(new Date());
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPlatform(1);
        orderInfo.setUserId(1L);
        when(userScanRecordService.queryLatestScanRecord(any(UserScanRequest.class))).thenReturn(null);
        promoCodeBcpCheckService.checkUserScanTime(verifyCalculationRequest, orderInfo, 1);
    }

    @Test(expected = CommissionCalculateException.class)
    public void testCheckUserScanTimeWhenResponseIsNotSuccess() throws Throwable {
        CommissionVerifyCalculationRequest verifyCalculationRequest = new CommissionVerifyCalculationRequest();
        verifyCalculationRequest.setVerifyTime(new Date());
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPlatform(1);
        orderInfo.setUserId(1L);
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = new PromoQRCodeResponse<>();
        response.setCode("101");
        response.setMsg("参数错误");
        when(userScanRecordService.queryLatestScanRecord(any(UserScanRequest.class))).thenReturn(response);
        promoCodeBcpCheckService.checkUserScanTime(verifyCalculationRequest, orderInfo, 1);
    }

    // Other test cases remain unchanged
    @Test
    public void testCheckUserScanTimeWhenScanRecordListIsEmpty() throws Throwable {
        CommissionVerifyCalculationRequest verifyCalculationRequest = new CommissionVerifyCalculationRequest();
        verifyCalculationRequest.setVerifyTime(new Date());
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPlatform(1);
        orderInfo.setUserId(1L);
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = new PromoQRCodeResponse<>();
        response.setCode("000");
        response.setMsg("成功");
        response.setData(Collections.emptyList());
        when(userScanRecordService.queryLatestScanRecord(any(UserScanRequest.class))).thenReturn(response);
        boolean result = promoCodeBcpCheckService.checkUserScanTime(verifyCalculationRequest, orderInfo, 1);
        assertFalse(result);
    }

    @Test
    public void testCheckUserScanTimeWhenScanRecordListIsNotEmpty() throws Throwable {
        CommissionVerifyCalculationRequest verifyCalculationRequest = new CommissionVerifyCalculationRequest();
        verifyCalculationRequest.setVerifyTime(new Date());
        OrderInfoBO orderInfo = new OrderInfoBO();
        orderInfo.setPlatform(1);
        orderInfo.setUserId(1L);
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = new PromoQRCodeResponse<>();
        response.setCode("000");
        response.setMsg("成功");
        response.setData(Arrays.asList(new UserScanRecordDTO()));
        when(userScanRecordService.queryLatestScanRecord(any(UserScanRequest.class))).thenReturn(response);
        boolean result = promoCodeBcpCheckService.checkUserScanTime(verifyCalculationRequest, orderInfo, 1);
        assertTrue(result);
    }
}
