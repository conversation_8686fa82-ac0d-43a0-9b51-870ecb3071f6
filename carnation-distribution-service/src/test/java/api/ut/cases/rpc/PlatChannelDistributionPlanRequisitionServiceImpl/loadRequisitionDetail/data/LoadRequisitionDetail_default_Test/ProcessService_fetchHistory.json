[{"typeName": "com.dianping.gb.audit.platform.biz.longtypeservice.ProcessService", "urlType": 0, "serviceType": "PIEGON", "serviceName": "processService", "identityURI": "PIGEON://gb-audit-platform-service/http://service.dianping.com/gb-audit-platform-longprocessservice_1.0.0.fetchHistory(int,long)", "methodName": "fetchHistory", "requestTypes": ["int", "long"], "returnType": "java.util.List<com.dianping.tp.audit.dto.HistoryDTO>", "request": [3010, 1857780407785791558], "response": [{"operation": "CREATE", "user": {"userType": "SSO_USER", "userID": 20210082}, "desc": "创建流程", "memo": "", "addTime": "1731764339000", "rejection": false}, {"operation": "FLOW", "user": {"userType": "SSO_USER", "userID": 20210082}, "desc": "联盟结算佣金规则信息部分提交完成", "memo": "", "addTime": "1731764340000", "rejection": false, "nodeName": "录入联盟结算佣金规则信息"}, {"operation": "REASSIGN", "user": {"userType": "SYSTEM_USER", "userID": 34}, "desc": "改签用户'宁浩然'", "memo": "", "addTime": "1731764561000", "rejection": false, "nodeName": "运营主管待审核"}, {"operation": "FLOW", "user": {"userType": "SSO_USER", "userID": 20210082}, "desc": "运营主管驳回", "memo": "就是驳回你", "addTime": "1731764596000", "rejection": false, "nodeName": "运营主管"}], "genericService": false}]