{"serviceType": "PIEGON", "methodName": "loadRequisitionDetail", "request": [{"requisitionId": "1857780407785791558"}], "response": {"code": 200, "msg": "success", "data": {"requisitionId": "1857780407785791558", "requisitionType": 1, "title": "驳回测试", "channel": "quan_quan", "tradeType": 3, "background": "驳回测试", "detailFileOverviewVo": {"detailFileUrl": "https://msstest.sankuai.com/dzusergrowth-distribution-core/distribution88d7266e809942fb9b274db090371f811731764336057.xlsx?AWSAccessKeyId=SRV_MMijIbnJWtGXyPKB6wItMaFVVUcDBBLp&Expires=1731911015&Signature=3ipe6IMtr9XtEbX6qDq3Qi5cHjg%3D", "totalCount": 1, "succeedCount": 0, "failedCount": 0, "importingCount": 0}, "auditStatus": 4, "requisitionAuditNodeDetailList": [{"nodeName": "", "level": 0, "auditor": "宁浩然/ninghaoran", "status": 4, "auditTime": "2024-11-16 21:38:59"}, {"nodeName": "运营主管", "level": 1, "auditor": "宁浩然/ninghaoran", "status": 2, "auditTime": "2024-11-16 21:43:16"}, {"nodeName": "BU head", "level": 2, "status": 3}, {"nodeName": "财务FP", "level": 3, "status": 3}, {"nodeName": "到综商业产品策略", "level": 4, "status": 3}]}, "doCrow": false, "success": true}, "traceId": "4978967721179960348", "traceContext": {"org_request_header_key_data_score_tmp": "", "org_request_header_key_data_score": ""}, "params": {}, "httpMethod": "GET", "testXURL": "https://testx.sankuai.com/inspector/record/share?share-code=eyJyZWNvcmRJZCI6IjQ4NDQ3OTMiLCJlc0lkcyI6WyJzb0RzUFpNQkhONkxPTGxEU3VrciJdLCJkYXRhU291cmNlIjoiVEVTVFgifQ=="}