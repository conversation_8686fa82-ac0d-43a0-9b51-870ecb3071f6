[{"typeName": "com.sankuai.meituan.org.openapi.iface.IEmpRemoteService", "urlType": 0, "serviceType": "THRIFT", "serviceName": "iEmpRemoteService", "identityURI": "THRIFT://com.sankuai.hrmdm.org.gateway/com.sankuai.meituan.org.openapi.iface.IEmpRemoteService.queryByEmpId(com.sankuai.meituan.org.queryservice.domain.param.emp.QueryByEmpIdParam)", "methodName": "queryByEmpId", "requestTypes": ["com.sankuai.meituan.org.queryservice.domain.param.emp.QueryByEmpIdParam"], "returnType": "com.sankuai.meituan.org.queryservice.domain.EmpDto", "request": [{"empIds": ["20210082"], "tenantId": 1, "businessUnitList": ["MT"], "orgAppKey": "f31ab3cff7", "authType": "EMP"}], "response": {"empId": "20210082", "name": "宁浩然", "mis": "<PERSON><PERSON><PERSON><PERSON>", "tenantId": 1, "source": "MT"}, "genericService": false}, {"typeName": "com.sankuai.meituan.org.openapi.iface.IEmpRemoteService", "urlType": 0, "serviceType": "THRIFT", "serviceName": "iEmpRemoteService", "identityURI": "THRIFT://com.sankuai.hrmdm.org.gateway/com.sankuai.meituan.org.openapi.iface.IEmpRemoteService.queryByEmpId(com.sankuai.meituan.org.queryservice.domain.param.emp.QueryByEmpIdParam)", "methodName": "queryByEmpId", "requestTypes": ["com.sankuai.meituan.org.queryservice.domain.param.emp.QueryByEmpIdParam"], "returnType": "com.sankuai.meituan.org.queryservice.domain.EmpDto", "request": [{"empIds": ["20210082"], "tenantId": 1, "businessUnitList": ["MT"], "orgAppKey": "f31ab3cff7", "authType": "EMP"}], "response": {"empId": "20210082", "name": "宁浩然", "mis": "<PERSON><PERSON><PERSON><PERSON>", "tenantId": 1, "source": "MT"}, "genericService": false}]