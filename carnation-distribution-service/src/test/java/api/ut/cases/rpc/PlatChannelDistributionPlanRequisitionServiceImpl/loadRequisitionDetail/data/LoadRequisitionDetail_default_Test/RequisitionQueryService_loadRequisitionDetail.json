[{"typeName": "com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService", "urlType": 0, "serviceType": "PIEGON", "serviceName": "requisitionQueryService", "identityURI": "PIGEON://com.sankuai.dzusergrowth.distribution.plan/com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService.loadRequisitionDetail(com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionDetailLoadRequest)", "methodName": "loadRequisitionDetail", "requestTypes": ["com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionDetailLoadRequest"], "returnType": "com.sankuai.dzusergrowth.common.api.response.Response<com.sankuai.dzusergrowth.distribution.plan.api.dto.RequisitionDetailDTO>", "request": [{"requisitionId": 1857780407785791558}], "response": {"code": 200, "message": "success", "data": {"requisitionId": 1857780407785791558, "requisitionType": 1, "tradeType": 3, "channel": "quan_quan", "title": "驳回测试", "background": "驳回测试", "detailFile": {"detailFileName": "distribution88d7266e809942fb9b274db090371f811731764336057.xlsx", "totalCount": 1, "succeedCount": 0, "failedCount": 0, "importingCount": 0}, "status": 4}}, "genericService": false}]