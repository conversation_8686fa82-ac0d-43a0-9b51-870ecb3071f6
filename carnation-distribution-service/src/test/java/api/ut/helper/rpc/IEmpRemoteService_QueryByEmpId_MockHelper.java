package api.ut.helper.rpc;

import com.google.common.reflect.TypeToken;
import com.sankuai.meituan.org.openapi.iface.IEmpRemoteService;
import com.sankuai.meituan.org.queryservice.domain.EmpDto;
import com.sankuai.meituan.org.queryservice.domain.param.emp.QueryByEmpIdParam;
import com.sankuai.testapi.sdk.matcher.JsonArgumentMatcher;
import com.sankuai.testapi.sdk.utils.SerializationUtils;
import lombok.Data;
import org.mockito.Mockito;

import java.util.List;

import static org.mockito.ArgumentMatchers.argThat;

@Data
public class IEmpRemoteService_QueryByEmpId_MockHelper {
    private String identityURI = "THRIFT://com.sankuai.hrmdm.org.gateway/com.sankuai.meituan.org.openapi.iface.IEmpRemoteService.queryByEmpId(com.sankuai.meituan.org.queryservice.domain.param.emp.QueryByEmpIdParam)";

    private String mockPath;

    private IEmpRemoteService iEmpRemoteService;

    private List<QueryByEmpIdParam> params1;

    private List<EmpDto> mockResults;

    static {
    }

    public static IEmpRemoteService_QueryByEmpId_MockHelper buildMock(IEmpRemoteService iEmpRemoteService, String dataPath) throws Exception {
        IEmpRemoteService_QueryByEmpId_MockHelper mockHelper = new IEmpRemoteService_QueryByEmpId_MockHelper(iEmpRemoteService, dataPath);
        mockHelper.mock();
        return mockHelper;
    }

    private IEmpRemoteService_QueryByEmpId_MockHelper(IEmpRemoteService iEmpRemoteService, String dataPath) throws Exception {

        this.mockPath = dataPath + "IEmpRemoteService_queryByEmpId.json";

        this.iEmpRemoteService = iEmpRemoteService;

        this.params1 = (List<QueryByEmpIdParam>) SerializationUtils.loadMockRequest(mockPath, 1,
                new TypeToken<List<QueryByEmpIdParam>>() {
                }.getType());

        this.mockResults = (List<EmpDto>) SerializationUtils.loadMockResponse(mockPath, new
                TypeToken<List<EmpDto>>() {
                }.getType());
    }


    public void mock() throws Exception {
        for (int i = 0; i < mockResults.size(); i++) {
            Mockito.when(iEmpRemoteService.queryByEmpId(argThat(new JsonArgumentMatcher<>(params1.get(i))))).thenReturn(mockResults.get(i));
        }
    }

    public QueryByEmpIdParam getParam1() {
        return params1.get(0);
    }

    public void setParam1(QueryByEmpIdParam param) {
        this.params1.set(0, param);
    }

    public EmpDto getMockResult() {
        return this.mockResults.get(0);
    }

    public void setMockResult(EmpDto result) {
        this.mockResults.set(0, result);
    }

}
