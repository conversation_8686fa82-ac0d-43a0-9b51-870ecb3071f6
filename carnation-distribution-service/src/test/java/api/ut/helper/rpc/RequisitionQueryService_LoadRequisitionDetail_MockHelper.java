package api.ut.helper.rpc;

import com.google.common.reflect.TypeToken;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.RequisitionDetailDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionDetailLoadRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService;
import com.sankuai.testapi.sdk.matcher.JsonArgumentMatcher;
import com.sankuai.testapi.sdk.utils.SerializationUtils;
import lombok.Data;
import org.mockito.Mockito;

import java.util.List;

import static org.mockito.ArgumentMatchers.argThat;

@Data
public class RequisitionQueryService_LoadRequisitionDetail_MockHelper {
    private String identityURI = "PIGEON://com.sankuai.dzusergrowth.distribution.plan/com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService.loadRequisitionDetail(com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionDetailLoadRequest)";

    private String mockPath;

    private RequisitionQueryService requisitionQueryService;

    private List<RequisitionDetailLoadRequest> params1;

    private List<Response<RequisitionDetailDTO>> mockResults;

    static {
    }

    public static RequisitionQueryService_LoadRequisitionDetail_MockHelper buildMock(RequisitionQueryService requisitionQueryService, String dataPath) throws Exception {
        RequisitionQueryService_LoadRequisitionDetail_MockHelper mockHelper = new RequisitionQueryService_LoadRequisitionDetail_MockHelper(requisitionQueryService, dataPath);
        mockHelper.mock();
        return mockHelper;
    }

    private RequisitionQueryService_LoadRequisitionDetail_MockHelper(RequisitionQueryService requisitionQueryService, String dataPath) throws Exception {

        this.mockPath = dataPath + "RequisitionQueryService_loadRequisitionDetail.json";

        this.requisitionQueryService = requisitionQueryService;

        this.params1 = (List<RequisitionDetailLoadRequest>) SerializationUtils.loadMockRequest(mockPath, 1,
                new TypeToken<List<RequisitionDetailLoadRequest>>() {
                }.getType());

        this.mockResults = (List<Response<RequisitionDetailDTO>>) SerializationUtils.loadMockResponse(mockPath, new
                TypeToken<List<Response<RequisitionDetailDTO>>>() {
                }.getType());
    }


    public void mock() throws Exception {
        for (int i = 0; i < mockResults.size(); i++) {
            Mockito.when(requisitionQueryService.loadRequisitionDetail(argThat(new JsonArgumentMatcher<>(params1.get(i))))).thenReturn(mockResults.get(i));
        }
    }

    public RequisitionDetailLoadRequest getParam1() {
        return params1.get(0);
    }

    public void setParam1(RequisitionDetailLoadRequest param) {
        this.params1.set(0, param);
    }

    public Response<RequisitionDetailDTO> getMockResult() {
        return this.mockResults.get(0);
    }

    public void setMockResult(Response<RequisitionDetailDTO> result) {
        this.mockResults.set(0, result);
    }

}
