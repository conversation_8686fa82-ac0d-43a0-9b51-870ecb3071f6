package api.ut.helper.rpc;


import com.dianping.tp.audit.dto.HistoryDTO;
import com.google.common.reflect.TypeToken;
import com.sankuai.carnation.distribution.distributionplan.acl.PlatformAuditAcl;
import com.sankuai.testapi.sdk.utils.SerializationUtils;
import lombok.Data;
import org.mockito.Mockito;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@Data
public class ProcessService_FetchHistory_MockHelper {
    private String identityURI = "PIGEON://gb-audit-platform-service/http://service.dianping.com/gb-audit-platform-longprocessservice_1.0.0.fetchHistory(int,long)";

    private String mockPath;

    private PlatformAuditAcl platformAuditAcl;

    private List<Integer> params1;

    private List<Long> params2;

    private List<List<HistoryDTO>> mockResults;

    static {
    }

    public static ProcessService_FetchHistory_MockHelper buildMock(PlatformAuditAcl platformAuditAcl, String dataPath) throws Exception {
        ProcessService_FetchHistory_MockHelper mockHelper = new ProcessService_FetchHistory_MockHelper(platformAuditAcl, dataPath);
        mockHelper.mock();
        return mockHelper;
    }

    private ProcessService_FetchHistory_MockHelper(PlatformAuditAcl platformAuditAcl, String dataPath) throws Exception {

        this.mockPath = dataPath + "ProcessService_fetchHistory.json";

        this.platformAuditAcl = platformAuditAcl;

        this.params1 = (List<Integer>) SerializationUtils.loadMockRequest(mockPath, 1,
                new TypeToken<List<Integer>>() {
                }.getType());

        this.params2 = (List<Long>) SerializationUtils.loadMockRequest(mockPath, 2,
                new TypeToken<List<Long>>() {
                }.getType());

        this.mockResults = (List<List<HistoryDTO>>) SerializationUtils.loadMockResponse(mockPath, new
                TypeToken<List<List<HistoryDTO>>>() {
                }.getType());
    }


    public void mock() throws Exception {
        for (int i = 0; i < mockResults.size(); i++) {
            when(platformAuditAcl.fetchHistory(anyLong())).thenReturn(mockResults.get(i));
        }
    }

    public Integer getParam1() {
        return params1.get(0);
    }

    public void setParam1(Integer param) {
        this.params1.set(0, param);
    }

    public Long getParam2() {
        return params2.get(0);
    }

    public void setParam2(Long param) {
        this.params2.set(0, param);
    }

    public List<HistoryDTO> getMockResult() {
        return this.mockResults.get(0);
    }

    public void setMockResult(List<HistoryDTO> result) {
        this.mockResults.set(0, result);
    }

}
