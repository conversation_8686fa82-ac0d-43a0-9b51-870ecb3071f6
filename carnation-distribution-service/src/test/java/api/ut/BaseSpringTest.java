package api.ut;


import com.dianping.gb.audit.platform.biz.service.ProcessService;
import com.sankuai.carnation.distribution.ApplicationLoader;
import com.sankuai.carnation.distribution.common.acl.AmazonS3AclService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService;
import com.sankuai.meituan.org.openapi.iface.IEmpRemoteService;
import com.sankuai.testapi.sdk.helper.GeneralizedCallHelper;
import com.sankuai.testapi.sdk.utils.RegisterMockUtil;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 接口级单元测试生成工具testAPI使用文档：
 * https://km.sankuai.com/collabpage/**********
 */
@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApplicationLoader.class)
public class BaseSpringTest {

    static {
        RegisterMockUtil.mock();
    }

    @MockBean(name = "requisitionQueryService")
    protected RequisitionQueryService requisitionQueryService;
    @MockBean(name = "processService")
    protected ProcessService processService;
    @MockBean(name = "iEmpRemoteService")
    protected IEmpRemoteService iEmpRemoteService;
    @MockBean(name = "amazonS3AclService")
    protected AmazonS3AclService amazonS3AclService;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        GeneralizedCallHelper.finish();
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {
    }
}