package base;

import com.sankuai.carnation.distribution.ApplicationLoader;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.service.impl.PrivateLiveAnchorDistributorGroupDomainServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;

import javax.annotation.Resource;

@SpringBootTest(classes = ApplicationLoader.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class BaseTest extends AbstractTestNGSpringContextTests {
}
