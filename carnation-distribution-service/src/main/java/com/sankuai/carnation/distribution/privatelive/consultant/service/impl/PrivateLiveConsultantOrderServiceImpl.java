package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.idmapper.service.DealMapperService;
import com.meituan.beauty.idmapper.service.UserMapperService;
import com.meituan.beauty.user.dto.UserDto;
import com.sankuai.carnation.distribution.common.acl.UserAclService;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderIntentionPageQueryRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantOrderInfoDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantOrderNumDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantOrderRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveUserOrderFromWxDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveUserOrderFromWxRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantOrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderBizCodeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderPinTuanStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/1/10
 * @Description:
 */
@Service
@Slf4j
public class PrivateLiveConsultantOrderServiceImpl implements PrivateLiveConsultantOrderService {

    @Autowired
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    @Autowired
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Autowired
    private PrivateLiveConsultantTaskRepository consultantTaskRepository;

    @Autowired
    private UserAclService userAclService;

    @Autowired
    private UserMapperService userMapperService;

    @Autowired
    private DealMapperService dealMapperService;

    @Override
    public RemoteResponse<PrivateLiveConsultantOrderNumDTO> queryConsultantOrderNum(long consultantTaskId) {
        try {
            List<PrivateLiveOrderIntentionResult> orderIntentionResultList = orderIntentionResultRepository.queryByTaskId(consultantTaskId);
            PrivateLiveConsultantOrderNumDTO consultantOrderNumDTO = new PrivateLiveConsultantOrderNumDTO();
            int unPaidNum = 0;
            int paidNum = 0;
            int refundNum = 0;
            int verifyNum = 0;
            for (PrivateLiveOrderIntentionResult orderIntentionResult : orderIntentionResultList) {
                ConsultantOrderStatusEnum statusEnum = ConsultantOrderStatusEnum.from(OrderStatusEnum.from(orderIntentionResult.getStatus()));
                switch (statusEnum) {
                    case UNPAID:
                        unPaidNum++;
                        break;
                    case PAID:
                        paidNum++;
                        break;
                    case REFUND:
                        refundNum++;
                        break;
                    case VERIFY:
                        verifyNum++;
                        break;
                }
            }
            consultantOrderNumDTO.setUnPaidNum(unPaidNum);
            consultantOrderNumDTO.setPaidNum(paidNum);
            consultantOrderNumDTO.setRefundNum(refundNum);
            consultantOrderNumDTO.setVerifyNum(verifyNum);
            return RemoteResponse.success(consultantOrderNumDTO);
        } catch (Exception e) {
            log.error("[PrivateLiveConsultantOrderService].queryConsultantOrderNum error, consultantTaskId is {}, exception is", consultantTaskId, e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @Override
    public PaginationRemoteResponse<PrivateLiveConsultantOrderInfoDTO> queryConsultantOrderInfo(PrivateLiveConsultantOrderRequest request) {
        try {
            PrivateLiveOrderIntentionPageQueryRequest queryRequest = new PrivateLiveOrderIntentionPageQueryRequest();
            long consultantTaskId = request.getConsultantTaskId();
            queryRequest.setConsultantTaskId(consultantTaskId);
            String keyWord = request.getKeyWord();
            if (StringUtils.isNotEmpty(keyWord)) {
                List<Long> dpUserIdList = Lists.newArrayList();
                List<Long> mtUserIdList = Lists.newArrayList();

                // 数字19位或者24位，查询条件为订单号
                if (keyWord.matches("\\d{19}")) {
                    queryRequest.setLongOrderId(Long.parseLong(keyWord));
                }
                if (keyWord.matches("\\d{24}")) {
                    queryRequest.setOrderId(keyWord);
                }

                // 数字11位，查询条件为手机号
                if (keyWord.matches("^1\\d{10}$")) {
                    UserDto dpUserDto = userAclService.getUserByMobile(PlatformEnum.DP.getCode(), keyWord);
                    if (dpUserDto != null) {
                        dpUserIdList.add(dpUserDto.getUserId());
                    }
                    UserDto mtUserDto = userAclService.getUserByMobile(PlatformEnum.MT.getCode(), keyWord);
                    if (mtUserDto != null) {
                        mtUserIdList.add(mtUserDto.getUserId());
                    }
                }

                // 其余条件为微信昵称模糊查询
                List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.searchByNickName(keyWord, consultantTaskId);
                List<Long> wxMtUserIdList = userIntentionResultList.stream().filter(userIntentionResult -> userIntentionResult.getUserId() != 0L)
                        .distinct()
                        .map(PrivateLiveUserIntentionResult::getUserId).collect(Collectors.toList());
                mtUserIdList.addAll(wxMtUserIdList);
                dpUserIdList.addAll(userMapperService.mt2dp(wxMtUserIdList).values());

                queryRequest.setDpUserIdList(dpUserIdList);
                queryRequest.setMtUserIdList(mtUserIdList);

                if (StringUtils.isEmpty(queryRequest.getOrderId()) &&
                        (queryRequest.getLongOrderId() == null || queryRequest.getLongOrderId() <= 0L) &&
                        CollectionUtils.isEmpty(queryRequest.getDpUserIdList()) &&
                        CollectionUtils.isEmpty(queryRequest.getMtUserIdList())
                ) {
                    return PaginationRemoteResponse.success(null, 0);
                }
            }
            Long mtUserId = request.getMtUserId();
            if (mtUserId != null && mtUserId > 0L) {
                queryRequest.setMtUserIdList(Lists.newArrayList(mtUserId));
                Long dpUserId = userMapperService.mt2dp(mtUserId);
                if (dpUserId != null && dpUserId > 0L) {
                    queryRequest.setDpUserIdList(Lists.newArrayList(dpUserId));
                }
            }

            queryRequest.setStatusList(OrderStatusEnum.from(ConsultantOrderStatusEnum.from(request.getStatus())));
            queryRequest.setPageNo(request.getPageNo());
            queryRequest.setPageSize(request.getPageSize());

            List<PrivateLiveOrderIntentionResult> orderIntentionResultList = orderIntentionResultRepository.pageQuery(queryRequest);

            List<PrivateLiveConsultantOrderInfoDTO> consultantOrderInfoDTOList = convertToConsultantOrderInfo(orderIntentionResultList);

            long count = orderIntentionResultRepository.getCount(queryRequest);
            return PaginationRemoteResponse.success(consultantOrderInfoDTOList, count);
        } catch (Exception e) {
            log.error("[PrivateLiveConsultantOrderService].queryConsultantOrderInfo error, request is {}, exception is", JSON.toJSONString(request), e);
            return PaginationRemoteResponse.failure(e.getMessage());
        }
    }

    private List<PrivateLiveConsultantOrderInfoDTO> convertToConsultantOrderInfo(List<PrivateLiveOrderIntentionResult> orderIntentionResultList) {
        List<PrivateLiveConsultantOrderInfoDTO> consultantOrderInfoDTOList = Lists.newArrayList();
        for (PrivateLiveOrderIntentionResult orderIntentionResult : orderIntentionResultList) {
            PrivateLiveConsultantOrderInfoDTO consultantOrderInfoDTO = new PrivateLiveConsultantOrderInfoDTO();
            consultantOrderInfoDTO.setLongOrderId(orderIntentionResult.getLongOrderId());
            consultantOrderInfoDTO.setOrderId(orderIntentionResult.getOrderId());
            // 团购商品id区分美团、点评，泛商品不区分
            long mtProductId = orderIntentionResult.getProductId();
            if (orderIntentionResult.getProductType() == ProductTypeEnum.TUAN_DEAL.getCode() && orderIntentionResult.getPlatform() == PlatformEnum.DP.getCode()) {
                mtProductId = (long) dealMapperService.dp2mt((int) mtProductId);
            }
            consultantOrderInfoDTO.setMtProductId(mtProductId);
            consultantOrderInfoDTO.setProductType(orderIntentionResult.getProductType());
            consultantOrderInfoDTO.setMtUserId(orderIntentionResult.getPlatform() == PlatformEnum.DP.getCode() ? userMapperService.dp2mt(orderIntentionResult.getUserId()) : orderIntentionResult.getUserId());
            consultantOrderInfoDTO.setTotalMoney(orderIntentionResult.getTotalMoney());
            consultantOrderInfoDTO.setProductNum(orderIntentionResult.getQuantity());
            consultantOrderInfoDTO.setAddTime(orderIntentionResult.getAddTime());
            consultantOrderInfoDTO.setStatus(ConsultantOrderStatusEnum.from(OrderStatusEnum.from(orderIntentionResult.getStatus())).getCode());
            if (OrderBizCodeEnum.PREPAY_PINTUAN_ORDER.getBizCode().equals(orderIntentionResult.getBizCode())) {
                consultantOrderInfoDTO.setOrderGroupStatus(StringUtils.isNotBlank(orderIntentionResult.getField2())
                        ? Integer.parseInt(orderIntentionResult.getField2()) : OrderPinTuanStatusEnum.OPEN.getCode());
            }
            consultantOrderInfoDTOList.add(consultantOrderInfoDTO);
        }
        return consultantOrderInfoDTOList;
    }

    @Override
    public RemoteResponse<Map<String, List<PrivateLiveUserOrderFromWxDTO>>> queryUserOrderListAndIntentConsult(PrivateLiveUserOrderFromWxRequest request) {

        // 参数校验
        if (null == request || StringUtils.isBlank(request.getLiveId()) || CollectionUtils.isEmpty(request.getWxIdList())) {
            return RemoteResponse.fail("Invalid request parameters");
        }

        if (request.getWxIdList().size() > 50) {
            return RemoteResponse.fail("wxIdList size must less than 50");
        }

        // 根据wxid + liveid 找到mtuserid
        Map<String, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndWxIds(request.getLiveId(), request.getWxIdList());
        if (MapUtils.isEmpty(userIntentionResultMap)) {
            return RemoteResponse.success(Collections.EMPTY_MAP);
        }


        Map<Long, String> mtUserId2WxIdMap =
                userIntentionResultMap.entrySet().stream()
                        .filter(entry -> null != entry.getValue().getUserId() && entry.getValue().getUserId() > 0 && StringUtils.isNotBlank(entry.getValue().getWxId()))
                        .collect(Collectors.toMap(entry -> entry.getValue().getUserId(), entry -> entry.getKey(), (a, b) -> a));
        List<Long> mtUserIdList = Lists.newArrayList(mtUserId2WxIdMap.keySet());
        List<PrivateLiveOrderIntentionResult> privateLiveOrderIntentionResults = orderIntentionResultRepository.queryOrderByLiveIdAndUserIds(request.getLiveId(), mtUserIdList);
        if (CollectionUtils.isEmpty(privateLiveOrderIntentionResults)) {
            return RemoteResponse.success(Collections.EMPTY_MAP);
        }

        List<Long> taskIdList = privateLiveOrderIntentionResults.stream().map(PrivateLiveOrderIntentionResult::getConsultantTaskId).distinct().collect(Collectors.toList());

        // 查询咨询师名称
        List<PrivateLiveConsultantTask> taskList = consultantTaskRepository.batchLoadByTaskIds(taskIdList);
        Map<Long, PrivateLiveConsultantTask> taskMap = taskList.stream().collect(Collectors.toMap(PrivateLiveConsultantTask::getId, Function.identity()));


        List<PrivateLiveUserOrderFromWxDTO> userOrderFromWxDTOList = privateLiveOrderIntentionResults.stream().map(
                orderIntent -> {
                    PrivateLiveUserOrderFromWxDTO userOrderFromWxDTO = new PrivateLiveUserOrderFromWxDTO();
                    userOrderFromWxDTO.setMtUserId(orderIntent.getUserId());
                    userOrderFromWxDTO.setWxId(mtUserId2WxIdMap.get(orderIntent.getUserId()));
                    userOrderFromWxDTO.setOrderId(orderIntent.getOrderId());
                    userOrderFromWxDTO.setTotalMoney(orderIntent.getTotalMoney());
                    userOrderFromWxDTO.setConsultantTaskId(orderIntent.getConsultantTaskId());
                    if (null != taskMap.get(orderIntent.getConsultantTaskId())) {
                        userOrderFromWxDTO.setConsultantNickName(taskMap.get(orderIntent.getConsultantTaskId()).getNickname());
                        userOrderFromWxDTO.setConsultantShareName(taskMap.get(orderIntent.getConsultantTaskId()).getShareName());
                    }
                    userOrderFromWxDTO.setProductType(orderIntent.getProductType());
                    userOrderFromWxDTO.setMtProductId(orderIntent.getProductId());
                    userOrderFromWxDTO.setStatus(orderIntent.getStatus());
                    return userOrderFromWxDTO;
                }
        ).collect(Collectors.toList());

        Map<String, List<PrivateLiveUserOrderFromWxDTO>> wxIdToOrderListMap = userOrderFromWxDTOList.stream().collect(Collectors.groupingBy(PrivateLiveUserOrderFromWxDTO::getWxId));

        // 根据mtuserid + liveid找到订单明细
        return RemoteResponse.success(wxIdToOrderListMap);
    }
}
