package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @author: wuweizhen
 * @Date: 2023/6/5 10:17
 * @Description:
 */
@Slf4j
public class DateUtils {

    public static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_PATTERN_NO_HOUR_MINUTE_SECOND = "yyyy-MM-dd";

    /**
     * 通过时间秒毫秒数判断两个时间的间隔(秒）
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long differentSecondByMillisecond(Date date1, Date date2) {
        long second = (date2.getTime() - date1.getTime()) / 1000L;
        return second;
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔(毫秒）
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long differentMsByMillisecond(Date date1, Date date2) {
        return date2.getTime() - date1.getTime();
    }

    public static int differentSecondToTomorrow(Date date) {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_YEAR, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        Date tomorrow = c.getTime();
        return (int) ((tomorrow.getTime() - date.getTime()) / 1000);
    }

    public static int differentDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);
        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) {
            int timeDistance = 0;
            for (int i = year2; i < year1; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) {
                    timeDistance += 366;
                } else {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day1 - day2);
        } else {
            return day1 - day2;
        }
    }


    /**
     * 毫秒转化为日期
     */
    public static Date toDate(long sec) {
        return new Date(sec);
    }

    /**
     * 获取指定日期的0点
     *
     * @param date
     * @return
     */
    public static Date getZeroDate(Date date) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils.getZeroDate(java.util.Date)");
        if (null == date) {
            return date;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date addDay(Date date, int day) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, day);
        return c.getTime();
    }

    public static Date addHour(Date date, int hour) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils.addHour(java.util.Date,int)");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.HOUR, hour);
        return c.getTime();
    }

    public static Date addMinute(Date date, int minute) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MINUTE, minute);
        return c.getTime();
    }

    public static Date addSecond(Date date, int second) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils.addSecond(java.util.Date,int)");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.SECOND, second);
        return c.getTime();
    }

    public static String format(Date date) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils.format(java.util.Date)");
        return format(date, DEFAULT_DATE_PATTERN);
    }

    public static String simpleFormat(Date date) {
        return format(date, DATE_PATTERN_NO_HOUR_MINUTE_SECOND);
    }

    public static String format(Date date, String pattern) {
        if (date == null || StringUtils.isBlank(pattern)) {
            return "";
        }
        try {
            return new SimpleDateFormat(pattern).format(date);
        } catch (Exception e) {
            log.error("[DateUtils.format], date: {}, pattern: {}", JSONObject.toJSONString(date),pattern, e);
            return "";
        }
    }

    /**
     * 获取日期列表中最早的日期
     * @param dateList
     * @return
     */
    public static Date getEarliestDate(List<Date> dateList) {
        if (dateList == null || CollectionUtils.isEmpty(dateList)) {
            return null;
        }
        Date earliestDate = new Date();
        for (Date date : dateList) {
            if (date.before(earliestDate)) {
                earliestDate = date;
            }
        }
        return earliestDate;
    }

    /**
     * 获取日期列表中最晚的日期
     * @param dateList
     * @return
     */
    public static Date getLatestDate(List<Date> dateList) {
        if (dateList == null || CollectionUtils.isEmpty(dateList)) {
            return null;
        }
        Date latestDate = dateList.get(0);
        for (Date date : dateList) {
            if (date.after(latestDate)) {
                latestDate = date;
            }
        }
        return latestDate;
    }

    public static List<Date> getZeroDateEveryDay(Date startTime, Date endTime) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils.getZeroDateEveryDay(java.util.Date,java.util.Date)");
        List<Date> dateList = new ArrayList<>();
        Date date = getZeroDate(startTime);
        while (differentMsByMillisecond(date, endTime) >= 0) {
            dateList.add(date);
            date = addDay(date, 1);
        }
        return dateList;
    }

    public static Date getDateByStr(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.DEFAULT_DATE_PATTERN);
            return sdf.parse(dateStr);
        }catch (Exception e){
            log.error("日期解析异常.dateStr:{}.errorMsg:{}",dateStr,e.getMessage(), e);
            return null;
        }
    }
}
