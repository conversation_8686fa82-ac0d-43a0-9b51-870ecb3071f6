package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.general.category.response.Response;
import com.dianping.general.category.service.GeneralCategoryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.distributionplan.acl.GeneralCategoryAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralCategoryTree;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCategory;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@Service
@Slf4j
public class GeneralCategoryAclImpl implements GeneralCategoryAcl {
    @Resource
    private GeneralCategoryService generalCategoryService;

    private final static String CAT_TYPE = GeneralCategoryAcl.class.getSimpleName();


    @Override
    public List<GeneralCategoryTree> getCategoryTreeByTradeType(Integer tradeType) {
        List<GeneralCategoryTree> generalCategoryTreeList = Lists.newArrayList();
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "getCategoryTreeByTradeType");
        try {
            if (Objects.isNull(tradeType)) {
                throw new RuntimeException("IllegalParam");
            }
            Response<String> response = generalCategoryService.getCategoryTree(tradeType);

            if (Objects.isNull(response)) {
                throw new DistributionPlanException("根据交易类型查询类目树接口返回为空");
            }
            if (response.isFail()) {
                log.error("GeneralCategoryAcl.getCategoryTreeByTradeType invoke generalCategoryService.getCategoryTree failed, tradeType:{}, response:{}", tradeType, JSON.toJSONString(response));
                throw new RuntimeException("根据交易类型查询类目树接口返回失败:" + response.getResultMessage());
            }
            if (Objects.isNull(response.getResult())) {
                log.error("generalCategoryService.getCategoryTree failed, tradeType:{}, response:{}", tradeType, JSON.toJSONString(response));
                throw new RuntimeException("根据交易类型查询类目树接口返回失败");
            }
            String result = response.getResult();
            generalCategoryTreeList = JsonUtil.toList(result, GeneralCategoryTree.class);
            transaction.setSuccessStatus();
            return generalCategoryTreeList;
        } catch (Exception e) {
            log.error("GeneralCategoryAcl.getCategoryTreeByTradeType failed, tradeType:{}", tradeType, e);
            transaction.setStatus(e);
            return generalCategoryTreeList;
        } finally {
            transaction.complete();
        }
    }

    @Override
    public Map<ProductCategory, List<ProductCategory>> getCategoryMap(Integer tradeType) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "getCategoryTreeByTradeType");
        try {
            if (Objects.isNull(tradeType)) {
                throw new IllegalArgumentException("IllegalParam");
            }
            List<GeneralCategoryTree> generalCategoryTreeList = Lists.newArrayList();

            Response<String> response = generalCategoryService.getCategoryTree(tradeType);
            if (Objects.isNull(response)) {
                throw new DistributionPlanException("根据交易类型查询类目树接口返回为空");
            }
            if (response.isFail()) {
                log.error("GeneralCategoryAcl.getCategoryMap invoke generalCategoryService.getCategoryTree failed, tradeType:{}, response:{}", tradeType, JSON.toJSONString(response));
                throw new RuntimeException("根据交易类型查询类目树接口返回失败:" + response.getResultMessage());
            }
            if (Objects.isNull(response.getResult())) {
                log.error("generalCategoryService.getCategoryMap failed, tradeType:{}, response:{}", tradeType, JSON.toJSONString(response));
                throw new RuntimeException("根据交易类型查询类目树接口返回为空");
            }
            String result = response.getResult();
            generalCategoryTreeList = JsonUtil.toList(result, GeneralCategoryTree.class);

            Map<ProductCategory, List<ProductCategory>> categoryMap = Maps.newHashMap();
            generalCategoryTreeList.forEach(generalCategoryTree -> {
                if (null == generalCategoryTree || CollectionUtils.isEmpty(generalCategoryTree.getChildList())) {
                    return;
                }
                ProductCategory parentCategory = ProductCategory.builder()
                        .platCategoryId(generalCategoryTree.getPlatformCategoryId())
                        .categoryName(generalCategoryTree.getCategoryName())
                        .build();
                List<ProductCategory> childCategoryList = generalCategoryTree.getChildList().stream()
                        .map(child -> ProductCategory.builder()
                                .platCategoryId(child.getPlatformCategoryId())
                                .categoryName(child.getCategoryName()).build()).collect(Collectors.toList());
                categoryMap.put(parentCategory, childCategoryList);
            });
            return categoryMap;
        } catch (Exception e) {
            log.error("GeneralCategoryAcl.getCategoryTreeByTradeType failed, tradeType:{}", tradeType, e);
            transaction.setStatus(e);
            return Maps.newHashMap();
        } finally {
            transaction.complete();
        }
    }
}