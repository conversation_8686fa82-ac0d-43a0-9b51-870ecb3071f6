package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionShopRowBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/10/20
 * @Description:
 */
@Slf4j
public class GroundPromotionShopRowListener implements ReadListener<GroundPromotionShopRowBO> {

    @Getter
    private List<GroundPromotionShopRowBO> cachedDataList = Lists.newArrayList();

    @Override
    public void invoke(GroundPromotionShopRowBO groundPromotionShopRowBO, AnalysisContext analysisContext) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionShopRowListener.invoke(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionShopRowBO,com.alibaba.excel.context.AnalysisContext)");
        if (!checkFormat(groundPromotionShopRowBO)) {
            groundPromotionShopRowBO.setLoadResult(ExcelParseResultEnum.FAIL.getCode());
            groundPromotionShopRowBO.setFailReason("上传格式不正确");
        }
        if (groundPromotionShopRowBO.getDealIdList() == null) {
            groundPromotionShopRowBO.setDealIdList("0");
        }
        cachedDataList.add(groundPromotionShopRowBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionShopRowListener.doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext)");
    }

    private boolean checkFormat(GroundPromotionShopRowBO groundPromotionShopRowBO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionShopRowListener.checkFormat(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionShopRowBO)");
        try {
            Long.parseLong(groundPromotionShopRowBO.getDpShopId());
            if (groundPromotionShopRowBO.getDealIdList() != null) {
                List<String> dealIdList = Lists.newArrayList(groundPromotionShopRowBO.getDealIdList().split(","));
                for (String dealId : dealIdList) {
                    Long.parseLong(dealId);
                }
            }
        }catch (Exception e) {
            log.error("GroundPromotionShopRowStrBO param is invalid, param is {}, exception is {}", JSONObject.toJSON(groundPromotionShopRowBO), e);
            return false;
        }
        return true;
    }
}
