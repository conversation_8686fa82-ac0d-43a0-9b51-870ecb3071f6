package com.sankuai.carnation.distribution.privatelive.consultant.migrate;

import com.meituan.nibscp.unity.migration.framework.pojo.BizRequest;
import com.meituan.nibscp.unity.migration.framework.pojo.ParamParseDTO;
import com.meituan.nibscp.unity.migration.framework.spi.SingleParamParser;
import org.springframework.stereotype.Service;

@Service
public class QueryRankByConditionParamParser implements SingleParamParser {
    @Override
    public ParamParseDTO parse(String migrationCode, String moduleName, BizRequest bizRequest) {
        ParamParseDTO paramParse = new ParamParseDTO();
        return paramParse;
    }
}