package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionMallRowBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/10/23
 * @Description:
 */
@Slf4j
public class GroundPromotionMallRowListener implements ReadListener<GroundPromotionMallRowBO> {

    @Getter
    private List<GroundPromotionMallRowBO> cachedDataList = Lists.newArrayList();

    @Override
    public void invoke(GroundPromotionMallRowBO groundPromotionMallRowBO, AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionMallRowListener.invoke(GroundPromotionMallRowBO,AnalysisContext)");
        if (!checkFormat(groundPromotionMallRowBO)) {
            groundPromotionMallRowBO.setLoadResult(ExcelParseResultEnum.FAIL.getCode());
            groundPromotionMallRowBO.setFailReason("上传格式不正确");
        }
        if (groundPromotionMallRowBO.getDealIdList() == null) {
            groundPromotionMallRowBO.setDealIdList("0");
        }
        cachedDataList.add(groundPromotionMallRowBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionMallRowListener.doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext)");
    }

    private boolean checkFormat(GroundPromotionMallRowBO groundPromotionMallRowBO) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionMallRowListener.checkFormat(GroundPromotionMallRowBO)");
        try {
            Long.parseLong(groundPromotionMallRowBO.getDpShopId());
            if (groundPromotionMallRowBO.getDealIdList() != null) {
                List<String> dealIdList = Lists.newArrayList(groundPromotionMallRowBO.getDealIdList().split(","));
                for (String dealId : dealIdList) {
                    Long.parseLong(dealId);
                }
            }
        }catch (Exception e) {
            log.error("GroundPromotionMallRowBO param is invalid, param is {}, exception is {}", JSONObject.toJSON(groundPromotionMallRowBO), e);
            return false;
        }
        return true;
    }
}
