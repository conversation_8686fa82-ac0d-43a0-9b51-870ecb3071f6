package com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.service.model.MerchantChannelDistributionPlanOperateContext;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.utils.DateUtils;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanSubjectDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanCreateRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanQueryRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanCreator;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanOperator;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanSubjectField;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

import static com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.MerchantChannelDistributionPlanServiceImpl.END_TIME_OF_PERMANENT_EFFECTIVE;

/**
 * <AUTHOR>
 */
@Component
public class RecreateDistributionPlanStrategy implements MerchantChannelDistributionPlanOperateStrategy {

    @Resource
    private DistributionPlanCommandService distributionPlanCommandService;

    @Resource
    private DistributionPlanQueryService distributionPlanQueryService;

    @Resource
    private RedisLockService redisLockService;

    @PostConstruct
    public void init() {
        MerchantChannelDistributionPlanOperateStrategyFactory.register(MerchantChannelDistributionPlanOperateTypeEnum.RECREATE, this);
    }

    @Override
    public void validatePlanCanOperate(MerchantChannelDistributionPlanOperateContext context) {
        if (context.getDistributionPlan().getBeginTime().before(context.getDistributionPlan().getEndTime())
                && context.getDistributionPlan().getEndTime().getTime() >= context.getOperateDate().getTime()) {
            throw new IllegalArgumentException("当前商品佣金配置未结束，无法重新上线");
        }

        DistributionPlanQueryRequest request = DistributionPlanQueryRequest.builder()
                .subjectFieldList(Lists.newArrayList(buildSubjectField(context.getDistributionPlan().getSubject())))
                .channel(context.getDistributionPlan().getChannel())
                .sceneCode(context.getDistributionPlan().getSceneCode())
                .build();
        Response<List<DistributionPlanDTO>> distributionPlanDtoResponse = distributionPlanQueryService.queryLatestDistributionPlan( request);
        if (distributionPlanDtoResponse.respFailed()) {
            throw new DistributionPlanException(distributionPlanDtoResponse.getMessage());
        }
        if (CollectionUtils.isEmpty(distributionPlanDtoResponse.getData())) {
            return;
        }
        DistributionPlanDTO distributionPlan = distributionPlanDtoResponse.getData().stream().max(Comparator.comparing(DistributionPlanDTO::getBeginTime)).get();
        if (distributionPlan.getBeginTime().getTime() >= context.getOperateDate().getTime()) {
            throw new IllegalArgumentException("当前商品存在待生效的佣金配置，无法重新上线");
        }
        Date todayNight = DateUtils.getToDayNight(context.getOperateDate());
        if (distributionPlan.getEndTime().getTime() > todayNight.getTime()) {
            throw new IllegalArgumentException("当前商品存在生效中的佣金配置，无法重新上线");
        }
    }

    @Override
    public void operate(MerchantChannelDistributionPlanOperateContext context) {
        DistributionPlanCreateRequest request = DistributionPlanCreateRequest.builder()
                .sceneCode(context.getDistributionPlan().getSceneCode())
                .channel(context.getDistributionPlan().getChannel())
                .planType(context.getDistributionPlan().getPlanType())
                .subjectField(buildSubjectField(context.getDistributionPlan().getSubject()))
                .creator(DistributionPlanCreator.builder()
                        .creatorId(String.valueOf(context.getMtCustomerId())).build())
                .operator(DistributionPlanOperator.builder().operatorId(String.valueOf(context.getMtAccountId())).build())
                .beginTime(DateUtils.getNextDayMorning(context.getOperateDate()).getTime())
                .endTime(END_TIME_OF_PERMANENT_EFFECTIVE)
                .commissionRateMap(context.getDistributionPlan().getCommissionRateMap())
                .build();
        Response response = redisLockService.tryLock("CreateMerchantDistributionPlan_" + context.getDistributionPlan().getSubject().getField1(),
                () -> distributionPlanCommandService.createDistributionPlan(request));
        if (response.respFailed()) {
            throw new DistributionPlanException(response.getMessage());
        }
    }

    private DistributionPlanSubjectField buildSubjectField(DistributionPlanSubjectDTO subject) {
        return DistributionPlanSubjectField.builder()
                .subjectType(subject.getSubjectType())
                .field1(subject.getField1())
                .field2(subject.getField2())
                .field3(subject.getField3()).build();
    }
}