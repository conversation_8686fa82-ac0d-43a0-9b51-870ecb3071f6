package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.cat.Cat;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPTCodeCacheBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundQrLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.carnation.distribution.groundpromotion.constants.GroundPromotionConstants.SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/20
 * @Description:
 */
@Service
@Slf4j
public class RedisAcl {

    public static final int SHELF_TYPE = 1;
    public static final int POI_TYPE = 2;
    public static final int ANCHOR_TYPE = 3;

    public static final int QUANQUAN_GROUP = 4;

    public static final int DZ_GROUP = 5;
    @Autowired
    private RedisStoreClient redisStoreClient;

    Pattern patternAnchorId = Pattern.compile("anchorid=(\\d+)");
    Pattern patternUtmSource = Pattern.compile("utm_source=(\\S+)");
    Pattern patternDynamicChannelId = Pattern.compile("channelId=(\\S+)");


    /**
     * 地推兼职扫码记录写入缓存
     *
     * @param groundPartTimeQrLog
     * @param expireTime
     */
    public void setGroundPartTimeQrLogCache(GroundPartTimeQrLog groundPartTimeQrLog, int expireTime) {

        int groundType = 0;
        long bizId = 0;
        if (groundPartTimeQrLog.getShelfId() != null && groundPartTimeQrLog.getShelfId() > 0) {
            groundType = SHELF_TYPE;
            bizId = groundPartTimeQrLog.getShelfId();
        } else if (groundPartTimeQrLog.getPoiId() != null && groundPartTimeQrLog.getPoiId() > 0) {
            groundType = POI_TYPE;
            bizId = groundPartTimeQrLog.getPoiId();
        }

        StoreKey storeKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, groundType, bizId, groundPartTimeQrLog.getUserId(), groundPartTimeQrLog.getPlatform());
        GroundPromotionPTCodeCacheBO groundPromotionPTCodeCacheBO = GroundPromotionPTCodeCacheBO.builder()
                .code(groundPartTimeQrLog.getCode())
                .scanTime(groundPartTimeQrLog.getScanTime())
                .bizType(groundPartTimeQrLog.getBizType())
                .id(groundPartTimeQrLog.getId())
                .build();
        redisStoreClient.set(storeKey, groundPromotionPTCodeCacheBO, expireTime);

        anchorId(groundPartTimeQrLog, expireTime);
        utmSource(groundPartTimeQrLog, expireTime);
        dynamicChannelId(groundPartTimeQrLog, expireTime);


    }

    private void anchorId(GroundPartTimeQrLog groundPartTimeQrLog, int expireTime) {
        if (StringUtils.isNotEmpty(groundPartTimeQrLog.getShelfUrl())) {
            Matcher matcher = patternAnchorId.matcher(groundPartTimeQrLog.getShelfUrl());
            if (matcher.find()) {
                long anchorId = Long.parseLong(matcher.group(1));
                StoreKey anchorIdStoreKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, ANCHOR_TYPE, anchorId, groundPartTimeQrLog.getUserId(), groundPartTimeQrLog.getPlatform());
                GroundPromotionPTCodeCacheBO cacheBO = GroundPromotionPTCodeCacheBO.builder()
                        .code(groundPartTimeQrLog.getCode())
                        .scanTime(groundPartTimeQrLog.getScanTime())
                        .bizType(groundPartTimeQrLog.getBizType())
                        .id(groundPartTimeQrLog.getId())
                        .build();
                redisStoreClient.set(anchorIdStoreKey, cacheBO, expireTime);

            }
        }
    }

    private void utmSource(GroundPartTimeQrLog groundPartTimeQrLog, int expireTime) {
        if (StringUtils.isNotEmpty(groundPartTimeQrLog.getShelfUrl())) {
            Matcher matcher = patternUtmSource.matcher(groundPartTimeQrLog.getShelfUrl());
            if (matcher.find()) {
                StoreKey anchorIdStoreKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, QUANQUAN_GROUP, matcher.group(1), groundPartTimeQrLog.getUserId(), groundPartTimeQrLog.getPlatform());
                GroundPromotionPTCodeCacheBO cacheBO = GroundPromotionPTCodeCacheBO.builder()
                        .code(groundPartTimeQrLog.getCode())
                        .scanTime(groundPartTimeQrLog.getScanTime())
                        .bizType(groundPartTimeQrLog.getBizType())
                        .id(groundPartTimeQrLog.getId())
                        .build();
                redisStoreClient.set(anchorIdStoreKey, cacheBO, expireTime);

            }
        }
    }

    private void dynamicChannelId(GroundPartTimeQrLog groundPartTimeQrLog, int expireTime) {
        if (StringUtils.isNotEmpty(groundPartTimeQrLog.getShelfUrl())) {
            Matcher matcher = patternDynamicChannelId.matcher(groundPartTimeQrLog.getShelfUrl());
            if (matcher.find()) {
                StoreKey anchorIdStoreKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, DZ_GROUP, matcher.group(1), groundPartTimeQrLog.getUserId(), groundPartTimeQrLog.getPlatform());
                GroundPromotionPTCodeCacheBO cacheBO = GroundPromotionPTCodeCacheBO.builder()
                        .code(groundPartTimeQrLog.getCode())
                        .scanTime(groundPartTimeQrLog.getScanTime())
                        .bizType(groundPartTimeQrLog.getBizType())
                        .id(groundPartTimeQrLog.getId())
                        .build();
                redisStoreClient.set(anchorIdStoreKey, cacheBO, expireTime);

            }
        }
    }

    /**
     * 获取地推兼职扫码记录缓存
     *
     * @param platform {@link com.sankuai.carnation.distribution.intention.enums.PlatformEnum}
     * @return
     */
    public GroundPromotionPTCodeCacheBO getGroundPartTimeQrLogCache(int groundType, long bizId, long userId, int platform) {
        StoreKey storeKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, groundType, bizId, userId, platform);
        GroundPromotionPTCodeCacheBO groundPromotionPTCodeCacheBO = redisStoreClient.get(storeKey);
        return groundPromotionPTCodeCacheBO;
    }
    public GroundPromotionPTCodeCacheBO getGroundPartTimeQrLogCache(int groundType, String bizId, long userId, int platform) {
        StoreKey storeKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, groundType, bizId, userId, platform);
        GroundPromotionPTCodeCacheBO groundPromotionPTCodeCacheBO = redisStoreClient.get(storeKey);
        return groundPromotionPTCodeCacheBO;
    }

    public Map<Long, GroundPromotionPTCodeCacheBO> batchGroundPartTimeQrLogCache(int groundType, List<Long> shelfIds, long userId, int platform) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.RedisAcl.batchGroundPartTimeQrLogCache(int,java.util.List,long,int)");
        List<StoreKey> storeKeys = shelfIds.stream().map(shelfId -> new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, groundType, shelfId, userId, platform)).collect(Collectors.toList());
        Map<StoreKey, GroundPromotionPTCodeCacheBO> map = redisStoreClient.multiGet(storeKeys);
        Map<Long, GroundPromotionPTCodeCacheBO> shelfIdMap = map.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> (Long) entry.getKey().getParams()[1],
                        Map.Entry::getValue
                ));
        return shelfIdMap;
    }

    public void setRedisStoreClient(RedisStoreClient redisStoreClient) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.RedisAcl.setRedisStoreClient(com.dianping.squirrel.client.impl.redis.RedisStoreClient)");
        redisStoreClient = redisStoreClient;
    }

    public void setGroundAnchorQrLogCache(BizDistributorBO bizDistributorBO, GroundQrLog groundQrLog, int expireTime) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.RedisAcl.setGroundAnchorQrLogCache(com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO,com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundQrLog,int)");
        long anchorId = Long.parseLong(bizDistributorBO.getBizId());

        StoreKey anchorIdStoreKey = new StoreKey(SQUIRREL_GROUND_QR_LOG_CACHE_CATEGORY, ANCHOR_TYPE, anchorId, groundQrLog.getUserId(), groundQrLog.getPlatform());
        GroundPromotionPTCodeCacheBO cacheBO = GroundPromotionPTCodeCacheBO.builder()
                .code(bizDistributorBO.getDistributorCode())
                .scanTime(new Date())
                .bizType(DistributorBizTypeEnum.fromCode(bizDistributorBO.getBizType()).getDesc())
                .id(groundQrLog.getId())
                .build();
        redisStoreClient.set(anchorIdStoreKey, cacheBO, expireTime);
    }
}
