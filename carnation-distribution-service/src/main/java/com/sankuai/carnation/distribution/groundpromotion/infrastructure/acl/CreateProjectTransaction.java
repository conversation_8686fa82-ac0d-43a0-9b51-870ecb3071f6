package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionProjectDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.OrgRequest;
import com.sankuai.carnation.distribution.groundpromotion.dto.ProjectDealInfoDTO;
import com.sankuai.carnation.distribution.groundpromotion.enums.AuditTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionScopeTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.OrgStatusEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.UserTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProject;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectAuditNode;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDeal;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.Org;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/15
 * @Description:
 */
@Slf4j
@Component
public class CreateProjectTransaction {

    @Autowired
    private GroundPromotionAuditNodeRepository groundPromotionAuditNodeRepository;

    @Autowired
    private OrgRepository orgRepository;

    @Autowired
    private GroundPromotionProjectRepository groundPromotionProjectRepository;

    @Autowired
    private GroundPromotionProjectDealRepository groundPromotionProjectDealRepository;

    @Transactional
    public RemoteResponse<Boolean> createProjectByCity(GroundPromotionProjectDTO groundPromotionProjectDTO) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreateProjectTransaction.createProjectByCity(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionProjectDTO)");
        try {
            // 插入根审批节点
            GroundPromotionProjectAuditNode auditNode = new GroundPromotionProjectAuditNode();
            auditNode.setParentId(0L);
            auditNode.setAuditNodeType(AuditTypeEnum.INIT_AUDIT.getCode());
            auditNode.setOperatorMis(groundPromotionProjectDTO.getCreatorMis());
            groundPromotionAuditNodeRepository.insert(auditNode);
            long auditId = auditNode.getId();

            // 根据业务线id查询管理员，插入子审批节点
            OrgRequest orgRequest = new OrgRequest();
            orgRequest.setUserType(Lists.newArrayList(UserTypeEnum.ADMIN.getCode()));
            orgRequest.setBuIdList(Lists.newArrayList(groundPromotionProjectDTO.getBuId()));
            orgRequest.setStatus(OrgStatusEnum.VALID.getCode());
            List<Org> orgList = orgRepository.query(orgRequest);
            List<String> adminMisIdList;
            if (CollectionUtils.isEmpty(orgList)) {
                adminMisIdList = Lists.newArrayList("chenhaoyang02", "zhangxing34");
            } else {
                adminMisIdList = orgList.stream()
                        .map(Org::getMisId)
                        .distinct()
                        .collect(Collectors.toList());
            }
            GroundPromotionProjectAuditNode childAuditNode = new GroundPromotionProjectAuditNode();
            childAuditNode.setParentId(auditId);
            childAuditNode.setAuditNodeType(AuditTypeEnum.IN_AUDIT.getCode());
            childAuditNode.setOperatorMis(String.join(",", adminMisIdList));
            groundPromotionAuditNodeRepository.insert(childAuditNode);

            // 将团单信息做汇总
            List<ProjectDealInfoDTO> dealInfoList = groundPromotionProjectDTO.getDealInfoList();
            Map<Long, List<ProjectDealInfoDTO>> dealInfoMap = dealInfoList.stream()
                    .collect(Collectors.groupingBy(ProjectDealInfoDTO::getShopId));

            List<ProjectDealInfoDTO> mergeDealInfoList = new ArrayList<>();
            for (List<ProjectDealInfoDTO> deals : dealInfoMap.values()) {
                ProjectDealInfoDTO mergeDealInfo = new ProjectDealInfoDTO();
                mergeDealInfo.setMallId(deals.get(0).getMallId());
                mergeDealInfo.setShopId(deals.get(0).getShopId());
                mergeDealInfo.setDealIdList(deals.stream()
                        .map(ProjectDealInfoDTO::getDealIdList)
                        .flatMap(Collection::stream)
                        .distinct()
                        .collect(Collectors.toList()));
                mergeDealInfo.setPoiType(deals.get(0).getPoiType());
                mergeDealInfoList.add(mergeDealInfo);
            }

            // 插入立项表
            GroundPromotionProject groundPromotionProject = new GroundPromotionProject();
            groundPromotionProject.setBuId(groundPromotionProjectDTO.getBuId());
            groundPromotionProject.setProjectName(groundPromotionProjectDTO.getProjectName());
            groundPromotionProject.setStartTime(groundPromotionProjectDTO.getStartTime());
            groundPromotionProject.setEndTime(groundPromotionProjectDTO.getEndTime());
            groundPromotionProject.setScopeType(groundPromotionProjectDTO.getScopeType());
            groundPromotionProject.setCityId(groundPromotionProjectDTO.getDpCityIdList().get(0));
            groundPromotionProject.setTemplateId(groundPromotionProjectDTO.getTemplateId());
            groundPromotionProject.setCreatorMis(groundPromotionProjectDTO.getCreatorMis());
            groundPromotionProject.setAuditNodeId(auditId);
            groundPromotionProject.setArrangementInfo(JSONObject.toJSONString(groundPromotionProjectDTO.getArrangementInfoList()));
            groundPromotionProject.setStatus(AuditTypeEnum.IN_AUDIT.getCode());
            groundPromotionProjectRepository.insert(groundPromotionProject);
            long projectId = groundPromotionProject.getId();

            // 插入团单表
            List<GroundPromotionProjectDeal> projectDealList = mergeDealInfoList.stream()
                    .map(dealInfo -> {
                        GroundPromotionProjectDeal projectDeal = new GroundPromotionProjectDeal();
                        projectDeal.setProjectId(projectId);
                        projectDeal.setScopeType(groundPromotionProjectDTO.getScopeType());
                        projectDeal.setMallId(dealInfo.getMallId());
                        projectDeal.setShopId(dealInfo.getShopId());
                        projectDeal.setDealId(StringUtils.join(dealInfo.getDealIdList(), ","));
                        if (groundPromotionProject.getScopeType() == GroundPromotionScopeTypeEnum.STANDARD_PRODUCT.getCode()) {
                            projectDeal.setProductType(ProductTypeEnum.STANDARD_PRODUCT.getCode());
                        } else {
                            projectDeal.setProductType(ProductTypeEnum.TUAN_DEAL.getCode());
                        }
                        return projectDeal;
                    }).collect(Collectors.toList());
            groundPromotionProjectDealRepository.batchInsert(projectDealList);
            return RemoteResponse.success(true);
        } catch (Exception e) {
            log.error("[createProjectByCity] error, request is {}, exception is", JSONObject.toJSONString(groundPromotionProjectDTO), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return RemoteResponse.fail(e.getMessage());
        }
    }
}
