package com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy;

import com.sankuai.carnation.distribution.distributionplan.service.model.MerchantChannelDistributionPlanOperateContext;

/**
 * <AUTHOR>
 */
public interface MerchantChannelDistributionPlanOperateStrategy {

    /**
     * 校验计划是否可操作
     *
     * @param context
     */
    void validatePlanCanOperate(MerchantChannelDistributionPlanOperateContext context);

    /**
     * 操作
     *
     * @param context
     */
    void operate(MerchantChannelDistributionPlanOperateContext context);
}