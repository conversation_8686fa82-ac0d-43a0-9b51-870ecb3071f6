package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.dz.coupon.base.dto.CouponDTO;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.refund.application.api.dto.RefundApplicationMessageDTO;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.dianping.refund.platform.api.model.enums.RefundProcessTemplate;
import com.dianping.refund.platform.api.model.enums.RefundStateCode;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.swallow.common.message.Destination;
import com.dianping.swallow.common.message.Message;
import com.dianping.swallow.consumer.BackoutMessageException;
import com.dianping.swallow.consumer.Consumer;
import com.dianping.swallow.consumer.ConsumerConfig;
import com.dianping.swallow.consumer.MessageListener;
import com.dianping.swallow.consumer.impl.ConsumerFactoryImpl;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.meituan.nibtp.trade.client.buy.enums.OrderExtraFieldEnum;
import com.sankuai.carnation.distribution.groundpromotion.dommain.part.time.GroundPromotionPartTimeOrderCalService;
import com.sankuai.carnation.distribution.groundpromotion.dommain.part.time.OrderOperateNotifyInfo;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.utils.ProductTypeAnalyser;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelResultDataService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderOperateEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveOrderIntentionServiceImpl;
import com.sankuai.technician.trade.common.enums.OrderTypeEnum;
import com.sankuai.technician.trade.types.enums.OrderLogBizTypeEnum;
import com.sankuai.technician.trade.types.enums.OrderLogOptTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/24
 * @Description: 退款消息（团购、预订、标品使用交易事件，预付和次卡使用该消息）
 */
@Component
@Slf4j
public class GroundPromotionOrderRefundConsumer implements InitializingBean {

    @Autowired
    private GetUnifiedOrderService getUnifiedOrderService;

    @Autowired
    private OrderChannelResultDataService resultDataService;

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private RedisStoreClient redisStoreClient;

    @Autowired
    private OrderCouponAcl orderCouponAcl;

    @Autowired
    private GroundPromotionPartTimeOrderCalService groundPromotionPartTimeOrderCalService;

    @Autowired
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Autowired
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    private final ThreadPoolExecutor taskRunningPool = new ThreadPoolExecutor(20, 40, 1, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(1000), new NamedThreadFactory(getClass().getSimpleName() + ".taskRunningPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public void afterPropertiesSet() throws Exception {
        ConsumerConfig config = new ConsumerConfig();
        config.setThreadPoolSize(1);
        Consumer consumer = ConsumerFactoryImpl.getInstance().createConsumer(
                Destination.topic("refund_platform_status_change"),
                "dz.order.distribute.normal.order.refund.consumer",
                config);

        consumer.setListener(new MessageListener() {
            @Override
            public void onMessage(Message message) throws BackoutMessageException {
                log.info("[{}] messageId: {}", getClass().getSimpleName(), message.getMessageId());
                RefundApplicationMessageDTO refundMessage = JSON.parseObject(message.getContent(), RefundApplicationMessageDTO.class);
                if (!needConsume(refundMessage)) {
                    return;
                }
                process(refundMessage);
            }
        });
        consumer.start();
    }

    private void process(RefundApplicationMessageDTO refundMessage) throws BackoutMessageException {
        try {
            if (!ProductTypeUtils.isPrepay(refundMessage.getBizType())
                    && !ProductTypeUtils.isTimeCard(refundMessage.getBizType())) {
                // 过滤预付和次卡
                return;
            }
            String orderId = refundMessage.getUnifiedorderID();
            // 只取已经归因
            DistributionOrderChannelCalResult result = resultDataService.getCalResultByOrder(DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode(), orderId);
            if (result != null && result.getChannel().equals(DistributionBusinessChannelEnum.GROUND_PROMOTION.getCode()) && result.getIntentionType() == IntentionTypeEnum.WEAK_INTENTION.getCode()) {
                String distributionCode = result.getDistributionCode();
                distributionCode = URLDecoder.decode(distributionCode);
                UnifiedOrderWithId unifiedOrder = getUnifiedOrderService.getByUnifiedOrderId(orderId);

                long dpShopId = PayPlatform.isMtPlatform(unifiedOrder.getPlatform()) ? shopMapperService.mt2dp(unifiedOrder.getLongMtShopId()) : unifiedOrder.getLongShopId();

                if (distributionCode.matches("^dt.+") && Long.parseLong(distributionCode.substring(distributionCode.indexOf("$$") + 2)) == dpShopId) {
                    // 命中弱归因兼职订单
                    // 防止重复消费 key为订单id+退款凭证+退款申请时间
                    StoreKey storeKey = new StoreKey("GroundPromotionOrderCalLock", orderId + refundMessage.getTargetVoucherList().toString() + refundMessage.getAddTime().toString(), OrderChannelCalculateTaskTypeEnum.GROUND_PROMOTION_INDIRECT_INTENTION.getCode());
                    List<OrderOperateNotifyInfo> notifyList = buildCancelNotify(refundMessage, distributionCode);
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        // 组装消息并发送
                        CompletableFuture.runAsync(() -> groundPromotionPartTimeOrderCalService.calculate(notifyList), taskRunningPool);
                    }
                }
            }

            // 私域直播退款订单处理开关，这里的开关标识的是走不走交易的退款，逻辑处理迁移至收单系统
            Boolean tradeRefundProcessSwitch = Lion.getBoolean(Environment.getAppName(), "privatelive.trade.order.refund.handle.switch", true);
            if (tradeRefundProcessSwitch) {
                PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
                if (orderIntentionResult != null) {
                    // 分布式锁，防止重复消费
                    StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + refundMessage.getStatus() + refundMessage.getTargetVoucherList().toString() + refundMessage.getAddTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        // 更新数据
                        String finalOrderId = orderId;
                        CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                                .orderId(finalOrderId)
                                .operateType(OrderOperateEnum.REFUND_SUCCESS.getCode())
                                .receiptIdList(refundMessage.getTargetVoucherList())
                                .build()), taskRunningPool);
                    }
                }
            }

            //// 发起退款申请
            //if (refundApplicationMessageDTO.getStatus().equals(RefundStateCode.CREATED.value)) {
            //    PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
            //    if (orderIntentionResult != null) {
            //        // 分布式锁，防止重复消费
            //        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + refundApplicationMessageDTO.getStatus() + refundApplicationMessageDTO.getTargetVoucherList().toString() + refundApplicationMessageDTO.getAddTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
            //        if (redisStoreClient.setnx(storeKey, true, 60)) {
            //            // 更新数据
            //            String finalOrderId = orderId;
            //            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(finalOrderId, OrderOperateEnum.REFUND_CREATE.getCode(), refundApplicationMessageDTO.getTargetVoucherList()), taskRunningPool);
            //        }
            //    }
            //}
            //
            //// 退款取消or退款失败，需要回退到之前的状态
            //if (refundApplicationMessageDTO.getStatus().equals(RefundStateCode.CANCEL.value)
            //    || refundApplicationMessageDTO.getStatus().equals(RefundStateCode.FAIL.value)) {
            //    PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
            //    if (orderIntentionResult != null) {
            //        // 分布式锁，防止重复消费
            //        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + refundApplicationMessageDTO.getStatus() + refundApplicationMessageDTO.getTargetVoucherList().toString() + refundApplicationMessageDTO.getAddTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
            //        if (redisStoreClient.setnx(storeKey, true, 60)) {
            //            // 更新数据
            //            String finalOrderId = orderId;
            //            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(finalOrderId, OrderOperateEnum.REFUND_FAIL.getCode(), Lists.newArrayList()), taskRunningPool);
            //        }
            //    }
            //}

        } catch (Exception e) {
            log.error("GroundPromotionOrderRefundConsumer error msg:{}", JSONObject.toJSONString(refundMessage), e);
            throw new BackoutMessageException();
        }
    }

    private List<OrderOperateNotifyInfo> buildCancelNotify(RefundApplicationMessageDTO refundApplicationMessageDTO, String distributionCode) {
        List<OrderOperateNotifyInfo> notifyList = Lists.newArrayList();

        UnifiedOrderWithId unifiedOrder = getUnifiedOrderService.getByUnifiedOrderId(refundApplicationMessageDTO.getUnifiedorderID());

        long poiId = PayPlatform.isMtPlatform(unifiedOrder.getPlatform()) ? unifiedOrder.getLongMtShopId() : shopMapperService.dp2mt(unifiedOrder.getLongShopId());

        List<String> receiptIDList = refundApplicationMessageDTO.getTargetVoucherList();

        Map<Long, CouponDTO> couponDTOMap = orderCouponAcl.findCouponByIDs(receiptIDList.stream().map(Long::parseLong).collect(Collectors.toList()));
        boolean buyMagicMemberCoupon = false;
        if (unifiedOrder.getNibExtraFields() != null && unifiedOrder.getNibExtraFields().containsKey(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey())) {
            buyMagicMemberCoupon = "true".equals(unifiedOrder.getNibExtraFields().get(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey()));
        }
        for (String receiptId : receiptIDList) {
            OrderOperateNotifyInfo notifyInfo = new OrderOperateNotifyInfo();

            CouponDTO couponDTO = couponDTOMap.get(Long.parseLong(receiptId));
            notifyInfo.setProductType(ProductTypeAnalyser.fromOrderBizType(refundApplicationMessageDTO.getBizType()).getCode());
            notifyInfo.setProductId(Long.parseLong(unifiedOrder.getSpugId()));
            notifyInfo.setOrderId(unifiedOrder.getUnifiedOrderId());
            notifyInfo.setOrderType(OrderTypeEnum.TRADE_ORDER.getId());
            notifyInfo.setOrderIdLong(unifiedOrder.getLongOrderId());
            notifyInfo.setOperateType(OrderLogOptTypeEnum.CANCEL.getCode());
            notifyInfo.setBizId(receiptId);
            notifyInfo.setBizType(OrderLogBizTypeEnum.FUN_COUPON.getCode());
            notifyInfo.setExtKey(couponDTO.getCouponValue());
            notifyInfo.setMtShopId(poiId);
            notifyInfo.setActionTime(refundApplicationMessageDTO.getUpdateTime());
            notifyInfo.setExtInfo(new HashMap<String, String>() {{
                put("flag", distributionCode);
            }});
            notifyInfo.setUserId(unifiedOrder.getUserId());
            notifyInfo.setMtUserId(unifiedOrder.getMtUserId());
            notifyInfo.setPlatform(PayPlatform.isMtPlatform(unifiedOrder.getPlatform()) ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
            notifyInfo.setTotalAmount(unifiedOrder.getTotalAmount());
            notifyInfo.setAddTime(unifiedOrder.getAddTime());
            notifyInfo.setPayTime(unifiedOrder.getPaySuccessTime());
            notifyInfo.setBuyMagicMemberCoupons(buyMagicMemberCoupon ? 1 : 0);
            notifyList.add(notifyInfo);
        }

        return notifyList;
    }


    private boolean needConsume(RefundApplicationMessageDTO refundMessage) {
        //不能为null
        if (refundMessage == null) {
            return false;
        }
        //得有统一订单号
        boolean matchOrderId = StringUtils.isNotEmpty(refundMessage.getUnifiedorderID());
        //得有需要退款的券
        boolean matchReceipt = CollectionUtils.isNotEmpty(refundMessage.getTargetVoucherList());
        //status要是退款成功
        boolean matchStatus = refundMessage.getStatus().equals(RefundStateCode.COMPLETE.value);
        // 不能是购买失败退款和重复支付退款
        boolean matchTemplateId = refundMessage.getRefundTemplateID() != RefundProcessTemplate.BUY_FAIL_REFUND.getTemplateID()
                && refundMessage.getRefundTemplateID() != RefundProcessTemplate.REPEAT_PAY_REFUND.getTemplateID();

        return matchOrderId && matchReceipt && matchStatus && matchTemplateId;
    }
}
