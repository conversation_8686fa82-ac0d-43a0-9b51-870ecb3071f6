package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.zsts.commission.unify.api.UnifiedCommissionQueryService;
import com.dianping.zsts.commission.unify.dto.DealGroupCommissionDto;
import com.dianping.zsts.commission.unify.request.DealGroupCommissionRequest;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductCommissionBasisAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.enums.CommissionBasisTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Service
@Slf4j
public class ProductCommissionBasisAclImpl implements ProductCommissionBasisAcl {

    private final static String CAT_TYPE = ProductCommissionBasisAcl.class.getSimpleName();

    @Resource
    private UnifiedCommissionQueryService unifiedCommissionQueryService;

    @Override
    public List<ProductCommissionBasisInfo> batchQueryProductCommissionBasis(List<ProductCommissionBasisQueryRequest> productCommissionList) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "batchQueryProductCommissionBasis");
        try {
            if (CollectionUtils.isEmpty(productCommissionList)) {
                throw new DistributionPlanException("IllegalParam");
            }
            List<DealGroupCommissionRequest> requestList = productCommissionList.stream().map(productCommission -> {
                DealGroupCommissionRequest request = new DealGroupCommissionRequest();
                request.setDealGroupID(productCommission.getDpProductId());
                if (StringUtils.isNotBlank(productCommission.getDealPrice())) {
                    request.setDealPrice(productCommission.getDealPrice());
                }
                return request;
            }).collect(Collectors.toList());
            Map<Long, DealGroupCommissionDto> response = unifiedCommissionQueryService.batchQueryDealGroupCommission(requestList);
            if (Objects.isNull(response)) {
                throw new DistributionPlanException("QuerySettleCommissionBasisTypeFailed");
            }

            List<ProductCommissionBasisInfo> resultList = productCommissionList.stream()
                    .flatMap(productCommission -> {
                        DealGroupCommissionDto dealGroupCommissionDto = response.get(productCommission.getDpProductId());
                        if (Objects.isNull(dealGroupCommissionDto) || Objects.isNull(dealGroupCommissionDto.getChangeBaseCategoryFlag())) {
                            Cat.logEvent("QuerySettleCommissionBasisTypeEmpty", "QuerySettleCommissionBasisTypeEmpty");
                            log.error("{} QuerySettleCommissionBasisTypeEmpty, productCommission:{}", CAT_TYPE, JsonUtil.toJson(productCommission));
                            return Stream.of(ProductCommissionBasisInfo.builder()
                                    .dpProductId(productCommission.getDpProductId())
                                    .build());
                        }
                        return Stream.of(ProductCommissionBasisInfo.builder()
                                .dpProductId(dealGroupCommissionDto.getDealGroupId())
                                .commissionBasisType(dealGroupCommissionDto.getChangeBaseCategoryFlag()
                                        ? CommissionBasisTypeEnum.COMMISSION_GROUP
                                        : CommissionBasisTypeEnum.NOT_COMMISSION_GROUP)
                                .build());
                    })
                    .collect(Collectors.toList());
            transaction.setSuccessStatus();
            return resultList;
        } catch (Exception e) {
            Cat.logEvent("QuerySettleCommissionBasisTypeFailed", "Query settleCommissionBasisType failed");
            log.error("ProductCommissionBasisAcl.batchQueryDealGroupCommission failed, productCommissionList:{}", JsonUtil.toJson(productCommissionList), e);
            transaction.setStatus(e);
            return Lists.newArrayList();
        } finally {
            transaction.complete();
        }
    }
}