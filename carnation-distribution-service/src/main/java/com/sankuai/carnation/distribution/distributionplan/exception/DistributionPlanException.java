package com.sankuai.carnation.distribution.distributionplan.exception;


import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;

/**
 * <AUTHOR>
 */
public class DistributionPlanException extends RuntimeException {

    DistributionBusinessChannelEnum channel;

    public DistributionPlanException(String message) {
        super(message);
    }

    public DistributionPlanException(DistributionBusinessChannelEnum channel) {
        this.channel = channel;
    }

    public DistributionPlanException(DistributionBusinessChannelEnum channel, String message) {
        super(message);
        this.channel = channel;
    }

    public DistributionPlanException(DistributionBusinessChannelEnum channel, String message, Throwable cause) {
        super(message, cause);
        this.channel = channel;
    }

    public DistributionPlanException(DistributionBusinessChannelEnum channel, Throwable cause) {
        super(cause);
        this.channel = channel;
    }

    @Override
    public String toString() {
        String channelDesc = channel == null ? "未知渠道" : channel.getDesc();
        return "DistributionPlanException{" +
                "channel='" + channelDesc + '\'' +
                super.getLocalizedMessage() +
                '}';
    }
}