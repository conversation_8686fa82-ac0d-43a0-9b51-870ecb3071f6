package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.tpfun.product.api.common.IResponse;
import com.dianping.tpfun.product.api.sku.aggregate.ProductStaticDetailBySpuServiceV2;
import com.dianping.tpfun.product.api.sku.aggregate.dto.spu.SpuDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.spu.SpuProductDTO;
import com.dianping.tpfun.product.api.sku.request.PageQuerySpuProductRequest;
import com.dianping.tpfun.product.api.sku.request.QuerySpuRequest;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.distributionplan.acl.StandardProductAcl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StandardProductAclImpl implements StandardProductAcl {

    @Resource
    private ProductStaticDetailBySpuServiceV2 productStaticDetailBySpuServiceV2;

    @Override
    public boolean checkSpuValid(Long spuId) {
        try {
            if (spuId == null || spuId <= 0L) {
                throw new RuntimeException("StandardProductAclParamIllegal");
            }
            PageQuerySpuProductRequest pageQuerySpuProductRequest = new PageQuerySpuProductRequest();
            pageQuerySpuProductRequest.setSpuId(spuId);
            pageQuerySpuProductRequest.setIsPlatformProduct(0);
            pageQuerySpuProductRequest.setStart(0L);
            pageQuerySpuProductRequest.setLimit(20);
            IResponse<List<SpuProductDTO>> response = productStaticDetailBySpuServiceV2.pageQuerySpuProductByRequest(pageQuerySpuProductRequest);
            if (response == null || !response.isSuccess()) {
                Cat.logEvent("InvokeSpuServiceFailed",  String.format("spuId:%s,response:%s", spuId, JSON.toJSONString(response)));
                return false;
            }
            if (CollectionUtils.isEmpty(response.getResult())) {
                Cat.logEvent("SpuRelatedProductNotFound",  String.format("spuId:%s,response:%s", spuId, JSON.toJSONString(response)));
                return false;
            }

            Optional<SpuProductDTO> optional = response.getResult().stream()
                    .filter(spuProduct -> spuProduct.getIsPlatformProduct().equals(0))
                    .filter(spuProduct -> spuProduct.getProductId() !=null && spuProduct.getProductId() > 0L)
                    .findAny();

            return optional.isPresent();
        } catch (Exception e) {
            log.error("StandardProductAcl.checkSpuValid failed, spuId:{}", spuId, e);
            return false;
        }
    }

    @Override
    public Map<Long, Long> loadPlatformProductId2SpuIdMap(List<Long> platformProductIds) {
        try {
            if (CollectionUtils.isEmpty(platformProductIds)) {
                throw new RuntimeException("StandardProductAclParamIllegal");
            }

            QuerySpuRequest querySpuRequest = new QuerySpuRequest();
            querySpuRequest.setProductIds(platformProductIds);
            IResponse<List<SpuDTO>> response = productStaticDetailBySpuServiceV2.querySpuByRequest(querySpuRequest);
            if (response == null || !response.isSuccess()) {
                throw new RuntimeException("InvokeSpuServiceFailed");
            }
            if (CollectionUtils.isEmpty(response.getResult())) {
                throw new RuntimeException("ProductRelatedSpuNotFound");
            }

            Map<Long, Long> platformProductId2SpuIdMap = Maps.newHashMap();
            response.getResult().forEach(spuDto -> platformProductId2SpuIdMap.put(spuDto.getProductId(), spuDto.getSpuId()));

            return platformProductId2SpuIdMap;
        } catch (RuntimeException re) {
            Cat.logEvent(re.getMessage(),  String.format("platformProductIds:%s", platformProductIds));
            log.error("StandardProductAcl.loadPlatformProductId2SpuIdMap failed, platformProductIds:{}", platformProductIds, re);
            return Maps.newHashMap();
        } catch (Exception e) {
            log.error("StandardProductAcl.loadPlatformProductId2SpuIdMap failed, platformProductIds:{}", platformProductIds, e);
            return Maps.newHashMap();
        }
    }
}
