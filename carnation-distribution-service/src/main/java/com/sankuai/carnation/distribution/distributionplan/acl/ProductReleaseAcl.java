package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ChannelProductReleaseResult;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductReleaseRequest;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ReleasedProductInfo;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductReleaseAcl {
    /**
     * 批量上架/下架
     * @param productReleaseRequest
     * @return
     */
    ChannelProductReleaseResult releaseChannelProduct(ProductReleaseRequest productReleaseRequest);

    /**
     * 批量查询投放商品信息
     * @param productType
     * @param mtProductIds
     * @param channel
     * @return
     */
    List<ReleasedProductInfo> batchQueryReleasedProduct(ProductTypeEnum productType, List<Long> mtProductIds,
                                                        DistributionBusinessChannelEnum channel);
}