package com.sankuai.carnation.distribution.distributioncode.domain.model;

import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributionCodeParamMappingDO {

    private Long mappingId;

    private DistributionBusinessChannelEnum channel;

    private String distributionCode;

    private String bizParam1;

    private String bizParam2;

    private String bizParam3;

    private String bizParam4;

    public void validateParam() {
        if (this.channel == null) {
            throw new RuntimeException("分销渠道信息不能为空");
        }

        if (this.channel.equals(DistributionBusinessChannelEnum.WEI_BO)) {
            if (StringUtils.isBlank(this.bizParam1)) {
                throw new RuntimeException("微博渠道下的商品id不能为空");
            }
            if (StringUtils.isBlank(this.bizParam2)) {
                throw new RuntimeException("微博渠道下的分销人pid不能为空");
            }
            if (StringUtils.isBlank(this.bizParam3)) {
                throw new RuntimeException("微博渠道下的分销计划id不能为空");
            }
            if (StringUtils.isBlank(this.bizParam4)) {
                throw new RuntimeException("微博渠道下的分销商品类型不能为空");
            }
            return;
        }
        throw new RuntimeException("分销渠道目前只支持微博渠道");
    }

    public void generateCode(long uniqueId) {
        String distributionCode = channel.getCode() + uniqueId;
        this.setDistributionCode(distributionCode);
    }
}
