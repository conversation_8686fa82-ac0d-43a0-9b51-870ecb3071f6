package com.sankuai.carnation.distribution.distributionplan.domain.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.acl.DealProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.PoiMountAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductReleaseAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.*;
import com.sankuai.carnation.distribution.distributionplan.constant.LionConstant;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.*;
import com.sankuai.carnation.distribution.distributionplan.dto.ChannelProductPushResultQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.dto.DistributionProductPushResultDTO;
import com.sankuai.carnation.distribution.distributionplan.dto.ProductDistributionStatusDTO;
import com.sankuai.carnation.distribution.distributionplan.dto.ProductPushRequest;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductPushStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductPushTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.repository.DistributionProductPushRepository;
import com.sankuai.carnation.distribution.distributionplan.utils.LionService;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.utils.DateUtils;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DistributionProductPushService {

    @Resource
    private LionService lionService;

    @Resource
    private DistributionProductPushRepository distributionProductPushRepository;

    @Resource
    private DistributionPlanDoService distributionPlanDoService;

    @Resource
    private ProductReleaseAcl productReleaseAcl;

    @Resource
    private DealProductAcl dealProductAcl;

    @Resource
    private PoiMountAcl poiMountAcl;

    private static final int DISTRIBUTION_PRODUCT_UPLOAD_EXCEL_MAX_ROW_NUM = 20000;

    private static final int DISTRIBUTION_PUSH_PRODUCT_PARTITION_SIZE = 500;

    private static final String CAT_TYPE = DistributionProductPushService.class.getSimpleName();

    /**
     * 商品叶子结点为空时候，如果要配置在快手类目映射配置：
     * pushable.kuaishou.deal.product.category.config.map
     * 中，等价于0L
     */
    public static final long NULL_PRODUCT_LEAF_CATEGORY_ID = 0L;

    /**
     * 上传本次新增的分销商品到数据库中
     * @param excelName
     * @param channel
     * @return
     */
    public Integer uploadDistributionProduct(String excelName, DistributionBusinessChannelEnum channel) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "uploadDistributionProduct");
        try {
            HashSet<ProductUniqueUnit> productUniqueUnits = parseDistributionProductUploadExcel(excelName, channel);
            if (CollectionUtils.isEmpty(productUniqueUnits)) {
                throw new RuntimeException("ProductUniqueUnitsEmpty");
            }

            List<DistributionProductPush> pushDistributionProductList = Lists.newArrayList();
            productUniqueUnits.forEach(productUniqueUnit -> pushDistributionProductList.add(DistributionProductPush.initByProductUniqueUnit(productUniqueUnit)));
            return distributionProductPushRepository.batchAddDistributionProductPush(pushDistributionProductList);
        } catch (RuntimeException re) {
            log.error("uploadDistributionProduct encounter runtime exception, excelName:{}, channel:{}, message:{}", excelName, channel, re.getMessage(), re);
            transaction.setStatus(re);
            return 0;
        } catch (Exception e) {
            log.error("uploadDistributionProduct failed, excelName:{}, channel:{}", excelName, channel, e);
            transaction.setStatus(e);
            return 0;
        } finally {
            transaction.complete();
        }
    }

    /**
     * 解析分销商品上传的excel
     * @param excelName
     * @return
     */
    private HashSet<ProductUniqueUnit> parseDistributionProductUploadExcel(String excelName, DistributionBusinessChannelEnum channel) throws IOException {
        HashSet<ProductUniqueUnit> productUniqueUnits = Sets.newHashSet();
        Workbook workbook = lionService.getFile(excelName);
        Sheet sheet = workbook.getSheetAt(0);
        int totalNum = sheet.getLastRowNum();
        if (totalNum > Lion.getInt(MdpContextUtils.getAppKey(), LionConstant.PRODUCT_UPLOAD_EXCEL_ROW_LIMIT, DISTRIBUTION_PRODUCT_UPLOAD_EXCEL_MAX_ROW_NUM)) {
            log.error(CAT_TYPE + " distributionProductUploadExcel too large, excelName;{}, totalNum:{}", excelName, totalNum);
            Cat.logEvent("ProductUploadExcelTooLarge", "distributionProductUploadExcel too large");
            throw new DistributionPlanException(DistributionBusinessChannelEnum.KUAISHOU_E_COMMERCE, "ProductUploadExcelTooLarge");
        }
        for (int index = 1; index <= totalNum; index++) {
            Row row = sheet.getRow(index);
            if (null == row) {
                Cat.logEvent("ExcelExistNullRow", "distributionProductUploadExcel:exist null row");
                continue;
            }
            if (null == row.getCell(0) || null == row.getCell(1)) {
                Cat.logEvent("ExcelExistNullValue", "distributionProductUploadExcel:exist null value");
                continue;
            }
            row.getCell(0).setCellType(CellType.STRING);
            String mtProductIdString = row.getCell(0).getStringCellValue().trim();
            row.getCell(1).setCellType(CellType.STRING);
            String productTypeString = row.getCell(1).getStringCellValue().trim();
            if (StringUtils.isBlank(mtProductIdString)) {
                continue;
            }
            if (StringUtils.isBlank(productTypeString)) {
                continue;
            }
            try {
                //美团商品id
                Long mtProductId = Long.valueOf(mtProductIdString);
                Integer productType = Integer.parseInt(productTypeString);
                productUniqueUnits.add(ProductUniqueUnit.builder().mtProductId(mtProductId).productType(productType).channel(channel.getCode()).build());
            } catch (IllegalArgumentException ie) {
                log.error("DistributionProductPushService.parseProductListExcel illegal param, rowId:{}, mtProductIdString:{}, productType:{}", index, mtProductIdString, productTypeString, ie);
                Cat.logEvent("ParseProductExcelRowFailed", "distributionProductUploadExcel:ParseProductExcelRowFailed");
            }
        }

        return productUniqueUnits;
    }

    /**
     * 推送商品到渠道侧
     * @param pushRequest
     * @return
     */
    public Map<Long, DistributionProductPushResultDTO> pushDistributionProduct(ProductPushRequest pushRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pushDistributionProduct");
        try {
            if (!pushRequest.getProductType().equals(ProductTypeEnum.MT_DEAL_GROUP.getCode())) {
                log.error("DistributionProductPushService.pushDistributionProduct productType only support MT_DEAL_GROUP, pushRequest:{}", pushRequest);
                throw new DistributionPlanException(DistributionBusinessChannelEnum.fromCode(pushRequest.getChannel()), "ProductTypeNotSupport");
            }

            Map<Long, DistributionProductPushResultDTO> productPushResultMap = Maps.newHashMap();

            List<Long> distinctMtProductIds = pushRequest.getProductIds().stream().distinct().collect(Collectors.toList());
            List<List<Long>> mtProductIdsFragment = Lists.partition(distinctMtProductIds, DISTRIBUTION_PUSH_PRODUCT_PARTITION_SIZE);

            mtProductIdsFragment.forEach(mtProductIds -> {
                DistributionProductPushQueryRequest request = DistributionProductPushQueryRequest.builder().productIds(mtProductIds)
                        .channelList(Lists.newArrayList(pushRequest.getChannel()))
                        .productTypeList(Lists.newArrayList(pushRequest.getProductType())).build();
                List<DistributionProductPush> distributionProductPushes = distributionProductPushRepository.queryDistributionProductPush(request);
                if (CollectionUtils.isEmpty(distributionProductPushes) || distributionProductPushes.size() < mtProductIds.size()) {
                    List<DistributionProductPush> incrementalDistributionProductPushes = DistributionProductPush.batchInitIncrementalProduct(pushRequest, mtProductIds, distributionProductPushes);
                    Cat.logEvent("DistributionProductInitializing", "exist products have not been pushed");
                    distributionProductPushes.addAll(incrementalDistributionProductPushes);
                }

                Map<Long, DistributionProductPushResultDTO> cannotBePushProductMap = filterCannotBePushProduct(pushRequest, distributionProductPushes);

                Map<Long, DistributionProductPushResultDTO> pushableProductPushResultMap = pushDistributionProduct(DistributionProductPushTypeEnum.from(pushRequest.getPushType()), distributionProductPushes, cannotBePushProductMap.keySet());

                List<DistributionProductPush> incrementalDistributionProductPushes = distributionProductPushes.stream().filter(push -> push.getPushId() == null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(incrementalDistributionProductPushes)) {
                    distributionProductPushRepository.batchAddDistributionProductPush(incrementalDistributionProductPushes);
                }
                List<DistributionProductPush> updateDistributionProductPushes = distributionProductPushes.stream().filter(push -> push.getPushId() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(updateDistributionProductPushes)) {
                    distributionProductPushRepository.batchUpdateDistributionProductPushStatus(updateDistributionProductPushes);
                }

                productPushResultMap.putAll(cannotBePushProductMap);
                productPushResultMap.putAll(pushableProductPushResultMap);
            });

            return productPushResultMap;
        } catch (DistributionPlanException de) {
            log.error("pushDistributionProduct encounter runtime exception, pushRequest:{}, message:{}", pushRequest, de.getMessage(), de);
            transaction.setStatus(de);
            return Maps.newHashMap();
        } catch (Exception e) {
            log.error("pushDistributionProduct failed, pushRequest:{}", pushRequest, e);
            transaction.setStatus(e);
            return Maps.newHashMap();
        } finally {
            transaction.complete();
        }
    }

    /**
     * 对可推送的商品进行逐个投放
     * @param pushType 投放类型：上架或者下架
     * @param distributionProducts 所有商品列表
     * @param cannotPushProductIds 不可推送的商品id列表
     * @return
     */
    private Map<Long, DistributionProductPushResultDTO> pushDistributionProduct(DistributionProductPushTypeEnum pushType, List<DistributionProductPush> distributionProducts, Set<Long> cannotPushProductIds) {
        if (CollectionUtils.isEmpty(distributionProducts)) {
            Cat.logEvent("EmptyDistributionProduct", "pushDistributionProduct product list is empty");
            return Maps.newHashMap();
        }

        Map<Long, DistributionProductPushResultDTO> productPushResultDtoMap = Maps.newHashMap();
        Date currentDate = new Date();
        distributionProducts.forEach(pushableProduct -> {
            if (cannotPushProductIds.contains(pushableProduct.getMtProductId())) {
                return;
            }

            ProductReleaseRequest releaseRequest = ProductReleaseRequest.builder().pushType(pushType)
                    .pushProductUnit(PushProductUnit.buildByDistributionProductPush(pushableProduct)).build();

            ChannelProductReleaseResult releaseResult = productReleaseAcl.releaseChannelProduct(releaseRequest);
            DistributionProductPushResultDTO pushResultDto = DistributionProductPushResultDTO.builder().productId(pushableProduct.getMtProductId())
                    .productType(pushableProduct.getProductType().getCode())
                    .channel(pushableProduct.getChannel().getCode())
                    .pushResult(releaseResult.getSuccess())
                    .failReason(releaseResult.getFailMsg()).build();
            productPushResultDtoMap.put(pushableProduct.getMtProductId(), pushResultDto);
            if (releaseResult.getSuccess() && pushType.equals(DistributionProductPushTypeEnum.ONLINE)) {
                pushableProduct.setStatus(DistributionProductPushStatusEnum.PUSH_ONLINE_SUCCEED);
                pushableProduct.setOnlineTime(currentDate);
                return;
            }
            if (releaseResult.getSuccess() && pushType.equals(DistributionProductPushTypeEnum.OFFLINE)) {
                pushableProduct.setOfflineTime(currentDate);
                pushableProduct.setStatus(DistributionProductPushStatusEnum.PUSH_OFFLINE_SUCCEED);
                return;
            }
            if (!releaseResult.getSuccess() && pushType.equals(DistributionProductPushTypeEnum.ONLINE)) {
                pushableProduct.setStatus(DistributionProductPushStatusEnum.PUSH_ONLINE_FAILED);
                pushableProduct.setPushResult(releaseResult.getFailMsg());
                return;
            }
            if (!releaseResult.getSuccess() && pushType.equals(DistributionProductPushTypeEnum.OFFLINE)) {
                pushableProduct.setStatus(DistributionProductPushStatusEnum.PUSH_OFFLINE_FAILED);
                pushableProduct.setPushResult(releaseResult.getFailMsg());
            }
        });
        return productPushResultDtoMap;
    }

    /**
     * 过滤出不可推送的商品及其不可推送原因
     * @param pushRequest
     * @param distributionProductPushes
     * @return
     */
    private Map<Long, DistributionProductPushResultDTO> filterCannotBePushProduct(ProductPushRequest pushRequest,
                                                                    List<DistributionProductPush> distributionProductPushes) {
        if (Lion.getBoolean(MdpContextUtils.getAppKey(), "kuaishou.push.product.filter.skip.switch", false)) {
            Cat.logEvent("PushProductFilterSkipSwitchOn",  "PushProductFilterSkipSwitchOn");
            return Maps.newHashMap();
        }
        Map<String, PushableKuaiShouDealProductCategory> pushableKuaiShouDealProductCategoryMap = Lion.getMap(MdpContextUtils.getAppKey(),
                "pushable.kuaishou.deal.product.category.config.map", PushableKuaiShouDealProductCategory.class, Maps.newHashMap());
        if (MapUtils.isEmpty(pushableKuaiShouDealProductCategoryMap)) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.fromCode(pushRequest.getChannel()), "ProductPushableCategoryValidateConfigNotFound");
        }

        Map<Long, DistributionProductPushResultDTO> productPushResultDtoMap = Maps.newHashMap();
        distributionProductPushes.forEach(distributionProduct -> {
            List<DealProductInfo> dealProductInfos = dealProductAcl.queryMtDealProductByIds(Lists.newArrayList(distributionProduct.getMtProductId()));
            if (CollectionUtils.isEmpty(dealProductInfos) || null == dealProductInfos.get(0)) {
                distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                distributionProduct.setPushResult("商品未找到");
                productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品未找到"));
                return;
            }
            // 下架商品只需要校验商品存在性即可
            if (pushRequest.getPushType().equals(DistributionProductPushTypeEnum.OFFLINE.getType())) {
                return;
            }

            DealProductInfo dealProductInfo = dealProductInfos.get(0);
            if (!dealProductInfo.getOnlineStatus()) {
                distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                distributionProduct.setPushResult("商品不在线，请上线后再推送渠道分销");
                productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品不在线，请上线后再推送渠道分销"));
                return;
            }

            if (!dealProductInfo.validateCategory(pushableKuaiShouDealProductCategoryMap)) {
                distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                distributionProduct.setPushResult("商品类目不在分销商品类目集合中");
                productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品类目不在分销商品类目集合中"));
                return;
            }
            if (CollectionUtils.isEmpty(dealProductInfo.getApplyMtShopIds())) {
                distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                distributionProduct.setPushResult("商品的适用美团门店为空");
                productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品的适用美团门店为空"));
                return;
            }
            // 团单次卡增加卡控，不允许推送团单次卡类型deal
            if (pushRequest.getProductType().equals(ProductTypeEnum.MT_DEAL_GROUP.getCode())
                    && (dealProductInfo.getTradeType() != null && TradeTypeEnum.COUNT_CARD.getCode() == dealProductInfo.getTradeType())) {
                    distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                    distributionProduct.setPushResult("商品为团单次卡");
                    productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品为团单次卡"));
                    return;
            }

            // 售卖结束时间校验，如果售卖结束时间小于等于当前时间，不允许推送
            if (StringUtils.isNotBlank(dealProductInfo.getEndSaleDate()) && DateUtils.parseToTs(dealProductInfo.getEndSaleDate(), null) < System.currentTimeMillis()) {
                distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                distributionProduct.setPushResult("商品售卖结束时间小于当前时间");
                productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品售卖结束时间小于当前时间"));
                return; 
            }

            Map<Long, String> mtPoiToMountPoiIdMap = poiMountAcl.batchGetMtPoiMountPoiId(dealProductInfo.getApplyMtShopIds(), DistributionBusinessChannelEnum.fromCode(pushRequest.getChannel()));
            if (MapUtils.isEmpty(mtPoiToMountPoiIdMap) || mtPoiToMountPoiIdMap.values().stream().noneMatch(StringUtils::isNotBlank)) {
                distributionProduct.setStatus(DistributionProductPushStatusEnum.VALIDATE_FAILED);
                distributionProduct.setPushResult("商品的适用美团门店均未在渠道侧融合成功");
                productPushResultDtoMap.put(distributionProduct.getMtProductId(), buildFailedProductPushResult(distributionProduct, "商品的适用美团门店均未在渠道侧融合成功"));
            }
        });

        return productPushResultDtoMap;
    }

    private DistributionProductPushResultDTO buildFailedProductPushResult(DistributionProductPush distributionProduct, String failReason) {
        return DistributionProductPushResultDTO.builder().productId(distributionProduct.getMtProductId())
                .channel(distributionProduct.getChannel().getCode())
                .productType(distributionProduct.getProductType().getCode())
                .pushResult(false)
                .failReason(failReason).build();
    }

    public List<ProductDistributionStatusDTO> batchQueryChannelProductPushResult(ChannelProductPushResultQueryRequest request) {
        List<ReleasedProductInfo> releasedProductInfos = productReleaseAcl.batchQueryReleasedProduct(request.getProductType(), request.getProductIds(), request.getChannel());
        Map<Long, ReleasedProductInfo> productIdToReleasedInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(releasedProductInfos)) {
            releasedProductInfos.forEach(releasedProductInfo -> productIdToReleasedInfoMap.put(releasedProductInfo.getMtProductId(), releasedProductInfo));
        }

        if (request.getChannel().equals(DistributionBusinessChannelEnum.KUAISHOU_E_COMMERCE)) {
            DistributionProductPushQueryRequest distributionProductPushQueryRequest = DistributionProductPushQueryRequest.builder()
                    .productIds(request.getProductIds())
                    .productTypeList(Lists.newArrayList(request.getProductType().getCode()))
                    .channelList(Lists.newArrayList(request.getChannel().getCode()))
                    .build();
            List<DistributionProductPush> distributionProductPushes = distributionProductPushRepository.queryDistributionProductPush(distributionProductPushQueryRequest);
            Map<Long, DistributionProductPush> productIdToPushInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(distributionProductPushes)) {
                distributionProductPushes.forEach(distributionProductPush -> productIdToPushInfoMap.put(distributionProductPush.getMtProductId(), distributionProductPush));
            }

            return request.getProductIds().stream().map(productId -> buildProductDistributionStatusResult(productId, productIdToReleasedInfoMap.get(productId), productIdToPushInfoMap.get(productId))).collect(Collectors.toList());
        }


        if (request.getChannel().equals(DistributionBusinessChannelEnum.WEI_BO)) {
            DistributionPlanQueryRequest distributionPlanQueryRequest = DistributionPlanQueryRequest.builder()
                    .productIds(request.getProductIds())
                    .productType(request.getProductType().getCode())
                    .channel(request.getChannel().getCode())
                    .build();
            List<DistributionPlanDO> distributionPlanDos = distributionPlanDoService.batchQueryDistributionPlans(distributionPlanQueryRequest);
            Map<Long, DistributionPlanDO> productIdToDistributionPlanDoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(distributionPlanDos)) {
                distributionPlanDos.forEach(distributionPlanDo -> productIdToDistributionPlanDoMap.put(distributionPlanDo.getProductId(), distributionPlanDo));
            }
            return request.getProductIds().stream().map(productId -> buildProductDistributionStatusResult(productId, productIdToReleasedInfoMap.get(productId), productIdToDistributionPlanDoMap.get(productId))).collect(Collectors.toList());
        }

        return Lists.newArrayList();
    }

    private ProductDistributionStatusDTO buildProductDistributionStatusResult(Long productId,
                                                                              ReleasedProductInfo releasedProductInfo,
                                                                              DistributionProductPush distributionProductPush) {
        ProductDistributionStatusDTO distributionProductPushChannelResult = new ProductDistributionStatusDTO();
        distributionProductPushChannelResult.setProductId(productId);
        if (null != releasedProductInfo) {
            distributionProductPushChannelResult.setProductType(releasedProductInfo.getProductType().getCode());
            distributionProductPushChannelResult.setChannel(releasedProductInfo.getChannel().getCode());
            distributionProductPushChannelResult.setReleaseStatus(releasedProductInfo.getPushResult().getCode());
            distributionProductPushChannelResult.setReleaseStatusDesc(releasedProductInfo.getPushResult().getDesc());
        }
        if (null != distributionProductPush) {
            distributionProductPushChannelResult.setProductType(distributionProductPush.getProductType().getCode());
            distributionProductPushChannelResult.setChannel(distributionProductPush.getChannel().getCode());
            distributionProductPushChannelResult.setDistributionStatus(distributionProductPush.getStatus().getCode());
            distributionProductPushChannelResult.setDistributionStatusDesc(distributionProductPush.getStatus().getDesc());
        }

        return distributionProductPushChannelResult;
    }


    private ProductDistributionStatusDTO buildProductDistributionStatusResult(Long productId,
                                                                              ReleasedProductInfo releasedProductInfo,
                                                                              DistributionPlanDO distributionPlanDo) {
        ProductDistributionStatusDTO distributionProductPushChannelResult = new ProductDistributionStatusDTO();
        distributionProductPushChannelResult.setProductId(productId);
        if (null != distributionPlanDo) {
            distributionProductPushChannelResult.setProductType(distributionPlanDo.getProductType().getCode());
            distributionProductPushChannelResult.setChannel(distributionPlanDo.getChannel().getCode());
            distributionProductPushChannelResult.setDistributionStatus(distributionPlanDo.getStatus().getCode());
            distributionProductPushChannelResult.setDistributionStatusDesc(distributionPlanDo.getStatus().getDesc());
        }
        if (null != releasedProductInfo) {
            distributionProductPushChannelResult.setProductType(releasedProductInfo.getProductType().getCode());
            distributionProductPushChannelResult.setChannel(releasedProductInfo.getChannel().getCode());
            distributionProductPushChannelResult.setReleaseStatus(releasedProductInfo.getPushResult().getCode());
            distributionProductPushChannelResult.setReleaseStatusDesc(releasedProductInfo.getPushResult().getDesc());
        }
        return distributionProductPushChannelResult;
    }
}
