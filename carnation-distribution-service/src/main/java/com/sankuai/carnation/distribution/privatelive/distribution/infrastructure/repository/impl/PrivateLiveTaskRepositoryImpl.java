package com.sankuai.carnation.distribution.privatelive.distribution.infrastructure.repository.impl;

import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantTaskMapper;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.distribution.converter.PrivateLiveTaskConverter;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.model.PrivateLiveTaskBO;
import com.sankuai.carnation.distribution.privatelive.distribution.domain.repository.PrivateLiveTaskRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * @Author: bianzhan
 * @CreateTime: 2024-08-19 15:00
 * @Description:
 */
@Service
public class PrivateLiveTaskRepositoryImpl implements PrivateLiveTaskRepository {

    @Resource
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Resource
    private PrivateLiveTaskConverter privateLiveTaskConverter;


    @Resource
    private PrivateLiveConsultantTaskMapper privateLiveConsultantTaskMapper;

    @Override
    public PrivateLiveTaskBO queryByAccountIdAndLive(Long accountId, String liveId) {
        PrivateLiveConsultantTask privateLiveConsultantTask = taskRepository.loadByConsultantIdAndLiveId(accountId, liveId);
        if (ObjectUtils.isEmpty(privateLiveConsultantTask)) {
            return null;
        }
        return privateLiveTaskConverter.toEntity(privateLiveConsultantTask);
    }

    @Override
    public PrivateLiveTaskBO save(PrivateLiveTaskBO privateLiveTaskBO) {
        if (privateLiveTaskBO.getId() != null) {
            taskRepository.update(privateLiveTaskConverter.toPoJo(privateLiveTaskBO));
            return privateLiveTaskBO;
        }
        long id = taskRepository.insert(privateLiveTaskConverter.toPoJo(privateLiveTaskBO));
        privateLiveTaskBO.setId(id);
        return privateLiveTaskBO;
    }

}
