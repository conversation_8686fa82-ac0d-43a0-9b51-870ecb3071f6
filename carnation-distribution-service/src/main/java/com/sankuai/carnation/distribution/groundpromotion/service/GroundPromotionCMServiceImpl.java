package com.sankuai.carnation.distribution.groundpromotion.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.common.enums.DistributorUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.dto.operate.BizDistributorOperateDTO;
import com.sankuai.carnation.distribution.distributor.service.BizDistributorService;
import com.sankuai.carnation.distribution.groundpromotion.dto.*;
import com.sankuai.carnation.distribution.groundpromotion.enums.*;
import com.sankuai.carnation.distribution.groundpromotion.exception.GroundPromotionException;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.GroundPromotionQueryService;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.LinkUtils;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.MessageAcl;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.*;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.*;
import com.sankuai.carnation.distribution.utils.DateUtils;
import com.sankuai.carnation.distribution.utils.MaterialPicUtil;
import com.sankuai.carnation.distribution.utils.ShortCodeUtil;
import com.sankuai.carnation.distribution.xm.dto.BaseXMMessageDTO;
import com.sankuai.carnation.distribution.xm.dto.text.TextXMMessageDTO;
import com.sankuai.carnation.distribution.xm.enums.XMMessageTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.carnation.distribution.groundpromotion.constants.GroundPromotionConstants.BIND_MIS_ID_SEPARATOR;
import static com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionDealStatusEnum.*;

/**
 * <AUTHOR>
 * @since 2023/6/7 16:20
 */
@Service
@Slf4j
public class GroundPromotionCMServiceImpl implements GroundPromotionCMService {

    @Autowired
    private OrgService orgService;

    @Autowired
    private GroundPromotionRepository groundPromotionRepository;

    @Autowired
    private PromotionalDealRepository promotionalDealRepository;

    @Autowired
    private GroundMaterialRepository groundMaterialRepository;

    @Autowired
    private BizDistributorService bizDistributorService;

    @Autowired
    private GroundPromotionQueryService groundPromotionQueryService;

    @Autowired
    private GroundDistributorRepository groundDistributorRepository;

    @Autowired
    private MerchantsRepository merchantsRepository;

    @Autowired
    private OrgRepository orgRepository;

    @Autowired
    private ShopAcl shopAcl;

    @Autowired
    private MessageAcl messageAcl;

    //private static final ThreadPoolExecutor EXECUTOR =
    //        ResizableCapacityThreadPoolExecutorFactory.create("GroundPromotionCMService_Pool", 50, 100, 10, TimeUnit.SECONDS,
    //                1000);
    private static final ThreadPoolExecutor cmSubmitPool =
            (ThreadPoolExecutor) ExecutorServices.forThreadPoolExecutor("GroundPromotionCMSubmit_Pool", 5, 20, 10,
                    TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final ThreadPoolExecutor generateMaterialPool =
            (ThreadPoolExecutor) ExecutorServices.forThreadPoolExecutor("GroundPromotionGenerateMaterial_Pool", 2, 20, 10,
                    TimeUnit.SECONDS, new ArrayBlockingQueue<>(5000), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final ThreadPoolExecutor notifyPool =
            (ThreadPoolExecutor) ExecutorServices.forThreadPoolExecutor("GroundPromotionNotify_Pool", 5, 20, 10,
                    TimeUnit.SECONDS, new ArrayBlockingQueue<>(5000), new ThreadPoolExecutor.CallerRunsPolicy());

    // 2023.10.18 前端以高为基准进行等比例缩放，因此去除该值
    //private static final int MATERIAL_SHOW_WIDTH = 260;

    private static final int MATERIAL_SHOW_HEIGHT = 400;

    private static final int MATERIAL_MIS_X = 3;

    private static final int MATERIAL_MIS_Y = 397;

    private static final String MATERIAL_ZIP_CONFIG = Lion.getString(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.ground.promotion.material.zip");

    public static final int RETRY_CNT = 3;

    @Override
    public RemoteResponse<GroundPromotionShopDTO> cmQueryDistribution(GroundPromotionCMQueryRequest request) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmQueryDistribution(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionCMQueryRequest)");
        try {
            if (Objects.isNull(request) || StringUtils.isEmpty(request.getCmMisId()) || request.getGroundPromotionId() <= 0) {
                log.error("[cmQueryDistribution] req param invalid, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("param invalid");
            }
            ////1.检查角色
            //RemoteResponse<Boolean> checkRep = orgService.checkIsValidUserType(request.getCmMisId(), UserTypeEnum.CITY_MANAGER.getCode());
            //if (checkRep.getData() == null || !checkRep.getData()) {
            //    log.error("[cmQueryDistribution] misId invalid, req is {}", JSONObject.toJSONString(request));
            //    return RemoteResponse.fail("misId invalid");
            //}
            //2.检验地推活动有效性
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmSubmitDistribution] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotionId invalid");
            }
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotion,
                    Lists.newArrayList(SALES_PERSON_REPORT.getCode(), CITY_MANAGER_CONFIRM.getCode(), CITY_MANAGER_DISTRIBUTE.getCode()));
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                log.error("[cmSubmitDistribution] promotionalDeals empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.success(null);
            }
            //3.填充门店信息、商品信息
            return RemoteResponse.success(assembleGroundPromotionShopDeals(groundPromotion, promotionalDeals, request.getAppVersion()));
        } catch (Exception e) {
            log.error("[cmQueryDistribution] error, req is {}, exception is ", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<String> cmSubmitDistribution(GroundPromotionCMSubmitRequest request) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmSubmitDistribution(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionCMSubmitRequest)");
        try {
            if (Objects.isNull(request) || StringUtils.isEmpty(request.getCmMisId()) || request.getGroundPromotionId() <= 0 ||
                    CollectionUtils.isEmpty(request.getDistributions())) {
                log.error("[cmSubmitDistribution] req param invalid, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("参数错误");
            }
            List<PromotionShopDistributionDTO> distributions = request.getDistributions();
            if (distributions.stream().anyMatch(distribution -> CollectionUtils.isEmpty(distribution.getMisIds()))) {
                return RemoteResponse.fail("门店无对应销售人员无法提交");
            }
            //RemoteResponse<Boolean> checkRep = orgService.checkIsValidUserType(request.getCmMisId(), UserTypeEnum.CITY_MANAGER.getCode());
            //if (checkRep.getData() == null || !checkRep.getData()) {
            //    log.error("[cmSubmitDistribution] misId invalid, req is {}", JSONObject.toJSONString(request));
            //    return RemoteResponse.fail("misId invalid");
            //}
            //2.检查地推活动是否有效
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmSubmitDistribution] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotionId invalid");
            }
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotion,
                    Lists.newArrayList(SALES_PERSON_REPORT.getCode(), CITY_MANAGER_CONFIRM.getCode()));
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                log.error("[cmSubmitDistribution] promotionalDeals empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotion deals empty");
            }
            Map<Long, List<String>> shopId2MisIds = request.getDistributions().stream()
                            .filter(distribution -> distribution.getPointId() == 0L)
                            .collect(Collectors.toMap(PromotionShopDistributionDTO::getShopId, PromotionShopDistributionDTO::getMisIds, (o1, o2) -> o2));
            Map<Long, List<PromotionalDeal>> shopId2PromotionDeals = promotionalDeals.stream()
                            .filter(promotionalDeal -> promotionalDeal.getPointId() == 0L)
                            .collect(Collectors.groupingBy(PromotionalDeal::getShopId));

            Map<Long, List<String>> pointId2MisIds = request.getDistributions().stream()
                            .filter(distribution -> distribution.getPointId() != 0L)
                            .collect(Collectors.toMap(PromotionShopDistributionDTO::getPointId, PromotionShopDistributionDTO::getMisIds, (o1, o2) -> o2));
            Map<Long, List<PromotionalDeal>> pointId2PromotionDeals = promotionalDeals.stream()
                            .filter(promotionalDeal -> promotionalDeal.getPointId() != 0L)
                            .collect(Collectors.groupingBy(PromotionalDeal::getPointId));

            cmSubmitPool.execute(() -> {
                //3.更新绑定人员
                List<Map<Long, List<String>>> updatedMap = processDistribution(groundPromotion, shopId2MisIds, shopId2PromotionDeals, pointId2MisIds, pointId2PromotionDeals);
                log.info("[cmSubmitDistribution] processDistribution success, shopId2misIds is {}, pointId2MisIds is {}, updatedMap is {}", JSONObject.toJSONString(shopId2MisIds), JSONObject.toJSONString(pointId2MisIds), JSONObject.toJSONString(updatedMap));
                //4.生成物料
                generateMaterial(groundPromotion.getId(), updatedMap.get(0), updatedMap.get(1));
                //5.插入org
                List<String> misIdList = updatedMap.stream()
                            .flatMap(map -> map.values().stream())
                            .flatMap(List::stream)
                            .distinct()
                            .collect(Collectors.toList());
                insertSaleOrg(groundPromotion.getCityId(), misIdList, groundPromotion.getBuId());
                //6.通知销售
                notifySale(groundPromotion, misIdList);
            });
            return RemoteResponse.success("success");
        } catch (Exception e) {
            log.error("[cmSubmitDistribution] error, req is {}, exception is ", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Deprecated
    @Override
    public RemoteResponse<String> cmBatchSubmitDistribution(String misId, List<Long> groundPromotionIdList) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmBatchSubmitDistribution(java.lang.String,java.util.List)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Transactional
    public Map<Long, String> cmSubmitDistribution(long groundPromotionId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmSubmitDistribution(long)");
        Map<Long, String> resultMap = Maps.newHashMap();
        try {
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(groundPromotionId);
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotionId,
                    Lists.newArrayList(SALES_PERSON_REPORT.getCode(), CITY_MANAGER_CONFIRM.getCode()));
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                log.error("[cmSubmitDistribution] promotionalDeals empty, groundPromotionId is {}", groundPromotionId);
                resultMap.put(groundPromotionId, "活动无门店/团单");
                return resultMap;
            }
            if (promotionalDeals.stream().anyMatch(promotionalDeal -> StringUtils.isEmpty(promotionalDeal.getBindMisid()))) {
                resultMap.put(groundPromotionId, "门店/点位未分配销售");
                return resultMap;
            }
            Map<Long, List<String>> shopId2MisIds = Maps.newHashMap();
            Map<Long, List<PromotionalDeal>> shopId2PromotionDeals = Maps.newHashMap();
            Map<Long, List<String>> pointId2MisIds = Maps.newHashMap();
            Map<Long, List<PromotionalDeal>> pointId2PromotionDeals = Maps.newHashMap();

            switch (GroundPromotionScopeTypeEnum.fromCode(groundPromotion.getScopeType())) {
                case SHOP:
                    shopId2MisIds = promotionalDeals.stream()
                            .collect(Collectors.toMap(
                                    PromotionalDeal::getShopId,
                                    promotionalDeal -> Arrays.asList(promotionalDeal.getBindMisid().split(BIND_MIS_ID_SEPARATOR)),
                                    (ov, nv) -> nv));
                    shopId2PromotionDeals = promotionalDeals.stream()
                            .collect(Collectors.groupingBy(PromotionalDeal::getShopId));
                    break;
                case POINT:
                case MALL:
                    pointId2MisIds = promotionalDeals.stream()
                            .collect(Collectors.toMap(
                                    PromotionalDeal::getPointId,
                                    promotionalDeal -> Arrays.asList(promotionalDeal.getBindMisid().split(BIND_MIS_ID_SEPARATOR)),
                                    (ov, nv) -> nv));
                    pointId2PromotionDeals = promotionalDeals.stream()
                            .collect(Collectors.groupingBy(PromotionalDeal::getPointId));
                    break;
            }
            // 更新绑定人员
            List<Map<Long, List<String>>> updatedMap = processDistribution(groundPromotion, shopId2MisIds, shopId2PromotionDeals, pointId2MisIds, pointId2PromotionDeals);
            // 生成物料
            generateMaterial(groundPromotion.getId(), updatedMap.get(0), updatedMap.get(1));
            // 插入org
            List<String> misIdList = updatedMap.stream()
                    .flatMap(map -> map.values().stream())
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
            insertSaleOrg(groundPromotion.getCityId(), misIdList, groundPromotion.getBuId());
            // 通知销售
            notifySale(groundPromotion, misIdList);
            resultMap.put(groundPromotionId, "success");
            return resultMap;
        } catch (Exception e) {
            log.error("[cmSubmitDistribution] error, groundPromotionId is {}, exception is", groundPromotionId, e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            resultMap.put(groundPromotionId, "success");
            return resultMap;
        }
    }

    @Override
    public RemoteResponse<String> cmSubmitDistributionWithExtInfo(GroundPromotionCMSubmitRequest request) {
        try {
            if (Objects.isNull(request) || StringUtils.isEmpty(request.getCmMisId()) || request.getGroundPromotionId() <= 0 ||
                    CollectionUtils.isEmpty(request.getDistributions())) {
                log.error("[cmSubmitDistribution] req param invalid, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("参数错误");
            }
            List<PromotionShopDistributionDTO> distributions = request.getDistributions();
            if (distributions.stream().anyMatch(distribution -> CollectionUtils.isEmpty(distribution.getMisIds()))) {
                return RemoteResponse.fail("门店无对应销售人员无法提交");
            }
            //2.检查地推活动是否有效
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmSubmitDistribution] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotionId invalid");
            }
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotion,
                    Lists.newArrayList(SALES_PERSON_REPORT.getCode(), CITY_MANAGER_CONFIRM.getCode()));
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                log.error("[cmSubmitDistribution] promotionalDeals empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotion deals empty");
            }

            Map<Pair<Long, DistributorExtInfoDTO>, List<String>> shopId2MisIds = request.getDistributions().stream()
                    .filter(distribution -> distribution.getPointId() == 0L)
                    .collect(Collectors.groupingBy(distribution -> Pair.of(distribution.getShopId(), distribution.getDistributorExtInfoDTO())))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            entry -> entry.getValue().stream().flatMap(distribution -> distribution.getMisIds().stream()).collect(Collectors.toList())));

            Map<Pair<Long, DistributorExtInfoDTO>, List<PromotionalDeal>> shopId2PromotionDeals = promotionalDeals.stream()
                    .filter(promotionalDeal -> promotionalDeal.getPointId() == 0L)
                    .collect(Collectors.groupingBy(promotionalDeal -> Pair.of(promotionalDeal.getShopId(), JSONObject.parseObject(promotionalDeal.getExtInfo(), DistributorExtInfoDTO.class)),
                            Collectors.mapping(Function.identity(), Collectors.toList())));

            Map<Pair<Long, DistributorExtInfoDTO>, List<String>> pointId2MisIds = request.getDistributions().stream()
                    .filter(distribution -> distribution.getPointId() != 0L)
                    .collect(Collectors.groupingBy(distribution -> Pair.of(distribution.getPointId(), distribution.getDistributorExtInfoDTO())))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            entry -> entry.getValue().stream().flatMap(distribution -> distribution.getMisIds().stream()).distinct().collect(Collectors.toList())));

            Map<Pair<Long, DistributorExtInfoDTO>, List<PromotionalDeal>> pointId2PromotionDeals = promotionalDeals.stream()
                    .filter(promotionalDeal -> promotionalDeal.getPointId() != 0L)
                    .collect(Collectors.groupingBy(promotionalDeal -> Pair.of(promotionalDeal.getPointId(), JSONObject.parseObject(promotionalDeal.getExtInfo(), DistributorExtInfoDTO.class)),
                            Collectors.mapping(Function.identity(), Collectors.toList())));

            cmSubmitPool.execute(() -> {
                //3.更新绑定人员
                List<Map<Pair<Long, DistributorExtInfoDTO>, List<String>>> updatedMap = processDistributionWithExtInfo(groundPromotion, shopId2MisIds, shopId2PromotionDeals, pointId2MisIds, pointId2PromotionDeals);
                log.info("[cmSubmitDistribution] processDistribution success, shopId2misIds is {}, pointId2MisIds is {}, updatedMap is {}", JSONObject.toJSONString(shopId2MisIds), JSONObject.toJSONString(pointId2MisIds), JSONObject.toJSONString(updatedMap));
                //4.生成物料
                generateMaterialWithExtInfo(groundPromotion.getId(), updatedMap.get(0), updatedMap.get(1));
                log.info("[cmSubmitDistribution] generateMaterial success, groundPromotion id is {}, updatedMap is {}", groundPromotion.getId(), JSONObject.toJSONString(updatedMap));
                //5.插入org
                List<String> misIdList = updatedMap.stream()
                            .flatMap(map -> map.values().stream())
                            .flatMap(List::stream)
                            .distinct()
                            .collect(Collectors.toList());
                insertSaleOrg(groundPromotion.getCityId(), misIdList, groundPromotion.getBuId());
                //6.通知销售
                notifySale(groundPromotion, misIdList);
            });
            return RemoteResponse.success("success");
        } catch (Exception e) {
            log.error("[cmSubmitDistribution] error, req is {}, exception is ", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<String> cmSubmitStandardDistributionWithExtInfo(GroundPromotionCMSubmitRequest request) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmSubmitStandardDistributionWithExtInfo(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionCMSubmitRequest)");
        try {
            if (Objects.isNull(request) || StringUtils.isEmpty(request.getCmMisId()) || request.getGroundPromotionId() <= 0 ||
                    CollectionUtils.isEmpty(request.getDistributions())) {
                log.error("[cmSubmitDistribution] req param invalid, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("参数错误");
            }
            List<PromotionShopDistributionDTO> distributions = request.getDistributions();
            if (distributions.stream().anyMatch(distribution -> CollectionUtils.isEmpty(distribution.getMisIds()))) {
                return RemoteResponse.fail("门店无对应销售人员无法提交");
            }
            //2.检查地推活动是否有效
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmSubmitDistribution] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotionId invalid");
            }
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotion,
                    Lists.newArrayList(SALES_PERSON_REPORT.getCode(), CITY_MANAGER_CONFIRM.getCode()));
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                log.error("[cmSubmitDistribution] promotionalDeals empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotion deals empty");
            }

            Map<DistributorExtInfoDTO, List<String>> extInfo2MisIds = request.getDistributions().stream()
                    .collect(Collectors.groupingBy(distribution -> distribution.getDistributorExtInfoDTO()))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            entry -> entry.getValue().stream().flatMap(distribution -> distribution.getMisIds().stream()).collect(Collectors.toList())));

            Map<DistributorExtInfoDTO, List<PromotionalDeal>> extInfo2PromotionDeals = promotionalDeals.stream()
                    .collect(Collectors.groupingBy(promotionalDeal -> JSONObject.parseObject(promotionalDeal.getExtInfo(), DistributorExtInfoDTO.class)));

            cmSubmitPool.execute(() -> {
                //3.更新绑定人员
                Map<DistributorExtInfoDTO, List<String>> updatedMap = processStandardDistributionWithExtInfo(groundPromotion, extInfo2MisIds, extInfo2PromotionDeals);
                log.info("[cmSubmitDistribution] processDistribution success, extInfo2MisIds is {}, updatedMap is {}", JSONObject.toJSONString(extInfo2MisIds), JSONObject.toJSONString(updatedMap));
                //4.生成物料
                generateStandardMaterialWithExtInfo(groundPromotion.getId(), updatedMap);
                log.info("[cmSubmitDistribution] generateMaterial success, groundPromotion id is {}, updatedMap is {}", groundPromotion.getId(), JSONObject.toJSONString(updatedMap));
                //5.插入org
                List<String> misIdList = updatedMap.values().stream()
                        .flatMap(List::stream)
                        .distinct()
                        .collect(Collectors.toList());
                insertSaleOrg(groundPromotion.getCityId(), misIdList, groundPromotion.getBuId());
                //6.通知销售
                notifySale(groundPromotion, misIdList);
            });
            return RemoteResponse.success("success");
        } catch (Exception e) {
            log.error("[cmSubmitDistribution] error, req is {}, exception is ", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<String> cmNotifyDistribution(long groundPromotionId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmNotifyDistribution(long)");
        try {
            //1.参数校验
            if (groundPromotionId <= 0) {
                log.error("[cmNotifyDistribution] req param invalid, groundPromotionId is {}", groundPromotionId);
                return RemoteResponse.fail("param invalid");
            }
            //2.检查地推活动是否有效
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(groundPromotionId);
            if (groundPromotion == null) {
                log.error("[cmNotifyDistribution] groundPromotion empty, groundPromotionId is {}", groundPromotionId);
                return RemoteResponse.fail("promotion empty");
            }
            //3.判断是否已经分配
            List<PromotionalDeal> notSubmitDeals = queryPromotionDealByPromotionId(groundPromotion, Lists.newArrayList(MERCHANTS_OPERATOR_CONFIRM.getCode(), CITY_MANAGER_CONFIRM.getCode(), SALES_PERSON_REPORT.getCode()));
            if (CollectionUtils.isEmpty(notSubmitDeals)) {
                log.error("[cmNotifyDistribution] has confirmed promotion deals, groundPromotionId is {}", groundPromotionId);
                return RemoteResponse.success("has confirmed");
            }
            //4.未分配，查询招商运营和城市主管账号
            List<Merchants> merchants = merchantsRepository.queryById(groundPromotion.getMerchantsId());
            List<String> merchantsMisIds = merchants.stream().map(Merchants::getMisId).distinct().collect(Collectors.toList());
            OrgRequest orgRequest = new OrgRequest();
            orgRequest.setUserType(Lists.newArrayList(UserTypeEnum.CITY_MANAGER.getCode()));
            orgRequest.setStatus(OrgStatusEnum.VALID.getCode());
            orgRequest.setDpCityIdList(Lists.newArrayList(groundPromotion.getCityId()));
            RemoteResponse<List<OrgDTO>> orgResp = orgService.query(orgRequest);
            List<OrgDTO> orgList = Optional.ofNullable(orgResp).map(RemoteResponse::getData).orElse(Lists.newArrayList());
            Set<String> cmMidIds = orgList.stream().map(OrgDTO::getMisId).collect(Collectors.toSet());
            cmMidIds.addAll(merchantsMisIds);
            List<String> allMisIds = Lists.newArrayList(cmMidIds);
            //5.执行通知
            List<List<String>> partitions = Lists.partition(allMisIds, 10);
            for (List<String> partition : partitions) {
                notifyPool.execute(() -> notifyCmSinglePartition(groundPromotion, partition));
            }
        } catch (Exception e) {
            log.error("[cmNotifyDistribution] error, groundPromotionId is {}, exception is ", groundPromotionId, e);
            return RemoteResponse.fail("系统异常");
        }
        return RemoteResponse.success("success");
    }

    @Override
    public RemoteResponse<GroundPromotionSaleListDTO> cmQuerySaleList(GroundPromotionCMQueryRequest request) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmQuerySaleList(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionCMQueryRequest)");
        try {
            if (request == null || request.getGroundPromotionId() <= 0) {
                log.error("[cmQuerySaleList] req param invalid, request is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("param invalid");
            }
            //1.检查角色
            //RemoteResponse<Boolean> checkRep = orgService.checkIsValidUserType(request.getCmMisId(), UserTypeEnum.CITY_MANAGER.getCode());
            //if (checkRep.getData() == null || !checkRep.getData()) {
            //    log.error("[cmQuerySaleList] misId invalid, req is {}", JSONObject.toJSONString(request));
            //    return RemoteResponse.fail("misId invalid");
            //}
            //2.检验地推活动有效性
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmQuerySaleList] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("promotionId invalid");
            }
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotion, Lists.newArrayList());
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                log.error("[cmQuerySaleList] promotionalDeals empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.success(null);
            }
            List<String> misIds = promotionalDeals.stream().map(PromotionalDeal::getBindMisid).flatMap(bindMisIds ->
                    Arrays.stream(StringUtils.split(bindMisIds, BIND_MIS_ID_SEPARATOR))).distinct().collect(Collectors.toList());
            GroundPromotionSaleListDTO saleListDTO = new GroundPromotionSaleListDTO();
            List<OrgDTO> saleList = Lists.newArrayList();
            saleListDTO.setSaleList(saleList);
            misIds.forEach(misId -> {
                OrgDTO orgDTO = new OrgDTO();
                orgDTO.setMisId(misId);
                orgDTO.setCityId(groundPromotion.getCityId());
                orgDTO.setStatus(1);
                orgDTO.setUserType(UserTypeEnum.SALE.getCode());
                saleList.add(orgDTO);
            });
            return RemoteResponse.success(saleListDTO);
        } catch (Exception e) {
            log.error("[cmSubmitDistribution] error, req is {}, exception is ", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail("系统异常");
        }
    }

    private void notifyCmSinglePartition(GroundPromotion groundPromotion, List<String> partition) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.notifyCmSinglePartition(GroundPromotion,List)");
        try {
            Map<String, String> message = Maps.newHashMap();
            message.put("title", "地推活动即将开始，请尽快提交名单");
            message.put("image", "https://img.meituan.net/beautyimg/5e153a48e13054a950cb40e7e50e209581614.jpg");
            message.put("content", "点击卡片查看");
            message.put("link", LinkUtils.buildCityManagerLink(groundPromotion.getId(), 1));
            messageAcl.pushMessage(new BaseXMMessageDTO() {
                @Override
                public XMMessageTypeEnum getType() {
                    return XMMessageTypeEnum.LINK;
                }

                @Override
                public String toJSONString() {
                    return JSONObject.toJSONString(message);
                }
            }, partition);
        } catch (Exception e) {
            log.error("[notifyCmSinglePartition] error, groundPromotionId is {}, misIds: {}, exception is ", groundPromotion.getId(), JSONObject.toJSONString(partition), e);
        }
    }

    private GroundPromotionShopDTO assembleGroundPromotionShopDeals(GroundPromotion groundPromotion, List<PromotionalDeal> promotionalDeals, String appVersion) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.assembleGroundPromotionShopDeals(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion,java.util.List,java.lang.String)");
        //查询点位和商户信息
        int cityId = groundPromotion.getCityId();
        List<GroundPromotionPoint> pointList = groundPromotionQueryService.queryPoint(promotionalDeals.stream()
                .filter(promotionalDeal -> promotionalDeal.getPointId() != 0L)
                .map(PromotionalDeal::getPointId).distinct().collect(Collectors.toList()));

        List<DpPoiDTO> dpPoiDTOList = shopAcl.getDpPoiDTOList(promotionalDeals.stream()
                .filter(promotionalDeal -> promotionalDeal.getPointId() == 0L)
                .map(PromotionalDeal::getShopId).distinct().collect(Collectors.toList()));

        Map<Long, List<PromotionalDeal>> pointId2PromotionDeals = promotionalDeals.stream()
                .filter(promotionalDeal -> promotionalDeal.getPointId() != 0L)
                .collect(Collectors.groupingBy(PromotionalDeal::getPointId));
        Map<Long, List<PromotionalDeal>> shopId2PromotionDeals = promotionalDeals.stream()
                .collect(Collectors.groupingBy(PromotionalDeal::getShopId));
        Map<Long, List<PromotionalDeal>> shopExcludePointId2PromotionDeals = promotionalDeals.stream()
                .filter(promotionalDeal -> promotionalDeal.getPointId() == 0L)
                .collect(Collectors.groupingBy(PromotionalDeal::getShopId));

        //查询团单信息
        Map<Integer, DealProductDTO> dealId2DealProductVO = Maps.newHashMap();
        if(Lion.getBoolean(Environment.getAppName(),"cmQueryDistribution.assembleGroundPromotionShopDeals",false)){
            Map<Long, List<DealProductDTO>> shopId2DealProductVOs = groundPromotionQueryService.batchQueryDeals(shopId2PromotionDeals, cityId, appVersion);
            dealId2DealProductVO = shopId2DealProductVOs.entrySet().stream().flatMap(entry -> entry.getValue().stream())
                    .collect(Collectors.toMap(DealProductDTO::getProductId, Function.identity(), (o1, o2) -> o2));
        }

        GroundPromotionShopDTO groundPromotionShopDTO = new GroundPromotionShopDTO();
        groundPromotionShopDTO.setGroundPromotionStatus(groundPromotion.getStatus());
        groundPromotionShopDTO.setGroundPromotionId(groundPromotion.getId());
        groundPromotionShopDTO.setPromotionShopCount(pointList.size() + dpPoiDTOList.size());
        groundPromotionShopDTO.setChosenPromotionShopCount(buildSaleHasSubmitShopCnt(pointId2PromotionDeals, shopExcludePointId2PromotionDeals));
        groundPromotionShopDTO.setEditStatus(buildEditStatus(groundPromotion, promotionalDeals));

        Map<Integer, DealProductDTO> finalDealId2DealProductVO = dealId2DealProductVO;
        List<PromotionShopDTO> promotionShopDTOList = pointList.stream().sorted(Comparator.comparingLong(GroundPromotionPoint::getId))
                .map(groundPromotionPoint -> buildPromotionShopDTOWithPoint(groundPromotionPoint, pointId2PromotionDeals, finalDealId2DealProductVO))
                .collect(Collectors.toList());

        promotionShopDTOList.addAll(dpPoiDTOList.stream().sorted(Comparator.comparing(DpPoiDTO::getShopId))
                .map(dpPoiDTO -> buildPromotionShopDTO(dpPoiDTO, shopExcludePointId2PromotionDeals, finalDealId2DealProductVO))
                .collect(Collectors.toList()));
        groundPromotionShopDTO.setPromotionShopDTOList(promotionShopDTOList);
        return groundPromotionShopDTO;
    }

    private Boolean buildEditStatus(GroundPromotion groundPromotion, List<PromotionalDeal> promotionalDeals) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildEditStatus(GroundPromotion,List)");
        if (CollectionUtils.isEmpty(promotionalDeals)) {
            return Boolean.TRUE;
        }
        if (groundPromotion.getStatus() >= GroundPromotionActEnum.END.getCode() || new Date().after(groundPromotion.getGroundEndTime())) {
            return Boolean.TRUE;
        }
        //销售已报名或者主管已分配，销售均不可提交
        return promotionalDeals.stream().anyMatch(promotionalDeal ->
                promotionalDeal.getStatus() == GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
    }

    private int buildSaleHasSubmitShopCnt(Map<Long, List<PromotionalDeal>> pointId2PromotionDeals, Map<Long, List<PromotionalDeal>> shopId2PromotionDeals) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildSaleHasSubmitShopCnt(java.util.Map,java.util.Map)");
        AtomicInteger cnt = new AtomicInteger();
        pointId2PromotionDeals.forEach((pointId, deals) -> {
            if (CollectionUtils.isEmpty(deals)) {
                return;
            }
            if (deals.stream().anyMatch(deal -> deal.getStatus() == SALES_PERSON_REPORT.getCode() || deal.getStatus() == CITY_MANAGER_DISTRIBUTE.getCode())) {
                cnt.getAndIncrement();
            }
        });
        shopId2PromotionDeals.forEach((shopId, deals) -> {
            if (CollectionUtils.isEmpty(deals)) {
                return;
            }
            if (deals.stream().anyMatch(deal -> deal.getStatus() == SALES_PERSON_REPORT.getCode() || deal.getStatus() == CITY_MANAGER_DISTRIBUTE.getCode())) {
                cnt.getAndIncrement();
            }
        });
        return cnt.get();
    }

    private PromotionShopDTO buildPromotionShopDTO(DpPoiDTO dpPoiDTO, Map<Long, List<PromotionalDeal>> shopId2PromotionDeals,
                                                   Map<Integer, DealProductDTO> dealId2DealProductVO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildPromotionShopDTO(com.sankuai.sinai.data.api.dto.DpPoiDTO,java.util.Map,java.util.Map)");
        if (dpPoiDTO == null || MapUtils.isEmpty(shopId2PromotionDeals)) {
            return null;
        }
        PromotionShopDTO promotionShopDTO = new PromotionShopDTO();
        promotionShopDTO.setShopId(dpPoiDTO.getShopId());
        promotionShopDTO.setIsPoint(false);
        promotionShopDTO.setPointId(0L);
        String shopName = dpPoiDTO.getShopName();
        if (StringUtils.isNotEmpty(dpPoiDTO.getBranchName())) {
            shopName += "（" + dpPoiDTO.getBranchName() + "）";
        }
        promotionShopDTO.setName(shopName);
        promotionShopDTO.setLocation(dpPoiDTO.getAddress());

        List<PromotionalDeal> promotionDeals = shopId2PromotionDeals.getOrDefault(dpPoiDTO.getShopId(), Lists.newArrayList());
        Set<String> shopBindMisIds = getSingleShopBindMisIds(promotionDeals);
        promotionShopDTO.setHasSubmit(getCityManagerHasDistribute(promotionDeals));
        promotionShopDTO.setMisIds(Lists.newArrayList(shopBindMisIds));
        promotionShopDTO.setPromotionDealList(buildPromotionDealDTOs(promotionDeals, dealId2DealProductVO));
        return promotionShopDTO;
    }

    private PromotionShopDTO buildPromotionShopDTOWithPoint(GroundPromotionPoint point, Map<Long, List<PromotionalDeal>> pointId2PromotionDeals,
                                                            Map<Integer, DealProductDTO> dealId2DealProductVO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildPromotionShopDTOWithPoint(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint,java.util.Map,java.util.Map)");
        if (point == null || MapUtils.isEmpty(pointId2PromotionDeals)) {
            return null;
        }
        PromotionShopDTO promotionShopDTO = new PromotionShopDTO();
        promotionShopDTO.setShopId(0L);
        promotionShopDTO.setIsPoint(true);
        promotionShopDTO.setPointId(point.getId());
        promotionShopDTO.setName(point.getPointName());

        List<PromotionalDeal> promotionDeals = pointId2PromotionDeals.getOrDefault(point.getId(), Lists.newArrayList());
        Set<String> shopBindMisIds = getSingleShopBindMisIds(promotionDeals);
        promotionShopDTO.setHasSubmit(getCityManagerHasDistribute(promotionDeals));
        promotionShopDTO.setMisIds(Lists.newArrayList(shopBindMisIds));
        promotionShopDTO.setPromotionDealList(buildPromotionDealDTOs(promotionDeals, dealId2DealProductVO));
        return promotionShopDTO;
    }

    private Boolean getCityManagerHasDistribute(List<PromotionalDeal> promotionDeals) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.getCityManagerHasDistribute(java.util.List)");
        return promotionDeals.stream().anyMatch(promotionalDeal -> promotionalDeal.getStatus() == GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
    }

    private List<PromotionDealDTO> buildPromotionDealDTOs(List<PromotionalDeal> promotionalDeals, Map<Integer, DealProductDTO> dealId2DealProductVO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildPromotionDealDTOs(java.util.List,java.util.Map)");
        return promotionalDeals.stream().map(promotionalDeal -> {
            DealProductDTO dealProductVO = dealId2DealProductVO.getOrDefault(promotionalDeal.getDealId().intValue(), new DealProductDTO());
            PromotionDealDTO promotionDealDTO = new PromotionDealDTO();
            promotionDealDTO.setDealId(promotionalDeal.getDealId());
            promotionDealDTO.setMisId(Lists.newArrayList(StringUtils.split(promotionalDeal.getBindMisid(), BIND_MIS_ID_SEPARATOR)));
            promotionDealDTO.setPromotionalType(PromoTypeEnum.DIRECT_PROMO.getDesc());
            promotionDealDTO.setPromotionalDealId(promotionalDeal.getId());
            promotionDealDTO.setTitle(dealProductVO.getTitle());
            promotionDealDTO.setPrice(dealProductVO.getSalePrice());
            promotionDealDTO.setLink(dealProductVO.getDetailJumpUrl());
            return promotionDealDTO;
        }).collect(Collectors.toList());
    }


    private Set<String> getSingleShopBindMisIds(List<PromotionalDeal> promotionDeals) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.getSingleShopBindMisIds(java.util.List)");
        if (CollectionUtils.isEmpty(promotionDeals)) {
            return Sets.newHashSet();
        }
        return promotionDeals.stream().flatMap(promotionalDeal -> Arrays.stream(StringUtils.split(promotionalDeal.getBindMisid(), BIND_MIS_ID_SEPARATOR)))
                .collect(Collectors.toSet());
    }

    private void notifySale(GroundPromotion groundPromotion, List<String> misIdList) {
        if (CollectionUtils.isEmpty(misIdList)) {
            return;
        }
        // 过滤脱敏手机号
        misIdList = misIdList.stream()
                .filter(misId -> !misId.matches("^\\d{3}\\*{4}\\d{4}$"))
                .collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(misIdList, 10);
        for (List<String> partition : partitions) {
            notifyPool.execute(() -> notifySalesSinglePartition(groundPromotion, partition));
        }
    }

    private void notifySalesSinglePartition(GroundPromotion groundPromotion, List<String> partition) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.notifySalesSinglePartition(GroundPromotion,List)");
        try {
            Map<String, String> message = Maps.newHashMap();
            message.put("title", groundPromotion.getName() + "已生成您的专属二维码，活动将于" +
                    DateUtils.format(groundPromotion.getGroundStartTime(), DateUtils.DEFAULT_DAY_PATTERN) + "开始，请及时准备物料");
            message.put("image", "https://img.meituan.net/beautyimg/5e153a48e13054a950cb40e7e50e209581614.jpg");
            message.put("content", "点击卡片查看");
            message.put("link", LinkUtils.buildSaleMaterialLink(groundPromotion.getId()));
            messageAcl.pushMessage(new BaseXMMessageDTO() {
                @Override
                public XMMessageTypeEnum getType() {
                    return XMMessageTypeEnum.LINK;
                }

                @Override
                public String toJSONString() {
                    return JSONObject.toJSONString(message);
                }
            }, partition);
        } catch (Exception e) {
            log.error("[notifySinglePartition] error, groundPromotionId is {}, misIds: {}, exception is ", groundPromotion.getId(), JSONObject.toJSONString(partition), e);
        }
    }


    private void generateMaterial(long groundPromotionId, Map<Long, List<String>> updatedShopId2MisIds, Map<Long, List<String>> updatedPointId2MisIds) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.generateMaterial(long,java.util.Map,java.util.Map)");
        generateMaterialPool.setCorePoolSize(50);
        generateMaterialPool.setMaximumPoolSize(100);
        if (MapUtils.isEmpty(updatedShopId2MisIds) && MapUtils.isEmpty(updatedPointId2MisIds)) {
            return;
        }
        List<Long> shopIds = Lists.newArrayList(updatedShopId2MisIds.keySet());
        List<DpPoiDTO> dpPoiDTOList = shopAcl.getDpPoiDTOList(shopIds);
        List<Long> pointIds = Lists.newArrayList(updatedPointId2MisIds.keySet());
        List<GroundPromotionPoint> pointList = groundPromotionQueryService.queryPoint(pointIds);
        if (CollectionUtils.isEmpty(dpPoiDTOList) && CollectionUtils.isEmpty(pointList)) {
            return;
        }
        Map<Long, DpPoiDTO> shopId2DpPoiDTO = dpPoiDTOList.stream().collect(Collectors.toMap(DpPoiDTO::getShopId, Function.identity(), (o1, o2) -> o2));

        Map<Long, GroundPromotionPoint> pointId2Point = pointList.stream().collect(Collectors.toMap(GroundPromotionPoint::getId, Function.identity(), (o1, o2) -> o2));
        List<CompletableFuture<Boolean>> generateFutures = Lists.newArrayList();
        for (Map.Entry<Long, List<String>> entry : updatedShopId2MisIds.entrySet()) {
            Long shopId = entry.getKey();
            List<String> misIds = entry.getValue();
            if (CollectionUtils.isEmpty(misIds)) {
                continue;
            }
            DpPoiDTO dpPoiDTO = shopId2DpPoiDTO.get(shopId);
            generateFutures.add(CompletableFuture.supplyAsync(() ->
                    generateSingleShopMaterial(groundPromotionId, dpPoiDTO, misIds, new DistributorExtInfoDTO()), generateMaterialPool));
        }
        for (Map.Entry<Long, List<String>> entry : updatedPointId2MisIds.entrySet()) {
            Long pointId = entry.getKey();
            List<String> misIds = entry.getValue();
            if (CollectionUtils.isEmpty(misIds)) {
                continue;
            }
            GroundPromotionPoint point = pointId2Point.get(pointId);
            generateFutures.add(CompletableFuture.supplyAsync(() ->
                    generateSinglePointMaterial(groundPromotionId, point, misIds, new DistributorExtInfoDTO()), generateMaterialPool));
        }

        //物料全部生成完成再返回，执行下一步操作
        CompletableFuture.allOf(generateFutures.toArray(new CompletableFuture[0])).join();
        generateMaterialPool.setCorePoolSize(2);
        generateMaterialPool.setMaximumPoolSize(20);
    }

    private void generateMaterialWithExtInfo(long groundPromotionId, Map<Pair<Long, DistributorExtInfoDTO>, List<String>> updatedShopId2MisIds, Map<Pair<Long, DistributorExtInfoDTO>, List<String>> updatedPointId2MisIds) {
        generateMaterialPool.setCorePoolSize(50);
        generateMaterialPool.setMaximumPoolSize(100);
        if (MapUtils.isEmpty(updatedShopId2MisIds) && MapUtils.isEmpty(updatedPointId2MisIds)) {
            return;
        }
        List<Long> shopIds = Lists.newArrayList();
        if (MapUtils.isNotEmpty(updatedShopId2MisIds)) {
            shopIds = updatedShopId2MisIds.keySet().stream().map(Pair::getLeft).distinct().collect(Collectors.toList());
        }
        List<DpPoiDTO> dpPoiDTOList = shopAcl.getDpPoiDTOList(shopIds);

        List<Long> pointIds = Lists.newArrayList();
        if (MapUtils.isNotEmpty(updatedPointId2MisIds)) {
            pointIds = updatedPointId2MisIds.keySet().stream().map(Pair::getLeft).distinct().collect(Collectors.toList());
        }
        List<GroundPromotionPoint> pointList = groundPromotionQueryService.queryPoint(pointIds);
        if (CollectionUtils.isEmpty(dpPoiDTOList) && CollectionUtils.isEmpty(pointList)) {
            return;
        }
        Map<Long, DpPoiDTO> shopId2DpPoiDTO = dpPoiDTOList.stream().collect(Collectors.toMap(DpPoiDTO::getShopId, Function.identity(), (o1, o2) -> o2));        Map<Long, GroundPromotionPoint> pointId2Point = pointList.stream().collect(Collectors.toMap(GroundPromotionPoint::getId, Function.identity(), (o1, o2) -> o2));
        List<CompletableFuture<Boolean>> generateFutures = Lists.newArrayList();
        for (Map.Entry<Pair<Long, DistributorExtInfoDTO>, List<String>> entry : updatedShopId2MisIds.entrySet()) {
            Long shopId = entry.getKey().getLeft();
            DistributorExtInfoDTO distributorExtInfoDTO = entry.getKey().getRight();
            List<String> misIds = entry.getValue();
            if (CollectionUtils.isEmpty(misIds)) {
                continue;
            }
            DpPoiDTO dpPoiDTO = shopId2DpPoiDTO.get(shopId);
            generateFutures.add(CompletableFuture.supplyAsync(() ->
                    generateSingleShopMaterial(groundPromotionId, dpPoiDTO, misIds, distributorExtInfoDTO), generateMaterialPool));
        }
        for (Map.Entry<Pair<Long, DistributorExtInfoDTO>, List<String>> entry : updatedPointId2MisIds.entrySet()) {
            Long pointId = entry.getKey().getLeft();
            DistributorExtInfoDTO distributorExtInfoDTO = entry.getKey().getRight();
            List<String> misIds = entry.getValue();
            if (CollectionUtils.isEmpty(misIds)) {
                continue;
            }
            GroundPromotionPoint point = pointId2Point.get(pointId);
            generateFutures.add(CompletableFuture.supplyAsync(() ->
                    generateSinglePointMaterial(groundPromotionId, point, misIds, distributorExtInfoDTO), generateMaterialPool));
        }

        //物料全部生成完成再返回，执行下一步操作
        CompletableFuture.allOf(generateFutures.toArray(new CompletableFuture[0])).join();
        generateMaterialPool.setCorePoolSize(2);
        generateMaterialPool.setMaximumPoolSize(20);
    }

    private void generateStandardMaterialWithExtInfo(long groundPromotionId, Map<DistributorExtInfoDTO, List<String>> updatedExtInfo2MisIds) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.generateStandardMaterialWithExtInfo(long,java.util.Map)");
        generateMaterialPool.setCorePoolSize(50);
        generateMaterialPool.setMaximumPoolSize(100);
        if (MapUtils.isEmpty(updatedExtInfo2MisIds)) {
            return;
        }

        List<CompletableFuture<Boolean>> generateFutures = Lists.newArrayList();
        for (Map.Entry<DistributorExtInfoDTO, List<String>> entry : updatedExtInfo2MisIds.entrySet()) {
            DistributorExtInfoDTO distributorExtInfoDTO = entry.getKey();
            List<String> misIds = entry.getValue();
            if (CollectionUtils.isEmpty(misIds)) {
                continue;
            }
            generateFutures.add(CompletableFuture.supplyAsync(() ->
                    generateStandardMaterial(groundPromotionId, misIds, distributorExtInfoDTO), generateMaterialPool));
        }

        //物料全部生成完成再返回，执行下一步操作
        CompletableFuture.allOf(generateFutures.toArray(new CompletableFuture[0])).join();
        generateMaterialPool.setCorePoolSize(2);
        generateMaterialPool.setMaximumPoolSize(20);
    }

    private boolean generateSingleShopMaterial(long groundPromotionId, DpPoiDTO dpPoiDTO, List<String> misIds, DistributorExtInfoDTO distributorExtInfoDTO) {
        for (String misId : misIds) {
            generalSingleSaleMaterial(groundPromotionId, dpPoiDTO, null, misId, distributorExtInfoDTO);
        }
        return Boolean.TRUE;
    }

    private boolean generateSinglePointMaterial(long groundPromotionId, GroundPromotionPoint point, List<String> misIds, DistributorExtInfoDTO distributorExtInfoDTO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.generateSinglePointMaterial(long,com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint,java.util.List,com.sankuai.carnation.distribution.groundpromotion.dto.DistributorExtInfoDTO)");
        for (String misId : misIds) {
            generalSingleSaleMaterial(groundPromotionId, null, point, misId, distributorExtInfoDTO);
        }
        return Boolean.TRUE;
    }

    private boolean generateStandardMaterial(long groundPromotionId, List<String> misIds, DistributorExtInfoDTO distributorExtInfoDTO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.generateStandardMaterial(long,java.util.List,com.sankuai.carnation.distribution.groundpromotion.dto.DistributorExtInfoDTO)");
        for (String misId : misIds) {
            generalSingleSaleMaterial(groundPromotionId, null, null, misId, distributorExtInfoDTO);
        }
        return Boolean.TRUE;
    }

    private void generalSingleSaleMaterial(long groundPromotionId, DpPoiDTO dpPoiDTO, GroundPromotionPoint point, String misId, DistributorExtInfoDTO distributorExtInfoDTO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.generalSingleSaleMaterial(long,com.sankuai.sinai.data.api.dto.DpPoiDTO,com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint,java.lang.String,com.sankuai.carnation.distribution.groundpromotion.dto.DistributorExtInfoDTO)");
        try {
            //1.生成分销码，并记录分销系统
            String distributorCode = editBizDistributorWithRetry(groundPromotionId, misId);
            if (StringUtils.isEmpty(distributorCode)) {
                Cat.logEvent("GroundPromotion", "GenDistributorCodeFailed");
                log.error("[generalSingleSaleMaterial] error, GenDistributorCodeFailed groundPromotionId is {}, shopId: {}, pointId: {}, misId: {}",
                        groundPromotionId, Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getShopId() : 0, Objects.nonNull(point) ? point.getId() : 0, misId);
                return;
            }
            //2.构建落地页链接
            String landingUrl;

            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(groundPromotionId);

            switch (GroundPromotionScopeTypeEnum.fromCode(groundPromotion.getScopeType())) {
                case SHOP:
                    landingUrl = LinkUtils.buildLandingPageLink(distributorCode, dpPoiDTO.getShopId(), 0);
                    break;
                case POINT:
                case MALL:
                    landingUrl = LinkUtils.buildPointLandingPageLink(distributorCode, point.getId(), point.getShowType(), 0);
                    break;
                case STANDARD_PRODUCT:
                    landingUrl = LinkUtils.buildPointLandingPageLink(distributorCode, 0, 2, 0);
                    break;
                default:
                    landingUrl = LinkUtils.buildLandingPageLink(distributorCode, 0, 0);
            }

            //3.查询地推活动所有物料
            List<GroundMaterial> groundMaterials = groundMaterialRepository.queryByPromotionId(groundPromotionId);
            if (CollectionUtils.isEmpty(groundMaterials)) {
                log.error("[generalSingleSaleMaterial] error, groundMaterials is empty, groundPromotionId: {}, misId: {}", groundPromotionId, misId);
                return;
            }

            //4.根据物料生成海报，并存储DB
            for (GroundMaterial groundMaterial : groundMaterials) {
                generateSingleMaterial(groundMaterial, landingUrl, groundPromotionId, dpPoiDTO, point, misId, distributorCode, distributorExtInfoDTO);
            }
        } catch (Exception e) {
            log.error("[generalSingleSaleMaterial] error, groundPromotionId is {}, shopId: {}, pointId: {}, misId: {}, exception is ",
                    groundPromotionId, Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getShopId() : 0, Objects.nonNull(point) ? point.getId() : 0, misId, e);
        }
    }

    private void generateSingleMaterial(GroundMaterial groundMaterial, String landingUrl, long groundPromotionId, DpPoiDTO dpPoiDTO, GroundPromotionPoint point,
                                        String misId, String distributorCode, DistributorExtInfoDTO distributorExtInfoDTO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.generateSingleMaterial(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial,java.lang.String,long,com.sankuai.sinai.data.api.dto.DpPoiDTO,com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint,java.lang.String,java.lang.String,com.sankuai.carnation.distribution.groundpromotion.dto.DistributorExtInfoDTO)");
        try {
            if (groundMaterial == null) {
                return;
            }
            String backgroundPic = groundMaterial.getPicturePath();
            //BigDecimal showWidthRealRatio = getShowWidthRealRatio(backgroundPic);
            BigDecimal showHeightRealRatio = getShowHeightRealRatio(backgroundPic);
            int qrCodeWidth = Optional.ofNullable(groundMaterial.getQrWidth()).map(BigDecimal::intValue).orElse(100);
            String qrCode = MaterialPicUtil.postQrCode(landingUrl, BigDecimal.valueOf(qrCodeWidth).multiply(showHeightRealRatio).intValue());
            String qrCodeBucketPath = getImgBucketPath(qrCode);
            String fileName = getFileName(backgroundPic);
            String postPictureUrl = buildDistributorPictureUrl(groundMaterial, dpPoiDTO, misId, point, qrCodeBucketPath, fileName, backgroundPic, BigDecimal.ZERO, showHeightRealRatio);
//            String distributorPic = MaterialPicUtil.reUpload(postPictureUrl);


            GroundDistributor groundDistributor = new GroundDistributor();
            groundDistributor.setGroundPromotionId(groundPromotionId);
            groundDistributor.setMaterialId(groundMaterial.getId());
            groundDistributor.setShopId(Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getShopId() : 0L);
            groundDistributor.setPointId(Objects.nonNull(point) ? point.getId() : 0L);
            groundDistributor.setExtInfo(JSONObject.toJSONString(distributorExtInfoDTO));

            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(groundMaterial.getGroundPromotionId());

            switch (GroundPromotionScopeTypeEnum.fromCode(groundPromotion.getScopeType())) {
                case SHOP:
                    groundDistributor.setScopeType(GroundPromotionScopeTypeEnum.SHOP.getCode());
                    groundDistributor.setScopeId(dpPoiDTO.getShopId());
                    break;
                case POINT:
                    groundDistributor.setScopeType(GroundPromotionScopeTypeEnum.POINT.getCode());
                    groundDistributor.setScopeId(point.getId());
                    break;
                case MALL:
                    groundDistributor.setScopeType(GroundPromotionScopeTypeEnum.MALL.getCode());
                    groundDistributor.setScopeId(point.getId());
                    break;
                case STANDARD_PRODUCT:
                    groundDistributor.setScopeType(GroundPromotionScopeTypeEnum.STANDARD_PRODUCT.getCode());
                    break;
                default:
                    groundDistributor.setScopeType(GroundPromotionScopeTypeEnum.UNKNOWN.getCode());
                    break;
            }

            groundDistributor.setMisId(misId);
            groundDistributor.setDistributorCode(distributorCode);
            groundDistributor.setQrCode(qrCode);
            groundDistributor.setDistributorPic(postPictureUrl);
            groundDistributor.setLandingUrl(landingUrl);
            int retryCnt = 0;
            int insertRow = 0;
            while (retryCnt < RETRY_CNT && insertRow <= 0) {
                insertRow = groundDistributorRepository.insert(groundDistributor);
                retryCnt++;
            }

            if (insertRow <= 0) {
                Cat.logError(new GroundPromotionException(String.format("生成物料失败, groundPromotionId: %s, shopId: %s, pointId: %s, misId: %s",
                        groundPromotionId, Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getShopId() : 0, Objects.nonNull(point) ? point.getId() : 0, misId)));
                log.error("[generateSingleMaterial] insert db failed, groundPromotionId is {}, shopId: {}, pointId: {}, misId: {}, groundMaterial: {}, landingUrl: {}, exception is ",
                        groundPromotionId, Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getShopId() : 0, Objects.nonNull(point) ? point.getId() : 0, misId, JSONObject.toJSONString(groundMaterial), landingUrl);
            }
        } catch (Exception e) {
            log.error("[generateSingleMaterial] error, groundPromotionId is {}, shopId: {}, pointId: {}, misId: {}, groundMaterial: {}, landingUrl: {}, exception is ",
                    groundPromotionId, Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getShopId() : 0, Objects.nonNull(point) ? point.getId() : 0, misId, JSONObject.toJSONString(groundMaterial), landingUrl, e);
        }
    }

    private String buildDistributorPictureUrl(GroundMaterial groundMaterial, DpPoiDTO dpPoiDTO, String misId, GroundPromotionPoint point, String qrCodeBucketPath,
                                              String fileName, String backgroundPic, BigDecimal showWidthRealRatio, BigDecimal showHeightRealRatio) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildDistributorPictureUrl(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial,com.sankuai.sinai.data.api.dto.DpPoiDTO,java.lang.String,com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint,java.lang.String,java.lang.String,java.lang.String,java.math.BigDecimal,java.math.BigDecimal)");
        StringBuilder sb = new StringBuilder();
        sb.append("watermark=1&&object=").append(getBase64UrlEncoder(qrCodeBucketPath))
                .append("&p=1&x=").append(groundMaterial.getQrX().multiply(showHeightRealRatio).intValue())
                .append("&y=").append(groundMaterial.getQrY().multiply(showHeightRealRatio).intValue());
        if (groundMaterial.getIsStoreName() > 0) {
            int fontSize = BigDecimal.valueOf(groundMaterial.getTypeFaceSize()).multiply(showHeightRealRatio).intValue();
            String name;

            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(groundMaterial.getGroundPromotionId());

            switch (GroundPromotionScopeTypeEnum.fromCode(groundPromotion.getScopeType())) {
                case SHOP:
                    name = dpPoiDTO.getShopName();
                    if (StringUtils.isNotEmpty(dpPoiDTO.getBranchName())) {
                        name += "（" + dpPoiDTO.getBranchName() + "）";
                    }
                    break;
                case POINT:
                case MALL:
                    name = point.getPointName();
                    break;
                case STANDARD_PRODUCT:
                    name = "标品推广";
                    break;
                default:
                    name = "";
            }

            sb.append("|watermark=2&&text=").append(getBase64UrlEncoder(name))
                    .append("&size=").append(fontSize)
                    .append("&type=U0hC")
                    .append("&color=").append(getBase64UrlEncoder(groundMaterial.getTypeFaceColor()))
                    .append("&x=").append(groundMaterial.getStoreX().multiply(showHeightRealRatio).intValue())
                    .append("&y=").append(groundMaterial.getStoreY().multiply(showHeightRealRatio).intValue());
        }
        if (groundMaterial.getIsMisId() > 0) {
            int misFontSize = BigDecimal.valueOf(groundMaterial.getMisFontSize()).multiply(showHeightRealRatio).intValue();
            sb.append("|watermark=2&&text=").append(getBase64UrlEncoder(misId))
                    .append("&size=").append(misFontSize)
                    .append("&type=U0hC")
                    .append("&color=").append(getBase64UrlEncoder(groundMaterial.getMisFontColor()))
                    .append("&x=").append(BigDecimal.valueOf(MATERIAL_MIS_X).multiply(showHeightRealRatio).intValue())
                    .append("&y=").append(BigDecimal.valueOf(MATERIAL_MIS_Y).multiply(showHeightRealRatio).intValue());
        }
        sb.append("|").append(MATERIAL_ZIP_CONFIG);
        return MaterialPicUtil.process(fileName, backgroundPic, sb.toString());
    }

    //private BigDecimal getShowWidthRealRatio(String url) {
    //    try {
    //        ImageResult imageResult = MaterialPicUtil.queryImageInfo(getImgBucketPath(url));
    //        if (imageResult == null) {
    //            return BigDecimal.ONE;
    //        }
    //        int realWidth = imageResult.getWidth();
    //        BigDecimal ratio = BigDecimal.valueOf(realWidth).divide(BigDecimal.valueOf(MATERIAL_SHOW_WIDTH), 10, BigDecimal.ROUND_HALF_UP);
    //        return ratio.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ONE : ratio;
    //    } catch (Exception e) {
    //        log.error("[getShowWidthRealRatio] error, url: {}, exception is ", url, e);
    //        return BigDecimal.ONE;
    //    }
    //}

    private BigDecimal getShowHeightRealRatio(String url) {
        try {
            ImageResult imageResult = MaterialPicUtil.queryImageInfo(getImgBucketPath(url));
            if (imageResult == null) {
                return BigDecimal.ONE;
            }
            int realHeight = imageResult.getHeight();
            BigDecimal ratio = BigDecimal.valueOf(realHeight).divide(BigDecimal.valueOf(MATERIAL_SHOW_HEIGHT), 10, BigDecimal.ROUND_HALF_UP);
            return ratio.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ONE : ratio;
        } catch (Exception e) {
            log.error("[getShowWidthRealRatio] error, url: {}, exception is ", url, e);
            return BigDecimal.ONE;
        }
    }

    private String getBase64UrlEncoder(String raw) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.getBase64UrlEncoder(java.lang.String)");
        try {
            if (StringUtils.isEmpty(raw)) {
                return StringUtils.EMPTY;
            }
            String base64 = new String(Base64.getEncoder().encode(raw.getBytes(StandardCharsets.UTF_8)));
            return URLEncoder.encode(base64, "UTF-8");
        } catch (Exception e) {
            log.error("[getBase64UrlEncoder] error, raw: {}, exception is ", raw, e);
            return StringUtils.EMPTY;
        }
    }

    private String getImgBucketPath(String img) throws Exception {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.getImgBucketPath(java.lang.String)");
        URL url = new URL(img);
        return url.getPath();
    }

    private String getFileName(String img) throws Exception{
        URL url = new URL(img);
        return Paths.get(url.getPath()).getFileName().toString();
    }

    private String buildDistributionParam(String distributorCode, long groundPromotionId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.buildDistributionParam(java.lang.String,long)");
        return String.format("dt%s$$%s", distributorCode, groundPromotionId);
    }

    private String editBizDistributorWithRetry(long groundPromotionId, String misId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.editBizDistributorWithRetry(long,java.lang.String)");
        int retryCnt = 0;
        while (retryCnt < 5) {
            String distributorCode = buildDistributionParam(ShortCodeUtil.gen(), groundPromotionId);
            BizDistributorOperateDTO bizDistributorOperateDTO = new BizDistributorOperateDTO();
            bizDistributorOperateDTO.setBizId(String.valueOf(groundPromotionId));
            bizDistributorOperateDTO.setBizType(DistributorBizTypeEnum.GROUND_PROMOTION.getCode());
            bizDistributorOperateDTO.setStatus(DistributionStatusEnum.VALID.getCode());
            bizDistributorOperateDTO.setUserId(misId);
            bizDistributorOperateDTO.setUserType(DistributorUserTypeEnum.MIS_ID.getCode());
            bizDistributorOperateDTO.setDistributorCode(distributorCode);
            RemoteResponse<Long> bizResp = bizDistributorService.editBizDistributor(bizDistributorOperateDTO);
            if (bizResp.isSuccess() && bizResp.getData() != null && bizResp.getData() > 0) {
                return distributorCode;
            }
            retryCnt++;
        }
        return StringUtils.EMPTY;
    }


    private List<Map<Long, List<String>>> processDistribution(GroundPromotion groundPromotion, Map<Long, List<String>> shopId2MisIds,
                                                              Map<Long, List<PromotionalDeal>> shopId2PromotionDeals,
                                                              Map<Long, List<String>> pointId2MisIds,
                                                              Map<Long, List<PromotionalDeal>> pointId2PromotionDeals) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.processDistribution(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion,java.util.Map,java.util.Map,java.util.Map,java.util.Map)");
        Map<Long, List<String>> updatedShopId2MisIds = Maps.newHashMap();
        for (Map.Entry<Long, List<String>> entry : shopId2MisIds.entrySet()) {
            long shopId = entry.getKey();
            List<String> misIds = entry.getValue();
            List<PromotionalDeal> shopPromotionDeals = shopId2PromotionDeals.getOrDefault(shopId, Lists.newArrayList());
            if (CollectionUtils.isEmpty(shopPromotionDeals)) {
                continue;
            }
            List<Long> ids = shopPromotionDeals.stream().map(PromotionalDeal::getId).collect(Collectors.toList());
            int updateRow = promotionalDealRepository.batchUpdateBindMisIdAndStatus(ids, StringUtils.join(misIds, BIND_MIS_ID_SEPARATOR), GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
            if (updateRow > 0) {
                updatedShopId2MisIds.put(shopId, misIds);
            }
        }
        Map<Long, List<String>> updatedPointId2MisIds = Maps.newHashMap();
        for (Map.Entry<Long, List<String>> entry : pointId2MisIds.entrySet()) {
            long pointId = entry.getKey();
            List<String> misIds = entry.getValue();
            List<PromotionalDeal> pointPromotionDeals = pointId2PromotionDeals.getOrDefault(pointId, Lists.newArrayList());
            if (CollectionUtils.isEmpty(pointPromotionDeals)) {
                continue;
            }
            List<Long> ids = pointPromotionDeals.stream().map(PromotionalDeal::getId).collect(Collectors.toList());
            int updateRow = promotionalDealRepository.batchUpdateBindMisIdAndStatus(ids, StringUtils.join(misIds, BIND_MIS_ID_SEPARATOR), GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
            if (updateRow > 0) {
                updatedPointId2MisIds.put(pointId, misIds);
            }
        }

        //更新地推活动状态至正在地推中
        if (new Date().after(groundPromotion.getGroundStartTime()) &&
                groundPromotion.getStatus() != GroundPromotionActEnum.IN_PROGRESS.getCode()) {
            groundPromotionRepository.updateStatus(groundPromotion.getId(), GroundPromotionActEnum.CITY_DISTRIBUTE.getCode(),
                    GroundPromotionActEnum.IN_PROGRESS.getCode());
        }
        return Lists.newArrayList(updatedShopId2MisIds, updatedPointId2MisIds);
    }

    private List<Map<Pair<Long, DistributorExtInfoDTO>, List<String>>> processDistributionWithExtInfo(GroundPromotion groundPromotion, Map<Pair<Long, DistributorExtInfoDTO>, List<String>> shopId2MisIds,
                                                              Map<Pair<Long, DistributorExtInfoDTO>, List<PromotionalDeal>> shopId2PromotionDeals,
                                                              Map<Pair<Long, DistributorExtInfoDTO>, List<String>> pointId2MisIds,
                                                              Map<Pair<Long, DistributorExtInfoDTO>, List<PromotionalDeal>> pointId2PromotionDeals) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.processDistributionWithExtInfo(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion,java.util.Map,java.util.Map,java.util.Map,java.util.Map)");
        Map<Pair<Long, DistributorExtInfoDTO>, List<String>> updatedShopId2MisIds = Maps.newHashMap();
        for (Map.Entry<Pair<Long, DistributorExtInfoDTO>, List<String>> entry : shopId2MisIds.entrySet()) {
            long shopId = entry.getKey().getLeft();
            DistributorExtInfoDTO distributorExtInfoDTO = entry.getKey().getRight();
            List<String> misIds = entry.getValue();
            List<PromotionalDeal> shopPromotionDeals = shopId2PromotionDeals.getOrDefault(Pair.of(shopId, distributorExtInfoDTO), Lists.newArrayList());
            if (CollectionUtils.isEmpty(shopPromotionDeals)) {
                continue;
            }
            List<Long> ids = shopPromotionDeals.stream().map(PromotionalDeal::getId).collect(Collectors.toList());
            int updateRow = promotionalDealRepository.batchUpdateBindMisIdAndStatus(ids, StringUtils.join(misIds, BIND_MIS_ID_SEPARATOR), GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
            if (updateRow > 0) {
                updatedShopId2MisIds.put(Pair.of(shopId, distributorExtInfoDTO), misIds);
            }
        }
        Map<Pair<Long, DistributorExtInfoDTO>, List<String>> updatedPointId2MisIds = Maps.newHashMap();
        for (Map.Entry<Pair<Long, DistributorExtInfoDTO>, List<String>> entry : pointId2MisIds.entrySet()) {
            long pointId = entry.getKey().getLeft();
            DistributorExtInfoDTO distributorExtInfoDTO = entry.getKey().getRight();
            List<String> misIds = entry.getValue();
            List<PromotionalDeal> pointPromotionDeals = pointId2PromotionDeals.getOrDefault(Pair.of(pointId, distributorExtInfoDTO), Lists.newArrayList());
            if (CollectionUtils.isEmpty(pointPromotionDeals)) {
                continue;
            }
            List<Long> ids = pointPromotionDeals.stream().map(PromotionalDeal::getId).collect(Collectors.toList());
            int updateRow = promotionalDealRepository.batchUpdateBindMisIdAndStatus(ids, StringUtils.join(misIds, BIND_MIS_ID_SEPARATOR), GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
            if (updateRow > 0) {
                updatedPointId2MisIds.put(Pair.of(pointId, distributorExtInfoDTO), misIds);
            }
        }

        //更新地推活动状态至正在地推中
        if (new Date().after(groundPromotion.getGroundStartTime()) &&
                groundPromotion.getStatus() != GroundPromotionActEnum.IN_PROGRESS.getCode()) {
            groundPromotionRepository.updateStatus(groundPromotion.getId(), GroundPromotionActEnum.CITY_DISTRIBUTE.getCode(),
                    GroundPromotionActEnum.IN_PROGRESS.getCode());
        }
        return Lists.newArrayList(updatedShopId2MisIds, updatedPointId2MisIds);
    }

    private Map<DistributorExtInfoDTO, List<String>> processStandardDistributionWithExtInfo(GroundPromotion groundPromotion, Map<DistributorExtInfoDTO, List<String>> extInfo2MisIds,
                                                                                                      Map<DistributorExtInfoDTO, List<PromotionalDeal>> extInfo2PromotionDeals) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.processStandardDistributionWithExtInfo(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion,java.util.Map,java.util.Map)");
        Map<DistributorExtInfoDTO, List<String>> updatedExtInfo2MisIds = Maps.newHashMap();
        for (Map.Entry<DistributorExtInfoDTO, List<String>> entry : extInfo2MisIds.entrySet()) {
            DistributorExtInfoDTO distributorExtInfoDTO = entry.getKey();
            List<String> misIds = entry.getValue();
            List<PromotionalDeal> shopPromotionDeals = extInfo2PromotionDeals.getOrDefault(distributorExtInfoDTO, Lists.newArrayList());
            if (CollectionUtils.isEmpty(shopPromotionDeals)) {
                continue;
            }
            List<Long> ids = shopPromotionDeals.stream().map(PromotionalDeal::getId).collect(Collectors.toList());
            int updateRow = promotionalDealRepository.batchUpdateBindMisIdAndStatus(ids, StringUtils.join(misIds, BIND_MIS_ID_SEPARATOR), GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE.getCode());
            if (updateRow > 0) {
                updatedExtInfo2MisIds.put(distributorExtInfoDTO, misIds);
            }
        }


        //更新地推活动状态至正在地推中
        if (new Date().after(groundPromotion.getGroundStartTime()) &&
                groundPromotion.getStatus() != GroundPromotionActEnum.IN_PROGRESS.getCode()) {
            groundPromotionRepository.updateStatus(groundPromotion.getId(), GroundPromotionActEnum.CITY_DISTRIBUTE.getCode(),
                    GroundPromotionActEnum.IN_PROGRESS.getCode());
        }
        return updatedExtInfo2MisIds;
    }

    private GroundPromotion queryGroundPromotionByPromotionId(long groundPromotionId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.queryGroundPromotionByPromotionId(long)");
        GroundPromotionShopRequest req = new GroundPromotionShopRequest();
        req.setGroundPromotionId(groundPromotionId);
        List<GroundPromotion> groundPromotions = groundPromotionRepository.query(req);
        return CollectionUtils.isEmpty(groundPromotions) ? null : groundPromotions.get(0);
    }

    private List<GroundPromotion> queryGroundPromotionByPromotionId(List<Long> groundPromotionIdList) {
        List<GroundPromotion> groundPromotions = groundPromotionRepository.batchQuery(groundPromotionIdList);
        return groundPromotions;
    }

    private List<PromotionalDeal> queryPromotionDealByPromotionId(GroundPromotion groundPromotion, List<Integer> statusList) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.queryPromotionDealByPromotionId(GroundPromotion,List)");
        GroundPromotionDealRequest req = new GroundPromotionDealRequest();
        req.setGroundPromotionId(groundPromotion.getId());
        req.setMerchantsId(groundPromotion.getMerchantsId());
        req.setStatus(statusList);
        return promotionalDealRepository.query(req);
    }

    private List<PromotionalDeal> queryPromotionDealByPromotionId(long groundPromotionId, List<Integer> statusList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.queryPromotionDealByPromotionId(long,java.util.List)");
        GroundPromotionDealRequest req = new GroundPromotionDealRequest();
        req.setGroundPromotionId(groundPromotionId);
        req.setStatus(statusList);
        return promotionalDealRepository.query(req);
    }

    public RemoteResponse<Boolean> cmQueryIsDistribute(GroundPromotionCMQueryRequest request) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.cmQueryIsDistribute(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionCMQueryRequest)");
        try {
            if (request == null || request.getGroundPromotionId() < 0) {
                log.error("[cmQuerySaleList] req param invalid, request is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("param invalid");
            }
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmQuerySaleList] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("活动不存在");
            }
            List<PromotionalDeal> promotionalDeals = queryPromotionDealByPromotionId(groundPromotion,
                    Lists.newArrayList(CITY_MANAGER_DISTRIBUTE.getCode()));
            if (CollectionUtils.isEmpty(promotionalDeals)) {
                //log.error("[cmQueryIsDistribute] promotionalDeals empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.success(false);
            }
            return RemoteResponse.success(true);
        } catch (IllegalArgumentException e) {
            log.error("GroundPromotionCMService cmQueryIsDistribute error!,request:{}", request, e);
            return RemoteResponse.success(false);
        }
    }

    public RemoteResponse<String> getActivityGroupPromotionCodeUrl(GroundPromotionCMQueryRequest request) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.getActivityGroupPromotionCodeUrl(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionCMQueryRequest)");
        try {
            //检查校验,顺便获取下活动的名称
            if (request == null || request.getGroundPromotionId() < 0) {
                log.error("[cmQuerySaleList] req param invalid, request is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("param invalid");
            }
            GroundPromotion groundPromotion = queryGroundPromotionByPromotionId(request.getGroundPromotionId());
            if (groundPromotion == null) {
                log.error("[cmQuerySaleList] groundPromotion empty, req is {}", JSONObject.toJSONString(request));
                return RemoteResponse.fail("活动不存在");
            }
            List<GroundDistributor> groundDistributors = groundDistributorRepository.queryAllByGroundPromotionId(request.getGroundPromotionId());
            if (CollectionUtils.isEmpty(groundDistributors)) {
                log.error("[cmNotifyDistribution] getActivityGroupPromotionCodeUrl empty, groundPromotionId is {}", request.getGroundPromotionId());
                return RemoteResponse.fail("非法参数：未获取到活动信息");
            }
            Map<Long, String> shopNames = getShopNames(groundDistributors);
            Map<Long, String> pointNames = getPointNames(groundDistributors);
            Map<String, List<List<String>>> sheetData = new HashMap<>();
            List<List<String>> rowData = Lists.newArrayList();
            rowData.add(Lists.newArrayList("活动ID", "点评ShopId", "门店名称", "点位名称", "misId", "地推物料Url"));
            groundDistributors.forEach(item -> {
                        List<String> cellData = Lists.newArrayList();
                        cellData.add(String.valueOf(request.getGroundPromotionId()));
                        String shopId = "";
                        String shopName = "";
                        String pointName = "";

                        switch (GroundPromotionScopeTypeEnum.fromCode(item.getScopeType())) {
                            case SHOP:
                                shopId = String.valueOf(item.getScopeId());
                                shopName = shopNames.getOrDefault(item.getScopeId(), "");
                                break;
                            case POINT:
                            case MALL:
                                pointName = pointNames.getOrDefault(item.getScopeId(), "");
                                break;
                            case STANDARD_PRODUCT:
                                pointName = "标品推广";
                                break;
                        }

                        cellData.add(shopId);
                        cellData.add(shopName);
                        cellData.add(pointName);
                        cellData.add(item.getMisId());
                        cellData.add(item.getDistributorPic());
                        rowData.add(cellData);
                    }
            );
            sheetData.put("result", rowData);

            FileBody fileBody = FileBodyBuilder.buildExcelFileBody("物料码数据", sheetData);
            if (fileBody.getUrl() == null) {
                pushMessage2Dx(new Date(), "下载地推物料失败，失败原因：云文件上传失败", Lists.newArrayList(request.getCmMisId()));
                return RemoteResponse.fail("下载地推物料失败，失败原因：云文件上传失败");
            }
            TextXMMessageDTO messageDTO = new TextXMMessageDTO();
            messageDTO.setText(String.format("您于%s时间获取地推活动物料数据，请点击%s下载", DateUtils.format(new Date()), fileBody.getUrl()));
            messageAcl.pushMessage(messageDTO, Lists.newArrayList(request.getCmMisId()));
            return RemoteResponse.success("下载成功！请稍后到大象消息中进行下载");
        } catch (IllegalArgumentException e) {
            log.error("GroundPromotionCMService getActivityGroupPromotionCodeUrl error!,request:{}", request, e);
            pushMessage2Dx(new Date(), "下载地推物料失败，失败原因：非法参数", Lists.newArrayList(request.getCmMisId()));
            return RemoteResponse.fail(e.getMessage());
        } catch (Exception e) {
            log.error("GroundPromotionCMService getActivityGroupPromotionCodeUrl error!,request:{}", request, e);
            pushMessage2Dx(new Date(), "下载地推物料失败，失败原因：系统异常", Lists.newArrayList(request.getCmMisId()));
            return RemoteResponse.fail("系统异常");
        }
    }

    @Override
    public RemoteResponse<String> batchGetActivityGroupPromotionCodeUrl(List<Long> groundPromotionIdList, String cmMisId) {
        try {
            if (StringUtils.isEmpty(cmMisId) || CollectionUtils.isEmpty(groundPromotionIdList)) {
                log.error("[batchGetActivityGroupPromotionCodeUrl] req param invalid, ids is {}, cmMisId is {}", JSONObject.toJSONString(groundPromotionIdList), cmMisId);
                return RemoteResponse.fail("参数错误");
            }

            List<GroundDistributor> groundDistributors = groundDistributorRepository.query(groundPromotionIdList);
            if (CollectionUtils.isEmpty(groundDistributors)) {
                log.error("[batchGetActivityGroupPromotionCodeUrl] distributor empty, ids is {}, cmMisId is {}", JSONObject.toJSONString(groundPromotionIdList), cmMisId);
                return RemoteResponse.fail("参数错误：未获取到活动信息");
            }

            List<GroundPromotion> groundPromotionList = queryGroundPromotionByPromotionId(groundPromotionIdList);
            Map<Long, GroundPromotion> groundPromotionMap = groundPromotionList.stream()
                    .collect(Collectors.toMap(GroundPromotion::getId, Function.identity(), (ov, nv) -> nv));
            Map<Long, String> shopNames = getShopNames(groundDistributors);
            Map<Long, String> pointNames = getPointNames(groundDistributors);
            Map<String, List<List<String>>> sheetData = new HashMap<>();
            List<List<String>> rowData = Lists.newArrayList();
            rowData.add(Lists.newArrayList("活动ID", "点评ShopId", "门店名称", "点位名称", "misId", "地推物料Url", "地推开始时间", "地推结束时间"));
            groundDistributors.forEach(item -> {
                        List<String> cellData = Lists.newArrayList();
                        cellData.add(String.valueOf(item.getGroundPromotionId()));
                        String shopId = "";
                        String shopName = "";
                        String pointName = "";

                        switch (GroundPromotionScopeTypeEnum.fromCode(item.getScopeType())) {
                            case SHOP:
                                shopId = String.valueOf(item.getScopeId());
                                shopName = shopNames.getOrDefault(item.getScopeId(), "");
                                break;
                            case POINT:
                            case MALL:
                                pointName = pointNames.getOrDefault(item.getScopeId(), "");
                                break;
                            case STANDARD_PRODUCT:
                                pointName = "标品推广";
                                break;
                        }

                        cellData.add(shopId);
                        cellData.add(shopName);
                        cellData.add(pointName);
                        cellData.add(item.getMisId());
                        cellData.add(item.getDistributorPic());

                        GroundPromotion groundPromotion = groundPromotionMap.get(item.getGroundPromotionId());
                        cellData.add(DateUtils.format(groundPromotion.getGroundStartTime()));
                        cellData.add(DateUtils.format(groundPromotion.getGroundEndTime()));
                        rowData.add(cellData);
                    }
            );
            sheetData.put("result", rowData);

            FileBody fileBody = FileBodyBuilder.buildExcelFileBody("物料码数据", sheetData);
            if (fileBody.getUrl() == null) {
                pushMessage2Dx(new Date(), "下载地推物料失败，失败原因：云文件上传失败", Lists.newArrayList(cmMisId));
                return RemoteResponse.fail("下载地推物料失败，失败原因：云文件上传失败");
            }
            TextXMMessageDTO messageDTO = new TextXMMessageDTO();
            messageDTO.setText(String.format("您于%s时间获取地推活动物料数据，请点击%s下载", DateUtils.format(new Date()), fileBody.getUrl()));
            messageAcl.pushMessage(messageDTO, Lists.newArrayList(cmMisId));
            return RemoteResponse.success("下载成功！请稍后到大象消息中进行下载");

        } catch (Exception e) {
            log.error("[batchGetActivityGroupPromotionCodeUrl] error, ids is {}, cmMisId is {}, exception is ", JSONObject.toJSONString(groundPromotionIdList), cmMisId, e);
            return RemoteResponse.fail("系统异常");
        }
    }


    private void pushMessage2Dx(Date date, String msg, List<String> pushId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionCMServiceImpl.pushMessage2Dx(java.util.Date,java.lang.String,java.util.List)");
        TextXMMessageDTO messageDTO = new TextXMMessageDTO();
        messageDTO.setText(String.format("您于%s时间获取活动物料码数据%s", DateUtils.format(date), msg));
        messageAcl.pushMessage(messageDTO, pushId);
    }

    @NotNull
    private Map<Long, String> getShopNames(List<GroundDistributor> groundDistributors) {
        List<Long> shopIdList = groundDistributors.stream()
                .filter(groundDistributor -> groundDistributor.getScopeType() == GroundPromotionScopeTypeEnum.SHOP.getCode())
                .map(GroundDistributor::getScopeId)
                .distinct()
                .collect(Collectors.toList());
        List<DpPoiDTO> dpPoiDTOList = shopAcl.getDpPoiDTOList(shopIdList, Lists.newArrayList("shopId", "shopName", "branchName"));
        Map<Long, String> shopId2Name = new HashMap<>();
        for (DpPoiDTO dpPoiDTO : dpPoiDTOList) {
            long shopId = dpPoiDTO.getShopId();
            String shopName = dpPoiDTO.getShopName();
            if (StringUtils.isNotEmpty(dpPoiDTO.getBranchName())) {
                shopName += "（" + dpPoiDTO.getBranchName() + "）";
            }
            shopId2Name.put(shopId, shopName);
        }
        return shopId2Name;
    }

    private Map<Long, String> getPointNames(List<GroundDistributor> groundDistributors) {
        List<Long> pointIdList = groundDistributors.stream()
                .filter(groundDistributor -> groundDistributor.getScopeType() == GroundPromotionScopeTypeEnum.POINT.getCode() || groundDistributor.getScopeType() == GroundPromotionScopeTypeEnum.MALL.getCode())
                .map(GroundDistributor::getScopeId)
                .distinct()
                .collect(Collectors.toList());
        List<GroundPromotionPoint> pointList = groundPromotionQueryService.queryPoint(pointIdList);
        return pointList.stream()
                .collect(Collectors.toMap(GroundPromotionPoint::getId, GroundPromotionPoint::getPointName, (v1, v2) -> v2));
    }

    private void insertSaleOrg(int cityId, List<String> misIdList, int buId) {
        List<Org> orgList = misIdList.stream()
                .map(misId -> {
                    Org org = Org.builder()
                            .cityId(cityId)
                            .misId(misId)
                            .userType(UserTypeEnum.SALE.getCode())
                            .status(OrgStatusEnum.VALID.getCode())
                            .buId(buId).build();
                    return org;
                }).collect(Collectors.toList());

        int retryCnt = 0;
        int insertRow = 0;
        while (retryCnt < RETRY_CNT && insertRow < orgList.size()) {
            insertRow = orgRepository.insert(orgList);
            retryCnt++;
        }
        if (insertRow < orgList.size()) {
            Cat.logError(new GroundPromotionException(String.format("销售插入org表失败, cityId: %s, misIdList: %s", cityId, misIdList)));
        }
    }

}
