package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.beautycontent.beautylaunchapi.findShop.service.LaunchShopService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: wuweizhen
 * @Date: 2023/6/1 17:26
 * @Description:
 */
@Service
@Slf4j
public class ShopAcl {

    @MdpPigeonClient(url = "com.sankuai.beautycontent.beautylaunchapi.findShop.service.LaunchShopService", timeout = 5000)
    private LaunchShopService launchShopService;

    @Autowired
    private DpPoiService dpPoiService;

    public static final List<String> DP_POI_QUERY_DEFAULT_FIELDS = Lists.newArrayList(
            "shopId",
            "shopName",
            "branchName",
            "lng",
            "lat",
            "cityId",
            "uuid",
            "backMainCategoryPath",
            "address",
            "defaultPic"
    );

    /**
     * 根据商户id列表返回商户详细信息列表（新接口，支持long类型shopId）
     * @param shopIdList
     * @param fields 返回字段，详见https://km.sankuai.com/page/927514836
     * @return 商户详细信息列表
     */
    public List<DpPoiDTO> getDpPoiDTOList(List<Long> shopIdList, List<String> fields) {
        List<DpPoiDTO> dpPoiDTOList = null;
        dpPoiDTOList = Lists.partition(shopIdList, 50).stream()
                .map(partList -> {
                    try {
                        DpPoiRequest request = new DpPoiRequest();
                        request.setShopIds(partList);
                        request.setFields(fields);
                        return dpPoiService.findShopsByShopIds(request);
                    } catch (Exception e) {
                        log.error("dpPoiService.findShopsByShopIds error", e);
                        return null;
                    }
                }).filter(Objects::nonNull)
                .reduce((list1, list2) -> {
                    list1.addAll(list2);
                    return list1;
                }).orElse(null);
        return null == dpPoiDTOList ? Collections.emptyList() : dpPoiDTOList;
    }

    public List<DpPoiDTO> getDpPoiDTOList(List<Long> shopIdList) {
        return getDpPoiDTOList(shopIdList, DP_POI_QUERY_DEFAULT_FIELDS);
    }

}
