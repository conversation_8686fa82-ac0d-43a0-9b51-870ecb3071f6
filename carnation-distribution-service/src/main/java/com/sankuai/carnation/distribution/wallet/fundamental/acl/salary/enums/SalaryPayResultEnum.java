package com.sankuai.carnation.distribution.wallet.fundamental.acl.salary.enums;

import lombok.Getter;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/9/20
 **/
@Getter
public enum SalaryPayResultEnum {

    UNKNOWN(0, "未知"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    EXCEPTION(3, "异常");

    private final int code;

    private final String desc;

    SalaryPayResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SalaryPayResultEnum fromCode(int code) {
        for (SalaryPayResultEnum enumValue : SalaryPayResultEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }
}
