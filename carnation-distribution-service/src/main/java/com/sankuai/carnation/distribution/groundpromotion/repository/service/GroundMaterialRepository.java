package com.sankuai.carnation.distribution.groundpromotion.repository.service;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundMaterialMapper;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterialExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/8 19:12
 */
@Service
@Slf4j
public class GroundMaterialRepository {

    @Autowired
    private GroundMaterialMapper groundMaterialMapper;

    public List<GroundMaterial> queryByPromotionId(long groundPromotionId) {
        if (groundPromotionId <= 0) {
            return Lists.newArrayList();
        }
        GroundMaterialExample groundMaterialExample = new GroundMaterialExample();
        GroundMaterialExample.Criteria criteria = groundMaterialExample.createCriteria();

        criteria.andGroundPromotionIdEqualTo(groundPromotionId);

        return groundMaterialMapper.selectByExample(groundMaterialExample);
    }

    public List<GroundMaterial> queryByIds(List<Long> materialIds){
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundMaterialRepository.queryByIds(java.util.List)");
        if (CollectionUtils.isEmpty(materialIds)) {
            return Lists.newArrayList();
        }
        GroundMaterialExample groundMaterialExample = new GroundMaterialExample();
        GroundMaterialExample.Criteria criteria = groundMaterialExample.createCriteria();

        criteria.andIdIn(materialIds);

        return groundMaterialMapper.selectByExample(groundMaterialExample);
    }

}
