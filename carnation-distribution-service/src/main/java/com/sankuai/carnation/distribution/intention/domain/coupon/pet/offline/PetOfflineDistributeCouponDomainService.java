package com.sankuai.carnation.distribution.intention.domain.coupon.pet.offline;

import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.UserCouponDTO;
import com.sankuai.carnation.distribution.intention.acl.bo.RainbowLotteryCouponBO;
import com.sankuai.carnation.distribution.intention.acl.bo.request.RainbowEnterLotteryRequest;
import com.sankuai.carnation.distribution.intention.acl.service.RainbowLotteryAclService;
import com.sankuai.carnation.distribution.intention.domain.coupon.pet.offline.bo.PetOfflineCouponConfigBO;
import com.sankuai.carnation.distribution.intention.domain.coupon.record.UserIntentionCouponDomainService;
import com.sankuai.carnation.distribution.intention.domain.coupon.record.bo.UserIntentionCouponBO;
import com.sankuai.carnation.distribution.intention.domain.user.code.UserCodeIntentionDomainService;
import com.sankuai.carnation.distribution.intention.domain.user.code.bo.UserCodeIntentionBO;
import com.sankuai.carnation.distribution.intention.dto.pet.offline.distribute.PetOfflineDistributePopUpCouponDTO;
import com.sankuai.carnation.distribution.intention.dto.pet.offline.distribute.request.PetOfflineDistributeCouponIssueRequest;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.exceptions.PetOfflineDistributeException;
import com.sankuai.carnation.distribution.utils.HaimaRequestClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/7/4
 **/
@Component
@Slf4j
public class PetOfflineDistributeCouponDomainService {

    private static final String BIZ_CODE = DistributionBusinessChannelEnum.PET_DISTRIBUTE.getCode();

    private final ThreadPoolExecutor intentionRecordPool = new ThreadPoolExecutor(10, 20, 1, TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(100), new NamedThreadFactory("PetOfflineDistributeCouponDomainService.IntentionRecordPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private HaimaRequestClient haimaRequestClient;

    @Autowired
    private RainbowLotteryAclService lotteryAclService;

    @Autowired
    private UserIntentionCouponDomainService intentionCouponDomainService;

    @Autowired
    private UserCodeIntentionDomainService codeIntentionDomainService;

    public PetOfflineDistributePopUpCouponDTO issueCoupon(PetOfflineDistributeCouponIssueRequest request) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.intention.domain.coupon.pet.offline.PetOfflineDistributeCouponDomainService.issueCoupon(PetOfflineDistributeCouponIssueRequest)");
        if (request == null) {
            throw new PetOfflineDistributeException("请求不能为空");
        }
        if (PlatformEnum.UNKNOWN.equals(PlatformEnum.fromCode(request.getPlatform())) || request.getUserId() <= 0L) {
            throw new PetOfflineDistributeException("请求用户未登录");
        }
        if (StringUtils.isBlank(request.getDistributorCode())) {
            throw new PetOfflineDistributeException("缺少推广人信息");
        }
        recordScanIntention(request);

        if (request.getCityId() <= 0) {
            throw new PetOfflineDistributeException("缺少城市信息");
        }
        PetOfflineCouponConfigBO couponConfig = getCouponConfig();
        if (couponConfig == null) {
            throw new PetOfflineDistributeException("缺少发券信息配置");
        }
        RainbowEnterLotteryRequest lotteryRequest = buildEntryLotteryRequest(request, couponConfig);
        if (lotteryRequest == null) {
            throw new PetOfflineDistributeException("发券活动未配置");
        }
        RemoteResponse<List<RainbowLotteryCouponBO>> response = lotteryAclService.enterCouponLottery(lotteryRequest);
        if (!response.isSuccess()) {
            throw new PetOfflineDistributeException(response.getMsg());
        }
        recordCouponIntention(response.getData(), request.getDistributorCode());
        PetOfflineDistributePopUpCouponDTO popUpCouponDTO = new PetOfflineDistributePopUpCouponDTO();
        List<UserCouponDTO> couponDTOList = response.getData().stream()
                .filter(Objects::nonNull)
                .map(bo -> {
                    UserCouponDTO couponDTO = new UserCouponDTO();
                    couponDTO.setCouponId(bo.getCouponId());
                    couponDTO.setAmount(bo.getAmount());
                    couponDTO.setLimitAmount(bo.getLimitAmount());
                    couponDTO.setCouponTitle(bo.getCouponTitle());
                    couponDTO.setStartTime(bo.getStartTime());
                    couponDTO.setEndTime(bo.getEndTime());
                    return couponDTO;
                })
                .collect(Collectors.toList());
        popUpCouponDTO.setBannerUrl(couponConfig.getBanner());
        popUpCouponDTO.setUserCouponDTOList(couponDTOList);
        return popUpCouponDTO;
    }

    private PetOfflineCouponConfigBO getCouponConfig() {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.intention.domain.coupon.pet.offline.PetOfflineDistributeCouponDomainService.getCouponConfig()");
        return haimaRequestClient.getConfig("pet_offline_distribute_coupon", Maps.newHashMap(), PetOfflineCouponConfigBO.class);
    }

    private RainbowEnterLotteryRequest buildEntryLotteryRequest(PetOfflineDistributeCouponIssueRequest request, PetOfflineCouponConfigBO couponConfig) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "c.sankuai.carnation.distribution.intention.domain.coupon.pet.offline.PetOfflineDistributeCouponDomainService.buildEntryLotteryRequest(PetOfflineDistributeCouponIssueRequest,PetOfflineCouponConfigBO)");
        RainbowEnterLotteryRequest lotteryRequest = new RainbowEnterLotteryRequest();
        lotteryRequest.setEcode(request.getPlatform() == PlatformEnum.DP.getCode() ? couponConfig.getDpLotteryEvent() : couponConfig.getMtLotteryEvent());
        if (StringUtils.isBlank(lotteryRequest.getEcode())) {
            return null;
        }
        lotteryRequest.setPlatform(request.getPlatform());
        lotteryRequest.setUserId(request.getUserId());
        lotteryRequest.setCityId(request.getCityId());
        return lotteryRequest;
    }

    private void recordScanIntention(PetOfflineDistributeCouponIssueRequest request) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.intention.domain.coupon.pet.offline.PetOfflineDistributeCouponDomainService.recordScanIntention(PetOfflineDistributeCouponIssueRequest)");
        UserCodeIntentionBO intention = new UserCodeIntentionBO();
        intention.setBizCode(BIZ_CODE);
        intention.setBizId(String.format("%s_%s_%s", Calendar.getInstance().getTimeInMillis(), request.getPlatform(), request.getUserId()));
        intention.setIntentionCode(request.getDistributorCode());
        intention.setUserType(request.getPlatform());
        intention.setUserId(request.getUserId());
        codeIntentionDomainService.addUserIntention(intention);
    }

    private void recordCouponIntention(List<RainbowLotteryCouponBO> couponBOList, String distributorCode) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.intention.domain.coupon.pet.offline.PetOfflineDistributeCouponDomainService.recordCouponIntention(java.util.List,java.lang.String)");
        if (CollectionUtils.isEmpty(couponBOList)) {
            return;
        }
        try {
            couponBOList.stream()
                    .filter(Objects::nonNull)
                    .filter(coupon -> StringUtils.isNotBlank(coupon.getCouponReceiptId()) && StringUtils.isNotBlank(coupon.getCouponId()))
                    .map(coupon -> {
                        UserIntentionCouponBO intention = new UserIntentionCouponBO();
                        intention.setBizCode(BIZ_CODE);
                        intention.setBizId(String.format("%s_%s_%s", coupon.getCouponReceiptId(), coupon.getPlatform(), coupon.getUserId()));
                        intention.setUserType(coupon.getPlatform());
                        intention.setUserId(coupon.getUserId());
                        intention.setDistributorId(distributorCode);
                        intention.setCouponId(coupon.getCouponId());
                        intention.setCouponReceiptId(coupon.getCouponReceiptId());
                        return intention;
                    })
                    .forEach(intention -> CompletableFuture.runAsync(() -> {
                        try {
                            intentionCouponDomainService.addUserIntentionCouponRecord(intention);
                        } catch (Exception exp) {
                            log.error("[{}.recordCouponIntention.single] intention: {}", getClass().getSimpleName(), intention, exp);
                        }
                    }, intentionRecordPool));
        } catch (Exception e) {
            throw new PetOfflineDistributeException(e);
        }
    }
}
