package com.sankuai.carnation.distribution.distributor.service.impl.pet.distribution;

import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.common.repository.model.CommonQrCode;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorChannelRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.enums.DistributorBindUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.service.impl.pet.util.PetDistributorRootService;
import com.sankuai.carnation.distribution.distributor.service.pet.distribution.PetDistributionQRCodeService;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class PetDistributionQRCodeServiceImpl implements PetDistributionQRCodeService {

    @Autowired
    private UniversalQRCodeGeneratorService generatorService;

    @Autowired
    private DistributorRootService distributorRootService;

    @Autowired
    private PetDistributorRootService petDistributorRootService;

    @Autowired
    private DistributorChannelRootService channelRootService;

    @Autowired
    private DistributorGroupRootService groupRootService;

    @Override
    public RemoteResponse<String> queryOrGenerateQRCode(Long dpShopId) {
        try {
            if (dpShopId == null || dpShopId <= 0) {
                return RemoteResponse.fail("点评门店id不正确！");
            }
            List<CommonQrCode> commonQrCodes = petDistributorRootService.queryPetDistributionCode(dpShopId);
            String codeImageUrl = Optional.ofNullable(commonQrCodes).orElse(Lists.newArrayList()).stream().findFirst()
                    .map(CommonQrCode::getCodeImage).orElse(null);
            if (StringUtils.isNotBlank(codeImageUrl)) {
                log.info("queryOrGenerateQRCode, dpShopId = {}, commonQrCodes = {}.", dpShopId, commonQrCodes);
                return RemoteResponse.success(codeImageUrl);
            }

            // 需要生成码
            String distributorCode = getDistributorCode(dpShopId);
            if (StringUtils.isBlank(distributorCode)) {
                return RemoteResponse.fail("无法获取分销码，请重试");
            }
            QRCodeConfigDTO qrCodeConfigDTO = buildQrCodeConfigDTO(dpShopId, distributorCode);
            RemoteResponse<QRCodeConfigDTO> response = generatorService.insertQRCode(qrCodeConfigDTO);
            if (!response.isSuccess()) {
                return RemoteResponse.fail(response.getMsg());
            }
            String imageUrl = Optional.ofNullable(response.getData()).map(QRCodeConfigDTO::getImageUrl).orElse(null);
            if (StringUtils.isBlank(imageUrl)) {
                log.error("queryOrGenerateQRCode, 创建宠物分销码失败! dpShopId = {}, response = {}.", dpShopId, response);
                return RemoteResponse.fail("创建宠物分销码失败!");
            }
            return RemoteResponse.success(imageUrl);
        } catch (Exception e) {
            log.error("queryOrGenerateQRCode, happen a exception! dpShopId = {}.", dpShopId, e);
            return RemoteResponse.fail("系统异常！");
        }
    }

    private String getDistributorCode(Long dpShopId) {
        DistributorBO distributorBO = distributorRootService.getDistributor(DistributorBindUserTypeEnum.DP_SHOP.getCode(), dpShopId);
        String distributorCode = Optional.ofNullable(distributorBO)
                .filter(distributor -> distributor.getStatus() != null && distributor.getStatus().equals(DistributionStatusEnum.VALID.getCode()))
                .map(DistributorBO::getDistributorCode)
                .orElse(null);
        if (StringUtils.isNotBlank(distributorCode)) {
            return distributorCode;
        }
        List<DistributionChannelBO> distributionChannelBOS = channelRootService.getChannelByChannelCode(DistributionBusinessChannelEnum.PET_DISTRIBUTE.getCode());
        if (CollectionUtils.isEmpty(distributionChannelBOS)) {
            log.error("queryOrGenerateQRCode, 宠物分销渠道为空! dpShopId = {}, distributionChannelBOS = {}.", dpShopId, distributionChannelBOS);
            return null;
        }
        List<DistributorGroupBO> distributorGroupBOS = groupRootService.getGroupByChannel(distributionChannelBOS.get(0).getChannelId(), 0, 10);
        if (CollectionUtils.isEmpty(distributorGroupBOS)) {
            log.error("queryOrGenerateQRCode, 宠物分销商为空! dpShopId = {}, distributorGroupBOS = {}.", dpShopId, distributorGroupBOS);
            return null;
        }
        int maxGroupId = distributorGroupBOS.stream()
                .filter(Objects::nonNull)
                .mapToInt(DistributorGroupBO::getGroupId)
                .max()
                .orElse(0);
        DistributorBO bo = new DistributorBO();
        bo.setUserType(DistributorBindUserTypeEnum.DP_SHOP.getCode());
        bo.setUserId(dpShopId);
        bo.setDistributorGroupId(maxGroupId);
        bo.setStatus(DistributionStatusEnum.VALID.getCode());
        long distributorId = distributorRootService.setDistributor(bo);
        return Optional.ofNullable(distributorRootService.getDistributor(distributorId))
                .map(DistributorBO::getDistributorCode)
                .orElse(null);
    }

    private QRCodeConfigDTO buildQrCodeConfigDTO(Long dpShopId, String distributorCode) {
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setBizType(BizTypeEnum.PET_SALE_CODE.getType());
        qrCodeConfigDTO.setBizId(dpShopId);
        String codeUrl = "imeituan://www.meituan.com/gc/mrn?mrn_biz=gcbu&mrn_entry=searchpage&"
                + "mrn_component=mrn-gc-searchresult&categoryid=20693&keyword=%E5%AE%A0%E7%89%A9%E5%8C%BB%E9%99%A2&"
                + "source=petfenxiao&eventpromochannel=" + distributorCode + "&mrn_min_version=0.9.0";
        qrCodeConfigDTO.setCodeUrl(codeUrl);
        return qrCodeConfigDTO;
    }
}
