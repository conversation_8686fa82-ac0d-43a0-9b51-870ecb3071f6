package com.sankuai.carnation.distribution.groundpromotion.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.base.DealGroupBaseService;
import com.dianping.deal.base.dto.DealGroupBaseDTO;
import com.dianping.deal.shop.DealShopQueryService;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponses;
import com.meituan.beauty.sakura.account.dto.CityDTO;
import com.meituan.beauty.sakura.account.dto.ProvinceDTO;
import com.sankuai.carnation.distribution.groundpromotion.constants.GroundPromotionConstants;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.PageShowTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.dto.*;
import com.sankuai.carnation.distribution.groundpromotion.enums.ExcelParseStatusEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionActEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.*;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq.GroundEventTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq.GroundPromotionProducer;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.*;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.*;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.*;
import com.sankuai.carnation.distribution.groundpromotion.transfer.GroundPromotionBatchTransfer;
import com.sankuai.carnation.distribution.groundpromotion.transfer.GroundPromotionPointTransfer;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.promocode.privilege.dto.PageResponseDTO;
import com.sankuai.carnation.distribution.xm.dto.link.LinkMessageDTO;
import com.sankuai.carnation.distribution.xm.dto.link.LinkXMMessageDTO;
import com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum;
import com.sankuai.security.sdk.SecSdk;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.technician.trade.order.service.OrderReceiptUpdateService;
import com.sankuai.technician.trade.types.enums.OrderCheatStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.carnation.distribution.groundpromotion.constants.GroundPromotionConstants.WHITE_DOMAIN_LIST;
import static com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionDealStatusEnum.CITY_MANAGER_DISTRIBUTE;

/**
 * <AUTHOR>
 * @date 2023/5/31
 * @Description
 */
@Service
@Slf4j
@Transactional
public class GroundPromotionActServiceImpl implements GroundPromotionActService {
    private final ThreadPoolExecutor batchShopQueryPool = new ThreadPoolExecutor(5, 20, 1, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(150), new NamedThreadFactory(getClass().getSimpleName() + ".BatchShopQueryPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    private final ThreadPoolExecutor batchDealQueryPool = new ThreadPoolExecutor(2, 20, 1, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100), new NamedThreadFactory(getClass().getSimpleName() + ".BatchDealQueryPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    private final ThreadPoolExecutor batchDealShopQueryPool = new ThreadPoolExecutor(2, 20, 1, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(100), new NamedThreadFactory(getClass().getSimpleName() + ".BatchDealShopQueryPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    private final ThreadPoolExecutor batchMisQueryPool = new ThreadPoolExecutor(2, 10, 1, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(50), new NamedThreadFactory(getClass().getSimpleName() + ".BatchMisQueryPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    private final int MAX_POINT_DEAL_NUM = 20;

    @Autowired
    private GroundPromotionRepository groundPromotionRepository;

    @Autowired
    private GroundPromotionAdvancedInfoRepository groundPromotionAdvancedInfoRepository;

    @Autowired
    private GroundPromotionProducer groundPromotionProducer;

    @Autowired
    private SpuAcl spuAcl;

    @Autowired
    private CityService cityService;

    @Autowired
    private CityInfoService cityInfoService;

    @Autowired
    private MerchantsRepository merchantsRepository;

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private MessageAcl messageAcl;

    @Autowired
    private DpPoiService dpPoiService;

    @Autowired
    private DealGroupBaseService dealGroupBaseService;

    @Autowired
    private DealShopQueryService dealShopQueryService;

    @Autowired
    private GroundPromotionBatchExcelUtils groundPromotionBatchExcelUtils;

    @Autowired
    private GroundPromotionPointExcelUtils groundPromotionPointExcelUtils;

    @Autowired
    private CreateResultExcelUtils createResultExcelUtils;

    @Autowired
    private RedisStoreClient redisStoreClient;

    @Autowired
    private PromotionalDealRepository promotionalDealRepository;

    @Autowired
    private GroundPartTimeQrLogRepository groundPartTimeQrLogRepository;

    @Autowired
    private RedisAcl redisAcl;

    @Autowired
    private OrderReceiptUpdateService orderReceiptUpdateService;

    @Override
    public RemoteResponse<Boolean> insertGroundP(GroundPromotionDTO groundPromotionDTO) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.insertGroundP(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionDTO)");
        //新增
        try {
            if (Objects.isNull(groundPromotionDTO)) {
                log.error("[insertGroundP]groundPromotionDTO is empty ");
                return RemoteResponse.fail("param invalid , groundPromotionDTOList is empty");
            }
            Long merchantsId = groundPromotionDTO.getMerchantsId();
            List<String> projectName = spuAcl.querySpu(merchantsId);
            GroundPromotion promotion = new GroundPromotion();
            promotion.setMerchantsId(groundPromotionDTO.getMerchantsId());
            promotion.setName(groundPromotionDTO.getName());
            promotion.setCityId(groundPromotionDTO.getCityId());
            promotion.setGroundStartTime(groundPromotionDTO.getGroundStartTime());
            promotion.setGroundEndTime(groundPromotionDTO.getGroundEndTime());
            promotion.setAllotStartTime(groundPromotionDTO.getAllotStartTime());
            promotion.setAllotEndTime(groundPromotionDTO.getAllotEndTime());
            promotion.setProjectName(CollectionUtils.isNotEmpty(projectName) ? String.join(",", projectName) : "");
            if (DateUtils.differentMsByMillisecond(new Date(), groundPromotionDTO.getAttractStartTime()) > 0) {
                promotion.setStatus(GroundPromotionActEnum.NOT_STARTED.getCode());
            } else {
                promotion.setStatus(GroundPromotionActEnum.INVESTMENT_PROMOTION.getCode());
            }
            groundPromotionRepository.insert(promotion);
            long groundPromotionId = promotion.getId();
            Merchants merchants = new Merchants();
            merchants.setMerchantsId(groundPromotionDTO.getMerchantsId());
            merchants.setGroundPromotionId(groundPromotionId);
            merchants.setMerchantsStartTime(groundPromotionDTO.getAttractStartTime());
            merchants.setMerchantsEndTime(groundPromotionDTO.getAttractEndTime());
            merchants.setProjectName(CollectionUtils.isNotEmpty(projectName) ? projectName.get(0) : "");
            merchants.setMisId(groundPromotionDTO.getMisId());
            List<GroundMaterial> groundMaterialList = new ArrayList<>();
            List<GroundMaterialDTO> groundMaterialDTOList = groundPromotionDTO.getMaterials();
            if (CollectionUtils.isNotEmpty(groundMaterialDTOList)) {
                for (GroundMaterialDTO materialDTO : groundMaterialDTOList) {
                    GroundMaterial groundMaterial = new GroundMaterial();
                    groundMaterial.setMaterialName(materialDTO.getMaterialName());
                    groundMaterial.setQrY(materialDTO.getQrY());
                    groundMaterial.setQrX(materialDTO.getQrX());
                    groundMaterial.setStoreName(Optional.ofNullable(materialDTO.getStoreName()).orElse(""));
                    groundMaterial.setIsStoreName(Optional.ofNullable(materialDTO.getIsStoreName()).orElse((byte) 0));
                    groundMaterial.setTypeFaceSize(materialDTO.getTypeFaceSize());
                    groundMaterial.setTypeFaceColor(materialDTO.getTypeFaceColor());
                    groundMaterial.setPicturePath(materialDTO.getPicturePath());
                    groundMaterial.setQrWidth(materialDTO.getQrWidth());
                    groundMaterial.setQrHeight(materialDTO.getQrHeight());
                    groundMaterial.setGroundPromotionId(groundPromotionId);
                    groundMaterial.setStoreX(materialDTO.getStoreX());
                    groundMaterial.setStoreY(materialDTO.getStoreY());
                    groundMaterial.setIsMisId(materialDTO.getIsMisId());
                    groundMaterial.setMisFontSize(materialDTO.getMisFontSize());
                    groundMaterial.setMisFontColor(materialDTO.getMisFontColor());

                    // 其他属性的设置
                    groundMaterialList.add(groundMaterial);
                }
            }
            merchantsRepository.insert(merchants);
            materialRepository.insertMaterial(groundMaterialList);

            // insert couponSetting
            GroundAdvancedCoupon groundAdvancedCoupon = groundPromotionDTO.getCoupon();
            if (Objects.nonNull(groundAdvancedCoupon)) {
                GroundPromotionAdvancedInfo groundPromotionAdvancedInfo =
                        buildAdvancedInfo(groundAdvancedCoupon, groundPromotionId);
                groundPromotionAdvancedInfoRepository.insert(groundPromotionAdvancedInfo);
            }

            //发送消息
            sendMessage(Lists.newArrayList(promotion), Lists.newArrayList(merchants));
            return RemoteResponse.success(true);
        } catch (Exception e) {
            log.error("[insertGroundP]groundPromotionDTOList is {},exception is ",
                    JSONObject.toJSONString(groundPromotionDTO), e);
            LinkXMMessageDTO linkXMMessageDTO = new LinkXMMessageDTO();
            linkXMMessageDTO.setLinkMessageDTO(new LinkMessageDTO("事件：创建地推活动失败；快去找相关同学处理", "", "https://img.meituan.net/beautyimg/29029437ab8a74cd6d44c6bd7da4b90d8951.jpg", JSONObject.toJSONString(groundPromotionDTO)));
            messageAcl.pushMessage(linkXMMessageDTO, Lists.newArrayList("zhangxing34", "liruiqi05", "yuanwei.xiong"));
            return RemoteResponse.fail(e.getMessage());
        }

    }


    private void sendMessage(List<GroundPromotion> groundPromotionList, List<Merchants> merchants) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.sendMessage(java.util.List,java.util.List)");
        Map<Long, Merchants> map = merchants.stream().collect(Collectors.toMap(Merchants::getMerchantsId, o -> o, (k1, k2) -> k1));
        //首先判断，招商活动是否已经开始，如果未开始，状态置为未开始，如果已开始需发消息
        //然后有两个事件，1 前24小时给城市主管发消息
        //2 地推活动结束，将状态流转到已结束
        //3 招商活动开始
        //4 地推活动开始，更改状态
        //5 招商报名结束，适当延迟一段时间，避免招商侧数据有延迟
        groundPromotionList.stream().forEach(groundPromotion -> {
            Date groundStartTime = groundPromotion.getGroundStartTime();
            Date groundEndTime = groundPromotion.getGroundEndTime();
            Merchants merchant = map.get(groundPromotion.getMerchantsId());
            Date attractStartTime = merchant.getMerchantsStartTime();
            Date attractEndTime = merchant.getMerchantsEndTime();

            // 招商活动开始时，修改地推活动状态为“招商中”
            if (DateUtils.differentMsByMillisecond(new Date(), attractStartTime) > 0) {
                groundPromotionProducer.asyncSendDelayMsg(
                        GroundEventTypeEnum.PROMOTION_TURN_TO_INVESTMENT,
                        groundPromotion.getId(),
                        null,
                        DateUtils.differentMsByMillisecond(new Date(), attractStartTime));
            }
            //说明距离地推活动开始时间已不足一天，那就5分钟后发消息
            if (DateUtils.differentMsByMillisecond(new Date(), DateUtils.addDay(groundStartTime, -1)) < 0) {
                groundPromotionProducer.asyncSendDelayMsg(
                        GroundEventTypeEnum.NOTIFY_CM,
                        groundPromotion.getId(),
                        null,
                        5 * 60 * 1000L);
            } else {
                groundPromotionProducer.asyncSendDelayMsg(
                        GroundEventTypeEnum.NOTIFY_CM,
                        groundPromotion.getId(),
                        null,
                        DateUtils.differentMsByMillisecond(new Date(), DateUtils.addDay(groundStartTime, -1)));
            }

            // 地推活动结束时，修改地推活动状态为“已结束”
            groundPromotionProducer.asyncSendDelayMsg(
                    GroundEventTypeEnum.PROMOTION_TURN_TO_END,
                    groundPromotion.getId(),
                    null,
                    DateUtils.differentMsByMillisecond(new Date(), groundEndTime));
            // 地推活动开始时，修改地推活动状态为“地推中”
            groundPromotionProducer.asyncSendDelayMsg(
                    GroundEventTypeEnum.PROMOTION_TURN_TO_IN_PROGRESS,
                    groundPromotion.getId(),
                    null,
                    DateUtils.differentMsByMillisecond(new Date(), groundStartTime));
            // 招商活动结束时，根据招商id拉取数据
            groundPromotionProducer.asyncSendDelayMsg(
                    GroundEventTypeEnum.INSERT_ATTRACT_LIST,
                    groundPromotion.getId(),
                    null,
                    DateUtils.differentMsByMillisecond(new Date(), DateUtils.addMinute(attractEndTime, 30)));
        });

    }

    @Override
    public RemoteResponse<Boolean> updateGroundPromotion(GroundPromotionDTO groundPromotionDTO) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.updateGroundPromotion(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionDTO)");
        try {
            if (Objects.isNull(groundPromotionDTO) || groundPromotionDTO.getId() == 0L) {
                log.error("[updateGroundPromotion] groundPromotionDTO is {}",
                        JSONObject.toJSONString(groundPromotionDTO));
                return RemoteResponse.fail("param invalid");
            }
            //首先删除所有
            materialRepository.updateStatusByGroundPromotionId(groundPromotionDTO.getId(), 1);
            //然后添加所有
            List<GroundMaterialDTO> materialDTOS = groundPromotionDTO.getMaterials();
            List<GroundMaterial> materialList = new ArrayList<>();
            for (GroundMaterialDTO materialDTO : materialDTOS) {
                GroundMaterial material = new GroundMaterial();
                material.setMaterialName(materialDTO.getMaterialName());
                material.setPicturePath(materialDTO.getPicturePath());
                material.setQrX(materialDTO.getQrX());
                material.setQrY(materialDTO.getQrY());
                material.setQrWidth(materialDTO.getQrWidth());
                material.setQrHeight(materialDTO.getQrHeight());
                material.setStoreX(materialDTO.getStoreX());
                material.setStoreName(Optional.ofNullable(materialDTO.getStoreName()).orElse(""));
                material.setStoreY(materialDTO.getStoreY());
                material.setTypeFaceSize(materialDTO.getTypeFaceSize());
                material.setTypeFaceColor(materialDTO.getTypeFaceColor());
                material.setIsStoreName(Optional.ofNullable(materialDTO.getIsStoreName()).orElse((byte) 0));
                material.setGroundPromotionId(groundPromotionDTO.getId());
                material.setIsMisId(materialDTO.getIsMisId());
                material.setMisFontSize(materialDTO.getMisFontSize());
                material.setMisFontColor(materialDTO.getMisFontColor());
                materialList.add(material);
            }
            for (GroundMaterial material : materialList) {
                materialRepository.insert(material);
            }

            // update couponSetting
            List<GroundPromotionAdvancedInfo> advancedInfos =
                    groundPromotionAdvancedInfoRepository.queryByGroundPromotionId(groundPromotionDTO.getId());
            GroundAdvancedCoupon groundAdvancedCoupon = groundPromotionDTO.getCoupon();
            if (CollectionUtils.isEmpty(advancedInfos)) {
                if (Objects.nonNull(groundAdvancedCoupon)) {
                    GroundPromotionAdvancedInfo groundPromotionAdvancedInfo = buildAdvancedInfo(groundAdvancedCoupon, groundPromotionDTO.getId());
                    groundPromotionAdvancedInfoRepository.insert(groundPromotionAdvancedInfo);
                }
            } else {
                GroundPromotionAdvancedInfoExample example = new GroundPromotionAdvancedInfoExample();
                example.createCriteria().andGroundPromotionIdEqualTo(groundPromotionDTO.getId());
                groundPromotionAdvancedInfoRepository.updateSelective(
                        buildAdvancedInfo(groundAdvancedCoupon, groundPromotionDTO.getId()),
                        example);
            }

            return RemoteResponse.success(true);

        } catch (Exception e) {
            log.error("[updateGroundPromotion]groundPromotionDTO is {},exception is ",
                    JSONObject.toJSONString(groundPromotionDTO), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @Override
    public PageResponseDTO<List<GroundPromotionDTO>> query(GroundPromotionRequest groundPromotionRequest) {
        try {
            if (Objects.isNull(groundPromotionRequest)) {
                log.error("[query] groundPromotionRequest is null");
                return PageResponseDTO.createFailedResponse("param is invalid");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<GroundPromotion> groundPromotionList = groundPromotionRepository.query(groundPromotionRequest);
            if (CollectionUtils.isEmpty(groundPromotionList)) {
                return PageResponseDTO.createSuccessResponse(Lists.newArrayList(), 0, 0, groundPromotionRequest.getLimit());
            }
            List<GroundPromotionDTO> groundPromotionDTOList = groundPromotionList.stream().map(groundPromotion -> {
                GroundPromotionDTO groundPromotionDTO = new GroundPromotionDTO();
                BeanUtils.copyProperties(groundPromotion, groundPromotionDTO);
                return groundPromotionDTO;
            }).sorted(Comparator.comparing(GroundPromotionDTO::getId).reversed()).collect(Collectors.toList());
            List<GroundPromotionDTO> result = groundPromotionDTOList;
            if (groundPromotionRequest.getLimit() > 0) {
                result = groundPromotionDTOList.stream().skip(groundPromotionRequest.getOffset()).limit(groundPromotionRequest.getLimit()).collect(Collectors.toList());
            }
            result.stream().forEach(groundPromotionDTO -> {
                List<Merchants> merchants = merchantsRepository.queryByGroundPromotionId(groundPromotionDTO.getId());
                if (CollectionUtils.isNotEmpty(merchants)) {
                    groundPromotionDTO.setAttractStartTime(merchants.get(0).getMerchantsStartTime());
                    groundPromotionDTO.setAttractEndTime(merchants.get(0).getMerchantsEndTime());
                    groundPromotionDTO.setMerchantsTime(sdf.format(merchants.get(0).getMerchantsStartTime()) + "-" + sdf.format(merchants.get(0).getMerchantsEndTime()));
                }
                Date groundStartTime = groundPromotionDTO.getGroundStartTime();
                Date groundEndTime = groundPromotionDTO.getGroundEndTime();
                groundPromotionDTO.setGroundTime(sdf.format(groundStartTime) + "-" + sdf.format(groundEndTime));
                Integer cityId = groundPromotionDTO.getCityId();
                RemoteResponse<List<ProvinceDTO>> provinceResponse = cityService.getCity();
                if (provinceResponse.isSuccess() && CollectionUtils.isNotEmpty(provinceResponse.getData())) {
                    String cityName = provinceResponse.getData().stream()
                            .flatMap(provinceDTO -> provinceDTO.getCityDTOS().stream())
                            .filter(cityDTO -> cityDTO.getCityId().equals(cityId))
                            .map(CityDTO::getCityName)
                            .findFirst()
                            .orElse(null);
                    groundPromotionDTO.setCity(cityName);
                }
            });

            return PageResponseDTO.createSuccessResponse(result, groundPromotionDTOList.size(), result.size(), groundPromotionRequest.getLimit());
        } catch (Exception e) {
            log.error("[query]groundPromotionRequest is {},exception is ",
                    JSONObject.toJSONString(groundPromotionRequest), e);
            return PageResponseDTO.createFailedResponse(e.getMessage());
        }
    }

    @Override
    public PageResponseDTO<List<GroundPromotionDTO>> search(GroundPromotionRequest groundPromotionRequest) {
        try {
            if (Objects.isNull(groundPromotionRequest)) {
                log.error("[query] groundPromotionRequest is null");
                return PageResponseDTO.createFailedResponse("param is invalid");
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<GroundPromotion> groundPromotionList = groundPromotionRepository.search(groundPromotionRequest);
            if (CollectionUtils.isEmpty(groundPromotionList)) {
                return PageResponseDTO.createSuccessResponse(Lists.newArrayList(), 0, 0, groundPromotionRequest.getLimit());
            }
            List<GroundPromotionDTO> groundPromotionDTOList = groundPromotionList.stream().map(groundPromotion -> {
                GroundPromotionDTO groundPromotionDTO = new GroundPromotionDTO();
                BeanUtils.copyProperties(groundPromotion, groundPromotionDTO);
                return groundPromotionDTO;
            }).sorted(Comparator.comparing(GroundPromotionDTO::getId).reversed()).collect(Collectors.toList());
            List<GroundPromotionDTO> result = groundPromotionDTOList;
            if (groundPromotionRequest.getLimit() > 0) {
                result = groundPromotionDTOList.stream().skip(groundPromotionRequest.getOffset()).limit(groundPromotionRequest.getLimit()).collect(Collectors.toList());
            }
            Map<Long, List<Merchants>> merchantsMap = merchantsRepository.queryByGroundPromotionIdList(result.stream()
                    .map(GroundPromotionDTO::getId).collect(Collectors.toList()));
            Map<Long, List<PromotionalDeal>> hasDistributeDealMap = promotionalDealRepository.queryByGroundPromotion(result.stream()
                            .filter(groundPromotionDTO -> groundPromotionDTO.getStatus() == GroundPromotionActEnum.CITY_DISTRIBUTE.getCode())
                            .map(GroundPromotionDTO::getId).collect(Collectors.toList()),
                    Lists.newArrayList(CITY_MANAGER_DISTRIBUTE.getCode()));

            result.stream().forEach(groundPromotionDTO -> {
                List<Merchants> merchants = merchantsMap.get(groundPromotionDTO.getId());
                if (CollectionUtils.isNotEmpty(merchants)) {
                    groundPromotionDTO.setAttractStartTime(merchants.get(0).getMerchantsStartTime());
                    groundPromotionDTO.setAttractEndTime(merchants.get(0).getMerchantsEndTime());
                    groundPromotionDTO.setMerchantsTime(sdf.format(merchants.get(0).getMerchantsStartTime()) + "-" + sdf.format(merchants.get(0).getMerchantsEndTime()));
                }
                Date groundStartTime = groundPromotionDTO.getGroundStartTime();
                Date groundEndTime = groundPromotionDTO.getGroundEndTime();
                groundPromotionDTO.setGroundTime(sdf.format(groundStartTime) + "-" + sdf.format(groundEndTime));
                Integer cityId = groundPromotionDTO.getCityId();
                RemoteResponse<List<ProvinceDTO>> provinceResponse = cityService.getCity();
                if (provinceResponse.isSuccess() && CollectionUtils.isNotEmpty(provinceResponse.getData())) {
                    String cityName = provinceResponse.getData().stream()
                            .flatMap(provinceDTO -> provinceDTO.getCityDTOS().stream())
                            .filter(cityDTO -> cityDTO.getCityId().equals(cityId))
                            .map(CityDTO::getCityName)
                            .findFirst()
                            .orElse(null);
                    groundPromotionDTO.setCity(cityName);
                }
                if (groundPromotionDTO.getStatus() == GroundPromotionActEnum.IN_PROGRESS.getCode()) {
                    groundPromotionDTO.setIsDownloadMaterial(true);
                } else if (groundPromotionDTO.getStatus() == GroundPromotionActEnum.CITY_DISTRIBUTE.getCode()) {
                    groundPromotionDTO.setIsDownloadMaterial(CollectionUtils.isNotEmpty(hasDistributeDealMap.get(groundPromotionDTO.getId())));
                } else {
                    groundPromotionDTO.setIsDownloadMaterial(false);
                }

            });

            return PageResponseDTO.createSuccessResponse(result, groundPromotionDTOList.size(), result.size(), groundPromotionRequest.getLimit());
        } catch (Exception e) {
            log.error("[search]groundPromotionRequest is {},exception is ",
                    JSONObject.toJSONString(groundPromotionRequest), e);
            return PageResponseDTO.createFailedResponse(e.getMessage());
        }
    }

    @Override
    public RemoteResponse<Boolean> changeStatus(GroundPromotionDTO groundPromotionDTO) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.changeStatus(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionDTO)");
        try {
            if (Objects.isNull(groundPromotionDTO) || groundPromotionDTO.getId() == 0L) {
                log.error("[changeStatus] groundPromotionDTO is {}", JSONObject.toJSONString(groundPromotionDTO));
                return RemoteResponse.fail("param invalid");
            }
            GroundPromotion groundPromotion = new GroundPromotion();
            groundPromotion.setStatus(groundPromotionDTO.getStatus());
            groundPromotion.setId(groundPromotionDTO.getId());
            groundPromotionRepository.update(groundPromotion);
            return RemoteResponse.success(true);
        } catch (Exception e) {
            log.error("[changeStatus]groundPromotionDTO is {},exception is ",
                    JSONObject.toJSONString(groundPromotionDTO), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @Deprecated
    @Override
    public RemoteResponse<Boolean> batchUpdateBu(List<Long> idList, int buId) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.batchUpdateBu(java.util.List,int)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Override
    @Deprecated
    public RemoteResponse<List<GroundPromotionDTO>> queryByIdList(GroundPromotionRequest groundPromotionRequest) {
        try {
            if (Objects.isNull(groundPromotionRequest) || groundPromotionRequest.getId() == 0L) {
                log.error("[query] groundPromotionRequest is invalid");
                return RemoteResponse.fail("param is invalid");
            }

            List<GroundPromotion> groundPromotions = groundPromotionRepository.query(groundPromotionRequest);
            if (Objects.isNull(groundPromotions)) {
                log.error("[query] groundPromotion is null");
                return RemoteResponse.fail("data not found");
            }

            List<GroundPromotionDTO> data = groundPromotions
                    .stream()
                    .map(t -> {
                        GroundPromotionDTO groundPromotionDTO = new GroundPromotionDTO();
                        BeanUtils.copyProperties(t, groundPromotionDTO);
                        return groundPromotionDTO;
                    }).collect(Collectors.toList());
            return RemoteResponses.success(data);
        } catch (Exception e) {
            log.error("[query] groundPromotionRequest is {}, exception is ",
                    JSONObject.toJSONString(groundPromotionRequest), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @Override
    @Deprecated
    public RemoteResponse<Boolean> copyByIdList(GroundPromotionRequest groundPromotionRequest) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.copyByIdList(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionRequest)");
        try {
            if (Objects.isNull(groundPromotionRequest) || groundPromotionRequest.getId() == 0L) {
                log.error("[query] groundPromotionRequest is invalid");
                return RemoteResponse.fail("param is invalid");
            }
            // 根据id列表查询活动信息
            List<GroundPromotion> groundPromotions = groundPromotionRepository.query(groundPromotionRequest);
            if (Objects.isNull(groundPromotions)) {
                log.error("[query] groundPromotion is null");
                return RemoteResponse.fail("data not found");
            }

            // 将查询到的活动信息转换为DTO对象，并复制到新的列表中
            GroundPromotionDTO groundPromotionDTO = new GroundPromotionDTO();
            BeanUtils.copyProperties(groundPromotions, groundPromotionDTO);
            List<GroundPromotionDTO> gpList = Collections.singletonList(groundPromotionDTO);
            List<GroundPromotion> groundPromotionList = gpList.stream().map(dto -> {
                // 将DTO对象转换为活动对象，并设置创建时间
                GroundPromotion promotion = new GroundPromotion();
                BeanUtils.copyProperties(dto, promotion);
                promotion.setCreateTime(new Timestamp(System.currentTimeMillis()));
                return promotion;
            }).collect(Collectors.toList());
            // 将新的活动列表插入到数据库中
            int result = groundPromotionRepository.insert(groundPromotionList.get(0));
            return result > 0 ? RemoteResponse.success(true) : RemoteResponse.success(false);
        } catch (Exception e) {
            log.error("[query] groundPromotionRequest is {}, exception is ",
                    JSONObject.toJSONString(groundPromotionRequest), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    /**
     * *
     *
     * @param groundPromotionStatusRequest
     * @return
     */
    @Override
    public RemoteResponse<Boolean> changStatus(GroundPromotionStatusRequest groundPromotionStatusRequest) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.changStatus(com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionStatusRequest)");
        try {
            if (Objects.isNull(groundPromotionStatusRequest) || Objects.isNull(groundPromotionStatusRequest.getGroundPromotionId())
                    || Objects.isNull(groundPromotionStatusRequest.getSourceStatus()) || Objects.isNull(groundPromotionStatusRequest.getTargetStatus())) {
                return RemoteResponse.fail("param invalid");
            }
            return RemoteResponse.success(groundPromotionRepository.updateStatus(groundPromotionStatusRequest.getGroundPromotionId(), groundPromotionStatusRequest.getSourceStatus(), groundPromotionStatusRequest.getTargetStatus()));
        } catch (Exception e) {
            log.error("[changStatus] groundPromotionStatusRequest is {}, exception is ",
                    JSONObject.toJSONString(groundPromotionStatusRequest), e);
            return RemoteResponse.fail(e.getMessage());

        }
    }

    @Override
    public RemoteResponse<GroundPromotionDTO> queryMaterialById(long id) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.queryMaterialById(long)");
        try {
            if (id <= 0L) {
                log.error("[queryMaterialById] id is invalid");
                return RemoteResponse.fail("param is invalid");
            }
            GroundPromotionRequest groundPromotionRequest = new GroundPromotionRequest();
            groundPromotionRequest.setId(id);
            List<GroundMaterial> groundMaterials = materialRepository.queryMaterialByGroundPromotionId(id);
            List<GroundPromotion> groundPromotionList = groundPromotionRepository.query(groundPromotionRequest);
            List<Merchants> merchantsList = merchantsRepository.queryByGroundPromotionId(id);
            if (CollectionUtils.isEmpty(groundPromotionList)) {
                return RemoteResponse.fail("地推活动不存在");
            }
            GroundPromotion groundPromotion = groundPromotionList.get(0);
            GroundPromotionDTO groundPromotionDTO = new GroundPromotionDTO();
            BeanUtils.copyProperties(groundPromotion, groundPromotionDTO);
            Date groundStartTime = groundPromotionDTO.getGroundStartTime();
            Date groundEndTime = groundPromotionDTO.getGroundEndTime();
            groundPromotionDTO.setGroundTime(groundStartTime.toString() + groundEndTime.toString());
            List<GroundMaterialDTO> groundMaterialDTOList = groundMaterials.stream()
                    .filter(groundMaterial -> groundMaterial.getGroundPromotionId().equals(groundPromotion.getId()))
                    .map(groundMaterial -> {
                        GroundMaterialDTO groundMaterialDTO = new GroundMaterialDTO();
                        BeanUtils.copyProperties(groundMaterial, groundMaterialDTO);
                        return groundMaterialDTO;
                    }).collect(Collectors.toList());
            groundPromotionDTO.setMaterials(groundMaterialDTOList);
            if (CollectionUtils.isNotEmpty(merchantsList)) {
                Merchants merchant = merchantsList.get(0);
                groundPromotionDTO.setAttractStartTime(merchant.getMerchantsStartTime());
                groundPromotionDTO.setAttractEndTime(merchant.getMerchantsEndTime());
                groundPromotionDTO.setMisId(merchant.getMisId());
                groundPromotionDTO.setMerchantsTime(merchant.getMerchantsStartTime().toString() + merchant.getMerchantsEndTime().toString());
            }
            Integer cityId = groundPromotionDTO.getCityId();
            RemoteResponse<List<ProvinceDTO>> city = cityService.getCity();
            List<CityDTO> cityDTOS = city.getData().get(0).getCityDTOS();
            String cityName = cityDTOS.stream()
                    .filter(cityDTO -> cityDTO.getCityId().equals(cityId))
                    .map(CityDTO::getCityName)
                    .findFirst()
                    .orElse(null);
            groundPromotionDTO.setCity(cityName);

            // query promotionAdvancedInfo by id
            List<GroundPromotionAdvancedInfo> advancedInfos = groundPromotionAdvancedInfoRepository.queryByGroundPromotionId(id);
            GroundAdvancedCoupon couponInfo = new GroundAdvancedCoupon();
            if (CollectionUtils.isEmpty(advancedInfos) || Objects.isNull(advancedInfos.get(0)) || advancedInfos.get(0).getIsIssueCoupon() == 0) {
                couponInfo.setIssueCoupon(false);
            } else {
                couponInfo.setIssueCoupon(true);
                couponInfo.setDpCouponCode(advancedInfos.get(0).getDpCouponCode());
                couponInfo.setMtCouponCode(advancedInfos.get(0).getMtCouponCode());
            }
            if (CollectionUtils.isEmpty(advancedInfos) || Objects.isNull(advancedInfos.get(0)) || advancedInfos.get(0).getIsIssueWxGroupCoupon() == 0) {
                couponInfo.setIssueWxGroupCoupon(false);
            } else {
                couponInfo.setIssueWxGroupCoupon(true);
                couponInfo.setWxGroupCouponInfo(JSONObject.parseObject(advancedInfos.get(0).getWxGroupCouponInfo(), WxGroupCouponInfoDTO.class));
            }
            groundPromotionDTO.setCoupon(couponInfo);

            return RemoteResponse.success(groundPromotionDTO);
        } catch (
                Exception e) {
            log.error("[queryMaterialById] id is {}, exception is ",
                    JSONObject.toJSONString(id), e);
            return RemoteResponse.fail(e.getMessage());

        }
    }

    @Override
    public void loadAndParseGroundExcel(String fileKey, String url, List<Integer> dpCityIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.loadAndParseGroundExcel(java.lang.String,java.lang.String,java.util.List)");
        try {
            if (!SecSdk.securitySSRF(url, WHITE_DOMAIN_LIST)) {

                throw new RuntimeException("请求域名不在白名单中！");
            }

            int expireTime = 60;
            StoreKey storeKey = new StoreKey(GroundPromotionConstants.SQUIRREL_GROUND_BATCH_TASK_CATEGORY, fileKey);
            redisStoreClient.set(storeKey, GroundPromotionBatchInfoDTO.builder().status(ExcelParseStatusEnum.RUNNING.getCode()).build(), expireTime);
            // 读取excel
            List<GroundPromotionBatchRowStrBo> groundPromotionBatchRowStrBoList = groundPromotionBatchExcelUtils.getExcelDataByParseFile(url);

            if (!groundPromotionBatchRowStrBoList.isEmpty()) {
                // 逐行解析excel
                groundPromotionBatchRowStrBoList = parseGroundExcel(groundPromotionBatchRowStrBoList, dpCityIdList);
                if (Objects.nonNull(groundPromotionBatchRowStrBoList)) {
                    // 生成excel
                    String resultUrl = groundPromotionBatchExcelUtils.generateFile(fileKey, groundPromotionBatchRowStrBoList);
                    if (Strings.isNotBlank(resultUrl) && redisStoreClient.exists(storeKey)) {
                        //解析完成，且解析结果无误，更新redis
                        GroundPromotionBatchInfoDTO groundPromotionBatchInfoDTO = redisStoreClient.get(storeKey);
                        groundPromotionBatchInfoDTO.setStatus(ExcelParseStatusEnum.SUCCESS.getCode());
                        groundPromotionBatchInfoDTO.setTotalCount((int) groundPromotionBatchRowStrBoList.stream().count());
                        groundPromotionBatchInfoDTO.setSuccessCount((int) groundPromotionBatchRowStrBoList.stream()
                                .filter(strBo -> strBo.getLoadResult().equals(ExcelParseResultEnum.SUCCESS.getDesc()))
                                .count());
                        groundPromotionBatchInfoDTO.setUrl(resultUrl);
                        redisStoreClient.set(storeKey, groundPromotionBatchInfoDTO, GroundPromotionConstants.SQUIRREL_GROUND_BATCH_RESULT_EXPIRE_TIME);
                        return;
                    }
                }
            }
            // 解析完成，但是解析过程出错
            redisStoreClient.set(storeKey, GroundPromotionBatchInfoDTO.builder().status(ExcelParseStatusEnum.FAIL.getCode()).build(), GroundPromotionConstants.SQUIRREL_GROUND_BATCH_RESULT_EXPIRE_TIME);
        } catch (Exception e) {
            log.error("[loadAndParseGroundExcel] error, fileKey is {}, exception is", fileKey, e);
        }
    }

    private List<GroundPromotionBatchRowStrBo> parseGroundExcel(List<GroundPromotionBatchRowStrBo> groundPromotionBatchRowStrBoList, List<Integer> dpCityIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.parseGroundExcel(java.util.List,java.util.List)");
        try {
            List<GroundPromotionBatchRowStrBo> remainedList = groundPromotionBatchRowStrBoList.stream()
                    .filter(strBo -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBo.getLoadResult())).collect(Collectors.toList());

            // 构建cityId
            List<Integer> cityIdList = remainedList.stream()
                    .map(strBo -> Integer.parseInt(strBo.getDpCityId()))
                    .distinct()
                    .collect(Collectors.toList());

            // 构建shopId
            List<Long> shopIdList = remainedList.stream()
                    .map(strBo -> Long.parseLong(strBo.getDpShopId()))
                    .distinct()
                    .collect(Collectors.toList());

            // 构建dealId
            List<Integer> dealIdList = remainedList.stream()
                    .filter(strBo -> Long.parseLong(strBo.getDpDealId()) <= Integer.MAX_VALUE)
                    .map(strBo -> Integer.parseInt(strBo.getDpDealId()))
                    .distinct()
                    .collect(Collectors.toList());

            // 构建销售misId
            List<String> misIdList = remainedList.stream()
                    .flatMap(strBo -> Arrays.stream(strBo.getMisIdList().split(",")))
                    .distinct()
                    .collect(Collectors.toList());

            // 查询父级城市id
            Map<Integer, List<Integer>> cityParentMap = cityInfoService.batchLoadParentCities(cityIdList);

            // 查询shop
            batchShopQueryPool.setCorePoolSize(20);
            batchShopQueryPool.setMaximumPoolSize(50);
            List<CompletableFuture<List<DpPoiDTO>>> shopFutureList = Lists.partition(shopIdList, 50).stream()
                    .map(partList -> {
                        DpPoiRequest request = new DpPoiRequest();
                        request.setShopIds(partList);
                        request.setFields(Lists.newArrayList("shopId", "cityId"));
                        return CompletableFuture.supplyAsync(() -> {
                            try {
                                return dpPoiService.findShopsByShopIds(request);
                            } catch (Exception e) {
                                log.error("[parseGroundExcel.findShops] error, partList is {}, exception is", partList, e);
                                return null;
                            }
                        }, batchShopQueryPool);
                    }).collect(Collectors.toList());

            // 查询deal明细
            List<CompletableFuture<Map<Integer, DealGroupBaseDTO>>> dealFutureList = Lists.partition(dealIdList, 100).stream()
                    .map(partList -> CompletableFuture.supplyAsync(() -> {
                        Map<Integer, DealGroupBaseDTO> dealGroupMap = dealGroupBaseService.multiGetDealGroup(partList);
                        return dealGroupMap;
                    }, batchDealQueryPool))
                    .collect(Collectors.toList());

            // 查询dealShop明细
            List<CompletableFuture<Map<Integer, List<Long>>>> dealShopFutureList = Lists.partition(dealIdList, 100).stream()
                    .map(partList -> CompletableFuture.supplyAsync(() -> {
                        Map<Integer, List<Long>> dealShopMap = dealShopQueryService.queryDisplayLongShopIdByDealGroup(partList);
                        return dealShopMap;
                    }, batchDealShopQueryPool))
                    .collect(Collectors.toList());

            // 查询misId
            List<CompletableFuture<Map<String, Long>>> uIdFutureList = Lists.partition(misIdList, 200).stream()
                    .map(partList -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return messageAcl.getUUIDs(partList);
                        } catch (Exception e) {
                            log.error("[parseGroundExcel.getUID] error, idList is {}, exception is", partList, e);
                            return null;
                        }
                    }, batchMisQueryPool)).collect(Collectors.toList());


            // 获取shop结果
            Map<Long, DpPoiDTO> dpPoiDTOMap = CompletableFuture.allOf(shopFutureList.toArray(new CompletableFuture[0])).thenApply(
                    val -> shopFutureList.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .reduce((list1, list2) -> {
                                list1.addAll(list2);
                                return list1;
                            }).orElse(Lists.newArrayList()).stream()
                            .collect(Collectors.toMap(DpPoiDTO::getShopId, o -> o, (k1, k2) -> k2))).join();
            batchShopQueryPool.setCorePoolSize(5);
            batchShopQueryPool.setMaximumPoolSize(20);

            // 获取deal结果
            Map<Integer, DealGroupBaseDTO> dealGroupMap = CompletableFuture.allOf(dealFutureList.toArray(new CompletableFuture[0])).thenApply(
                    val -> dealFutureList.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .reduce((resultMap, itemMap) -> {
                                resultMap.putAll(itemMap);
                                return resultMap;
                            }).orElse(Maps.newHashMap())).join();

            Map<Integer, List<Long>> dealShopMap = CompletableFuture.allOf(dealShopFutureList.toArray(new CompletableFuture[0])).thenApply(
                    val -> dealShopFutureList.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .reduce((resultMap, itemMap) -> {
                                resultMap.putAll(itemMap);
                                return resultMap;
                            }).orElse(Maps.newHashMap())).join();

            // 获取misId结果
            Map<String, Long> uIdMap = CompletableFuture.allOf(uIdFutureList.toArray(new CompletableFuture[0])).thenApply(
                    val -> uIdFutureList.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .reduce((resultMap, itemMap) -> {
                                resultMap.putAll(itemMap);
                                return resultMap;
                            }).orElse(Maps.newHashMap())).join();

            Date now = Calendar.getInstance().getTime();
            groundPromotionBatchRowStrBoList.stream().filter(strBo -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBo.getLoadResult()))
                    .forEach(strBo -> {
                        try {
                            GroundPromotionBatchRowBo bo = GroundPromotionBatchTransfer.strBo2Bo(strBo);
                            // 校验城市id合法
                            if (!dpCityIdList.contains(bo.getDpCityId())) {
                                List<Integer> parentList = cityParentMap.get(bo.getDpCityId());
                                if (!parentList.stream().anyMatch(dpCityIdList::contains)) {
                                    strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBo.setFailReason("城市不在活动范围");
                                    return;
                                }
                            }

                            // 校验门店id存在 门店id与城市id匹配
                            if (!dpPoiDTOMap.containsKey(bo.getDpShopId())) {
                                strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBo.setFailReason("门店ID不存在");
                                return;
                            } else if (dpPoiDTOMap.get(bo.getDpShopId()).getCityId() != bo.getDpCityId()) {
                                strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBo.setFailReason("门店ID与城市ID匹配失败");
                                return;
                            }

                            // 校验团单id存在
                            // 团单id为0，表示所有团单，跳过检验
                            if (bo.getDpDealId() != 0) {
                                if (!dealGroupMap.containsKey((int) bo.getDpDealId())) {
                                    strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBo.setFailReason("团单ID不存在");
                                    return;
                                }
                                // 校验团单id与分店id匹配 团单在线且在可售时间范围
                                DealGroupBaseDTO dealGroupBaseDTO = dealGroupMap.get((int) bo.getDpDealId());
                                List<Long> dealShopIdList = dealShopMap.get((int) bo.getDpDealId());
                                if (!dealShopIdList.contains(bo.getDpShopId())) {
                                    strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBo.setFailReason("门店ID与团单ID匹配失败");
                                    return;
                                } else if (dealGroupBaseDTO.getStatus() == DealGroupStatusEnum.OFFLINE.getCode()) {
                                    strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBo.setFailReason("团单已下线");
                                    return;
                                }
                                if (dealGroupBaseDTO.getBeginDate().after(now) || dealGroupBaseDTO.getEndDate().before(now)) {
                                    strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBo.setFailReason("团单不在可售时间范围");
                                    return;
                                }
                            }

                            // 校验销售misid存在
                            boolean misInvalid = bo.getMisIdList().stream()
                                    .filter(mis -> !mis.equals("") && !NumberUtils.isDigits(mis))
                                    .anyMatch(mis -> !uIdMap.containsKey(mis));
                            if (misInvalid) {
                                strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBo.setFailReason("mis号不存在");
                                return;
                            }

                            boolean mobileInvalid = bo.getMisIdList().stream()
                                    .filter(mobile -> NumberUtils.isDigits(mobile))
                                    .anyMatch(mobile -> mobile.length() != 11);
                            if (mobileInvalid) {
                                strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBo.setFailReason("兼职手机号不合法");
                                return;
                            }

                            strBo.setLoadResult(ExcelParseResultEnum.SUCCESS.getDesc());
                        } catch (Exception e) {
                            log.error("[parseGroundExcel.checkBo] error, strBo is {}, exception is", JSONObject.toJSONString(strBo), e);
                            strBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                            strBo.setFailReason("未知错误，请联系zhangxing34");
                        }
                    });
            return groundPromotionBatchRowStrBoList;
        } catch (Exception e) {
            log.error("[parseGroundExcel] error, groundPromotionBatchRowStrBoList is {}, exception is", JSONObject.toJSONString(groundPromotionBatchRowStrBoList), e);
            return null;
        }
    }

    @Override
    public RemoteResponse<GroundPromotionBatchInfoDTO> getGroundExcelParseResult(String fileKey) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.getGroundExcelParseResult(java.lang.String)");
        try {
            StoreKey storeKey = new StoreKey(GroundPromotionConstants.SQUIRREL_GROUND_BATCH_TASK_CATEGORY, fileKey);
            if (!redisStoreClient.exists(storeKey)) {
                return RemoteResponse.success(GroundPromotionBatchInfoDTO.builder().status(ExcelParseStatusEnum.FAIL.getCode()).build());
            }
            return RemoteResponse.success(redisStoreClient.get(storeKey));
        } catch (Exception e) {
            log.error("[getGroundExcelParseResult] error, fileKey is {}, exception is", fileKey, e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @Override
    public RemoteResponse<List<Long>> queryIdByTime(Date startTime, Date endTime) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.queryIdByTime(java.util.Date,java.util.Date)");
        try {
            GroundPromotionRequest groundPromotionRequest = new GroundPromotionRequest();
            groundPromotionRequest.setGroundEndAfterDate(startTime);
            groundPromotionRequest.setGroundStartBeforeDate(endTime);
            List<Long> ids = groundPromotionRepository.query(groundPromotionRequest).stream()
                    .map(GroundPromotion::getId)
                    .collect(Collectors.toList());
            return RemoteResponse.success(ids);
        } catch (Exception e) {
            log.error("[queryIdByTime] error, startTime is {}, endTime is {}, exception is", startTime, endTime, e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    private GroundPromotionAdvancedInfo buildAdvancedInfo(GroundAdvancedCoupon groundAdvancedCoupon, long groundPromotionId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.buildAdvancedInfo(com.sankuai.carnation.distribution.groundpromotion.dto.GroundAdvancedCoupon,long)");
        GroundPromotionAdvancedInfo groundPromotionAdvancedInfo =
                new GroundPromotionAdvancedInfo();
        groundPromotionAdvancedInfo.setGroundPromotionId(groundPromotionId);
        if (Objects.nonNull(groundAdvancedCoupon)) {
            groundPromotionAdvancedInfo.setIsIssueCoupon(groundAdvancedCoupon.isIssueCoupon() ? (byte) 1 : (byte) 0);
            groundPromotionAdvancedInfo.setDpCouponCode(groundAdvancedCoupon.isIssueCoupon() ? groundAdvancedCoupon.getDpCouponCode() : "");
            groundPromotionAdvancedInfo.setMtCouponCode(groundAdvancedCoupon.isIssueCoupon() ? groundAdvancedCoupon.getMtCouponCode() : "");
            groundPromotionAdvancedInfo.setIsIssueWxGroupCoupon(groundAdvancedCoupon.isIssueWxGroupCoupon() ? (byte) 1 : (byte) 0);
            groundPromotionAdvancedInfo.setWxGroupCouponInfo(Objects.nonNull(groundAdvancedCoupon.getWxGroupCouponInfo()) ?
                    JSONObject.toJSONString(groundAdvancedCoupon.getWxGroupCouponInfo()) : "");
        }
        return groundPromotionAdvancedInfo;
    }

    @Deprecated
    @Override
    public String getDpPoiDTO(Long dpShopId) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.getDpPoiDTO(java.lang.Long)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Deprecated
    @Override
    public String getOrder(Long orderId) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.getOrder(java.lang.Long)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Override
    public void loadAndParsePointExcel(String fileKey, String url, String parsedExcelUrl, List<Integer> dpCityIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.loadAndParsePointExcel(java.lang.String,java.lang.String,java.lang.String,java.util.List)");
        try {
            if (!SecSdk.securitySSRF(url, WHITE_DOMAIN_LIST) || !SecSdk.securitySSRF(parsedExcelUrl, WHITE_DOMAIN_LIST)) {
                throw new RuntimeException("请求域名不在白名单中！");
            }

            StoreKey storeKey = new StoreKey(GroundPromotionConstants.SQUIRREL_GROUND_BATCH_TASK_CATEGORY, fileKey);
            // 读取excel
            List<GroundPromotionPointRowStrBO> groundPromotionPointRowStrBOList = groundPromotionPointExcelUtils.getExcelDataByParseFile(url, 2);
            List<CreateResultRowStrBO> createResultRowStrBOList = createResultExcelUtils.getExcelDataByParseFile(parsedExcelUrl);

            if (!groundPromotionPointRowStrBOList.isEmpty() && !createResultRowStrBOList.isEmpty()) {
                // 逐行解析excel
                groundPromotionPointRowStrBOList = parsePointExcel(groundPromotionPointRowStrBOList, createResultRowStrBOList, dpCityIdList);
                if (Objects.nonNull(groundPromotionPointRowStrBOList)) {
                    // 生成excel
                    String resultUrl = groundPromotionPointExcelUtils.generateFile(fileKey, groundPromotionPointRowStrBOList);
                    if (Strings.isNotBlank(resultUrl) && redisStoreClient.exists(storeKey)) {
                        //解析完成，且解析结果无误，更新redis
                        GroundPromotionBatchInfoDTO groundPromotionBatchInfoDTO = redisStoreClient.get(storeKey);
                        groundPromotionBatchInfoDTO.setStatus(ExcelParseStatusEnum.SUCCESS.getCode());
                        groundPromotionBatchInfoDTO.setTotalCount((int) groundPromotionPointRowStrBOList.stream().count());
                        groundPromotionBatchInfoDTO.setSuccessCount((int) groundPromotionPointRowStrBOList.stream()
                                .filter(strBo -> strBo.getLoadResult().equals(ExcelParseResultEnum.SUCCESS.getDesc()))
                                .count());
                        groundPromotionBatchInfoDTO.setUrl(resultUrl);
                        redisStoreClient.set(storeKey, groundPromotionBatchInfoDTO, GroundPromotionConstants.SQUIRREL_GROUND_BATCH_RESULT_EXPIRE_TIME);
                        return;
                    }
                }
            }
            // 解析完成，但是解析过程出错
            redisStoreClient.set(storeKey, GroundPromotionBatchInfoDTO.builder().status(ExcelParseStatusEnum.FAIL.getCode()).build(), GroundPromotionConstants.SQUIRREL_GROUND_BATCH_RESULT_EXPIRE_TIME);
        } catch (Exception e) {
            log.error("[loadAndParsePointExcel] error, url is {}, exception is", url, e);
        }
    }

    private List<GroundPromotionPointRowStrBO> parsePointExcel(List<GroundPromotionPointRowStrBO> pointRowStrBOList,
                                                               List<CreateResultRowStrBO> createResultRowStrBOList,
                                                               List<Integer> dpCityIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.parsePointExcel(java.util.List,java.util.List,java.util.List)");
        try {
            // 快速地推文件shopId
            List<Long> shopIdList = createResultRowStrBOList.stream()
                    .filter(strBo -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBo.getLoadResult()))
                    .map(strBo -> Long.parseLong(strBo.getDpShopId()))
                    .distinct()
                    .collect(Collectors.toList());

            // 快速地推文件shopId与dealId的映射
            Map<Long, Set<Long>> shopId2DealId = createResultRowStrBOList.stream()
                    .filter(strBo -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBo.getLoadResult()))
                    .collect(Collectors.groupingBy(
                            strBo -> Long.parseLong(strBo.getDpShopId()),
                            Collectors.mapping(strBo -> Long.parseLong(strBo.getDpDealId()), Collectors.toSet())
                    ));

            // 快速地推文件shopId与cityId的映射
            Map<Long, Integer> shopId2CityId = createResultRowStrBOList.stream()
                    .filter(strBo -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBo.getLoadResult()))
                    .collect(Collectors.toMap(strBo -> Long.parseLong(strBo.getDpShopId()),
                            strBo -> Integer.parseInt(strBo.getDpCityId()), (v1, v2) -> v2));

            List<GroundPromotionPointRowStrBO> remainedList = pointRowStrBOList.stream()
                    .filter(strBO -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBO.getLoadResult())).collect(Collectors.toList());

            // 构建cityId
            List<Integer> cityIdList = remainedList.stream()
                    .map(strBo -> Integer.parseInt(strBo.getDpCityId()))
                    .distinct()
                    .collect(Collectors.toList());

            // 构建销售misId
            List<String> misIdList = remainedList.stream()
                    .flatMap(strBo -> Arrays.stream(strBo.getMisIdList().split(",")))
                    .distinct()
                    .collect(Collectors.toList());

            // 查询父级城市id
            Map<Integer, List<Integer>> cityParentMap = cityInfoService.batchLoadParentCities(cityIdList);

            // 查询misId
            List<CompletableFuture<Map<String, Long>>> uIdFutureList = Lists.partition(misIdList, 200).stream()
                    .map(partList -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return messageAcl.getUUIDs(partList);
                        } catch (Exception e) {
                            log.error("[parsePointExcel.getUID] error, idList is {}, exception is", partList, e);
                            return null;
                        }
                    }, batchMisQueryPool)).collect(Collectors.toList());

            // 获取misId结果
            Map<String, Long> uIdMap = CompletableFuture.allOf(uIdFutureList.toArray(new CompletableFuture[0])).thenApply(
                    val -> uIdFutureList.stream()
                            .map(CompletableFuture::join)
                            .filter(Objects::nonNull)
                            .reduce((resultMap, itemMap) -> {
                                resultMap.putAll(itemMap);
                                return resultMap;
                            }).orElse(Maps.newHashMap())).join();

            pointRowStrBOList.stream().filter(strBO -> !ExcelParseResultEnum.FAIL.getDesc().equals(strBO.getLoadResult())).collect(Collectors.toList())
                    .forEach(strBO -> {
                        try {
                            GroundPromotionPointRowBO bo = GroundPromotionPointTransfer.strBo2Bo(strBO, false);

                            // 校验门店id存在
                            List<Long> pointShopIdList = bo.getDpShopIdList();
                            boolean shopInvalid = false;
                            StringBuilder failReason = new StringBuilder("以下门店ID不存在：\n");
                            for (long pointShopId : pointShopIdList) {
                                if (!shopIdList.contains(pointShopId)) {
                                    failReason.append(pointShopId + "\n");
                                    shopInvalid = true;
                                }
                            }
                            if (shopInvalid) {
                                strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBO.setFailReason(failReason.toString());
                                return;
                            }

                            // 校验团单数量
                            boolean dealInvalid = false;
                            failReason = new StringBuilder("以下门店ID找不到对应团单：\n");
                            for (long pointShopId : pointShopIdList) {
                                if (shopId2DealId.get(pointShopId).contains(0L)) {
                                    failReason.append(pointShopId + "\n");
                                    dealInvalid = true;
                                }
                            }
                            if (dealInvalid) {
                                strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBO.setFailReason(failReason.toString());
                                return;
                            }

                            int totalDealNum = 0;
                            for (long pointShopId : pointShopIdList) {
                                Set<Long> dealIdList = shopId2DealId.get(pointShopId);
                                totalDealNum += dealIdList.size();
                                if (totalDealNum > MAX_POINT_DEAL_NUM) {
                                    strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBO.setFailReason(String.format("团单数量最多%s个", MAX_POINT_DEAL_NUM));
                                    return;
                                }
                            }

                            // 校验落地页展示方式是否正确
                            int showType = bo.getShowType();
                            if (PageShowTypeEnum.fromCode(showType) == PageShowTypeEnum.UNKNOWN) {
                                strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBO.setFailReason("没有对应的落地页");
                                return;
                            }

                            // 校验城市id
                            int cityId = bo.getDpCityId();
                            if (!dpCityIdList.contains(cityId)) {
                                List<Integer> parentList = cityParentMap.get(cityId);
                                if (!parentList.stream().anyMatch(dpCityIdList::contains)) {
                                    strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBO.setFailReason("城市不在活动范围");
                                    return;
                                }
                            }
                            for (long pointShopId : pointShopIdList) {
                                if (shopId2CityId.get(pointShopId) != cityId) {
                                    strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                    strBO.setFailReason("门店ID与城市ID匹配失败");
                                    return;
                                }
                            }

                            // 校验销售misId存在
                            boolean misInvalid = bo.getMisIdList().stream()
                                    .filter(mis -> !mis.equals("") && !NumberUtils.isDigits(mis))
                                    .anyMatch(mis -> !uIdMap.containsKey(mis));
                            if (misInvalid) {
                                strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBO.setFailReason("mis号不存在");
                                return;
                            }

                            boolean mobileInvalid = bo.getMisIdList().stream()
                                    .filter(mobile -> NumberUtils.isDigits(mobile))
                                    .anyMatch(mobile -> mobile.length() != 11);
                            if (mobileInvalid) {
                                strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                                strBO.setFailReason("兼职手机号不合法");
                                return;
                            }
                            strBO.setLoadResult(ExcelParseResultEnum.SUCCESS.getDesc());
                        } catch (Exception e) {
                            log.error("[parsePointExcel.checkBO] error, strBO is {}, exception is", JSONObject.toJSONString(strBO), e);
                            strBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
                            strBO.setFailReason("未知错误，请联系zhangxing34");
                        }
                    });
            return pointRowStrBOList;
        } catch (Exception e) {
            log.error("[parsePointExcel] error, pointRowStrBOList is {}, exception is", JSONObject.toJSONString(pointRowStrBOList), e);
            return null;
        }
    }


    @Override
    public RemoteResponse<GroundPromotionBatchInfoDTO> getPointExcelParseResult(String fileKey) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.getPointExcelParseResult(java.lang.String)");
        try {
            StoreKey storeKey = new StoreKey(GroundPromotionConstants.SQUIRREL_GROUND_BATCH_TASK_CATEGORY, fileKey);
            if (!redisStoreClient.exists(storeKey)) {
                return RemoteResponse.success(GroundPromotionBatchInfoDTO.builder().status(ExcelParseStatusEnum.FAIL.getCode()).build());
            }
            return RemoteResponse.success(redisStoreClient.get(storeKey));
        } catch (Exception e) {
            log.error("[getPointExcelParseResult] error, fileKey is {}, exception is", fileKey, e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @Override
    public void createQrScanLog(String code, String bizType, int cooperateType, long userId, int platform, long shelfId, long poiId, long scanTime, String shelfUrl) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.createQrScanLog(java.lang.String,java.lang.String,int,long,int,long,long,long,java.lang.String)");
        GroundPartTimeQrLog groundPartTimeQrLog = GroundPartTimeQrLog.builder()
                .code(code)
                .bizType(bizType)
                .cooperateType(cooperateType)
                .shelfId(shelfId)
                .poiId(poiId)
                .userId(userId)
                .platform(platform)
                .scanTime(DateUtils.toDate(scanTime))
                .shelfUrl(shelfUrl).build();
        groundPartTimeQrLogRepository.insert(groundPartTimeQrLog);

        // 写缓存
        redisAcl.setGroundPartTimeQrLogCache(groundPartTimeQrLog, DateUtils.differentSecondToTomorrow(new Date()));
    }

    @Override
    public RemoteResponse<Boolean> updateOrderCheatStatus(String orderId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.service.GroundPromotionActServiceImpl.updateOrderCheatStatus(java.lang.String)");
        try {
            TechnicianResp<Boolean> resp = orderReceiptUpdateService.updateOrderCheatStatus(orderId, DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode(), OrderCheatStatus.CHEAT.getCode());
            if (resp.respSuccess()) {
                return RemoteResponse.success(resp.getData());
            }
            return RemoteResponse.fail(resp.getMsg());
        } catch (Exception e) {
            log.error("[updateOrderCheatStatus] error, orderId is {}, exception is ", orderId, e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

}

