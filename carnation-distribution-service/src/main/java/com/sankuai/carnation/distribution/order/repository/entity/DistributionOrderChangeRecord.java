package com.sankuai.carnation.distribution.order.repository.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: distribution_order_change_record
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DistributionOrderChangeRecord {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: order_id
     *   说明: 订单单号
     */
    private String orderId;

    /**
     *   字段: order_type
     *   说明: 订单类型:1-交易订单
     */
    private Integer orderType;
    /**
     *   字段: change_reason
     *   说明: 来源类型 1-分销侧 2-开放平台通知
     */
    private String changeReason;

    /**
     *   字段: channel
     *   说明: 意向渠道
     */
    private String channel;

    /**
     *   字段: action_time
     *   说明: 变更时间
     */
    private Date actionTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    private Integer isDeleted;
}