package com.sankuai.carnation.distribution.privatelive.account.model;

import com.sankuai.carnation.distribution.privatelive.account.enums.PrivateLiveTaskTypeEnum;
import lombok.*;

/**
 * @Author: bi<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-08-15 16:24
 * @Description:
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class EarliestPrivateLiveTaskRequest {

    private Long accountId;

    private Long taskId;

    private PrivateLiveTaskTypeEnum taskType;
}
