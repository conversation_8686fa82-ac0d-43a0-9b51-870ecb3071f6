package com.sankuai.carnation.distribution.distributioncode.facade.model;

import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.domain.model.CommissionStrategyVO;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.intention.dto.user.module.CommissionStrategy;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeiBoDistributionCode {

    private DistributionBusinessChannelEnum channel;

    private String distributionCode;

    private Long planId;

    private String distributorPid;

    private Long productId;

    private ProductTypeEnum productType;

    private Long distributorId;

    private CommissionStrategy commissionStrategy;

    public void fillDistributorInfo(BizDistributorBO bizDistributorBo) {
        this.setDistributorId(bizDistributorBo.getDistributorId());
    }

    public void fillDistributionPlanInfo(DistributionPlanDO distributionPlanDO) {
        CommissionStrategyVO commissionStrategyVo = distributionPlanDO.getCommissionStrategyVo();
        this.setCommissionStrategy(CommissionStrategy.builder()
                .commissionRate(commissionStrategyVo.getCommissionRate())
                .channelCommissionRate(commissionStrategyVo.getChannelCommissionRate())
                .platformCommissionRate(commissionStrategyVo.getPlatformCommissionRate())
                .distributorCommissionRate(commissionStrategyVo.getDistributorCommissionRate())
                .build());
    }
}
