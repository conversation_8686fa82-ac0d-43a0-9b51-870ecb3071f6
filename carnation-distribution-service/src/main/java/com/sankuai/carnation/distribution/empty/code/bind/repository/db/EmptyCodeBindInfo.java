package com.sankuai.carnation.distribution.empty.code.bind.repository.db;

import java.io.Serializable;
import java.util.Date;

public class EmptyCodeBindInfo implements Serializable {
    private Long id;

    private Integer bizType;

    private Long bizId;

    private Integer codeSource;

    private String codeKey;

    private String codeLink;

    private String codeImage;

    private Integer bindKeyType;

    private String bindKey;

    private String bindExtInfo;

    private Integer operatorType;

    private String operatorId;

    private Integer status;

    private Date addTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Integer getCodeSource() {
        return codeSource;
    }

    public void setCodeSource(Integer codeSource) {
        this.codeSource = codeSource;
    }

    public String getCodeKey() {
        return codeKey;
    }

    public void setCodeKey(String codeKey) {
        this.codeKey = codeKey == null ? null : codeKey.trim();
    }

    public String getCodeLink() {
        return codeLink;
    }

    public void setCodeLink(String codeLink) {
        this.codeLink = codeLink == null ? null : codeLink.trim();
    }

    public String getCodeImage() {
        return codeImage;
    }

    public void setCodeImage(String codeImage) {
        this.codeImage = codeImage == null ? null : codeImage.trim();
    }

    public Integer getBindKeyType() {
        return bindKeyType;
    }

    public void setBindKeyType(Integer bindKeyType) {
        this.bindKeyType = bindKeyType;
    }

    public String getBindKey() {
        return bindKey;
    }

    public void setBindKey(String bindKey) {
        this.bindKey = bindKey == null ? null : bindKey.trim();
    }

    public String getBindExtInfo() {
        return bindExtInfo;
    }

    public void setBindExtInfo(String bindExtInfo) {
        this.bindExtInfo = bindExtInfo == null ? null : bindExtInfo.trim();
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId == null ? null : operatorId.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EmptyCodeBindInfo other = (EmptyCodeBindInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBizType() == null ? other.getBizType() == null : this.getBizType().equals(other.getBizType()))
            && (this.getBizId() == null ? other.getBizId() == null : this.getBizId().equals(other.getBizId()))
            && (this.getCodeSource() == null ? other.getCodeSource() == null : this.getCodeSource().equals(other.getCodeSource()))
            && (this.getCodeKey() == null ? other.getCodeKey() == null : this.getCodeKey().equals(other.getCodeKey()))
            && (this.getCodeLink() == null ? other.getCodeLink() == null : this.getCodeLink().equals(other.getCodeLink()))
            && (this.getCodeImage() == null ? other.getCodeImage() == null : this.getCodeImage().equals(other.getCodeImage()))
            && (this.getBindKeyType() == null ? other.getBindKeyType() == null : this.getBindKeyType().equals(other.getBindKeyType()))
            && (this.getBindKey() == null ? other.getBindKey() == null : this.getBindKey().equals(other.getBindKey()))
            && (this.getBindExtInfo() == null ? other.getBindExtInfo() == null : this.getBindExtInfo().equals(other.getBindExtInfo()))
            && (this.getOperatorType() == null ? other.getOperatorType() == null : this.getOperatorType().equals(other.getOperatorType()))
            && (this.getOperatorId() == null ? other.getOperatorId() == null : this.getOperatorId().equals(other.getOperatorId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBizType() == null) ? 0 : getBizType().hashCode());
        result = prime * result + ((getBizId() == null) ? 0 : getBizId().hashCode());
        result = prime * result + ((getCodeSource() == null) ? 0 : getCodeSource().hashCode());
        result = prime * result + ((getCodeKey() == null) ? 0 : getCodeKey().hashCode());
        result = prime * result + ((getCodeLink() == null) ? 0 : getCodeLink().hashCode());
        result = prime * result + ((getCodeImage() == null) ? 0 : getCodeImage().hashCode());
        result = prime * result + ((getBindKeyType() == null) ? 0 : getBindKeyType().hashCode());
        result = prime * result + ((getBindKey() == null) ? 0 : getBindKey().hashCode());
        result = prime * result + ((getBindExtInfo() == null) ? 0 : getBindExtInfo().hashCode());
        result = prime * result + ((getOperatorType() == null) ? 0 : getOperatorType().hashCode());
        result = prime * result + ((getOperatorId() == null) ? 0 : getOperatorId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }
}