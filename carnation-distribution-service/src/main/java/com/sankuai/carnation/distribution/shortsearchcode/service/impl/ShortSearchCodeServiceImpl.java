package com.sankuai.carnation.distribution.shortsearchcode.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.SubCodeRelationDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.enums.QrExternalSourceTypeEnum;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.minicode.domain.tech.promocode.TechPromoCodeMiniCodeDomainService;
import com.sankuai.carnation.distribution.minicode.domain.bo.MiniCodeBO;
import com.sankuai.carnation.distribution.shortsearchcode.dto.MiniCodeDTO;
import com.sankuai.carnation.distribution.shortsearchcode.dto.ShortSearchCodeDTO;
import com.sankuai.carnation.distribution.shortsearchcode.dto.request.MiniCodeDetailRequest;
import com.sankuai.carnation.distribution.shortsearchcode.service.ShortSearchCodeService;
import com.sankuai.carnation.distribution.utils.PromoCodeUrlUtils;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeForCService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/11 16:53
 */
@Service
@Slf4j
public class ShortSearchCodeServiceImpl implements ShortSearchCodeService {

    @Autowired
    private TechPromoCodeMiniCodeDomainService techPromoCodeMiniCodeDomainService;

    @Autowired
    private PromoQRCodeCService promoQRCodeCService;

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private PromoCodeForCService promoCodeForCService;

    @Autowired
    private UniversalQRCodeGeneratorService universalQRCodeGeneratorService;

    @Override
    public RemoteResponse<ShortSearchCodeDTO> getShortCode(int techId) {
        if (techId <= 0) {
            return RemoteResponse.fail("Invalid techId");
        }
        ShortSearchCodeDTO dto = new ShortSearchCodeDTO();
        try {
            String shortCode = techPromoCodeMiniCodeDomainService.generateMiniCode(techId);
            if (StringUtils.isBlank(shortCode)) {
                Cat.logMetricForCount("职人口令码生成生成失败");
            }
            dto.setShortCode(shortCode);
        } catch (Exception e) {
            log.error("Failed to generate mini code for techId: " + techId, e);
            return RemoteResponse.fail("Failed to generate mini code");
        }
        return RemoteResponse.success(dto);
    }

    @Override
    public RemoteResponse<MiniCodeDTO> getMiniCodeDetail(MiniCodeDetailRequest request) {
        if (request == null || StringUtils.isBlank(request.getShortCode()) || request.getPlatform() <= 0 || request.getCityId() <= 0) {
            return RemoteResponse.success(null);
        }
        try {
            MiniCodeDTO dto = new MiniCodeDTO();

            List<MiniCodeBO> miniCodeBOS = techPromoCodeMiniCodeDomainService.loadByCodeAndCity(request.getShortCode(), request.getPlatform(), request.getCityId());
            if (miniCodeBOS == null || miniCodeBOS.isEmpty()) {
                return RemoteResponse.fail("未找到对应的口令码信息");
            }

            StaffCodeDTO codeDTO = getSubCodeRelationDTOs(miniCodeBOS.get(0).getBindUserId());
            if (codeDTO == null) {
                return RemoteResponse.fail("未找到对应的职人码信息");
            }

            dto.setRedirectLink(codeDTO.getCodeUrl());
            return RemoteResponse.success(dto);
        } catch (Exception e) {
            log.error("[ShortSearchCodeService.getMiniCodeDetail] request: {}", JSON.toJSONString(request), e);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Override
    public RemoteResponse<String> getCodeJumpUrl(MiniCodeDetailRequest request) {
        if (request == null || StringUtils.isBlank(request.getShortCode()) || request.getPlatform() <= 0 || request.getCityId() <= 0) {
            return RemoteResponse.success("参数非法");
        }
        try {
            List<MiniCodeBO> miniCodeBOS = techPromoCodeMiniCodeDomainService.loadByCodeAndCity(request.getShortCode(), request.getPlatform(), request.getCityId());
            if (miniCodeBOS == null || miniCodeBOS.isEmpty()) {
                return RemoteResponse.fail("未找到对应的口令码信息");
            }
            StaffCodeDTO codeDTO = getSubCodeRelationDTOs(miniCodeBOS.get(0).getBindUserId());
            if (codeDTO == null) {
                return RemoteResponse.fail("未找到对应的职人码信息");
            }
            Long mtShopId = shopMapperService.dp2mt(codeDTO.getDpShopIdLong());
            if(mtShopId == null){
                return RemoteResponse.fail("未找到对应的MT门店ID");
            }
            PromoCodeLandingPageRequest pageRequest = new PromoCodeLandingPageRequest();
            pageRequest.setQrClientType(request.getPlatform() == PlatformEnum.MT.getCode() ? QRClientType.MT_APP.getCode() : QRClientType.DP_APP.getCode());
            pageRequest.setCodeType(PromoCodeType.STAFF_CODE.code);
            pageRequest.setPoiId(mtShopId);
            pageRequest.setStaffCodeId(codeDTO.getId());

            String secretKey = "";
            RemoteResponse<QRCodeConfigDTO> queryResponse = universalQRCodeGeneratorService.queryQRCode(codeDTO.getId(), String.valueOf(codeDTO.getId()), BizTypeEnum.PROMO_STAFF_CODE.getType());
            if (queryResponse.isSuccess() && queryResponse.getData() != null) {
                secretKey = queryResponse.getData().getSecretKey();
            } else {
                RemoteResponse<QRCodeConfigDTO> insertResponse = universalQRCodeGeneratorService.insertQRCode(buildQRCodeConfigDTO(codeDTO.getId(), String.valueOf(codeDTO.getId()), BizTypeEnum.PROMO_STAFF_CODE.getType()));
                if (insertResponse.isSuccess() && insertResponse.getData() != null) {
                    secretKey = insertResponse.getData().getSecretKey();
                }
            }
            pageRequest.setCodeKey(secretKey);

            RemoteResponse<String> urlResponse = promoCodeForCService.queryPromoCodeLandingPageUrl(pageRequest);
            if (urlResponse.isSuccess() && StringUtils.isNotBlank(urlResponse.getData())) {
                return RemoteResponse.success(urlResponse.getData());
            } else {
                return RemoteResponse.fail(urlResponse.getMsg());
            }
        } catch (Exception e) {
            log.error("[ShortSearchCodeService.getCodeJumpUrl] request: {}", JSON.toJSONString(request), e);
            return RemoteResponse.fail("服务器错误");
        }
    }

    private StaffCodeDTO getSubCodeRelationDTOs(Long bindUserId) {
        PromoQRCodeResponse<List<SubCodeRelationDTO>> relation = promoQRCodeCService.getSubCodeRelationByExternal(
                QrExternalSourceTypeEnum.TECHNICIAN.getCode(), String.valueOf(bindUserId));
        if (relation == null || !relation.isSuccess() || relation.getData() == null) {
            return null;
        }
        // 最新的数据排第一位
        List<SubCodeRelationDTO> dtos = relation.getData().stream()
                .sorted(Comparator.comparing(SubCodeRelationDTO::getUpdateTime).reversed())
                .collect(Collectors.toList());

        for (SubCodeRelationDTO dto : dtos) {
            PromoQRCodeResponse<StaffCodeDTO> staffCodeDTOResponse = promoQRCodeCService
                    .getStaffCodeDTOByCodeId(dto.getSubCodeId());
            if (staffCodeDTOResponse != null && staffCodeDTOResponse.isSuccess()
                    && staffCodeDTOResponse.getData() != null && staffCodeDTOResponse.getData().getStatus() == 1) {
                return staffCodeDTOResponse.getData();
            }
        }
        return null;
    }

    private QRCodeConfigDTO buildQRCodeConfigDTO(Long bizId, String bizIdStr, int bizType) {
        QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
        qrCodeConfigDTO.setBizId(bizId);
        qrCodeConfigDTO.setBizIdStr(bizIdStr);
        qrCodeConfigDTO.setBizType(bizType);
        qrCodeConfigDTO.setNeedPadding(true);
        qrCodeConfigDTO.setDynamicFlag(true);
        qrCodeConfigDTO.setLogoFileUrl("https://img.meituan.net/beautyimg/2e9a1378e2fa97128729a7063f716f0b5713.png");
        return qrCodeConfigDTO;
    }
}
