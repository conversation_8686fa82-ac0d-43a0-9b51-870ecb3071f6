package com.sankuai.carnation.distribution.distributionplan.service.impl.merchant;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductCommissionBasisAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.DealProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.GeneralDealGroupSearchAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.MerchantAuthAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.*;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.DistributionProductPageQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanCreateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanOperateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanPageQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.MerchantChannelDistributionPlanOperateStrategy;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy.MerchantChannelDistributionPlanOperateStrategyFactory;
import com.sankuai.carnation.distribution.distributionplan.service.merchant.MerchantChannelDistributionPlanService;
import com.sankuai.carnation.distribution.distributionplan.service.model.ProductCommissionRange;
import com.sankuai.carnation.distribution.distributionplan.service.model.MerchantChannelDistributionPlanOperateContext;
import com.sankuai.carnation.distribution.distributionplan.service.validate.MerchantChannelDistributionPlanValidator;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.CreateMerchantChannelDistributionPlanVO;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.DistributionPlanProductVO;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.MerchantChannelDistributionPlanVO;
import com.sankuai.carnation.distribution.distributionplan.vo.merchant.OperateMerchantChannelDistributionPlanVO;
import com.sankuai.carnation.distribution.utils.DateUtils;
import com.sankuai.dzusergrowth.common.api.enums.DistributionChannelEnum;
import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.*;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanCreateRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanPageQueryRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanQueryRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanCreator;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanOperator;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanSubjectField;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@MdpPigeonServer
@Slf4j
public class MerchantChannelDistributionPlanServiceImpl implements MerchantChannelDistributionPlanService {

    private static final String CAT_TYPE = MerchantChannelDistributionPlanService.class.getSimpleName();

    @Autowired
    private GeneralDealGroupSearchAcl generalDealGroupSearchAcl;

    @Autowired
    private DealProductAcl dealProductAcl;

    @Autowired
    private MerchantAuthAcl merchantAuthAcl;

    @Autowired
    private ProductCommissionBasisAcl productCommissionBasisAcl;

    @Resource
    private DistributionPlanQueryService distributionPlanQueryService;

    @Resource
    private DistributionPlanCommandService distributionPlanCommandService;

    @Resource
    private MerchantChannelDistributionPlanOperateStrategyFactory merchantChannelDistributionPlanOperateStrategyFactory;

    @Resource
    private RedisLockService redisLockService;

    /**
     * 2099年12月31日 23:59:59
     */
    public static final long END_TIME_OF_PERMANENT_EFFECTIVE = 4102415999000L;

    /**
     * 商家端展示文案
     */
    private static final String EFFECTIVE_PERMANENTLY = "永久有效";

    /**
     * 商家端展示文案
     */
    private static final String PERCENT_SIGN = "%";

    @Override
    public PaginationRemoteResponse<MerchantChannelDistributionPlanVO> pageQueryDistributionPlan(MerchantChannelDistributionPlanPageQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pageQueryDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!MerchantChannelDistributionPlanValidator.validateDistributionPlanPageQueryRequest(request, errorMsg)) {
                log.error("MerchantChannelDistributionPlanService.pageQueryDistributionPlan illegal param:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return PaginationRemoteResponse.failure(errorMsg.toString());
            }

            Optional<Long> customerIdOptional = merchantAuthAcl.getCustomerIdByAccountId(request.getAccountId());
            if (!customerIdOptional.isPresent()) {
                throw new DistributionPlanException("未查询到客户信息");
            }

            DealProductInfo dealProductInfo = null;
            if (Objects.nonNull(request.getProductId())) {
                List<DealProductInfo> dealProductInfoList = dealProductAcl.queryDpDealProductByIds(Lists.newArrayList(request.getProductId()));
                if (CollectionUtils.isEmpty(dealProductInfoList)) {
                    throw new DistributionPlanException("未查询到商品信息");
                }
                dealProductInfo = dealProductInfoList.get(0);
            }

            DistributionPlanPageQueryRequest distributionPlanPageQueryRequest = buildDistributionPlanPageQueryRequest(request, customerIdOptional.get(), dealProductInfo);
            Response<PageResult<DistributionPlanDTO>> response = distributionPlanQueryService.pageQueryDistributionPlan(distributionPlanPageQueryRequest);
            if (response.respFailed() || Objects.isNull(response.getData())) {
                throw new DistributionPlanException("查询额外佣金配置失败，请稍后重试");
            }

            if (CollectionUtils.isEmpty(response.getData().getData())) {
                Cat.logEvent("MerchantDistributionPlanEmptyPage", "MerchantChannelDistributionPlanService query empty page");
                return PaginationRemoteResponse.success(Lists.newArrayList(), response.getData().getTotalCount());
            }

            List<Long> mtProductIds = response.getData().getData().stream()
                    .filter(distributionPlan -> distributionPlan.getSubject().getSubjectType().equals(DistributionPlanSubjectTypeEnum.PRODUCT.getCode()))
                    .map(distributionPlan -> distributionPlan.getSubject().getField1())
                    .map(Long::valueOf).distinct().collect(Collectors.toList());
            Map<Long, DealProductInfo> dealProductInfoMap = dealProductAcl.queryMtDealProductToMap(mtProductIds);

            Date nowDate = new Date();
            List<MerchantChannelDistributionPlanVO> merchantChannelDistributionPlanVoList = response.getData().getData().stream()
                    .map(e -> buildMerchantChannelDistributionPlanVO(e, dealProductInfoMap, nowDate)).collect(Collectors.toList());
            transaction.setSuccessStatus();
            return PaginationRemoteResponse.success(merchantChannelDistributionPlanVoList, response.getData().getTotalCount());
        } catch (DistributionPlanException de) {
            transaction.setStatus(de);
            log.error("MerchantChannelDistributionPlanService.pageQueryDistributionPlan biz failed", de);
            return PaginationRemoteResponse.failure(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("pageQueryDistributionPlantFailed", "pageQueryDistributionPlan failed");
            log.error("MerchantChannelDistributionPlanService.pageQueryDistributionPlan failed", e);
            transaction.setStatus(e);
            return PaginationRemoteResponse.failure(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public PaginationRemoteResponse<DistributionPlanProductVO> pageQueryDistributionProduct(DistributionProductPageQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pageQueryDistributionProduct");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!MerchantChannelDistributionPlanValidator.validateDistributionProductPageQueryRequest(request, errorMsg)) {
                log.error("MerchantChannelDistributionPlanService.pageQueryDistributionProduct illegal param:{}， errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return PaginationRemoteResponse.failure(errorMsg.toString());
            }

            Optional<Long> customerIdOptional = merchantAuthAcl.getCustomerIdByAccountId(request.getAccountId());
            if (!customerIdOptional.isPresent()) {
                throw new DistributionPlanException("未查询到客户信息");
            }
            GeneralDealGroupSearchResult result = searchDealGroups(request, customerIdOptional.get());
            if (CollectionUtils.isEmpty(result.getProductIdList())) {
                Cat.logEvent("MerchantDistributionProductEmptyPage", "MerchantChannelDistributionPlanService query empty page");
                return PaginationRemoteResponse.success(Lists.newArrayList(), result.getTotalHits());
            }

            List<DealProductInfo> dealProductInfoList = dealProductAcl.queryDpDealProductByIds(result.getProductIdList());
            if (CollectionUtils.isEmpty(dealProductInfoList)) {
                return PaginationRemoteResponse.success(Lists.newArrayList(), result.getTotalHits());
            }

            List<ProductCommissionBasisInfo> commissionResults = queryProductCommissionBasisInfo(dealProductInfoList);
            if (CollectionUtils.isEmpty(commissionResults)) {
                return PaginationRemoteResponse.success(Lists.newArrayList(), result.getTotalHits());
            }

            // Map<计费基数类型，商品佣金率范围>
            Map<String, ProductCommissionRange> commissionBasisList = Lion.getMap(Environment.getAppName(),
                    "merchant.channel.distribution.plan.commission.basis.type.to.commission.range.map", ProductCommissionRange.class, Maps.newHashMap());
            if (MapUtils.isEmpty(commissionBasisList)) {
                throw new DistributionPlanException("未查询到佣金率范围配置");
            }
            Map<Long, ProductCommissionRange> dpProductId2CommissionBasisMap = getCommissionBasisByProduct(commissionResults, commissionBasisList);

            DistributionPlanQueryRequest distributionPlanQueryRequest = buildDistributionPlanQueryRequest(dealProductInfoList.stream().map(DealProductInfo::getMtProductId).collect(Collectors.toList()));
            Response<List<DistributionPlanDTO>> response = distributionPlanQueryService.queryLatestDistributionPlan(distributionPlanQueryRequest);
            if (response.respFailed()) {
                throw new DistributionPlanException("查询额外佣金配置失败，请稍后重试");
            }
            Map<Long, Boolean> mtProductId2CanConfigPlanMap = queryProductId2CanConfigPlanMap(response.getData());

            List<DistributionPlanProductVO> voList = buildDistributionPlanProductVOList(dealProductInfoList, dpProductId2CommissionBasisMap
                    , mtProductId2CanConfigPlanMap);

            transaction.setSuccessStatus();
            // 团购搜索接口，超出4998条需要使用游标搜索，固限制查询总页数
            return PaginationRemoteResponse.success(voList, result.getTotalHits() > 4998 ? 4998 : result.getTotalHits());
        } catch (DistributionPlanException de) {
            transaction.setStatus(de);
            log.error("MerchantChannelDistributionPlanService.pageQueryDistributionProduct biz failed", de);
            return PaginationRemoteResponse.failure(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("pageQueryDistributionProductFailed", "pageQueryDistributionProduct failed");
            log.error("MerchantChannelDistributionPlanService.pageQueryDistributionProduct failed", e);
            transaction.setStatus(e);
            return PaginationRemoteResponse.failure(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<CreateMerchantChannelDistributionPlanVO> createDistributionPlan(MerchantChannelDistributionPlanCreateRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "createDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!MerchantChannelDistributionPlanValidator.validateDistributionPlanCreateRequest(request, errorMsg)) {
                log.error("MerchantChannelDistributionPlanService.createDistributionPlan illegal param:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            //todo 权益点校验，后面再加，暂时别管
            Optional<Long> customerIdOptional = merchantAuthAcl.getCustomerIdByAccountId(request.getAccountId());
            if (!customerIdOptional.isPresent()) {
                throw new IllegalArgumentException("未查询到客户信息");
            }

            Date nowDate = new Date();
            if (silentPeriodValidate(nowDate)) {
                transaction.setStatus("当前时段不支持创建，请明日0点后重试");
                log.error("MerchantChannelDistributionPlanService.createDistributionPlan in silent period, param:{}, nowDate:{}", request, nowDate.getTime());
                return RemoteResponse.fail("当前时段不支持创建，请明日0点后重试");
            }

            List<DealProductInfo> dealProductInfoList = dealProductAcl.queryDpDealProductByIds(Lists.newArrayList(request.getProductId()));
            if (CollectionUtils.isEmpty(dealProductInfoList)) {
                throw new IllegalArgumentException("未查询到商品信息");
            }
            DealProductInfo dealProductInfo = dealProductInfoList.stream().findFirst().orElse(null);
            if (Objects.isNull(dealProductInfo)) {
                throw new IllegalArgumentException("未查询到商品信息");
            }
            if (!dealProductInfo.getOnlineStatus()) {
                throw new IllegalArgumentException("商品已下架");
            }
            if (!dealProductInfo.getPlatformCustomerId().equals(customerIdOptional.get())) {
                throw new IllegalArgumentException("当前商品的客户id与登陆账号的客户id不一致");
            }

            ProductCommissionBasisQueryRequest productCommissionBasisQueryRequest = ProductCommissionBasisQueryRequest.builder().dpProductId(request.getProductId()).dealPrice(dealProductInfo.getSalePrice()).build();
            List<ProductCommissionBasisInfo> commissionBasisInfoList = productCommissionBasisAcl.batchQueryProductCommissionBasis(Lists.newArrayList(productCommissionBasisQueryRequest));
            if (CollectionUtils.isEmpty(commissionBasisInfoList)) {
                throw new DistributionPlanException("提交失败，请稍后进行重试");
            }

            if (isCommissionRateOutOfRange(new BigDecimal(request.getCommissionRate()), commissionBasisInfoList.get(0))) {
                throw new IllegalArgumentException("商品佣金率超出规定范围");
            }

            BigDecimal commissionRate = new BigDecimal(request.getCommissionRate()).divide(new BigDecimal(100));
            Map<Integer, BigDecimal> commissionRateMap = Maps.newHashMap();
            commissionRateMap.put(DistributionCommissionTypeEnum.MERCHANT_CHANNEL_COMMISSION.getCode(), commissionRate);
            DistributionPlanCreateRequest distributionPlanCreateRequest = DistributionPlanCreateRequest.builder()
                    .sceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode())
                    .channel(DistributionChannelEnum.QUAN_QUAN.getCode())
                    .creator(DistributionPlanCreator.builder().creatorId(String.valueOf(customerIdOptional.get())).build())
                    .operator(DistributionPlanOperator.builder().operatorId(String.valueOf(request.getAccountId())).build())
                    .planType(DistributionPlanTypeEnum.UNIVERSAL_PLAN.getType())
                    .beginTime(DateUtils.getNextDayMorning(nowDate).getTime())
                    .endTime(END_TIME_OF_PERMANENT_EFFECTIVE)
                    .subjectField(DistributionPlanSubjectField.builder().subjectType(DistributionPlanSubjectTypeEnum.PRODUCT.getCode()).field1(String.valueOf(dealProductInfo.getMtProductId())).build())
                    .commissionRateMap(commissionRateMap).build();

            Response response = redisLockService.tryLock("CreateMerchantDistributionPlan_" + request.getProductId(),
                    () -> distributionPlanCommandService.createDistributionPlan(distributionPlanCreateRequest));
            if (response.respFailed()) {
                throw new DistributionPlanException("创建分销计划失败");
            }
            transaction.setSuccessStatus();
            return RemoteResponse.success(null);
        } catch (IllegalArgumentException ie) {
            transaction.setStatus(ie);
            log.error("MerchantChannelDistributionPlanService.createDistributionPlan illegal argument, request:{}", request, ie);
            return RemoteResponse.fail(ie.getMessage());
        } catch (DistributionPlanException de) {
            transaction.setStatus(de);
            log.error("MerchantChannelDistributionPlanService.createDistributionPlan biz failed, request:{}", request, de);
            return RemoteResponse.fail("创建额外佣金配置失败");
        } catch (Exception e) {
            Cat.logEvent("createDistributionPlanFailed", "createDistributionPlan failed");
            log.error("MerchantChannelDistributionPlanService.createDistributionPlan failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    /**
     * 静默期校验
     *
     * @param nowDate
     */
    private boolean silentPeriodValidate(Date nowDate) {
        Map<String, String> map = Lion.getMap(Environment.getAppName(), "merchant.channel.create.distribution.plan.silent.time", String.class);
        Calendar silentStartTime = DateUtils.convertStrToCalendar(map.get("silentStartTime"));
        Calendar silentEndTime = DateUtils.convertStrToCalendar(map.get("silentEndTime"));
        if (Objects.nonNull(silentStartTime) && Objects.nonNull(silentEndTime)) {
            Calendar nowCalendar = Calendar.getInstance();
            nowCalendar.setTime(nowDate);
            // 检查当前时间是否在静默期内
            if (nowCalendar.after(silentStartTime) && nowCalendar.before(silentEndTime)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public RemoteResponse<OperateMerchantChannelDistributionPlanVO> operateDistributionPlan(MerchantChannelDistributionPlanOperateRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "operateDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!MerchantChannelDistributionPlanValidator.validateDistributionPlanOperateRequest(request, errorMsg)) {
                log.error("MerchantChannelDistributionPlanService.operateDistributionPlan illegal argument:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            //todo 权益点校验，后面再加
            Optional<Long> customerIdOptional = merchantAuthAcl.getCustomerIdByAccountId(request.getAccountId());
            if (!customerIdOptional.isPresent()) {
                throw new IllegalArgumentException("未查询到客户信息");
            }

            Date nowDate = new Date();
            if (silentPeriodValidate(nowDate)) {
                transaction.setStatus("当前时段不支持变更，请明日0点后重试");
                log.error("MerchantChannelDistributionPlanService.operateDistributionPlan in silent period, param:{}, nowDate:{}", request, nowDate.getTime());
                return RemoteResponse.fail("当前时段不支持变更，请明日0点后重试");
            }

            Response<List<DistributionPlanDTO>> distributionPlanDtoResponse = distributionPlanQueryService.queryDistributionPlanByIds(Lists.newArrayList(request.getPlanId()));
            if (distributionPlanDtoResponse.respFailed()) {
                throw new IllegalArgumentException("查询额外佣金配置失败");
            }
            if (CollectionUtils.isEmpty(distributionPlanDtoResponse.getData()) || null == distributionPlanDtoResponse.getData().get(0)) {
                throw new IllegalArgumentException("未查询到额外佣金配置");
            }

            DistributionPlanDTO distributionPlanDto = distributionPlanDtoResponse.getData().get(0);
            if (!distributionPlanDto.getSceneCode().equals(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode())
                    || !distributionPlanDto.getCreator().getCreatorId().equals(String.valueOf(customerIdOptional.get()))) {
                throw new IllegalArgumentException("当前客户无权限变更该商品的额外佣金配置");
            }

            MerchantChannelDistributionPlanOperateStrategy operateStrategy = merchantChannelDistributionPlanOperateStrategyFactory.getStrategyByOperateType(MerchantChannelDistributionPlanOperateTypeEnum.fromCode(request.getOperateType()));
            if (operateStrategy == null) {
                throw new DistributionPlanException("暂不支持当前操作");
            }

            MerchantChannelDistributionPlanOperateContext context = MerchantChannelDistributionPlanOperateContext.buildContext(distributionPlanDto, request, customerIdOptional.get(), nowDate);
            operateStrategy.validatePlanCanOperate(context);
            operateStrategy.operate(context);

            transaction.setSuccessStatus();
            return RemoteResponse.success(null);
        } catch (IllegalArgumentException ie) {
            transaction.setStatus(ie);
            log.error("MerchantChannelDistributionPlanService.operateDistributionPlan illegal argument, request:{}", request, ie);
            return RemoteResponse.fail(ie.getMessage());
        } catch (DistributionPlanException de) {
            transaction.setStatus(de);
            log.error("MerchantChannelDistributionPlanService.operateDistributionPlan biz failed, request:{}", request, de);
            return RemoteResponse.fail("更新额外佣金配置失败");
        } catch (Exception e) {
            Cat.logEvent("operateDistributionPlanFailed", "operateDistributionPlan failed");
            log.error("MerchantChannelDistributionPlanService.operateDistributionPlan failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private MerchantChannelDistributionPlanVO buildMerchantChannelDistributionPlanVO(DistributionPlanDTO distributionPlan,
                                                                                     Map<Long, DealProductInfo> dealProductInfoMap,
                                                                                     Date nowDate) {
        Long mtProductId = Long.valueOf(distributionPlan.getSubject().getField1());
        DealProductInfo dealProductInfo = dealProductInfoMap.get(mtProductId);

        MerchantChannelDistributionPlanVO merchantChannelDistributionPlanVO = new MerchantChannelDistributionPlanVO();
        merchantChannelDistributionPlanVO.setPlanId(distributionPlan.getPlanId());
        merchantChannelDistributionPlanVO.setProductId(mtProductId);
        if (Objects.nonNull(dealProductInfo)) {
            merchantChannelDistributionPlanVO.setProductId(dealProductInfo.getDpProductId());
            merchantChannelDistributionPlanVO.setProductName(dealProductInfo.getProductName());
        }

        BigDecimal commissionRate = distributionPlan.getCommissionRateMap().get(DistributionCommissionTypeEnum.MERCHANT_CHANNEL_COMMISSION.getCode());
        if (Objects.nonNull(commissionRate)) {
            merchantChannelDistributionPlanVO.setCommissionRate(commissionRate.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + PERCENT_SIGN);
        }
        merchantChannelDistributionPlanVO.setBeginTime(DateUtils.format(distributionPlan.getBeginTime(), "yyyy.MM.dd"));
        merchantChannelDistributionPlanVO.setEndTime(distributionPlan.getEndTime().getTime() == END_TIME_OF_PERMANENT_EFFECTIVE ?
                EFFECTIVE_PERMANENTLY : DateUtils.format(distributionPlan.getEndTime(), "yyyy.MM.dd"));

        //已取消的计划，已取消的计划不支持任何操作
        if (distributionPlan.getEndTime().before(distributionPlan.getBeginTime())) {
            merchantChannelDistributionPlanVO.setStatus(MerchantChannelDistributionPlanStatusEnum.CANCELED.getCode());
            merchantChannelDistributionPlanVO.setEndTime(EFFECTIVE_PERMANENTLY);
            return merchantChannelDistributionPlanVO;
        }

        //待生效的计划，支持下线和修改佣金率
        if (distributionPlan.getBeginTime().getTime() >= nowDate.getTime()) {
            merchantChannelDistributionPlanVO.setStatus(MerchantChannelDistributionPlanStatusEnum.WAIT_EFFECTIVE.getCode());
            merchantChannelDistributionPlanVO.setOperableTypeList(Lists.newArrayList(MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION.getType(),
                    MerchantChannelDistributionPlanOperateTypeEnum.TERMINATE.getType()));
            return merchantChannelDistributionPlanVO;
        }

        //已结束的计划，支持重新上线
        if (distributionPlan.getEndTime().getTime() <= nowDate.getTime()) {
            merchantChannelDistributionPlanVO.setStatus(MerchantChannelDistributionPlanStatusEnum.FINISHED.getCode());
            merchantChannelDistributionPlanVO.setOperableTypeList(Lists.newArrayList(MerchantChannelDistributionPlanOperateTypeEnum.RECREATE.getType()));
            return merchantChannelDistributionPlanVO;
        }

        //生效中的计划，只有未来时间为2099-12-31 23:59:59的时候才可操作，不然说明本身已经被修改过，不允许任何操作
        merchantChannelDistributionPlanVO.setStatus(MerchantChannelDistributionPlanStatusEnum.EXECUTING.getCode());
        if (distributionPlan.getEndTime().getTime() == END_TIME_OF_PERMANENT_EFFECTIVE) {
            merchantChannelDistributionPlanVO.setOperableTypeList(Lists.newArrayList(MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION.getType(),
                    MerchantChannelDistributionPlanOperateTypeEnum.TERMINATE.getType()));
        }
        return merchantChannelDistributionPlanVO;
    }

    private DistributionPlanPageQueryRequest buildDistributionPlanPageQueryRequest(MerchantChannelDistributionPlanPageQueryRequest request,
                                                                                   Long mtCustomerId, DealProductInfo dealProductInfo) {
        DistributionPlanPageQueryRequest distributionPlanPageQueryRequest = DistributionPlanPageQueryRequest.builder()
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .sceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode())
                .channelList(Lists.newArrayList(DistributionChannelEnum.QUAN_QUAN.getCode()))
                .creator(DistributionPlanCreator.builder()
                        .creatorId(String.valueOf(mtCustomerId)).build())
                .build();
        if (Objects.nonNull(dealProductInfo)) {
            distributionPlanPageQueryRequest.setSubjectFieldList(Lists.newArrayList(DistributionPlanSubjectField.builder()
                    .subjectType(DistributionPlanSubjectTypeEnum.PRODUCT.getCode())
                    .field1(String.valueOf(dealProductInfo.getMtProductId())).build()));
        }
        if (null != request.getStatus()) {
            if (request.getStatus() == MerchantChannelDistributionPlanStatusEnum.EXECUTING.getCode()) {
                distributionPlanPageQueryRequest.setEffectivePeriodType(DistributionPlanEffectivePeriodTypeEnum.CURRENT_PERIOD.getCode());
            } else if (request.getStatus() == MerchantChannelDistributionPlanStatusEnum.FINISHED.getCode()) {
                distributionPlanPageQueryRequest.setEffectivePeriodType(DistributionPlanEffectivePeriodTypeEnum.PAST_PERIOD.getCode());
            } else if (request.getStatus() == MerchantChannelDistributionPlanStatusEnum.WAIT_EFFECTIVE.getCode()) {
                distributionPlanPageQueryRequest.setEffectivePeriodType(DistributionPlanEffectivePeriodTypeEnum.FUTURE_PERIOD.getCode());
            }
        }

        return distributionPlanPageQueryRequest;
    }

    private Map<Long, Boolean> queryProductId2CanConfigPlanMap(List<DistributionPlanDTO> distributionPlanList) {
        if (CollectionUtils.isEmpty(distributionPlanList)) {
            return Maps.newHashMap();
        }

        Date nowDate = new Date();
        Date nowDateNight = DateUtils.getToDayNight(nowDate);
        Map<Long, Boolean> productId2CanConfigPlanMap = Maps.newHashMap();
        distributionPlanList.forEach(distributionPlan -> {
            Long productId = Long.parseLong(distributionPlan.getSubject().getField1());
            if (distributionPlan.getEndTime().before(distributionPlan.getBeginTime())) {
                productId2CanConfigPlanMap.put(productId, true);
                return;
            }
            if (distributionPlan.getEndTime().getTime() <= nowDateNight.getTime()) {
                productId2CanConfigPlanMap.put(productId, true);
                return;
            }


            productId2CanConfigPlanMap.put(productId, false);
        });

        return productId2CanConfigPlanMap;
    }

    private DistributionPlanQueryRequest buildDistributionPlanQueryRequest(List<Long> productIdList) {
        DistributionPlanQueryRequest distributionPlanQueryRequest = DistributionPlanQueryRequest.builder()
                .sceneCode(DistributionPlanSceneCodeEnum.MERCHANT_SUBSIDY_CHANNEL.getCode())
                .channel(DistributionChannelEnum.QUAN_QUAN.getCode())
                .build();

        List<DistributionPlanSubjectField> subjectFieldList = Lists.newArrayList();
        productIdList.forEach(productId -> {
            DistributionPlanSubjectField subjectField = DistributionPlanSubjectField.builder()
                    .subjectType(DistributionPlanSubjectTypeEnum.PRODUCT.getCode())
                    .field1(String.valueOf(productId))
                    .build();
            subjectFieldList.add(subjectField);
        });

        distributionPlanQueryRequest.setSubjectFieldList(subjectFieldList);
        return distributionPlanQueryRequest;
    }

    /**
     *
     * @param commissionRate      佣金率
     * @param commissionBasisInfo 计费基数信息
     * @return 是否超出范围内  false 没有超出 true 超出
     */
    public static boolean isCommissionRateOutOfRange(BigDecimal commissionRate,
                                               ProductCommissionBasisInfo commissionBasisInfo) {
        //佣金率上下限配置
        Map<String, ProductCommissionRange> commissionBasisType2RangeMap = Lion.getMap(Environment.getAppName(),
                "merchant.channel.distribution.plan.commission.basis.type.to.commission.range.map", ProductCommissionRange.class);
        if (MapUtils.isEmpty(commissionBasisType2RangeMap)) {
            throw new DistributionPlanException("佣金率上下限配置未找到");
        }
        ProductCommissionRange productCommissionRange = commissionBasisType2RangeMap.get(String.valueOf(commissionBasisInfo.getCommissionBasisType().getType()));
        if (productCommissionRange == null) {
            throw new DistributionPlanException("未找到对应计费基数的佣金率上下限配置");
        }
        return commissionRate.compareTo(new BigDecimal(productCommissionRange.getMinCommissionValue())) <= 0
                || commissionRate.compareTo(new BigDecimal(productCommissionRange.getMaxCommissionValue())) > 0;
    }

    /**
     * 根据商品ID获取佣金基数
     *
     * @param commissionResults   是否收佣结果
     * @param commissionBasisList 佣金基数集
     * @return 商品和佣金的映射 key：商品ID value：佣金基数
     */
    private Map<Long, ProductCommissionRange> getCommissionBasisByProduct(List<ProductCommissionBasisInfo> commissionResults,
                                                                          Map<String, ProductCommissionRange> commissionBasisList) {
        return commissionResults.stream()
                .map(commissionResult -> {
                    if (commissionResult.getCommissionBasisType() == null) {
                        // 如果CommissionBasisType为空，直接返回productId对应的Entry，CommissionBasis为null
                        return new AbstractMap.SimpleEntry<>(commissionResult.getDpProductId(), ProductCommissionRange.builder().build());
                    } else {
                        // 查找匹配的CommissionBasis
                        ProductCommissionRange productCommissionRange = commissionBasisList.get(String.valueOf(commissionResult.getCommissionBasisType().getType()));
                        if (Objects.isNull(productCommissionRange)) {
                            return new AbstractMap.SimpleEntry<>(commissionResult.getDpProductId(), ProductCommissionRange.builder().build());
                        }
                        // 如果找到匹配的CommissionBasis，返回对应的Entry；否则，返回CommissionBasis为null的Entry
                        return new AbstractMap.SimpleEntry<>(commissionResult.getDpProductId(), productCommissionRange);
                    }
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 查询团购商品
     *
     * @param request    入参
     * @param customerId 客户ID
     * @return 查询结果
     */
    private GeneralDealGroupSearchResult searchDealGroups(DistributionProductPageQueryRequest request, Long customerId) {
        DealGroupSearchRequest searchRequest = DealGroupSearchRequest.builder()
                .dpShopId(request.getDpShopId())
                .productId(request.getProductId())
                .productName(request.getProductName())
                .customerId(customerId)
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize()).build();
        return generalDealGroupSearchAcl.searchDpDealGroups(searchRequest);
    }

    /**
     * 查询商品佣金标识
     *
     * @param dealProductInfoList 商品信息
     * @return 商品佣金结果
     */
    private List<ProductCommissionBasisInfo> queryProductCommissionBasisInfo(List<DealProductInfo> dealProductInfoList) {
        List<ProductCommissionBasisQueryRequest> productCommissionReqList = dealProductInfoList.stream().map(dealProductInfo ->
                        ProductCommissionBasisQueryRequest.builder()
                                .dpProductId(dealProductInfo.getDpProductId())
                                .dealPrice(dealProductInfo.getSalePrice()).build())
                .collect(Collectors.toList());
        return productCommissionBasisAcl.batchQueryProductCommissionBasis(productCommissionReqList);
    }

    /**
     * 构建分销商品
     *
     * @param dealProductInfoList 商品信息
     * @param dpProductId2CommissionBasisMap  商品佣金
     * @param mtProductId2CanConfigPlanMap 商品是否可配置分销
     * @return 分销商品集
     */
    private List<DistributionPlanProductVO> buildDistributionPlanProductVOList(List<DealProductInfo> dealProductInfoList
            , Map<Long, ProductCommissionRange> dpProductId2CommissionBasisMap, Map<Long, Boolean> mtProductId2CanConfigPlanMap) {
        return dealProductInfoList.stream().map(dealProductInfo -> {
            DistributionPlanProductVO.DistributionPlanProductVOBuilder builder = DistributionPlanProductVO.builder()
                    .productId(dealProductInfo.getDpProductId())
                    .productName(dealProductInfo.getProductName())
                    .productPic(dealProductInfo.getPicPath())
                    .priceText(dealProductInfo.getSalePrice());
            Boolean flag = mtProductId2CanConfigPlanMap.get(dealProductInfo.getMtProductId());
            if (Objects.nonNull(flag) && Boolean.FALSE.equals(flag)) {
                builder.canSelected(false);
                builder.tips("该商品已配置额外佣金");
            } else {
                builder.canSelected(true);
            }

            ProductCommissionRange productCommissionRange = dpProductId2CommissionBasisMap.get(dealProductInfo.getDpProductId());
            if (Objects.nonNull(productCommissionRange)) {
                builder.minCommissionValue(productCommissionRange.getMinCommissionValue())
                        .maxCommissionValue(productCommissionRange.getMaxCommissionValue());
            }
            return builder.build();
        }).collect(Collectors.toList());
    }
}