package com.sankuai.carnation.distribution.distributionplan.domain;

import com.sankuai.carnation.distribution.distributionplan.domain.model.*;
import com.sankuai.carnation.distribution.distributionplan.dto.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface DistributionPlanDoService {

    /**
     * 添加分销计划
     * @param request
     * @return
     */
    Long addDistributionPlan(DistributionPlanAddRequest request);

    /**
     * 编辑分销计划
     * @param request
     * @return
     */
    boolean editDistributionPlan(DistributionPlanEditRequest request);

    /**
     * 操作分销计划
     * @param request
     * @return
     */
    boolean operateDistributionPlan(DistributionPlanOperateRequest request);

    /**
     * 更新分销计划状态
     * @param request
     * @return
     */
    boolean updateDistributionPlanStatus(DistributionPlanStatusUpdateRequest request);

    /**
     * 编辑分销计划
     * @param planId
     * @return
     */
    Optional<DistributionPlanDO> loadDistributionPlan(Long planId);

    /**
     * 查询分销计划
     * @param planIds
     * @return
     */
    Map<Long, DistributionPlanDO> queryDistributionPlanByIds(List<Long> planIds);

    /**
     * 分页查询分销计划
     * @param pageQuery
     * @return
     */
    PageResult<DistributionPlanDO> pageQueryDistributionPlan(DistributionPlanPageQuery pageQuery);

    /**
     * 批量查询分销计划列表
     * @param request
     * @return
     */
    List<DistributionPlanDO> batchQueryDistributionPlans(DistributionPlanQueryRequest request);
}
