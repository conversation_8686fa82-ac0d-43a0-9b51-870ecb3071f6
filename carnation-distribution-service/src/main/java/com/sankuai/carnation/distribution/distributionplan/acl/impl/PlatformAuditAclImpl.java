package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.gb.audit.platform.biz.longtypeservice.ProcessService;
import com.dianping.tp.audit.dto.HistoryDTO;
import com.sankuai.carnation.distribution.distributionplan.acl.PlatformAuditAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class PlatformAuditAclImpl implements PlatformAuditAcl {

    private static final String CAT_TYPE = PlatformAuditAcl.class.getSimpleName();

    private static final Integer PROCESS_TYPE = 3010;

    @Autowired
    private ProcessService processService;

    @Override
    public List<HistoryDTO> fetchHistory(Long subjectId) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "_fetchHistory");
        try {
            if (subjectId == null) {
                throw new RuntimeException("获取审批历史失败, subjectId为空");
            }
            List<HistoryDTO> historyDTOS = processService.fetchHistory(PROCESS_TYPE, subjectId);
            if (CollectionUtils.isEmpty(historyDTOS)) {
                throw new RuntimeException("获取审批历史失败, 结果为空");
            }
            log.info("获取审批历史成功, subjectId : {}, result : {}", subjectId, historyDTOS);
            return historyDTOS;
        } catch (Exception e) {
            log.error("{}, fetchHistory failed!, subjectId : {}", CAT_TYPE, subjectId, e);
            Cat.logError(e);
            return null;
        } finally {
            transaction.complete();
        }
    }
}
