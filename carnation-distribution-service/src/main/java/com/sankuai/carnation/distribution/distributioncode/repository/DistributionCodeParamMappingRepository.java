package com.sankuai.carnation.distribution.distributioncode.repository;

import com.sankuai.carnation.distribution.distributioncode.domain.model.DistributionCodeParamMappingDO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface DistributionCodeParamMappingRepository {

    /**
     * 添加分销码参数映射
     * @param distributionCodeParamMappingDo
     * @return
     */
    boolean addDistributionCodeParamMapping(DistributionCodeParamMappingDO distributionCodeParamMappingDo);

    /**
     * 基于分销码的参数查询分销码
     * @param distributionCodeParamMappingDo
     * @return
     */
    Optional<DistributionCodeParamMappingDO> loadDistributionCodeByBizParams(DistributionCodeParamMappingDO distributionCodeParamMappingDo);

    /**
     * 基于渠道、分销码查询分销码
     * @param channel
     * @param distributionCode
     * @return
     */
    DistributionCodeParamMappingDO loadByChannelAndDistributionCode(DistributionBusinessChannelEnum channel, String distributionCode);

    /**
     * 基于渠道、分销码查询分销码
     * @param channel
     * @param distributionCodes
     * @return
     */
    List<DistributionCodeParamMappingDO> queryByChannelAndDistributionCodes(DistributionBusinessChannelEnum channel, List<String> distributionCodes);
}
