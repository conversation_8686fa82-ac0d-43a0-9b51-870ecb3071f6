package com.sankuai.carnation.distribution.distributionplan.service.model;

import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanOperateRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MerchantChannelDistributionPlanOperateContext {

    /**
     * 当前变更的分销计划
     */
    private DistributionPlanDTO distributionPlan;

    /**
     * 点评客户id
     */
    private Long mtCustomerId;

    /**
     * 操作日期
     */
    private Date operateDate;

    /**
     * 点评账号id
     */
    private Long mtAccountId;

    /**
     * 新的佣金率，百分位
     */
    private BigDecimal commissionRate;

    public static MerchantChannelDistributionPlanOperateContext buildContext(DistributionPlanDTO distributionPlanDto,
                                                                             MerchantChannelDistributionPlanOperateRequest request,
                                                                             Long customerId,
                                                                             Date operateDate) {
        MerchantChannelDistributionPlanOperateContext context = new MerchantChannelDistributionPlanOperateContext();
        if (Objects.nonNull(request.getCommissionRate())){
            BigDecimal commissionRate = new BigDecimal(request.getCommissionRate()).divide(new BigDecimal(100));
            context.setCommissionRate(commissionRate);
        }
        context.setMtAccountId(request.getAccountId());
        context.setOperateDate(operateDate);
        context.setMtCustomerId(customerId);
        context.setDistributionPlan(distributionPlanDto);
        return context;
    }
}
