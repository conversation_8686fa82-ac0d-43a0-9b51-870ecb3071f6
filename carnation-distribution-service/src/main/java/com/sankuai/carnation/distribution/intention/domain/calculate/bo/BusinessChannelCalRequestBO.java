package com.sankuai.carnation.distribution.intention.domain.calculate.bo;

import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import lombok.Data;

/**
 * 功能描述: 计算任务所属渠道计算请求
 *
 * <AUTHOR>
 * @date 2022/06/16
 **/
@Data
public class BusinessChannelCalRequestBO {

    /**
     * 订单类型
     * @see DistributionOrderTypeEnum
     */
    private int orderType;

    private String orderId;

    /**
     * 分销码
     */
    private String code;

    /**
     * 商品类型
     * @see com.sankuai.carnation.distribution.common.enums.ProductTypeEnum
     */
    private int productType;
}
