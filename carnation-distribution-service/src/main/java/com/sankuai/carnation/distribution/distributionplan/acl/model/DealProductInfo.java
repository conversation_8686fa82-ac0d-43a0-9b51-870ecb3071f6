package com.sankuai.carnation.distribution.distributionplan.acl.model;

import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.distributionplan.domain.impl.DistributionProductPushService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.PushableKuaiShouDealProductCategory;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DealProductInfo {

    private Long dpProductId;

    /**
     * 团购商品id
     */
    private Long mtProductId;

    /**
     * 商品二级类目id
     */
    private Long categoryId;

    /**
     * 商品叶子类目id
     */
    private Long leafCategoryId;

    /**
     * 团购商品名称
     */
    private String productName;

    /**
     * 是否在线
     * 目前1或者2就认为在线，否则均认为不在线
     * @see com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum
     */
    private Boolean onlineStatus;

    /**
     * 团购使用美团门店id列表
     */
    private List<Long> applyMtShopIds;
    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 商家设置结束售卖时间
     */
    private String endSaleDate;

    /**
     * 团单售价
     */
    private String salePrice;

    /**
     * 团单原价（市场价）
     */
    private String marketPrice;

    /**
     * 团单图片
     */
    private String picPath;

    /**
     * 平台客户id
     */
    private Long platformCustomerId;

    public boolean validateCategory(Map<String, PushableKuaiShouDealProductCategory> pushableKuaiShouDealProductCategoryMap) {
        if (this.categoryId == null) {
            Cat.logEvent("NullProductCategory", "product category is null!");
            return false;
        }

        PushableKuaiShouDealProductCategory validateConfig = pushableKuaiShouDealProductCategoryMap.get(String.valueOf(this.categoryId));
        if (validateConfig == null) {
            Cat.logEvent("ProductCategoryConfigNotFound", "product category is not config in lion config");
            return false;
        }

        if (CollectionUtils.isEmpty(validateConfig.getLeafCategoryIds())) {
            return true;
        }

        //商品二级类目配置了部分叶子类目可推送的场景下，商品叶子类目为null等价于0
        Long leafCategoryId = this.leafCategoryId == null ? DistributionProductPushService.NULL_PRODUCT_LEAF_CATEGORY_ID :  this.leafCategoryId;
        return validateConfig.getLeafCategoryIds().contains(leafCategoryId);
    }
}
