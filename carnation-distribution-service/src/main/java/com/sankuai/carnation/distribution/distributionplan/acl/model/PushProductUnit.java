package com.sankuai.carnation.distribution.distributionplan.acl.model;

import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPush;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PushProductUnit {

    private Long mtProductId;

    private ProductTypeEnum productType;

    private DistributionBusinessChannelEnum channel;

    public static PushProductUnit buildByDistributionProductPush(DistributionProductPush distributionProduct) {
        return PushProductUnit.builder()
                .mtProductId(distributionProduct.getMtProductId())
                .productType(distributionProduct.getProductType())
                .channel(distributionProduct.getChannel()).build();
    }

    public static PushProductUnit buildByDistributionPlanDo(DistributionPlanDO distributionPlanDo) {
        return PushProductUnit.builder()
                .mtProductId(distributionPlanDo.getProductId())
                .productType(distributionPlanDo.getProductType())
                .channel(distributionPlanDo.getChannel()).build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PushProductUnit)) {
            return false;
        }
        PushProductUnit that = (PushProductUnit) o;
        return Objects.equals(getMtProductId(), that.getMtProductId()) &&
                getProductType() == that.getProductType() &&
                getChannel() == that.getChannel();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getMtProductId(), getProductType(), getChannel());
    }
}
