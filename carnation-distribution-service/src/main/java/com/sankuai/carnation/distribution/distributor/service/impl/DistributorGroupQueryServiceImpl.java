package com.sankuai.carnation.distribution.distributor.service.impl;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.medicine.common.exception.utils.ExceptionUtils;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.appication.DistributorAppService;
import com.sankuai.carnation.distribution.distributor.assembler.DistributorVOAssembler;
import com.sankuai.carnation.distribution.distributor.domain.DistributorChannelRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.domain.exception.DistributorException;
import com.sankuai.carnation.distribution.distributor.domain.utils.DistributorBeanTransfer;
import com.sankuai.carnation.distribution.distributor.domain.utils.DistributorGroupBeanTransfer;
import com.sankuai.carnation.distribution.distributor.dto.DistributorDTO;
import com.sankuai.carnation.distribution.distributor.dto.DistributorGroupDTO;
import com.sankuai.carnation.distribution.distributor.dto.request.PageQueryDistributorRequest;
import com.sankuai.carnation.distribution.distributor.model.DistributorBindModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.distributor.service.DistributorGroupQueryService;
import com.sankuai.carnation.distribution.distributor.vo.ApplyDistributorVO;
import com.sankuai.carnation.distribution.distributor.vo.DistributorVO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.BizSceneException;
import com.sankuai.carnation.distribution.privatelive.distribution.startar.aop.RespCatProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 功能描述: 分销商查询服务
 *
 * <AUTHOR>
 * @date 2021/12/24
 **/
@Service
@Slf4j
public class DistributorGroupQueryServiceImpl implements DistributorGroupQueryService {

    @Autowired
    private DistributorGroupRootService groupRootService;

    @Autowired
    private DistributorRootService distributorRootService;

    @Autowired
    private DistributorChannelRootService channelRootService;

    @Resource
    private DistributorAppService distributorAppService;

    @Resource
    private DistributorVOAssembler distributorVOAssembler;


    @Override
    public RemoteResponse<DistributorGroupDTO> getDistributorGroup(String groupCode) {
        try {
            DistributorGroupBO groupBO = groupRootService.getDistributorGroup(groupCode);
            return RemoteResponse.success(DistributorGroupBeanTransfer.bo2dto(groupBO));
        } catch (DistributorException distributorException) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getDistributorGroup", distributorException, groupCode);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getDistributorGroup", e, groupCode);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Override
    public RemoteResponse<DistributorGroupDTO> getDistributorGroup(int groupId) {
        try {
            DistributorGroupBO groupBO = groupRootService.getDistributorGroup(groupId);
            return RemoteResponse.success(DistributorGroupBeanTransfer.bo2dto(groupBO));
        } catch (DistributorException distributorException) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getDistributorGroup", distributorException, groupId);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getDistributorGroup", e, groupId);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Override
    public RemoteResponse<DistributorGroupDTO> getDistributorGroup(int userType, long userId) {
        try {
            DistributorGroupBO groupBO = groupRootService.getDistributorGroup(userType, userId);
            return RemoteResponse.success(DistributorGroupBeanTransfer.bo2dto(groupBO));
        } catch (DistributorException distributorException) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getDistributorGroup", distributorException, userType, userId);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getDistributorGroup", e, userType, userId);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Override
    public RemoteResponse<DistributorGroupDTO> getValidDistributorGroup(String groupCode) {
        RemoteResponse<DistributorGroupDTO> response = getDistributorGroup(groupCode);
        if (!response.isSuccess()) {
            return response;
        }
        return RemoteResponse.success(Optional.of(response)
                .map(RemoteResponse::getData)
                .filter(dto -> dto.getStatus() == DistributionStatusEnum.VALID.getCode())
                .orElse(null)
        );
    }

    @Override
    public RemoteResponse<DistributorGroupDTO> getValidDistributorGroup(int groupId) {
        RemoteResponse<DistributorGroupDTO> response = getDistributorGroup(groupId);
        if (!response.isSuccess()) {
            return response;
        }
        return RemoteResponse.success(Optional.of(response)
                .map(RemoteResponse::getData)
                .filter(dto -> dto.getStatus() == DistributionStatusEnum.VALID.getCode())
                .orElse(null)
        );
    }

    @Override
    public RemoteResponse<DistributorGroupDTO> getValidDistributorGroup(int userType, long userId) {
        RemoteResponse<DistributorGroupDTO> response = getDistributorGroup(userType, userId);
        if (!response.isSuccess()) {
            return response;
        }
        return RemoteResponse.success(Optional.of(response)
                .map(RemoteResponse::getData)
                .filter(dto -> dto.getStatus() == DistributionStatusEnum.VALID.getCode())
                .orElse(null)
        );
    }

    @Override
    public RemoteResponse<Map<Integer, DistributorGroupDTO>> batchGetDistributorGroupById(List<Integer> groupIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl.batchGetDistributorGroupById(java.util.List)");
        try {
            Map<Integer, DistributorGroupBO> boMap = groupRootService.batchGetDistributorGroupById(groupIdList);
            Map<Integer, DistributorGroupDTO> dtoMap = boMap.values().stream()
                    .map(DistributorGroupBeanTransfer::bo2dto)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            DistributorGroupDTO::getGroupId,
                            Function.identity(),
                            (ov, nv) -> nv
                    ));
            return RemoteResponse.success(dtoMap);
        } catch (DistributorException distributorException) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".batchGetDistributorGroupById", distributorException, groupIdList);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".batchGetDistributorGroupById", e, groupIdList);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Deprecated
    @Override
    public RemoteResponse<Map<Long, DistributorGroupDTO>> batchGetDistributorGroupByUser(int userType, List<Long> userIdList) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl.batchGetDistributorGroupByUser(int,java.util.List)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Deprecated
    @Override
    public RemoteResponse<Map<Integer, DistributorGroupDTO>> batchGetValidDistributorGroupById(List<Integer> groupIdList) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl.batchGetValidDistributorGroupById(java.util.List)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Deprecated
    @Override
    public RemoteResponse<Map<Long, DistributorGroupDTO>> batchGetValidDistributorGroupByUser(int userType, List<Long> userIdList) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl.batchGetValidDistributorGroupByUser(int,java.util.List)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Override
    public RemoteResponse<List<DistributorGroupDTO>> getGroupByChannelCode(String channelCode, int offset, int limit) {
        if (StringUtils.isBlank(channelCode) || limit <= 0) {
            return RemoteResponse.success(Lists.newArrayList());
        }
        if (offset < 0) {
            offset = 0;
        }
        try {
            List<DistributionChannelBO> channelList = channelRootService.getChannelByChannelCode(channelCode);
            int channelId = channelList.stream()
                    .filter(channel -> channel.getStatus().equals(DistributionStatusEnum.VALID.getCode()))
                    .map(DistributionChannelBO::getChannelId)
                    .findFirst()
                    .orElse(0);
            if (channelId <= 0) {
                return RemoteResponse.success(Lists.newArrayList());
            }
            List<DistributorGroupBO> groupList = groupRootService.getGroupByChannel(channelId, offset, limit);
            return RemoteResponse.success(groupList.stream()
                    .map(DistributorGroupBeanTransfer::bo2dto)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            );
        } catch (DistributorException distributorException) {
            log.error("[{}.getGroupByChannelCode] channelCode: {}, offset: {}, limit: {}", getClass().getSimpleName(), channelCode, offset, limit, distributorException);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            log.error("[{}.getGroupByChannelCode] channelCode: {}, offset: {}, limit: {}", getClass().getSimpleName(), channelCode, offset, limit, e);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Deprecated
    @Override
    public RemoteResponse<Integer> getGroupMemberCount(String groupCode) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl.getGroupMemberCount(java.lang.String)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Override
    public RemoteResponse<Integer> getGroupMemberCount(int groupId) {
        try {
            int groupMemberCount = groupRootService.getGroupMemberCount(groupId);
            return RemoteResponse.success(groupMemberCount);
        } catch (DistributorException distributorException) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getGroupMemberCount", distributorException, groupId);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getGroupMemberCount", e, groupId);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Deprecated
    @Override
    public RemoteResponse<List<DistributorDTO>> getGroupMemberList(String groupCode, int offset, int limit) {
        Cat.logEvent("INVALID_INTERFACE", "com.sankuai.carnation.distribution.distributor.service.impl.DistributorGroupQueryServiceImpl.getGroupMemberList(java.lang.String,int,int)");
        // 无效代码清理
        throw new RuntimeException("此无效方法已被清理下线");
    }

    @Override
    public RemoteResponse<List<DistributorDTO>> getGroupMemberList(int groupId, int offset, int limit) {
        try {
            List<Long> distributorIdList = groupRootService.getGroupMemberList(groupId, offset, limit);
            List<DistributorDTO> dtoList = getDistributorDTOList(distributorIdList);
            return RemoteResponse.success(dtoList);
        } catch (DistributorException distributorException) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getGroupMemberList", distributorException, groupId, offset, limit);
            return RemoteResponse.fail(distributorException.getMessage());
        } catch (Exception e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getGroupMemberList", e, groupId, offset, limit);
            return RemoteResponse.fail("服务器错误");
        }
    }

    @Override
    @RespCatProcess
    public RemoteResponse<PageDataDTO<ApplyDistributorVO>> getGroupMemberList(PageQueryDistributorRequest pageQueryDistributorRequest) {
        validatePageQueryDistributorRequest(pageQueryDistributorRequest);
        PageDataDTO<DistributorBindModel> distributorVOPageDataDTO = distributorAppService.pageQueryDistributor(pageQueryDistributorRequest);
        return RemoteResponse.success(PageDataDTO.<ApplyDistributorVO>builder()
                        .pageInfoDTO(distributorVOPageDataDTO.getPageInfoDTO())
                        .list(distributorVOAssembler.toApplyDistributorVOList(distributorVOPageDataDTO.getList()))
                .build());
    }

    private void validatePageQueryDistributorRequest(PageQueryDistributorRequest pageQueryDistributorRequest) {
        if(ObjectUtils.isEmpty(pageQueryDistributorRequest.getDistributorGroupId())) {
            throw new BizSceneException("团长ID不能为空");
        }
        if(ObjectUtils.isEmpty(pageQueryDistributorRequest.getPageNum()) || pageQueryDistributorRequest.getPageNum() <= 0) {
            throw new BizSceneException("页码不能为空");
        }
        if(ObjectUtils.isEmpty(pageQueryDistributorRequest.getPageSize()) || pageQueryDistributorRequest.getPageSize() <= 0) {
            throw new BizSceneException("每页数量不能为空");
        }
        if(pageQueryDistributorRequest.getPageSize() > 20) {
            throw new BizSceneException("每页数量不能大于20");
        }
    }

    private List<DistributorDTO> getDistributorDTOList(List<Long> distributorIdList) {
        Map<Long, DistributorBO> distributorBOMap = distributorRootService.batchGetDistributorById(distributorIdList);
        return distributorIdList.stream()
                .map(distributorBOMap::get)
                .filter(Objects::nonNull)
                .map(DistributorBeanTransfer::bo2dto)
                .collect(Collectors.toList());
    }
}
