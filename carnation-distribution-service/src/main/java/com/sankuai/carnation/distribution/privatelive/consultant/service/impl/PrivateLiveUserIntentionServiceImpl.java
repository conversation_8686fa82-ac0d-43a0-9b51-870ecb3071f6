package com.sankuai.carnation.distribution.privatelive.consultant.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.common.codec.json.SimpleJacksonUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.beauty.user.dto.UserDto;
import com.meituan.medicine.common.exception.utils.ExceptionUtils;
import com.meituan.nibscp.unity.migration.distributed.proxy.annotation.DistributedProxy;
import com.meituan.nibscp.unity.migration.distributed.proxy.enums.FlowType;
import com.meituan.nibscp.unity.migration.framework.annation.FlowControl;
import com.sankuai.carnation.distribution.common.acl.UserAclService;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.appication.DistributorAppService;
import com.sankuai.carnation.distribution.distributor.appication.DistributorGroupAppService;
import com.sankuai.carnation.distribution.distributor.model.DistributorGroupModel;
import com.sankuai.carnation.distribution.distributor.model.DistributorModel;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.privatelive.account.enums.PrivateLiveTaskTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.CommunityConsultantUseRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.product.ProductAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.wechat.WechatUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.bo.ConsultantUserStatisticBO;
import com.sankuai.carnation.distribution.privatelive.consultant.bo.ProductBasicsInfoBO;
import com.sankuai.carnation.distribution.privatelive.consultant.converter.ProductBasicsInfoBOConverter;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.group.ConsultantBindGroupInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.group.GroupTraceBindConsultantDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.group.GroupUserActionInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.group.GroupUserInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.intention.modify.ConsultantBatchChangeProducer;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.intention.modify.PrivateLiveIntentionModifyDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionDomainService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.user.PrivateLiveUserIntentionQueryRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.sourceboard.*;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.*;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.PrivateLiveIntentionException;
import com.sankuai.carnation.distribution.privatelive.consultant.migrate.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.*;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.*;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantAccountService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveUserIntentionService;
import com.sankuai.carnation.distribution.utils.MobileTokenUtil;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteRelationInfoDTO;
import com.sankuai.dz.srcm.pchat.dto.activity.MemberInviteWxUserInfo;
import com.sankuai.dz.srcm.pchat.enums.activity.MemberInviteRelationType;
import com.sankuai.dz.srcm.privatelive.community.dto.*;
import com.sankuai.dz.srcm.user.dto.ConsultantBasicDataDTO;
import com.sankuai.dz.srcm.user.dto.ConsultantBasicDataRequest;
import com.sankuai.dz.srcm.user.dto.ConsultantChangeRequest;
import com.sankuai.dz.srcm.user.enums.PrivateSphereUserTagEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.miniprogram.WechatUserInfoDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.saasconfig.TraceAbilityTypeEnum;
import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveUserIntentionService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO.getTotalPageCountByTotalCountAndPageSize;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/12/29
 * @Description:
 */
@Service
@Slf4j
public class PrivateLiveUserIntentionServiceImpl implements PrivateLiveUserIntentionService {

    private final String CAT_TYPE = PrivateLiveUserIntentionService.class.getSimpleName();

    @Autowired
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Autowired
    private PrivateLiveGroupUserBehaviorLogRepository groupUserBehaviorLogRepository;

    @Autowired
    private PrivateLiveIntentionExpireTimeRepository intentionExpireTimeRepository;

    @Autowired
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Autowired
    private PrivateLiveUserIntentionLogRepository privateLiveUserIntentionLogRepository;
    @Autowired
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @Autowired
    private PrivateLiveConsultantAccountService consultantAccountService;

    @Autowired
    private PrivateLiveDistributorCodeService distributorCodeService;

    @Autowired
    private PrivateLiveIntentionExpireServiceImpl privateLiveIntentionExpireService;

    @Autowired
    private WechatUserAclService wechatUserAclService;

    @Autowired
    private PrivateLiveConsultantVerifyService privateLiveConsultantVerifyService;
    @Autowired
    private UserAclService userAclService;

    @Autowired
    private PrivateLiveConsultantSummaryRepository privateLiveConsultantSummaryRepository;
    @Autowired
    private PrivateSphereUserAclService privateSphereUserAclService;
    @Autowired
    private RpcUserRetrieveService.Iface userRetrieveService;

    @Autowired
    private PrivateLiveUserIntentionDomainService userIntentionDomainService;

    @Autowired
    private PrivateLiveIntentionModifyDomainService modifyDomainService;

    @Autowired
    private MobileTokenUtil mobileTokenUtil;

    @Autowired
    private ProductAclService productAclService;

    @Autowired
    private PrivateLiveAclService privateLiveAclService;

    @Autowired
    private ConsultantBatchChangeProducer consultantBatchChangeProducer;

    @Autowired
    private DistributorGroupAppService distributorGroupAppService;

    @Autowired
    private DistributorAppService distributorAppService;
    @Autowired
    private DzPrivateLiveUserIntentionService dzPrivateLiveUserIntentionService;

    public static final ThreadPoolExecutor LIVE_INTENTION_STATISTIC_POOL = Rhino.newThreadPool("live_intention_statistic_pool",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(50)).getExecutor();

    public static final ThreadPoolExecutor LIVE_INTENTION_PRODUCT_POOL = Rhino.newThreadPool("live_intention_product_pool",
            DefaultThreadPoolProperties.Setter().withCoreSize(4).withMaxSize(8)).getExecutor();

    public static final ThreadPoolExecutor LIVE_CONSULTANT_QUERY_POOL = Rhino.newThreadPool("live_consultant_pool",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(50).withMaxQueueSize(200)).getExecutor();

    private static final ThreadPoolExecutor BATCH_QUERY_THREADPOOL = Rhino.newThreadPool("userinfo-batch-query",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(50)).getExecutor();


    public void initFromOrderOrFromIntentionCode(long userId, String distributorCode, String liveId, long consultantTaskId, Integer intentionType, String remark) {
        PrivateLiveUserIntentionResult privateLiveUserIntentionResult = new PrivateLiveUserIntentionResult();
        privateLiveUserIntentionResult.setUserId(userId);
        String unionId = wechatUserAclService.queryMtUnionIdByUserId(userId);
        if (StringUtils.isNotEmpty(unionId)) {
            privateLiveUserIntentionResult.setUnionId(unionId);
        }
        WechatUserInfoDTO wechatUserInfoDTO = wechatUserAclService.queryWechatUserByUserId(userId);
        if (wechatUserInfoDTO != null) {
            privateLiveUserIntentionResult.setWxNickName(wechatUserInfoDTO.getNickName());
        }
        privateLiveUserIntentionResult.setDistributorCode(distributorCode);
        privateLiveUserIntentionResult.setLiveId(liveId);
        privateLiveUserIntentionResult.setConsultantTaskId(consultantTaskId);
        privateLiveUserIntentionResult.setStatus(UserIntentionStatusEnum.VALID.getCode());
        privateLiveUserIntentionResult.setUserType(GroupUserTypeEnum.LINK_USER.getCode());
        privateLiveUserIntentionResult.setIntentionType(intentionType);
        userIntentionDomainService.insertIntentionResultAndLog(privateLiveUserIntentionResult, remark);
    }

    public RemoteResponse<Boolean> initFromGroup(ConsultantBindGroupInfo bindGroupInfo) {
        try {
            // 0.装饰userInfo，某些情况下，用户wxId已经绑定userId，但是上游传过来的时候没有带上
            bindGroupInfo.setUserList(decorateUserInfo(bindGroupInfo.getUserList()));

            // 1.插入日志表
            List<PrivateLiveGroupUserBehaviorLog> groupUserBehaviorLogList = buildLogList(bindGroupInfo);
            groupUserBehaviorLogRepository.batchInsert(groupUserBehaviorLogList);

            // 2.参数校验
            List<GroupUserInfo> userList = bindGroupInfo.getUserList();
            Long consultantTaskId = bindGroupInfo.getConsultantTaskId();
            RemoteResponse<PrivateLiveDistributorCodeDTO> checkResponse = checkParam(consultantTaskId, new Date());
            if (!checkResponse.isSuccess()) {
                log.error("[PrivateLiveUserIntentionServiceImpl].initFromGroup error, bindGroupInfo is {}, msg is {}", JSONObject.toJSONString(bindGroupInfo), checkResponse.getMsg());
                insertIntentionFailLog(userList, "", consultantTaskId, "参数校验失败，请查看日志！");
                return RemoteResponse.fail(checkResponse.getMsg());
            }

            String liveId = checkResponse.getData().getLiveId();
            String distributorCode = checkResponse.getData().getDistributorCode();

            // 3.过滤当前直播id下已经归因的的wxId
            List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.queryByLiveId(liveId, Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode(), UserIntentionStatusEnum.WAIT.getCode()));

            List<String> wxIdList = userIntentionResultList.stream()
                    .filter(userIntentionResult -> !userIntentionResult.getStatus().equals(UserIntentionStatusEnum.INVALID.getCode()))
                    .filter(userIntentionResult -> StringUtils.isNotEmpty(userIntentionResult.getWxId()))
                    .map(PrivateLiveUserIntentionResult::getWxId).collect(Collectors.toList());
            insertIntentionFailLog(userList, wxIdList, Lists.newArrayList(), Lists.newArrayList(), liveId, consultantTaskId, "该用户在当前直播id下已归因");
            userList = userList.stream()
                    .filter(user -> !wxIdList.contains(user.getWxId()))
                    .collect(Collectors.toList());

            // 3.2 过滤已归因的userId数据(userType不为3)
            // 场景：同一个人两个微信号，但是登录的美团账号是同一个
            List<Long> userIdList = userIntentionResultList.stream()
                    .filter(userIntentionResult -> !userIntentionResult.getStatus().equals(UserIntentionStatusEnum.INVALID.getCode()))
                    .filter(userIntentionResult -> userIntentionResult.getUserId() != null && userIntentionResult.getUserId() > 0L)
                    .filter(userIntentionResult -> !userIntentionResult.getUserType().equals(3))
                    .map(PrivateLiveUserIntentionResult::getUserId).collect(Collectors.toList());
            insertIntentionFailLog(userList, Lists.newArrayList(), Lists.newArrayList(), userIdList, liveId, consultantTaskId, "该用户在当前直播id下已归因");
            userList = userList.stream()
                    .filter(user -> !userIdList.contains(user.getMtUserId()))
                    .collect(Collectors.toList());

            // 4.筛选仅有userId的已归因链接用户数据
            // 场景：用户先下咨询师A的单，此时只有userId，后进群
            Map<Long, GroupUserInfo> userMap = userList.stream()
                    .filter(user -> user.getMtUserId() != null && user.getMtUserId() > 0)
                    .collect(Collectors.toMap(
                            GroupUserInfo::getMtUserId,
                            Function.identity(),
                            (ov, nv) -> nv
                    ));
            Map<Long, PrivateLiveUserIntentionResult> updateUserMap = userIntentionResultList.stream()
                    .filter(userIntentionResult -> !userIntentionResult.getStatus().equals(UserIntentionStatusEnum.INVALID.getCode()))
                    .filter(userIntentionResult -> userIntentionResult.getUserType().equals(3))
                    .filter(userIntentionResult -> userMap.containsKey(userIntentionResult.getUserId()))
                    .collect(Collectors.toMap(
                            PrivateLiveUserIntentionResult::getId,
                            Function.identity(),
                            (ov, nv) -> nv
                    ));
            for (Map.Entry<Long, PrivateLiveUserIntentionResult> entry : updateUserMap.entrySet()) {
                GroupUserInfo groupUserInfo = userMap.get(entry.getValue().getUserId());
                if (entry.getValue().getConsultantTaskId().equals(consultantTaskId)) {
                    // 仅为同一咨询师时，才需要更新数据
                    PrivateLiveUserIntentionResult updateUserIntentionResult = new PrivateLiveUserIntentionResult();
                    updateUserIntentionResult.setWxId(groupUserInfo.getWxId());
                    updateUserIntentionResult.setWxNickName(groupUserInfo.getWxNickname());
                    updateUserIntentionResult.setUserType(groupUserInfo.getUserType());
                    // 更新日志
                    userIntentionDomainService.updateIntentionResultAndLog(entry.getValue(), updateUserIntentionResult, "群初始化，wxId和userId关联成功");
                } else {
                    // 归因失败日志
                    insertIntentionFailLog(Lists.newArrayList(groupUserInfo), liveId, consultantTaskId, "该用户在当前直播id下已通过下单归因");
                }
            }
            userList = userList.stream()
                    .filter(user -> !updateUserMap.containsKey(user.getMtUserId()))
                    .collect(Collectors.toList());

            // 5.过滤同一场直播下的其他咨询师
            RemoteResponse<List<String>> unionIdsResponse = privateLiveConsultantVerifyService.getUnionIdsByLiveId(liveId);
            if (unionIdsResponse.isSuccess()) {
                List<String> consultantUnionIdList = unionIdsResponse.getData();
                insertIntentionFailLog(userList, Lists.newArrayList(), consultantUnionIdList, Lists.newArrayList(), liveId, consultantTaskId, "该用户在当前直播id下是咨询师");
                userList = userList.stream()
                        .filter(user -> !consultantUnionIdList.contains(user.getUnionId()))
                        .collect(Collectors.toList());
            }

            // 6.插入剩余信息到归因表
            List<PrivateLiveUserIntentionResult> retainUserIntentionResultList = buildIntentionResultList(consultantTaskId, userList, liveId, distributorCode);
            userIntentionDomainService.insertIntentionResultAndLog(retainUserIntentionResultList, "咨询师群初始化归因");

            return RemoteResponse.success(true);
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("初始化社群用户归因失败, bindGroupInfo: %s", JSONObject.toJSONString(bindGroupInfo))));
            log.error("[PrivateLiveUserIntentionServiceImpl].initFromGroup error, bindGroupInfo is {}, exception is", JSONObject.toJSONString(bindGroupInfo), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<Boolean> intentionCalc(GroupUserActionInfo groupUserActionInfo) {
        try {
            // 0.装饰userInfo，某些情况下，用户wxId已经绑定userId，但是上游传过来的时候没有带上
            groupUserActionInfo = decorateUserActionInfo(groupUserActionInfo);

            // 1.插入日志表
            PrivateLiveGroupUserBehaviorLog groupUserBehaviorLog = buildLog(groupUserActionInfo);
            groupUserBehaviorLogRepository.insert(groupUserBehaviorLog);

            if (groupUserActionInfo.getActionType() == 2) {
                return RemoteResponse.success(false);
            }

            // 2.参数校验
            // 查询直播间溯源方式
            String liveId = groupUserActionInfo.getLiveId();
            Long consultantTaskId = 0L;
            String distributorCode;
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(liveId);
            if (traceAbilityType == null) {
                Cat.logError(new PrivateLiveIntentionException(String.format("社群用户进群归因失败, 参数校验失败, groupUserActionInfo: %s", JSONObject.toJSONString(groupUserActionInfo))));
                log.error("[PrivateLiveUserIntentionServiceImpl].intentionCalc error, groupUserActionInfo is {}", JSONObject.toJSONString(groupUserActionInfo));
                insertIntentionFailLog(Lists.newArrayList(groupUserActionInfo), liveId, 0L, "查询直播间溯源方式失败");
                return RemoteResponse.fail("查询直播间溯源方式失败");
            }
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                // 个人溯源
                if (groupUserActionInfo.getInviterUserInfo() == null) {
                    // 如果邀请人为空，则为群内无归属
                    consultantTaskId = 0L;
                    groupUserActionInfo.setUserType(GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode());
                } else {
                    // 根据邀请人信息，查询上级中最近的有效咨询师
                    MemberInviteWxUserInfo inviterUserInfo = groupUserActionInfo.getInviterUserInfo();
                    Pair<Long, Integer> taskAndUserTypepair = queryNearestValidConsultantTaskId(inviterUserInfo.getInviteWxId(), inviterUserInfo.getInviteUnionId(), liveId);
                    consultantTaskId = taskAndUserTypepair.getLeft();
                    groupUserActionInfo.setUserType(taskAndUserTypepair.getRight());
                }
                groupUserActionInfo.setConsultantTaskId(consultantTaskId);
            } else if (TraceAbilityTypeEnum.GROUP.equals(traceAbilityType)) {
                // 社群溯源
                consultantTaskId = groupUserActionInfo.getConsultantTaskId();
            }

            // 个人溯源且咨询师任务id为0
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType) && consultantTaskId.equals(0L)) {
                RemoteResponse<String> checkResponse = checkLiveParam(liveId, DateUtils.toDate(groupUserActionInfo.getActionTime()));
                if (!checkResponse.isSuccess()) {
                    log.error("[PrivateLiveUserIntentionServiceImpl].intentionCalc error, groupUserActionInfo is {}, msg is {}", JSONObject.toJSONString(groupUserActionInfo), checkResponse.getMsg());
                    insertIntentionFailLog(Lists.newArrayList(groupUserActionInfo), liveId, consultantTaskId, checkResponse.getMsg());
                    return RemoteResponse.fail(checkResponse.getMsg());
                }
                distributorCode = checkResponse.getData();
                groupUserActionInfo.setUserType(GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode());
            } else {
                RemoteResponse<PrivateLiveDistributorCodeDTO> checkResponse = checkParam(consultantTaskId, DateUtils.toDate(groupUserActionInfo.getActionTime()));
                if (!checkResponse.isSuccess()) {
                    log.error("[PrivateLiveUserIntentionServiceImpl].intentionCalc error, groupUserActionInfo is {}, msg is {}", JSONObject.toJSONString(groupUserActionInfo), checkResponse.getMsg());
                    insertIntentionFailLog(Lists.newArrayList(groupUserActionInfo), liveId, consultantTaskId, checkResponse.getMsg());
                    return RemoteResponse.fail(checkResponse.getMsg());
                }
                distributorCode = checkResponse.getData().getDistributorCode();
            }

            // 3.根据wxId查询是否已经归因
            PrivateLiveUserIntentionQueryRequest queryRequest = new PrivateLiveUserIntentionQueryRequest();
            queryRequest.setLiveId(liveId);
            queryRequest.setWxId(groupUserActionInfo.getWxId());
            queryRequest.setStatusList(Lists.newArrayList(UserIntentionStatusEnum.WAIT.getCode(), UserIntentionStatusEnum.VALID.getCode()));
            List<PrivateLiveUserIntentionResult> wxIdIntentionResultList = userIntentionResultRepository.query(queryRequest);
            if (CollectionUtils.isNotEmpty(wxIdIntentionResultList)) {
                insertIntentionFailLog(Lists.newArrayList(groupUserActionInfo), Lists.newArrayList(groupUserActionInfo.getWxId()), Lists.newArrayList(), Lists.newArrayList(), liveId, consultantTaskId, "该用户在当前直播id下已归因");
                return RemoteResponse.success(false);
            }

            // 4.根据userId查询是否已归因
            if (groupUserActionInfo.getMtUserId() != null && groupUserActionInfo.getMtUserId() > 0) {
                queryRequest = new PrivateLiveUserIntentionQueryRequest();
                queryRequest.setLiveId(liveId);
                queryRequest.setUserId(groupUserActionInfo.getMtUserId());
                queryRequest.setStatusList(Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode()));
                List<PrivateLiveUserIntentionResult> userIdIntentionResultList = userIntentionResultRepository.query(queryRequest);
                if (CollectionUtils.isNotEmpty(userIdIntentionResultList)) {
                    PrivateLiveUserIntentionResult userIntentionResult = userIdIntentionResultList.get(0);
                    if (userIntentionResult.getUserType().equals(GroupUserTypeEnum.LINK_USER.getCode())) {
                        // 在进群之前已经通过下单进行归因，且当前仍然是未进群用户
                        PrivateLiveUserIntentionResult updateUserIntentionResult = new PrivateLiveUserIntentionResult();
                        updateUserIntentionResult.setWxId(groupUserActionInfo.getWxId());
                        updateUserIntentionResult.setWxNickName(groupUserBehaviorLog.getWxNickName());
                        if (userIdIntentionResultList.get(0).getConsultantTaskId().equals(consultantTaskId)) {
                            // 已归因咨询师与当前计算得到的咨询师为同一人，则更新用户类型
                            updateUserIntentionResult.setUserType(groupUserActionInfo.getUserType());
                        } else {
                            // 不相等情况下，说明进了别人的群或者被别人拉进了群，则算作裂变
                            updateUserIntentionResult.setUserType(GroupUserTypeEnum.FISSION_USER.getCode());
                        }
                        userIntentionDomainService.updateIntentionResultAndLog(userIntentionResult, updateUserIntentionResult, "用户进群，wxId和userId关联成功");
                        return RemoteResponse.success(true);
                    } else {
                        insertIntentionFailLog(Lists.newArrayList(groupUserActionInfo), liveId, consultantTaskId, "该用户在当前直播id下已通过下单归因");
                        return RemoteResponse.success(false);
                    }
                }
            }

            // 5.过滤同一场直播下的其他咨询师
            RemoteResponse<List<String>> unionIdsResponse = privateLiveConsultantVerifyService.getUnionIdsByLiveId(liveId);
            if (unionIdsResponse.isSuccess() && unionIdsResponse.getData().contains(groupUserActionInfo.getUnionId())) {
                insertIntentionFailLog(Lists.newArrayList(groupUserActionInfo), Lists.newArrayList(), unionIdsResponse.getData(), Lists.newArrayList(), liveId, consultantTaskId, "该用户在当前直播id下是咨询师");
                return RemoteResponse.success(false);
            }

            // 6.插入归因表
            PrivateLiveUserIntentionResult userIntentionResult = buildIntentionResult(groupUserActionInfo, liveId, distributorCode);
            userIntentionDomainService.insertIntentionResultAndLog(userIntentionResult, "用户进群归因");

            return RemoteResponse.success(true);
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("社群用户进群归因失败, groupUserActionInfo: %s", JSONObject.toJSONString(groupUserActionInfo))));
            log.error("[PrivateLiveUserIntentionServiceImpl].intentionCalc error, groupUserActionInfo is {}, exception is", JSONObject.toJSONString(groupUserActionInfo), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<Boolean> updateUserType(ConsultantBindGroupInfo reverseInfo) {
        List<GroupUserInfo> userList = reverseInfo.getUserList();
        Long consultantTaskId = reverseInfo.getConsultantTaskId();
        // 参数校验
        if (CollectionUtils.isEmpty(userList)) {
            log.error("[PrivateLiveUserIntentionServiceImpl].updateUserType error, reverseInfo is {}",
                    JSONObject.toJSONString(reverseInfo));
            return RemoteResponse.fail("用户信息不能为空");
        }
        RemoteResponse<PrivateLiveDistributorCodeDTO> checkResponse = checkParam(consultantTaskId, new Date());
        if (!checkResponse.isSuccess()) {
            log.error("[PrivateLiveUserIntentionServiceImpl].updateUserType error, reverseInfo is {}, msg is {}",
                    JSONObject.toJSONString(reverseInfo), checkResponse.getMsg());
            insertIntentionFailLog(userList, "", consultantTaskId, "回刷数据参数校验失败；原因：" + checkResponse.getMsg());
            return RemoteResponse.fail(checkResponse.getMsg());
        }
        String liveId = checkResponse.getData().getLiveId();
        // 查询当前咨询师任务下用户数据
        Map<Long, List<PrivateLiveUserIntentionResult>> wxIdIntentionResultListMap = userIntentionResultRepository
                .batchQueryByConsultantTaskId(liveId, Lists.newArrayList(consultantTaskId));
        List<PrivateLiveUserIntentionResult> userIntentionResultList = wxIdIntentionResultListMap.get(consultantTaskId);
        if (CollectionUtils.isEmpty(userIntentionResultList)) {
            insertIntentionFailLog(userList,
                    userList.stream().map(GroupUserInfo::getWxId).distinct().collect(Collectors.toList()),
                    Lists.newArrayList(), Lists.newArrayList(), liveId, consultantTaskId, "当前咨询师下无归因客资");
            return RemoteResponse.success(false);
        }
        // 获取本次要回刷的用户数据
        Map<String, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultList.stream()
                .filter(userIntentionResult -> !userIntentionResult.getStatus()
                        .equals(UserIntentionStatusEnum.INVALID.getCode()))
                .collect(
                        Collectors.toMap(PrivateLiveUserIntentionResult::getWxId, Function.identity(), (ov, nv) -> nv));
        Map<String, GroupUserInfo> userGroupMap = userList.stream()
                .filter(user -> userIntentionResultMap.containsKey(user.getWxId()))
                .collect(Collectors.toMap(GroupUserInfo::getWxId, Function.identity(), (ov, nv) -> nv));
        for (Map.Entry<String, PrivateLiveUserIntentionResult> entry : userIntentionResultMap.entrySet()) {
            if (userGroupMap.containsKey(entry.getKey())) {
                // 更新用户类型并记录日志
                PrivateLiveUserIntentionResult updateUserIntentionResult = new PrivateLiveUserIntentionResult();
//                BeanUtils.copyProperties(entry.getValue(), updateUserIntentionResult);
                updateUserIntentionResult.setUserType(userGroupMap.get(entry.getKey()).getUserType());
                userIntentionDomainService.updateIntentionResultAndLog(entry.getValue(), updateUserIntentionResult,
                        "咨询师裂变用户数据回刷成功");
            }
        }
        return RemoteResponse.success(true);
    }

    public RemoteResponse<Boolean> userStateChange(GroupUserInfo groupUserInfo) {
        try {
            String wxId = groupUserInfo.getWxId();
            String unionId = groupUserInfo.getUnionId();
            Long mtUserId = groupUserInfo.getMtUserId();
            Map<String, List<PrivateLiveUserIntentionResult>> liveIdToUserIntentionResultMap = Maps.newHashMap();
            Set<PrivateLiveUserIntentionResult> userIntentionResultSet = Sets.newHashSet();

            // 1.根据wxId、unionId、userId分别查询归因表
            if (StringUtils.isNotEmpty(wxId)) {
                List<PrivateLiveUserIntentionResult> wxIdUserIntentionList = userIntentionResultRepository.queryByWxId(wxId, Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode()));
                userIntentionResultSet.addAll(wxIdUserIntentionList);
            }
            if (StringUtils.isNotEmpty(unionId)) {
                List<PrivateLiveUserIntentionResult> unionIdUserIntentionList = userIntentionResultRepository.queryByUnionId(unionId, Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode()));
                userIntentionResultSet.addAll(unionIdUserIntentionList);
            }
            if (mtUserId != null && mtUserId > 0) {
                List<PrivateLiveUserIntentionResult> userIdUserIntentionList = userIntentionResultRepository.queryByUserId(mtUserId, Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode()));
                userIntentionResultSet.addAll(userIdUserIntentionList);
            }
            liveIdToUserIntentionResultMap = userIntentionResultSet.stream().collect(Collectors.groupingBy(PrivateLiveUserIntentionResult::getLiveId));

            // 2.同一个liveId下，如果有多条，仅保留最早那一条数据
            liveIdToUserIntentionResultMap.values().forEach(list ->
                    list.sort(Comparator.comparing(PrivateLiveUserIntentionResult::getCreateTime))
            );

            //// 2.同一个liveId下，如果有多条，以链接用户为准
            //liveIdToUserIntentionResultMap.values().forEach(list ->
            //        list.sort(Comparator.comparing(PrivateLiveUserIntentionResult::getUserType).reversed())
            //);

            // 3.更新归因表
            for (Map.Entry<String, List<PrivateLiveUserIntentionResult>> entry : liveIdToUserIntentionResultMap.entrySet()) {
                List<PrivateLiveUserIntentionResult> userIntentionResultList = entry.getValue();
                PrivateLiveUserIntentionResult updateUserIntentionResult = new PrivateLiveUserIntentionResult();
                updateUserIntentionResult.setWxId(wxId);
                updateUserIntentionResult.setUnionId(unionId);
                updateUserIntentionResult.setUserId(mtUserId);
                updateUserIntentionResult.setWxNickName(groupUserInfo.getWxNickname());
                updateUserIntentionResult.setStatus(UserIntentionStatusEnum.VALID.getCode());
                userIntentionDomainService.updateIntentionResultAndLog(userIntentionResultList.get(0), updateUserIntentionResult, "用户小程序授权，归因信息更新");
                for (int i = 1; i < userIntentionResultList.size(); i++) {
                    userIntentionDomainService.updateIntentionStatusAndLog(userIntentionResultList.get(i), UserIntentionStatusEnum.INVALID.getCode(), "用户小程序授权，归因信息更新");
                }
            }

            return RemoteResponse.success(true);
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("社群用户信息更新失败, groupUserInfo: %s", JSONObject.toJSONString(groupUserInfo))));
            log.error("[PrivateLiveUserIntentionServiceImpl].userStateChange error, groupUserInfo is {}, exception is", JSONObject.toJSONString(groupUserInfo), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    /**
     * 咨询师通过授权，需要无效之前用户归因结果
     * <p>
     * 解决逆向流程：咨询师先进群邀人，再授权
     *
     * @param consultantTaskId
     */
    public void passConsultant(Long consultantTaskId) {
        try {
            RemoteResponse<PrivateLiveConsultantTaskDTO> taskResponse = consultantAccountService.loadByTaskId(consultantTaskId);
            if (taskResponse.isSuccess() && taskResponse.getData() != null) {
                PrivateLiveConsultantTaskDTO taskDTO = taskResponse.getData();
                String liveId = taskDTO.getLiveId();
                String unionId = taskDTO.getUnionId();
                RemoteResponse<String> checkResponse = checkLiveParam(liveId, new Date());
                if (!checkResponse.isSuccess()) {
                    log.info(getClass().getSimpleName() + ".passConsultant, checkLive fail, req is {}, msg is {}", consultantTaskId, checkResponse.getMsg());
                    return;
                }
                PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultRepository.queryByUnionIdAndLiveId(unionId, liveId);
                if (userIntentionResult != null) {
                    // 无效之前的用户归因记录
                    userIntentionDomainService.updateIntentionStatusAndLog(userIntentionResult, UserIntentionStatusEnum.INVALID.getCode(), "咨询师通过授权，原归因关系更新");
                    // 调用社群接口更新
                    ConsultantChangeRequest changeRequest = new ConsultantChangeRequest();
                    //changeRequest.setOriginConsultantTaskId(userIntentionResult.getConsultantTaskId());
                    changeRequest.setWxId(userIntentionResult.getWxId());
                    changeRequest.setUnionId(unionId);
                    changeRequest.setUserId(userIntentionResult.getUserId());
                    changeRequest.setLiveId(liveId);
                    consultantBatchChangeProducer.sendMessageWithChangeRequest(Lists.newArrayList(changeRequest));
                }
                // 根据unionId查询日志获得wxId、userId
                List<PrivateLiveGroupUserBehaviorLog> userBehaviorLogList = groupUserBehaviorLogRepository.queryByUnionId(unionId);
                if (CollectionUtils.isNotEmpty(userBehaviorLogList)) {
                    // 调整之前的订单归因记录
                    PrivateLiveIntentionModifyRequest modifyRequest = new PrivateLiveIntentionModifyRequest();
                    modifyRequest.setWxId(userBehaviorLogList.get(0).getWxId());
                    modifyRequest.setMtUserId(userBehaviorLogList.get(0).getUserId());
                    modifyRequest.setNewConsultantTaskId(consultantTaskId);
                    modifyRequest.setLiveId(liveId);
                    modifyRequest.setOperatorId(-1L);
                    modifyRequest.setRemark("咨询师通过授权，归因记录回刷");
                    modifyDomainService.modifyIntention(modifyRequest, IntentionModifyTypeEnum.ORDER_INTENTION_MODIFY.getCode());
                }
                TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(liveId);
                if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType) && CollectionUtils.isNotEmpty(userBehaviorLogList)) {
                    // 个人溯源的情况下，需要调整下级用户
                    Map<PrivateLiveUserIntentionResult, Integer> userIntentionResultMap = queryDownUserIntentionResult(userBehaviorLogList.get(0).getWxId(), liveId);
                    List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(userIntentionResultMap, consultantTaskId, "咨询师授权，归因记录回刷");
                    modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
                }
            }
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("咨询师用户归因结果更新失败, consultantTaskId: %s", consultantTaskId)));
            log.error("[PrivateLiveUserIntentionServiceImpl].passConsultant error, consultantTaskId is {}, exception is", consultantTaskId, e);
        }
    }

    /**
     * 咨询师取消授权
     *
     * @param consultantTaskId
     */
    public void cancelConsultant(Long consultantTaskId) {
        try {
            // 根据consultantTaskId查询unionId
            RemoteResponse<PrivateLiveConsultantTaskDTO> taskResponse = consultantAccountService.loadByTaskId(consultantTaskId);
            if (taskResponse.isSuccess() && taskResponse.getData() != null) {
                PrivateLiveConsultantTaskDTO taskDTO = taskResponse.getData();
                String liveId = taskDTO.getLiveId();
                String unionId = taskDTO.getUnionId();
                RemoteResponse<String> checkResponse = checkLiveParam(liveId, new Date());
                if (!checkResponse.isSuccess()) {
                    log.info(getClass().getSimpleName() + ".cancelConsultant, checkLive fail, req is {}, msg is {}", consultantTaskId, checkResponse.getMsg());
                    return;
                }
                TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(liveId);
                if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                    // 根据unionId查询日志获得wxId、userId
                    List<PrivateLiveGroupUserBehaviorLog> userBehaviorLogList = groupUserBehaviorLogRepository.queryByUnionId(unionId);
                    if (CollectionUtils.isNotEmpty(userBehaviorLogList)) {
                        // 1、查询最近有效咨询师
                        Pair<Long, Integer> consultantPair = queryNearestValidConsultantTaskId(userBehaviorLogList.get(0).getWxId(), liveId);
                        // 2、调整本人归因数据modifyIntention  种子or裂变or无归属
                        PrivateLiveIntentionModifyRequest modifyRequest = new PrivateLiveIntentionModifyRequest();
                        modifyRequest.setWxId(userBehaviorLogList.get(0).getWxId());
                        modifyRequest.setMtUserId(userBehaviorLogList.get(0).getUserId());
                        modifyRequest.setWxNickName(userBehaviorLogList.get(0).getWxNickName());
                        modifyRequest.setOriginConsultantTaskId(0L);
                        modifyRequest.setNewConsultantTaskId(consultantPair.getLeft());
                        modifyRequest.setLiveId(liveId);
                        modifyRequest.setOperatorId(-1L);
                        modifyRequest.setTargetUserType(consultantPair.getRight());
                        modifyRequest.setRemark("咨询师取消授权，归因记录回刷");
                        modifyDomainService.modifyIntention(modifyRequest, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
                        // 3、根据consultantTaskId查询已有归因任务，将这部分用户修改为裂变or无归属  buildModifyRequestList
                        List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.queryByConsultantTaskId(liveId, consultantTaskId);

                        Map<PrivateLiveUserIntentionResult, Integer> userIntentionResultMap = userIntentionResultList.stream()
                                .collect(Collectors.toMap(Function.identity(),
                                        value -> consultantPair.getLeft().equals(0L) ? GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode() : GroupUserTypeEnum.FISSION_USER.getCode(),
                                        (ov, nv) -> nv));
                        List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(userIntentionResultMap, consultantPair.getLeft(), "咨询师取消授权，归因记录回刷");
                        modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
                    }
                }
            }
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("咨询师用户归因结果更新失败, consultantTaskId: %s", consultantTaskId)));
            log.error("[PrivateLiveUserIntentionServiceImpl].cancelConsultant error, consultantTaskId is {}, exception is", consultantTaskId, e);
        }

    }

    /**
     * 群内用户关联成为咨询师
     *
     * @param groupTraceBindConsultantDTO
     */
    public void bindConsultant(GroupTraceBindConsultantDTO groupTraceBindConsultantDTO) {
        try {
            String wxId = groupTraceBindConsultantDTO.getWxId();
            String liveId = groupTraceBindConsultantDTO.getLiveId();
            Long consultantTaskId = groupTraceBindConsultantDTO.getConsultantTaskId();
            RemoteResponse<String> checkResponse = checkLiveParam(liveId, new Date());
            if (!checkResponse.isSuccess()) {
                log.info(getClass().getSimpleName() + ".bindConsultant, checkLive fail, req is {}, msg is {}", JSON.toJSONString(groupTraceBindConsultantDTO), checkResponse.getMsg());
                return;
            }
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(liveId);
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                // 1、无效之前的归因记录
                Map<String, PrivateLiveUserIntentionResult> userIntentionResultWxMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndWxIds(liveId, Lists.newArrayList(wxId));
                PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultWxMap.get(wxId);
                if (userIntentionResult != null) {
                    userIntentionDomainService.updateIntentionStatusAndLog(userIntentionResult, UserIntentionStatusEnum.INVALID.getCode(), "群内用户关联咨询师，原归因关系更新");
                    // 调整之前的订单归因记录
                    PrivateLiveIntentionModifyRequest modifyRequest = new PrivateLiveIntentionModifyRequest();
                    modifyRequest.setWxId(userIntentionResult.getWxId());
                    modifyRequest.setMtUserId(userIntentionResult.getUserId());
                    modifyRequest.setOriginConsultantTaskId(userIntentionResult.getConsultantTaskId());
                    modifyRequest.setNewConsultantTaskId(consultantTaskId);
                    modifyRequest.setLiveId(liveId);
                    modifyRequest.setOperatorId(-1L);
                    modifyRequest.setRemark("群内用户关联咨询师，归因记录回刷");
                    modifyDomainService.modifyIntention(modifyRequest, IntentionModifyTypeEnum.ORDER_INTENTION_MODIFY.getCode());
                    // 调用社群接口更新
                    ConsultantChangeRequest changeRequest = new ConsultantChangeRequest();
                    //changeRequest.setOriginConsultantTaskId(userIntentionResult.getConsultantTaskId());
                    changeRequest.setWxId(wxId);
                    changeRequest.setUnionId(userIntentionResult.getUnionId());
                    changeRequest.setUserId(userIntentionResult.getUserId());
                    changeRequest.setLiveId(liveId);
                    consultantBatchChangeProducer.sendMessageWithChangeRequest(Lists.newArrayList(changeRequest));
                }
                // 2、调整下级用户
                Map<PrivateLiveUserIntentionResult, Integer> userIntentionResultMap = queryDownUserIntentionResult(wxId, liveId);
                List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(userIntentionResultMap, consultantTaskId, "群内用户关联咨询师，归因记录回刷");
                modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
            }
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("群内用户关联成为咨询师，归因结果更新失败, groupTraceBindConsultantDTO: %s", groupTraceBindConsultantDTO)));
            log.error("[PrivateLiveUserIntentionServiceImpl].bindConsultant error, groupTraceBindConsultantDTO is {}, exception is", groupTraceBindConsultantDTO, e);
        }
    }

    /**
     * 群内用户取消关联咨询师
     *
     * @param groupTraceBindConsultantDTO
     */
    public void unBindConsultant(GroupTraceBindConsultantDTO groupTraceBindConsultantDTO) {
        try {
            RemoteResponse<String> checkResponse = checkLiveParam(groupTraceBindConsultantDTO.getLiveId(), new Date());
            if (!checkResponse.isSuccess()) {
                log.info(getClass().getSimpleName() + ".unBindConsultant, checkLive fail, req is {}, msg is {}", JSON.toJSONString(groupTraceBindConsultantDTO), checkResponse.getMsg());
                return;
            }
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(groupTraceBindConsultantDTO.getLiveId());
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                // 1、查询最近有效咨询师
                Pair<Long, Integer> consultantPair = queryNearestValidConsultantTaskId(groupTraceBindConsultantDTO.getWxId(), groupTraceBindConsultantDTO.getLiveId());
                // 2、调整本人归因数据modifyIntention  种子or裂变or无归属
                PrivateLiveIntentionModifyRequest modifyRequest = new PrivateLiveIntentionModifyRequest();
                modifyRequest.setWxId(groupTraceBindConsultantDTO.getWxId());
                modifyRequest.setMtUserId(groupTraceBindConsultantDTO.getMtUserId());
                modifyRequest.setUnionId(groupTraceBindConsultantDTO.getUnionId());
                modifyRequest.setWxNickName(groupTraceBindConsultantDTO.getWxNickname());
                modifyRequest.setOriginConsultantTaskId(0L);
                modifyRequest.setNewConsultantTaskId(consultantPair.getLeft());
                modifyRequest.setLiveId(groupTraceBindConsultantDTO.getLiveId());
                modifyRequest.setOperatorId(-1L);
                modifyRequest.setTargetUserType(consultantPair.getRight());
                modifyRequest.setRemark("群内用户取消关联咨询师，归因记录回刷");
                modifyDomainService.modifyIntention(modifyRequest, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
                // 3、根据consultantTaskId查询已有归因任务，将这部分用户修改为裂变or无归属  buildModifyRequestList
                List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.queryByConsultantTaskId(groupTraceBindConsultantDTO.getLiveId(), groupTraceBindConsultantDTO.getConsultantTaskId());
                // 过滤下单归因用户
                userIntentionResultList = userIntentionResultList.stream()
                        .filter(userIntentionResult -> userIntentionResult.getUserType() != GroupUserTypeEnum.LINK_USER.getCode())
                        .collect(Collectors.toList());

                Map<PrivateLiveUserIntentionResult, Integer> userIntentionResultMap = userIntentionResultList.stream()
                        .collect(Collectors.toMap(Function.identity(),
                                value -> consultantPair.getLeft().equals(0L) ? GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode() : GroupUserTypeEnum.FISSION_USER.getCode(),
                                (ov, nv) -> nv));
                List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(userIntentionResultMap, consultantPair.getLeft(), "群内用户取消关联咨询师，归因记录回刷");
                modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
            }
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("群内用户取消关联咨询师，归因结果更新失败, groupTraceBindConsultantDTO: %s", groupTraceBindConsultantDTO)));
            log.error("[PrivateLiveUserIntentionServiceImpl].unBindConsultant error, groupTraceBindConsultantDTO is {}, exception is", groupTraceBindConsultantDTO, e);
        }
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "queryUserBindTask",
            newProcessorMethod = "queryUserBindTaskNew",
            oldProcessorMethod = "queryUserBindTaskOld",
            paramParser = QueryUserBindTaskParamParser.class
    )
    public RemoteResponse<Long> queryUserBindTask(PrivateLiveUserBindTaskRequest request) {
        try {
            if (StringUtils.isEmpty(request.getWxId()) && StringUtils.isEmpty(request.getUnionId())
                    && (request.getMtUserId() == null || request.getMtUserId() <= 0L)) {
                return RemoteResponse.fail("输入参数不合法");
            }
            if (StringUtils.isEmpty(request.getLiveId())) {
                return RemoteResponse.fail("输入参数不合法");
            }
            PrivateLiveUserIntentionQueryRequest queryRequest = new PrivateLiveUserIntentionQueryRequest();
            queryRequest.setWxId(request.getWxId());
            queryRequest.setUnionId(request.getUnionId());
            queryRequest.setUserId(request.getMtUserId());
            queryRequest.setLiveId(request.getLiveId());
            queryRequest.setStatusList(Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode()
                    , UserIntentionStatusEnum.WAIT.getCode(), UserIntentionStatusEnum.EXPIRED.getCode()));
            List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.forceQuery(queryRequest);
            return RemoteResponse.success(CollectionUtils.isNotEmpty(userIntentionResultList) ? userIntentionResultList.get(0).getConsultantTaskId() : null);
        } catch (Exception e) {
            log.error("[PrivateLiveUserIntentionServiceImpl].queryUserBindTask error, request is {}, exception is", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<Long> queryUserBindTaskOld(PrivateLiveUserBindTaskRequest request) {
        try {
            if (StringUtils.isEmpty(request.getWxId()) && StringUtils.isEmpty(request.getUnionId())
                    && (request.getMtUserId() == null || request.getMtUserId() <= 0L)) {
                return RemoteResponse.fail("输入参数不合法");
            }
            if (StringUtils.isEmpty(request.getLiveId())) {
                return RemoteResponse.fail("输入参数不合法");
            }
            PrivateLiveUserIntentionQueryRequest queryRequest = new PrivateLiveUserIntentionQueryRequest();
            queryRequest.setWxId(request.getWxId());
            queryRequest.setUnionId(request.getUnionId());
            queryRequest.setUserId(request.getMtUserId());
            queryRequest.setLiveId(request.getLiveId());
            queryRequest.setStatusList(Lists.newArrayList(UserIntentionStatusEnum.VALID.getCode()
                    , UserIntentionStatusEnum.WAIT.getCode(), UserIntentionStatusEnum.EXPIRED.getCode()));
            List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.forceQuery(queryRequest);
            return RemoteResponse.success(CollectionUtils.isNotEmpty(userIntentionResultList) ? userIntentionResultList.get(0).getConsultantTaskId() : null);
        } catch (Exception e) {
            log.error("[PrivateLiveUserIntentionServiceImpl].queryUserBindTask error, request is {}, exception is", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<Long> queryUserBindTaskNew(PrivateLiveUserBindTaskRequest request) {
        return dzPrivateLiveUserIntentionService.queryUserBindTask(request);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "modifyUserIntention",
            newProcessorMethod = "modifyUserIntentionNew",
            oldProcessorMethod = "modifyUserIntentionOld",
            paramParser = ModifyUserIntentionParamParser.class
    )
    public RemoteResponse<Boolean> modifyUserIntention(PrivateLiveIntentionModifyRequest request) {
        try {
            if (!checkModifyParam(request)) {
                return RemoteResponse.fail("输入参数不合法");
            }
            RemoteResponse<String> checkResponse = checkLiveParam(request.getLiveId(), new Date());
            if (!checkResponse.isSuccess()) {
                log.info(getClass().getSimpleName() + ".modifyUserIntention, checkLive fail, req is {}, msg is {}", JSON.toJSONString(request), checkResponse.getMsg());
                return RemoteResponse.fail(checkResponse.getMsg());
            }
            request.setRemark("人工修改归因");
            int userType = getUserTypeByInviteRelation(request.getWxId(), request.getMtUserId(), request.getLiveId(), request.getNewConsultantTaskId());
            request.setTargetUserType(userType);
            request.setTargetIntentionType(UserIntentionTypeEnum.MANUAL_INTENTION.getCode());
            modifyDomainService.modifyIntention(request, IntentionModifyTypeEnum.USER_INTENTION_MODIFY.getCode());
            // 个人溯源时，需要调整下级用户客资
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(request.getLiveId());
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                String wxId = request.getWxId();
                String liveId = request.getLiveId();
                if (StringUtils.isEmpty(wxId)) {
                    Long mtUserId = request.getMtUserId();
                    Map<Long, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndUserIds(liveId, Lists.newArrayList(mtUserId));
                    PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultMap.get(mtUserId);
                    if (userIntentionResult != null) {
                        wxId = userIntentionResult.getWxId();
                    }
                }
                List<PrivateLiveUserIntentionResult> validDownUserIntentionResult = queryValidDownUserIntentionResult(wxId, liveId, request.getOriginConsultantTaskId());
                List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(validDownUserIntentionResult, request.getNewConsultantTaskId(), "个人溯源时人工修改归因，下级用户一并调整");
                modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_INTENTION_MODIFY.getCode());
            }
            return RemoteResponse.success(true);
        } catch (PrivateLiveIntentionException privateLiveIntentionException) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserIntention error, request is {}, exception is", JSONObject.toJSONString(request), privateLiveIntentionException);
            RemoteResponse response = RemoteResponse.fail(privateLiveIntentionException.getMessage());
            response.setCode(ResponseEnum.INNER_FAILURE.code);
            return response;
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserIntention error, request is {}, exception is", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }
    @DistributedProxy(
            migrationCode = "com.sankuai.dzusergrowth.privatelive",
            key = "distributedProxy.modifyUserIntention",
            expression = "prefix-${tracerId()}"
    )
    public RemoteResponse<Boolean> modifyUserIntentionOld(PrivateLiveIntentionModifyRequest request) {
        try {
            if (!checkModifyParam(request)) {
                return RemoteResponse.fail("输入参数不合法");
            }
            RemoteResponse<String> checkResponse = checkLiveParam(request.getLiveId(), new Date());
            if (!checkResponse.isSuccess()) {
                log.info(getClass().getSimpleName() + ".modifyUserIntention, checkLive fail, req is {}, msg is {}", JSON.toJSONString(request), checkResponse.getMsg());
                return RemoteResponse.fail(checkResponse.getMsg());
            }
            request.setRemark("人工修改归因");
            int userType = getUserTypeByInviteRelation(request.getWxId(), request.getMtUserId(), request.getLiveId(), request.getNewConsultantTaskId());
            request.setTargetUserType(userType);
            request.setTargetIntentionType(UserIntentionTypeEnum.MANUAL_INTENTION.getCode());
            modifyDomainService.modifyIntention(request, IntentionModifyTypeEnum.USER_INTENTION_MODIFY.getCode());
            // 个人溯源时，需要调整下级用户客资
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(request.getLiveId());
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                String wxId = request.getWxId();
                String liveId = request.getLiveId();
                if (StringUtils.isEmpty(wxId)) {
                    Long mtUserId = request.getMtUserId();
                    Map<Long, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndUserIds(liveId, Lists.newArrayList(mtUserId));
                    PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultMap.get(mtUserId);
                    if (userIntentionResult != null) {
                        wxId = userIntentionResult.getWxId();
                    }
                }
                List<PrivateLiveUserIntentionResult> validDownUserIntentionResult = queryValidDownUserIntentionResult(wxId, liveId, request.getOriginConsultantTaskId());
                List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(validDownUserIntentionResult, request.getNewConsultantTaskId(), "个人溯源时人工修改归因，下级用户一并调整");
                modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_INTENTION_MODIFY.getCode());
            }
            return RemoteResponse.success(true);
        } catch (PrivateLiveIntentionException privateLiveIntentionException) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserIntention error, request is {}, exception is", JSONObject.toJSONString(request), privateLiveIntentionException);
            RemoteResponse response = RemoteResponse.fail(privateLiveIntentionException.getMessage());
            response.setCode(ResponseEnum.INNER_FAILURE.code);
            return response;
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserIntention error, request is {}, exception is", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }
    @DistributedProxy(
            migrationCode = "com.sankuai.dzusergrowth.privatelive",
            key = "distributedProxy.modifyUserIntention",
            expression = "prefix-${tracerId()}"
    )
    public RemoteResponse<Boolean> modifyUserIntentionNew(PrivateLiveIntentionModifyRequest request) {
        return dzPrivateLiveUserIntentionService.modifyUserIntention(request);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "modifyUserAndOrderIntention",
            newProcessorMethod = "modifyUserAndOrderIntentionNew",
            oldProcessorMethod = "modifyUserAndOrderIntentionOld",
            paramParser = ModifyUserAndOrderIntentionParamParser.class
    )
    public RemoteResponse<Boolean> modifyUserAndOrderIntention(PrivateLiveIntentionModifyRequest request) {
        try {
            if (!checkModifyParam(request)) {
                return RemoteResponse.fail("输入参数不合法");
            }
            RemoteResponse<String> checkResponse = checkLiveParam(request.getLiveId(), new Date());
            if (!checkResponse.isSuccess()) {
                log.info(getClass().getSimpleName() + ".modifyUserAndOrderIntention, checkLive fail, req is {}, msg is {}", JSON.toJSONString(request), checkResponse.getMsg());
                return RemoteResponse.fail(checkResponse.getMsg());
            }
            request.setRemark("人工修改归因");
            int userType = getUserTypeByInviteRelation(request.getWxId(), request.getMtUserId(), request.getLiveId(), request.getNewConsultantTaskId());
            request.setTargetUserType(userType);
            request.setTargetIntentionType(UserIntentionTypeEnum.MANUAL_INTENTION.getCode());
            modifyDomainService.modifyIntention(request, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
            // 个人溯源时，需要调整下级用户客资
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(request.getLiveId());
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                String wxId = request.getWxId();
                String liveId = request.getLiveId();
                if (StringUtils.isEmpty(wxId)) {
                    Long mtUserId = request.getMtUserId();
                    Map<Long, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndUserIds(liveId, Lists.newArrayList(mtUserId));
                    PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultMap.get(mtUserId);
                    if (userIntentionResult != null) {
                        wxId = userIntentionResult.getWxId();
                    }
                }
                List<PrivateLiveUserIntentionResult> validDownUserIntentionResult = queryValidDownUserIntentionResult(wxId, liveId, request.getOriginConsultantTaskId());
                List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(validDownUserIntentionResult, request.getNewConsultantTaskId(), "个人溯源时人工修改归因，下级用户一并调整");
                modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
            }
            return RemoteResponse.success(true);
        } catch (PrivateLiveIntentionException privateLiveIntentionException) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserAndOrderIntention error, request is {}, exception is", JSONObject.toJSONString(request), privateLiveIntentionException);
            RemoteResponse response = RemoteResponse.fail(privateLiveIntentionException.getMessage());
            response.setCode(ResponseEnum.INNER_FAILURE.code);
            return response;
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户订单归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserAndOrderIntention error, request is {}, exception is", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @DistributedProxy(
            migrationCode = "com.sankuai.dzusergrowth.privatelive",
            key = "distributedProxy.modifyUserAndOrderIntention",
            expression = "prefix-${tracerId()}"
    )
    public RemoteResponse<Boolean> modifyUserAndOrderIntentionOld(PrivateLiveIntentionModifyRequest request) {
        try {
            if (!checkModifyParam(request)) {
                return RemoteResponse.fail("输入参数不合法");
            }
            RemoteResponse<String> checkResponse = checkLiveParam(request.getLiveId(), new Date());
            if (!checkResponse.isSuccess()) {
                log.info(getClass().getSimpleName() + ".modifyUserAndOrderIntention, checkLive fail, req is {}, msg is {}", JSON.toJSONString(request), checkResponse.getMsg());
                return RemoteResponse.fail(checkResponse.getMsg());
            }
            request.setRemark("人工修改归因");
            int userType = getUserTypeByInviteRelation(request.getWxId(), request.getMtUserId(), request.getLiveId(), request.getNewConsultantTaskId());
            request.setTargetUserType(userType);
            request.setTargetIntentionType(UserIntentionTypeEnum.MANUAL_INTENTION.getCode());
            modifyDomainService.modifyIntention(request, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
            // 个人溯源时，需要调整下级用户客资
            TraceAbilityTypeEnum traceAbilityType = privateLiveAclService.queryLiveTraceConfig(request.getLiveId());
            if (TraceAbilityTypeEnum.PERSON.equals(traceAbilityType)) {
                String wxId = request.getWxId();
                String liveId = request.getLiveId();
                if (StringUtils.isEmpty(wxId)) {
                    Long mtUserId = request.getMtUserId();
                    Map<Long, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndUserIds(liveId, Lists.newArrayList(mtUserId));
                    PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultMap.get(mtUserId);
                    if (userIntentionResult != null) {
                        wxId = userIntentionResult.getWxId();
                    }
                }
                List<PrivateLiveUserIntentionResult> validDownUserIntentionResult = queryValidDownUserIntentionResult(wxId, liveId, request.getOriginConsultantTaskId());
                List<PrivateLiveIntentionModifyRequest> requestList = buildModifyRequestList(validDownUserIntentionResult, request.getNewConsultantTaskId(), "个人溯源时人工修改归因，下级用户一并调整");
                modifyDomainService.batchModifyIntention(requestList, IntentionModifyTypeEnum.USER_ORDER_INTENTION_MODIFY.getCode());
            }
            return RemoteResponse.success(true);
        } catch (PrivateLiveIntentionException privateLiveIntentionException) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserAndOrderIntention error, request is {}, exception is", JSONObject.toJSONString(request), privateLiveIntentionException);
            RemoteResponse response = RemoteResponse.fail(privateLiveIntentionException.getMessage());
            response.setCode(ResponseEnum.INNER_FAILURE.code);
            return response;
        } catch (Exception e) {
            Cat.logError(new PrivateLiveIntentionException(String.format("修改用户订单归因失败, request: %s", JSONObject.toJSONString(request))));
            log.error("[PrivateLiveUserIntentionServiceImpl].modifyUserAndOrderIntention error, request is {}, exception is", JSONObject.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    @DistributedProxy(
            migrationCode = "com.sankuai.dzusergrowth.privatelive",
            key = "distributedProxy.modifyUserAndOrderIntention",
            expression = "prefix-${tracerId()}"
    )
    public RemoteResponse<Boolean> modifyUserAndOrderIntentionNew(PrivateLiveIntentionModifyRequest request) {
        return dzPrivateLiveUserIntentionService.modifyUserAndOrderIntention(request);
    }

    private void insertIntentionFailLog(List<GroupUserInfo> userList, List<String> wxIdList, List<String> unionIdList, List<Long> userIdList, String liveId, long consultantTaskId, String remark) {
        // 由于用户已归因、用户是咨询师，导致的归因失败，需要过滤
        List<GroupUserInfo> filterUserList = userList.stream()
                .filter(user -> wxIdList.contains(user.getWxId()) || unionIdList.contains(user.getUnionId()) || userIdList.contains(user.getMtUserId()))
                .collect(Collectors.toList());
        insertIntentionFailLog(filterUserList, liveId, consultantTaskId, remark);
    }

    private void insertIntentionFailLog(List<GroupUserInfo> userList, String liveId, long consultantTaskId, String remark) {
        List<PrivateLiveUserIntentionLog> intentionLogList = userList.stream()
                .map(user -> {
                    PrivateLiveUserIntentionLog intentionLog = new PrivateLiveUserIntentionLog();
                    intentionLog.setWxId(Optional.ofNullable(user.getWxId()).orElse(""));
                    intentionLog.setUnionId(Optional.ofNullable(user.getUnionId()).orElse(""));
                    intentionLog.setUserId(Optional.ofNullable(user.getMtUserId()).orElse(0L));
                    intentionLog.setLiveId(liveId);
                    intentionLog.setConsultantTaskId(consultantTaskId);
                    intentionLog.setCalResult(UserIntentionStatusEnum.INVALID.getCode());
                    intentionLog.setRemark(remark);
                    intentionLog.setType(IntentionLogTypeEnum.SYSTEM_CREATE.getCode());
                    intentionLog.setRelatedRecordId(0L);
                    intentionLog.setRelatedModifyId(0L);
                    return intentionLog;
                }).collect(Collectors.toList());
        privateLiveUserIntentionLogRepository.batchInsert(intentionLogList);
    }

    private List<PrivateLiveGroupUserBehaviorLog> buildLogList(ConsultantBindGroupInfo bindGroupInfo) {
        long consultantTaskId = bindGroupInfo.getConsultantTaskId();
        String chatRoomSerialNo = bindGroupInfo.getChatRoomSerialNo();
        List<GroupUserInfo> userList = bindGroupInfo.getUserList();
        List<PrivateLiveGroupUserBehaviorLog> groupUserBehaviorLogList = new ArrayList<>();
        for (GroupUserInfo groupUserInfo : userList) {
            PrivateLiveGroupUserBehaviorLog groupUserBehaviorLog = new PrivateLiveGroupUserBehaviorLog();
            groupUserBehaviorLog.setConsultantTaskId(consultantTaskId);
            groupUserBehaviorLog.setChatroomSerialNo(chatRoomSerialNo);
            groupUserBehaviorLog.setWxId(Optional.ofNullable(groupUserInfo.getWxId()).orElse(""));
            groupUserBehaviorLog.setUnionId(Optional.ofNullable(groupUserInfo.getUnionId()).orElse(""));
            groupUserBehaviorLog.setUserId(Optional.ofNullable(groupUserInfo.getMtUserId()).orElse(0L));
            groupUserBehaviorLog.setWxNickName(Optional.ofNullable(groupUserInfo.getWxNickname()).orElse(""));
            groupUserBehaviorLog.setUserType(groupUserInfo.getUserType());
            groupUserBehaviorLog.setActionType(GroupUserActionTypeEnum.INIT.getCode());
            groupUserBehaviorLog.setActionTime(new Date());
            groupUserBehaviorLog.setLiveid("");
            groupUserBehaviorLogList.add(groupUserBehaviorLog);
        }
        return groupUserBehaviorLogList;
    }

    private PrivateLiveGroupUserBehaviorLog buildLog(GroupUserActionInfo groupUserActionInfo) {
        PrivateLiveGroupUserBehaviorLog groupUserBehaviorLog = new PrivateLiveGroupUserBehaviorLog();
        groupUserBehaviorLog.setConsultantTaskId(groupUserActionInfo.getConsultantTaskId());
        groupUserBehaviorLog.setChatroomSerialNo(groupUserActionInfo.getChatRoomSerialNo());
        groupUserBehaviorLog.setWxId(groupUserActionInfo.getWxId());
        groupUserBehaviorLog.setUnionId(groupUserActionInfo.getUnionId());
        groupUserBehaviorLog.setUserId(groupUserActionInfo.getMtUserId());
        groupUserBehaviorLog.setWxNickName(groupUserActionInfo.getWxNickname());
        groupUserBehaviorLog.setUserType(groupUserActionInfo.getUserType());
        groupUserBehaviorLog.setActionType(groupUserActionInfo.getActionType());
        groupUserBehaviorLog.setActionTime(DateUtils.toDate(groupUserActionInfo.getActionTime()));
        groupUserBehaviorLog.setLiveid(groupUserActionInfo.getLiveId());
        groupUserBehaviorLog.setExtra(JSON.toJSONString(groupUserActionInfo.getInviterUserInfo()));
        return groupUserBehaviorLog;
    }

    /**
     * 装饰群用户信息，某些情况下，用户wxId已经绑定userId，但是上游传过来的时候没有带上，需要查询
     *
     * @return
     */
    private List<GroupUserInfo> decorateUserInfo(List<GroupUserInfo> userInfoList) {
        userInfoList.stream().filter(groupUserInfo -> StringUtils.isEmpty(groupUserInfo.getUnionId()))
                .forEach(groupUserInfo -> {
                    String wxId = groupUserInfo.getWxId();
                    List<PrivateLiveUserIntentionResult> wxIdUserIntentionList = userIntentionResultRepository.queryByWxIdWithUnionIdNotNull(wxId);
                    if (CollectionUtils.isNotEmpty(wxIdUserIntentionList)) {
                        groupUserInfo.setUnionId(wxIdUserIntentionList.get(0).getUnionId());
                        groupUserInfo.setMtUserId(wxIdUserIntentionList.get(0).getUserId());
                    }
                });
        return userInfoList;
    }

    /**
     * 装饰群用户信息，某些情况下，用户wxId已经绑定userId，但是上游传过来的时候没有带上，需要查询
     *
     * @return
     */
    private GroupUserActionInfo decorateUserActionInfo(GroupUserActionInfo userActionInfo) {
        if (StringUtils.isEmpty(userActionInfo.getUnionId())) {
            String wxId = userActionInfo.getWxId();
            List<PrivateLiveUserIntentionResult> wxIdUserIntentionList = userIntentionResultRepository.queryByWxIdWithUnionIdNotNull(wxId);
            if (CollectionUtils.isNotEmpty(wxIdUserIntentionList)) {
                userActionInfo.setUnionId(wxIdUserIntentionList.get(0).getUnionId());
                userActionInfo.setMtUserId(wxIdUserIntentionList.get(0).getUserId());
            }
        }
        return userActionInfo;
    }

    private List<PrivateLiveUserIntentionResult> buildIntentionResultList(long consultantTaskId, List<GroupUserInfo> userList, String liveId, String distributorCode) {
        List<PrivateLiveUserIntentionResult> userIntentionResultList = new ArrayList<>();
        for (GroupUserInfo groupUserInfo : userList) {
            PrivateLiveUserIntentionResult userIntentionResult = new PrivateLiveUserIntentionResult();
            userIntentionResult.setWxId(Optional.ofNullable(groupUserInfo.getWxId()).orElse(""));
            userIntentionResult.setUnionId(Optional.ofNullable(groupUserInfo.getUnionId()).orElse(""));
            userIntentionResult.setUserId(Optional.ofNullable(groupUserInfo.getMtUserId()).orElse(0L));
            userIntentionResult.setWxNickName(Optional.ofNullable(groupUserInfo.getWxNickname()).orElse(""));
            userIntentionResult.setDistributorCode(distributorCode);
            userIntentionResult.setLiveId(liveId);
            userIntentionResult.setConsultantTaskId(consultantTaskId);
            userIntentionResult.setStatus(UserIntentionStatusEnum.VALID.getCode());
            // 进群即归因，因此以下代码作废
            //// 如果已授权，此处status为有效
            //if (groupUserInfo.getMtUserId() != null && groupUserInfo.getMtUserId() > 0L) {
            //    userIntentionResult.setStatus(UserIntentionStatusEnum.VALID.getCode());
            //} else {
            //    // 否则为等待归因
            //    userIntentionResult.setStatus(UserIntentionStatusEnum.WAIT.getCode());
            //}
            userIntentionResult.setUserType(groupUserInfo.getUserType());
            userIntentionResult.setIntentionType(UserIntentionTypeEnum.JOIN_GROUP_INTENTION.getCode());
            userIntentionResultList.add(userIntentionResult);
        }
        return userIntentionResultList;
    }

    private PrivateLiveUserIntentionResult buildIntentionResult(GroupUserActionInfo groupUserActionInfo, String liveId, String distributorCode) {
        PrivateLiveUserIntentionResult userIntentionResult = new PrivateLiveUserIntentionResult();
        userIntentionResult.setWxId(groupUserActionInfo.getWxId());
        userIntentionResult.setUnionId(groupUserActionInfo.getUnionId());
        userIntentionResult.setUserId(groupUserActionInfo.getMtUserId());
        userIntentionResult.setWxNickName(groupUserActionInfo.getWxNickname());
        userIntentionResult.setDistributorCode(distributorCode);
        userIntentionResult.setLiveId(liveId);
        userIntentionResult.setConsultantTaskId(groupUserActionInfo.getConsultantTaskId());
        userIntentionResult.setStatus(UserIntentionStatusEnum.VALID.getCode());
        // 进群即归因，因此以下代码作废
        //// 如果已授权，此处status为有效
        //if (groupUserActionInfo.getMtUserId() != null && groupUserActionInfo.getMtUserId() > 0L) {
        //    userIntentionResult.setStatus(UserIntentionStatusEnum.VALID.getCode());
        //} else {
        //    // 否则为等待归因
        //    userIntentionResult.setStatus(UserIntentionStatusEnum.WAIT.getCode());
        //}
        userIntentionResult.setUserType(groupUserActionInfo.getUserType());
        userIntentionResult.setIntentionType(UserIntentionTypeEnum.JOIN_GROUP_INTENTION.getCode());
        return userIntentionResult;
    }

    private RemoteResponse<PrivateLiveDistributorCodeDTO> checkParam(Long consultantTaskId, Date actionTime) {
        if (consultantTaskId == null || consultantTaskId <= 0L) {
            return RemoteResponse.fail("咨询师任务id不存在");
        }
        // 1.查询咨询师是否在线
        RemoteResponse<Boolean> consultantValidResponse = privateLiveConsultantVerifyService.checkConsultantValid(consultantTaskId);
        if (!consultantValidResponse.isSuccess() || !consultantValidResponse.getData()) {
            return RemoteResponse.fail("咨询师不在线");
        }

        // 2.根据咨询师任务id查找直播id
        RemoteResponse<PrivateLiveDistributorCodeDTO> liveAndTaskResponse = distributorCodeService.loadLiveAndCodeByTask(consultantTaskId);
        if (!liveAndTaskResponse.isSuccess() || liveAndTaskResponse.getData() == null) {
            return RemoteResponse.fail("咨询师任务id不存在");
        }
        String liveId = liveAndTaskResponse.getData().getLiveId();

        // 3.查询是否在归因有效期内
        PrivateLiveIntentionExpireTime intentionExpireTime = intentionExpireTimeRepository.queryByLiveId(liveId);

        if (intentionExpireTime != null && (intentionExpireTime.getStatus() == DistributionStatusEnum.INVALID.getCode()
                || intentionExpireTime.getExpireTime().before(actionTime))) {
            return RemoteResponse.fail("归因有效期已过期");
        }

        // 没有归因有效期
        if (intentionExpireTime == null) {
            RemoteResponse<Boolean> initResponse = privateLiveIntentionExpireService.init(liveId, actionTime);
            if (!initResponse.isSuccess() || !initResponse.getData()) {
                return RemoteResponse.fail(initResponse.getMsg());
            }
        }

        return RemoteResponse.success(liveAndTaskResponse.getData());
    }

    private RemoteResponse<String> checkLiveParam(String liveId, Date actionTime) {
        // 1.查询是否在归因有效期内
        PrivateLiveIntentionExpireTime intentionExpireTime = intentionExpireTimeRepository.queryByLiveId(liveId);

        if (intentionExpireTime != null && (intentionExpireTime.getStatus() == DistributionStatusEnum.INVALID.getCode()
                || intentionExpireTime.getExpireTime().before(actionTime))) {
            return RemoteResponse.fail("归因有效期已过期");
        }

        // 没有归因有效期
        if (intentionExpireTime == null) {
            RemoteResponse<Boolean> initResponse = privateLiveIntentionExpireService.init(liveId, actionTime);
            if (!initResponse.isSuccess() || !initResponse.getData()) {
                return RemoteResponse.fail(initResponse.getMsg());
            }
        }
        RemoteResponse<String> codeResponse = distributorCodeService.loadCodeByLiveId(liveId);
        if (!codeResponse.isSuccess() || StringUtils.isEmpty(codeResponse.getData())) {
            return RemoteResponse.fail("直播id不存在");
        }
        return RemoteResponse.success(codeResponse.getData());
    }

    private boolean checkModifyParam(PrivateLiveIntentionModifyRequest request) {
        if (StringUtils.isEmpty(request.getWxId()) && (request.getMtUserId() == null || request.getMtUserId() == 0L)) {
            return false;
        }
        if (request.getOriginConsultantTaskId() == null) {
            return false;
        }
        if (request.getNewConsultantTaskId() == null || request.getNewConsultantTaskId() == 0L) {
            return false;
        }
        if (StringUtils.isEmpty(request.getLiveId())) {
            return false;
        }
        return request.getOperatorId() != null && request.getOperatorId() != 0L;
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "queryUserInfoByLiveId",
            newProcessorMethod = "queryUserInfoByLiveIdNew",
            oldProcessorMethod = "queryUserInfoByLiveIdOld",
            paramParser = QueryUserInfoByLiveIdParamParser.class
    )
    public RemoteResponse<PrivateLiveSourceBoardStatisticDTO> queryUserInfoByLiveId(String liveId) {
        if (StringUtils.isBlank(liveId)) {
            return RemoteResponse.fail("直播id不能为空");
        }
        PrivateLiveSourceBoardStatisticDTO statisticDTO = new PrivateLiveSourceBoardStatisticDTO();
        List<PrivateLiveUserTypeBaseCount> userTypeCountList = userIntentionResultRepository.countUserTypeByLiveId(liveId);
        Map<Integer, Integer> userTypeToCountMap = userTypeCountList.stream().filter(Objects::nonNull).collect(Collectors.toMap(PrivateLiveUserTypeBaseCount::getType, PrivateLiveUserTypeBaseCount::getCount));
        statisticDTO.setLiveId(liveId);
        statisticDTO.setSeedUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.SEED_USER.getCode(), 0));
        statisticDTO.setFissionUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.FISSION_USER.getCode(), 0));
        statisticDTO.setUnEnterGroupUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.LINK_USER.getCode(), 0));
        Integer groupUnIntentionUserCnt = userTypeToCountMap.getOrDefault(GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode(), 0);
        statisticDTO.setAllUserCnt(statisticDTO.getSeedUserCnt() + statisticDTO.getFissionUserCnt() + statisticDTO.getUnEnterGroupUserCnt() + groupUnIntentionUserCnt);
        return RemoteResponse.success(statisticDTO);
    }


    public RemoteResponse<PrivateLiveSourceBoardStatisticDTO> queryUserInfoByLiveIdOld(String liveId) {
        if (StringUtils.isBlank(liveId)) {
            return RemoteResponse.fail("直播id不能为空");
        }
        PrivateLiveSourceBoardStatisticDTO statisticDTO = new PrivateLiveSourceBoardStatisticDTO();
        List<PrivateLiveUserTypeBaseCount> userTypeCountList = userIntentionResultRepository.countUserTypeByLiveId(liveId);
        Map<Integer, Integer> userTypeToCountMap = userTypeCountList.stream().filter(Objects::nonNull).collect(Collectors.toMap(PrivateLiveUserTypeBaseCount::getType, PrivateLiveUserTypeBaseCount::getCount));
        statisticDTO.setLiveId(liveId);
        statisticDTO.setSeedUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.SEED_USER.getCode(), 0));
        statisticDTO.setFissionUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.FISSION_USER.getCode(), 0));
        statisticDTO.setUnEnterGroupUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.LINK_USER.getCode(), 0));
        Integer groupUnIntentionUserCnt = userTypeToCountMap.getOrDefault(GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode(), 0);
        statisticDTO.setAllUserCnt(statisticDTO.getSeedUserCnt() + statisticDTO.getFissionUserCnt() + statisticDTO.getUnEnterGroupUserCnt() + groupUnIntentionUserCnt);
        return RemoteResponse.success(statisticDTO);
    }

    public RemoteResponse<PrivateLiveSourceBoardStatisticDTO> queryUserInfoByLiveIdNew(String liveId) {
        return dzPrivateLiveUserIntentionService.queryUserInfoByLiveId(liveId);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "pageQuerySourceBoardConsultList",
            newProcessorMethod = "pageQuerySourceBoardConsultListNew",
            oldProcessorMethod = "pageQuerySourceBoardConsultListOld",
            paramParser = PageQuerySourceBoardConsultListParamParser.class
    )
    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> pageQuerySourceBoardConsultList(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }
            if (request.getPageNo() <= 0 || request.getPageSize() <= 0 || request.getPageSize() > 50) {
                return RemoteResponse.fail("分页参数错误");
            }

            PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO> consultStatisticsDTOPageDataDTO = new PageDataDTO<>();
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
            consultStatisticsDTOPageDataDTO.setPageInfoDTO(pageInfoDTO);
            consultStatisticsDTOPageDataDTO.setList(Collections.EMPTY_LIST);

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            long totalNum = privateLiveConsultantTaskRepository.countByCondition(request.getLiveId(), request.getConsultName(), request.getConsultPhone(), taskIds);
            if (totalNum == 0L) {
                return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
            }

            pageInfoDTO.setTotalCount((int) totalNum);
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.pageLoadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds, computeOffSet(request.getPageNo(),
                            request.getPageSize()),
                    request.getPageSize());

            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
            }

            List<PrivateLiveSourceBoardConsultStatisticsDTO> statisticsDTOS = consultStatistics(request.getLiveId(), privateLiveConsultantTasks);
            consultStatisticsDTOPageDataDTO.setList(statisticsDTOS);
            return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#pageQuerySourceBoardConsultList", SimpleJacksonUtils.serialize(request), e);
            log.error("pageQuerySourceBoardConsultList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> pageQuerySourceBoardConsultListOld(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }
            if (request.getPageNo() <= 0 || request.getPageSize() <= 0 || request.getPageSize() > 50) {
                return RemoteResponse.fail("分页参数错误");
            }

            PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO> consultStatisticsDTOPageDataDTO = new PageDataDTO<>();
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
            consultStatisticsDTOPageDataDTO.setPageInfoDTO(pageInfoDTO);
            consultStatisticsDTOPageDataDTO.setList(Collections.EMPTY_LIST);

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            long totalNum = privateLiveConsultantTaskRepository.countByCondition(request.getLiveId(), request.getConsultName(), request.getConsultPhone(), taskIds);
            if (totalNum == 0L) {
                return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
            }

            pageInfoDTO.setTotalCount((int) totalNum);
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.pageLoadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds, computeOffSet(request.getPageNo(),
                            request.getPageSize()),
                    request.getPageSize());

            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
            }

            List<PrivateLiveSourceBoardConsultStatisticsDTO> statisticsDTOS = consultStatistics(request.getLiveId(), privateLiveConsultantTasks);
            consultStatisticsDTOPageDataDTO.setList(statisticsDTOS);
            return RemoteResponse.success(consultStatisticsDTOPageDataDTO);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#pageQuerySourceBoardConsultList", SimpleJacksonUtils.serialize(request), e);
            log.error("pageQuerySourceBoardConsultList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultStatisticsDTO>> pageQuerySourceBoardConsultListNew(PrivateLiveSourceBoardConsultRequest request) {
        return dzPrivateLiveUserIntentionService.pageQuerySourceBoardConsultList(request);
    }


    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "pageQuerySourceBoardConsultUserList",
            newProcessorMethod = "pageQuerySourceBoardConsultUserListNew",
            oldProcessorMethod = "pageQuerySourceBoardConsultUserListOld",
            paramParser = PageQuerySourceBoardConsultUserListParamParser.class
    )
    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> pageQuerySourceBoardConsultUserList(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }
            if (request.getPageNo() <= 0 || request.getPageSize() <= 0 || request.getPageSize() > 50) {
                return RemoteResponse.fail("分页参数错误");
            }
            PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO> consultUserStatisticsDTOPageDataDTO = new PageDataDTO<>();
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
            consultUserStatisticsDTOPageDataDTO.setPageInfoDTO(pageInfoDTO);
            consultUserStatisticsDTOPageDataDTO.setList(Collections.EMPTY_LIST);

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            long totalNum = privateLiveConsultantTaskRepository.countByCondition(request.getLiveId(), request.getConsultName(), request.getConsultPhone(), taskIds);
            if (totalNum == 0L) {
                return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
            }

            pageInfoDTO.setTotalCount((int) totalNum);
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.pageLoadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds, computeOffSet(request.getPageNo(),
                            request.getPageSize()),
                    request.getPageSize());

            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
            }

            List<PrivateLiveSourceBoardConsultUserStatisticsDTO> statisticsDTOS = consultUserStatistics(request.getLiveId(), privateLiveConsultantTasks);
            consultUserStatisticsDTOPageDataDTO.setList(statisticsDTOS);
            return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#pageQuerySourceBoardConsultList", SimpleJacksonUtils.serialize(request), e);
            log.error("pageQuerySourceBoardConsultList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> pageQuerySourceBoardConsultUserListOld(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }
            if (request.getPageNo() <= 0 || request.getPageSize() <= 0 || request.getPageSize() > 50) {
                return RemoteResponse.fail("分页参数错误");
            }
            PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO> consultUserStatisticsDTOPageDataDTO = new PageDataDTO<>();
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
            consultUserStatisticsDTOPageDataDTO.setPageInfoDTO(pageInfoDTO);
            consultUserStatisticsDTOPageDataDTO.setList(Collections.EMPTY_LIST);

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            long totalNum = privateLiveConsultantTaskRepository.countByCondition(request.getLiveId(), request.getConsultName(), request.getConsultPhone(), taskIds);
            if (totalNum == 0L) {
                return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
            }

            pageInfoDTO.setTotalCount((int) totalNum);
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.pageLoadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds, computeOffSet(request.getPageNo(),
                            request.getPageSize()),
                    request.getPageSize());

            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
            }

            List<PrivateLiveSourceBoardConsultUserStatisticsDTO> statisticsDTOS = consultUserStatistics(request.getLiveId(), privateLiveConsultantTasks);
            consultUserStatisticsDTOPageDataDTO.setList(statisticsDTOS);
            return RemoteResponse.success(consultUserStatisticsDTOPageDataDTO);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#pageQuerySourceBoardConsultList", SimpleJacksonUtils.serialize(request), e);
            log.error("pageQuerySourceBoardConsultList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardConsultUserStatisticsDTO>> pageQuerySourceBoardConsultUserListNew(PrivateLiveSourceBoardConsultRequest request) {
        return dzPrivateLiveUserIntentionService.pageQuerySourceBoardConsultUserList(request);
    }

    private List<PrivateLiveSourceBoardConsultStatisticsDTO> consultStatistics(String liveId, List<PrivateLiveConsultantTask> privateLiveConsultantTasks) throws Exception {

        List<Long> taskIdList = privateLiveConsultantTasks.stream().map(PrivateLiveConsultantTask::getId).collect(Collectors.toList());

        // 获取咨询师所属的团长渠道
        CompletableFuture<Map<Long, DistributorGroupModel>> distributorGroupFur = CompletableFuture.supplyAsync(
                () -> queryConsultantDistributor(privateLiveConsultantTasks), LIVE_INTENTION_STATISTIC_POOL
        );

        // 获取咨询师每个类型的用户数量
        CompletableFuture<Map<Long, List<PrivateLiveUserTypeCount>>> privateLiveUserTypeCountMapFur = CompletableFuture.supplyAsync(
                () -> userIntentionResultRepository.countUserTypeMapByConsultantTaskId(liveId, taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        // 获取咨询师的归因用户列表
        CompletableFuture<Map<Long, List<PrivateLiveUserIntentionResult>>> taskIdToUserIntentListMapFur = CompletableFuture.supplyAsync(
                () -> userIntentionResultRepository.batchQueryByConsultantTaskId(liveId, taskIdList), LIVE_INTENTION_STATISTIC_POOL

        );

        // 获取咨询师的归因用户业绩列表
        CompletableFuture<Map<Long, List<PrivateLiveConsultantUserStatistics>>> privateLiveConsultantUserStatisticsMapFur = CompletableFuture.supplyAsync(
                () -> privateLiveConsultantSummaryRepository.batchGetSummaryMapByConsultantTasks(taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        // 查询跟进情况
        CompletableFuture<Map<Long, SourceBoardConsultFollowUpInfo>> consultFollowUpInfoMapFur = CompletableFuture.supplyAsync(
                () -> privateSphereUserAclService.queryConsultTaskFollowUpInfoMap(liveId, taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        CompletableFuture.allOf(distributorGroupFur, privateLiveUserTypeCountMapFur, taskIdToUserIntentListMapFur,
                privateLiveConsultantUserStatisticsMapFur, consultFollowUpInfoMapFur).get(3, TimeUnit.SECONDS);

        Map<Long, DistributorGroupModel> distributorGroupModelMap = distributorGroupFur.join();
        Map<Long, List<PrivateLiveUserTypeCount>> privateLiveUserTypeCountMap = privateLiveUserTypeCountMapFur.join();
        Map<Long, List<PrivateLiveUserIntentionResult>> taskIdToUserIntentListMap = taskIdToUserIntentListMapFur.join();
        Map<Long, List<PrivateLiveConsultantUserStatistics>> privateLiveConsultantUserStatisticsMap = privateLiveConsultantUserStatisticsMapFur.join();
        Map<Long, SourceBoardConsultFollowUpInfo> consultFollowUpInfoMap = consultFollowUpInfoMapFur.join();

        return privateLiveConsultantTasks.stream()
                .map(consultantTask -> buildConsultStatisticsDTO(consultantTask,
                        distributorGroupModelMap.get(consultantTask.getId()),
                        privateLiveUserTypeCountMap.get(consultantTask.getId()),
                        taskIdToUserIntentListMap.get(consultantTask.getId()),
                        privateLiveConsultantUserStatisticsMap.get(consultantTask.getId()),
                        consultFollowUpInfoMap.get(consultantTask.getId()))).collect(Collectors.toList());
    }

    /**
     * key为咨询师任务ID
     *
     * @param privateLiveConsultantTasks
     * @return
     */
    private Map<Long, DistributorGroupModel> queryConsultantDistributor(List<PrivateLiveConsultantTask> privateLiveConsultantTasks) {
        if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
            return Maps.newHashMap();
        }
        List<Long> accountIds = privateLiveConsultantTasks.stream()
                .filter(e -> e.getTaskType() == PrivateLiveTaskTypeEnum.DISTRIBUTOR.getCode())
                .map(PrivateLiveConsultantTask::getConsultantId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            return Maps.newHashMap();
        }
        Map<Long, DistributorGroupModel> distributorGroupModels = distributorGroupAppService.getRelationDistributorGroupMap(accountIds);
//        Map<Long, DistributorGroupModel> accountId2Distributor = distributorGroupModels.stream()
//                .collect(Collectors.toMap(DistributorGroupModel::getAccountId, Function.identity(), (v1, v2) -> v1));
        return privateLiveConsultantTasks.stream()
                .collect(HashMap::new, (m, a) -> {
                            DistributorGroupModel distributorGroupModel = distributorGroupModels.get(a.getConsultantId());
                            if (!ObjectUtils.isEmpty(distributorGroupModel)) {
                                m.put(a.getId(), distributorGroupModel);
                            }
                        },
                        HashMap::putAll);
    }

    private PrivateLiveSourceBoardConsultStatisticsDTO buildConsultStatisticsDTO(PrivateLiveConsultantTask consultantTask,
                                                                                 DistributorGroupModel distributorGroupModel,
                                                                                 List<PrivateLiveUserTypeCount> userTypeCountList,
                                                                                 List<PrivateLiveUserIntentionResult> userIntentionResultList,
                                                                                 List<PrivateLiveConsultantUserStatistics> userIntentionSaleList,
                                                                                 SourceBoardConsultFollowUpInfo consultFollowUpInfo) {
        PrivateLiveSourceBoardConsultStatisticsDTO statisticsDTO = new PrivateLiveSourceBoardConsultStatisticsDTO();
        statisticsDTO.setLiveId(consultantTask.getLiveId());
        statisticsDTO.setTaskId(consultantTask.getId());
        statisticsDTO.setConsultName(consultantTask.getShareName());
        statisticsDTO.setActualName(consultantTask.getActualName());
        statisticsDTO.setConsultantId(consultantTask.getConsultantId());

        if (Objects.nonNull(distributorGroupModel)) {
            statisticsDTO.setDistributorGroupName(distributorGroupModel.getGroupName());
        }
        try {
            statisticsDTO.setPhoneNumber(mobileTokenUtil.getMobileMask(consultantTask.getPhoneNumber()));
        } catch (Exception e) {
            log.error("pageQuerySourceBoardConsultList, get phone number fail", e);
            statisticsDTO.setPhoneNumber(StringUtils.EMPTY);
        }

        // 总业绩做累加
        statisticsDTO.setAllSaleInfo(convertAllSaleInfo(userIntentionSaleList));

        // 跟进情况
        statisticsDTO.setFollowUpInfo(consultFollowUpInfo == null ? SourceBoardConsultFollowUpInfo.empty() : consultFollowUpInfo);

        // 有效客户
        statisticsDTO.setAllUserInfo(convertAllUserInfo(userTypeCountList));

        // 种子用户转化
        statisticsDTO.setSeedSaleInfo(convertSeedSaleInfo(userIntentionResultList, userIntentionSaleList));

        // 裂变用户转化
        statisticsDTO.setFissionSaleInfo(convertFissionSaleInfo(userIntentionResultList, userIntentionSaleList));

        // 未进群用户转化
        statisticsDTO.setUnEnterGroupSaleInfo(convertUnEnterSaleInfo(userIntentionResultList, userIntentionSaleList));
        return statisticsDTO;
    }

    private SourceBoardConsultUserInfo convertAllUserInfo(List<PrivateLiveUserTypeCount> userTypeCountList) {
        if (CollectionUtils.isEmpty(userTypeCountList)) {
            return SourceBoardConsultUserInfo.empty();
        }

        SourceBoardConsultUserInfo userInfo = new SourceBoardConsultUserInfo();

        Map<Integer, Integer> userTypeToCountMap = userTypeCountList.stream().filter(Objects::nonNull).collect(Collectors.toMap(PrivateLiveUserTypeCount::getType,
                PrivateLiveUserTypeCount::getCount));


        userInfo.setSeedUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.SEED_USER.getCode(), 0));
        userInfo.setFissionUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.FISSION_USER.getCode(), 0));
        userInfo.setUnEnterGroupUserCnt(userTypeToCountMap.getOrDefault(GroupUserTypeEnum.LINK_USER.getCode(), 0));
        userInfo.setAllUserCnt(userInfo.getSeedUserCnt() + userInfo.getFissionUserCnt() + userInfo.getUnEnterGroupUserCnt());
        return userInfo;
    }

    private SourceBoardConsultAllSaleInfo convertAllSaleInfo(List<PrivateLiveConsultantUserStatistics> userIntentionSaleList) {

        if (CollectionUtils.isEmpty(userIntentionSaleList)) {
            return SourceBoardConsultAllSaleInfo.empty();
        }

        long totalGTV = userIntentionSaleList.stream().mapToLong(PrivateLiveConsultantUserStatistics::getGmvAmt).sum();
        int totalOrder = userIntentionSaleList.stream().mapToInt(PrivateLiveConsultantUserStatistics::getPayOrderCnt).sum();
        long totalUserCnt = userIntentionSaleList.stream().filter(e -> e.getPayOrderCnt() > 0).map(PrivateLiveConsultantUserStatistics::getMtUserId).collect(Collectors.toSet()).size();

        SourceBoardConsultAllSaleInfo sourceBoardConsultAllSaleInfo = new SourceBoardConsultAllSaleInfo();
        sourceBoardConsultAllSaleInfo.setTotalGTV(totalGTV);
        sourceBoardConsultAllSaleInfo.setTotalOrderCnt(totalOrder);
        sourceBoardConsultAllSaleInfo.setTotalUserCnt(totalUserCnt);
        return sourceBoardConsultAllSaleInfo;
    }

    private SourceBoardConsultSeedSaleInfo convertSeedSaleInfo(List<PrivateLiveUserIntentionResult> userIntentionResultList,
                                                               List<PrivateLiveConsultantUserStatistics> userIntentionSaleList) {
        SourceBoardConsultSeedSaleInfo seedSaleInfo = SourceBoardConsultSeedSaleInfo.empty();
        if (CollectionUtils.isEmpty(userIntentionResultList) || CollectionUtils.isEmpty(userIntentionSaleList)) {
            return seedSaleInfo;
        }
        // 获取所有种子用户
        List<Long> allSeedUser = userIntentionResultList.stream().filter(e -> GroupUserTypeEnum.SEED_USER.getCode() == e.getUserType())
                .map(PrivateLiveUserIntentionResult::getUserId).collect(Collectors.toList());

        List<PrivateLiveConsultantUserStatistics> allSeedUserStatistic = userIntentionSaleList.stream().filter(e -> allSeedUser.contains(e.getMtUserId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allSeedUserStatistic)) {
            // 种子用户gtv
            long gtv = allSeedUserStatistic.stream().mapToLong(PrivateLiveConsultantUserStatistics::getGmvAmt).sum();
            seedSaleInfo.setSeedUserGTV(gtv);
            // 种子用户订单数
            int orderNum = allSeedUserStatistic.stream().mapToInt(PrivateLiveConsultantUserStatistics::getPayOrderCnt).sum();
            seedSaleInfo.setSeedUserOrderCnt(orderNum);
        }
        return seedSaleInfo;
    }

    private SourceBoardConsultFissionSaleInfo convertFissionSaleInfo(List<PrivateLiveUserIntentionResult> userIntentionResultList,
                                                                     List<PrivateLiveConsultantUserStatistics> userIntentionSaleList) {
        SourceBoardConsultFissionSaleInfo fissionSaleInfo = SourceBoardConsultFissionSaleInfo.empty();
        if (CollectionUtils.isEmpty(userIntentionResultList) || CollectionUtils.isEmpty(userIntentionSaleList)) {
            return fissionSaleInfo;
        }
        // 获取所有裂变用户
        List<Long> allFissionUser = userIntentionResultList.stream().filter(e -> GroupUserTypeEnum.FISSION_USER.getCode() == e.getUserType())
                .map(PrivateLiveUserIntentionResult::getUserId).collect(Collectors.toList());

        List<PrivateLiveConsultantUserStatistics> allFissionUserStatistic = userIntentionSaleList.stream().filter(e -> allFissionUser.contains(e.getMtUserId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allFissionUserStatistic)) {
            // 裂变用户gtv
            long gtv = allFissionUserStatistic.stream().mapToLong(PrivateLiveConsultantUserStatistics::getGmvAmt).sum();
            fissionSaleInfo.setFissionUserGTV(gtv);
            // 裂变用户订单数
            int orderNum = allFissionUserStatistic.stream().mapToInt(PrivateLiveConsultantUserStatistics::getPayOrderCnt).sum();
            fissionSaleInfo.setFissionUserOrderCnt(orderNum);
        }
        return fissionSaleInfo;
    }

    private SourceBoardConsultUnEnterGroupSaleInfo convertUnEnterSaleInfo(List<PrivateLiveUserIntentionResult> userIntentionResultList,
                                                                          List<PrivateLiveConsultantUserStatistics> userIntentionSaleList) {
        SourceBoardConsultUnEnterGroupSaleInfo unEnterSaleInfo = SourceBoardConsultUnEnterGroupSaleInfo.empty();
        if (CollectionUtils.isEmpty(userIntentionResultList) || CollectionUtils.isEmpty(userIntentionSaleList)) {
            return unEnterSaleInfo;
        }
        // 获取所有未进群用户
        List<Long> allUnEnterUser = userIntentionResultList.stream().filter(e -> GroupUserTypeEnum.LINK_USER.getCode() == e.getUserType())
                .map(PrivateLiveUserIntentionResult::getUserId).collect(Collectors.toList());

        List<PrivateLiveConsultantUserStatistics> allUnEnterUserStatistic = userIntentionSaleList.stream().filter(e -> allUnEnterUser.contains(e.getMtUserId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allUnEnterUserStatistic)) {
            // 未进群用户gtv
            long gtv = allUnEnterUserStatistic.stream().mapToLong(PrivateLiveConsultantUserStatistics::getGmvAmt).sum();
            unEnterSaleInfo.setUnEnterUserGTV(gtv);
            // 未进群用户订单数
            int orderNum = allUnEnterUserStatistic.stream().mapToInt(PrivateLiveConsultantUserStatistics::getPayOrderCnt).sum();
            unEnterSaleInfo.setUnEnterUserOrderCnt(orderNum);
        }
        return unEnterSaleInfo;
    }

    private int computeOffSet(int pageNo, int pageSize) {
        return (pageNo - 1) * pageSize;
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "pageQuerySourceBoardCustomerList",
            newProcessorMethod = "pageQuerySourceBoardCustomerListNew",
            oldProcessorMethod = "pageQuerySourceBoardCustomerListOld",
            paramParser = PageQuerySourceBoardCustomerListParamParser.class
    )
    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> pageQuerySourceBoardCustomerList(PrivateLiveSourceBoardCustomerRequest request) {
        try {
            // 验证请求参数
            validateRequest(request);
            Long mtUserId = null;

            // 获取美团用户id
            if (StringUtils.isNotBlank(request.getPhoneNumber())) {
                mtUserId = getMtUserId(request);
                if (null == mtUserId || mtUserId == 0L) {
                    return buildEmptyPageDataDTO(request);
                }
            }

            long total = 0;
            List<PrivateLiveUserIntentionResult> userIntentionResults = Lists.newArrayList();
            if (CollectionUtils.isEmpty(request.getTagIds())) {
                // 条件获取用户信息列表
                userIntentionResults = pageByUserIntentionResults(request, mtUserId, UserIntentionStatusEnum.VALID_STATUS_LIST);
                total = userIntentionResultRepository.countByCondition(request.getLiveId(), request.getWxNickname(), mtUserId,
                        request.getConsultantId(), request.getUserType(), UserIntentionStatusEnum.VALID_STATUS_LIST);
            } else {
                CustomerListByTagRequest tagRequest = new CustomerListByTagRequest();
                tagRequest.setLiveId(request.getLiveId());
                tagRequest.setConsultantTaskId(request.getConsultantId());
                tagRequest.setTagIdList(request.getTagIds());
                tagRequest.setPageNum(request.getPageNo());
                tagRequest.setPageSize(request.getPageSize());
                if (mtUserId != null && mtUserId > 0) {
                    tagRequest.setUserId(Lists.newArrayList(mtUserId));
                }
                if (StringUtils.isNotEmpty(request.getWxNickname())) {
                    tagRequest.setWxNickName(request.getWxNickname());
                }
                PageRemoteResponse<CommunityCustomerDTO> customerBaseInfo = privateSphereUserAclService.queryCustomerListByTag(tagRequest);

                if (customerBaseInfo == null || customerBaseInfo.getCode() != ResponseEnum.SUCCESS.code) {
                    return RemoteResponse.fail("查询社群信息失败");
                }
                total = customerBaseInfo.getTotalHit();
                if (total == 0 || CollectionUtils.isEmpty(customerBaseInfo.getData())) {
                    PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
                    PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO> pageDataDTO = new PageDataDTO<>();
                    pageDataDTO.setPageInfoDTO(pageInfoDTO);
                    pageDataDTO.setList(Lists.newArrayList());
                    return RemoteResponse.success(pageDataDTO);
                }

                // 条件获取用户信息列表
                List<String> wxIds = customerBaseInfo.getData().stream().map(CommunityCustomerDTO::getWxId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                List<Long> userIds = customerBaseInfo.getData().stream().map(CommunityCustomerDTO::getUserId).filter(Objects::nonNull).filter(e -> e > 0).collect(Collectors.toList());
                log.info("pageQuerySourceBoardCustomerList, wxIds: {}, userIds: {}", JSON.toJSONString(wxIds), JSON.toJSONString(userIds));
                userIntentionResults = userIntentionResultRepository.queryByWxIdOrUsers(request.getLiveId(), request.getConsultantId(), wxIds, userIds);
                log.info("pageQuerySourceBoardCustomerList, userIntention: {}", JSON.toJSONString(userIntentionResults));
            }

            return customerListStatistic(request, userIntentionResults, total);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#pageQuerySourceBoardCustomerList",
                    SimpleJacksonUtils.serialize(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> pageQuerySourceBoardCustomerListOld(PrivateLiveSourceBoardCustomerRequest request) {
        try {
            // 验证请求参数
            validateRequest(request);
            Long mtUserId = null;

            // 获取美团用户id
            if (StringUtils.isNotBlank(request.getPhoneNumber())) {
                mtUserId = getMtUserId(request);
                if (null == mtUserId || mtUserId == 0L) {
                    return buildEmptyPageDataDTO(request);
                }
            }

            long total = 0;
            List<PrivateLiveUserIntentionResult> userIntentionResults = Lists.newArrayList();
            if (CollectionUtils.isEmpty(request.getTagIds())) {
                // 条件获取用户信息列表
                userIntentionResults = pageByUserIntentionResults(request, mtUserId, UserIntentionStatusEnum.VALID_STATUS_LIST);
                total = userIntentionResultRepository.countByCondition(request.getLiveId(), request.getWxNickname(), mtUserId,
                        request.getConsultantId(), request.getUserType(), UserIntentionStatusEnum.VALID_STATUS_LIST);
            } else {
                CustomerListByTagRequest tagRequest = new CustomerListByTagRequest();
                tagRequest.setLiveId(request.getLiveId());
                tagRequest.setConsultantTaskId(request.getConsultantId());
                tagRequest.setTagIdList(request.getTagIds());
                tagRequest.setPageNum(request.getPageNo());
                tagRequest.setPageSize(request.getPageSize());
                if (mtUserId != null && mtUserId > 0) {
                    tagRequest.setUserId(Lists.newArrayList(mtUserId));
                }
                if (StringUtils.isNotEmpty(request.getWxNickname())) {
                    tagRequest.setWxNickName(request.getWxNickname());
                }
                PageRemoteResponse<CommunityCustomerDTO> customerBaseInfo = privateSphereUserAclService.queryCustomerListByTag(tagRequest);

                if (customerBaseInfo == null || customerBaseInfo.getCode() != ResponseEnum.SUCCESS.code) {
                    return RemoteResponse.fail("查询社群信息失败");
                }
                total = customerBaseInfo.getTotalHit();
                if (total == 0 || CollectionUtils.isEmpty(customerBaseInfo.getData())) {
                    PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
                    PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO> pageDataDTO = new PageDataDTO<>();
                    pageDataDTO.setPageInfoDTO(pageInfoDTO);
                    pageDataDTO.setList(Lists.newArrayList());
                    return RemoteResponse.success(pageDataDTO);
                }

                // 条件获取用户信息列表
                List<String> wxIds = customerBaseInfo.getData().stream().map(CommunityCustomerDTO::getWxId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                List<Long> userIds = customerBaseInfo.getData().stream().map(CommunityCustomerDTO::getUserId).filter(Objects::nonNull).filter(e -> e > 0).collect(Collectors.toList());
                log.info("pageQuerySourceBoardCustomerList, wxIds: {}, userIds: {}", JSON.toJSONString(wxIds), JSON.toJSONString(userIds));
                userIntentionResults = userIntentionResultRepository.queryByWxIdOrUsers(request.getLiveId(), request.getConsultantId(), wxIds, userIds);
                log.info("pageQuerySourceBoardCustomerList, userIntention: {}", JSON.toJSONString(userIntentionResults));
            }

            return customerListStatistic(request, userIntentionResults, total);
        } catch (Exception e) {
            log.error("pageQuerySourceBoardCustomerList.error.msg:{}",e.getMessage(),e);
            Cat.logErrorWithCategory(CAT_TYPE + "#pageQuerySourceBoardCustomerList",
                    SimpleJacksonUtils.serialize(request), e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> pageQuerySourceBoardCustomerListNew(PrivateLiveSourceBoardCustomerRequest request) {
        return dzPrivateLiveUserIntentionService.pageQuerySourceBoardCustomerList(request);
    }

    private RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> customerListStatistic(PrivateLiveSourceBoardCustomerRequest request, List<PrivateLiveUserIntentionResult> userIntentionResults, long total) throws Exception {
        if (CollectionUtils.isEmpty(userIntentionResults)) {
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(0).build();
            PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO> pageDataDTO = new PageDataDTO<>();
            pageDataDTO.setPageInfoDTO(pageInfoDTO);
            pageDataDTO.setList(Lists.newArrayList());
            return RemoteResponse.success(pageDataDTO);
        }

        // 获取用户基础信息映射
        CompletableFuture<HashMap<Long, PrivateLiveUserIntentionResult>> userInfoFur = CompletableFuture.supplyAsync(() ->
                getUserInfoMap(userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        // 获取咨询师基础数据映射
        CompletableFuture<Map<Pair<String, Long>, ConsultantBasicDataDTO.Response>> basicDataFur = CompletableFuture.supplyAsync(() ->
                getConsultantBasicDate(request, userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        // 获取用户归属咨询师的团长渠道信息
        CompletableFuture<Map<Long, DistributorGroupModel>> consultantDistributorFur = CompletableFuture.supplyAsync(() ->
                queryUserConsultantDistributorGroup(userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        // 获取用户统计表数据
        CompletableFuture<List<PrivateLiveConsultantUserStatistics>> userStatisticsFur = CompletableFuture.supplyAsync(() ->
                queryUserStatistics(request, userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        // 查询社群标签信息
        CompletableFuture<List<CommunityLiveUserTagDetailDTO>> userTagDetailFur = CompletableFuture.supplyAsync(() ->
                queryUserTagInfo(request.getLiveId(), userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        // 查询意向商品信息
        CompletableFuture<Map<String, SourceBoardIntentionProductInfo>> intentionProductsFur = CompletableFuture.supplyAsync(() ->
                queryIntentionProduct(request.getLiveId(), userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        // 查询上一级分享人
        CompletableFuture<Map<String, String>> invitorInfoFur = CompletableFuture.supplyAsync(() ->
                queryInvitorInfo(request.getLiveId(), userIntentionResults), LIVE_INTENTION_STATISTIC_POOL);

        CompletableFuture.allOf(userInfoFur, basicDataFur, consultantDistributorFur, userStatisticsFur, userTagDetailFur, intentionProductsFur, invitorInfoFur).get(3, TimeUnit.SECONDS);

        HashMap<Long, PrivateLiveUserIntentionResult> userInfoMap = userInfoFur.join();
        Map<Pair<String, Long>, ConsultantBasicDataDTO.Response> basicDataMap = basicDataFur.join();
        List<PrivateLiveConsultantUserStatistics> userStatistics = userStatisticsFur.join();
        Map<Long, DistributorGroupModel> distributorGroupModelMap = consultantDistributorFur.join();
        List<CommunityLiveUserTagDetailDTO> userTagDetail = userTagDetailFur.join();
        Map<String, SourceBoardIntentionProductInfo> intentionProducts = intentionProductsFur.join();
        Map<String, String> invitorInfo = invitorInfoFur.join();

        // 构建客资统计数据DTO列表
        List<PrivateLiveSourceBoardCustomerStatisticsDTO> dtos = buildCustomerStatisticsDTOs(userStatistics, userIntentionResults,
                userInfoMap, basicDataMap, distributorGroupModelMap, userTagDetail, intentionProducts, invitorInfo);

        PageInfoDTO pageInfoDTO = buildPageInfoDTO(request, total);
        return buildSuccessPageDataDTO(pageInfoDTO, dtos);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "querySourceBoardConsultList",
            newProcessorMethod = "querySourceBoardConsultListNew",
            oldProcessorMethod = "querySourceBoardConsultListOld",
            paramParser = QuerySourceBoardConsultListParamParser.class
    )
    public RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> querySourceBoardConsultList(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(Lists.newArrayList());
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.loadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds);
            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(Collections.EMPTY_LIST);
            }

            List<PrivateLiveSourceBoardConsultStatisticsDTO> statisticsDTOS = consultStatistics(request.getLiveId(), privateLiveConsultantTasks);
            return RemoteResponse.success(statisticsDTOS);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#querySourceBoardConsultList", SimpleJacksonUtils.serialize(request), e);
            log.error("querySourceBoardConsultList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> querySourceBoardConsultListOld(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(Lists.newArrayList());
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.loadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds);
            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(Collections.EMPTY_LIST);
            }

            List<PrivateLiveSourceBoardConsultStatisticsDTO> statisticsDTOS = consultStatistics(request.getLiveId(), privateLiveConsultantTasks);
            return RemoteResponse.success(statisticsDTOS);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#querySourceBoardConsultList", SimpleJacksonUtils.serialize(request), e);
            log.error("querySourceBoardConsultList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<List<PrivateLiveSourceBoardConsultStatisticsDTO>> querySourceBoardConsultListNew(PrivateLiveSourceBoardConsultRequest request) {
        return dzPrivateLiveUserIntentionService.querySourceBoardConsultList(request);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "queryTradeInfoByLiveId",
            newProcessorMethod = "queryTradeInfoByLiveIdNew",
            oldProcessorMethod = "queryTradeInfoByLiveIdOld",
            paramParser = QueryTradeInfoByLiveIdParamParser.class
    )
    public RemoteResponse<PrivateLiveSourceBoardTradeStatisticDTO> queryTradeInfoByLiveId(String liveId) {
        try {
            if (StringUtils.isBlank(liveId)) {
                return RemoteResponse.fail("liveId不能为空");
            }
            PrivateLiveSourceBoardTradeStatisticDTO statisticDTO = PrivateLiveSourceBoardTradeStatisticDTO.empty();

            // 查询直播间下的所有用户的交易数据
            List<ConsultantUserStatisticBO> userStatisticBOS = privateLiveConsultantSummaryRepository.queryLiveUserStatistic(liveId);
            if (CollectionUtils.isEmpty(userStatisticBOS)) {
                return RemoteResponse.success(PrivateLiveSourceBoardTradeStatisticDTO.empty());
            }
            List<Long> mtUserIds = userStatisticBOS.stream()
                    .map(ConsultantUserStatisticBO::getMtUserId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, Integer> userId2Type = batchQueryUserType(liveId, mtUserIds);
            for (ConsultantUserStatisticBO userStatisticBO : userStatisticBOS) {
                if (Objects.nonNull(userId2Type.get(userStatisticBO.getMtUserId()))) {
                    userStatisticBO.setUserType(userId2Type.get(userStatisticBO.getMtUserId()));
                }
            }

            // 按用户类型groupBy
            Map<Integer, List<ConsultantUserStatisticBO>> userType2Statistic = userStatisticBOS.stream()
                    .filter(e -> Objects.nonNull(e.getUserType()))
                    .filter(e -> e.getConsultantTaskId() > 0)
                    .collect(Collectors.groupingBy(ConsultantUserStatisticBO::getUserType));

            // 总交易
            SourceBoardConsultAllSaleInfo allSaleInfo = SourceBoardConsultAllSaleInfo.empty();
            allSaleInfo.setTotalGTV(userStatisticBOS.stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
            allSaleInfo.setTotalOrderCnt(userStatisticBOS.stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            statisticDTO.setAllSaleInfo(allSaleInfo);

            // 种子用户交易
            SourceBoardConsultSeedSaleInfo seedSaleInfo = SourceBoardConsultSeedSaleInfo.empty();
            if (CollectionUtils.isNotEmpty(userType2Statistic.get(GroupUserTypeEnum.SEED_USER.getCode()))) {
                seedSaleInfo.setSeedUserGTV(userType2Statistic.get(GroupUserTypeEnum.SEED_USER.getCode()).stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
                seedSaleInfo.setSeedUserOrderCnt(userType2Statistic.get(GroupUserTypeEnum.SEED_USER.getCode()).stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            }
            statisticDTO.setSeedSaleInfo(seedSaleInfo);

            // 裂变用户交易
            SourceBoardConsultFissionSaleInfo fissionSaleInfo = SourceBoardConsultFissionSaleInfo.empty();
            if (CollectionUtils.isNotEmpty(userType2Statistic.get(GroupUserTypeEnum.FISSION_USER.getCode()))) {
                fissionSaleInfo.setFissionUserGTV(userType2Statistic.get(GroupUserTypeEnum.FISSION_USER.getCode()).stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
                fissionSaleInfo.setFissionUserOrderCnt(userType2Statistic.get(GroupUserTypeEnum.FISSION_USER.getCode()).stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            }
            statisticDTO.setFissionSaleInfo(fissionSaleInfo);

            // 未进群用户交易
            SourceBoardConsultUnEnterGroupSaleInfo unEnterGroupSaleInfo = SourceBoardConsultUnEnterGroupSaleInfo.empty();
            if (CollectionUtils.isNotEmpty(userType2Statistic.get(GroupUserTypeEnum.LINK_USER.getCode()))) {
                unEnterGroupSaleInfo.setUnEnterUserGTV(userType2Statistic.get(GroupUserTypeEnum.LINK_USER.getCode()).stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
                unEnterGroupSaleInfo.setUnEnterUserOrderCnt(userType2Statistic.get(GroupUserTypeEnum.LINK_USER.getCode()).stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            }
            statisticDTO.setUnEnterGroupSaleInfo(unEnterGroupSaleInfo);
            return RemoteResponse.success(statisticDTO);
        } catch (Exception e) {
            log.error("queryTradeInfoByLiveId, query fail, liveId: {}", liveId, e);
            return RemoteResponse.fail("查询失败");
        }
    }

    public RemoteResponse<PrivateLiveSourceBoardTradeStatisticDTO> queryTradeInfoByLiveIdOld(String liveId) {
        try {
            if (StringUtils.isBlank(liveId)) {
                return RemoteResponse.fail("liveId不能为空");
            }
            PrivateLiveSourceBoardTradeStatisticDTO statisticDTO = PrivateLiveSourceBoardTradeStatisticDTO.empty();

            // 查询直播间下的所有用户的交易数据
            List<ConsultantUserStatisticBO> userStatisticBOS = privateLiveConsultantSummaryRepository.queryLiveUserStatistic(liveId);
            if (CollectionUtils.isEmpty(userStatisticBOS)) {
                return RemoteResponse.success(PrivateLiveSourceBoardTradeStatisticDTO.empty());
            }
            List<Long> mtUserIds = userStatisticBOS.stream()
                    .map(ConsultantUserStatisticBO::getMtUserId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, Integer> userId2Type = batchQueryUserType(liveId, mtUserIds);
            for (ConsultantUserStatisticBO userStatisticBO : userStatisticBOS) {
                if (Objects.nonNull(userId2Type.get(userStatisticBO.getMtUserId()))) {
                    userStatisticBO.setUserType(userId2Type.get(userStatisticBO.getMtUserId()));
                }
            }

            // 按用户类型groupBy
            Map<Integer, List<ConsultantUserStatisticBO>> userType2Statistic = userStatisticBOS.stream()
                    .filter(e -> Objects.nonNull(e.getUserType()))
                    .filter(e -> e.getConsultantTaskId() > 0)
                    .collect(Collectors.groupingBy(ConsultantUserStatisticBO::getUserType));

            // 总交易
            SourceBoardConsultAllSaleInfo allSaleInfo = SourceBoardConsultAllSaleInfo.empty();
            allSaleInfo.setTotalGTV(userStatisticBOS.stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
            allSaleInfo.setTotalOrderCnt(userStatisticBOS.stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            statisticDTO.setAllSaleInfo(allSaleInfo);

            // 种子用户交易
            SourceBoardConsultSeedSaleInfo seedSaleInfo = SourceBoardConsultSeedSaleInfo.empty();
            if (CollectionUtils.isNotEmpty(userType2Statistic.get(GroupUserTypeEnum.SEED_USER.getCode()))) {
                seedSaleInfo.setSeedUserGTV(userType2Statistic.get(GroupUserTypeEnum.SEED_USER.getCode()).stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
                seedSaleInfo.setSeedUserOrderCnt(userType2Statistic.get(GroupUserTypeEnum.SEED_USER.getCode()).stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            }
            statisticDTO.setSeedSaleInfo(seedSaleInfo);

            // 裂变用户交易
            SourceBoardConsultFissionSaleInfo fissionSaleInfo = SourceBoardConsultFissionSaleInfo.empty();
            if (CollectionUtils.isNotEmpty(userType2Statistic.get(GroupUserTypeEnum.FISSION_USER.getCode()))) {
                fissionSaleInfo.setFissionUserGTV(userType2Statistic.get(GroupUserTypeEnum.FISSION_USER.getCode()).stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
                fissionSaleInfo.setFissionUserOrderCnt(userType2Statistic.get(GroupUserTypeEnum.FISSION_USER.getCode()).stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            }
            statisticDTO.setFissionSaleInfo(fissionSaleInfo);

            // 未进群用户交易
            SourceBoardConsultUnEnterGroupSaleInfo unEnterGroupSaleInfo = SourceBoardConsultUnEnterGroupSaleInfo.empty();
            if (CollectionUtils.isNotEmpty(userType2Statistic.get(GroupUserTypeEnum.LINK_USER.getCode()))) {
                unEnterGroupSaleInfo.setUnEnterUserGTV(userType2Statistic.get(GroupUserTypeEnum.LINK_USER.getCode()).stream().mapToLong(ConsultantUserStatisticBO::getGmvAmt).sum());
                unEnterGroupSaleInfo.setUnEnterUserOrderCnt(userType2Statistic.get(GroupUserTypeEnum.LINK_USER.getCode()).stream().mapToInt(ConsultantUserStatisticBO::getPayOrderCnt).sum());
            }
            statisticDTO.setUnEnterGroupSaleInfo(unEnterGroupSaleInfo);
            return RemoteResponse.success(statisticDTO);
        } catch (Exception e) {
            log.error("queryTradeInfoByLiveId, query fail, liveId: {}", liveId, e);
            return RemoteResponse.fail("查询失败");
        }
    }

    public RemoteResponse<PrivateLiveSourceBoardTradeStatisticDTO> queryTradeInfoByLiveIdNew(String liveId) {
        return dzPrivateLiveUserIntentionService.queryTradeInfoByLiveId(liveId);
    }

    private Map<Long, Integer> batchQueryUserType(String liveId, List<Long> userIds) {
        try {
            if (CollectionUtils.isEmpty(userIds)) {
                return Maps.newHashMap();
            }
            List<List<Long>> partitionList = Lists.partition(userIds, 200);
            List<CompletableFuture<Map<Long, Integer>>> futures = new ArrayList<>();
            for (List<Long> partition : partitionList) {
                futures.add(CompletableFuture.supplyAsync(() -> userIntentionResultRepository.queryUserIntentionType(liveId, partition),
                        BATCH_QUERY_THREADPOOL));
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(5, TimeUnit.SECONDS);
            return futures.stream()
                    .map(CompletableFuture::join)
                    .map(Map::entrySet)
                    .flatMap(Set::stream)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("batchQueryUserType, query fail, liveId: {}", liveId, e);
            return Maps.newHashMap();
        }
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "queryTagUserDetailByTaskId",
            newProcessorMethod = "queryTagUserDetailByTaskIdNew",
            oldProcessorMethod = "queryTagUserDetailByTaskIdOld",
            paramParser = QueryTagUserDetailByTaskIdParamParser.class
    )
    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardTagUserDTO>> queryTagUserDetailByTaskId(PrivateLiveSourceBoardTagUserRequest request) {
        try {
            if (StringUtils.isBlank(request.getLiveId()) || request.getConsultantTaskId() == null || request.getTagId() == null) {
                return RemoteResponse.fail("参数非法");
            }

            // 查询咨询师所有的待跟进用户
            CommunityUserQueryByTagRequest tagRequest = new CommunityUserQueryByTagRequest();
            tagRequest.setLiveId(request.getLiveId());
            tagRequest.setConsultantTaskId(request.getConsultantTaskId());
            tagRequest.setTagId(request.getTagId());
            tagRequest.setPageNo(request.getPageNo());
            tagRequest.setPageSize(request.getPageSize());

            PageDataDTO<CommunityUserSimpleDTO> tagUser = privateSphereUserAclService.queryConsultantUserWithTag(tagRequest);
            if (tagUser == null) {
                return RemoteResponse.fail("未查询到特征用户");
            }

            List<PrivateLiveSourceBoardTagUserDTO> unfollowUserDTOS = Lists.newArrayList();
            if (CollectionUtils.isEmpty(tagUser.getList())) {
                PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(tagUser.getPageInfoDTO().getTotalCount()).build();
                PageDataDTO<PrivateLiveSourceBoardTagUserDTO> pageDataDTO = new PageDataDTO<>();
                pageDataDTO.setPageInfoDTO(pageInfoDTO);
                pageDataDTO.setList(unfollowUserDTOS);
                return RemoteResponse.success(pageDataDTO);
            }

            // 获取所有的userId
            List<Long> userIds = tagUser.getList().stream().map(CommunityUserSimpleDTO::getUserId).filter(Objects::nonNull).filter(e -> e > 0).collect(Collectors.toList());
            List<String> unionIds = tagUser.getList().stream().map(CommunityUserSimpleDTO::getUnionId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<String> wxIds = tagUser.getList().stream().map(CommunityUserSimpleDTO::getWxId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<String> onlyWxIds = tagUser.getList().stream().filter(e -> (StringUtils.isEmpty(e.getUnionId()) && StringUtils.isNotEmpty(e.getWxId()))).map(CommunityUserSimpleDTO::getWxId).collect(Collectors.toList());


            // 获取用户基础信息映射
            CompletableFuture<List<PrivateLiveUserIntentionResult>> userInfoFur = CompletableFuture.supplyAsync(() ->
                    userIntentionResultRepository.queryByWxIdOrUsers(request.getLiveId(), request.getConsultantTaskId(), wxIds, userIds), LIVE_INTENTION_STATISTIC_POOL);


            // 获取用户统计表数据
            CompletableFuture<List<PrivateLiveConsultantUserStatistics>> userStatisticsFur = CompletableFuture.supplyAsync(() ->
                    privateLiveConsultantSummaryRepository.queryLoadByConsultantAndMtUserIds(request.getLiveId(), request.getConsultantTaskId(), userIds), LIVE_INTENTION_STATISTIC_POOL);

            // 查询社群标签信息
            CompletableFuture<List<CommunityLiveUserTagDetailDTO>> userTagDetailFur = CompletableFuture.supplyAsync(() ->
                    queryUserTagDetail(request.getLiveId(), unionIds, onlyWxIds), LIVE_INTENTION_STATISTIC_POOL);

            CompletableFuture.allOf(userInfoFur, userStatisticsFur, userTagDetailFur).get(3, TimeUnit.SECONDS);

            List<PrivateLiveUserIntentionResult> userIntentionResultList = userInfoFur.join();
            List<PrivateLiveConsultantUserStatistics> userStatistics = userStatisticsFur.join();
            List<CommunityLiveUserTagDetailDTO> tagDetail = userTagDetailFur.join();

            List<PrivateLiveSourceBoardTagUserDTO> unfollowRes = buildTagUserDTO(tagUser.getList(), userIntentionResultList, userStatistics, tagDetail);
            PageDataDTO<PrivateLiveSourceBoardTagUserDTO> unfollowUserDTOPageDataDTO = new PageDataDTO<>();
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(tagUser.getPageInfoDTO().getTotalCount()).build();
            unfollowUserDTOPageDataDTO.setList(unfollowRes);
            unfollowUserDTOPageDataDTO.setPageInfoDTO(pageInfoDTO);
            return RemoteResponse.success(unfollowUserDTOPageDataDTO);
        } catch (Exception e) {
            log.error("queryTagUserDetailByTaskId, query fail, request: {}", request, e);
            return RemoteResponse.fail("查询失败");
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardTagUserDTO>> queryTagUserDetailByTaskIdOld(PrivateLiveSourceBoardTagUserRequest request) {
        try {
            if (StringUtils.isBlank(request.getLiveId()) || request.getConsultantTaskId() == null || request.getTagId() == null) {
                return RemoteResponse.fail("参数非法");
            }

            // 查询咨询师所有的待跟进用户
            CommunityUserQueryByTagRequest tagRequest = new CommunityUserQueryByTagRequest();
            tagRequest.setLiveId(request.getLiveId());
            tagRequest.setConsultantTaskId(request.getConsultantTaskId());
            tagRequest.setTagId(request.getTagId());
            tagRequest.setPageNo(request.getPageNo());
            tagRequest.setPageSize(request.getPageSize());

            PageDataDTO<CommunityUserSimpleDTO> tagUser = privateSphereUserAclService.queryConsultantUserWithTag(tagRequest);
            if (tagUser == null) {
                return RemoteResponse.fail("未查询到特征用户");
            }

            List<PrivateLiveSourceBoardTagUserDTO> unfollowUserDTOS = Lists.newArrayList();
            if (CollectionUtils.isEmpty(tagUser.getList())) {
                PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(tagUser.getPageInfoDTO().getTotalCount()).build();
                PageDataDTO<PrivateLiveSourceBoardTagUserDTO> pageDataDTO = new PageDataDTO<>();
                pageDataDTO.setPageInfoDTO(pageInfoDTO);
                pageDataDTO.setList(unfollowUserDTOS);
                return RemoteResponse.success(pageDataDTO);
            }

            // 获取所有的userId
            List<Long> userIds = tagUser.getList().stream().map(CommunityUserSimpleDTO::getUserId).filter(Objects::nonNull).filter(e -> e > 0).collect(Collectors.toList());
            List<String> unionIds = tagUser.getList().stream().map(CommunityUserSimpleDTO::getUnionId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<String> wxIds = tagUser.getList().stream().map(CommunityUserSimpleDTO::getWxId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<String> onlyWxIds = tagUser.getList().stream().filter(e -> (StringUtils.isEmpty(e.getUnionId()) && StringUtils.isNotEmpty(e.getWxId()))).map(CommunityUserSimpleDTO::getWxId).collect(Collectors.toList());


            // 获取用户基础信息映射
            CompletableFuture<List<PrivateLiveUserIntentionResult>> userInfoFur = CompletableFuture.supplyAsync(() ->
                    userIntentionResultRepository.queryByWxIdOrUsers(request.getLiveId(), request.getConsultantTaskId(), wxIds, userIds), LIVE_INTENTION_STATISTIC_POOL);


            // 获取用户统计表数据
            CompletableFuture<List<PrivateLiveConsultantUserStatistics>> userStatisticsFur = CompletableFuture.supplyAsync(() ->
                    privateLiveConsultantSummaryRepository.queryLoadByConsultantAndMtUserIds(request.getLiveId(), request.getConsultantTaskId(), userIds), LIVE_INTENTION_STATISTIC_POOL);

            // 查询社群标签信息
            CompletableFuture<List<CommunityLiveUserTagDetailDTO>> userTagDetailFur = CompletableFuture.supplyAsync(() ->
                    queryUserTagDetail(request.getLiveId(), unionIds, onlyWxIds), LIVE_INTENTION_STATISTIC_POOL);

            CompletableFuture.allOf(userInfoFur, userStatisticsFur, userTagDetailFur).get(3, TimeUnit.SECONDS);

            List<PrivateLiveUserIntentionResult> userIntentionResultList = userInfoFur.join();
            List<PrivateLiveConsultantUserStatistics> userStatistics = userStatisticsFur.join();
            List<CommunityLiveUserTagDetailDTO> tagDetail = userTagDetailFur.join();

            List<PrivateLiveSourceBoardTagUserDTO> unfollowRes = buildTagUserDTO(tagUser.getList(), userIntentionResultList, userStatistics, tagDetail);
            PageDataDTO<PrivateLiveSourceBoardTagUserDTO> unfollowUserDTOPageDataDTO = new PageDataDTO<>();
            PageInfoDTO pageInfoDTO = PageInfoDTO.builder().totalCount(tagUser.getPageInfoDTO().getTotalCount()).build();
            unfollowUserDTOPageDataDTO.setList(unfollowRes);
            unfollowUserDTOPageDataDTO.setPageInfoDTO(pageInfoDTO);
            return RemoteResponse.success(unfollowUserDTOPageDataDTO);
        } catch (Exception e) {
            log.error("queryTagUserDetailByTaskId, query fail, request: {}", request, e);
            return RemoteResponse.fail("查询失败");
        }
    }

    public RemoteResponse<PageDataDTO<PrivateLiveSourceBoardTagUserDTO>> queryTagUserDetailByTaskIdNew(PrivateLiveSourceBoardTagUserRequest request) {
        return dzPrivateLiveUserIntentionService.queryTagUserDetailByTaskId(request);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "querySourceBoardConsultUserList",
            newProcessorMethod = "querySourceBoardConsultUserListNew",
            oldProcessorMethod = "querySourceBoardConsultUserListOld",
            paramParser = QuerySourceBoardConsultUserListParamParser.class
    )
    public RemoteResponse<List<PrivateLiveSourceBoardConsultUserStatisticsDTO>> querySourceBoardConsultUserList(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(Lists.newArrayList());
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.loadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds);
            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(Lists.newArrayList());
            }

            List<PrivateLiveSourceBoardConsultUserStatisticsDTO> statisticsDTOS = consultUserStatistics(request.getLiveId(), privateLiveConsultantTasks);
            return RemoteResponse.success(statisticsDTOS);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#querySourceBoardConsultUserList", SimpleJacksonUtils.serialize(request), e);
            log.error("querySourceBoardConsultUserList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<List<PrivateLiveSourceBoardConsultUserStatisticsDTO>> querySourceBoardConsultUserListOld(PrivateLiveSourceBoardConsultRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getLiveId())) {
                return RemoteResponse.fail("liveId不能为空");
            }

            List<Long> taskIds = Lists.newArrayList();
            if (Objects.nonNull(request.getTaskType()) || CollectionUtils.isNotEmpty(request.getDistributorGroupIds())) {
                taskIds = getTaskIdByDistributorId(request.getLiveId(), request.getTaskType(), request.getDistributorGroupIds());
                if (CollectionUtils.isEmpty(taskIds)) {
                    return RemoteResponse.success(Lists.newArrayList());
                }
            }

            // 召回本场直播全量咨询师 (liveid + taskid)
            List<PrivateLiveConsultantTask> privateLiveConsultantTasks = privateLiveConsultantTaskRepository.loadByCondition(request.getLiveId(), request.getConsultName(),
                    request.getConsultPhone(), taskIds);
            if (CollectionUtils.isEmpty(privateLiveConsultantTasks)) {
                return RemoteResponse.success(Lists.newArrayList());
            }

            List<PrivateLiveSourceBoardConsultUserStatisticsDTO> statisticsDTOS = consultUserStatistics(request.getLiveId(), privateLiveConsultantTasks);
            return RemoteResponse.success(statisticsDTOS);
        } catch (Exception e) {
            Cat.logErrorWithCategory(CAT_TYPE + "#querySourceBoardConsultUserList", SimpleJacksonUtils.serialize(request), e);
            log.error("querySourceBoardConsultUserList fail", e);
            return RemoteResponse.fail(e.getMessage());
        }
    }

    public RemoteResponse<List<PrivateLiveSourceBoardConsultUserStatisticsDTO>> querySourceBoardConsultUserListNew(PrivateLiveSourceBoardConsultRequest request) {
        return dzPrivateLiveUserIntentionService.querySourceBoardConsultUserList(request);
    }

    @Override
    @FlowControl(migrationCode = "com.sankuai.dzusergrowth.privatelive",
            moduleName = "queryUserIntentionTypeByUserIds",
            newProcessorMethod = "queryUserIntentionTypeByUserIdsNew",
            oldProcessorMethod = "queryUserIntentionTypeByUserIdsOld",
            paramParser = QueryUserIntentionTypeByUserIdsParamParser.class
    )
    public RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> queryUserIntentionTypeByUserIds(QueryUserIntentionTypeByUserIdsRequest request) {
        validateRequest(request);
        String liveId = request.getLiveId();
        List<Long> userIds = request.getUserIds();

        List<PrivateLiveUserIntentionResult> privateLiveUserIntentionResults = userIntentionResultRepository.batchQueryByUserId(liveId, userIds);
        if (CollectionUtils.isEmpty(privateLiveUserIntentionResults)) {
            return RemoteResponse.success(Lists.newArrayList());
        }

        // 去重
        List<QueryUserIntentionTypeByUserIdsResponse> responses = privateLiveUserIntentionResults.stream().map(o -> {
            QueryUserIntentionTypeByUserIdsResponse dto = new QueryUserIntentionTypeByUserIdsResponse();
            BeanUtils.copyProperties(o, dto);
            return dto;
        }).distinct().collect(Collectors.toList());

        // 补充咨询师信息
        Set<Long> existUserIds = responses.stream().map(QueryUserIntentionTypeByUserIdsResponse::getUserId).collect(Collectors.toSet());
        for (Long userId : userIds) {
            if (existUserIds.contains(userId)) {
                continue;
            }
            String unionId = wechatUserAclService.queryMtUnionIdByUserId(userId);
            RemoteResponse<Long> checkResponse = privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(unionId, liveId);
            if (checkResponse.isSuccess() && checkResponse.getData() > 0) {
                responses.add(QueryUserIntentionTypeByUserIdsResponse.builder()
                        .userType(GroupUserTypeEnum.CONSULTANT.getCode())
                        .liveId(liveId)
                        .userId(userId)
                        .build());
            }
            existUserIds.add(userId);
        }
        return RemoteResponse.success(responses);
    }

    public RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> queryUserIntentionTypeByUserIdsOld(QueryUserIntentionTypeByUserIdsRequest request) {
        validateRequest(request);
        String liveId = request.getLiveId();
        List<Long> userIds = request.getUserIds();

        List<PrivateLiveUserIntentionResult> privateLiveUserIntentionResults = userIntentionResultRepository.batchQueryByUserId(liveId, userIds);
        if (CollectionUtils.isEmpty(privateLiveUserIntentionResults)) {
            return RemoteResponse.success(Lists.newArrayList());
        }

        // 去重
        List<QueryUserIntentionTypeByUserIdsResponse> responses = privateLiveUserIntentionResults.stream().map(o -> {
            QueryUserIntentionTypeByUserIdsResponse dto = new QueryUserIntentionTypeByUserIdsResponse();
            BeanUtils.copyProperties(o, dto);
            return dto;
        }).distinct().collect(Collectors.toList());

        // 补充咨询师信息
        Set<Long> existUserIds = responses.stream().map(QueryUserIntentionTypeByUserIdsResponse::getUserId).collect(Collectors.toSet());
        for (Long userId : userIds) {
            if (existUserIds.contains(userId)) {
                continue;
            }
            String unionId = wechatUserAclService.queryMtUnionIdByUserId(userId);
            RemoteResponse<Long> checkResponse = privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(unionId, liveId);
            if (checkResponse.isSuccess() && checkResponse.getData() > 0) {
                responses.add(QueryUserIntentionTypeByUserIdsResponse.builder()
                        .userType(GroupUserTypeEnum.CONSULTANT.getCode())
                        .liveId(liveId)
                        .userId(userId)
                        .build());
            }
            existUserIds.add(userId);
        }
        return RemoteResponse.success(responses);
    }

    public RemoteResponse<List<QueryUserIntentionTypeByUserIdsResponse>> queryUserIntentionTypeByUserIdsNew(QueryUserIntentionTypeByUserIdsRequest request) {
        return dzPrivateLiveUserIntentionService.queryUserIntentionTypeByUserIds(request);
    }

    private static void validateRequest(QueryUserIntentionTypeByUserIdsRequest request) {
        String liveId = request.getLiveId();
        List<Long> userIds = request.getUserIds();
        Preconditions.checkArgument(userIds.size() <= 20, "单次查询不能超过20个");
        Preconditions.checkArgument(StringUtils.isNotEmpty(liveId), "liveId不能为空");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(userIds), "userIds不能为空");
    }


    /**
     * 验证请求参数
     *
     * @param request 客户请求对象
     */
    private void validateRequest(PrivateLiveSourceBoardCustomerRequest request) {
        if (null == request || StringUtils.isBlank(request.getLiveId())) {
            throw new IllegalArgumentException("liveId不能为空");
        }
        if (request.getPageNo() <= 0 || request.getPageSize() <= 0 || request.getPageSize() > 50) {
            throw new IllegalArgumentException("分页参数错误");
        }
    }

    /**
     * 根据请求获取咨询师任务ID
     *
     * @param request 客户请求对象
     * @return 咨询师任务ID，如果没有找到则返回null
     */
    private Long getConsultantTaskId(PrivateLiveSourceBoardCustomerRequest request) {
        if (request.getConsultantId() != null) {
            PrivateLiveConsultantTask consultantTask = privateLiveConsultantTaskRepository
                    .loadByConsultantIdAndLiveId(request.getConsultantId(), request.getLiveId());
            if (consultantTask != null) {
                return consultantTask.getId();
            }
        }
        return null;
    }

    /**
     * 根据请求中的手机号码获取美团用户ID
     *
     * @param request 客户请求对象
     * @return 美团用户ID，如果没有找到则返回null
     */
    private Long getMtUserId(PrivateLiveSourceBoardCustomerRequest request) {
        UserDto mtUserDto = userAclService.getUserByMobile(PlatformEnum.MT.getCode(), request.getPhoneNumber());
        if (mtUserDto != null) {
            return mtUserDto.getUserId();
        } else {
            return 0L;
        }
    }

    /**
     * 获取用户意向结果列表
     *
     * @param request  客户请求对象
     * @param mtUserId 美团用户ID
     * @param status   状态列表
     * @return 用户意向结果列表
     */
    private List<PrivateLiveUserIntentionResult> pageByUserIntentionResults(PrivateLiveSourceBoardCustomerRequest request, Long mtUserId, List<Integer> status) {
        List<PrivateLiveUserIntentionResult> userIntentionResults = userIntentionResultRepository.pageByCondition(request.getLiveId(), request.getWxNickname(),
                mtUserId, request.getConsultantId(), request.getUserType(), status, request.getSortMap(), computeOffSet(request.getPageNo(), request.getPageSize()), request.getPageSize());
        return userIntentionResults;
    }

    /**
     * 获取咨询师基础数据DTO
     *
     * @param liveId               客户请求对象
     * @param userIntentionResults 用户意向结果列表
     * @return 咨询师基础数据DTO
     */
    private ConsultantBasicDataDTO getConsultantBasicDataDTO(String liveId, List<PrivateLiveUserIntentionResult> userIntentionResults) {
        try {
            List<List<PrivateLiveUserIntentionResult>> userIntentionPartition = Lists.partition(userIntentionResults, 10);
            List<CompletableFuture<ConsultantBasicDataDTO>> completableFutures = Lists.newArrayList();
            for (List<PrivateLiveUserIntentionResult> userIntentionResultList : userIntentionPartition) {
                completableFutures.add(CompletableFuture.supplyAsync(() -> {
                    ConsultantBasicDataRequest basicDataRequest = new ConsultantBasicDataRequest();
                    basicDataRequest.setLiveId(liveId);
                    List<ConsultantBasicDataRequest.Request> requests = userIntentionResultList.stream().map(
                            result -> {
                                ConsultantBasicDataRequest.Request subReq = new ConsultantBasicDataRequest.Request();
                                subReq.setConsultantTaskId(result.getConsultantTaskId());
                                subReq.setUserId(result.getUserId());
                                subReq.setUnionId(result.getUnionId());
                                subReq.setWxId(result.getWxId());
                                return subReq;
                            }
                    ).collect(Collectors.toList());
                    basicDataRequest.setData(requests);
                    return privateSphereUserAclService.queryConsultantBasicData(basicDataRequest);
                }, LIVE_CONSULTANT_QUERY_POOL));
            }
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get(3, TimeUnit.SECONDS);
            List<List<ConsultantBasicDataDTO.Response>> response = completableFutures.stream()
                    .map(CompletableFuture::join).filter(Objects::nonNull).map(ConsultantBasicDataDTO::getResponse).collect(Collectors.toList());
            ConsultantBasicDataDTO basicDataDTO = new ConsultantBasicDataDTO();
            basicDataDTO.setLiveId(liveId);
            basicDataDTO.setResponse(response.stream().flatMap(List::stream).collect(Collectors.toList()));
            log.info("getConsultantBasicDataDTO, user: {}, res: {}", JSON.toJSONString(userIntentionResults), JSON.toJSONString(basicDataDTO));
            return basicDataDTO;
        } catch (Exception e) {
            log.error("getConsultantBasicDataDTO, query fail, liveId: {}, user: {}", liveId, JSON.toJSONString(userIntentionResults), e);
            return null;
        }

    }

    /**
     * 构建咨询师基础数据映射
     *
     * @param basicDataDTO 咨询师基础数据DTO
     * @return 基础数据映射，键为用户ID，值为基础数据响应对象
     */
    private Map<Pair<String, Long>, ConsultantBasicDataDTO.Response> getBasicDataMap(ConsultantBasicDataDTO basicDataDTO) {
        Map<Pair<String, Long>, ConsultantBasicDataDTO.Response> basicDataMap = new HashMap<>();
        if (basicDataDTO != null && CollectionUtils.isNotEmpty(basicDataDTO.getResponse())) {
            basicDataDTO.getResponse().stream().filter(Objects::nonNull).forEach(it -> {
                basicDataMap.put(Pair.of(it.getWxId(), it.getUserId()), it);
            });
        }
        return basicDataMap;
    }

    /**
     * 获取用户统计数据列表
     *
     * @param request 客户请求对象
     * @param userIds 用户id列表
     * @return 用户统计数据列表
     */
    private List<PrivateLiveConsultantUserStatistics> getUserStatistics(PrivateLiveSourceBoardCustomerRequest request, List<Long> userIds) {
        return privateLiveConsultantSummaryRepository.queryLoadByMtUserIds(request.getLiveId(), userIds);
    }

    /**
     * 构建用户信息映射
     *
     * @param resultList 用户信息列表
     * @return 用户信息映射，键为用户ID，值为用户信息对象
     */
    private HashMap<Long, PrivateLiveUserIntentionResult> getUserInfoMap(List<PrivateLiveUserIntentionResult> resultList) {
        HashMap<Long, PrivateLiveUserIntentionResult> userInfoMap = new HashMap<>();
        resultList.stream().forEach(info -> {
            userInfoMap.put(info.getUserId(), info);
        });
        return userInfoMap;
    }

    private Map<Long, DistributorGroupModel> queryUserConsultantDistributorGroup(List<PrivateLiveUserIntentionResult> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return Maps.newHashMap();
        }
        List<Long> taskIds = resultList.stream()
                .filter(e -> e.getConsultantTaskId() > 0)
                .map(PrivateLiveUserIntentionResult::getConsultantTaskId)
                .collect(Collectors.toList());
        List<PrivateLiveConsultantTask> taskList = privateLiveConsultantTaskRepository.batchLoadByTaskIds(taskIds);
        return queryConsultantDistributor(taskList);
    }

    /**
     * 构建客户统计数据DTO列表
     *
     * @param userStatistics 用户统计数据列表
     * @param userInfoMap    用户信息映射
     * @param basicDataMap   基础数据映射
     * @return 客户统计数据DTO列表
     */
    private List<PrivateLiveSourceBoardCustomerStatisticsDTO> buildCustomerStatisticsDTOs(
            List<PrivateLiveConsultantUserStatistics> userStatistics,
            List<PrivateLiveUserIntentionResult> userIntentionResults,
            HashMap<Long, PrivateLiveUserIntentionResult> userInfoMap,
            Map<Pair<String, Long>, ConsultantBasicDataDTO.Response> basicDataMap,
            Map<Long, DistributorGroupModel> distributorGroupModelMap,
            List<CommunityLiveUserTagDetailDTO> userTagDetail,
            Map<String, SourceBoardIntentionProductInfo> intentionProducts,
            Map<String, String> invitorInfo) {

        HashMap<Long, PrivateLiveConsultantUserStatistics> statisticsMap = new HashMap<>();
        userStatistics.stream()
                // 获取当前归因的统计数据
                .filter(statistics -> userInfoMap.containsKey(statistics.getMtUserId()) && statistics
                        .getConsultantTaskId().equals(userInfoMap.get(statistics.getMtUserId()).getConsultantTaskId()))
                .forEach(statistics -> statisticsMap.put(statistics.getMtUserId(), statistics));

        Map<String, List<CommunityLiveUserTagDetailDTO.TagDetail>> unionId2Tag = userTagDetail.stream().filter(e -> StringUtils.isNotEmpty(e.getUnionId()))
                .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO::getUnionId, CommunityLiveUserTagDetailDTO::getTagIds, (v1, v2) -> v1));
        Map<String, List<CommunityLiveUserTagDetailDTO.TagDetail>> wxId2Tag = userTagDetail.stream().filter(e -> StringUtils.isNotEmpty(e.getWxId()))
                .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO::getWxId, CommunityLiveUserTagDetailDTO::getTagIds, (v1, v2) -> v1));

        return userIntentionResults.stream().map(result -> {
            PrivateLiveSourceBoardCustomerStatisticsDTO dto = new PrivateLiveSourceBoardCustomerStatisticsDTO();
            dto.setUserId(result.getUserId());
            dto.setWxId(result.getWxId());
            dto.setNickName(result.getWxNickName());
            dto.setUserType(result.getUserType());
            dto.setAttributionTime(result.getUpdateTime());
            dto.setTaskId(result.getConsultantTaskId());
            dto.setPhoneNumber(getUserRealPhone(result.getUserId()));
            PrivateLiveConsultantUserStatistics statistics = statisticsMap.get(result.getUserId());
            if (statistics != null) {
                dto.setConsultantShareName(statistics.getConsultantShareName());
                dto.setTradeOrderCnt(statistics.getPayOrderCnt());
                dto.setGmvAmt(statistics.getGmvAmt());
                dto.setActualPayAmt(statistics.getActualPayAmt());
                dto.setVerifyGmvAmt(statistics.getVerifyGmvAmt());
                dto.setRefundGmvAmt(statistics.getRefundGmvAmt());
            }

            ConsultantBasicDataDTO.Response basicDataResponse = basicDataMap.get(Pair.of(result.getWxId(), result.getUserId()));
            // 取不到数据，代表用户未进群
            if (basicDataResponse != null) {
                dto.setFollowUpStatus(basicDataResponse.getTagId());
                dto.setInvitePeopleNum(basicDataResponse.getInviteCount());
                dto.setAvatar(basicDataResponse.getAvatar());
            }
            if (result.getConsultantTaskId() > 0 && Objects.nonNull(distributorGroupModelMap.get(result.getConsultantTaskId()))) {
                dto.setDistributorGroupName(distributorGroupModelMap.get(result.getConsultantTaskId()).getGroupName());
            }
            if (StringUtils.isBlank(dto.getAvatar())) {
                //获取授权的用户信息
                WechatUserInfoDTO wechatUserInfoDTO = wechatUserAclService
                        .queryWechatUserByUserId(result.getUserId());
                if (wechatUserInfoDTO != null) {
                    dto.setAvatar(wechatUserInfoDTO.getAvatarUrl());
                    dto.setNickName(wechatUserInfoDTO.getNickName());
                }
            }
            // 设置默认值，防止未进群查不到数据（跟进状态：未跟进、邀请人数：0）
            if (dto.getFollowUpStatus() == null) {
                dto.setFollowUpStatus(12002);
            }
            if (dto.getInvitePeopleNum() == null) {
                dto.setInvitePeopleNum(0);
            }
            //设置咨询师名称，防止查不到统计数据导致名称不存在（咨询师分享名称）
            if (dto.getConsultantShareName() == null || StringUtils.isBlank(dto.getConsultantActualName())) {
                PrivateLiveConsultantTask consultantTask = privateLiveConsultantTaskRepository
                        .loadById(result.getConsultantTaskId());
                if (consultantTask != null) {
                    dto.setConsultantShareName(consultantTask.getShareName());
                    dto.setConsultantActualName(consultantTask.getActualName());
                }
            }
            // 设置用户特征标签信息
            if (StringUtils.isNotEmpty(result.getUnionId())) {
                List<CommunityLiveUserTagDetailDTO.TagDetail> tagDetails = unionId2Tag.get(result.getUnionId());
                SourceBoardUserTagInfo userTagInfo = new SourceBoardUserTagInfo();
                if (CollectionUtils.isNotEmpty(tagDetails)) {
                    Map<Integer, CommunityLiveUserTagDetailDTO.TagDetail> type2TagDetail = tagDetails.stream().filter(e ->
                                    (e.getTagType() == PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType() || e.getTagType() == PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()))
                            .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO.TagDetail::getTagType, Function.identity(), (v1, v2) -> v1));
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()) != null) {
                        userTagInfo.setIntentionTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()).getTagId());
                    }
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()) != null) {
                        userTagInfo.setPurchaseTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()).getTagId());
                    }
                }
                dto.setUserTagInfo(userTagInfo);
            } else {
                List<CommunityLiveUserTagDetailDTO.TagDetail> tagDetails = wxId2Tag.get(result.getWxId());
                SourceBoardUserTagInfo userTagInfo = new SourceBoardUserTagInfo();
                if (CollectionUtils.isNotEmpty(tagDetails)) {
                    Map<Integer, CommunityLiveUserTagDetailDTO.TagDetail> type2TagDetail = tagDetails.stream().filter(e ->
                                    (e.getTagType() == PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType() || e.getTagType() == PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()))
                            .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO.TagDetail::getTagType, Function.identity(), (v1, v2) -> v1));
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()) != null) {
                        userTagInfo.setIntentionTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()).getTagId());
                    }
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()) != null) {
                        userTagInfo.setPurchaseTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()).getTagId());
                    }
                }
                dto.setUserTagInfo(userTagInfo);
            }


            // 设置意向商品信息
            SourceBoardIntentionProductInfo intentionProductInfo = intentionProducts.get(result.getUnionId());
            dto.setIntentionProductInfo(intentionProductInfo);

            // 设置访问者信息
            String invitor = invitorInfo.get(result.getWxId());
            SourceBoardInvitorInfo sourceBoardInvitorInfo = new SourceBoardInvitorInfo();
            sourceBoardInvitorInfo.setInvitorName(invitor);
            dto.setInvitorInfo(sourceBoardInvitorInfo);
            dto.setIntentionType(Objects.nonNull(result.getIntentionType()) ? result.getIntentionType() : 0);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据美团用户ID获取用户真实手机号码
     *
     * @param mtUserId 美团用户ID
     * @return 用户真实手机号码，如果获取失败则返回空字符串
     */
    private String getUserRealPhone(Long mtUserId) {
        UserFields userFields = new UserFields();
        userFields.setMobile(true);
        try {
            UserRespMsg userResp = userRetrieveService.getUserByIdWithMsg(mtUserId, userFields);
            if (userResp.isSuccess() && userResp.getUser() != null) {
                return userResp.getUser().getMobile();
            }
        } catch (TException e) {
            ExceptionUtils.logError(getClass().getSimpleName() + ".getUserRealPhone", e, mtUserId);
        }
        return StringUtils.EMPTY;
    }

    private PageInfoDTO buildPageInfoDTO(PrivateLiveSourceBoardCustomerRequest request, Long total) {
        int totalPageCount = getTotalPageCountByTotalCountAndPageSize(total.intValue(), request.getPageSize());
        return PageInfoDTO.builder()
                .currentPageNum(request.getPageNo())
                .pageSize(request.getPageSize())
                .totalCount(total.intValue())
                .totalPageCount(totalPageCount)
                .build();
    }

    private RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> buildEmptyPageDataDTO(PrivateLiveSourceBoardCustomerRequest request) {
        PageInfoDTO pageInfoDTO = PageInfoDTO.builder().currentPageNum(request.getPageNo())
                .pageSize(request.getPageSize()).totalCount(0)
                .totalPageCount(
                        getTotalPageCountByTotalCountAndPageSize(0, request.getPageSize()))
                .build();
        return RemoteResponse.success(PageDataDTO.<PrivateLiveSourceBoardCustomerStatisticsDTO>builder()
                .pageInfoDTO(pageInfoDTO).list(new ArrayList<>()).build());
    }

    private RemoteResponse<PageDataDTO<PrivateLiveSourceBoardCustomerStatisticsDTO>> buildSuccessPageDataDTO(PageInfoDTO pageInfoDTO, List<PrivateLiveSourceBoardCustomerStatisticsDTO> dtos) {
        return RemoteResponse.success(PageDataDTO.<PrivateLiveSourceBoardCustomerStatisticsDTO>builder()
                .pageInfoDTO(pageInfoDTO).list(dtos).build());
    }

    private List<PrivateLiveSourceBoardConsultUserStatisticsDTO> consultUserStatistics(String liveId, List<PrivateLiveConsultantTask> privateLiveConsultantTasks) throws Exception {

        List<Long> taskIdList = privateLiveConsultantTasks.stream().map(PrivateLiveConsultantTask::getId).collect(Collectors.toList());

        // 获取咨询师所属的团长渠道
        CompletableFuture<Map<Long, DistributorGroupModel>> distributorGroupFur = CompletableFuture.supplyAsync(
                () -> queryConsultantDistributor(privateLiveConsultantTasks), LIVE_INTENTION_STATISTIC_POOL
        );

        // 获取咨询师每个类型的用户数量
        CompletableFuture<Map<Long, List<PrivateLiveUserTypeCount>>> privateLiveUserTypeCountMapFur = CompletableFuture.supplyAsync(
                () -> userIntentionResultRepository.countUserTypeMapByConsultantTaskId(liveId, taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        // 获取咨询师下的特征的用户
        CompletableFuture<Map<Long, SourceBoardConsultantTagUserInfo>> consultantTagUserFur = CompletableFuture.supplyAsync(
                () -> queryConsultantTagUserCnt(liveId, taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        // 获取咨询师的归因用户业绩列表
        CompletableFuture<Map<Long, List<PrivateLiveConsultantUserStatistics>>> userStatisticsMapFur = CompletableFuture.supplyAsync(
                () -> privateLiveConsultantSummaryRepository.batchGetSummaryMapByConsultantTasks(taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        // 查询跟进情况
        CompletableFuture<Map<Long, SourceBoardConsultFollowUpInfo>> consultFollowUpInfoMapFur = CompletableFuture.supplyAsync(
                () -> privateSphereUserAclService.queryConsultTaskFollowUpInfoMap(liveId, taskIdList), LIVE_INTENTION_STATISTIC_POOL
        );

        CompletableFuture.allOf(distributorGroupFur, privateLiveUserTypeCountMapFur, consultantTagUserFur, consultFollowUpInfoMapFur).get(3, TimeUnit.SECONDS);

        Map<Long, DistributorGroupModel> distributorGroupModelMap = distributorGroupFur.join();
        Map<Long, List<PrivateLiveUserTypeCount>> task2UserType = privateLiveUserTypeCountMapFur.join();
        Map<Long, SourceBoardConsultantTagUserInfo> task2TagUser = consultantTagUserFur.join();
        Map<Long, SourceBoardConsultFollowUpInfo> task2FollowUser = consultFollowUpInfoMapFur.join();
        Map<Long, List<PrivateLiveConsultantUserStatistics>> task2SaleInfo = userStatisticsMapFur.join();

        return privateLiveConsultantTasks.stream().map(e -> {
            return buildConsultUserStatisticsDTO(e, distributorGroupModelMap, task2UserType, task2TagUser, task2FollowUser, task2SaleInfo);
        }).collect(Collectors.toList());
    }

    private Map<Long, SourceBoardConsultantTagUserInfo> queryConsultantTagUserCnt(String liveId, List<Long> taskIdList) {
        Map<Long, SourceBoardConsultantTagUserInfo> tagUserInfoMap = Maps.newHashMap();
        CommunityConsultantUserTagRequest tagRequest = new CommunityConsultantUserTagRequest();
        tagRequest.setLiveId(liveId);
        tagRequest.setConsultantIds(taskIdList);
        tagRequest.setTagIds(Lists.newArrayList(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagId(), PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagId(),
                PrivateSphereUserTagEnum.PAY_ORDER_TAG.getTagId(), PrivateSphereUserTagEnum.VIEW_LIVE_TAG.getTagId()));
        CommunityConsultantTagUserStatDTO tagUserStatDTO = privateSphereUserAclService.queryConsultantUserWithTags(tagRequest);
        if (tagUserStatDTO == null || CollectionUtils.isEmpty(tagUserStatDTO.getTagUserDetailDTOS())) {
            return tagUserInfoMap;
        }
        Map<Long, CommunityConsultantTagUserDetailDTO> task2TagDetailMap = tagUserStatDTO.getTagUserDetailDTOS().stream().collect(Collectors.toMap(CommunityConsultantTagUserDetailDTO::getConsultantTaskId, Function.identity(), (v1, v2) -> v1));
        for (Long taskId : taskIdList) {
            SourceBoardConsultantTagUserInfo tagUserInfo = SourceBoardConsultantTagUserInfo.empty();
            tagUserInfoMap.put(taskId, tagUserInfo);

            CommunityConsultantTagUserDetailDTO userDetailDTO = task2TagDetailMap.get(taskId);
            if (userDetailDTO == null || MapUtils.isEmpty(userDetailDTO.getTag2UserIds())) {
                continue;
            }

            // 高意向用户数
            List<Long> highIntentionUsers = Lists.newArrayList();
            if (CollectionUtils.isEmpty(userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagId()))) {
                tagUserInfo.setHighIntentionUserCnt(0);
            } else {
                highIntentionUsers = userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagId()).stream().distinct().collect(Collectors.toList());
                tagUserInfo.setHighIntentionUserCnt(highIntentionUsers.size());
            }


            // 高购买力用户数
            List<Long> highPurchaseUsers = Lists.newArrayList();
            if (CollectionUtils.isEmpty(userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagId()))) {
                tagUserInfo.setHighPurchaseUserCnt(0);
            } else {
                highPurchaseUsers = userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagId()).stream().distinct().collect(Collectors.toList());
                tagUserInfo.setHighPurchaseUserCnt(highPurchaseUsers.size());
            }


            // 已支付用户数
            List<Long> paidUsers = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.PAY_ORDER_TAG.getTagId()))) {
                paidUsers = userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.PAY_ORDER_TAG.getTagId()).stream().distinct().collect(Collectors.toList());

            }
            if (CollectionUtils.isNotEmpty(highIntentionUsers) && CollectionUtils.isNotEmpty(paidUsers)) {
                List<Long> highIntentionPaidUser = highIntentionUsers.stream().filter(paidUsers::contains).collect(Collectors.toList());
                tagUserInfo.setHighIntentionPaidUserCnt(highIntentionPaidUser.size());
            }

            if (CollectionUtils.isNotEmpty(highPurchaseUsers) && CollectionUtils.isNotEmpty(paidUsers)) {
                List<Long> highPurchasePaidUser = highPurchaseUsers.stream().filter(paidUsers::contains).collect(Collectors.toList());
                tagUserInfo.setHighIntentionPaidUserCnt(highPurchasePaidUser.size());
            }

            // 已看播的用户数
            List<Long> viewUsers = Lists.newArrayList();
            if (CollectionUtils.isEmpty(userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.VIEW_LIVE_TAG.getTagId()))) {
                tagUserInfo.setEnterUserCnt(0);
            } else {
                viewUsers = userDetailDTO.getTag2UserIds().get(PrivateSphereUserTagEnum.VIEW_LIVE_TAG.getTagId()).stream().distinct().collect(Collectors.toList());
                tagUserInfo.setEnterUserCnt(viewUsers.size());
            }
        }
        return tagUserInfoMap;
    }

    private PrivateLiveSourceBoardConsultUserStatisticsDTO buildConsultUserStatisticsDTO(PrivateLiveConsultantTask consultantTask,
                                                                                         Map<Long, DistributorGroupModel> distributorGroupModelMap,
                                                                                         Map<Long, List<PrivateLiveUserTypeCount>> task2UserType,
                                                                                         Map<Long, SourceBoardConsultantTagUserInfo> task2TagUser,
                                                                                         Map<Long, SourceBoardConsultFollowUpInfo> task2FollowUser,
                                                                                         Map<Long, List<PrivateLiveConsultantUserStatistics>> task2UserStatistic) {
        PrivateLiveSourceBoardConsultUserStatisticsDTO statisticsDTO = new PrivateLiveSourceBoardConsultUserStatisticsDTO();
        statisticsDTO.setLiveId(consultantTask.getLiveId());
        statisticsDTO.setTaskId(consultantTask.getId());
        statisticsDTO.setConsultName(consultantTask.getShareName());
        statisticsDTO.setActualName(consultantTask.getActualName());
        statisticsDTO.setConsultantId(consultantTask.getConsultantId());

        if (Objects.nonNull(distributorGroupModelMap.get(consultantTask.getId()))) {
            statisticsDTO.setDistributorGroupName(distributorGroupModelMap.get(consultantTask.getId()).getGroupName());
        }
        try {
            statisticsDTO.setPhoneNumber(mobileTokenUtil.getMobileMask(consultantTask.getPhoneNumber()));
        } catch (Exception e) {
            log.error("pageQuerySourceBoardConsultList, get phone number fail", e);
            statisticsDTO.setPhoneNumber(StringUtils.EMPTY);
        }

        // 跟进情况
        statisticsDTO.setFollowUpInfo(task2FollowUser.get(consultantTask.getId()) == null ? SourceBoardConsultFollowUpInfo.empty() : task2FollowUser.get(consultantTask.getId()));

        // 有效客户
        statisticsDTO.setAllUserInfo(convertAllUserInfo(task2UserType.get(consultantTask.getId())));

        // 特征
        statisticsDTO.setTagUserInfo(task2TagUser.get(consultantTask.getId()) == null ? SourceBoardConsultantTagUserInfo.empty() : task2TagUser.get(consultantTask.getId()));

        // 总业绩
        statisticsDTO.setAllSaleInfo(convertAllSaleInfo(task2UserStatistic.get(consultantTask.getId())));

        return statisticsDTO;

    }

    private List<CommunityLiveUserTagDetailDTO> queryUserTagInfo(String liveId, List<PrivateLiveUserIntentionResult> userIntentionResults) {
        List<CommunityLiveUserTagDetailDTO> res = Lists.newArrayList();
        List<String> unionIds = userIntentionResults.stream().map(PrivateLiveUserIntentionResult::getUnionId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<String> wxIds = userIntentionResults.stream().filter(e -> (StringUtils.isEmpty(e.getUnionId()) && StringUtils.isNotEmpty(e.getWxId()))).map(PrivateLiveUserIntentionResult::getWxId).collect(Collectors.toList());
        CommunityLiveUserTagDTO userTagDTO = privateSphereUserAclService.queryUserTags(liveId, unionIds, wxIds);
        if (userTagDTO == null || CollectionUtils.isEmpty(userTagDTO.getUserTagDetailDTOS())) {
            return res;
        }
        return userTagDTO.getUserTagDetailDTOS();
    }

    private List<CommunityLiveUserTagDetailDTO> queryUserTagDetail(String liveId, List<String> unionIds, List<String> wxIds) {
        List<CommunityLiveUserTagDetailDTO> res = Lists.newArrayList();
        CommunityLiveUserTagDTO userTagDTO = privateSphereUserAclService.queryUserTags(liveId, unionIds, wxIds);
        if (userTagDTO == null || CollectionUtils.isEmpty(userTagDTO.getUserTagDetailDTOS())) {
            return res;
        }
        return userTagDTO.getUserTagDetailDTOS();
    }

    private Map<String, SourceBoardIntentionProductInfo> queryIntentionProduct(String liveId, List<PrivateLiveUserIntentionResult> userIntentionResults) {
        try {
            log.info("customerListStatistic queryIntentionProduct, liveId: {}, userIntention: {}", liveId, JSON.toJSONString(userIntentionResults));
            if (CollectionUtils.isEmpty(userIntentionResults)) {
                return Maps.newHashMap();
            }

            Map<String, SourceBoardIntentionProductInfo> res = Maps.newHashMap();
            List<CommunityConsultantUseRequest> requests = userIntentionResults.stream().map(e -> {
                CommunityConsultantUseRequest useRequest = new CommunityConsultantUseRequest();
                useRequest.setConsultantTaskId(e.getConsultantTaskId());
                useRequest.setUnionId(e.getUnionId());
                return useRequest;
            }).collect(Collectors.toList());
            List<CommunityLiveUserIntentionProductDTO> intentionProductDTOS = privateSphereUserAclService.batchQueryUserIntentionProduct(liveId, requests);
            if (CollectionUtils.isEmpty(intentionProductDTOS)) {
                return res;
            }

            Set<Long> consultantTasks = userIntentionResults.stream().map(PrivateLiveUserIntentionResult::getConsultantTaskId).collect(Collectors.toSet());
            Map<Long, List<CommunityLiveUserIntentionProductDetailDTO>> task2IntentionProduct = intentionProductDTOS.stream()
                    .collect(Collectors.toMap(CommunityLiveUserIntentionProductDTO::getConsultantTaskId, CommunityLiveUserIntentionProductDTO::getIntentionProductDetailDTOList, (v1, v2) -> v1));

            Set<Integer> dealProductSet = new HashSet<>();
            Set<Integer> prepayProductSet = new HashSet<>();

            for (Long taskId : consultantTasks) {
                log.info("customerListStatistic queryIntentionProduct, taskId: {}", taskId);
                List<CommunityLiveUserIntentionProductDetailDTO> productDTOS = task2IntentionProduct.get(taskId);
                if (CollectionUtils.isEmpty(productDTOS)) {
                    continue;
                }

                Map<Integer, List<CommunityLiveUserIntentionProductDetailDTO>> type2Product = productDTOS.stream().
                        collect(Collectors.groupingBy(CommunityLiveUserIntentionProductDetailDTO::getProductType));

                if (MapUtils.isNotEmpty(type2Product) && CollectionUtils.isNotEmpty(type2Product.get(ProductTypeEnum.GROUP_BUY.getValue()))) {
                    dealProductSet.addAll(type2Product.get(ProductTypeEnum.GROUP_BUY.getValue())
                            .stream().map(CommunityLiveUserIntentionProductDetailDTO::getProductId).map(Long::intValue).collect(Collectors.toSet()));
                }

                if (MapUtils.isNotEmpty(type2Product) && CollectionUtils.isNotEmpty(type2Product.get(ProductTypeEnum.PREPAID.getValue()))) {
                    prepayProductSet.addAll(type2Product.get(ProductTypeEnum.PREPAID.getValue())
                            .stream().map(CommunityLiveUserIntentionProductDetailDTO::getProductId).map(Long::intValue).collect(Collectors.toSet()));
                }
            }

            List<Integer> dealProductId = new ArrayList<>(dealProductSet);
            List<Integer> prepayProductId = new ArrayList<>(prepayProductSet);
            log.info("customerListStatistic queryIntentionProduct, dealProduct: {}, prepayProduct: {}", JSON.toJSONString(dealProductId), JSON.toJSONString(prepayProductId));

            // 查询商品信息
            CompletableFuture<List<ProductBasicsInfoBO>> dealProductFur = CompletableFuture.supplyAsync(() -> {
                if (CollectionUtils.isNotEmpty(dealProductId)) {
                    return productAclService.batchQueryByDealGroupByDealGroupIds(dealProductId)
                            .stream().map(ProductBasicsInfoBOConverter::converter).collect(Collectors.toList());
                } else {
                    return Lists.newArrayList();
                }
            }, LIVE_INTENTION_PRODUCT_POOL);

            CompletableFuture<List<ProductBasicsInfoBO>> prepayProductFur = CompletableFuture.supplyAsync(() -> {
                if (CollectionUtils.isNotEmpty(prepayProductId)) {
                    return productAclService.batchGetProducts(prepayProductId)
                            .stream().map(ProductBasicsInfoBOConverter::converter).collect(Collectors.toList());
                } else {
                    return Lists.newArrayList();
                }
            }, LIVE_INTENTION_PRODUCT_POOL);
            CompletableFuture.allOf(dealProductFur, prepayProductFur).get(3, TimeUnit.SECONDS);
            List<ProductBasicsInfoBO> dealProducts = dealProductFur.join();
            List<ProductBasicsInfoBO> prepayProducts = prepayProductFur.join();

            Map<Long, ProductBasicsInfoBO> id2DealProductInfo = dealProducts.stream().collect(Collectors.toMap(ProductBasicsInfoBO::getProductId, Function.identity(), (v1, v2) -> v1));
            Map<Long, ProductBasicsInfoBO> id2PrepayProductInfo = prepayProducts.stream().collect(Collectors.toMap(ProductBasicsInfoBO::getProductId, Function.identity(), (v1, v2) -> v1));

            for (Long taskId : consultantTasks) {
                List<CommunityLiveUserIntentionProductDetailDTO> productDTOS = task2IntentionProduct.get(taskId);
                if (CollectionUtils.isEmpty(productDTOS)) {
                    continue;
                }

                Map<String, CommunityLiveUserIntentionProductDetailDTO> union2Product = productDTOS.stream().collect(Collectors.toMap(CommunityLiveUserIntentionProductDetailDTO::getUnionId, Function.identity(), (v1, v2) -> v1));
                for (String unionId : union2Product.keySet()) {
                    SourceBoardIntentionProductInfo productInfo = new SourceBoardIntentionProductInfo();
                    productInfo.setProductId(union2Product.get(unionId).getProductId());
                    productInfo.setProductType(union2Product.get(unionId).getProductType());
                    if (productInfo.getProductType() == ProductTypeEnum.PREPAID.getValue()) {
                        ProductBasicsInfoBO basicsInfoBO = id2PrepayProductInfo.get(productInfo.getProductId());
                        productInfo.setProductName(basicsInfoBO == null ? "" : basicsInfoBO.getProductName());
                        productInfo.setPrice(basicsInfoBO == null ? 0L : basicsInfoBO.getPrice().multiply(BigDecimal.valueOf(100)).longValue());
                    }
                    if (productInfo.getProductType() == ProductTypeEnum.GROUP_BUY.getValue()) {
                        ProductBasicsInfoBO basicsInfoBO = id2DealProductInfo.get(productInfo.getProductId());
                        productInfo.setProductName(basicsInfoBO == null ? "" : basicsInfoBO.getProductName());
                        productInfo.setPrice(basicsInfoBO == null ? 0L : basicsInfoBO.getPrice().multiply(BigDecimal.valueOf(100)).longValue());
                    }
                    res.put(unionId, productInfo);
                }
            }
            return res;
        } catch (Exception e) {
            log.error("customerListStatistic queryIntentionProduct, query fail, liveId: {}", liveId, e);
            return Maps.newHashMap();
        }

    }

    private Map<String, String> queryInvitorInfo(String liveId, List<PrivateLiveUserIntentionResult> userIntentionResults) {
        Map<String, String> res = Maps.newHashMap();
        List<CommunityConsultantUseRequest> requests = userIntentionResults.stream().filter(e -> e.getUserType() == GroupUserTypeEnum.FISSION_USER.getCode()).map(e -> {
            CommunityConsultantUseRequest useRequest = new CommunityConsultantUseRequest();
            useRequest.setConsultantTaskId(e.getConsultantTaskId());
            useRequest.setWxId(e.getWxId());
            return useRequest;
        }).collect(Collectors.toList());
        List<CommunityUserInvitorDTO> invitorDTOS = privateSphereUserAclService.batchQueryUserInvitor(liveId, requests);
        if (CollectionUtils.isEmpty(invitorDTOS)) {
            return res;
        }
        List<Long> consultantTasks = userIntentionResults.stream().map(PrivateLiveUserIntentionResult::getConsultantTaskId).collect(Collectors.toList());
        Map<Long, List<CommunityUserInvitorDetailDTO>> task2Invitor = invitorDTOS.stream()
                .collect(Collectors.toMap(CommunityUserInvitorDTO::getConsultantTaskId, CommunityUserInvitorDTO::getInvitorDetailDTOS, (v1, v2) -> v1));

        for (Long taskId : consultantTasks) {
            List<CommunityUserInvitorDetailDTO> invitorDetailDTOS = task2Invitor.get(taskId);
            if (CollectionUtils.isEmpty(invitorDetailDTOS)) {
                continue;
            }

            Map<String, CommunityUserInvitorDetailDTO> wxId2User = invitorDetailDTOS.stream()
                    .filter(Objects::nonNull)
                    .filter(detailDTO -> Objects.nonNull(detailDTO.getWxId()))
                    .collect(Collectors.toMap(CommunityUserInvitorDetailDTO::getWxId, Function.identity(), (v1, v2) -> mergeFunction(v1, v2)));

            for (String wxId : wxId2User.keySet()) {
                res.put(wxId, wxId2User.get(wxId).getInvitorWxName());
            }
        }
        return res;
    }

    private CommunityUserInvitorDetailDTO mergeFunction(CommunityUserInvitorDetailDTO v1, CommunityUserInvitorDetailDTO v2) {
        if (StringUtils.isBlank(v1.getInvitorWxName())) {
            return v2;
        } else {
            return v1;
        }
    }

    private List<PrivateLiveConsultantUserStatistics> queryUserStatistics(PrivateLiveSourceBoardCustomerRequest request, List<PrivateLiveUserIntentionResult> userIntentionResultList) {
        List<Long> userIds = userIntentionResultList.stream().map(PrivateLiveUserIntentionResult::getUserId)
                .collect(Collectors.toList());
        return privateLiveConsultantSummaryRepository.queryLoadByMtUserIds(request.getLiveId(), userIds);
    }

    private Map<Pair<String, Long>, ConsultantBasicDataDTO.Response> getConsultantBasicDate(PrivateLiveSourceBoardCustomerRequest request, List<PrivateLiveUserIntentionResult> userIntentionResultList) {
        ConsultantBasicDataDTO basicDataDTO = getConsultantBasicDataDTO(request.getLiveId(), userIntentionResultList);
        return getBasicDataMap(basicDataDTO);
    }

    private List<PrivateLiveSourceBoardTagUserDTO> buildTagUserDTO(List<CommunityUserSimpleDTO> userSimpleDTOs, List<PrivateLiveUserIntentionResult> intentionResults,
                                                                   List<PrivateLiveConsultantUserStatistics> userStatistics, List<CommunityLiveUserTagDetailDTO> tagInfo) {
        List<PrivateLiveSourceBoardTagUserDTO> tagUserDTOS = Lists.newArrayList();
        Map<String, String> wxId2IntentionResult = CollectionUtils.isEmpty(intentionResults) ?
                Maps.newHashMap() : intentionResults.stream().collect(Collectors.toMap(PrivateLiveUserIntentionResult::getWxId, PrivateLiveUserIntentionResult::getWxNickName, (v1, v2) -> v1));
        Map<Long, Long> userId2Statistic = CollectionUtils.isEmpty(userStatistics) ?
                Maps.newHashMap() : userStatistics.stream().collect(Collectors.toMap(PrivateLiveConsultantUserStatistics::getMtUserId, PrivateLiveConsultantUserStatistics::getGmvAmt, (v1, v2) -> v1));
        Map<String, List<CommunityLiveUserTagDetailDTO.TagDetail>> unionId2Tag = tagInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getUnionId()))
                .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO::getUnionId, CommunityLiveUserTagDetailDTO::getTagIds, (v1, v2) -> v1));
        Map<String, List<CommunityLiveUserTagDetailDTO.TagDetail>> wxId2Tag = tagInfo.stream().filter(e -> StringUtils.isNotEmpty(e.getWxId()))
                .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO::getWxId, CommunityLiveUserTagDetailDTO::getTagIds, (v1, v2) -> v1));

        for (CommunityUserSimpleDTO userSimpleDTO : userSimpleDTOs) {
            PrivateLiveSourceBoardTagUserDTO tagUserDTO = new PrivateLiveSourceBoardTagUserDTO();
            tagUserDTO.setUserId(userSimpleDTO.getUserId());
            tagUserDTO.setWxId(userSimpleDTO.getWxId());
            tagUserDTO.setNickName(wxId2IntentionResult.getOrDefault(userSimpleDTO.getWxId(), ""));
            if (StringUtils.isBlank(userSimpleDTO.getUnionId())) {
                tagUserDTO.setTotalGtv(0L);
                List<CommunityLiveUserTagDetailDTO.TagDetail> tagDetails = wxId2Tag.get(userSimpleDTO.getWxId());
                if (CollectionUtils.isNotEmpty(tagDetails)) {
                    Map<Integer, CommunityLiveUserTagDetailDTO.TagDetail> type2TagDetail = tagDetails.stream().filter(e ->
                                    (e.getTagType() == PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType() || e.getTagType() == PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()))
                            .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO.TagDetail::getTagType, Function.identity(), (v1, v2) -> v1));
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()) != null) {
                        tagUserDTO.setIntentionTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()).getTagId());
                    }
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()) != null) {
                        tagUserDTO.setPurchaseTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()).getTagId());
                    }
                }

            } else {
                tagUserDTO.setPhoneNumber(getUserRealPhone(userSimpleDTO.getUserId()));
                tagUserDTO.setTotalGtv(userId2Statistic.getOrDefault(userSimpleDTO.getUserId(), 0L));
                List<CommunityLiveUserTagDetailDTO.TagDetail> tagDetails = unionId2Tag.get(userSimpleDTO.getUnionId());
                if (CollectionUtils.isNotEmpty(tagDetails)) {
                    Map<Integer, CommunityLiveUserTagDetailDTO.TagDetail> type2TagDetail = tagDetails.stream().filter(e ->
                                    (e.getTagType() == PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType() || e.getTagType() == PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()))
                            .collect(Collectors.toMap(CommunityLiveUserTagDetailDTO.TagDetail::getTagType, Function.identity(), (v1, v2) -> v1));
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()) != null) {
                        tagUserDTO.setIntentionTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_INTENTION_TAG.getTagType()).getTagId());
                    }
                    if (type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()) != null) {
                        tagUserDTO.setPurchaseTag(type2TagDetail.get(PrivateSphereUserTagEnum.HIGH_PURCHASING_LEVEL_TAG.getTagType()).getTagId());
                    }
                }
            }
            tagUserDTOS.add(tagUserDTO);
        }
        return tagUserDTOS;
    }

    /**
     * 根据邀请人信息，查询上级中最近的有效咨询师（通过归因链路查找）
     * 进群时需要查询
     *
     * @param inviteWxId
     * @param inviteUnionId
     * @param liveId
     * @return 咨询师任务id、当前用户的用户类型
     */
    private Pair<Long, Integer> queryNearestValidConsultantTaskId(String inviteWxId, String inviteUnionId, String liveId) {
        // 检查邀请人是否咨询师
        RemoteResponse<Long> checkResponse = privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(inviteUnionId, liveId);
        if (checkResponse.isSuccess()) {
            return Pair.of(checkResponse.getData(), GroupUserTypeEnum.SEED_USER.getCode());
        }
        // 邀请人非咨询师，根据归因记录向上找最近有效咨询师
        // 查询归因记录
        PrivateLiveUserIntentionQueryRequest request = new PrivateLiveUserIntentionQueryRequest();
        request.setWxId(inviteWxId);
        request.setLiveId(liveId);
        request.setStatusList(UserIntentionStatusEnum.VALID_STATUS_LIST);

        Long consultantTaskId = queryFirstUserIntentionConsultantTaskId(request);
        // 不是无归属，且咨询师不合法，继续往上找
        // 直到为0或者是有效咨询师
        while (consultantTaskId != 0L && !isValidConsultant(consultantTaskId)) {
            RemoteResponse<String> unionIdResponse = privateLiveConsultantVerifyService.getUnionIdByTaskId(consultantTaskId);
            request = new PrivateLiveUserIntentionQueryRequest();
            request.setUnionId(unionIdResponse.getData());
            request.setLiveId(liveId);
            request.setStatusList(UserIntentionStatusEnum.VALID_STATUS_LIST);
            consultantTaskId = queryFirstUserIntentionConsultantTaskId(request);
        }
        return Pair.of(consultantTaskId, consultantTaskId == 0L ? GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode() : GroupUserTypeEnum.FISSION_USER.getCode());
    }

    /**
     * 根据用户信息，查询上级中最近的有效咨询师（通过归因链路查找）
     * 咨询师取消授权/取消关联时查询
     *
     * @param wxId
     * @param liveId
     * @return 咨询师任务id、当前用户的用户类型
     */
    private Pair<Long, Integer> queryNearestValidConsultantTaskId(String wxId, String liveId) {
        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = privateSphereUserAclService.queryInviteRelation(wxId, liveId, MemberInviteRelationType.UP.getCode());
        if (memberInviteRelationInfoDTO == null || memberInviteRelationInfoDTO.getUpInviteUserInfoList() == null || CollectionUtils.isEmpty(memberInviteRelationInfoDTO.getUpInviteUserInfoList())) {
            Cat.logError(new PrivateLiveIntentionException(String.format("查询邀约关系失败, wxId is %s, liveId is %s", wxId, liveId)));
            // 兜底群内无归属
            return Pair.of(0L, GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode());
        }
        List<MemberInviteWxUserInfo> upInviteUserInfoList = memberInviteRelationInfoDTO.getUpInviteUserInfoList().stream()
                .sorted(Comparator.comparing(MemberInviteWxUserInfo::getDistance)).collect(Collectors.toList());

        String inviteWxId = upInviteUserInfoList.get(0).getWxId();
        String inviteUnionId = upInviteUserInfoList.get(0).getUnionId();

        // 检查邀请人是否咨询师
        RemoteResponse<Long> checkResponse = privateLiveConsultantVerifyService.checkByUnionIdAndLiveId(inviteUnionId, liveId);
        if (checkResponse.isSuccess()) {
            return Pair.of(checkResponse.getData(), GroupUserTypeEnum.SEED_USER.getCode());
        }
        // 邀请人非咨询师，根据归因记录向上找最近有效咨询师
        // 查询归因记录
        PrivateLiveUserIntentionQueryRequest request = new PrivateLiveUserIntentionQueryRequest();
        request.setWxId(inviteWxId);
        request.setLiveId(liveId);
        request.setStatusList(UserIntentionStatusEnum.VALID_STATUS_LIST);

        Long consultantTaskId = queryFirstUserIntentionConsultantTaskId(request);
        // 不是无归属，且咨询师不合法，继续往上找
        // 直到为0或者是有效咨询师
        while (consultantTaskId != 0L && !isValidConsultant(consultantTaskId)) {
            RemoteResponse<String> unionIdResponse = privateLiveConsultantVerifyService.getUnionIdByTaskId(consultantTaskId);
            request = new PrivateLiveUserIntentionQueryRequest();
            request.setUnionId(unionIdResponse.getData());
            request.setLiveId(liveId);
            request.setStatusList(UserIntentionStatusEnum.VALID_STATUS_LIST);
            consultantTaskId = queryFirstUserIntentionConsultantTaskId(request);
        }
        return Pair.of(consultantTaskId, consultantTaskId == 0L ? GroupUserTypeEnum.GROUP_UN_INTENTION_USER.getCode() : GroupUserTypeEnum.FISSION_USER.getCode());
    }

    private boolean isValidConsultant(Long consultantTaskId) {
        RemoteResponse<Boolean> checkResponse = privateLiveConsultantVerifyService.checkConsultantValid(consultantTaskId);
        return checkResponse.isSuccess() && checkResponse.getData();
    }

    private Long queryFirstUserIntentionConsultantTaskId(PrivateLiveUserIntentionQueryRequest request) {
        List<PrivateLiveUserIntentionResult> userIntentionResultList = userIntentionResultRepository.query(request);
        if (CollectionUtils.isEmpty(userIntentionResultList)) {
            log.error(getClass().getSimpleName() + ".queryFirstUserIntentionConsultantTaskId error, No Valid UserIntention record, request: {}", request);
            return 0L;
        }
        return userIntentionResultList.get(0).getConsultantTaskId();
    }

    /**
     * 查询有效下级的归因记录，即调整归因时需要一并调整的用户（通过邀约关系查询）
     *
     * @param wxId
     * @param liveId
     * @param originConsultantTaskId
     * @return 归因结果
     */
    private List<PrivateLiveUserIntentionResult> queryValidDownUserIntentionResult(String wxId, String liveId, Long originConsultantTaskId) {
        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = privateSphereUserAclService.queryInviteRelation(wxId, liveId, MemberInviteRelationType.DOWN.getCode());
        if (memberInviteRelationInfoDTO == null) {
            Cat.logError(new PrivateLiveIntentionException(String.format("查询邀约关系失败, wxId is %s, liveId is %s", wxId, liveId)));
            return Lists.newArrayList();
        }
        List<MemberInviteWxUserInfo> downInviteUserInfoList = memberInviteRelationInfoDTO.getDownInviteUserInfoList();

        // 根据wxId查询归因表
        if (CollectionUtils.isEmpty(downInviteUserInfoList)) {
            return Lists.newArrayList();
        }
        List<String> wxIdList = downInviteUserInfoList.stream().map(MemberInviteWxUserInfo::getWxId).collect(Collectors.toList());
        List<PrivateLiveUserIntentionResult> userIntentionResultList = Lists.newArrayList(userIntentionResultRepository.queryUserIntentionByLiveIdAndWxIds(liveId, wxIdList).values());

        return userIntentionResultList.stream()
                .filter(intentionResult -> intentionResult.getConsultantTaskId().equals(originConsultantTaskId))
                .collect(Collectors.toList());
    }

    /**
     * 查询下级的归因记录，即咨询师授权/关联时需要调整的用户（通过邀约关系查询）
     *
     * @param wxId
     * @param liveId
     * @return 归因结果，当前用户的用户类型
     */
    private Map<PrivateLiveUserIntentionResult, Integer> queryDownUserIntentionResult(String wxId, String liveId) {
        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = privateSphereUserAclService.queryInviteRelation(wxId, liveId, MemberInviteRelationType.DOWN.getCode());
        if (memberInviteRelationInfoDTO == null) {
            Cat.logError(new PrivateLiveIntentionException(String.format("查询邀约关系失败, wxId is %s, liveId is %s", wxId, liveId)));
            return Maps.newHashMap();
        }
        List<MemberInviteWxUserInfo> downInviteUserInfoList = memberInviteRelationInfoDTO.getDownInviteUserInfoList();
        if (CollectionUtils.isEmpty(downInviteUserInfoList)) {
            return Maps.newHashMap();
        }
        List<String> unionIdList = privateLiveConsultantVerifyService.getUnionIdsByLiveId(liveId).getData();
        List<String> wxIdBlackList = Lists.newArrayList();
        List<MemberInviteWxUserInfo> validDownInviteUserInfoList = Lists.newArrayList();
        // 采用黑名单的形式，筛选从A开始往下找，找到第一个咨询师为止的所有用户
        downInviteUserInfoList.sort(Comparator.comparing(MemberInviteWxUserInfo::getDistance));
        for (MemberInviteWxUserInfo memberInviteWxUserInfo : downInviteUserInfoList) {
            // 如果是咨询师或者邀请人被拉黑，则将自己也加入黑名单
            if (unionIdList.contains(memberInviteWxUserInfo.getUnionId()) || wxIdBlackList.contains(memberInviteWxUserInfo.getInviteWxId())) {
                wxIdBlackList.add(memberInviteWxUserInfo.getWxId());
                continue;
            }
            validDownInviteUserInfoList.add(memberInviteWxUserInfo);
        }

        Map<String, MemberInviteWxUserInfo> inviteUserInfoMap = validDownInviteUserInfoList.stream().collect(Collectors.toMap(MemberInviteWxUserInfo::getWxId, Function.identity(), (v1, v2) -> v1));

        // 根据wxId查询归因表
        List<String> wxIdList = validDownInviteUserInfoList.stream().map(MemberInviteWxUserInfo::getWxId).collect(Collectors.toList());
        Map<String, PrivateLiveUserIntentionResult> userIntentionResultMap = userIntentionResultRepository.queryUserIntentionByLiveIdAndWxIds(liveId, wxIdList);

        return userIntentionResultMap.entrySet().stream().collect(Collectors.toMap(entry -> entry.getValue(),
                entry -> inviteUserInfoMap.get(entry.getKey()).getDistance() == 1 ? GroupUserTypeEnum.SEED_USER.getCode() : GroupUserTypeEnum.FISSION_USER.getCode(),
                (v1, v2) -> v1));
    }


    /**
     * 组装修改归因的request列表，用于咨询师授权/取消授权，关联/取消关联
     * 如果是调整归因需要批量组装request，请使用下面的方法
     *
     * @param userIntentionResultMap 归因结果，当前用户的用户类型的map
     * @param newConsultantTaskId
     * @param remark
     * @return
     */
    private List<PrivateLiveIntentionModifyRequest> buildModifyRequestList(Map<PrivateLiveUserIntentionResult, Integer> userIntentionResultMap, Long newConsultantTaskId, String remark) {
        List<PrivateLiveIntentionModifyRequest> requestList = userIntentionResultMap.entrySet().stream()
                .map(entry -> {
                    PrivateLiveUserIntentionResult result = entry.getKey();
                    PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
                    request.setWxId(result.getWxId());
                    request.setUnionId(result.getUnionId());
                    request.setMtUserId(result.getUserId());
                    request.setOriginConsultantTaskId(result.getConsultantTaskId());
                    request.setNewConsultantTaskId(newConsultantTaskId);
                    request.setLiveId(result.getLiveId());
                    request.setOperatorId(-1L);
                    // 手动调整归因时无需修改，咨询师授权/取消授权，关联/取消关联的时候需要修改
                    request.setTargetUserType(entry.getValue());
                    request.setRemark(remark);
                    return request;
                }).collect(Collectors.toList());
        return requestList;
    }

    /**
     * 组装修改归因的request列表，用于调整归因时使用
     *
     * @param userIntentionResultList 归因结果
     * @param newConsultantTaskId
     * @param remark
     * @return
     */
    private List<PrivateLiveIntentionModifyRequest> buildModifyRequestList(List<PrivateLiveUserIntentionResult> userIntentionResultList, Long newConsultantTaskId, String remark) {
        List<PrivateLiveIntentionModifyRequest> requestList = userIntentionResultList.stream()
                .map(intentionResult -> {
                    PrivateLiveIntentionModifyRequest request = new PrivateLiveIntentionModifyRequest();
                    request.setWxId(intentionResult.getWxId());
                    request.setMtUserId(intentionResult.getUserId());
                    request.setOriginConsultantTaskId(intentionResult.getConsultantTaskId());
                    request.setNewConsultantTaskId(newConsultantTaskId);
                    request.setLiveId(intentionResult.getLiveId());
                    request.setOperatorId(-1L);
                    request.setRemark(remark);
                    return request;
                }).collect(Collectors.toList());
        return requestList;
    }

    private int getUserTypeByInviteRelation(String wxId, Long userId, String liveId, Long consultantTaskId) {
        if (StringUtils.isEmpty(wxId)) {
            PrivateLiveUserIntentionResult userIntentionResult = userIntentionResultRepository.forceGetByUserIdAndLiveId(userId, liveId);
            wxId = userIntentionResult != null ? userIntentionResult.getWxId() : "";
        }
        if (StringUtils.isEmpty(wxId)) {
            // wxId为空，说明未进群
            return GroupUserTypeEnum.LINK_USER.getCode();
        }
        // wxId不为空，则查询邀约上级
        MemberInviteRelationInfoDTO memberInviteRelationInfoDTO = privateSphereUserAclService.queryInviteRelation(wxId, liveId, MemberInviteRelationType.UP.getCode());
        if (memberInviteRelationInfoDTO == null || memberInviteRelationInfoDTO.getUpInviteUserInfoList() == null || CollectionUtils.isEmpty(memberInviteRelationInfoDTO.getUpInviteUserInfoList())) {
            Cat.logError(new PrivateLiveIntentionException(String.format("查询邀约关系失败, wxId is %s, liveId is %s", wxId, liveId)));
            // 兜底未进群
            return GroupUserTypeEnum.LINK_USER.getCode();
        }
        List<MemberInviteWxUserInfo> upInviteUserInfoList = memberInviteRelationInfoDTO.getUpInviteUserInfoList().stream()
                .sorted(Comparator.comparing(MemberInviteWxUserInfo::getDistance)).collect(Collectors.toList());

        String inviteUnionId = upInviteUserInfoList.get(0).getUnionId();

        // 检查邀请人是否咨询师
        RemoteResponse<Boolean> checkResponse = privateLiveConsultantVerifyService.checkByUnionIdAndTaskId(inviteUnionId, consultantTaskId);
        if (checkResponse.isSuccess() && checkResponse.getData()) {
            // 是咨询师，设为种子用户
            return GroupUserTypeEnum.SEED_USER.getCode();
        } else {
            // 否则设为裂变用户
            return GroupUserTypeEnum.FISSION_USER.getCode();
        }
    }

    private List<Long> getTaskIdByDistributorId(String liveId, Integer taskType, List<Long> distributorGroupIds) {
        if (Objects.isNull(taskType) && CollectionUtils.isEmpty(distributorGroupIds)) {
            return Lists.newArrayList();
        }
        List<Long> taskIds = Lists.newArrayList();
        if (Objects.nonNull(taskType) && taskType == PrivateLiveTaskTypeEnum.CONSULTANT.getCode()) {
            // 获取普通咨询师
            List<PrivateLiveConsultantTask> consultants = privateLiveConsultantTaskRepository.loadByTypeAndConsultantId(liveId, taskType, Lists.newArrayList());
            taskIds.addAll(consultants.stream()
                    .map(PrivateLiveConsultantTask::getId)
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(distributorGroupIds)) {
            List<DistributorModel> distributorModels = distributorAppService.queryAllByDistributorGroupIds(distributorGroupIds);
            List<Long> consultantIds = distributorModels.stream()
                    .map(DistributorModel::getAccountId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(consultantIds)) {
                List<PrivateLiveConsultantTask> distributors = privateLiveConsultantTaskRepository.loadByTypeAndConsultantId(liveId, PrivateLiveTaskTypeEnum.DISTRIBUTOR.getCode(), consultantIds);
                taskIds.addAll(distributors.stream()
                        .map(PrivateLiveConsultantTask::getId)
                        .collect(Collectors.toList()));
            }
        }
        return taskIds.stream()
                .distinct()
                .collect(Collectors.toList());
    }
}
