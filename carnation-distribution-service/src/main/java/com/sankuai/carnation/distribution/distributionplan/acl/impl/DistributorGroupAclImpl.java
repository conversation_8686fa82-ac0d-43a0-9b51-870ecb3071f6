package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.distributionplan.acl.DistributorGroupAcl;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.privatelive.distribution.request.QueryPlanAndDistributorGroupRequest;
import com.sankuai.carnation.distribution.privatelive.distribution.response.QueryPlanAndDistributorGroupResponse;
import com.sankuai.carnation.distribution.privatelive.distribution.service.PrivateLivePlanDistributorGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DistributorGroupAclImpl implements DistributorGroupAcl {

    private static final String CAT_TYPE = DistributorGroupAcl.class.getSimpleName();

    @Resource
    private PrivateLivePlanDistributorGroupService privateLivePlanDistributorGroupService;

    @Override
    public boolean checkDistributorGroupBindLiveId(Long distributorGroupId, String liveId) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "checkDistributorGroupValid");
        try {
            QueryPlanAndDistributorGroupRequest request = new QueryPlanAndDistributorGroupRequest();
            request.setLiveId(liveId);
            request.setDistributorGroupId(distributorGroupId);
            RemoteResponse<QueryPlanAndDistributorGroupResponse> response = privateLivePlanDistributorGroupService.queryPlanAndDistributorGroup(request);
            if (response == null || !response.isSuccess()) {
                throw new DistributionPlanException("relation between distributorGroupId and liveId  not found");
            }

            return response.getData() != null;
        } catch (Exception e) {
            log.error("{} checkDistributorGroupValid error, distributorGroupId:{}", CAT_TYPE, distributorGroupId, e);
            transaction.setStatus(e);
            return false;
        } finally {
            transaction.complete();
        }
    }
}