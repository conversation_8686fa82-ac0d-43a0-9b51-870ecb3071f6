package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.shop.DealShopQueryService;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.tpfun.product.api.common.IResponse;
import com.dianping.tpfun.product.api.sku.aggregate.ProductStaticDetailBySpuServiceV2;
import com.dianping.tpfun.product.api.sku.aggregate.dto.spu.SpuProductDetailDTO;
import com.dianping.tpfun.product.api.sku.request.QuerySpuProductRequest;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.groundpromotion.dto.DealProductDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.PromoDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryPoiAndGoodsReq;
import com.sankuai.carnation.distribution.groundpromotion.enums.ClientTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.product.dto.ProductBaseInfo;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dztheme.spuproduct.SpuThemeQueryService;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import com.sankuai.dztheme.spuproduct.res.SpuAttrDTO;
import com.sankuai.dztheme.spuproduct.res.SpuDTO;
import com.sankuai.dztheme.spuproduct.res.SpuIdDTO;
import com.sankuai.dztheme.spuproduct.res.SpuResult;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wuweizhen
 * @Date: 2023/6/1 17:26
 * @Description:
 */
@Component
@Slf4j
public class DealAcl {
    private final static int COMMON_SPU_ID_TYPE = 16;

    @Autowired
    private DealProductService dealProductService;

    @Resource
    private DealIdMapperService dealIdMapperService;

    @Resource
    private DealGroupQueryService dealGroupQueryService;

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private DealShopQueryService dealShopQueryService;

    @Autowired
    private SpuThemeQueryService spuThemeQueryService;

    @Autowired
    private ProductStaticDetailBySpuServiceV2 productStaticDetailBySpuServiceV2;

    private final static String CATEGORY_NAME = "attr_fix_price_spu_category_id";

    private final static String LION_STANDARD_CATEGORY_SORT_LIST = "com.sankuai.medicalcosmetology.distribution.service.standard.product.category.sort.list";

    private static volatile Map<Long, Integer> sortCategoryMap;

    static {
        //初始化
        List<Long> sortList = Lion.getList(LION_STANDARD_CATEGORY_SORT_LIST, Long.class);
        Map<Long, Integer> sortMap = new HashMap<>();
        for (int i = 0; i < sortList.size(); i++) {
            sortMap.put(sortList.get(i), i);
        }
        sortCategoryMap = Collections.synchronizedMap(sortMap);
        //变更监听
        Lion.addConfigListener(LION_STANDARD_CATEGORY_SORT_LIST, new ConfigListener() {
            @Override
            public void configChanged(ConfigEvent configEvent) {
                log.info("[DealAcl] receive LION_STANDARD_CATEGORY_SORT_LIST event >> {}", configEvent);
                parseConfig(configEvent.getValue());
            }
        });
    }

    private static void parseConfig(String configValueStr) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.parseConfig(java.lang.String)");
        if (StringUtils.isBlank(configValueStr)) {
            configValueStr = Lion.get(LION_STANDARD_CATEGORY_SORT_LIST);
        }
        List<Long> sortList = JSONArray.parseArray(configValueStr, Long.class);
        Map<Long, Integer> sortMap = new HashMap<>();
        for (int i = 0; i < sortList.size(); i++) {
            sortMap.put(sortList.get(i), i);
        }
        sortCategoryMap = Collections.synchronizedMap(sortMap);
    }

    public Map<Long, List<DealProductDTO>> loadDealProduct(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        Map<Long, List<DealProductDTO>> result = new HashMap<>();
        try {
            List<DealProductRequest> dealProductRequestList = buildRequest(queryPoiAndGoodsReq);

            List<DealProductResult> dealProductResultList = dealProductRequestList.stream()
                    .map(dealProductRequest -> dealProductService.query(dealProductRequest)).collect(Collectors.toList());

            List<DealProductDTO> dealProductDTOS = dealProductResultList.stream()
                    .filter(dealProductResult -> Objects.nonNull(dealProductResult.getDeals()))
                    .map(dealProductResult -> dealProductResult.getDeals().stream()
                            .filter(Objects::nonNull)
                            .filter(this::isDealOnline)
                            .map(this::generateDealProductVO)
                            .collect(Collectors.toList()))
                    .reduce((list1, list2) -> {
                        list1.addAll(list2);
                        return list1;
                    }).orElse(Lists.newArrayList());

            dealProductDTOS = dealProductDTOS.stream()
                    .sorted(Comparator.comparingInt(DealProductDTO::getSaleCount).reversed())
                    .collect(Collectors.toList());
            result.put(queryPoiAndGoodsReq.getDpShopId(), dealProductDTOS);
            return result;
        } catch (Exception e) {
            log.error("DealAcl.loadDealProduct error, queryPoiAndGoodsReq={}", queryPoiAndGoodsReq, e);
        }
        return result;
    }

    private boolean isDealOnline(com.sankuai.dztheme.deal.dto.DealProductDTO deal) {
        return Objects.nonNull(deal) && deal.getAttrs().stream().filter(Objects::nonNull).filter(attr -> "dealStatusAttr".equalsIgnoreCase(attr.getName()) && StringUtils.isNotBlank(attr.getValue())).anyMatch(attr -> BooleanUtils.toBoolean(attr.getValue()));
    }
    //
    //private List<Integer> queryBoundedDpDealGroupIds(Integer dpShopId) {
    //    try {
    //        RemoteResponse<List<Long>> response = qrOfflineDealGroupApplicationService.queryBoundedDpDealGroupIds(dpShopId);
    //        if (response.isSuccess()) {
    //            return response.getData().stream().map(Long::intValue).collect(Collectors.toList());
    //        }
    //    } catch (Exception e) {
    //        log.error("CustomizeGroupWrapper.queryBoundedDpDealGroupIds error, dpShopId = {}.", dpShopId, e);
    //    }
    //    return Collections.emptyList();
    //}

    private List<DealProductRequest> buildRequest(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        List<DealProductRequest> queryRequestList = new ArrayList<>();

        List<Integer> dealIdList = queryPoiAndGoodsReq.getDealIdList().stream().map(Long::intValue).collect(Collectors.toList());

        queryRequestList = Lists.partition(dealIdList, 50).stream()
                .map(partList -> {
                    DealProductRequest queryRequest = new DealProductRequest();
                    queryRequest.setPlanId("10002255");
                    List<Integer> idList = queryPoiAndGoodsReq.getPlatform() == 1 ? partList : getMtDealIds(partList);
                    queryRequest.setProductIds(idList);

                    Map<String, Object> extParam = new HashMap<>(5);
                    extParam.put("platform", queryPoiAndGoodsReq.getPlatform());
                    extParam.put("cityId", queryPoiAndGoodsReq.getCityId());
                    extParam.put("userId", queryPoiAndGoodsReq.getUserId() <= 0 ? 0L : queryPoiAndGoodsReq.getUserId());
                    extParam.put("clientType", loadType(queryPoiAndGoodsReq).getCode());
                    extParam.put("dealIds", idList);
                    extParam.put("scene", 400201);
                    String dealProductSource = Lion.getString(Environment.getAppName(), "deal.product.source", null);
                    if (StringUtils.isNotBlank(dealProductSource)) {
                        extParam.put("dealProductSource", dealProductSource);
                    }
                    Map<Integer, Long> dealId2ShopIdForLong = new HashMap<>();
                    if (queryPoiAndGoodsReq.getPlatform() == 1) {
                        dealId2ShopIdForLong = queryRequest.getProductIds().stream().collect(Collectors.toMap(Function.identity(), v -> queryPoiAndGoodsReq.getDpShopId(), (v1, v2) -> v2));
                    } else {
                        Long mtShopId = shopMapperService.dp2mt(queryPoiAndGoodsReq.getDpShopId());
                        dealId2ShopIdForLong = queryRequest.getProductIds().stream().collect(Collectors.toMap(Function.identity(), v -> mtShopId, (v1, v2) -> v2));
                    }
                    extParam.put("dealId2ShopIdForLong", dealId2ShopIdForLong);
                    if (StringUtils.isNotBlank(queryPoiAndGoodsReq.getVersion())) {
                        extParam.put("appVersion", queryPoiAndGoodsReq.getVersion());
                    }
                    Cat.logEvent("AppVersion", "AppVersion:" + queryPoiAndGoodsReq.getVersion());
                    queryRequest.setExtParams(extParam);
                    return queryRequest;
                }).collect(Collectors.toList());

        return queryRequestList;
    }

    private List<SpuRequest> buildSpuThemeRequest(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        List<SpuRequest> queryRequestList = new ArrayList<>();

        List<Integer> spuIdList = queryPoiAndGoodsReq.getDealIdList().stream().map(Long::intValue).collect(Collectors.toList());

        queryRequestList = Lists.partition(spuIdList, 50).stream()
                .map(partList -> {
                    SpuRequest queryRequest = new SpuRequest();
                    queryRequest.setPlanId("11300051");
                    queryRequest.setSpuIds(
                            partList.stream().map(id -> {
                                SpuIdDTO spuIdDTO = new SpuIdDTO();
                                spuIdDTO.setId(id);
                                spuIdDTO.setIdType(COMMON_SPU_ID_TYPE);
                                return spuIdDTO;
                            }).collect(Collectors.toList()));
                    queryRequest.setExtParams(buildExtParams(queryPoiAndGoodsReq));
                    return queryRequest;
                }).collect(Collectors.toList());

        return queryRequestList;
    }

    private Map<String, Object> buildExtParams(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        Map<String, Object> extParam = Maps.newHashMap();
        extParam.put("platform", queryPoiAndGoodsReq.getPlatform());
        extParam.put("cityId", queryPoiAndGoodsReq.getCityId());
        extParam.put("userId", queryPoiAndGoodsReq.getUserId() <= 0 ? 0L : queryPoiAndGoodsReq.getUserId());
        extParam.put("lat", queryPoiAndGoodsReq.getLat());
        extParam.put("lng", queryPoiAndGoodsReq.getLng());
        //设备类型，点评APP-100，美团APP-200，点评H5-101，美团H5-201
        extParam.put("uaCode", loadType(queryPoiAndGoodsReq).getCode());
        extParam.put("appVersion", queryPoiAndGoodsReq.getVersion());
        extParam.put("queryPriceScene", 400000);
        return extParam;
    }

    public Map<Long, List<DealProductDTO>> loadStandardProduct(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        Map<Long, List<DealProductDTO>> result = new HashMap<>();
        try {
            List<SpuRequest> standardProductRequestList = buildSpuThemeRequest(queryPoiAndGoodsReq);
            log.info("[DealAce] loadStandardProduct query req = {}", JSONArray.toJSONString(standardProductRequestList));
            List<SpuResult> standardProductResults = standardProductRequestList.stream()
                    .map(standardProductRequest -> spuThemeQueryService.query(standardProductRequest)).collect(Collectors.toList());
            log.info("[DealAce] loadStandardProduct query resp = {}", JSONArray.toJSONString(standardProductResults));
            List<DealProductDTO> dealProductDTOS = standardProductResults.stream()
                    .filter(standardProductResult -> Objects.nonNull(standardProductResult.getSpuList()))
                    .map(standardProductResult -> standardProductResult.getSpuList().stream()
                            .filter(Objects::nonNull)
                            .filter(SpuDTO::isAvailable)
                            .map(this::buildDealProductDTOFromSpuDTO)
                            .collect(Collectors.toList()))
                    .reduce((list1, list2) -> {
                        list1.addAll(list2);
                        return list1;
                    }).orElse(Lists.newArrayList());

            final Map<Long, Integer> temp = sortCategoryMap;
            dealProductDTOS = dealProductDTOS.stream()
                    .sorted((o1, o2) -> {
                        // 根据分类和销量对商品进行排序，优先按照分类排序，再按照销量排序。如果没有的种类则排到最后面去
                        // 大于0则交换
                        if (!temp.getOrDefault(o1.getCategoryId(), 9999).equals(temp.getOrDefault(o2.getCategoryId(), 9999))) {
                            return temp.getOrDefault(o1.getCategoryId(), 9999) - temp.getOrDefault(o2.getCategoryId(), 9999);
                        } else {
                            return o2.getSaleCount() - o1.getSaleCount();
                        }
                    })
                    .collect(Collectors.toList());
            result.put(Long.valueOf(queryPoiAndGoodsReq.getCityId()), dealProductDTOS);
            return result;
        } catch (Exception e) {
            log.error("DealAcl.loadStandardProduct error, queryPoiAndGoodsReq={}", queryPoiAndGoodsReq, e);
        }
        return result;
    }

    private DealProductDTO buildDealProductDTOFromSpuDTO(SpuDTO spuDTO) {
        DealProductDTO vo = new DealProductDTO();
        vo.setProductId((int) spuDTO.getSpuId().getId());
        vo.setTitle(spuDTO.getSpuName());
        vo.setDetailJumpUrl(spuDTO.getJumpUrl());
        if (spuDTO.getSpuPrice() != null) {
            vo.setSalePrice(StringUtils.isNotBlank(spuDTO.getSpuPrice().getSalePrice()) ? spuDTO.getSpuPrice().getSalePrice() : "0");
            PromoDTO promoDTO = new PromoDTO();
            promoDTO.setPromo(spuDTO.getSpuPrice().getPromo() != null ? spuDTO.getSpuPrice().getPromo().getPromoTag() : "");
            vo.setPromoDTOList(Lists.newArrayList(promoDTO));
        }
        if (spuDTO.getSales() != null) {
            vo.setSale(spuDTO.getSales().getSaleDesc());
            vo.setSaleCount(spuDTO.getSales().getSaleCount());
        }
        vo.setPic(spuDTO.getHeadPic());
        SpuAttrDTO spuAttrDTO = spuDTO.getSpuAttrs().stream().filter(t -> t.getName().equals(CATEGORY_NAME)).findFirst().orElse(new SpuAttrDTO("", 0, "0"));
        vo.setCategoryId(Long.parseLong(spuAttrDTO.getValueStr()));
        return vo;
    }


    public VCClientTypeEnum loadType(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        boolean isAPP = queryPoiAndGoodsReq.getClientType() == ClientTypeEnum.NATIVE.code;
        boolean isDP = queryPoiAndGoodsReq.getPlatform() == 1;
        boolean isMini = queryPoiAndGoodsReq.getClientType() == ClientTypeEnum.MINIPROGRAM.code;
        if (isAPP && isDP) {
            return VCClientTypeEnum.DP_APP;
        }
        if (isAPP && !isDP) {
            return VCClientTypeEnum.MT_APP;
        }
        if (isMini && isDP) {
            return VCClientTypeEnum.DP_XCX;
        }
        if (isMini && !isDP) {
            return VCClientTypeEnum.MT_XCX;
        }
        return isDP ? VCClientTypeEnum.DP_M : VCClientTypeEnum.MT_I;

    }

    private DealProductDTO generateDealProductVO(com.sankuai.dztheme.deal.dto.DealProductDTO deal) {
        DealProductDTO vo = new DealProductDTO();
        vo.setProductId(deal.getProductId());
        vo.setTitle(deal.getName());
        String pageSource = Lion.getString(Environment.getAppName(), "deal.product.pagesource", null);
        if (org.apache.commons.lang.StringUtils.isNotBlank(pageSource)) {
            String url = deal.getDetailUr();
            if (url.contains("?")) {
                url = url+ "&"+ pageSource;
            } else {
                url = url + "?" + pageSource;
            }
            vo.setDetailJumpUrl(url);
        } else {
            vo.setDetailJumpUrl(deal.getDetailUr());
        }
        vo.setSalePrice(deal.getBasePriceTag());
        if (deal.getSale() != null) {
            vo.setSale(deal.getSale().getSaleTag());
            vo.setSaleCount(deal.getSale().getSale());
        }
        vo.setPic(deal.getHeadPic());
        List<PromoDTO> promoDTOList = deal.getPromoPrices().stream()
                .filter(Objects::nonNull)
                .filter(productPromoDTO -> productPromoDTO.getPromoType() == PromoTypeEnum.DIRECT_PROMO.getType())
                .map(productPromoDTO -> {
                    PromoDTO promoDTO = new PromoDTO();
                    promoDTO.setPromo(productPromoDTO.getPromoTag());
                    vo.setSalePrice(productPromoDTO.getPromoPriceTag());
                    return promoDTO;
                }).collect(Collectors.toList());
        vo.setPromoDTOList(promoDTOList);
        return vo;
    }

    public List<Integer> getMtDealIds(List<Integer> dpIds) {
        List<IdMapper> idMappers = dealIdMapperService.queryByDpDealGroupIds(dpIds);
        return idMappers.stream().map(IdMapper::getMtDealGroupID).collect(Collectors.toList());
    }

    public Map<Long, List<Long>> dealQueryShop(List<Long> dealIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.dealQueryShop(java.util.List)");
        Map<Long, List<Long>> dealAndShopMap = new HashMap<>();
        List<List<Long>> partitionDpDealGroupIds = Lists.partition(dealIdList, 20);
        for (List<Long> list : partitionDpDealGroupIds) {
            QueryByDealGroupIdRequest request = buildQueryByDealGroupIdRequest(list);

            try {
                // 单次查询20条，超出会返回错误 https://km.sankuai.com/collabpage/1603747248
                QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                if (null != response && null != response.getData() && CollectionUtils.isNotEmpty(response.getData().getList())) {
                    List<DealGroupDTO> dealGroupDTOS = response.getData().getList();
                    dealGroupDTOS.stream().forEach(dealGroupDTO -> {
                        DealGroupDisplayShopDTO dto = dealGroupDTO.getDisplayShopInfo();
                        if (Objects.nonNull(dto) && CollectionUtils.isNotEmpty(dto.getDpDisplayShopIds())) {
                            dealAndShopMap.put(dealGroupDTO.getDpDealGroupId(), dto.getDpDisplayShopIds());
                        }
                    });
                }
                log.info("[DealAcl] dealQueryShop dealIdList={},result={}", dealIdList, dealAndShopMap);
            } catch (TException e) {
                String msg = "dealQueryShop, request:" + JSON.toJSONString(request);
                log.error(msg, e);
            }
        }
        return dealAndShopMap;
    }

    private QueryByDealGroupIdRequest buildQueryByDealGroupIdRequest(List<Long> list) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.buildQueryByDealGroupIdRequest(java.util.List)");
        return QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(new HashSet<>(list), IdTypeEnum.DP)
                .displayShop(DealGroupDisplayShopBuilder.builder().dpDisplayShopIds())
                .build();
    }

    public List<Long> queryAllOnlineDealIdList(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        long dpShopId = queryPoiAndGoodsReq.getDpShopId();
        // 100 dp 200 mt 没有枚举类 见https://service.sankuai.com/#/services/deal-shop-query-service/docs/com.dianping.deal.shop.DealShopQueryService_pigeon/com.dianping.deal.shop.DealShopQueryService.querySaleDealGroupId(%252C%2520%252C%2520%252C%2520)
        int platform = queryPoiAndGoodsReq.getPlatform() == PlatformEnum.DP.getCode() ? 100 : 200;

        int start = 0;
        int limit = 100;
        int num = 100;
        List<Long> partDealIdList = dealShopQueryService.querySaleDealGroupId(dpShopId, platform, start, limit);
        List<Long> dealIdList = new ArrayList<>();
        while (org.apache.commons.collections.CollectionUtils.isNotEmpty(partDealIdList)) {
            dealIdList.addAll(partDealIdList);
            start += num;
            partDealIdList = dealShopQueryService.querySaleDealGroupId(dpShopId, platform, start, limit);
        }
        return dealIdList;
    }

    public List<Long> queryAllOnlineDealIdList(long shopId, int platform) {
        //long dpShopId = queryPoiAndGoodsReq.getDpShopId();
        // 100 dp 200 mt 没有枚举类 见https://service.sankuai.com/#/services/deal-shop-query-service/docs/com.dianping.deal.shop.DealShopQueryService_pigeon/com.dianping.deal.shop.DealShopQueryService.querySaleDealGroupId(%252C%2520%252C%2520%252C%2520)
        //int platform = queryPoiAndGoodsReq.getPlatform() == PlatformEnum.DP.getCode() ? 100 : 200;

        int start = 0;
        int limit = 100;
        int num = 100;
        List<Long> partDealIdList = dealShopQueryService.querySaleDealGroupId(shopId, platform, start, limit);
        List<Long> dealIdList = new ArrayList<>();
        while (org.apache.commons.collections.CollectionUtils.isNotEmpty(partDealIdList)) {
            dealIdList.addAll(partDealIdList);
            start += num;
            partDealIdList = dealShopQueryService.querySaleDealGroupId(shopId, platform, start, limit);
        }
        return dealIdList;
    }

    public List<PromotionalDeal> buildFinalDealList(List<PromotionalDeal> promotionalDealList) {
        List<PromotionalDeal> finalPromotionDealList = promotionalDealList.stream()
                .map(promotionalDeal -> {
                    if (promotionalDeal.getDealId() == 0L) {
                        QueryPoiAndGoodsReq queryPoiAndGoodsReq = new QueryPoiAndGoodsReq();
                        queryPoiAndGoodsReq.setDpShopId(promotionalDeal.getShopId());
                        queryPoiAndGoodsReq.setPlatform(PlatformEnum.DP.getCode());
                        List<Long> dealIdList = queryAllOnlineDealIdList(queryPoiAndGoodsReq);
                        List<PromotionalDeal> partDealList = new ArrayList<>();
                        for (long dealId : dealIdList) {
                            partDealList.add(PromotionalDeal.builder()
                                    .id(promotionalDeal.getId())
                                    .groundPromotionalId(promotionalDeal.getGroundPromotionalId())
                                    .promotionalType(promotionalDeal.getPromotionalType())
                                    .shopId(promotionalDeal.getShopId())
                                    .merchantsId(promotionalDeal.getMerchantsId())
                                    .status(promotionalDeal.getStatus())
                                    .bindMisid(promotionalDeal.getBindMisid())
                                    .dealId(dealId).build());
                        }
                        return partDealList;
                    } else {
                        return Lists.newArrayList(promotionalDeal);
                    }
                }).reduce((list1, list2) -> {
                    list1.addAll(list2);
                    return list1;
                }).orElse(Lists.newArrayList());
        return finalPromotionDealList;
    }

    public Map<Long, ProductBaseInfo> loadStandardProductInfo(List<Long> spuIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.loadStandardProductInfo(java.util.List)");
        Map<Long, ProductBaseInfo> productBaseInfoMap = new HashMap<>(spuIdList.size());
        try {
            Lists.partition(spuIdList, 50).forEach(partList -> {
                        IResponse<Map<Long, SpuProductDetailDTO>> mapIResponse = productStaticDetailBySpuServiceV2.querySpuPlatformProductByRequest(buildQuerySpuProductRequest(partList));
                        log.info("[DealAcl] loadStandardProductInfo resp = {}", JSONObject.toJSONString(mapIResponse));
                        if (!mapIResponse.isSuccess() || mapIResponse.getResult() == null || mapIResponse.getResult().isEmpty()) {
                            log.error("[DealAcl] loadStandardProductInfo resp = {}", JSONObject.toJSONString(mapIResponse));
                        }
                        mapIResponse.getResult().forEach((k, v) -> productBaseInfoMap.put(k, buildProductBaseInfo(v)));
                    }
            );
            return productBaseInfoMap;
        } catch (Exception exception) {
            log.error("[DealAcl] loadStandardProductInfo error req = {}", JSONObject.toJSONString(spuIdList), exception);
            return null;
        }
    }

    public Map<Long, SpuProductDetailDTO> loadStandardProductInfoByProductId(List<Long> productIdList, int mtCityId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.loadStandardProductInfoByProductId(java.util.List,int)");
        try {
            Map<Long, SpuProductDetailDTO> spuProductDetailDTOMap = Lists.partition(productIdList, 100).stream()
                    .map(partList -> {
                        IResponse<Map<Long, SpuProductDetailDTO>> mapIResponse = productStaticDetailBySpuServiceV2.querySpuPlatformProductById(partList, mtCityId);
                        log.info("[DealAcl] loadStandardProductInfoByProductId resp = {}", JSONObject.toJSONString(mapIResponse));
                        if (!mapIResponse.isSuccess() || mapIResponse.getResult() == null || mapIResponse.getResult().isEmpty()) {
                            log.error("[DealAcl] loadStandardProductInfoByProductId resp = {}", JSONObject.toJSONString(mapIResponse));
                            return null;
                        }
                        return mapIResponse.getResult();
                    })
                    .filter(Objects::nonNull)
                    .reduce((resultMap, itemMap) -> {
                        resultMap.putAll(itemMap);
                        return resultMap;
                    }).orElse(Maps.newHashMap());

            return spuProductDetailDTOMap;
        } catch (Exception exception) {
            log.error("[DealAcl] loadStandardProductInfo error req = {}", JSONObject.toJSONString(productIdList), exception);
            return null;
        }
    }

    private QuerySpuProductRequest buildQuerySpuProductRequest(List<Long> productIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.buildQuerySpuProductRequest(java.util.List)");
        QuerySpuProductRequest querySpuProductRequest = new QuerySpuProductRequest();
        querySpuProductRequest.setSpuIdList(productIdList);
        return querySpuProductRequest;
    }

    private ProductBaseInfo buildProductBaseInfo(SpuProductDetailDTO v) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl.buildProductBaseInfo(com.dianping.tpfun.product.api.sku.aggregate.dto.spu.SpuProductDetailDTO)");
        if (v == null) {
            return null;
        }
        ProductBaseInfo productBaseInfo = new ProductBaseInfo();
        productBaseInfo.setProductId(v.getProductId() != null ? v.getProductId() : 0);
        productBaseInfo.setProductType(Math.toIntExact(v.getCategoryId() != null ? v.getCategoryId() : 0));
        productBaseInfo.setTitle(v.getProductName());
        productBaseInfo.setAvailable(v.getStatus() == 1);
        return productBaseInfo;
    }
}
