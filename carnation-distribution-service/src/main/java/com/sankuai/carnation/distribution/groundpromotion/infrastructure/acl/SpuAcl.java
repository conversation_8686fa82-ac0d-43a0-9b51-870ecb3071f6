package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.deal.tag.DealMetaTagQueryService;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.dianping.gmm.investment.activity.rule.api.dto.IMResponse;
import com.dianping.gmm.investment.activity.rule.api.enums.rule.DealGroupCheckRuleEnum;
import com.dianping.gmm.investment.activity.rule.api.service.InvestmentActivityRuleQueryService;
import com.google.common.collect.Lists;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wuweizhen
 * @Date: 2023/6/12 16:00
 * @Description:
 */
@Slf4j
@Component
public class SpuAcl {

    @Autowired
    private InvestmentActivityRuleQueryService investmentActivityRuleQueryService;

    @Autowired
    private DealMetaTagQueryService dealMetaTagQueryService;

    /**
     * * 根据活动id，查询spu
     *
     * @param merchantsId
     */
    public List<String> querySpu(long merchantsId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.SpuAcl.querySpu(long)");
        try {
            IMResponse<Map<Integer, Object>> response = investmentActivityRuleQueryService.queryActivityRuleInput(merchantsId);
            if (response.isFail()) {
                log.error("[querySpu] merchantsId is {},response is {}", merchantsId, JSONObject.toJSONString(response));
                return Lists.newArrayList();
            }
            HashSet<Long> spuIds = (HashSet<Long>) response.getData().get(DealGroupCheckRuleEnum.DEAL_TAG.code);
            if (CollectionUtils.isEmpty(spuIds)) {
                log.error("[querySpu] merchantsId is {},response is {}", merchantsId, JSONObject.toJSONString(response));
                return Lists.newArrayList();
            }
            List<MetaTagDTO> metaTagDTOList = dealMetaTagQueryService.queryByTagIds(Lists.newArrayList(spuIds));
            if (CollectionUtils.isEmpty(metaTagDTOList)) {
                log.error("[querySpu] merchantsId is {},response is {},metaTagDTOList is empty", merchantsId, JSONObject.toJSONString(response));
                return Lists.newArrayList();
            }
            return metaTagDTOList.stream().map(metaTagDTO -> metaTagDTO.getValue())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[querySpu] merchantsId is {},exception is ", merchantsId, e);
            return Lists.newArrayList();
        }

    }


}