package com.sankuai.carnation.distribution.promocode.rebate.repository.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RbOrderVerifyRebateCommissionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public RbOrderVerifyRebateCommissionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public RbOrderVerifyRebateCommissionExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public RbOrderVerifyRebateCommissionExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public RbOrderVerifyRebateCommissionExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Integer value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Integer value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Integer value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Integer value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Integer> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Integer> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andCouponIdIsNull() {
            addCriterion("coupon_id is null");
            return (Criteria) this;
        }

        public Criteria andCouponIdIsNotNull() {
            addCriterion("coupon_id is not null");
            return (Criteria) this;
        }

        public Criteria andCouponIdEqualTo(String value) {
            addCriterion("coupon_id =", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotEqualTo(String value) {
            addCriterion("coupon_id <>", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdGreaterThan(String value) {
            addCriterion("coupon_id >", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_id >=", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdLessThan(String value) {
            addCriterion("coupon_id <", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdLessThanOrEqualTo(String value) {
            addCriterion("coupon_id <=", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdLike(String value) {
            addCriterion("coupon_id like", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotLike(String value) {
            addCriterion("coupon_id not like", value, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdIn(List<String> values) {
            addCriterion("coupon_id in", values, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotIn(List<String> values) {
            addCriterion("coupon_id not in", values, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdBetween(String value1, String value2) {
            addCriterion("coupon_id between", value1, value2, "couponId");
            return (Criteria) this;
        }

        public Criteria andCouponIdNotBetween(String value1, String value2) {
            addCriterion("coupon_id not between", value1, value2, "couponId");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeIsNull() {
            addCriterion("rebate_biz_type is null");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeIsNotNull() {
            addCriterion("rebate_biz_type is not null");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeEqualTo(Integer value) {
            addCriterion("rebate_biz_type =", value, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeNotEqualTo(Integer value) {
            addCriterion("rebate_biz_type <>", value, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeGreaterThan(Integer value) {
            addCriterion("rebate_biz_type >", value, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("rebate_biz_type >=", value, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeLessThan(Integer value) {
            addCriterion("rebate_biz_type <", value, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeLessThanOrEqualTo(Integer value) {
            addCriterion("rebate_biz_type <=", value, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeIn(List<Integer> values) {
            addCriterion("rebate_biz_type in", values, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeNotIn(List<Integer> values) {
            addCriterion("rebate_biz_type not in", values, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeBetween(Integer value1, Integer value2) {
            addCriterion("rebate_biz_type between", value1, value2, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("rebate_biz_type not between", value1, value2, "rebateBizType");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdIsNull() {
            addCriterion("rebate_biz_id is null");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdIsNotNull() {
            addCriterion("rebate_biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdEqualTo(String value) {
            addCriterion("rebate_biz_id =", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdNotEqualTo(String value) {
            addCriterion("rebate_biz_id <>", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdGreaterThan(String value) {
            addCriterion("rebate_biz_id >", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("rebate_biz_id >=", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdLessThan(String value) {
            addCriterion("rebate_biz_id <", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdLessThanOrEqualTo(String value) {
            addCriterion("rebate_biz_id <=", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdLike(String value) {
            addCriterion("rebate_biz_id like", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdNotLike(String value) {
            addCriterion("rebate_biz_id not like", value, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdIn(List<String> values) {
            addCriterion("rebate_biz_id in", values, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdNotIn(List<String> values) {
            addCriterion("rebate_biz_id not in", values, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdBetween(String value1, String value2) {
            addCriterion("rebate_biz_id between", value1, value2, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateBizIdNotBetween(String value1, String value2) {
            addCriterion("rebate_biz_id not between", value1, value2, "rebateBizId");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeIsNull() {
            addCriterion("rebate_user_type is null");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeIsNotNull() {
            addCriterion("rebate_user_type is not null");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeEqualTo(Integer value) {
            addCriterion("rebate_user_type =", value, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeNotEqualTo(Integer value) {
            addCriterion("rebate_user_type <>", value, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeGreaterThan(Integer value) {
            addCriterion("rebate_user_type >", value, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("rebate_user_type >=", value, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeLessThan(Integer value) {
            addCriterion("rebate_user_type <", value, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeLessThanOrEqualTo(Integer value) {
            addCriterion("rebate_user_type <=", value, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeIn(List<Integer> values) {
            addCriterion("rebate_user_type in", values, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeNotIn(List<Integer> values) {
            addCriterion("rebate_user_type not in", values, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeBetween(Integer value1, Integer value2) {
            addCriterion("rebate_user_type between", value1, value2, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("rebate_user_type not between", value1, value2, "rebateUserType");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdIsNull() {
            addCriterion("rebate_user_id is null");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdIsNotNull() {
            addCriterion("rebate_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdEqualTo(String value) {
            addCriterion("rebate_user_id =", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdNotEqualTo(String value) {
            addCriterion("rebate_user_id <>", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdGreaterThan(String value) {
            addCriterion("rebate_user_id >", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("rebate_user_id >=", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdLessThan(String value) {
            addCriterion("rebate_user_id <", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdLessThanOrEqualTo(String value) {
            addCriterion("rebate_user_id <=", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdLike(String value) {
            addCriterion("rebate_user_id like", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdNotLike(String value) {
            addCriterion("rebate_user_id not like", value, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdIn(List<String> values) {
            addCriterion("rebate_user_id in", values, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdNotIn(List<String> values) {
            addCriterion("rebate_user_id not in", values, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdBetween(String value1, String value2) {
            addCriterion("rebate_user_id between", value1, value2, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andRebateUserIdNotBetween(String value1, String value2) {
            addCriterion("rebate_user_id not between", value1, value2, "rebateUserId");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentIsNull() {
            addCriterion("predict_rebate_cent is null");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentIsNotNull() {
            addCriterion("predict_rebate_cent is not null");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentEqualTo(Long value) {
            addCriterion("predict_rebate_cent =", value, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentNotEqualTo(Long value) {
            addCriterion("predict_rebate_cent <>", value, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentGreaterThan(Long value) {
            addCriterion("predict_rebate_cent >", value, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentGreaterThanOrEqualTo(Long value) {
            addCriterion("predict_rebate_cent >=", value, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentLessThan(Long value) {
            addCriterion("predict_rebate_cent <", value, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentLessThanOrEqualTo(Long value) {
            addCriterion("predict_rebate_cent <=", value, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentIn(List<Long> values) {
            addCriterion("predict_rebate_cent in", values, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentNotIn(List<Long> values) {
            addCriterion("predict_rebate_cent not in", values, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentBetween(Long value1, Long value2) {
            addCriterion("predict_rebate_cent between", value1, value2, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andPredictRebateCentNotBetween(Long value1, Long value2) {
            addCriterion("predict_rebate_cent not between", value1, value2, "predictRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentIsNull() {
            addCriterion("final_rebate_cent is null");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentIsNotNull() {
            addCriterion("final_rebate_cent is not null");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentEqualTo(Long value) {
            addCriterion("final_rebate_cent =", value, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentNotEqualTo(Long value) {
            addCriterion("final_rebate_cent <>", value, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentGreaterThan(Long value) {
            addCriterion("final_rebate_cent >", value, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentGreaterThanOrEqualTo(Long value) {
            addCriterion("final_rebate_cent >=", value, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentLessThan(Long value) {
            addCriterion("final_rebate_cent <", value, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentLessThanOrEqualTo(Long value) {
            addCriterion("final_rebate_cent <=", value, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentIn(List<Long> values) {
            addCriterion("final_rebate_cent in", values, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentNotIn(List<Long> values) {
            addCriterion("final_rebate_cent not in", values, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentBetween(Long value1, Long value2) {
            addCriterion("final_rebate_cent between", value1, value2, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andFinalRebateCentNotBetween(Long value1, Long value2) {
            addCriterion("final_rebate_cent not between", value1, value2, "finalRebateCent");
            return (Criteria) this;
        }

        public Criteria andRebateDescIsNull() {
            addCriterion("rebate_desc is null");
            return (Criteria) this;
        }

        public Criteria andRebateDescIsNotNull() {
            addCriterion("rebate_desc is not null");
            return (Criteria) this;
        }

        public Criteria andRebateDescEqualTo(String value) {
            addCriterion("rebate_desc =", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescNotEqualTo(String value) {
            addCriterion("rebate_desc <>", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescGreaterThan(String value) {
            addCriterion("rebate_desc >", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescGreaterThanOrEqualTo(String value) {
            addCriterion("rebate_desc >=", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescLessThan(String value) {
            addCriterion("rebate_desc <", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescLessThanOrEqualTo(String value) {
            addCriterion("rebate_desc <=", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescLike(String value) {
            addCriterion("rebate_desc like", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescNotLike(String value) {
            addCriterion("rebate_desc not like", value, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescIn(List<String> values) {
            addCriterion("rebate_desc in", values, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescNotIn(List<String> values) {
            addCriterion("rebate_desc not in", values, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescBetween(String value1, String value2) {
            addCriterion("rebate_desc between", value1, value2, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andRebateDescNotBetween(String value1, String value2) {
            addCriterion("rebate_desc not between", value1, value2, "rebateDesc");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdIsNull() {
            addCriterion("activity_record_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdIsNotNull() {
            addCriterion("activity_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdEqualTo(Long value) {
            addCriterion("activity_record_id =", value, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdNotEqualTo(Long value) {
            addCriterion("activity_record_id <>", value, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdGreaterThan(Long value) {
            addCriterion("activity_record_id >", value, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("activity_record_id >=", value, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdLessThan(Long value) {
            addCriterion("activity_record_id <", value, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("activity_record_id <=", value, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdIn(List<Long> values) {
            addCriterion("activity_record_id in", values, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdNotIn(List<Long> values) {
            addCriterion("activity_record_id not in", values, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdBetween(Long value1, Long value2) {
            addCriterion("activity_record_id between", value1, value2, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andActivityRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("activity_record_id not between", value1, value2, "activityRecordId");
            return (Criteria) this;
        }

        public Criteria andPlatformUserIsNull() {
            addCriterion("platform_user is null");
            return (Criteria) this;
        }

        public Criteria andPlatformUserIsNotNull() {
            addCriterion("platform_user is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformUserEqualTo(String value) {
            addCriterion("platform_user =", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserNotEqualTo(String value) {
            addCriterion("platform_user <>", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserGreaterThan(String value) {
            addCriterion("platform_user >", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserGreaterThanOrEqualTo(String value) {
            addCriterion("platform_user >=", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserLessThan(String value) {
            addCriterion("platform_user <", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserLessThanOrEqualTo(String value) {
            addCriterion("platform_user <=", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserLike(String value) {
            addCriterion("platform_user like", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserNotLike(String value) {
            addCriterion("platform_user not like", value, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserIn(List<String> values) {
            addCriterion("platform_user in", values, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserNotIn(List<String> values) {
            addCriterion("platform_user not in", values, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserBetween(String value1, String value2) {
            addCriterion("platform_user between", value1, value2, "platformUser");
            return (Criteria) this;
        }

        public Criteria andPlatformUserNotBetween(String value1, String value2) {
            addCriterion("platform_user not between", value1, value2, "platformUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserIsNull() {
            addCriterion("group_new_user is null");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserIsNotNull() {
            addCriterion("group_new_user is not null");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserEqualTo(String value) {
            addCriterion("group_new_user =", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserNotEqualTo(String value) {
            addCriterion("group_new_user <>", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserGreaterThan(String value) {
            addCriterion("group_new_user >", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserGreaterThanOrEqualTo(String value) {
            addCriterion("group_new_user >=", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserLessThan(String value) {
            addCriterion("group_new_user <", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserLessThanOrEqualTo(String value) {
            addCriterion("group_new_user <=", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserLike(String value) {
            addCriterion("group_new_user like", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserNotLike(String value) {
            addCriterion("group_new_user not like", value, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserIn(List<String> values) {
            addCriterion("group_new_user in", values, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserNotIn(List<String> values) {
            addCriterion("group_new_user not in", values, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserBetween(String value1, String value2) {
            addCriterion("group_new_user between", value1, value2, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andGroupNewUserNotBetween(String value1, String value2) {
            addCriterion("group_new_user not between", value1, value2, "groupNewUser");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountIsNull() {
            addCriterion("verify_amount is null");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountIsNotNull() {
            addCriterion("verify_amount is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountEqualTo(Long value) {
            addCriterion("verify_amount =", value, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountNotEqualTo(Long value) {
            addCriterion("verify_amount <>", value, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountGreaterThan(Long value) {
            addCriterion("verify_amount >", value, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("verify_amount >=", value, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountLessThan(Long value) {
            addCriterion("verify_amount <", value, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountLessThanOrEqualTo(Long value) {
            addCriterion("verify_amount <=", value, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountIn(List<Long> values) {
            addCriterion("verify_amount in", values, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountNotIn(List<Long> values) {
            addCriterion("verify_amount not in", values, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountBetween(Long value1, Long value2) {
            addCriterion("verify_amount between", value1, value2, "verifyAmount");
            return (Criteria) this;
        }

        public Criteria andVerifyAmountNotBetween(Long value1, Long value2) {
            addCriterion("verify_amount not between", value1, value2, "verifyAmount");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}