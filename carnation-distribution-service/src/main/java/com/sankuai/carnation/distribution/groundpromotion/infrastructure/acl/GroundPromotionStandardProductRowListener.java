package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionStandardProductRowBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/10/23
 * @Description:
 */
@Slf4j
public class GroundPromotionStandardProductRowListener implements ReadListener<GroundPromotionStandardProductRowBO> {

    @Getter
    private List<GroundPromotionStandardProductRowBO> cachedDataList = Lists.newArrayList();

    @Override
    public void invoke(GroundPromotionStandardProductRowBO groundPromotionStandardProductRowBO, AnalysisContext analysisContext) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionStandardProductRowListener.invoke(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionStandardProductRowBO,com.alibaba.excel.context.AnalysisContext)");
        if (!checkFormat(groundPromotionStandardProductRowBO)) {
            groundPromotionStandardProductRowBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
            groundPromotionStandardProductRowBO.setFailReason("上传格式不正确");
        }
        cachedDataList.add(groundPromotionStandardProductRowBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionStandardProductRowListener.doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext)");
    }

    private boolean checkFormat(GroundPromotionStandardProductRowBO groundPromotionStandardProductRowBO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionStandardProductRowListener.checkFormat(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionStandardProductRowBO)");
        try {
            Long.parseLong(groundPromotionStandardProductRowBO.getCityId());
            Long.parseLong(groundPromotionStandardProductRowBO.getDealId());
        } catch (Exception e) {
            log.error("GroundPromotionStandardProductRowBO param is invalid, param is {}, exception is {}", JSONObject.toJSON(groundPromotionStandardProductRowBO), e);
            return false;
        }
        return true;
    }
}
