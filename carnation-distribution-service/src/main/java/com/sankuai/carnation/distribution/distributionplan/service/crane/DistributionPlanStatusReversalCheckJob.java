package com.sankuai.carnation.distribution.distributionplan.service.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.carnation.distribution.distributionplan.constant.LionConstant;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanPageQuery;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanStatusUpdateRequest;
import com.sankuai.carnation.distribution.distributionplan.domain.model.PageResult;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanOperationEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanStatusEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DistributionPlanStatusReversalCheckJob {

    @Resource
    private DistributionPlanDoService distributionPlanDoService;

    private static final long MILLIS_PER_SECOND = 1000L;

    @Crane("com.sankuai.medicalcosmetology.distribution.service.distribution.plan.status.check.task")
    public void execute(){
        try {
            int pageNo = 1;
            int pageSize = 100;
            DistributionPlanPageQuery pageQuery = DistributionPlanPageQuery.builder()
                    .channel(DistributionBusinessChannelEnum.WEI_BO.getCode())
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .statusList(Lists.newArrayList(DistributionPlanStatusEnum.INITIALIZE.getCode(), DistributionPlanStatusEnum.EXECUTING.getCode()))
                    .build();
            PageResult<DistributionPlanDO> pageResult = distributionPlanDoService.pageQueryDistributionPlan(pageQuery);
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                log.info("DistributionPlanStatusReversalCheckJob.execute distributionPlan not found, pageQuery:{}", pageQuery);
                return;
            }

            long maxToleranceDiffMillis = MILLIS_PER_SECOND * Lion.getLong(MdpContextUtils.getAppKey(), LionConstant.PLAN_STATUS_REVERSAL_MAX_TOLERANCE_DIFF_SECONDS, 30L);
            batchCheckDistributionPlanStatusReversal(pageResult.getRecords(), maxToleranceDiffMillis);

            int lastPageNo = pageResult.getTotalCount().intValue() / pageSize + 1;
            while (pageNo < lastPageNo) {
                pageNo++;
                pageQuery.setPageNo(pageNo);
                pageResult = distributionPlanDoService.pageQueryDistributionPlan(pageQuery);
                if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                    log.info("DistributionPlanStatusReversalCheckJob.execute distributionPlan not found, pageQuery:{}", pageQuery);
                    break;
                }
                batchCheckDistributionPlanStatusReversal(pageResult.getRecords(), maxToleranceDiffMillis);
            }
        } catch (Exception e) {
            log.error("DistributionPlanStatusReversalCheckJob.execute failed", e);
        }
    }

    private void batchCheckDistributionPlanStatusReversal(List<DistributionPlanDO> distributionPlanDos, long maxToleranceDiffMillis) {
        distributionPlanDos.forEach(distributionPlanDo -> {
            try {
                verifyStatusReversalAsExpected(distributionPlanDo, maxToleranceDiffMillis);
            } catch (RuntimeException re) {
                String errorMsg = String.format("checkTime:%s, planId:%s,status:%s,preBeginTime:%s,preEndTime:%s,maxToleranceDiffMillis:%s",
                        System.currentTimeMillis(), distributionPlanDo.getPlanId(), distributionPlanDo.getStatus(), distributionPlanDo.getPreBeginTime().getTime(), distributionPlanDo.getPreEndTime().getTime(), maxToleranceDiffMillis);
                Cat.logEvent(re.getMessage(), errorMsg);
                log.info("DistributionPlanStatusReversalCheckJob found unexpected plan status reversal, distributionPlan:{}, maxToleranceDiffMillis:{}", errorMsg, maxToleranceDiffMillis, re);
                compensateUnExpectedStatusReversal(distributionPlanDo);
            }
        });
    }

    private void verifyStatusReversalAsExpected(DistributionPlanDO distributionPlanDo, long maxToleranceDiffMillis) {
        long currentTimeStamp = System.currentTimeMillis();
        if (distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.INITIALIZE)) {
            long timeDiff = Math.subtractExact(currentTimeStamp, distributionPlanDo.getPreBeginTime().getTime());
            if (timeDiff >= maxToleranceDiffMillis) {
                throw new RuntimeException("DistributionPlanEnableUnPunctual");
            }

            return;
        }

        if (distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.EXECUTING)) {
            long timeDiff = Math.subtractExact(currentTimeStamp, distributionPlanDo.getPreEndTime().getTime());
            if (timeDiff >= maxToleranceDiffMillis) {
                throw new RuntimeException("DistributionPlanDisableUnPunctual");
            }
        }
    }

    private void compensateUnExpectedStatusReversal(DistributionPlanDO distributionPlanDo) {
        if (!Lion.getBoolean(MdpContextUtils.getAppKey(), LionConstant.PLAN_STATUS_REVERSAL_COMPENSATE_SWITCH, false)) {
            return;
        }

        log.info("DistributionPlanStatusReversalCheckJob compensate switch on, distributionPlanDo:{}", distributionPlanDo);
        DistributionPlanStatusUpdateRequest request = DistributionPlanStatusUpdateRequest.builder()
                .planId(distributionPlanDo.getPlanId())
                .channel(distributionPlanDo.getChannel())
                .build();
        if (distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.INITIALIZE)
                && distributionPlanDo.getPreBeginTime().getTime() <= System.currentTimeMillis()) {
            log.info("DistributionPlanStatusReversalCheckJob updateDistributionPlanStatus to begin, distributionPlanDo:{}", distributionPlanDo);
            request.setOperation(DistributionPlanOperationEnum.BEGIN);
            request.setOperationTime(distributionPlanDo.getPreBeginTime().getTime());
            distributionPlanDoService.updateDistributionPlanStatus(request);
            return;
        }

        if (distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.EXECUTING)
                && distributionPlanDo.getPreEndTime().getTime() <= System.currentTimeMillis()) {
            log.info("DistributionPlanStatusReversalCheckJob updateDistributionPlanStatus to end, distributionPlanDo:{}", distributionPlanDo);
            request.setOperation(DistributionPlanOperationEnum.END);
            request.setOperationTime(distributionPlanDo.getPreEndTime().getTime());
            distributionPlanDoService.updateDistributionPlanStatus(request);
        }
    }
}
