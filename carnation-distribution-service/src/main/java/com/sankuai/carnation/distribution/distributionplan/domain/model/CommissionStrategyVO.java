package com.sankuai.carnation.distribution.distributionplan.domain.model;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.carnation.distribution.distributionplan.constant.LionConstant;
import com.sankuai.carnation.distribution.intention.dto.user.module.CommissionStrategy;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommissionStrategyVO implements Serializable {

    /**
     * 佣金率
     */
    private BigDecimal commissionRate;

    /**
     * 达人佣金率
     */
    private BigDecimal distributorCommissionRate;

    /**
     * 美团平台佣金率
     */
    private BigDecimal platformCommissionRate;

    /**
     * 渠道佣金率
     */
    private BigDecimal channelCommissionRate;

    public static CommissionStrategyVO buildByCommissionRate(BigDecimal commissionRate, DistributionBusinessChannelEnum channel) {
        MathContext mathContext = new MathContext(2, RoundingMode.DOWN);
        BigDecimal platformCommissionRate = new BigDecimal(Lion.getString(MdpContextUtils.getAppKey(), LionConstant.PLATFORM_COMMISSION_RATE, "4.0"), mathContext);
        Map<String, String> channelTypeToCommissionRateMap = Lion.getMap(MdpContextUtils.getAppKey(), LionConstant.CHANNEL_COMMISSION_RATE_MAP, String.class, Maps.newHashMap());
        BigDecimal channelCommissionRate = new BigDecimal(channelTypeToCommissionRateMap.getOrDefault(channel.getCode(), "4.0"), mathContext);

        CommissionStrategyVO commissionStrategyVo = new CommissionStrategyVO();
        commissionStrategyVo.setCommissionRate(commissionRate.setScale(1, RoundingMode.DOWN));
        commissionStrategyVo.setPlatformCommissionRate(platformCommissionRate);
        commissionStrategyVo.setChannelCommissionRate(channelCommissionRate);

        BigDecimal distributorCommissionRate = commissionStrategyVo.getCommissionRate().subtract(platformCommissionRate).subtract(channelCommissionRate);
        commissionStrategyVo.setDistributorCommissionRate(distributorCommissionRate);
        return commissionStrategyVo;
    }

    public CommissionStrategy toCommissionStrategy() {
      return  CommissionStrategy.builder()
              .channelCommissionRate(channelCommissionRate)
              .commissionRate(commissionRate)
              .distributorCommissionRate(distributorCommissionRate)
              .platformCommissionRate(platformCommissionRate)
              .build() ;
    }
}
