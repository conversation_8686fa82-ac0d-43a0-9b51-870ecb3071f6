package com.sankuai.carnation.distribution.intention.domain.calculate.task.running;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.dztrade.enums.RelatedOrderTypeEnum;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderChannelTaskRunningResultBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.StaffCodeBargainBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.dto.DistributorDetailDTO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionCalculateResultEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelRunningTaskDataService;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.ReceiveRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.ReceiveRecordRequest;
import com.sankuai.medicalcosmetology.offline.code.api.service.staffbargain.StaffBargainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/6/3
 **/
@Slf4j
@Component
public class StaffBargainOrdersChannelTaskRunner extends AbsOrderChannelTaskRunner {


    @Autowired
    private StaffBargainService staffBargainService;

    @Resource
    private OrderChannelRunningTaskDataService runningTaskDataService;


    @Override
    protected OrderChannelTaskRunningResultBO calculate(IntentionCalculateTaskParamBO taskParam, OrderInfoBO orderInfoBO, DistributionOrderChannelCalRunningTaskWithBLOBs task) throws Exception {
        // 检查是否有职人一客一价信息
        if (orderInfoBO.getStaffCodeBargainBO() == null) {
            return OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        }

        //尾款单处理
        //if(orderInfoBO.getStaffCodeBargainBO().getExtraOrderType() == RelatedOrderTypeEnum.DEAL_GROUP_BALANCE_PAYMENT_ORDER.getId()) {
        //    DistributionOrderChannelCalRunningTaskWithBLOBs runningTask = runningTaskDataService.forceGetTaskByOrderAndType(orderInfoBO.getOrderType(), orderInfoBO.getStaffCodeBargainBO().getMainOrderId(), getSupportTaskType());
        //    if (runningTask == null) {
        //        Cat.logEvent("StaffBargainOrdersChannelTaskRunner", "runningTaskIsNull");
        //        return OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        //    }
        //    OrderChannelTaskRunningResultBO runningResultBO = JSON.parseObject(runningTask.getResult(), OrderChannelTaskRunningResultBO.class);
        //    StaffCodeBargainBO staffCodeBargainBO = orderInfoBO.getStaffCodeBargainBO();
        //    staffCodeBargainBO.setTechId(runningResultBO.getStaffCodeBargain().getTechId());
        //    staffCodeBargainBO.setStaffBargainCodeId(runningResultBO.getStaffCodeBargain().getStaffBargainCodeId());
        //}

        // 查询扫码记录
        RemoteResponse<List<ReceiveRecordDTO>> remoteResponse = queryReceiveRecords(orderInfoBO.getStaffCodeBargainBO().getStaffBargainCodeId());
        if (!remoteResponse.isSuccess() || CollectionUtils.isEmpty(remoteResponse.getData())) {
            return OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        }

        // 检查订单是否匹配
        List<ReceiveRecordDTO> receiveRecordDTOList = remoteResponse.getData();
        if (!isOrderMatched(receiveRecordDTOList, orderInfoBO)) {
            return OrderChannelTaskRunningResultBO.buildNotDistributionOrderResult(IntentionTypeEnum.STRONG_INTENTION.getCode());
        }

        // 构建分销订单结果
        return buildDistributionOrderResult(orderInfoBO);
    }

    @Override
    protected boolean isSyncTask(IntentionCalculateTaskParamBO taskParam) {
        return true;
    }

    @Override
    public int getSupportTaskType() {
        return OrderChannelCalculateTaskTypeEnum.STAFF_BARGAIN_MULTIPLE_ORDERS.getCode();
    }

    @Override
    public boolean isOrderHitTask(IntentionCalculateTaskParamBO taskParam, OrderInfoBO orderInfo) {
        return orderInfo.getStaffCodeBargainBO() != null;
    }

    private RemoteResponse<List<ReceiveRecordDTO>> queryReceiveRecords(long staffBargainCodeId) {
        ReceiveRecordRequest request = new ReceiveRecordRequest();
        request.setStaffBargainId(staffBargainCodeId);
        return staffBargainService.queryReceiveRecord(request);
    }

    private boolean isOrderMatched(List<ReceiveRecordDTO> receiveRecordDTOList, OrderInfoBO orderInfoBO) {

        for (ReceiveRecordDTO receiveRecordDTO : receiveRecordDTOList) {
            boolean result = receiveRecordDTO.getPlatform().equals(orderInfoBO.getPlatform()) && receiveRecordDTO.getUserId().equals(orderInfoBO.getUserId());
            if (result) {
                return true;
            }
        }
        return false;
    }

    private OrderChannelTaskRunningResultBO buildDistributionOrderResult(OrderInfoBO orderInfoBO) {
        OrderChannelTaskRunningResultBO orderChannelTaskRunningResultBO = new OrderChannelTaskRunningResultBO();
        orderChannelTaskRunningResultBO.setCalculateResult(IntentionCalculateResultEnum.DISTRIBUTION_ORDER.getCode());
        orderChannelTaskRunningResultBO.setBusinessChannel(DistributionBusinessChannelEnum.STAFF_BARGAIN_MULTIPLE_ORDERS.getCode());
        orderChannelTaskRunningResultBO.setDistributionCode(orderInfoBO.getDistributionCode());
        orderChannelTaskRunningResultBO.setIntentionType(IntentionTypeEnum.STRONG_INTENTION.getCode());
        orderChannelTaskRunningResultBO.setIntentionCreateTime(orderInfoBO.getPayTime());
        DistributorDetailDTO distributor = new DistributorDetailDTO();
        distributor.setDistributorId(orderInfoBO.getStaffCodeBargainBO().getTechId());
        distributor.setDistributionProductId(orderInfoBO.getStaffCodeBargainBO().getStaffBargainCodeId());
        orderChannelTaskRunningResultBO.setDistributor(distributor);
        orderChannelTaskRunningResultBO.setStaffCodeBargain(orderInfoBO.getStaffCodeBargainBO());
        return orderChannelTaskRunningResultBO;
    }
}
