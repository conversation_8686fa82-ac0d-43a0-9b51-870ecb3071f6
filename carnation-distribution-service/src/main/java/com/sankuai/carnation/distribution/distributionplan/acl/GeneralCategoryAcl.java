package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralCategoryTree;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCategory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
public interface GeneralCategoryAcl {

    /**
     * 根据交易类型查询类目树
     *
     * @param tradeType 交易类型
     * @return 类目树
     */
    List<GeneralCategoryTree> getCategoryTreeByTradeType(Integer tradeType);

    /**
     * 查询商品类目信息
     * @param tradeType
     * @return Map<商品一级类目, 关联商品二级类目列表>
     */
    Map<ProductCategory, List<ProductCategory>> getCategoryMap(Integer tradeType);
}