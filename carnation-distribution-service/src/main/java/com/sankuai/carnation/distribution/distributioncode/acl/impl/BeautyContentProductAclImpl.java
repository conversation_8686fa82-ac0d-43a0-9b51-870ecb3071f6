package com.sankuai.carnation.distribution.distributioncode.acl.impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.beautycontent.dzproduct.api.DzProductService;
import com.sankuai.beautycontent.dzproduct.dto.GetProductIdRequest;
import com.sankuai.beautycontent.dzproduct.dto.ProductIdResponse;
import com.sankuai.beautycontent.dzproduct.dto.ProductUrlDTO;
import com.sankuai.beautycontent.dzproduct.enums.BizCodeEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributioncode.acl.BeautyContentProductAcl;
import com.sankuai.carnation.distribution.distributioncode.acl.model.BeautyContentProductDTO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BeautyContentProductAclImpl implements BeautyContentProductAcl {

    @Resource
    private DzProductService dzBeautyContentProductService;

    /**
     * 分销渠道类型 --> 医美业务身份的映射关系
     */
    private static final Map<DistributionBusinessChannelEnum, BizCodeEnum> DISTRIBUTION_CHANNEL_TO_BEAUTY_BIZ_CODE_MAP = Maps.newHashMap();

    /**
     * 医美商品类型 --> 分销商品类型映射
     */
    private static final Map<Integer, ProductTypeEnum> BEAUTY_PRODUCT_TYPE_TO_DISTRIBUTION_PRODUCT_TYPE_MAP = Maps.newHashMap();

    static {
        DISTRIBUTION_CHANNEL_TO_BEAUTY_BIZ_CODE_MAP.put(DistributionBusinessChannelEnum.WEI_BO, BizCodeEnum.WEIBO);
        BEAUTY_PRODUCT_TYPE_TO_DISTRIBUTION_PRODUCT_TYPE_MAP.put(com.sankuai.beautycontent.dzproduct.enums.ProductTypeEnum.MEDICAL_PREPAY.code, ProductTypeEnum.PREPAY);
        BEAUTY_PRODUCT_TYPE_TO_DISTRIBUTION_PRODUCT_TYPE_MAP.put(com.sankuai.beautycontent.dzproduct.enums.ProductTypeEnum.DZ_BIAOPIN.code, ProductTypeEnum.STANDARD_PRODUCT);
    }

    @Override
    public Optional<BeautyContentProductDTO> parseChannelProductUrl(String url, DistributionBusinessChannelEnum channel) {
        try {
            if (StringUtils.isBlank(url) || !DISTRIBUTION_CHANNEL_TO_BEAUTY_BIZ_CODE_MAP.containsKey(channel)) {
                log.error("BeautyContentProductAcl.parseChannelProductUrl param error, url:{}, channel:{}", url, channel);
                return Optional.empty();
            }

            GetProductIdRequest request = new GetProductIdRequest();
            request.setBizCode(DISTRIBUTION_CHANNEL_TO_BEAUTY_BIZ_CODE_MAP.get(channel).code);
            request.setProductUrlList(Lists.newArrayList(url));
            ProductIdResponse productIdResponse = dzBeautyContentProductService.getIdByProductUrl(request);
            if (null == productIdResponse || !productIdResponse.getSuccess()
                    || MapUtils.isEmpty(productIdResponse.getUrl2IdMap()) || productIdResponse.getUrl2IdMap().get(url) == null) {
                Cat.logEvent("parseChannelProductUrlFailed", String.format("BeautyContentProductAcl.parseChannelProductUrl failed, url:%s, channel:%s", url, channel));
                log.error("BeautyContentProductAcl.parseChannelProductUrl invoke dzBeautyContentProductService.getIdByProductUrl failed, request:{}", request);
                throw new RuntimeException("医美商品链接解析失败");
            }

            ProductUrlDTO productUrl = productIdResponse.getUrl2IdMap().get(url);
            return Optional.of(buildBeautyContentProductDto(productUrl));
        } catch (Exception e) {
            log.error("BeautyContentProductAcl.parseChannelProductUrl failed, url:{}, channel:{}", url, channel, e);
            return Optional.empty();
        }
    }

    private BeautyContentProductDTO buildBeautyContentProductDto(ProductUrlDTO productUrl) {
        BeautyContentProductDTO beautyContentProduct = new BeautyContentProductDTO();
        beautyContentProduct.setProductId(Long.valueOf(productUrl.getProductId()));
        beautyContentProduct.setProductType(BEAUTY_PRODUCT_TYPE_TO_DISTRIBUTION_PRODUCT_TYPE_MAP.get(productUrl.getProductType()));
        return beautyContentProduct;
    }
}