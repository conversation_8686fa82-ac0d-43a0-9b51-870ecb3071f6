package com.sankuai.carnation.distribution.distributionplan.domain.model;

import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanOperationEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributionPlanStatusUpdateRequest {

    /**
     * 计划id
     */
    private Long planId;

    /**
     * 分销计划渠道
     */
    private DistributionBusinessChannelEnum channel;

    /**
     * 启用状态，1-启用，2-暂停，3-取消
     * @see DistributionPlanOperationEnum
     */
    private DistributionPlanOperationEnum operation;

    /**
     * 操作时间，计划开始或者结束时间
     */
    private Long operationTime;
}
