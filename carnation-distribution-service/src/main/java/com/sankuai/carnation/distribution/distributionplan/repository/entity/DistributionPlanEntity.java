package com.sankuai.carnation.distribution.distributionplan.repository.entity;

import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanIndustryTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DistributionPlanEntity {

    /**
     * 方案id
     */
    private Long planId;

    /**
     * 行业类型
     * @see DistributionPlanIndustryTypeEnum
     */
    private Integer industryType;

    /**
     * 计划类型
     * @see DistributionPlanTypeEnum
     */
    private Integer planType;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 渠道
     * @see DistributionBusinessChannelEnum
     */
    private String channel;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 计划开始时间
     */
    private Date preBeginTime;

    /**
     * 计划结束时间
     */
    private Date preEndTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 提成策略（json）
     */
    private String commissionStrategy;

    private Integer isDeleted;

    private Date addTime;

    private Date updateTime;
}
