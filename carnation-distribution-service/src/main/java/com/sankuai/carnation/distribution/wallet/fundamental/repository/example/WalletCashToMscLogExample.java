package com.sankuai.carnation.distribution.wallet.fundamental.repository.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WalletCashToMscLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public WalletCashToMscLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyIsNull() {
            addCriterion("unique_key is null");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyIsNotNull() {
            addCriterion("unique_key is not null");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyEqualTo(String value) {
            addCriterion("unique_key =", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyNotEqualTo(String value) {
            addCriterion("unique_key <>", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyGreaterThan(String value) {
            addCriterion("unique_key >", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyGreaterThanOrEqualTo(String value) {
            addCriterion("unique_key >=", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyLessThan(String value) {
            addCriterion("unique_key <", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyLessThanOrEqualTo(String value) {
            addCriterion("unique_key <=", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyLike(String value) {
            addCriterion("unique_key like", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyNotLike(String value) {
            addCriterion("unique_key not like", value, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyIn(List<String> values) {
            addCriterion("unique_key in", values, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyNotIn(List<String> values) {
            addCriterion("unique_key not in", values, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyBetween(String value1, String value2) {
            addCriterion("unique_key between", value1, value2, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andUniqueKeyNotBetween(String value1, String value2) {
            addCriterion("unique_key not between", value1, value2, "uniqueKey");
            return (Criteria) this;
        }

        public Criteria andBizCodeIsNull() {
            addCriterion("biz_code is null");
            return (Criteria) this;
        }

        public Criteria andBizCodeIsNotNull() {
            addCriterion("biz_code is not null");
            return (Criteria) this;
        }

        public Criteria andBizCodeEqualTo(String value) {
            addCriterion("biz_code =", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeNotEqualTo(String value) {
            addCriterion("biz_code <>", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeGreaterThan(String value) {
            addCriterion("biz_code >", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeGreaterThanOrEqualTo(String value) {
            addCriterion("biz_code >=", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeLessThan(String value) {
            addCriterion("biz_code <", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeLessThanOrEqualTo(String value) {
            addCriterion("biz_code <=", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeLike(String value) {
            addCriterion("biz_code like", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeNotLike(String value) {
            addCriterion("biz_code not like", value, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeIn(List<String> values) {
            addCriterion("biz_code in", values, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeNotIn(List<String> values) {
            addCriterion("biz_code not in", values, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeBetween(String value1, String value2) {
            addCriterion("biz_code between", value1, value2, "bizCode");
            return (Criteria) this;
        }

        public Criteria andBizCodeNotBetween(String value1, String value2) {
            addCriterion("biz_code not between", value1, value2, "bizCode");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdIsNull() {
            addCriterion("wall_flow_id is null");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdIsNotNull() {
            addCriterion("wall_flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdEqualTo(Long value) {
            addCriterion("wall_flow_id =", value, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdNotEqualTo(Long value) {
            addCriterion("wall_flow_id <>", value, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdGreaterThan(Long value) {
            addCriterion("wall_flow_id >", value, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdGreaterThanOrEqualTo(Long value) {
            addCriterion("wall_flow_id >=", value, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdLessThan(Long value) {
            addCriterion("wall_flow_id <", value, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdLessThanOrEqualTo(Long value) {
            addCriterion("wall_flow_id <=", value, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdIn(List<Long> values) {
            addCriterion("wall_flow_id in", values, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdNotIn(List<Long> values) {
            addCriterion("wall_flow_id not in", values, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdBetween(Long value1, Long value2) {
            addCriterion("wall_flow_id between", value1, value2, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andWallFlowIdNotBetween(Long value1, Long value2) {
            addCriterion("wall_flow_id not between", value1, value2, "wallFlowId");
            return (Criteria) this;
        }

        public Criteria andMscStatusIsNull() {
            addCriterion("msc_status is null");
            return (Criteria) this;
        }

        public Criteria andMscStatusIsNotNull() {
            addCriterion("msc_status is not null");
            return (Criteria) this;
        }

        public Criteria andMscStatusEqualTo(Integer value) {
            addCriterion("msc_status =", value, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusNotEqualTo(Integer value) {
            addCriterion("msc_status <>", value, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusGreaterThan(Integer value) {
            addCriterion("msc_status >", value, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("msc_status >=", value, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusLessThan(Integer value) {
            addCriterion("msc_status <", value, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusLessThanOrEqualTo(Integer value) {
            addCriterion("msc_status <=", value, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusIn(List<Integer> values) {
            addCriterion("msc_status in", values, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusNotIn(List<Integer> values) {
            addCriterion("msc_status not in", values, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusBetween(Integer value1, Integer value2) {
            addCriterion("msc_status between", value1, value2, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("msc_status not between", value1, value2, "mscStatus");
            return (Criteria) this;
        }

        public Criteria andMscCodeIsNull() {
            addCriterion("msc_code is null");
            return (Criteria) this;
        }

        public Criteria andMscCodeIsNotNull() {
            addCriterion("msc_code is not null");
            return (Criteria) this;
        }

        public Criteria andMscCodeEqualTo(Integer value) {
            addCriterion("msc_code =", value, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeNotEqualTo(Integer value) {
            addCriterion("msc_code <>", value, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeGreaterThan(Integer value) {
            addCriterion("msc_code >", value, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("msc_code >=", value, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeLessThan(Integer value) {
            addCriterion("msc_code <", value, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeLessThanOrEqualTo(Integer value) {
            addCriterion("msc_code <=", value, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeIn(List<Integer> values) {
            addCriterion("msc_code in", values, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeNotIn(List<Integer> values) {
            addCriterion("msc_code not in", values, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeBetween(Integer value1, Integer value2) {
            addCriterion("msc_code between", value1, value2, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("msc_code not between", value1, value2, "mscCode");
            return (Criteria) this;
        }

        public Criteria andMscMsgIsNull() {
            addCriterion("msc_msg is null");
            return (Criteria) this;
        }

        public Criteria andMscMsgIsNotNull() {
            addCriterion("msc_msg is not null");
            return (Criteria) this;
        }

        public Criteria andMscMsgEqualTo(String value) {
            addCriterion("msc_msg =", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgNotEqualTo(String value) {
            addCriterion("msc_msg <>", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgGreaterThan(String value) {
            addCriterion("msc_msg >", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgGreaterThanOrEqualTo(String value) {
            addCriterion("msc_msg >=", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgLessThan(String value) {
            addCriterion("msc_msg <", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgLessThanOrEqualTo(String value) {
            addCriterion("msc_msg <=", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgLike(String value) {
            addCriterion("msc_msg like", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgNotLike(String value) {
            addCriterion("msc_msg not like", value, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgIn(List<String> values) {
            addCriterion("msc_msg in", values, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgNotIn(List<String> values) {
            addCriterion("msc_msg not in", values, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgBetween(String value1, String value2) {
            addCriterion("msc_msg between", value1, value2, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andMscMsgNotBetween(String value1, String value2) {
            addCriterion("msc_msg not between", value1, value2, "mscMsg");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}