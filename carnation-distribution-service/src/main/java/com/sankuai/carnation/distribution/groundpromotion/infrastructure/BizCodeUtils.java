package com.sankuai.carnation.distribution.groundpromotion.infrastructure;

import com.dianping.dztrade.enums.BizCodeEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.utils.BizCodeAnalyser;

/**
 * @Author: chenhaoyang02  
 * @Date: 2023/11/24
 * @Description: com.dianping.dztrade.enums.BizCodeEnum 用于判断bizCode是否是团购、预付、预订、次卡和标品
 */ 
public class BizCodeUtils {

    public static boolean isGroupon(String bizCode) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.BizCodeUtils.isGroupon(java.lang.String)");
        ProductTypeEnum productTypeEnum = BizCodeAnalyser.fromOrderBizCode(bizCode);
        return productTypeEnum.equals(ProductTypeEnum.TUAN_DEAL);
    }

    public static boolean isPrepay(String bizCode) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.BizCodeUtils.isPrepay(java.lang.String)");
        ProductTypeEnum productTypeEnum = BizCodeAnalyser.fromOrderBizCode(bizCode);
        return productTypeEnum.equals(ProductTypeEnum.PREPAY);
    }

    public static boolean isBook(String bizCode) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.BizCodeUtils.isBook(java.lang.String)");
        ProductTypeEnum productTypeEnum = BizCodeAnalyser.fromOrderBizCode(bizCode);
        return productTypeEnum.equals(ProductTypeEnum.BOOK);
    }

    public static boolean isTimeCard(String bizCode){
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.BizCodeUtils.isTimeCard(java.lang.String)");
        ProductTypeEnum productTypeEnum = BizCodeAnalyser.fromOrderBizCode(bizCode);
        return productTypeEnum.equals(ProductTypeEnum.TIME_CARD);
    }

    public static boolean isStandard(String bizCode){
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.BizCodeUtils.isStandard(java.lang.String)");
        ProductTypeEnum productTypeEnum = BizCodeAnalyser.fromOrderBizCode(bizCode);
        return productTypeEnum.equals(ProductTypeEnum.STANDARD_PRODUCT);
    }

    // 标品在核销时会产生第二笔买单订单，标品的核销、已消费退都通过这笔订单发送mq
    public static boolean isSecondStandard(String bizCode) {
        return BizCodeEnum.unified_paybill.getBizCode().equals(bizCode);
    }
}
