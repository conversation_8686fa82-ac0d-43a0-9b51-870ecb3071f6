package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.common.enums.ProductItemStatusEnum;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.acl.TpfunProductAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TpfunProductAclImpl implements TpfunProductAcl {

    @Resource
    private ProductService productService;

    @Override
    public boolean checkProductValid(Long productId) {
        try {
            if (null == productId || productId <= 0L) {
                return false;
            }

            Product product = productService.getBaseProductById(productId.intValue());
            if (product == null) {
                throw new RuntimeException("productNotFound");
            }
            if (product.getStatus() != ProductItemStatusEnum.ONLINE_STATUS.getCode()) {
                throw new RuntimeException("productOffline");
            }

            return true;
        } catch (RuntimeException re) {
            log.info("TpfunProductAcl.checkProductValid return false, productId:{}", productId, re);
            Cat.logEvent("InvalidTpfunProduct", re.getMessage() + "InvalidTpfunProduct:" + productId);
            return false;
        } catch (Exception e) {
            Cat.logEvent("CheckProductValidFailed", "TpfunProductId:" + productId);
            log.error("TpfunProductAcl.checkProductValid failed, productId:{}", productId, e);
            return false;
        }
    }

    @Override
    public Boolean checkProductApplyToDpShopId(Long productId, Long dpShopId) {
        try {
            if (null == productId || productId <= 0L || dpShopId == null || dpShopId <= 0L) {
                return false;
            }

            Map<Integer, List<Long>> productToDpShopIdsMap = productService.mGetLongShopIds(Lists.newArrayList(productId.intValue()));
            if (MapUtils.isEmpty(productToDpShopIdsMap) || !productToDpShopIdsMap.containsKey(productId.intValue())) {
                throw new RuntimeException("未查询到商品的适用门店列表");
            }
            if (!productToDpShopIdsMap.get(productId.intValue()).contains(dpShopId)) {
                throw new RuntimeException("当前商品不适用于该点评门店");
            }
            return true;
        } catch (RuntimeException re) {
            log.info("TpfunProductAcl.checkProductApplyToDpShopId return false, productId:{}, dpShopId:{}", productId, dpShopId, re);
            Cat.logEvent("UnMatchProduct2DpShopId",  String.format("checkProductApplyToDpShopId productId:%s, dpShopId:%s", productId, dpShopId));
            return false;
        } catch (Exception e) {
            Cat.logEvent("CheckProductApplyToDpShopIdFailed", String.format("checkProductApplyToDpShopId productId:%s, dpShopId:%s", productId, dpShopId));
            log.error("TpfunProductAcl.checkProductApplyToDpShopId failed, productId:{}, dpShopId:{}", productId, dpShopId, e);
            return false;
        }
    }
}
