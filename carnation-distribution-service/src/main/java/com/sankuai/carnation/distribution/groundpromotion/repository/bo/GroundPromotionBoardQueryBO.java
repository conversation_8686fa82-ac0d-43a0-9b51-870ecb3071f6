package com.sankuai.carnation.distribution.groundpromotion.repository.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/8/11
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GroundPromotionBoardQueryBO {

    private Date startTime;

    private Date endTime;

    private List<Long> shopIdList;

    private List<String> misIdList;

    private List<Long> groundPromotionIdList;

    private List<Integer> cityIdList;

    private int pageNo;

    private int pageSize;

    private int sortParamName;

    private int sortType;

    private List<String> dims;

    private boolean isDetail;
}
