package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.CreateResultRowStrBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25
 * @description
 */
@Slf4j
public class CreateResultRowListener implements ReadListener<CreateResultRowStrBO> {

    @Getter
    private List<CreateResultRowStrBO> cachedDataList = Lists.newArrayList();

    @Override
    public void invoke(CreateResultRowStrBO createResultRowStrBO, AnalysisContext analysisContext) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreateResultRowListener.invoke(com.sankuai.carnation.distribution.groundpromotion.repository.bo.CreateResultRowStrBO,com.alibaba.excel.context.AnalysisContext)");
        if (ExcelParseResultEnum.SUCCESS.getDesc().equals(createResultRowStrBO.getLoadResult())) {
            if (!checkFormat(createResultRowStrBO)) {
                createResultRowStrBO.setParseResult(ExcelParseResultEnum.UNKNOWN);
            } else {
                createResultRowStrBO.setParseResult(ExcelParseResultEnum.SUCCESS);
            }
        } else if (ExcelParseResultEnum.FAIL.getDesc().equals(createResultRowStrBO.getLoadResult())) {
            createResultRowStrBO.setParseResult(ExcelParseResultEnum.FAIL);
        } else {
            createResultRowStrBO.setParseResult(ExcelParseResultEnum.UNKNOWN);
        }

        cachedDataList.add(createResultRowStrBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreateResultRowListener.doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext)");
    }

    private boolean checkFormat(CreateResultRowStrBO createResultRowStrBO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreateResultRowListener.checkFormat(com.sankuai.carnation.distribution.groundpromotion.repository.bo.CreateResultRowStrBO)");
        try {
            Integer.parseInt(createResultRowStrBO.getDpCityId());
            Long.parseLong(createResultRowStrBO.getDpShopId());
            Long.parseLong(createResultRowStrBO.getDpDealId());
        }catch (Exception e) {
            log.error("CreateResultRowStrBO param is invalid, param is {}, exception is", JSONObject.toJSON(createResultRowStrBO), e);
            return false;
        }
        return true;
    }
}
