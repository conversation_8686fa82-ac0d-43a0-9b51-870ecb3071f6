package com.sankuai.carnation.distribution.commisson.settle.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.event.api.commission.dto.OrderCommissionRateDTO;
import com.dianping.gmkt.event.api.commission.service.OrderCommissionRateQueryService;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateAmountResponse;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryDTO;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryPageResponse;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryRequest;
import com.sankuai.carnation.distribution.commission.settle.service.RebateQueryToolService;
import com.sankuai.carnation.distribution.commisson.repository.QueryRebateForOrderOrShopIdService;
import com.sankuai.technician.trade.api.settle.dto.SettleBillModelQuery;
import com.sankuai.technician.trade.api.settle.dto.SettleBillQueryModel;
import com.sankuai.technician.trade.api.settle.service.SettleBillQueryRemoteService;
import com.sankuai.technician.trade.api.settle.tradereturn.enums.ReturnBizLineEnum;
import com.sankuai.technician.trade.types.enums.BizLineEnum;
import com.sankuai.technician.trade.types.enums.SettleAccountRoleType;
import com.sankuai.technician.trade.types.enums.SettleBillStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RebateQueryToolServiceImpl implements RebateQueryToolService {
    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    @Autowired
    private QueryRebateForOrderOrShopIdService queryRebateForOrderOrShopIdService;

    @Autowired
    private SettleBillQueryRemoteService settleBillQueryRemoteService;

    @Autowired
    private OrderCommissionRateQueryService orderCommissionRateQueryService;

    @Override
    public RemoteResponse<RebateQueryPageResponse> queryRebateRecord(RebateQueryRequest request) {
        log.info("RebateQueryToolServiceImpl.queryRebateRecord, request: {}", JSON.toJSONString(request));
        RebateQueryPageResponse response = new RebateQueryPageResponse();
        try {
            Validate.isTrue(request != null, "请求参数为空");
            Validate.isTrue(request.getDpShopId() != null || request.getOrderId() != null, "请确认查询条件");
            if (request.getPageNum() <= 0) {
                request.setPageNum(DEFAULT_PAGE_NUM);
            }
            if (request.getPageSize() <= 0) {
                request.setPageSize(DEFAULT_PAGE_SIZE);
            }

            response.setPageNum(request.getPageNum());
            response.setPageSize(request.getPageSize());
            List<RebateQueryDTO> rebateQueryDTOS = new ArrayList<>();
            if (request.getOrderId() != null) {
                // 走订单查询
                rebateQueryDTOS = queryRebateForOrderOrShopIdService.queryRebateForOrder(request);
            } else {
                // 走门店查询
                Validate.isTrue(request.getStartTime() != null && request.getEndTime() != null, "请确认查询时间范围");
                Validate.isTrue(request.getStartTime() <= request.getEndTime(), "开始时间不能大于结束时间");
                rebateQueryDTOS = queryRebateForOrderOrShopIdService.queryRebateForShopId(request);
            }
            if (CollectionUtils.isEmpty(rebateQueryDTOS)) {
                response.setTotalNum(0L);
                return RemoteResponse.success(response);
            }
            // 分页处理
            int totalNum = rebateQueryDTOS.size();
            response.setTotalNum(rebateQueryDTOS.size());
            int offset = request.getOffset();
            int endIndex = Math.min(offset + response.getPageSize(), totalNum);
            if (offset < endIndex) {
                response.setResults(rebateQueryDTOS.subList(offset, endIndex));
            }
        } catch (IllegalArgumentException e) {
            log.error("RebateQueryToolServiceImpl.queryRebateRecord 参数错误, param: {}", JSON.toJSONString(request), e);
            return RemoteResponse.fail(e.getMessage());
        } catch (Exception e) {
            log.error("RebateQueryToolServiceImpl.queryRebateRecord error, request: {}", JSON.toJSONString(request), e);
            return RemoteResponse.fail("系统异常");
        }
        return RemoteResponse.success(response);
    }

    @Override
    public RemoteResponse<RebateAmountResponse> queryRebateAmount(String orderId) {
        log.info("RebateQueryToolServiceImpl.queryRebateAmount, orderId: {}", orderId);
        RebateAmountResponse response = new RebateAmountResponse();
        try {
            Validate.isTrue(orderId != null, "订单id为空");

            response.setOrderId(orderId);
            response.setProductType(queryRebateForOrderOrShopIdService.getProductTypeByOrderId(orderId));

            SettleBillModelQuery modelQuery = new SettleBillModelQuery();
            modelQuery.setBizLine(ReturnBizLineEnum.TECH_PROMO_CODE.getId());
            modelQuery.setBizPackageId(orderId);
            List<SettleBillQueryModel> settleBillQueryModels = settleBillQueryRemoteService.querySettleBill(modelQuery);
            if (CollectionUtils.isEmpty(settleBillQueryModels)) {
                return RemoteResponse.success(response);
            }
            List<SettleBillQueryModel> filteredQuery = settleBillQueryModels.stream()
                    .filter(settleBillQueryModel -> settleBillQueryModel.getRoleType() == SettleAccountRoleType.TECHNICIAN.getType())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredQuery)) {
                BigDecimal settleAmount = filteredQuery.stream().map(SettleBillQueryModel::getSettleAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.DOWN);
                response.setCommissionAmount(settleAmount);
                if (settleAmount.compareTo(BigDecimal.ZERO) == 0) {
                    response.setNotRebateReason(filteredQuery.get(0).getInvalidReason());
                }
            }
            OrderCommissionRateDTO orderCommissionRateDTO = orderCommissionRateQueryService
                    .queryOrderCommissionRate(orderId);
            if (orderCommissionRateDTO != null) {
                DecimalFormat df = new DecimalFormat("0.00%");
                response.setCommissionRate(df.format(orderCommissionRateDTO.getCommissionRate()));
            }
        }catch (IllegalArgumentException e){
            log.error("RebateQueryToolServiceImpl.queryRebateAmount error, orderId: {}", orderId, e);
            return RemoteResponse.fail("参数不合法");
        }catch (Exception e) {
            log.error("RebateQueryToolServiceImpl.queryRebateAmount error, orderId: {}", orderId, e);
            return RemoteResponse.fail("系统异常");
        }
        return RemoteResponse.success(response);
    }
}
