package com.sankuai.carnation.distribution.distributionplan.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.domain.model.*;
import com.sankuai.carnation.distribution.distributionplan.enums.*;
import com.sankuai.carnation.distribution.distributionplan.repository.DistributionPlanRepository;
import com.sankuai.carnation.distribution.distributionplan.repository.dao.DistributionPlanMapper;
import com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanEntity;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class DistributionPlanRepositoryImpl implements DistributionPlanRepository {

    @Resource
    private DistributionPlanMapper distributionPlanMapper;

    @Override
    public Long addDistributionPlan(DistributionPlanDO distributionPlanDo) {
        DistributionPlanEntity entity = convertDoToEntity(distributionPlanDo);
        distributionPlanMapper.insertDistributionPlan(entity);
        if (entity.getPlanId() == null || entity.getPlanId() <= 0L) {
            throw new RuntimeException("新增分销计划失败:addDistributionPlanFailed");
        }

        distributionPlanDo.setPlanId(entity.getPlanId());
        return distributionPlanDo.getPlanId();
    }

    @Override
    public Boolean updateDistributionPlan(DistributionPlanDO distributionPlanDo) {
        DistributionPlanEntity entity = convertDoToEntity(distributionPlanDo);
        int updateResult = distributionPlanMapper.updateDistributionPlan(entity);
        if (updateResult <= 0) {
            throw new RuntimeException("更新分销计划失败:updateDistributionPlanFailed");
        }

        return true;
    }

    @Override
    public Boolean updateDistributionPlanStatus(DistributionPlanDO distributionPlanDo) {
        DistributionPlanEntity entity = convertDoToEntity(distributionPlanDo);
        int updateResult = distributionPlanMapper.updateDistributionPlanStatus(entity);
        if (updateResult <= 0) {
            throw new RuntimeException("更新分销计划状态失败:updateDistributionPlanStatusFailed");
        }

        return true;
    }

    @Override
    public DistributionPlanDO loadDistributionPlanById(Long planId) {
        DistributionPlanEntity entity = distributionPlanMapper.loadDistributionPlanByPlanId(planId);
        if (null == entity) {
            return null;
        }
        return convertEntityToDo(entity);
    }

    @Override
    public List<DistributionPlanDO> queryDistributionPlanByIds(List<Long> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return Lists.newArrayList();
        }
        List<DistributionPlanEntity> entities = distributionPlanMapper.queryDistributionPlanByPlanIds(planIds);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return entities.stream().map(this::convertEntityToDo).collect(Collectors.toList());
    }

    @Override
    public List<DistributionPlanDO> queryDistributionPlan(DistributionPlanQueryRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getProductIds()) || StringUtils.isBlank(request.getChannel())
                || null == request.getProductType()) {
            return Lists.newArrayList();
        }
        List<DistributionPlanEntity> entities = distributionPlanMapper.queryDistributionPlanByRequest(request);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return entities.stream().map(this::convertEntityToDo).collect(Collectors.toList());
    }

    @Override
    public PageResult<DistributionPlanDO> pageQueryDistributionPlan(DistributionPlanPageQuery pageQuery) {
        PageResult<DistributionPlanDO> pageResult = PageResult.<DistributionPlanDO>builder()
                .pageNo(pageQuery.getPageNo())
                .pageSize(pageQuery.getPageSize())
                .records(Lists.newArrayList())
                .totalCount(0L)
                .build();

        long totalCount = distributionPlanMapper.countDistributionPlan(pageQuery);
        if (totalCount == 0) {
            return pageResult;
        }

        int limit = pageQuery.getPageSize();
        int offset = pageQuery.getPageSize() * (pageQuery.getPageNo() - 1);
        if (totalCount <= offset) {
            pageResult.setTotalCount(totalCount);
            return pageResult;
        }
        List<DistributionPlanEntity> planEntityPage = distributionPlanMapper.pageQueryDistributionPlan(pageQuery, limit, offset);
        if (CollectionUtils.isEmpty(planEntityPage)) {
            pageResult.setTotalCount(totalCount);
            return pageResult;
        }

        pageResult.setTotalCount(totalCount);
        pageResult.setRecords(planEntityPage.stream().map(this::convertEntityToDo).collect(Collectors.toList()));
        return pageResult;
    }

    private DistributionPlanDO convertEntityToDo(DistributionPlanEntity entity) {
        DistributionPlanDO distributionPlanDo = new DistributionPlanDO();
        distributionPlanDo.setPlanId(entity.getPlanId());
        distributionPlanDo.setIndustryType(DistributionPlanIndustryTypeEnum.from(entity.getIndustryType()));
        distributionPlanDo.setPlanType(DistributionPlanTypeEnum.from(entity.getPlanType()));
        distributionPlanDo.setPlanName(entity.getPlanName());
        distributionPlanDo.setChannel(DistributionBusinessChannelEnum.fromCode(entity.getChannel()));
        distributionPlanDo.setStatus(DistributionPlanStatusEnum.from(entity.getStatus()));
        distributionPlanDo.setPreBeginTime(entity.getPreBeginTime());
        distributionPlanDo.setPreEndTime(entity.getPreEndTime());
        distributionPlanDo.setEndTime(entity.getEndTime());
        distributionPlanDo.setProductId(entity.getProductId());
        distributionPlanDo.setProductType(ProductTypeEnum.fromCode(entity.getProductType()));
        CommissionStrategyVO commissionStrategyVo = JSONObject.parseObject(entity.getCommissionStrategy(), new TypeReference<CommissionStrategyVO>(){});
        distributionPlanDo.setCommissionStrategyVo(commissionStrategyVo);
        return distributionPlanDo;
    }

    private DistributionPlanEntity convertDoToEntity(DistributionPlanDO distributionPlanDo) {
        DistributionPlanEntity entity = new DistributionPlanEntity();
        entity.setPlanId(distributionPlanDo.getPlanId());
        entity.setIndustryType(distributionPlanDo.getIndustryType().getType());
        entity.setPlanType(distributionPlanDo.getPlanType().getType());
        entity.setPlanName(distributionPlanDo.getPlanName());
        entity.setChannel(distributionPlanDo.getChannel().getCode());
        entity.setStatus(distributionPlanDo.getStatus().getCode());
        entity.setPreBeginTime(distributionPlanDo.getPreBeginTime());
        entity.setPreEndTime(distributionPlanDo.getPreEndTime());
        entity.setEndTime(distributionPlanDo.getEndTime());
        entity.setProductId(distributionPlanDo.getProductId());
        entity.setProductType(distributionPlanDo.getProductType().getCode());
        entity.setCommissionStrategy(JSONObject.toJSONString(distributionPlanDo.getCommissionStrategyVo()));
        return entity;
    }
}
