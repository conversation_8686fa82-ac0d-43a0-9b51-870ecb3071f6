package com.sankuai.carnation.distribution.distributionplan.repository.dao;

import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPushQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionProductPushEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionProductPushMapper {

    /**
     * 新增分销商品推送
     * @param entity
     * @return
     */
    int insertDistributionProductPush(DistributionProductPushEntity entity);


    /**
     * 新增分销商品推送
     * @param entities
     * @return
     */
    int batchInsertDistributionProductPush(@Param("entities") List<DistributionProductPushEntity> entities);

    /**
     * 更新分销商品push
     * @param entity
     * @return
     */
    int updateDistributionProductPush(DistributionProductPushEntity entity);

    /**
     * 批量更新
     * @param entities
     * @return
     */
    int batchUpdateDistributionProductPushStatus(@Param("entities") List<DistributionProductPushEntity> entities);

    /**
     * 查询分销商品推送
     * @param channel
     * @param productId
     * @param productType
     * @return
     */
    DistributionProductPushEntity loadByDistributionProductPush(@Param("channel") String channel,
                                                                @Param("productId") Long productId,
                                                                @Param("productType") Integer productType);

    /**
     * 查询分销商品推送
     * @param request
     * @return
     */
    List<DistributionProductPushEntity> queryDistributionProductPush(DistributionProductPushQueryRequest request);
}
