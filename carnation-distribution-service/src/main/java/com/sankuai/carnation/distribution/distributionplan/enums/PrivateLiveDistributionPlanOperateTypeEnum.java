package com.sankuai.carnation.distribution.distributionplan.enums;

public enum PrivateLiveDistributionPlanOperateTypeEnum {
    ENABLE(1, "启用"),
    DISABLE(2, "停用"),
    ;

    private int code;
    private String desc;

    PrivateLiveDistributionPlanOperateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PrivateLiveDistributionPlanOperateTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrivateLiveDistributionPlanOperateTypeEnum typeEnum : PrivateLiveDistributionPlanOperateTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }
}
