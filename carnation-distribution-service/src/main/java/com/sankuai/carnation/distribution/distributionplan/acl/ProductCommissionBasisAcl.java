package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisQueryRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
public interface ProductCommissionBasisAcl {

    /**
     * 查询商品的计费基数信息
     * @param productCommissionList
     * @return
     */
    List<ProductCommissionBasisInfo> batchQueryProductCommissionBasis(List<ProductCommissionBasisQueryRequest> productCommissionList);
}