package com.sankuai.carnation.distribution.distributioncode.domain.impl;

import com.sankuai.carnation.distribution.common.enums.LeafKeyEnum;
import com.sankuai.carnation.distribution.distributioncode.domain.DistributionCodeDoService;
import com.sankuai.carnation.distribution.distributioncode.domain.model.DistributionCodeParamMappingDO;
import com.sankuai.carnation.distribution.distributioncode.repository.DistributionCodeParamMappingRepository;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.utils.IdGenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DistributionCodeDoServiceImpl implements DistributionCodeDoService {

    @Resource
    private DistributionCodeParamMappingRepository distributionCodeParamMappingRepository;

    @Resource
    private IdGenUtil idGenUtil;

    @Override
    public void generateDistributionCode(DistributionCodeParamMappingDO distributionCodeParamMappingDo) {
        distributionCodeParamMappingDo.validateParam();
        Optional<DistributionCodeParamMappingDO> distributionCodeParamMappingDoOptional = distributionCodeParamMappingRepository.loadDistributionCodeByBizParams(distributionCodeParamMappingDo);
        if (distributionCodeParamMappingDoOptional.isPresent()) {
            distributionCodeParamMappingDo.setDistributionCode(distributionCodeParamMappingDoOptional.get().getDistributionCode());
            distributionCodeParamMappingDo.setMappingId(distributionCodeParamMappingDoOptional.get().getMappingId());
            return ;
        }
        long uniqueId = idGenUtil.nextSnowflakeId(LeafKeyEnum.WEIBO_DISTRIBUTE.key);
        distributionCodeParamMappingDo.generateCode(uniqueId);
        distributionCodeParamMappingRepository.addDistributionCodeParamMapping(distributionCodeParamMappingDo);
    }

    @Override
    public DistributionCodeParamMappingDO parseDistributionCode(String distributionCode, DistributionBusinessChannelEnum channel) {
        return distributionCodeParamMappingRepository.loadByChannelAndDistributionCode(channel, distributionCode);
    }

    @Override
    public List<DistributionCodeParamMappingDO> batchParseDistributionCode(List<String> distributionCodes, DistributionBusinessChannelEnum channel) {
        return distributionCodeParamMappingRepository.queryByChannelAndDistributionCodes(channel, distributionCodes);
    }
}