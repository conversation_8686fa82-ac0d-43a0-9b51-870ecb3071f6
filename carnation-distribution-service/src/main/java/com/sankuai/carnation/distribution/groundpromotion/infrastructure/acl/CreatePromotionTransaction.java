package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.constants.GroundPromotionConstants;
import com.sankuai.carnation.distribution.groundpromotion.dto.GroundAdvancedCoupon;
import com.sankuai.carnation.distribution.groundpromotion.dto.GroundFastCreateDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.GroundMaterialDTO;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionActEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionDealStatusEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionScopeTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.PointStatusEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq.GroundEventTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq.GroundPromotionProducer;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPointRowBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundShopInfoBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.*;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/25
 * @description
 */
@Slf4j
@Component
public class CreatePromotionTransaction {

    @Autowired
    private GroundPromotionRepository groundPromotionRepository;

    @Autowired
    private MaterialRepository materialRepository;

    @Autowired
    private GroundPromotionAdvancedInfoRepository groundPromotionAdvancedInfoRepository;

    @Autowired
    private PromotionalDealRepository promotionalDealRepository;

    @Autowired
    private GroundPromotionPointRepository groundPromotionPointRepository;

    @Autowired
    private GroundPromotionProducer groundPromotionProducer;

    /**
     * 根据单个地推活动信息创建地推活动
     * @param dpCityId 点评城市id
     * @param groundFastCreateDTO 快速创建地推活动的的基本信息
     * @param table 地推店铺信息表，可以看作双层map，第一层key为cityId，第二层key为店铺id，value为 < Set<DealId>, Set<MisId> >
     * @return Pair<点评城市id, 地推活动id>
     */
    @Transactional
    public Pair<Integer, Long> create(Integer dpCityId, GroundFastCreateDTO groundFastCreateDTO,
                                      GroundShopTable table, Map<Integer, List<GroundPromotionPointRowBO>> pointMap, Map<Long, Set<Long>> shopDealMap) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.create(java.lang.Integer,com.sankuai.carnation.distribution.groundpromotion.dto.GroundFastCreateDTO,com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundShopTable,java.util.Map,java.util.Map)");
        // Insert ground_promotion
        GroundPromotion groundPromotion = generateGroundPromotion(dpCityId, groundFastCreateDTO);
        groundPromotionRepository.insert(groundPromotion);
        long groundPromotionId = groundPromotion.getId();

        // Insert ground_material
        List<GroundMaterial> groundMaterialList = generateMaterial(groundPromotionId, groundFastCreateDTO.getMaterials());
        materialRepository.insertMaterial(groundMaterialList);

        // Insert advanced_info
        GroundAdvancedCoupon groundAdvancedCoupon = groundFastCreateDTO.getCoupon();
        if (Objects.nonNull(groundAdvancedCoupon)) {
            GroundPromotionAdvancedInfo groundPromotionAdvancedInfo =
                    generateAdvancedInfo(groundPromotionId, groundAdvancedCoupon);
            groundPromotionAdvancedInfoRepository.insert(groundPromotionAdvancedInfo);
        }

        // Batch insert PromotionalDeal
        List<PromotionalDeal> promotionalDealList = new ArrayList<>();

        Map<Long, GroundShopInfoBO> groundShopInfoBOMap = table.getShopInfoByCity(dpCityId);
        if (!groundShopInfoBOMap.isEmpty()) {
            for (Map.Entry<Long, GroundShopInfoBO> entry : groundShopInfoBOMap.entrySet()) {
                Long dpShopId = entry.getKey();
                GroundShopInfoBO info = entry.getValue();
                if (Objects.nonNull(info) && CollectionUtils.isNotEmpty(info.getDealIdSet())) {
                    for (Long dealId : info.getDealIdSet()) {
                        promotionalDealList.add(generateDeal(groundPromotionId, dpShopId, 0L, dealId, info.getSales()));
                    }
                }
            }
        }

        List<GroundPromotionPointRowBO> pointRowBOList = pointMap.get(dpCityId);
        if (CollectionUtils.isNotEmpty(pointRowBOList)) {
            for (GroundPromotionPointRowBO pointRowBO : pointRowBOList) {
                // 插入点位
                GroundPromotionPoint point = generatePoint(pointRowBO);
                groundPromotionPointRepository.insert(point);
                long pointId = point.getId();

                // 获取点位的shop列表
                List<Long> dpShopIdList = pointRowBO.getDpShopIdList();
                if (CollectionUtils.isNotEmpty(dpShopIdList)) {
                    for (long dpShopId : dpShopIdList) {
                        // 获取shop对应的deal列表
                        Set<Long> dealIdSet = shopDealMap.get(dpShopId);
                        if (CollectionUtils.isNotEmpty(dealIdSet)) {
                            for (long dealId : dealIdSet) {
                                promotionalDealList.add(generateDeal(groundPromotionId, dpShopId, pointId, dealId, pointRowBO.getSales()));
                            }
                        }
                    }
                }
            }
        }

        promotionalDealRepository.insert(promotionalDealList);

        // Send mq
        sendMessage(groundPromotion);

        return Pair.of(dpCityId, groundPromotion.getId());
    }

    @Transactional
    public Pair<Integer, Long> createShopGroundPromotion(Integer dpCityId, GroundFastCreateDTO groundFastCreateDTO, GroundShopTable table) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.createShopGroundPromotion(java.lang.Integer,com.sankuai.carnation.distribution.groundpromotion.dto.GroundFastCreateDTO,com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundShopTable)");
        // Insert ground_promotion
        GroundPromotion groundPromotion = generateGroundPromotion(dpCityId, groundFastCreateDTO);
        groundPromotion.setName(groundPromotion.getName() + "（门店推）");
        groundPromotion.setScopeType(GroundPromotionScopeTypeEnum.SHOP.getCode());
        groundPromotionRepository.insert(groundPromotion);
        long groundPromotionId = groundPromotion.getId();

        // Insert ground_material
        List<GroundMaterial> groundMaterialList = generateMaterial(groundPromotionId, groundFastCreateDTO.getMaterials());
        materialRepository.insertMaterial(groundMaterialList);

        // Insert advanced_info
        GroundAdvancedCoupon groundAdvancedCoupon = groundFastCreateDTO.getCoupon();
        if (Objects.nonNull(groundAdvancedCoupon)) {
            GroundPromotionAdvancedInfo groundPromotionAdvancedInfo =
                    generateAdvancedInfo(groundPromotionId, groundAdvancedCoupon);
            groundPromotionAdvancedInfoRepository.insert(groundPromotionAdvancedInfo);
        }

        // Batch insert PromotionalDeal
        List<PromotionalDeal> promotionalDealList = new ArrayList<>();

        Map<Long, GroundShopInfoBO> groundShopInfoBOMap = table.getShopInfoByCity(dpCityId);
        if (!groundShopInfoBOMap.isEmpty()) {
            for (Map.Entry<Long, GroundShopInfoBO> entry : groundShopInfoBOMap.entrySet()) {
                Long dpShopId = entry.getKey();
                GroundShopInfoBO info = entry.getValue();
                if (Objects.nonNull(info) && CollectionUtils.isNotEmpty(info.getDealIdSet())) {
                    for (Long dealId : info.getDealIdSet()) {
                        promotionalDealList.add(generateDeal(groundPromotionId, dpShopId, 0L, dealId, info.getSales()));
                    }
                }
            }
        }

        promotionalDealRepository.insert(promotionalDealList);

        // Send mq
        sendMessage(groundPromotion);

        return Pair.of(dpCityId, groundPromotion.getId());
    }

    @Transactional
    public Pair<Integer, Long> createPointGroundPromotion(Integer dpCityId, GroundFastCreateDTO groundFastCreateDTO,
                                                          Map<Integer, List<GroundPromotionPointRowBO>> pointMap, Map<Long, Set<Long>> shopDealMap) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.createPointGroundPromotion(java.lang.Integer,com.sankuai.carnation.distribution.groundpromotion.dto.GroundFastCreateDTO,java.util.Map,java.util.Map)");
        // Insert ground_promotion
        GroundPromotion groundPromotion = generateGroundPromotion(dpCityId, groundFastCreateDTO);
        groundPromotion.setName(groundPromotion.getName() + "（点位推）");
        groundPromotion.setScopeType(GroundPromotionScopeTypeEnum.POINT.getCode());
        groundPromotionRepository.insert(groundPromotion);
        long groundPromotionId = groundPromotion.getId();

        // Insert ground_material
        List<GroundMaterial> groundMaterialList = generateMaterial(groundPromotionId, groundFastCreateDTO.getMaterials());
        materialRepository.insertMaterial(groundMaterialList);

        // Insert advanced_info
        GroundAdvancedCoupon groundAdvancedCoupon = groundFastCreateDTO.getCoupon();
        if (Objects.nonNull(groundAdvancedCoupon)) {
            GroundPromotionAdvancedInfo groundPromotionAdvancedInfo =
                    generateAdvancedInfo(groundPromotionId, groundAdvancedCoupon);
            groundPromotionAdvancedInfoRepository.insert(groundPromotionAdvancedInfo);
        }

        // Batch insert PromotionalDeal
        List<PromotionalDeal> promotionalDealList = new ArrayList<>();

        List<GroundPromotionPointRowBO> pointRowBOList = pointMap.get(dpCityId);
        if (CollectionUtils.isNotEmpty(pointRowBOList)) {
            for (GroundPromotionPointRowBO pointRowBO : pointRowBOList) {
                // 插入点位
                GroundPromotionPoint point = generatePoint(pointRowBO);
                groundPromotionPointRepository.insert(point);
                long pointId = point.getId();

                // 获取点位的shop列表
                List<Long> dpShopIdList = pointRowBO.getDpShopIdList();
                if (CollectionUtils.isNotEmpty(dpShopIdList)) {
                    for (long dpShopId : dpShopIdList) {
                        // 获取shop对应的deal列表
                        Set<Long> dealIdSet = shopDealMap.get(dpShopId);
                        if (CollectionUtils.isNotEmpty(dealIdSet)) {
                            for (long dealId : dealIdSet) {
                                promotionalDealList.add(generateDeal(groundPromotionId, dpShopId, pointId, dealId, pointRowBO.getSales()));
                            }
                        }
                    }
                }
            }
        }

        promotionalDealRepository.insert(promotionalDealList);

        // Send mq
        sendMessage(groundPromotion);

        return Pair.of(dpCityId, groundPromotion.getId());
    }
    private GroundPromotion generateGroundPromotion(Integer dpCityId, GroundFastCreateDTO groundFastCreateDTO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.generateGroundPromotion(java.lang.Integer,com.sankuai.carnation.distribution.groundpromotion.dto.GroundFastCreateDTO)");
        return GroundPromotion.builder()
                .name(groundFastCreateDTO.getName())
                .cityId(dpCityId)
                .status(GroundPromotionActEnum.CITY_DISTRIBUTE.getCode())
                .allotStartTime(groundFastCreateDTO.getAllotStartTime())
                .allotEndTime(groundFastCreateDTO.getAllotEndTime())
                .groundStartTime(groundFastCreateDTO.getGroundStartTime())
                .groundEndTime(groundFastCreateDTO.getGroundEndTime())
                .projectName("")
                .scopeType(groundFastCreateDTO.getScopeType())
                .creatorMis(groundFastCreateDTO.getCreatorMisId())
                .buId(groundFastCreateDTO.getBuId())
                .templateId(groundFastCreateDTO.getTemplateId())
                .build();
    }

    private List<GroundMaterial> generateMaterial(long groundPromotionId, List<GroundMaterialDTO> groundMaterialDTOList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.generateMaterial(long,java.util.List)");
        List<GroundMaterial> groundMaterialList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(groundMaterialDTOList)) {
            for (GroundMaterialDTO materialDTO : groundMaterialDTOList) {
                groundMaterialList.add(
                        GroundMaterial.builder()
                                .groundPromotionId(groundPromotionId)
                                .materialName(materialDTO.getMaterialName())
                                .qrX(materialDTO.getQrX())
                                .qrY(materialDTO.getQrY())
                                .isStoreName(Optional.ofNullable(materialDTO.getIsStoreName()).orElse((byte) 0))
                                .isMisId(Optional.ofNullable(materialDTO.getIsMisId()).orElse((byte) 0))
                                .storeName(Optional.ofNullable(materialDTO.getStoreName()).orElse(""))
                                .typeFaceSize(materialDTO.getTypeFaceSize())
                                .typeFaceColor(materialDTO.getTypeFaceColor())
                                .misFontSize(materialDTO.getMisFontSize())
                                .misFontColor(materialDTO.getMisFontColor())
                                .picturePath(materialDTO.getPicturePath())
                                .qrWidth(materialDTO.getQrWidth())
                                .qrHeight(materialDTO.getQrHeight())
                                .storeX(materialDTO.getStoreX())
                                .storeY(materialDTO.getStoreY())
                                .build());
            }
        }
        return groundMaterialList;
    }

    private GroundPromotionAdvancedInfo generateAdvancedInfo(long groundPromotionId, GroundAdvancedCoupon groundAdvancedCoupon) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.generateAdvancedInfo(long,com.sankuai.carnation.distribution.groundpromotion.dto.GroundAdvancedCoupon)");
        GroundPromotionAdvancedInfo groundPromotionAdvancedInfo =
                GroundPromotionAdvancedInfo.builder().groundPromotionId(groundPromotionId).build();
        if (Objects.nonNull(groundAdvancedCoupon)) {
            groundPromotionAdvancedInfo.setIsIssueCoupon(groundAdvancedCoupon.isIssueCoupon() ? (byte) 1 : (byte) 0);
            groundPromotionAdvancedInfo.setDpCouponCode(groundAdvancedCoupon.isIssueCoupon() ? groundAdvancedCoupon.getDpCouponCode() : "");
            groundPromotionAdvancedInfo.setMtCouponCode(groundAdvancedCoupon.isIssueCoupon() ? groundAdvancedCoupon.getMtCouponCode() : "");
            groundPromotionAdvancedInfo.setIsIssueWxGroupCoupon(groundAdvancedCoupon.isIssueWxGroupCoupon() ? (byte) 1 : (byte) 0);
            groundPromotionAdvancedInfo.setWxGroupCouponInfo(Objects.nonNull(groundAdvancedCoupon.getWxGroupCouponInfo()) ?
                    JSONObject.toJSONString(groundAdvancedCoupon.getWxGroupCouponInfo()) : "");
        }
        return groundPromotionAdvancedInfo;
    }

    private PromotionalDeal generateDeal(Long groundPromotionalId, Long dpShopId, Long pointId, Long dealId, String sales) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.generateDeal(java.lang.Long,java.lang.Long,java.lang.Long,java.lang.Long,java.lang.String)");
        return PromotionalDeal.builder()
                .dealId(dealId)
                .promotionalType("立减")
                .shopId(dpShopId)
                .pointId(pointId)
                .merchantsId(0L)
                .status(StringUtils.isBlank(sales)
                        ? GroundPromotionDealStatusEnum.CITY_MANAGER_CONFIRM.getCode()
                        : GroundPromotionDealStatusEnum.SALES_PERSON_REPORT.getCode())
                .bindMisid(sales)
                .groundPromotionalId(groundPromotionalId)
                .extInfo("")
                .productType(ProductTypeEnum.TUAN_DEAL.getCode())
                .build();
    }

    private void sendMessage(GroundPromotion groundPromotion) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.sendMessage(com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion)");
        Date groundStartTime = groundPromotion.getGroundStartTime();
        Date groundEndTime = groundPromotion.getGroundEndTime();

        groundPromotionProducer.asyncSendDelayMsg(
                GroundEventTypeEnum.PROMOTION_TURN_TO_END,
                groundPromotion.getId(),
                null,
                DateUtils.differentMsByMillisecond(new Date(), groundEndTime)
                );

        groundPromotionProducer.asyncSendDelayMsg(
                GroundEventTypeEnum.PROMOTION_TURN_TO_IN_PROGRESS,
                groundPromotion.getId(),
                null,
                DateUtils.differentMsByMillisecond(new Date(), groundStartTime)
        );
    }

    private GroundPromotionPoint generatePoint(GroundPromotionPointRowBO pointRowBO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreatePromotionTransaction.generatePoint(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPointRowBO)");
        return GroundPromotionPoint.builder()
                .pointName(pointRowBO.getPointName())
                .shopId(String.join(GroundPromotionConstants.BIND_MIS_ID_SEPARATOR, pointRowBO.getDpShopIdList().stream().map(String::valueOf).collect(Collectors.toList())))
                .showType(pointRowBO.getShowType())
                .lng(pointRowBO.getLng())
                .lat(pointRowBO.getLat())
                .status(PointStatusEnum.ON_LINE.getCode())
                .build();
    }
}
