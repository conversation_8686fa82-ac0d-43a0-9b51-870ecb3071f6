package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.EasyExcel;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.CreateResultRowStrBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import java.io.InputStream;
import java.net.URL;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/8/22
 * @Description:
 */
@Component
@Slf4j
public class CreateResultExcelUtils {

    public List<CreateResultRowStrBO> getExcelDataByParseFile(String url) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreateResultExcelUtils.getExcelDataByParseFile(java.lang.String)");
        List<CreateResultRowStrBO> excelDataList = parseFile(url);
        return excelDataList;
    }

    private List<CreateResultRowStrBO> parseFile(String url) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CreateResultExcelUtils.parseFile(java.lang.String)");
        try {
            HttpsURLConnection conn = (HttpsURLConnection) new URL(url).openConnection();
            InputStream is = conn.getInputStream();

            // Read excel
            CreateResultRowListener createResultRowListener = new CreateResultRowListener();
            EasyExcel.read(is, CreateResultRowStrBO.class, createResultRowListener).sheet(0).doRead();
            return createResultRowListener.getCachedDataList();
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".parseFile error, exception is", e);
            return Lists.newArrayList();
        }
    }
}
