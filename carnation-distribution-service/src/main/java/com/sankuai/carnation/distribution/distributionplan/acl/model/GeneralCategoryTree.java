package com.sankuai.carnation.distribution.distributionplan.acl.model;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GeneralCategoryTree {
    /**
     * 类目ID
     */
    private Long platformCategoryId;
    /**
     * 类目名称
     */
    private String categoryName;
    /**
     * 类目层级
     */
    private Integer level;
    /**
     * 类目序号
     */
    private Integer seq;
    /**
     * 类目子集
     */
    private List<GeneralCategoryTree> childList;

    /**
     * 父类目名称
     */
    private String parentCategoryName;

    /**
     * 父平台类目ID
     */
    private Long parentPlatformCategoryId;
}