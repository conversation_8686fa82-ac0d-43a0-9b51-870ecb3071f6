package com.sankuai.carnation.distribution.intention.domain.calculate.handler.code;

import com.dianping.cat.Cat;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.order.PromoQRCodeOrderDTO;
import com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeOrderService;
import com.dianping.technician.dto.common.CommonRes;
import com.dianping.technician.enums.ApplicationIdEnum;
import com.dianping.technician.service.TechFenYongService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributor.domain.DistributorChannelRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.BusinessChannelCalRequestBO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 功能描述: 分销标识解析工具
 *
 * <AUTHOR>
 * @date 2022/05/03
 **/
@Service
public class DistributionCodeAnalyser {

    private static final String CODE_SOURCE_TYPE = "DistributionCodeChannel";

    private final Cache<String, String> orderChannelCache = CacheBuilder.newBuilder()
            .expireAfterWrite(2, TimeUnit.SECONDS)
            .maximumSize(100000)
            .build();

    @Resource
    private DistributorRootService distributorRootService;

    @Resource
    private TechFenYongService techFenYongService;

    @Autowired
    private PromoQRCodeOrderService promoCodeOrderService;

    @Autowired
    private DistributorGroupRootService distributorGroupRootService;

    @Autowired
    private DistributorChannelRootService channelRootService;

    /**
     * 分销标识解析
     */
    public DistributionBusinessChannelEnum calculateChannelByCode(BusinessChannelCalRequestBO request) {
        if (request == null || StringUtils.isBlank(request.getCode())) {
            return DistributionBusinessChannelEnum.UNKNOWN;
        }
        String cacheKey = buildCacheKey(request);
        String channelCode = orderChannelCache.getIfPresent(cacheKey);
        if (channelCode != null) {
            return DistributionBusinessChannelEnum.fromCode(channelCode);
        }
        DistributionBusinessChannelEnum channel = null;
        // 美团联盟
        if (request.getCode().matches("^cps:daozong:.+")) {
            channel = DistributionBusinessChannelEnum.MTLM;
        }
        // 美团纷纷
        if (channel == null && request.getCode().matches("^py\\w+")) {
            DistributorBO distributor = distributorRootService.getDistributor(request.getCode());
            if (distributor != null) {
                channel = calculateDistributorV1BusinessChannel(distributor);
            }
        }
        // 地推
        if (channel == null && request.getCode().matches("^dt.+")) {
            BizDistributorBO distributor = distributorRootService.getBizDistributor(request.getCode());
            if (distributor != null) {
                channel = DistributionBusinessChannelEnum.GROUND_PROMOTION;
            }
        }
        if (channel == null && request.getCode().matches("^mbp\\w+")) {
            channel = DistributionBusinessChannelEnum.MEDICAL_BARGAIN;
        }
        if (channel == null && request.getCode().matches("^syzb.+")) {
            String distributorCode = request.getCode();
            distributorCode = URLDecoder.decode(distributorCode);
            BizDistributorBO distributor = distributorRootService.getBizDistributor(distributorCode);
            if (distributor != null) {
                channel = DistributionBusinessChannelEnum.PRIVATE_LIVE_CONSULTANT;
            }
        }
        // 手艺人
        if (channel == null) {
            ApplicationIdEnum applicationId = null;
            if (request.getProductType() == ProductTypeEnum.TUAN_DEAL.getCode()) {
                applicationId = ApplicationIdEnum.BEAUTY_DEAL_UNCOMMISSION;
            } else if (request.getProductType() == ProductTypeEnum.TIME_CARD.getCode()) {
                applicationId = ApplicationIdEnum.BEAUTY_TIMESCARD;
            }
            if (applicationId != null) {
                CommonRes<Integer> techIdResponse = techFenYongService.getTechIdByMkt(request.getCode(), applicationId);
                if (techIdResponse.isSuccess() && techIdResponse.getData() != null && techIdResponse.getData() > 0) {
                    channel = DistributionBusinessChannelEnum.TECHNICIAN;
                }
            }
        }
        if (channel == null) {
            channel = DistributionBusinessChannelEnum.UNKNOWN;
        }
        logCatEvent(channel);
        orderChannelCache.put(cacheKey, channel.getCode());
        return channel;
    }

    public DistributionBusinessChannelEnum calculateChannelForWarning(BusinessChannelCalRequestBO request) {
        String cacheKey = buildCacheKey(request);
        String channelCode = orderChannelCache.getIfPresent(cacheKey);
        if (channelCode != null && !DistributionBusinessChannelEnum.UNKNOWN.getCode().equalsIgnoreCase(channelCode)) {
            return DistributionBusinessChannelEnum.fromCode(channelCode);
        }
        DistributionBusinessChannelEnum channel = calculateChannelByCode(request);
        // 优惠码
        if (channel == null && isPromoCodeOrder(request.getOrderType(), request.getOrderId())) {
            channel = DistributionBusinessChannelEnum.PROMO_CODE;
        }
        if (channel == null) {
            channel = DistributionBusinessChannelEnum.UNKNOWN;
        }
        orderChannelCache.put(cacheKey, channel.getCode());
        return channel;
    }

    private DistributionBusinessChannelEnum calculateDistributorV1BusinessChannel(DistributorBO distributor) {
        if (distributor.getDistributorGroupId() != null) {
            DistributorGroupBO group = distributorGroupRootService.getDistributorGroup(distributor.getDistributorGroupId());
            if (group != null && CollectionUtils.isNotEmpty(group.getChannelList())) {
                List<DistributionChannelBO> channelList = channelRootService.getChannel(group.getChannelList());
                boolean isMedicalDistributeShopGroup = channelList.stream()
                        .anyMatch(channel -> channel.getCode().equalsIgnoreCase(DistributionBusinessChannelEnum.MEDICAL_DISTRIBUTE.getCode()));
                if (isMedicalDistributeShopGroup) {
                    return DistributionBusinessChannelEnum.MEDICAL_DISTRIBUTE;
                }
                boolean isPetDistribute = channelList.stream().anyMatch(channel -> channel.getCode().equalsIgnoreCase(DistributionBusinessChannelEnum.PET_DISTRIBUTE.getCode()));
                if (isPetDistribute) {
                    return DistributionBusinessChannelEnum.PET_DISTRIBUTE;
                }
            }
        }
        return DistributionBusinessChannelEnum.MT_FENFEN;
    }

    private boolean isPromoCodeOrder(int orderType, String orderId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.intention.domain.calculate.handler.code.DistributionCodeAnalyser.isPromoCodeOrder(int,java.lang.String)");
        if (orderType == DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode()) {
            CommonResponse<PromoQRCodeOrderDTO> response = promoCodeOrderService.getCodeOrderByUnifiedOrderId(orderId);
            return response != null && response.isSuccess() && response.getData() != null;
        } else if (orderType == DistributionOrderTypeEnum.DZ_LONG_ORDER_START_WITH_CHAR_4.getCode()) {
            CommonResponse<PromoQRCodeOrderDTO> response = promoCodeOrderService.getCodeOrderByLongOrderId(NumberUtils.toLong(orderId));
            return response != null && response.isSuccess() && response.getData() != null;
        }
        return false;
    }

    private String buildCacheKey(BusinessChannelCalRequestBO request) {
        return String.format("%s_%s", request.getOrderType(), request.getOrderId());
    }

    private void logCatEvent(DistributionBusinessChannelEnum channel) {
        Cat.logEvent(CODE_SOURCE_TYPE, channel.getCode());
        Map<String, String> channelTag = Maps.newHashMap();
        channelTag.put("渠道", channel.getDesc());
        Cat.logMetricForCount("渠道计算强意向渠道分布", 1, channelTag);
    }
}
