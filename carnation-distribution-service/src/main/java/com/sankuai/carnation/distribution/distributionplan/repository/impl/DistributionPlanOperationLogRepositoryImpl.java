package com.sankuai.carnation.distribution.distributionplan.repository.impl;

import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanOperationLog;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.repository.DistributionPlanOperationLogRepository;
import com.sankuai.carnation.distribution.distributionplan.repository.dao.DistributionPlanOperationLogMapper;
import com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanOperationLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class DistributionPlanOperationLogRepositoryImpl implements DistributionPlanOperationLogRepository {

    @Resource
    private DistributionPlanOperationLogMapper distributionPlanOperationLogMapper;

    @Override
    public boolean addDistributionPlanLog(DistributionPlanOperationLog distributionPlanOperationLog) {
        try {
            DistributionPlanOperationLogEntity entity = convertToEntity(distributionPlanOperationLog);
            int result = distributionPlanOperationLogMapper.insertDistributionPlanOperationLog(entity);
            if (result <= 0) {
                throw new DistributionPlanException("新增分销计划操作日志失败");
            }
            return true;
        } catch (Exception e) {
            Cat.logEvent("AddDistributionPlanLogFailed", "distributionPlanOperationLog:"+ distributionPlanOperationLog);
            log.error("DistributionPlanOperationLogRepository.addDistributionPlanLog failed, distributionPlanOperationLog:{}", distributionPlanOperationLog, e);
            return false;
        }
    }

    private DistributionPlanOperationLogEntity convertToEntity (DistributionPlanOperationLog distributionPlanOperationLog) {
        DistributionPlanOperationLogEntity entity = new DistributionPlanOperationLogEntity();
        entity.setPlanId(distributionPlanOperationLog.getPlanId());
        entity.setOperationType(distributionPlanOperationLog.getOperationType().getType());
        entity.setOperationContent(distributionPlanOperationLog.getOperationContent());
        entity.setOperatorType(distributionPlanOperationLog.getOperatorType().getType());
        entity.setOperator(distributionPlanOperationLog.getOperator());

        return entity;
    }
}