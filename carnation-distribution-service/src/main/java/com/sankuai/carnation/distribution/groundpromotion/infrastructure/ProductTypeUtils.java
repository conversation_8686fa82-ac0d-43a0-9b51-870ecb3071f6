package com.sankuai.carnation.distribution.groundpromotion.infrastructure;

import com.dianping.pay.order.domain.enums.ProductEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.utils.ProductTypeAnalyser;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/24
 * @Description: com.dianping.pay.order.domain.enums.ProductEnum 用于判断productType是否是团购、预付、预订、次卡和标品
 */
public class ProductTypeUtils {

    public static boolean isGroupon(int productType) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils.isGroupon(int)");
        return ProductEnum.isGrouponProduct(productType);
    }

    public static boolean isPrepay(int productType) {
        ProductTypeEnum productTypeEnum = ProductTypeAnalyser.fromOrderBizType(productType);
        return productTypeEnum.equals(ProductTypeEnum.PREPAY);
    }

    public static boolean isBook(int productType) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils.isBook(int)");
        ProductTypeEnum productTypeEnum = ProductTypeAnalyser.fromOrderBizType(productType);
        return productTypeEnum.equals(ProductTypeEnum.BOOK);
    }

    public static boolean isTimeCard(int productType){
        ProductTypeEnum productTypeEnum = ProductTypeAnalyser.fromOrderBizType(productType);
        return productTypeEnum.equals(ProductTypeEnum.TIME_CARD);
    }

    public static boolean isStandard(int productType){
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils.isStandard(int)");
        ProductTypeEnum productTypeEnum = ProductTypeAnalyser.fromOrderBizType(productType);
        return productTypeEnum.equals(ProductTypeEnum.STANDARD_PRODUCT);
    }
}
