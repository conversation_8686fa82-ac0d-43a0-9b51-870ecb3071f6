package com.sankuai.carnation.distribution.product.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/9/4
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DistributionBatchImportBO {

    private int productType;

    private long productId;

    private int platform;

    private String mobile;

    private List<String> productPicList;

    private int channel;

    private int basicHotValue = 0;

    private int cityId;

    private String startTime;

    private String endTime;

    private double commissionValue;

    private double shopCommissionValue;

    private String shareContext;

    private List<String> sharePicList;

    private String sharePicWishQrCode;
}
