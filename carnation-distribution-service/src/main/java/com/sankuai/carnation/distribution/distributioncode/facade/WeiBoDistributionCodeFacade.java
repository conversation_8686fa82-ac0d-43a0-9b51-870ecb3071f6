package com.sankuai.carnation.distribution.distributioncode.facade;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributioncode.acl.BeautyContentProductAcl;
import com.sankuai.carnation.distribution.distributioncode.acl.model.BeautyContentProductDTO;
import com.sankuai.carnation.distribution.distributioncode.domain.DistributionCodeDoService;
import com.sankuai.carnation.distribution.distributioncode.domain.model.DistributionCodeParamMappingDO;
import com.sankuai.carnation.distribution.distributioncode.domain.model.DistributionCodeParseRequest;
import com.sankuai.carnation.distribution.distributioncode.dto.CpsInfo;
import com.sankuai.carnation.distribution.distributioncode.dto.DistributionCodeCheckRequest;
import com.sankuai.carnation.distribution.distributioncode.dto.ProductCpsChainGenerateRequest;
import com.sankuai.carnation.distribution.distributioncode.facade.model.WeiBoDistributionCode;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WeiBoDistributionCodeFacade {

    @Resource
    private DistributionCodeDoService distributionCodeDoService;

    @Resource
    private DistributionPlanDoService distributionPlanDoService;

    @Resource
    private DistributorRootService distributorRootService;

    @Resource
    private BeautyContentProductAcl beautyContentProductAcl;

    @Resource
    private RedisLockService redisLockService;

    private static final String CPS_CHAIN_PARAM_KEY = "distributionCode";

    private static final String CAT_TYPE = WeiBoDistributionCodeFacade.class.getSimpleName();

    public CpsInfo generateCpsChain(ProductCpsChainGenerateRequest request) {
        BizDistributorBO bizDistributorBo = distributorRootService.getBizDistributor(request.getPid());
        if (bizDistributorBo == null) {
            throw new RuntimeException("distributorPidNotFound");
        }

        DistributionBusinessChannelEnum channel = DistributionBusinessChannelEnum.fromCode(request.getChannel());
        Optional<BeautyContentProductDTO> beautyContentProductOptional = beautyContentProductAcl.parseChannelProductUrl(request.getUrl(), channel);
        if (!beautyContentProductOptional.isPresent()) {
            throw new RuntimeException("channelProductUrlParseFailed");
        }

        DistributionPlanQueryRequest distributionPlanQueryRequest = DistributionPlanQueryRequest.builder()
                .productIds(Lists.newArrayList(beautyContentProductOptional.get().getProductId()))
                .productType(beautyContentProductOptional.get().getProductType().getCode())
                .channel(channel.getCode())
                .statusList(Lists.newArrayList(DistributionPlanStatusEnum.EXECUTING.getCode()))
                .build();
        List<DistributionPlanDO> distributionPlanDos = distributionPlanDoService.batchQueryDistributionPlans(distributionPlanQueryRequest);
        if (CollectionUtils.isEmpty(distributionPlanDos)) {
            throw new RuntimeException("productExecutingDistributionPlanNotFound");
        }

        DistributionCodeParamMappingDO distributionCodeParamMappingDo = DistributionCodeParamMappingDO.builder()
                .channel(channel)
                .bizParam1(String.valueOf(beautyContentProductOptional.get().getProductId()))
                .bizParam2(request.getPid())
                .bizParam3(String.valueOf(distributionPlanDos.get(0).getPlanId()))
                .bizParam4(String.valueOf(beautyContentProductOptional.get().getProductType().getCode()))
                .build();
        redisLockService.tryLock("generateCpsChain_"+distributionCodeParamMappingDo.toString(),
                () -> distributionCodeDoService.generateDistributionCode(distributionCodeParamMappingDo), 3);

        return CpsInfo.builder()
                .cpsUrl(String.format("%s&%s=%s", request.getUrl(), CPS_CHAIN_PARAM_KEY, distributionCodeParamMappingDo.getDistributionCode()))
                .commissionRate(DistributionPlanDO.calCommissionRateToThousandths(distributionPlanDos.get(0).getCommissionStrategyVo().getDistributorCommissionRate()))
                .build();
    }

    public List<WeiBoDistributionCode> batchParseDistributionCode(List<String> distributionCodes) {
        List<DistributionCodeParamMappingDO> distributionCodeParamMappingDos = distributionCodeDoService.batchParseDistributionCode(distributionCodes, DistributionBusinessChannelEnum.WEI_BO);
        if (CollectionUtils.isEmpty(distributionCodeParamMappingDos)) {
            throw new RuntimeException("distributionCodeNotFound");
        }

        List<WeiBoDistributionCode> weiBoDistributionCodes = distributionCodeParamMappingDos.stream().map(this::convertToWeiBoDistributionCode).collect(Collectors.toList());
        Map<Long, DistributionPlanDO> distributionPlanIdToDtoMap = distributionPlanDoService.queryDistributionPlanByIds(weiBoDistributionCodes.stream().map(WeiBoDistributionCode::getPlanId).distinct().collect(Collectors.toList()));

        weiBoDistributionCodes.forEach(weiBoDistributionCode -> {
            DistributionPlanDO distributionPlanDo = distributionPlanIdToDtoMap.get(weiBoDistributionCode.getPlanId());
            if (distributionPlanDo == null) {
                return;
            }
            weiBoDistributionCode.fillDistributionPlanInfo(distributionPlanDo);
        });
        return weiBoDistributionCodes;
    }

    public Optional<WeiBoDistributionCode> parseDistributionCode(DistributionCodeParseRequest request) {
        try {
            DistributionCodeParamMappingDO distributionCodeParamMappingDo = distributionCodeDoService.parseDistributionCode(request.getDistributionCode(), request.getChannel());
            if (null == distributionCodeParamMappingDo) {
                throw new RuntimeException("distributionCodeNotFound");
            }

            WeiBoDistributionCode weiBoDistributionCode = convertToWeiBoDistributionCode(distributionCodeParamMappingDo);
            if (request.getNeedDistributorInfo() != null && request.getNeedDistributorInfo()) {
                BizDistributorBO bizDistributorBo = distributorRootService.getBizDistributor(weiBoDistributionCode.getDistributorPid());
                if (bizDistributorBo == null) {
                    throw new RuntimeException("distributorPidNotFound");
                }

                weiBoDistributionCode.fillDistributorInfo(bizDistributorBo);
            }

            if (request.getNeedDistributionPlanInfo() != null && request.getNeedDistributionPlanInfo()) {
                Optional<DistributionPlanDO> distributionPlanDoOptional = distributionPlanDoService.loadDistributionPlan(weiBoDistributionCode.getPlanId());
                if (!distributionPlanDoOptional.isPresent()) {
                    throw new RuntimeException("distributionPlanNotFound");
                }

                weiBoDistributionCode.fillDistributionPlanInfo(distributionPlanDoOptional.get());
            }

            return Optional.of(weiBoDistributionCode);
        } catch (RuntimeException re) {
            Cat.logEvent("DistributionCodeParseFailed", re.getMessage() + "request:" + request);
            log.info("WeiBoDistributionCodeFacade.parseDistributionCode failed, request:{}", request, re);
            return Optional.empty();
        }
    }

    private WeiBoDistributionCode convertToWeiBoDistributionCode(DistributionCodeParamMappingDO distributionCodeParamMappingDo) {
        WeiBoDistributionCode weiBoDistributionCode = new WeiBoDistributionCode();
        weiBoDistributionCode.setChannel(distributionCodeParamMappingDo.getChannel());
        weiBoDistributionCode.setDistributionCode(distributionCodeParamMappingDo.getDistributionCode());
        weiBoDistributionCode.setProductId(Long.valueOf(distributionCodeParamMappingDo.getBizParam1()));
        weiBoDistributionCode.setDistributorPid(distributionCodeParamMappingDo.getBizParam2());
        weiBoDistributionCode.setPlanId(Long.valueOf(distributionCodeParamMappingDo.getBizParam3()));
        weiBoDistributionCode.setProductType(ProductTypeEnum.fromCode(Integer.parseInt(distributionCodeParamMappingDo.getBizParam4())));
        return weiBoDistributionCode;
    }

    public boolean checkDistributionCodeValid(DistributionCodeCheckRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "checkDistributionCodeValid");
        try {
            if (!DistributionBusinessChannelEnum.WEI_BO.equals(request.getChannel())) {
                log.error("WeiBoDistributionCodeFacade.checkDistributionCodeValid only support weibo channel, request:{}", request);
                throw new RuntimeException("channelUnSupport");
            }

            DistributionCodeParamMappingDO distributionCodeParamMappingDo = distributionCodeDoService.parseDistributionCode(request.getDistributionCode(), request.getChannel());
            if (null == distributionCodeParamMappingDo) {
                throw new RuntimeException("distributionCodeNotFound");
            }

            WeiBoDistributionCode weiBoDistributionCode = convertToWeiBoDistributionCode(distributionCodeParamMappingDo);
            if (!weiBoDistributionCode.getProductId().equals(request.getProductId())) {
                throw new RuntimeException("distributionCodeNotMatchProductId");
            }
            if (!weiBoDistributionCode.getProductType().equals(request.getProductType())) {
                throw new RuntimeException("distributionCodeNotMatchProductType");
            }

            Optional<DistributionPlanDO> distributionPlanDoOptional = distributionPlanDoService.loadDistributionPlan(weiBoDistributionCode.getPlanId());
            return distributionPlanDoOptional.isPresent() && distributionPlanDoOptional.get().getStatus().equals(DistributionPlanStatusEnum.EXECUTING);
        } catch (RuntimeException re) {
            Cat.logEvent("invalidDistributionCode", re.getMessage() + "request:" + request);
            log.error("WeiBoDistributionCodeFacade.checkDistributionCodeValid failed, request:{}", request, re);
            transaction.setStatus(re);
            return false;
        } finally {
            transaction.complete();
        }
    }
}
