package com.sankuai.carnation.distribution.intention.repository.db;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DistributionIntentionLogCriteria {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DistributionIntentionLogCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdIsNull() {
            addCriterion("intention_id is null");
            return (Criteria) this;
        }

        public Criteria andIntentionIdIsNotNull() {
            addCriterion("intention_id is not null");
            return (Criteria) this;
        }

        public Criteria andIntentionIdEqualTo(Long value) {
            addCriterion("intention_id =", value, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdNotEqualTo(Long value) {
            addCriterion("intention_id <>", value, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdGreaterThan(Long value) {
            addCriterion("intention_id >", value, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("intention_id >=", value, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdLessThan(Long value) {
            addCriterion("intention_id <", value, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdLessThanOrEqualTo(Long value) {
            addCriterion("intention_id <=", value, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdIn(List<Long> values) {
            addCriterion("intention_id in", values, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdNotIn(List<Long> values) {
            addCriterion("intention_id not in", values, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdBetween(Long value1, Long value2) {
            addCriterion("intention_id between", value1, value2, "intentionId");
            return (Criteria) this;
        }

        public Criteria andIntentionIdNotBetween(Long value1, Long value2) {
            addCriterion("intention_id not between", value1, value2, "intentionId");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdIsNull() {
            addCriterion("distributor_group_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdIsNotNull() {
            addCriterion("distributor_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdEqualTo(Integer value) {
            addCriterion("distributor_group_id =", value, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdNotEqualTo(Integer value) {
            addCriterion("distributor_group_id <>", value, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdGreaterThan(Integer value) {
            addCriterion("distributor_group_id >", value, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("distributor_group_id >=", value, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdLessThan(Integer value) {
            addCriterion("distributor_group_id <", value, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("distributor_group_id <=", value, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdIn(List<Integer> values) {
            addCriterion("distributor_group_id in", values, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdNotIn(List<Integer> values) {
            addCriterion("distributor_group_id not in", values, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("distributor_group_id between", value1, value2, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributorGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("distributor_group_id not between", value1, value2, "distributorGroupId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdIsNull() {
            addCriterion("distribution_product_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdIsNotNull() {
            addCriterion("distribution_product_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdEqualTo(Long value) {
            addCriterion("distribution_product_id =", value, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdNotEqualTo(Long value) {
            addCriterion("distribution_product_id <>", value, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdGreaterThan(Long value) {
            addCriterion("distribution_product_id >", value, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("distribution_product_id >=", value, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdLessThan(Long value) {
            addCriterion("distribution_product_id <", value, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdLessThanOrEqualTo(Long value) {
            addCriterion("distribution_product_id <=", value, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdIn(List<Long> values) {
            addCriterion("distribution_product_id in", values, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdNotIn(List<Long> values) {
            addCriterion("distribution_product_id not in", values, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdBetween(Long value1, Long value2) {
            addCriterion("distribution_product_id between", value1, value2, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andDistributionProductIdNotBetween(Long value1, Long value2) {
            addCriterion("distribution_product_id not between", value1, value2, "distributionProductId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}