package com.sankuai.carnation.distribution.privatelive.consultant.domain.order;

import lombok.Data;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/1/10
 * @Description:
 */
@Data
public class PrivateLiveOrderIntentionPageQueryRequest {

    private Long consultantTaskId;

    private String orderId;

    private Long longOrderId;

    private List<Long> dpUserIdList;

    private List<Long> mtUserIdList;

    private List<Integer> statusList;

    private Integer pageNo;

    private Integer pageSize;
}
