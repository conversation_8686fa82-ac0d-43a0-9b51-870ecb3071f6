package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.cat.Cat;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.CreateResultRowBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundShopInfoBO;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/25
 * @description
 */
public class GroundShopTable {

    private static final int EXCEPTED_ROWS = 50;
    private static final int EXCEPTED_CELLS_PER_ROW = 500;

    // < CityId, DpShopId, < Set<DealId>, Set<MisId>, sales> >
    private final Table<Integer, Long, GroundShopInfoBO> shopTable;

    public GroundShopTable() {
        shopTable = HashBasedTable.create(EXCEPTED_ROWS, EXCEPTED_CELLS_PER_ROW);
    }

    public Collection<GroundShopInfoBO> getAllShopInfo() {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundShopTable.getAllShopInfo()");
        return shopTable.values();
    }

    public Map<Long, GroundShopInfoBO> getShopInfoByCity(Integer dpCityId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundShopTable.getShopInfoByCity(java.lang.Integer)");
        return shopTable.row(dpCityId);
    }

    public GroundShopInfoBO put(CreateResultRowBO row) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundShopTable.put(com.sankuai.carnation.distribution.groundpromotion.repository.bo.CreateResultRowBO)");
        GroundShopInfoBO info = shopTable.get(row.getDpCityId(), row.getDpShopId());

        if (info == null) {
            info = new GroundShopInfoBO();
        }
        info.getDealIdSet().add(row.getDpDealId());
        info.getMisIdSet().addAll(row.getMisIdList());

        return shopTable.put(row.getDpCityId(), row.getDpShopId(), info);
    }
}
