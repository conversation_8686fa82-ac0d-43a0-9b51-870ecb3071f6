package com.sankuai.carnation.distribution.kuaishoubill.acl.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.carnation.distribution.kuaishoubill.acl.PolarisOpenAcl;
import com.sankuai.carnation.distribution.kuaishoubill.acl.model.KuaiShouBillResponse;
import com.sankuai.carnation.distribution.kuaishoubill.domain.model.KuaiShouBillPageQueryRequest;
import com.sankuai.connectivity.adapter.platform.client.api.InteractWithPartnerCommonApi;
import com.sankuai.connectivity.adapter.platform.client.model.request.PartnerRequestDTO;
import com.sankuai.connectivity.adapter.platform.client.model.response.PartnerResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PolarisOpenAclImpl implements PolarisOpenAcl {

    @Resource
    private InteractWithPartnerCommonApi interactWithPartnerCommonApi;

    /**
     * 北极星开放接口返回成功的code
     */
    private static final int POLARIS_OPEN_API_SUCCEED_CODE = 200;

    /**
     * 快手开放接口返回成功的code
     */
    private static final long KUAI_SHOU_OPEN_API_SUCCEED_CODE = 0L;

    private static final String KUAI_SHOU_IDENTITY_ID_ASSIGNED_BY_POLARIS = "kuai.shou.identity.id.assigned.by.polaris";

    private static final String KUAI_SHOU_RESPONSE_ENV_INVOKE_BY_POLARIS = "kuai.shou.response.env.invoke.by.polaris";

    @Override
    public KuaiShouBillResponse pageQueryKuaiShouBill(KuaiShouBillPageQueryRequest request) {
        try {
            if (null == request || null == request.getCursor() || request.getCursor() < 0L
                    || request.getPageSize() == null || request.getPageSize() > 50 || null == request.getBillDate()) {
                throw new RuntimeException("illegalParam");
            }

            PartnerRequestDTO partnerRequestDTO = new PartnerRequestDTO();
            //请求的外部商家标识，填直连侧分配的对接身份id（线上线下不一致，快手线下：47017，线上：1473）
            partnerRequestDTO.setId(Lion.getString(MdpContextUtils.getAppKey(), KUAI_SHOU_IDENTITY_ID_ASSIGNED_BY_POLARIS));
            //业务线标识，固定值 700
            partnerRequestDTO.setBizLine(700);
            //请求直连侧的接口标识
            partnerRequestDTO.setAction("dz.adapter.ks.bill.page.query");
            //请求外部商家的具体内容，直连侧实现内部数据模型映射到请求商家的数据模型
            partnerRequestDTO.setRequestBody(JSON.toJSONString(request));
            Map<String, String> extensionMap = Maps.newHashMap();
            //请求外部商家的正式还是测试环境, key:env, 正式 10, 测试 20，如果不区分，默认填10
            extensionMap.put("env", Lion.getString(MdpContextUtils.getAppKey(), KUAI_SHOU_RESPONSE_ENV_INVOKE_BY_POLARIS));
            partnerRequestDTO.setExtension(extensionMap);

            PartnerResponseDTO partnerResponse = interactWithPartnerCommonApi.interactWithPartner(partnerRequestDTO);
            if (null == partnerResponse || partnerResponse.getCode() == null || !partnerResponse.getCode().equals(POLARIS_OPEN_API_SUCCEED_CODE)) {
                throw new RuntimeException("invokePolarisReturnFailed, partnerRequest:"+ JSONObject.toJSONString(partnerRequestDTO));
            }
            if (StringUtils.isBlank(partnerResponse.getResponseBody())) {
                throw new RuntimeException("invokePolarisReturnEmptyData, partnerRequest:"+ JSONObject.toJSONString(partnerRequestDTO));
            }

            KuaiShouBillResponse kuaiShouBillResponse = JSON.parseObject(partnerResponse.getResponseBody(), new TypeReference<KuaiShouBillResponse>(){});
            if (null == kuaiShouBillResponse || null == kuaiShouBillResponse.getErrorCode() || !kuaiShouBillResponse.getErrorCode().equals(KUAI_SHOU_OPEN_API_SUCCEED_CODE)) {
                throw new RuntimeException(String.format("invokeKuaiShouApiFailed, partnerRequest:%s, kuaiShouBillResponse:%s",
                        JSONObject.toJSONString(partnerRequestDTO), kuaiShouBillResponse));
            }

            kuaiShouBillResponse.verifyDataIntegrity();
            return kuaiShouBillResponse;
        } catch (Exception e) {
            Cat.logEvent("PageQueryKuaiShouBillFailed", e.getMessage() + request);
            log.error("PolarisOpenAcl.pageQueryKuaiShouBill process failed, request:{}, msg:{}", request, e.getMessage(), e);
            return null;
        }
    }
}
