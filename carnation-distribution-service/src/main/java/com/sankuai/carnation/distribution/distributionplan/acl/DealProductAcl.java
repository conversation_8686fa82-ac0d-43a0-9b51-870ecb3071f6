package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.distributionplan.acl.model.DealProductInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DealProductAcl {

    /**
     * 批量查询美团商品id列表
     * @param mtProductIdList 美团商品id
     * @return
     */
    List<DealProductInfo> queryMtDealProductByIds(List<Long> mtProductIdList);
    /**
     * 批量查询点评商品id列表
     * @param dpProductIdList 点评商品id
     * @return
     */
    List<DealProductInfo> queryDpDealProductByIds(List<Long> dpProductIdList);

    /**
     * 批量查询美团商品id列表
     * @param mtProductIdList mt商品ID
     * @return 商品Map集
     */
    Map<Long, DealProductInfo> queryMtDealProductToMap(List<Long> mtProductIdList);
}
