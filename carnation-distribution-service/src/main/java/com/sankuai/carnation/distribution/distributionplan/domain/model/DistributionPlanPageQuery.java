package com.sankuai.carnation.distribution.distributionplan.domain.model;

import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DistributionPlanPageQuery {

    private Integer pageNo;

    private Integer pageSize;

    private Long productId;

    private Integer productType;

    /**
     * 渠道
     * @see DistributionBusinessChannelEnum
     */
    private String channel;

    private List<Integer> statusList;
}
