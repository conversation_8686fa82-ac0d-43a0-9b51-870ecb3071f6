package com.sankuai.carnation.distribution.distributionplan.service.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.carnation.distribution.distributionplan.constant.LionConstant;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanStatusUpdateRequest;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanOperationEnum;
import com.sankuai.carnation.distribution.distributionplan.service.producer.dto.DistributionPlanDelayMsg;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DistributionPlanDelayTaskConsumer implements InitializingBean {

    private IConsumerProcessor consumer;

    @Resource
    private DistributionPlanDoService distributionPlanDoService;

    private static final long MILLIS_PER_SECOND = 1000;

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.distribution.service");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "dz.distribution.plan.delay.task.consumer");
        consumer = MafkaClient.buildConsumerFactory(properties, "distribution.plan.status.change.delay.task");
        consumer.recvMessageWithParallel(String.class, this::handle);
    }

    private ConsumeStatus handle(MafkaMessage<String> mafkaMessage, MessagetContext messagetContext) {
        try {
            if (StringUtils.isBlank(mafkaMessage.getBody())) {
                throw new RuntimeException("消息体内容为空");
            }

            DistributionPlanDelayMsg distributionPlanDelayMsg = JSONObject.parseObject(mafkaMessage.getBody(), new TypeReference<DistributionPlanDelayMsg>(){});

            long timeDiff = Math.abs(Math.subtractExact(System.currentTimeMillis(), distributionPlanDelayMsg.getOperateTime()));
            long maxToleranceDiffMillis = MILLIS_PER_SECOND * Lion.getLong(MdpContextUtils.getAppKey(), LionConstant.PLAN_STATUS_REVERSAL_MAX_TOLERANCE_DIFF_SECONDS, 30L);
            if (timeDiff >= maxToleranceDiffMillis) {
                Cat.logEvent("DistributionPlanChangeMsgUnPunctual", "DistributionPlanDelayTaskConsumer received unpunctual msg:"+distributionPlanDelayMsg);
            }
            DistributionPlanStatusUpdateRequest request = DistributionPlanStatusUpdateRequest.builder()
                    .channel(DistributionBusinessChannelEnum.fromCode(distributionPlanDelayMsg.getChannel()))
                    .planId(distributionPlanDelayMsg.getPlanId())
                    .operation(DistributionPlanOperationEnum.from(distributionPlanDelayMsg.getOperateType()))
                    .operationTime(distributionPlanDelayMsg.getOperateTime())
                    .build();
            distributionPlanDoService.updateDistributionPlanStatus(request);

            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("DistributionPlanDelayTaskConsumer.handle failed, mafkaMessage:{}", JSONObject.toJSONString(mafkaMessage), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }
}
