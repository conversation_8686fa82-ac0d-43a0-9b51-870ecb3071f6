package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.EasyExcel;
import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionScopeTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionMallRowBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionShopRowBO;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionStandardProductRowBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/10/20
 * @Description:
 */
@Component
@Slf4j
public class GroundPromotionProjectExcelUtils {

    public List<?> getExcelDataByParseFile(String url, int poiType) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionProjectExcelUtils.getExcelDataByParseFile(java.lang.String,int)");
        List<?> excelDataList = parseFile(url, poiType);
        return excelDataList;
    }

    public static List<?> parseFile(String url, int poiType) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionProjectExcelUtils.parseFile(java.lang.String,int)");
        try {
            HttpsURLConnection conn = (HttpsURLConnection) new URL(url).openConnection();
            InputStream inputStream = conn.getInputStream();
            if (poiType == GroundPromotionScopeTypeEnum.SHOP.getCode()) {
                GroundPromotionShopRowListener groundPromotionShopRowListener = new GroundPromotionShopRowListener();
                EasyExcel.read(inputStream, GroundPromotionShopRowBO.class, groundPromotionShopRowListener).sheet(0).headRowNumber(1).doRead();

                return groundPromotionShopRowListener.getCachedDataList();
            } else if (poiType == GroundPromotionScopeTypeEnum.MALL.getCode()) {
                GroundPromotionMallRowListener groundPromotionMallRowListener = new GroundPromotionMallRowListener();
                EasyExcel.read(inputStream, GroundPromotionMallRowBO.class, groundPromotionMallRowListener).sheet(0).headRowNumber(1).doRead();
                return groundPromotionMallRowListener.getCachedDataList();
            }else if(poiType == GroundPromotionScopeTypeEnum.STANDARD_PRODUCT.getCode()){
                GroundPromotionStandardProductRowListener groundPromotionStandardProductRowListener = new GroundPromotionStandardProductRowListener();
                EasyExcel.read(inputStream, GroundPromotionStandardProductRowBO.class, groundPromotionStandardProductRowListener).sheet(0).headRowNumber(1).doRead();
                return groundPromotionStandardProductRowListener.getCachedDataList();
            } else {
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("[parseFile] error, exception is", e);
            return Lists.newArrayList();
        }
    }

    public String generateFile(String fileName, int poiType, List<?> groundPromotionProjectRowList) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionProjectExcelUtils.generateFile(java.lang.String,int,java.util.List)");
        try {
            Map<String, List<List<String>>> sheetData = Maps.newHashMap();
            if (poiType == GroundPromotionScopeTypeEnum.SHOP.getCode()) {
                List<List<String>> rowData = Lists.newArrayList();
                rowData.add(Lists.newArrayList("点评门店id",
                        "点评团单id",
                        "导入结果",
                        "失败原因"));
                for (GroundPromotionShopRowBO groundPromotionShopRowBO : (List<GroundPromotionShopRowBO>) groundPromotionProjectRowList) {
                    List<String> cellData = Lists.newArrayList();
                    cellData.add(groundPromotionShopRowBO.getDpShopId());
                    cellData.add(groundPromotionShopRowBO.getDealIdList());
                    cellData.add(ExcelParseResultEnum.fromCode(groundPromotionShopRowBO.getLoadResult()).getDesc());
                    cellData.add(groundPromotionShopRowBO.getFailReason());
                    rowData.add(cellData);
                }
                sheetData.put("result", rowData);
            } else if (poiType == GroundPromotionScopeTypeEnum.STANDARD_PRODUCT.getCode()) {
                List<List<String>> rowData = Lists.newArrayList();
                rowData.add(Lists.newArrayList("点评城市id",
                        "标品商品id",
                        "导入结果",
                        "失败原因"));
                for (GroundPromotionStandardProductRowBO groundPromotionStandardProductRowBO : (List<GroundPromotionStandardProductRowBO>) groundPromotionProjectRowList) {
                    List<String> cellData = Lists.newArrayList();
                    cellData.add(groundPromotionStandardProductRowBO.getCityId());
                    cellData.add(groundPromotionStandardProductRowBO.getDealId());
                    cellData.add(groundPromotionStandardProductRowBO.getLoadResult());
                    cellData.add(groundPromotionStandardProductRowBO.getFailReason());
                    rowData.add(cellData);
                }
                sheetData.put("result", rowData);
            }else {
                List<List<String>> rowData = Lists.newArrayList();
                rowData.add(Lists.newArrayList("点评商场id",
                        "点评门店id",
                        "点评团单id",
                        "导入结果",
                        "失败原因"));
                for (GroundPromotionMallRowBO groundPromotionMallRowBO : (List<GroundPromotionMallRowBO>) groundPromotionProjectRowList) {
                    List<String> cellData = Lists.newArrayList();
                    cellData.add(groundPromotionMallRowBO.getDpMallId());
                    cellData.add(groundPromotionMallRowBO.getDpShopId());
                    cellData.add(groundPromotionMallRowBO.getDealIdList());
                    cellData.add(ExcelParseResultEnum.fromCode(groundPromotionMallRowBO.getLoadResult()).getDesc());
                    cellData.add(groundPromotionMallRowBO.getFailReason());
                    rowData.add(cellData);
                }
                sheetData.put("result", rowData);
            }
            FileBody fileBody = FileBodyBuilder.buildExcelFileBody(fileName, sheetData);
            return fileBody.getUrl();
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".generateFile Error", e);
            Cat.logEvent(getClass().getSimpleName() + ".generateFile Error", e.getMessage());
            return "";
        }
    }

}
