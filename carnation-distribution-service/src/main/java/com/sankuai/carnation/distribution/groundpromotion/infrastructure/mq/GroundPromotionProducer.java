package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IDelayFutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * @author: wuweizhen
 * @Date: 2023/6/9 16:12
 * @Description:
 */
@Component
@Slf4j
public class GroundPromotionProducer implements InitializingBean {

    private IProducerProcessor producer;

   public void asyncSendDelayMsg(GroundEventTypeEnum eventType, Long groundPromotionId, Integer dpCityId, Long delayTime) {
       this.asyncSendDelayMsg(
                Event.builder()
                        .eventType(eventType.getCode())
                        .eventMsg(
                                EventMessage.builder()
                                        .groundPromotionId(groundPromotionId)
                                        .dpCityId(dpCityId)
                                        .build())
                        .build(),
                delayTime);
   }

    public void asyncSendDelayMsg(Event event, Long delayTime) {
        try {
            AsyncDelayProducerResult sendResult = producer.sendAsyncDelayMessage(JSONObject.toJSONString(event), delayTime, new IDelayFutureCallback() {
                @Override
                public void onSuccess(AsyncDelayProducerResult result) {
                    log.error("[GroundPromotionProducer.asyncSendDelayMsg]event is {},result is {}", JSONObject.toJSONString(event), JSONObject.toJSONString(result));
                }

                @Override
                public void onFailure(AsyncDelayProducerResult result) {
                    log.error("[GroundPromotionProducer]发送消息失败:msg={}", event);
                }
            });
        } catch (Exception e) {
            log.error("[GroundPromotionProducer]发送消息失败:msg={}，exception is", event, e);
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.distribution.service");
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "3");
        properties.setProperty(ConsumerConstants.MafkaDelayServerTimeOut, "2000");
        properties.setProperty(ConsumerConstants.MafkaDelayServerConnTimeout, "1000");
        properties.setProperty(ConsumerConstants.PRODUCER_SEND_MODE, "async");
        producer = MafkaClient.buildDelayProduceFactory(properties, "ground_promotion_event");
    }
}