package com.sankuai.carnation.distribution.distributionplan.repository.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPush;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPushQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.domain.model.ProductUniqueUnit;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductPushStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.repository.DistributionProductPushRepository;
import com.sankuai.carnation.distribution.distributionplan.repository.dao.DistributionProductPushMapper;
import com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionProductPushEntity;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class DistributionProductPushRepositoryImpl implements DistributionProductPushRepository {

    @Resource
    private DistributionProductPushMapper distributionProductPushMapper;

    private static final int BATCH_INSERT_SIZE = 200;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAddDistributionProductPush(List<DistributionProductPush> pushDistributionProductList) {
        if (CollectionUtils.isEmpty(pushDistributionProductList)) {
            return 0;
        }

        Set<Long> mtProductIds = Sets.newHashSet();
        Set<Integer> productTypeSet = Sets.newHashSet();
        Set<String> channelSet = Sets.newHashSet();
        pushDistributionProductList.forEach(distributionProductPush -> {
            mtProductIds.add(distributionProductPush.getMtProductId());
            productTypeSet.add(distributionProductPush.getProductType().getCode());
            channelSet.add(distributionProductPush.getChannel().getCode());
        });
        DistributionProductPushQueryRequest request = DistributionProductPushQueryRequest.builder()
                .productIds(Lists.newArrayList(mtProductIds))
                .productTypeList(Lists.newArrayList(productTypeSet))
                .channelList(Lists.newArrayList(channelSet))
                .build();
        List<DistributionProductPushEntity> existEntities = distributionProductPushMapper.queryDistributionProductPush(request);

        pushDistributionProductList = filterEverPushDistributionProduct(pushDistributionProductList, existEntities);
        if (CollectionUtils.isEmpty(pushDistributionProductList)) {
            return 0;
        }

        List<List<DistributionProductPush>> partitionList = Lists.partition(pushDistributionProductList, BATCH_INSERT_SIZE);

        partitionList.forEach(pushList -> {
            List<DistributionProductPushEntity> entities = pushList.stream().map(this::convertDoToEntity).collect(Collectors.toList());
            distributionProductPushMapper.batchInsertDistributionProductPush(entities);
        });
        return pushDistributionProductList.size();
    }

    @Override
    public List<DistributionProductPush> queryDistributionProductPush(DistributionProductPushQueryRequest request) {
        List<DistributionProductPushEntity> entities = distributionProductPushMapper.queryDistributionProductPush(request);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return entities.stream().map(this::convertEntityToDo).collect(Collectors.toList());
    }

    /**
     * 过滤已经投放过的商品
     * @param pushDistributionProductList
     * @param existEntities
     * @return
     */
    private List<DistributionProductPush> filterEverPushDistributionProduct(List<DistributionProductPush> pushDistributionProductList, List<DistributionProductPushEntity> existEntities) {
        if (CollectionUtils.isEmpty(existEntities)) {
            return pushDistributionProductList;
        }

        Set<ProductUniqueUnit> productUniqueUnitSet = existEntities.stream().map(entity -> ProductUniqueUnit.builder()
                .mtProductId(entity.getProductId())
                .productType(entity.getProductType())
                .channel(entity.getChannel())
                .build()).collect(Collectors.toSet());

        return pushDistributionProductList.stream().filter(product-> {
            ProductUniqueUnit productUniqueUnit = ProductUniqueUnit.builder()
                    .mtProductId(product.getMtProductId())
                    .productType(product.getProductType().getCode())
                    .channel(product.getChannel().getCode())
                    .build();
            return !productUniqueUnitSet.contains(productUniqueUnit);
        }).collect(Collectors.toList());
    }

    @Override
    public boolean batchUpdateDistributionProductPushStatus(List<DistributionProductPush> distributionProductPushes) {
        if (CollectionUtils.isEmpty(distributionProductPushes)) {
            throw new RuntimeException("EmptyDistributionProductPushes");
        }
        List<DistributionProductPushEntity> entities = distributionProductPushes.stream().map(this::convertDoToEntity).collect(Collectors.toList());
        distributionProductPushMapper.batchUpdateDistributionProductPushStatus(entities);
        return true;
    }

    private DistributionProductPush convertEntityToDo(DistributionProductPushEntity entity) {
        DistributionProductPush distributionProduct = new DistributionProductPush();
        distributionProduct.setPushId(entity.getId());
        distributionProduct.setMtProductId(entity.getProductId());
        distributionProduct.setChannel(DistributionBusinessChannelEnum.fromCode(entity.getChannel()));
        distributionProduct.setProductType(ProductTypeEnum.fromCode(entity.getProductType()));
        distributionProduct.setStatus(DistributionProductPushStatusEnum.from(entity.getStatus()));
        distributionProduct.setOnlineTime(entity.getOnlineTime());
        distributionProduct.setOfflineTime(entity.getOfflineTime());
        distributionProduct.setPushResult(entity.getPushResult());
        return distributionProduct;
    }

    private DistributionProductPushEntity convertDoToEntity(DistributionProductPush distributionProduct) {
        DistributionProductPushEntity entity = new DistributionProductPushEntity();
        entity.setId(distributionProduct.getPushId());
        entity.setProductId(distributionProduct.getMtProductId());
        entity.setProductType(distributionProduct.getProductType().getCode());
        entity.setChannel(distributionProduct.getChannel().getCode());
        entity.setStatus(distributionProduct.getStatus().getCode());
        entity.setOnlineTime(distributionProduct.getOnlineTime());
        entity.setOfflineTime(distributionProduct.getOfflineTime());
        entity.setPushResult(distributionProduct.getPushResult());
        return entity;
    }
}
