package com.sankuai.carnation.distribution.distributionplan.repository.dao;

import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanPageQuery;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionPlanMapper {

    /**
     * 新增分销计划
     * @param entity
     * @return
     */
    int insertDistributionPlan(DistributionPlanEntity entity);

    /**
     * 更新分销计划状态
     * @param entity
     * @return
     */
    int updateDistributionPlanStatus(DistributionPlanEntity entity);

    /**
     * 更新分销计划
     * @param entity
     * @return
     */
    int updateDistributionPlan(DistributionPlanEntity entity);

    /**
     * 分页查询分销计划
     * @param pageQuery
     * @param limit
     * @param offset
     * @return
     */
    List<DistributionPlanEntity> pageQueryDistributionPlan(@Param("pageQuery") DistributionPlanPageQuery pageQuery,
                                                           @Param("limit") int limit,
                                                           @Param("offset") int offset);

    /**
     * 查询分销计划
     * @param planId
     * @return
     */
    DistributionPlanEntity loadDistributionPlanByPlanId(@Param("planId")Long planId);

    /**
     * 查询分销计划
     * @param planIds
     * @return
     */
    List<DistributionPlanEntity> queryDistributionPlanByPlanIds(@Param("planIds")List<Long> planIds);

    /**
     * 查询分销计划
     * @param request
     * @return
     */
    List<DistributionPlanEntity> queryDistributionPlanByRequest(DistributionPlanQueryRequest request);

    /**
     * 查询分销计划总量
     * @param pageQuery
     * @return
     */
    long countDistributionPlan(DistributionPlanPageQuery pageQuery);
}