package com.sankuai.carnation.distribution.distributor.repository.db;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: distributor_group_bind
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DistributorGroupBind {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: distributor_group
     *   说明: 分销商id
     */
    private Integer distributorGroup;

    /**
     *   字段: distributor
     *   说明: 分销员id
     */
    private Long distributor;

    /**
     *   字段: status
     *   说明: 状态，1-有效，2-无效
     */
    private Integer status;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;
}