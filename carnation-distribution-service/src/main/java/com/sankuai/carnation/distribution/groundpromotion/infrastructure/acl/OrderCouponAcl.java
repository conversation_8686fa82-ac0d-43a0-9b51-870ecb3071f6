package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.cat.Cat;
import com.dianping.dz.coupon.base.dto.CouponVerifyRecordDTO;
import com.dianping.dz.coupon.base.enums.CouponStatus;
import com.dianping.dz.coupon.query.api.CouponQueryService;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.onlinequery.UnifiedOrderOnlineQueryService;
import com.dianping.pay.unifiedorder.onlinequery.model.QueryListResponse;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderPaymentAmountShareDTO;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.common.acl.UnifiedOrderAclService;
import com.sankuai.carnation.distribution.groundpromotion.exception.GroundPromotionException;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.dianping.dz.coupon.base.dto.CouponDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/21
 * @Description: 泛商品订单券相关服务 注意：userId为统一订单模型上的userId，即dp实id或dp虚id
 */
@Component
@Slf4j
public class OrderCouponAcl {

    @Autowired
    private CouponQueryService couponQueryService;

    @Autowired
    private UnifiedOrderOnlineQueryService unifiedOrderOnlineQueryService;

    @Autowired
    private UnifiedOrderAclService unifiedOrderAclService;

    public int getTotalCouponNum(String unifiedOrderId, long userId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl.getTotalCouponNum(java.lang.String,long)");
        return getTotalCoupon(unifiedOrderId, userId).size();
    }

    public int getTotalUsedCouponNum(String unifiedOrderId, long userId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl.getTotalUsedCouponNum(java.lang.String,long)");
        return getUsedCoupon(unifiedOrderId, userId).size();
    }

    public BigDecimal getTotalUsedCouponMoney(String unifiedOrderId) {
        BigDecimal totalUsedMoney = new BigDecimal(0);
        try {
            UnifiedOrderWithId unifiedOrder = unifiedOrderAclService.getOrder(unifiedOrderId);
            long userId = unifiedOrder.getUserId();
            List<CouponDTO> usedCouponDTOList = getUsedCoupon(unifiedOrderId, userId);
            if (CollectionUtils.isEmpty(usedCouponDTOList)) {
                return totalUsedMoney;
            }
            List<String> usedCouponIdList = usedCouponDTOList.stream()
                    .map(couponDTO -> couponDTO.getCouponId())
                    .map(String::valueOf).collect(Collectors.toList());


            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);

            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError("订单分摊金额查询失败", new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return totalUsedMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            Map<String, BigDecimal> usedCouponMap = amountShareDTOList.stream()
                    .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));

            for (String usedCouponId : usedCouponIdList) {
                if (ProductTypeUtils.isStandard(unifiedOrder.getBizType())) {
                    // todo 标品分摊金额上没有券id，且标品订单只有一张券，此为临时方案，直接取ReceiptAmount
                    totalUsedMoney = totalUsedMoney.add(amountShareDTOList.get(0).getReceiptAmount());
                } else {
                    totalUsedMoney = totalUsedMoney.add(usedCouponMap.get(usedCouponId));
                }
            }
            return totalUsedMoney;
        } catch (Exception e) {
            log.error("[getTotalUsedCouponMoney] error, orderId is {}, exception is", unifiedOrderId, e);
            return totalUsedMoney;
        }
    }

    public BigDecimal getTotalRefundCouponMoney(String unifiedOrderId) {
        BigDecimal totaRefundMoney = new BigDecimal(0);
        try {
            UnifiedOrderWithId unifiedOrder = unifiedOrderAclService.getOrder(unifiedOrderId);
            long userId = unifiedOrder.getUserId();

            List<CouponDTO> refundCouponDTOList = getRefundCoupon(unifiedOrderId, userId);
            if (CollectionUtils.isEmpty(refundCouponDTOList)) {
                return totaRefundMoney;
            }
            List<String> refundCouponIdList = refundCouponDTOList.stream()
                    .map(couponDTO -> couponDTO.getCouponId())
                    .map(String::valueOf).collect(Collectors.toList());


            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);

            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError("订单分摊金额查询失败", new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return totaRefundMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            Map<String, BigDecimal> refundCouponMap = amountShareDTOList.stream()
                    .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));

            for (String refundCouponId : refundCouponIdList) {
                if (ProductTypeUtils.isStandard(unifiedOrder.getBizType())) {
                    // todo 标品分摊金额上没有券id，且标品订单只有一张券，此为临时方案，直接取ReceiptAmount
                    totaRefundMoney = totaRefundMoney.add(amountShareDTOList.get(0).getReceiptAmount());
                } else {
                    totaRefundMoney = totaRefundMoney.add(refundCouponMap.get(refundCouponId));
                }
            }
            return totaRefundMoney;
        } catch (Exception e) {
            log.error("[getTotalRefundCouponMoney] error, orderId is {}, exception is", unifiedOrderId, e);
            return totaRefundMoney;
        }
    }

    public BigDecimal getTotalUsedCouponPayMoney(String unifiedOrderId) {
        BigDecimal totalUsedPayMoney = new BigDecimal(0);
        UnifiedOrderWithId unifiedOrder = unifiedOrderAclService.getOrder(unifiedOrderId);
        long userId = unifiedOrder.getUserId();
        List<CouponDTO> usedCouponDTOList = getUsedCoupon(unifiedOrderId, userId);
        if (CollectionUtils.isEmpty(usedCouponDTOList)) {
            return totalUsedPayMoney;
        }
        List<String> usedCouponIdList = usedCouponDTOList.stream()
                .map(couponDTO -> couponDTO.getCouponId())
                .map(String::valueOf).collect(Collectors.toList());

        QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
        if (CollectionUtils.isEmpty(response.getResultList())) {
            Cat.logError(new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
            return totalUsedPayMoney;
        }
        List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
        Map<String, BigDecimal> usedCouponMap = amountShareDTOList.stream()
                .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));
        Map<String, BigDecimal> totalDiscountMap = amountShareDTOList.stream()
                .collect(Collectors.groupingBy(
                        UnifiedOrderPaymentAmountShareDTO::getReceiptId,
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                dto -> dto.getPlatformAmount().add(dto.getMerchantAmount()),
                                BigDecimal::add
                        )
                ));

        for (String usedCouponId : usedCouponIdList) {
            if (ProductTypeUtils.isStandard(unifiedOrder.getBizType())) {
                // todo 标品分摊金额上没有券id，且标品订单只有一张券，此为临时方案，直接取ReceiptAmount
                totalUsedPayMoney = totalUsedPayMoney.add(usedCouponMap.get("")).subtract(totalDiscountMap.get(""));
            } else {
                totalUsedPayMoney = totalUsedPayMoney.add(usedCouponMap.get(usedCouponId)).subtract(totalDiscountMap.get(usedCouponId));
            }
        }
        return totalUsedPayMoney;
    }

    public List<CouponDTO> getTotalCoupon(String unifiedOrderId, long userId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl.getTotalCoupon(java.lang.String,long)");
        return couponQueryService.findCouponsByOrderIdAndOrderType(unifiedOrderId, 101, userId);
    }

    public List<CouponDTO> getUsedCoupon(String unifiedOrderId, long userId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl.getUsedCoupon(java.lang.String,long)");
        List<CouponDTO> couponDTOList = getTotalCoupon(unifiedOrderId, userId);
        return couponDTOList.stream().filter(couponDTO -> couponDTO.getStatus() == CouponStatus.Used.getValue()).collect(Collectors.toList());
    }

    public List<CouponDTO> getRefundCoupon(String unifiedOrderId, long userId) {
        List<CouponDTO> couponDTOList = getTotalCoupon(unifiedOrderId, userId);
        return couponDTOList.stream().filter(couponDTO -> couponDTO.getStatus() == CouponStatus.RefundSuccess.getValue()
        || couponDTO.getStatus() == CouponStatus.SuperRefund.getValue()).collect(Collectors.toList());
    }

    public List<CouponVerifyRecordDTO> getUsedCouponVerifyRecord(String unifiedOrderId, long userId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl.getUsedCouponVerifyRecord(java.lang.String,long)");
        List<CouponDTO> couponDTOList = getUsedCoupon(unifiedOrderId, userId);
        List<String> couponValues = couponDTOList.stream().map(CouponDTO::getCouponValue).collect(Collectors.toList());
        return couponQueryService.findVerifyRecordByCouponValue(couponValues);
    }

    public Map<Long, CouponDTO> findCouponByIDs(List<Long> couponIds){
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl.findCouponByIDs(java.util.List)");
        List<CouponDTO> couponList = couponQueryService.findCouponsByCouponIds(couponIds);
        if (CollectionUtils.isEmpty(couponList)) {
            return Maps.newHashMap();
        }
        return couponList.stream().collect(Collectors.toMap(CouponDTO::getCouponId, Function.identity(),(v1, v2)->v1));
    }
}
