package com.sankuai.carnation.distribution.distributionplan.acl.model;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ToString
public class ChannelProductReleaseResult {

    private PushProductUnit pushProductUnit;

    private Boolean success;

    private String failMsg;

    public static ChannelProductReleaseResult releaseFailedResult (PushProductUnit pushProductUnit, String failMsg) {
        return ChannelProductReleaseResult.builder()
                .pushProductUnit(pushProductUnit)
                .success(false)
                .failMsg(failMsg)
                .build();
    }

    public static ChannelProductReleaseResult releaseSucceedResult(PushProductUnit pushProductUnit) {
        return ChannelProductReleaseResult.builder()
                .pushProductUnit(pushProductUnit)
                .success(true)
                .failMsg("投放成功")
                .build();
    }
}
