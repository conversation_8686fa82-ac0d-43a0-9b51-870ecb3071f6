package com.sankuai.carnation.distribution.distributioncode.domain.model;

import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributionCodeParseRequest {

    /**
     * 分销码
     */
    private String distributionCode;

    /**
     * 渠道
     */
    private DistributionBusinessChannelEnum channel;

    /**
     * 需要分销人信息
     */
    private Boolean needDistributorInfo;

    /**
     * 需要分销计划信息
     */
    private Boolean needDistributionPlanInfo;
}