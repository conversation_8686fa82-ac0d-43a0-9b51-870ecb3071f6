package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.general.unified.search.api.dealgroupsearch.GeneralDealGroupSearchService;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.BaseSearchOption;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.DealGroupSearchDTO;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.PageOption;
import com.dianping.general.unified.search.api.dealgroupsearch.enums.DealGroupSearchIdPlatformEnum;
import com.dianping.general.unified.search.api.dealgroupsearch.enums.DealGroupStatusEnum;
import com.dianping.general.unified.search.api.dealgroupsearch.request.GeneralDealGroupSearchRequest;
import com.dianping.general.unified.search.api.dealgroupsearch.response.GeneralDealGroupSearchResponse;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.acl.GeneralDealGroupSearchAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealGroupSearchRequest;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralDealGroupSearchResult;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
@Service
@Slf4j
public class GeneralDealGroupSearchAclImpl implements GeneralDealGroupSearchAcl {

    private final static String CAT_TYPE = GeneralDealGroupSearchAcl.class.getSimpleName();

    @Resource
    private GeneralDealGroupSearchService generalDealGroupSearchService;

    @Override
    public GeneralDealGroupSearchResult searchDpDealGroups(DealGroupSearchRequest searchRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "searchDpDealGroups");
        try {
            if (Objects.isNull(searchRequest)) {
                throw new RuntimeException("IllegalParam");
            }
            GeneralDealGroupSearchRequest request = new GeneralDealGroupSearchRequest();
            request.setPlatform(DealGroupSearchIdPlatformEnum.DP);
            BaseSearchOption baseSearchOption = new BaseSearchOption();
            if (Objects.nonNull(searchRequest.getDpShopId()) && searchRequest.getDpShopId() > 0) {
                baseSearchOption.setShopIds(Lists.newArrayList(searchRequest.getDpShopId()));
            }
            if (StringUtils.isNotBlank(searchRequest.getProductName())) {
                baseSearchOption.setDealGroupTitles(Lists.newArrayList(searchRequest.getProductName()));
            }
            if (Objects.nonNull(searchRequest.getProductId()) && searchRequest.getProductId() > 0) {
                baseSearchOption.setDealGroupIds(Lists.newArrayList(searchRequest.getProductId()));
            }
            if (Objects.nonNull(searchRequest.getCustomerId()) && searchRequest.getCustomerId() > 0) {
                baseSearchOption.setPlatformCustomerIds(Lists.newArrayList(searchRequest.getCustomerId()));
            }
            baseSearchOption.setStatusIds(Lists.newArrayList(DealGroupStatusEnum.ONLINE));
            baseSearchOption.setInSale(true);
            PageOption pageOption = new PageOption();
            pageOption.setPageSize(searchRequest.getPageSize());
            pageOption.setPageNo(searchRequest.getPageNo());
            request.setBaseSearchOption(baseSearchOption);
            request.setPageOption(pageOption);
            GeneralDealGroupSearchResponse response = generalDealGroupSearchService.searchDealGroups(request);
            if (Objects.isNull(response)) {
                log.error("GeneralDealGroupSearchAcl.searchDpDealGroups invoke generalDealGroupSearchService return null response, request:{}",
                        JsonUtil.toJson(searchRequest));
                throw new DistributionPlanException("查询团购商品接口返回为空");
            }
            if (!response.isSuccess()) {
                log.error("GeneralDealGroupSearchAcl.searchDpDealGroups invoke generalDealGroupSearchService failed, request:{}, response:{}",
                        JsonUtil.toJson(searchRequest), JsonUtil.toJson(response));
                throw new DistributionPlanException("查询团购商品接口接口返回失败:" + response.getResultMessage());
            }
            //todo 这里商品的接口契约是一定不为空吗？如果没搜到呢，这里看下是不是不需要抛异常
            if (Objects.isNull(response.getResult())) {
                log.error("GeneralDealGroupSearchAcl.searchDpDealGroups invoke generalDealGroupSearchService return null data, request:{}, response:{}",
                        JsonUtil.toJson(searchRequest), JsonUtil.toJson(response));
                throw new DistributionPlanException("查询团购商品接口接口返回data为空");
            }
            List<Long> dealGroupIdList = response.getResult().stream().map(DealGroupSearchDTO::getDealGroupId).collect(Collectors.toList());
            transaction.setSuccessStatus();
            return GeneralDealGroupSearchResult.builder().productIdList(dealGroupIdList).totalHits(response.getTotalHits()).build();
        } catch (Exception e) {
            log.error("GeneralDealGroupSearchAcl.searchDpDealGroups failed, request:{}", JsonUtil.toJson(searchRequest), e);
            transaction.setStatus(e);
            return GeneralDealGroupSearchResult.emptyResult();
        } finally {
            transaction.complete();
        }
    }
}