package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionBatchRowStrBo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: chenhaoyang02
 * @date: 2023/7/24
 */
@Slf4j
public class GroundPromotionBatchRowListener implements ReadListener<GroundPromotionBatchRowStrBo> {

    @Getter
    private List<GroundPromotionBatchRowStrBo> cachedDataList = Lists.newArrayList();

    @Override
    public void invoke(GroundPromotionBatchRowStrBo groundPromotionBatchRowStrBo, AnalysisContext analysisContext) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionBatchRowListener.invoke(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionBatchRowStrBo,com.alibaba.excel.context.AnalysisContext)");
        if (!checkFormat(groundPromotionBatchRowStrBo)) {
            groundPromotionBatchRowStrBo.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
            groundPromotionBatchRowStrBo.setFailReason("上传格式不正确");
        }
        if (groundPromotionBatchRowStrBo.getMisIdList() == null) {
            groundPromotionBatchRowStrBo.setMisIdList("");
        }
        cachedDataList.add(groundPromotionBatchRowStrBo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionBatchRowListener.doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext)");
    }

    private boolean checkFormat(GroundPromotionBatchRowStrBo groundPromotionBatchRowStrBo) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionBatchRowListener.checkFormat(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionBatchRowStrBo)");
        try {
            Integer.parseInt(groundPromotionBatchRowStrBo.getDpCityId());
            Long.parseLong(groundPromotionBatchRowStrBo.getDpShopId());
            Long.parseLong(groundPromotionBatchRowStrBo.getDpDealId());
            if (groundPromotionBatchRowStrBo.getMisIdList() != null) {
                Lists.newArrayList(groundPromotionBatchRowStrBo.getMisIdList().split(","));
            }
        }catch (Exception e) {
            log.error("GroundPromotionBatchRowBo param is invalid, param is {}, exception is {}", JSONObject.toJSON(groundPromotionBatchRowStrBo), e);
            return false;
        }
        return true;
    }
}
