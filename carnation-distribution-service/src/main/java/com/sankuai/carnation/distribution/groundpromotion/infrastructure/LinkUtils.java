package com.sankuai.carnation.distribution.groundpromotion.infrastructure;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;

/**
 * @author: wuweizhen
 * @Date: 2023/6/13 11:04
 * @Description:
 */
@Slf4j
public class LinkUtils {

    private static final String TEST_DOMAIN = "https://mdz.dzu.test.sankuai.com/";
    private static final String PROD_DOMAIN = "https://mdz.sankuai.com/";

    private static final String MT_TEST_DOMAIN = "https://test-g.meituan.com/";

    private static final String MT_PROD_DOMAIN = "https://g.meituan.com/";

    private static final String SALE_CONFIRM_LINK = "dzcsr/biz-growth/tuituile/prompt-sale.html?promoId=&{promoId}";

    private static final String SALE_MATERIAL_LINK = "dzcsr/biz-growth/tuituile/prompt-material.html?promoId=%s";

    private static final String CITY_MANAGER_LINK = "dzcsr/biz-growth/tuituile/prompt-manager.html?promoId=%s&tabId=%s&titlebarVisible=0";

    private static final String LANDING_PAGE_LINK = "arche/dzbeauty/biz-growth/tuituile/group-list.html?groundPromotionCode=%s&dpShopId=%s&clientType=%s&source=1";

    private static final String POINT_LANDING_PAGE_LINK = "arche/dzbeauty/biz-growth/tuituile/group-list.html?groundPromotionCode=%s&pointId=%s&showType=%s&clientType=%s&source=1";

    private static final String PART_TIME_LANDING_PAGE_LINK = "arche/dzbeauty/biz-growth/tuituile/group-list.html?groundPromotionCode=%s&dpShopId=%s&clientType=%s&source=2&bizLine=%s&templateId=%s";

    private static final String OPERATOR_CONFIRM_LINK = "campaign-ground/ground/manage/product-manage.html?groundPromotionId=%s";

    private static final String LANDING_TRANSFER_LINK = "qrcode/transfer?url=%s&f_openId=1&f_token=1";

    public static String buildLandingPageLink(String distributionParam, long shopId, int clientType) {
        if (Environment.isTestEnv()) {
            String preLandingUrl = MT_TEST_DOMAIN + String.format(LANDING_PAGE_LINK, distributionParam, shopId, clientType);
            String encoderUrl = getUrlEncoder(preLandingUrl);
            if (StringUtils.isEmpty(encoderUrl)) {
                return preLandingUrl;
            }
            return MT_TEST_DOMAIN + String.format(LANDING_TRANSFER_LINK, getUrlEncoder(preLandingUrl));
        } else {
            String preLandingUrl =  MT_PROD_DOMAIN + String.format(LANDING_PAGE_LINK, distributionParam, shopId, clientType);
            String encoderUrl = getUrlEncoder(preLandingUrl);
            if (StringUtils.isEmpty(encoderUrl)) {
                return preLandingUrl;
            }
            return MT_PROD_DOMAIN + String.format(LANDING_TRANSFER_LINK, getUrlEncoder(preLandingUrl));
        }

    }

    public static String buildPointLandingPageLink(String distributionParam, long pointId, int showType, int clientType) {
        if (Environment.isTestEnv()) {
            String preLandingUrl = MT_TEST_DOMAIN + String.format(POINT_LANDING_PAGE_LINK, distributionParam, pointId, showType, clientType);
            String encoderUrl = getUrlEncoder(preLandingUrl);
            if (StringUtils.isEmpty(encoderUrl)) {
                return preLandingUrl;
            }
            return MT_TEST_DOMAIN + String.format(LANDING_TRANSFER_LINK, getUrlEncoder(preLandingUrl));
        } else {
            String preLandingUrl =  MT_PROD_DOMAIN + String.format(POINT_LANDING_PAGE_LINK, distributionParam, pointId, showType, clientType);
            String encoderUrl = getUrlEncoder(preLandingUrl);
            if (StringUtils.isEmpty(encoderUrl)) {
                return preLandingUrl;
            }
            return MT_PROD_DOMAIN + String.format(LANDING_TRANSFER_LINK, getUrlEncoder(preLandingUrl));
        }
    }

    public static String buildPartTimeLandingPageLink(String distributionParam, long shopId, int clientType, int bizLine, long templateId) {
        if (Environment.isTestEnv()) {
            String preLandingUrl = MT_TEST_DOMAIN + String.format(PART_TIME_LANDING_PAGE_LINK, distributionParam, shopId, clientType, bizLine, templateId);
            String encoderUrl = getUrlEncoder(preLandingUrl);
            if (StringUtils.isEmpty(encoderUrl)) {
                return preLandingUrl;
            }
            return MT_TEST_DOMAIN + String.format(LANDING_TRANSFER_LINK, getUrlEncoder(preLandingUrl));
        } else {
            String preLandingUrl =  MT_PROD_DOMAIN + String.format(PART_TIME_LANDING_PAGE_LINK, distributionParam, shopId, clientType, bizLine, templateId);
            String encoderUrl = getUrlEncoder(preLandingUrl);
            if (StringUtils.isEmpty(encoderUrl)) {
                return preLandingUrl;
            }
            return MT_PROD_DOMAIN + String.format(LANDING_TRANSFER_LINK, getUrlEncoder(preLandingUrl));
        }
    }

    public static String buildOperatorPageLik(long promoId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.LinkUtils.buildOperatorPageLik(long)");
        if (Environment.isTestEnv()) {
            return TEST_DOMAIN + String.format(OPERATOR_CONFIRM_LINK, promoId);
        }
        return PROD_DOMAIN + String.format(OPERATOR_CONFIRM_LINK, promoId);
    }

    public static String buildCityManagerLink(long promoId, int tabId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.LinkUtils.buildCityManagerLink(long,int)");
        if (Environment.isTestEnv()) {
            return MT_TEST_DOMAIN + String.format(CITY_MANAGER_LINK, promoId, tabId);
        }
        return MT_PROD_DOMAIN + String.format(CITY_MANAGER_LINK, promoId, tabId);
    }

    public static String buildSaleMaterialLink(long promoId) {
        if (Environment.isTestEnv()) {
            return MT_TEST_DOMAIN + String.format(SALE_MATERIAL_LINK, promoId);
        }
        return MT_PROD_DOMAIN + String.format(SALE_MATERIAL_LINK, promoId);
    }

    public static String buildSaleConfirmLink(long promoId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.LinkUtils.buildSaleConfirmLink(long)");
        if (Environment.isTestEnv()) {
            return MT_TEST_DOMAIN + (SALE_CONFIRM_LINK.replace("&{promoId}", String.valueOf(promoId)));
        }
        return MT_PROD_DOMAIN + (SALE_CONFIRM_LINK.replace("&{promoId}", String.valueOf(promoId)));
    }

    private static String getUrlEncoder(String url) {
        try {
            if (StringUtils.isEmpty(url)) {
                return StringUtils.EMPTY;
            }
            return URLEncoder.encode(url, "UTF-8");
        } catch (Exception e) {
            log.error("[getUrlEncoder] error, url: {}, exception is ", url, e);
            return StringUtils.EMPTY;
        }
    }
}