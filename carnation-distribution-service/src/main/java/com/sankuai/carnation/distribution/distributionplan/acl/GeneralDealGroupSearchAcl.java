package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.distributionplan.acl.model.DealGroupSearchRequest;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralDealGroupSearchResult;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
public interface GeneralDealGroupSearchAcl {

    /**
     * 根据条件搜索点评商品
     * @param searchRequest 条件入参
     * @return 商品ID集和数据总数
     */
    GeneralDealGroupSearchResult searchDpDealGroups(DealGroupSearchRequest searchRequest);
}