package com.sankuai.carnation.distribution.distributioncode.acl;

import com.sankuai.carnation.distribution.distributioncode.acl.model.BeautyContentProductDTO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface BeautyContentProductAcl {

    /**
     * 解析渠道商品url
     * @param url
     * @param channel
     * @return
     */
    Optional<BeautyContentProductDTO> parseChannelProductUrl(String url, DistributionBusinessChannelEnum channel);
}
