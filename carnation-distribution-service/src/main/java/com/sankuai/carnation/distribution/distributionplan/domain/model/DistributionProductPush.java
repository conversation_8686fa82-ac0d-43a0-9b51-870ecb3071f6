package com.sankuai.carnation.distribution.distributionplan.domain.model;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.dto.ProductPushRequest;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductPushStatusEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DistributionProductPush {

    private Long pushId;

    private Long mtProductId;

    private ProductTypeEnum productType;

    private DistributionBusinessChannelEnum channel;

    private DistributionProductPushStatusEnum status;

    private Date onlineTime;

    private Date offlineTime;

    private String pushResult;

    public static DistributionProductPush initByProductUniqueUnit(ProductUniqueUnit productUniqueUnit) {
        return DistributionProductPush.builder()
                .mtProductId(productUniqueUnit.getMtProductId())
                .productType(ProductTypeEnum.fromCode(productUniqueUnit.getProductType()))
                .channel(DistributionBusinessChannelEnum.fromCode(productUniqueUnit.getChannel()))
                .status(DistributionProductPushStatusEnum.INITIALIZE)
                .pushResult("")
                .build();
    }

    /**
     * 批量初始化增量的商品
     * @param pushRequest 本次商品推送请求
     * @param existDistributionProductPushes 已经推送过的商品列表
     * @return 未曾推送过的商品列表
     */
    public static List<DistributionProductPush> batchInitIncrementalProduct(ProductPushRequest pushRequest, List<Long> pushProductIds, List<DistributionProductPush> existDistributionProductPushes) {
        Set<Long> existProductIds = existDistributionProductPushes.stream().map(DistributionProductPush::getMtProductId).collect(Collectors.toSet());

        ProductTypeEnum productType = ProductTypeEnum.fromCode(pushRequest.getProductType());
        DistributionBusinessChannelEnum channel = DistributionBusinessChannelEnum.fromCode(pushRequest.getChannel());
        List<DistributionProductPush> distributionProductPushes = Lists.newArrayList();
        pushProductIds.forEach(productId -> {
            if (existProductIds.contains(productId)) {
                return;
            }
            DistributionProductPush distributionProductPush = DistributionProductPush.builder()
                    .mtProductId(productId)
                    .channel(channel)
                    .productType(productType)
                    .status(DistributionProductPushStatusEnum.INITIALIZE)
                    .pushResult("")
                    .build();
            distributionProductPushes.add(distributionProductPush);
        });

        return distributionProductPushes;
    }
}