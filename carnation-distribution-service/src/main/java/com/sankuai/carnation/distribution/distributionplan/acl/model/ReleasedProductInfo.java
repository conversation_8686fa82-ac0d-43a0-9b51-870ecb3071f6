package com.sankuai.carnation.distribution.distributionplan.acl.model;

import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductReleaseStatusEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@ToString
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReleasedProductInfo {
    /**
     * 美团商品id
     */
    private long mtProductId;

    /**
     * 三方商品id
     */
    private long thirdPartyProductId;

    /**
     * 渠道id
     * 从渠道转换而来 https://km.sankuai.com/collabpage/1727149819?from=citadel_collabpage
     */
    private DistributionBusinessChannelEnum channel;

    /**
     * 商品类型
     * 从ReleaseProductTypeEnum映射过来
     */
    private ProductTypeEnum productType;

    /**
     * 推送结果
     * 从MountStatusEnum、ReleaseStatusEnum映射过来的
     */
    private DistributionProductReleaseStatusEnum pushResult;
}
