package com.sankuai.carnation.distribution.product.bargain.medical.domain.product.maker;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.dianping.tgc.enums.CouponValueTypeEnum;
import com.dianping.tgc.enums.FullCutAmountType;
import com.dianping.tgc.enums.TGCBizTypeEnum;
import com.dianping.tgc.open.entity.ActivityDTO;
import com.dianping.tgc.open.entity.BatchQueryActivityResponseDTO;
import com.dianping.tgc.open.entity.CouponDTO;
import com.dianping.tgc.open.entity.CouponDTOInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.meituan.medicine.common.exception.utils.ExceptionUtils;
import com.sankuai.carnation.distribution.common.enums.AttrValueTypeEnum;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.common.product.PluralProductDTO;
import com.sankuai.carnation.distribution.distributor.domain.ConsultantAccountService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorChannelRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorGroupRootService;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.ConsultantAccountBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributionChannelBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.intention.domain.code.IntentionCodeDomainService;
import com.sankuai.carnation.distribution.intention.domain.code.bo.IntentionCodeBO;
import com.sankuai.carnation.distribution.intention.domain.coupon.bargain.medical.MedicalBargainCouponIssueRootService;
import com.sankuai.carnation.distribution.intention.enums.IntentionCodeTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionProductTypeEnum;
import com.sankuai.carnation.distribution.medical.bargain.contract.coupon.acl.CouponActivityAclService;
import com.sankuai.carnation.distribution.medical.bargain.contract.coupon.bo.MedicalBargainContractCouponBO;
import com.sankuai.carnation.distribution.medical.bargain.contract.coupon.domain.BargainContractCouponDomainService;
import com.sankuai.carnation.distribution.medical.bargain.contract.coupon.exceptions.BargainCouponException;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.PluralProductBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductDetailBO;
import com.sankuai.carnation.distribution.product.bargain.medical.domain.bo.MedicalBargainProductInitBO;
import com.sankuai.carnation.distribution.product.bargain.medical.domain.product.maker.checker.BargainMakerParamChecker;
import com.sankuai.carnation.distribution.product.bargain.medical.domain.product.maker.decorator.BargainSaleUnitDecorator;
import com.sankuai.carnation.distribution.product.bargain.medical.dto.MedicalBargainProductInitDTO;
import com.sankuai.carnation.distribution.product.bargain.medical.exceptions.MedicalBargainException;
import com.sankuai.carnation.distribution.product.bargain.medical.utils.MedicalBargainProductConstants;
import com.sankuai.carnation.distribution.product.v2.domain.ProductItemDomainService;
import com.sankuai.carnation.distribution.product.v2.domain.bo.ProductItemBO;
import com.sankuai.carnation.distribution.product.v2.exceptions.ProductItemException;
import com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemAttr;
import com.sankuai.carnation.distribution.product.v2.utils.ProductItemAttrBuilder;
import com.sankuai.sjst.ecom.epassportdpadapter.client.model.BizAccountFields;
import com.sankuai.sjst.ecom.epassportdpadapter.client.model.GetBizAccountResp;
import com.sankuai.sjst.ecom.epassportdpadapter.client.model.GetDpAccountIdByIDReq;
import com.sankuai.sjst.ecom.epassportdpadapter.client.service.BizAccountDpAdapterThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2022/09/30
 **/
@Component
@Slf4j
public class MedicalBargainProductMaker implements InitializingBean {

    private final ThreadPoolExecutor productInitDecoratePool = new ThreadPoolExecutor(30, 60, 1, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(100), new NamedThreadFactory(getClass().getSimpleName() + ".ProductInitDecoratePool"), new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private DistributorRootService distributorRootService;

    @Autowired
    private DistributorChannelRootService channelRootService;

    @Autowired
    private DistributorGroupRootService groupRootService;

    @Autowired
    private BargainSaleUnitDecorator bargainSaleUnitDecorator;

    @Autowired
    private BargainMakerParamChecker makerParamChecker;

    @Autowired
    private ProductItemDomainService productItemDomainService;

    @Autowired
    private IntentionCodeDomainService intentionCodeDomainService;

    @Autowired
    private MedicalBargainCouponIssueRootService couponIssueService;

    @Autowired
    private CouponActivityAclService couponActivityAclService;

    @Autowired
    private ConsultantAccountService consultantAccountService;

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private BargainContractCouponDomainService contractCouponDomainService;

    @Autowired
    private BizAccountDpAdapterThriftService.Iface bizAccountApAdapterService;

    @Transactional(rollbackFor = Exception.class)
    public long createBargainProduct(MedicalBargainProductInitDTO productInitDTO) {
        checkProductInitParam(productInitDTO);
        MedicalBargainProductInitBO productInitBO = dto2bo(productInitDTO);
        productInitBO = fulfillMedicalBargainProductInitBO(productInitBO);
        checkProductInitEntireInfo(productInitBO);
        fulfillProductCouponInfo(productInitBO);
        checkCouponCouldIssue(productInitBO);
        long bargainProductId = createMedicalBargainProduct(productInitBO);
        createIntentionCode(productInitBO);
        return bargainProductId;
    }

    /**
     * 校验创建入参设置值是否合法
     */
    private void checkProductInitParam(MedicalBargainProductInitDTO productInitDTO) {
        if (productInitDTO == null) {
            throw new MedicalBargainException("创建数据为空");
        }
        if (productInitDTO.getStartTime() == null || productInitDTO.getEndTime() == null) {
            throw new MedicalBargainException("商品优惠的有效时间未设置");
        }
        if (productInitDTO.getEndTime().before(productInitDTO.getStartTime())) {
            throw new MedicalBargainException("商品优惠结束时间早于开始时间");
        }
        if (productInitDTO.getEndTime().before(Calendar.getInstance().getTime())) {
            throw new MedicalBargainException("商品优惠已结束");
        }
        if (productInitDTO.getSalePrice() == null) {
            throw new MedicalBargainException("商品优惠金额未设置");
        }
        if (productInitDTO.getSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new MedicalBargainException("商品目标售卖价需大于0");
        }
        if (productInitDTO.getDistributorId() <= 0) {
            throw new MedicalBargainException("咨询师未设置");
        }
        if (CollectionUtils.isEmpty(productInitDTO.getProductList())) {
            throw new MedicalBargainException("售卖商品未设置");
        }
        List<Integer> legalProductTypeList = getLegalProductType();
        List<PluralProductDTO> notLegalList = productInitDTO.getProductList().stream()
                .filter(product -> product == null
                        || !legalProductTypeList.contains(product.getProductType())
                        || product.getProductId() <= 0L
                        || product.getAmount() <= 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notLegalList)) {
            throw new MedicalBargainException("尚未支持该类型商品");
        }
    }

    /**
     * 检查商品创建的基础信息
     */
    private void checkProductInitEntireInfo(MedicalBargainProductInitBO productInitBO) {
        checkProductInitDistributor(productInitBO);
        checkProductInitConsultantAccount(productInitBO);
        checkProductInitPlatformProduct(productInitBO);
        checkProductInitOwner(productInitBO);
        checkDiscountSeparation(productInitBO);
    }

    private void checkDiscountSeparation(MedicalBargainProductInitBO productInitBO) {
        BigDecimal minimalGap = BigDecimal.valueOf(0.02);
        BigDecimal totalPrice = productInitBO.getProductTotalPrice();
        BigDecimal discountAmount = totalPrice.subtract(productInitBO.getSalePrice());
        if (discountAmount.compareTo(minimalGap) <= 0) {
            return;
        }
        productInitBO.getProductList().forEach(product -> {
            BigDecimal salePrice = product.getProductDetail().getSalePrice();
            BigDecimal discount = salePrice.divide(totalPrice, 5, RoundingMode.HALF_UP).multiply(discountAmount);
            if (discount.compareTo(minimalGap) <= 0) {
                throw new MedicalBargainException("选择的商品原价相差超过10倍，且优惠金额过低，未能成功生单，请分开生单或降低支付金额");
            }
        });
    }

    private void checkProductInitDistributor(MedicalBargainProductInitBO productInitBO) {
        if (productInitBO.getDistributor() == null || productInitBO.getDistributor().getStatus() != DistributionStatusEnum.VALID.getCode()) {
            throw new MedicalBargainException("咨询师已下线");
        }
        if (productInitBO.getDistributor().getDistributorGroupId() == null || productInitBO.getDistributor().getDistributorGroupId() <= 0) {
            throw new MedicalBargainException("咨询师已解绑");
        }
        if (productInitBO.getChannel() == null || productInitBO.getChannel().getStatus() != DistributionStatusEnum.VALID.getCode()) {
            throw new MedicalBargainException("咨询师业务渠道已下线");
        }
        if (!productInitBO.getChannel().getCode().equalsIgnoreCase(MedicalBargainProductConstants.DISTRIBUTOR_CHANNEL)) {
            throw new MedicalBargainException("尚未获得咨询师权限");
        }
    }

    private void checkProductInitConsultantAccount(MedicalBargainProductInitBO productInitBO) {
        if (productInitBO.getConsultantAccount() == null || productInitBO.getConsultantAccount().getStatus() != DistributionStatusEnum.VALID.getCode()) {
            throw new MedicalBargainException("咨询师当前不在线");
        }
        if (productInitBO.getConsultantAccount().getShopId() == null || productInitBO.getConsultantAccount().getShopId() <= 0L) {
            throw new MedicalBargainException("咨询师未绑定门店");
        }
    }

    private void checkProductInitPlatformProduct(MedicalBargainProductInitBO productInitBO) {
        if (CollectionUtils.isEmpty(productInitBO.getProductList())) {
            throw new MedicalBargainException("未找到关联商品");
        }
        if (productInitBO.getProductTotalPrice() == null || productInitBO.getProductTotalPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new MedicalBargainException("商品总价太低");
        }
        if (productInitBO.getProductTotalPrice().compareTo(productInitBO.getSalePrice()) < 0) {
            throw new MedicalBargainException("优惠价格不能小于原价");
        }
        Long dpShopId = shopMapperService.mt2dp(productInitBO.getConsultantAccount().getShopId());
        if (dpShopId == null || dpShopId <= 0L) {
            throw new MedicalBargainException("未找到咨询师所属关联门店");
        }
        List<String> failReasonList = productInitBO.getProductList().stream()
                .map(product -> {
                    ProductDetailBO productDetail = product.getProductDetail();
                    String productName = Optional.ofNullable(productDetail).map(ProductDetailBO::getProductName).orElse(String.valueOf(product.getProductId()));
                    if (productDetail == null || productDetail.getStatus() != DistributionStatusEnum.VALID.getCode()) {
                        return String.format("商品（%s）尚未发布", productName);
                    }
                    if (productDetail.getSalePrice() == null || productDetail.getSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
                        return String.format("商品（%s）价格过低", productName);
                    }
                    if ((productDetail.getSaleStartTime() != null && productDetail.getSaleStartTime().after(productInitBO.getStartTime()))
                            || (productDetail.getSaleEndTime() != null && productDetail.getSaleEndTime().before(productInitBO.getEndTime()))) {
                        return String.format("商品（%s）有效期超过原始商品售卖时间", productName);
                    }
                    if (productDetail.getProductId() <= 0L) {
                        return "商品id不合法";
                    }
                    if (productDetail.getFirstSkuId() <= 0L) {
                        return String.format("商品（%s）未找到可售卖品", productName);
                    }
                    if (!productDetail.getShopIdList().contains(dpShopId)) {
                        return String.format("您暂没有权限推荐该商品（%s）", productName);
                    }
                    return null;
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(failReasonList)) {
            throw new MedicalBargainException(Joiner.on(";").join(failReasonList));
        }
        if (!makerParamChecker.checkParam(productInitBO)) {
            throw new MedicalBargainException("推广商品业务参数校验失败");
        }
    }

    private void checkProductInitOwner(MedicalBargainProductInitBO productInitBO) {
        if (productInitBO.getCustomerId() <= 0L) {
            throw new MedicalBargainException("推广原始商品的所属客户未找到");
        }
    }

    private MedicalBargainProductInitBO dto2bo(MedicalBargainProductInitDTO productInitDTO) {
        return MedicalBargainProductInitBO.builder()
                .productList(productDto2BoList(productInitDTO))
                .startTime(productInitDTO.getStartTime())
                .endTime(productInitDTO.getEndTime())
                .noStockLimit(productInitDTO.isNoStockLimit())
                .distributorId(productInitDTO.getDistributorId())
                .salePrice(productInitDTO.getSalePrice())
                .build();
    }

    private List<PluralProductBO> productDto2BoList(MedicalBargainProductInitDTO productInitDTO) {
        return productInitDTO.getProductList().stream()
                .map(productDTO -> new PluralProductBO(
                        productDTO.getProductType(),
                        productDTO.getProductId(),
                        productDTO.getAmount(),
                        Optional.of(productDTO.getSkuId()).filter(id -> id > 0).orElse(null))
                )
                .collect(Collectors.toList());
    }

    /**
     * 填充创建医美分销商品的信息
     * 包括：底层商品，分销人，商品对应customer
     */
    private MedicalBargainProductInitBO fulfillMedicalBargainProductInitBO(MedicalBargainProductInitBO baseInfo) {
        CompletableFuture<MedicalBargainProductInitBO> distributorDecorator = CompletableFuture.supplyAsync(() -> {
            try {
                return decorateDistributor(baseInfo);
            } catch (Exception e) {
                ExceptionUtils.logError(getClass().getSimpleName() + ".fulfillMedicalBargainProduct.distributor", e, baseInfo.getDistributorId());
                return baseInfo;
            }
        }, productInitDecoratePool);
        CompletableFuture<MedicalBargainProductInitBO> consultantAccountDecorator = CompletableFuture.supplyAsync(() -> {
            try {
                return decoratorConsultantAccount(baseInfo);
            } catch (Exception e) {
                ExceptionUtils.logError(getClass().getSimpleName() + ".fulfillMedicalBargainProduct.consultantAccount", e, baseInfo.getDistributorId());
                return baseInfo;
            }
        }, productInitDecoratePool);
        CompletableFuture<MedicalBargainProductInitBO> platformProductDecorator = CompletableFuture.supplyAsync(() -> {
            try {
                return bargainSaleUnitDecorator.decoratePlatformProduct(baseInfo);
            } catch (Exception e) {
                ExceptionUtils.logError(getClass().getSimpleName() + ".fulfillMedicalBargainProduct.platformProduct", e, baseInfo.getProductList());
                return baseInfo;
            }
        }, productInitDecoratePool);
        CompletableFuture<MedicalBargainProductInitBO> productOwnerDecorator = CompletableFuture.supplyAsync(() -> {
            try {
                return bargainSaleUnitDecorator.decorateProductOwner(baseInfo);
            } catch (Exception e) {
                ExceptionUtils.logError(getClass().getSimpleName() + ".fulfillMedicalBargainProduct.productOwner", e, baseInfo.getProductList());
                return baseInfo;
            }
        }, productInitDecoratePool);

        return CompletableFuture.allOf(distributorDecorator, consultantAccountDecorator, platformProductDecorator, productOwnerDecorator).thenApply(
                val -> {
                    MedicalBargainProductInitBO distributor = distributorDecorator.join();
                    MedicalBargainProductInitBO consultantAccount = consultantAccountDecorator.join();
                    MedicalBargainProductInitBO platformProduct = platformProductDecorator.join();
                    MedicalBargainProductInitBO productOwner = productOwnerDecorator.join();
                    return MedicalBargainProductInitBO.builder()
                            .startTime(baseInfo.getStartTime())
                            .endTime(baseInfo.getEndTime())
                            .noStockLimit(baseInfo.isNoStockLimit())
                            .productList(platformProduct.getProductList())
                            .distributorId(baseInfo.getDistributorId())
                            .salePrice(baseInfo.getSalePrice())
                            .productTotalPrice(platformProduct.getProductTotalPrice())
                            .distributor(distributor.getDistributor())
                            .consultantAccount(consultantAccount.getConsultantAccount())
                            .channel(distributor.getChannel())
                            .customerId(productOwner.getCustomerId())
                            .build();
                }
        ).join();
    }

    private MedicalBargainProductInitBO fulfillProductCouponInfo(MedicalBargainProductInitBO productInitBO) {
        try {
            BatchQueryActivityResponseDTO couponActivityResponse = couponActivityAclService.queryCouponActivityByCustomer((int) productInitBO.getCustomerId());
            CouponDTOInfo couponInfo = pickUpCoupon(productInitBO, couponActivityResponse);
            if (couponInfo == null) {
                List<MedicalBargainContractCouponBO> couponList = createDistributorGroupCoupon(productInitBO);
                if (CollectionUtils.isEmpty(couponList)) {
                    throw new BargainCouponException("创建订单失败，尚未配置渠道优惠券");
                }
                List<Integer> tgcBizType = getProductTypeList(productInitBO);
                MedicalBargainContractCouponBO contractCoupon = couponList.stream()
                        .filter(Objects::nonNull)
                        .filter(coupon -> coupon.getAccountId() != null && coupon.getAccountId().equals(productInitBO.getCustomerId()))
                        .filter(coupon -> tgcBizType.contains(coupon.getTgcBizType()))
                        .findFirst()
                        .orElse(null);
                if (contractCoupon == null) {
                    throw new BargainCouponException("创建订单失败，尚未配置渠道优惠券");
                }
                productInitBO.setDpCouponId(contractCoupon.getDpCouponId());
                productInitBO.setMtCouponId(contractCoupon.getMtCouponId());
            } else {
                productInitBO.setDpCouponId(Optional.of(couponInfo)
                        .filter(couponDTOInfo -> couponDTOInfo.getDpCouponDTO() != null && couponDTOInfo.getDpCouponDTO().getCouponValueType() == CouponValueTypeEnum.DYNAMIC_COUPON.getCode())
                        .map(CouponDTOInfo::getDpCouponDTO)
                        .map(CouponDTO::getCouponGroupID)
                        .map(String::valueOf)
                        .orElse(null));
                productInitBO.setMtCouponId(Optional.of(couponInfo)
                        .filter(couponDTOInfo -> couponDTOInfo.getMtCouponDTO() != null && couponDTOInfo.getMtCouponDTO().getCouponValueType() == CouponValueTypeEnum.DYNAMIC_COUPON.getCode())
                        .map(CouponDTOInfo::getMtCouponDTO)
                        .map(CouponDTO::getCouponGroupID)
                        .map(String::valueOf)
                        .orElse(null));
            }
            return productInitBO;
        } catch (BargainCouponException e) {
            throw new MedicalBargainException(e);
        }
    }

    private List<MedicalBargainContractCouponBO> createDistributorGroupCoupon(MedicalBargainProductInitBO productInitBO) {
        String createResult = "Success";
        try {
            long customerId = productInitBO.getCustomerId();
            long dpAccountId = 0L;
            if (productInitBO.getConsultantAccount().getAccountId() != null) {
                GetDpAccountIdByIDReq req = new GetDpAccountIdByIDReq();
                req.setId(productInitBO.getConsultantAccount().getAccountId().intValue());
                BizAccountFields fields = new BizAccountFields();
                fields.setDpID(true);
                fields.setId(true);
                req.setFields(fields);
                GetBizAccountResp resp = bizAccountApAdapterService.getDpAccountIdByID(req);
                if (resp != null && resp.getStatus() != null && resp.getStatus().getCode() == 0 && resp.getBizAccount() != null) {
                    dpAccountId = resp.getBizAccount().getDpID();
                }
            }
            return contractCouponDomainService.checkAndRecordContractCoupon(customerId, dpAccountId, getContractAncillary());
        } catch (Exception e) {
            log.error("[MedicalBargainProductMaker.createDistributorGroupCoupon] customerId: {}, distributorId: {}", productInitBO.getCustomerId(), productInitBO.getDistributorId(), e);
            createResult = "Fail." + e.getMessage();
            throw new BargainCouponException("创建订单失败，尚未配置渠道优惠券");
        } finally {
            Cat.logEvent("CreateBargainProduct.CreateCoupon", createResult);
        }
    }

    private int getContractAncillary() {
        return Lion.getInt("carnation-campaign-web", "carnation-campaign-web.consultant.customer.contract.type");
    }

    private CouponDTOInfo pickUpCoupon(MedicalBargainProductInitBO productInitBO, BatchQueryActivityResponseDTO response) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.product.bargain.medical.domain.product.maker.MedicalBargainProductMaker.pickUpCoupon(com.sankuai.carnation.distribution.product.bargain.medical.domain.bo.MedicalBargainProductInitBO,com.dianping.tgc.open.entity.BatchQueryActivityResponseDTO)");
        if (response == null || MapUtils.isEmpty(response.getActivityInfoMap())) {
            return null;
        }
        List<Integer> needTgcBizTypeList = getProductTypeList(productInitBO);
        Date now = Calendar.getInstance().getTime();
        List<CouponDTOInfo> couponDTOInfoList = response.getActivityInfoMap().values().stream()
                .filter(Objects::nonNull)
                .filter(activityDTO -> activityDTO.getCouponDTOInfo() != null
                        && activityDTO.getCustomerID() != null && activityDTO.getCustomerID() == productInitBO.getCustomerId()
                )
                .filter(activityDTO -> CollectionUtils.isNotEmpty(activityDTO.getTgcBizTypeSet()))
                .filter(activityDTO -> needTgcBizTypeList.stream()
                        .map(bizType -> activityDTO.getTgcBizTypeSet().contains(bizType))
                        .reduce((b1, b2) -> b1 && b2)
                        .orElse(true))
                .filter(activityDTO -> activityDTO.getStartDate() != null && activityDTO.getStartDate().before(now)
                        && activityDTO.getEndDate() != null && activityDTO.getEndDate().after(now))
                .sorted(Comparator.comparingInt(ActivityDTO::getActivityID).reversed())
                .map(ActivityDTO::getCouponDTOInfo)
                .filter(couponDTOInfo -> (couponDTOInfo.getDpCouponDTO() != null && couponDTOInfo.getDpCouponDTO().getCouponValueType() == CouponValueTypeEnum.DYNAMIC_COUPON.getCode())
                        || (couponDTOInfo.getMtCouponDTO() != null && couponDTOInfo.getMtCouponDTO().getCouponValueType() == CouponValueTypeEnum.DYNAMIC_COUPON.getCode()))
                .filter(couponDTOInfo -> (couponDTOInfo.getDpCouponDTO() != null && couponDTOInfo.getDpCouponDTO().getFullCutAmountType() == FullCutAmountType.ORDER_AMOUNT_RANGED.getCode())
                        || (couponDTOInfo.getMtCouponDTO() != null && couponDTOInfo.getMtCouponDTO().getFullCutAmountType() == FullCutAmountType.ORDER_AMOUNT_RANGED.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(couponDTOInfoList)) {
            return null;
        }
        boolean totalPriceOurOfRange = couponDTOInfoList.stream()
                .anyMatch(coupon -> (coupon.getDpCouponDTO() != null && coupon.getDpCouponDTO().getMaxFullCutAmount().compareTo(productInitBO.getProductTotalPrice()) < 0)
                        || (coupon.getMtCouponDTO() != null && coupon.getMtCouponDTO().getMaxFullCutAmount().compareTo(productInitBO.getProductTotalPrice()) < 0));
        if (totalPriceOurOfRange) {
            throw new MedicalBargainException("超出优惠券门槛范围");
        }
        if (productInitBO.getProductTotalPrice().compareTo(productInitBO.getSalePrice()) > 0) {
            BigDecimal promoAmount = productInitBO.getProductTotalPrice().subtract(productInitBO.getSalePrice());
            boolean promoAmountOutOfRange = couponDTOInfoList.stream()
                    .anyMatch(coupon -> (coupon.getDpCouponDTO() != null && coupon.getDpCouponDTO().getMaxDiscountAmount().compareTo(promoAmount) < 0)
                            || (coupon.getMtCouponDTO() != null && coupon.getMtCouponDTO().getMaxDiscountAmount().compareTo(promoAmount) < 0));
            if (promoAmountOutOfRange) {
                throw new MedicalBargainException("超出优惠金额范围");
            }
        }
        if (couponDTOInfoList.size() > 1) {
            Cat.logEvent("MedicalBargainCouponWarning",
                    "MultiCoupon" + productInitBO.getCustomerId(),
                    Event.SUCCESS,
                    String.format("dpCouponId: %s, mtCouponId: %s",
                            couponDTOInfoList.stream().map(CouponDTOInfo::getDpCouponDTO).map(CouponDTO::getCouponGroupID).collect(Collectors.toList()),
                            couponDTOInfoList.stream().map(CouponDTOInfo::getMtCouponDTO).map(CouponDTO::getCouponGroupID).collect(Collectors.toList()))
            );
        }
        return couponDTOInfoList.get(0);
    }

    private List<Integer> getProductTypeList(MedicalBargainProductInitBO productInitBO) {
        return productInitBO.getProductList().stream()
                .map(PluralProductBO::getProductType)
                .distinct()
                .map(productType -> {
                    if (productType == ProductTypeEnum.TUAN_DEAL.getCode()) {
                        return TGCBizTypeEnum.DEAL_GROUP.getCode();
                    } else if (productType == ProductTypeEnum.PREPAY.getCode()) {
                        return TGCBizTypeEnum.PREPAY.getCode();
                    }
                    return -1;
                })
                .collect(Collectors.toList());
    }

    private void checkCouponCouldIssue(MedicalBargainProductInitBO productInitBO) {
        if (productInitBO.getProductTotalPrice().subtract(productInitBO.getSalePrice()).compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        boolean couldIssue = couponIssueService.preSendCoupon(productInitBO);
        if (!couldIssue) {
            throw new MedicalBargainException("优惠抵用券暂无法发放");
        }
    }

    private long createMedicalBargainProduct(MedicalBargainProductInitBO productInitBO) {
        try {
            ProductItemBO itemBO = new ProductItemBO();
            itemBO.setProductItemId(productInitBO.getBargainProductId());
            itemBO.setBizCode(MedicalBargainProductConstants.STORE_BIZ_CODE);
            itemBO.setBizId(String.format("dis_%s_%s", productInitBO.getDistributorId(), Calendar.getInstance().getTimeInMillis()));
            itemBO.setStartTime(productInitBO.getStartTime());
            itemBO.setEndTime(productInitBO.getEndTime());
            itemBO.setStatus(DistributionStatusEnum.VALID.getCode());
            itemBO.setAttrList(buildItemAttrList(productInitBO));
            itemBO.setRelateProductList(productInitBO.getProductList());
            long bargainProductId = productItemDomainService.setProductItem(itemBO);
            if (bargainProductId > 0) {
                logCreateProductMetric(productInitBO);
            }
            productInitBO.setBargainProductId(bargainProductId);
            return bargainProductId;
        } catch (DuplicateKeyException dup) {
            log.error(getClass().getSimpleName() + ".createMedicalBargainProduct", dup);
            throw new MedicalBargainException("操作频繁，请重新创建商品");
        } catch (ProductItemException itemException) {
            throw new MedicalBargainException(itemException);
        }
    }

    private void createIntentionCode(MedicalBargainProductInitBO productInitBO) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(productInitBO.getEndTime());
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        IntentionCodeBO createIntentionRequest = IntentionCodeBO.builder()
                .bizCode(MedicalBargainProductConstants.STORE_BIZ_CODE)
                .bizId(productInitBO.getBargainProductId().toString())
                .codeType(IntentionCodeTypeEnum.PRODUCT_DISTRIBUTION.getCode())
                .intentionProductType(IntentionProductTypeEnum.DISTRIBUTION_PRODUCT_ITEM.getCode())
                .intentionProductId(productInitBO.getBargainProductId())
                .effectEndTime(calendar.getTime())
                .distributorId(String.valueOf(productInitBO.getDistributorId()))
                .build();
        String intentionCode = intentionCodeDomainService.createIntentionCode(createIntentionRequest, MedicalBargainProductConstants.INTENTION_CODE_PREFIX);
        productItemDomainService.addProductItemAttr(productInitBO.getBargainProductId(), ProductItemAttrBuilder.buildAttr("intentionCode", AttrValueTypeEnum.STRING, intentionCode));
    }

    private List<ProductItemAttr> buildItemAttrList(MedicalBargainProductInitBO productInitBO) {
        List<ProductItemAttr> attrList = Lists.newArrayList();
        attrList.add(ProductItemAttrBuilder.buildAttr("salePrice", AttrValueTypeEnum.DOUBLE, productInitBO.getSalePrice()));
        attrList.add(ProductItemAttrBuilder.buildAttr("totalPrice", AttrValueTypeEnum.DOUBLE, productInitBO.getProductTotalPrice()));
        attrList.add(ProductItemAttrBuilder.buildAttr("distributorId", AttrValueTypeEnum.LONG, productInitBO.getDistributorId()));
        attrList.add(ProductItemAttrBuilder.buildAttr("customerId", AttrValueTypeEnum.LONG, productInitBO.getCustomerId()));
        attrList.add(ProductItemAttrBuilder.buildAttr("dpCouponId", AttrValueTypeEnum.STRING, productInitBO.getDpCouponId()));
        attrList.add(ProductItemAttrBuilder.buildAttr("mtCouponId", AttrValueTypeEnum.STRING, productInitBO.getMtCouponId()));
        attrList.add(ProductItemAttrBuilder.buildAttr("mtShopId", AttrValueTypeEnum.LONG, productInitBO.getConsultantAccount().getShopId()));
        attrList.add(ProductItemAttrBuilder.buildAttr("noStockLimit", AttrValueTypeEnum.BOOLEAN, productInitBO.isNoStockLimit()));
        return attrList;
    }

    private MedicalBargainProductInitBO decorateDistributor(MedicalBargainProductInitBO productInitBO) {
        DistributorBO distributor = distributorRootService.getDistributor(productInitBO.getDistributorId());
        if (distributor == null) {
            throw new MedicalBargainException("咨询师未注册");
        }
        productInitBO.setDistributor(distributor);
        DistributorGroupBO group = groupRootService.getDistributorGroup(distributor.getDistributorGroupId());
        if (group == null || group.getStatus() != DistributionStatusEnum.VALID.getCode()) {
            throw new MedicalBargainException("咨询师未绑定门店");
        }
        List<DistributionChannelBO> channelList = channelRootService.getChannel(group.getChannelList());
        channelList = channelList.stream()
                .filter(channel -> channel.getStatus() == DistributionStatusEnum.VALID.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(channelList)) {
            throw new MedicalBargainException("咨询师渠道未设置");
        }
        if (channelList.size() > 1) {
            throw new MedicalBargainException("咨询师渠道数超过1");
        }
        productInitBO.setChannel(channelList.get(0));
        return productInitBO;
    }

    private MedicalBargainProductInitBO decoratorConsultantAccount(MedicalBargainProductInitBO productInitBO) {
        ConsultantAccountBO consultantAccount = consultantAccountService.selectByDistributorId(productInitBO.getDistributorId());
        productInitBO.setConsultantAccount(consultantAccount);
        return productInitBO;
    }

    private List<Integer> getLegalProductType() {
        return Lion.getList(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.medical.bargain.product.legal-type", Integer.class);
    }

    private void logCreateProductMetric(MedicalBargainProductInitBO productInitBO) {
        Cat.logMetricForCount("咨询师创建推广商品优惠金额", productInitBO.getProductTotalPrice().subtract(productInitBO.getSalePrice()).intValue());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        productInitDecoratePool.allowCoreThreadTimeOut(true);
    }
}
