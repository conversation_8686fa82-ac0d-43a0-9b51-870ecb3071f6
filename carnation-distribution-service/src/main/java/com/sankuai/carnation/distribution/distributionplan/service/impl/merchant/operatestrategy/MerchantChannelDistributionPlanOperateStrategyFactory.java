package com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy;

import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class MerchantChannelDistributionPlanOperateStrategyFactory {

    private static final Map<MerchantChannelDistributionPlanOperateTypeEnum, MerchantChannelDistributionPlanOperateStrategy> OPERATE_TYPE_TO_STRATEGY_MAP = new HashMap<>();

    public static void register(MerchantChannelDistributionPlanOperateTypeEnum operateType, MerchantChannelDistributionPlanOperateStrategy strategy) {
        OPERATE_TYPE_TO_STRATEGY_MAP.put(operateType, strategy);
    }

    public MerchantChannelDistributionPlanOperateStrategy getStrategyByOperateType(MerchantChannelDistributionPlanOperateTypeEnum operateType) {
        return OPERATE_TYPE_TO_STRATEGY_MAP.get(operateType);
    }
}
