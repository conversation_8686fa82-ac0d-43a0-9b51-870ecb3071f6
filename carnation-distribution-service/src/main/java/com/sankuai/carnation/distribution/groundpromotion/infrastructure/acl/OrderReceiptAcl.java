package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.onlinequery.UnifiedOrderOnlineQueryService;
import com.dianping.pay.unifiedorder.onlinequery.model.QueryListResponse;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderPaymentAmountShareDTO;
import com.dianping.receipt.query.api.ReceiptQueryService;
import com.dianping.receipt.query.dto.ReceiptDTO;
import com.dianping.receipt.query.enums.ReceiptStatusEnum;
import com.dianping.tuangou.receipt.record.api.ReceiptRecordService;
import com.dianping.tuangou.receipt.record.dto.ReceiptVerifySuccessDTO;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.common.acl.UnifiedOrderAclService;
import com.sankuai.carnation.distribution.groundpromotion.exception.GroundPromotionException;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/20
 * @Description: 团购订单券相关服务
 */
@Component
@Slf4j
public class OrderReceiptAcl {

    @Autowired
    private ReceiptQueryService receiptQueryService;

    @Autowired
    private UnifiedOrderOnlineQueryService unifiedOrderOnlineQueryService;

    @Autowired
    private ReceiptRecordService receiptRecordService;

    @Autowired
    private UnifiedOrderAclService unifiedOrderAclService;

    public int getTotalReceiptNum(String unifiedOrderId) {
        return getTotalReceipt(unifiedOrderId).size();
    }

    public int getTotalUsedReceiptNum(String unifiedOrderId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.getTotalUsedReceiptNum(java.lang.String)");
        return getUsedReceipt(unifiedOrderId).size();
    }

    public BigDecimal getTotalUsedReceiptMoney(String unifiedOrderId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.getTotalUsedReceiptMoney(java.lang.String)");
        BigDecimal totalUsedMoney = new BigDecimal(0);
        try {
            List<ReceiptDTO> usedReceiptList = getUsedReceipt(unifiedOrderId);
            if (CollectionUtils.isEmpty(usedReceiptList)) {
                return totalUsedMoney;
            }
            List<String> usedReceiptIdList = usedReceiptList.stream()
                    .map(receiptDTO -> receiptDTO.getReceiptID())
                    .map(String::valueOf).collect(Collectors.toList());

            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError("订单分摊金额查询失败", new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return totalUsedMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            Map<String, BigDecimal> usedReceiptMap = amountShareDTOList.stream()
                    .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));

            for (String usedReceiptId : usedReceiptIdList) {
                totalUsedMoney = totalUsedMoney.add(usedReceiptMap.get(usedReceiptId));
            }
            return totalUsedMoney;
        } catch (Exception e) {
            log.error("[getTotalUsedReceiptMoney] error, orderId is {}, exception is", unifiedOrderId, e);
            return totalUsedMoney;
        }
    }

    public BigDecimal getTotalRefundReceiptMoney(String unifiedOrderId) {
        BigDecimal totalRefundMoney = new BigDecimal(0);
        try {
            List<ReceiptDTO> refundReceiptList = getRefundReceipt(unifiedOrderId);
            if (CollectionUtils.isEmpty(refundReceiptList)) {
                return totalRefundMoney;
            }
            List<String> refundReceiptIdList = refundReceiptList.stream()
                    .map(receiptDTO -> receiptDTO.getReceiptID())
                    .map(String::valueOf).collect(Collectors.toList());

            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError("订单分摊金额查询失败", new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return totalRefundMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            Map<String, BigDecimal> refundReceiptMap = amountShareDTOList.stream()
                    .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));

            for (String refundReceiptId : refundReceiptIdList) {
                totalRefundMoney = totalRefundMoney.add(refundReceiptMap.get(refundReceiptId));
            }
            return totalRefundMoney;
        } catch (Exception e) {
            log.error("[getTotalRefundReceiptMoney] error, orderId is {}, exception is", unifiedOrderId, e);
            return totalRefundMoney;
        }
    }

    public BigDecimal getTotalUsedReceiptPayMoney(String unifiedOrderId) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.getTotalUsedReceiptPayMoney(java.lang.String)");
        BigDecimal totalUsedPayMoney = new BigDecimal(0);
        try {
            List<ReceiptDTO> usedReceiptList = getUsedReceipt(unifiedOrderId);
            if (CollectionUtils.isEmpty(usedReceiptList)) {
                return totalUsedPayMoney;
            }

            List<String> usedReceiptIdList = usedReceiptList.stream()
                    .map(receiptDTO -> receiptDTO.getReceiptID())
                    .map(String::valueOf).collect(Collectors.toList());

            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError(new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return totalUsedPayMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            Map<String, BigDecimal> usedReceiptMap = amountShareDTOList.stream()
                    .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));

            Map<String, BigDecimal> totalDiscountMap = amountShareDTOList.stream()
                    .collect(Collectors.groupingBy(
                            UnifiedOrderPaymentAmountShareDTO::getReceiptId,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    dto -> dto.getPlatformAmount().add(dto.getMerchantAmount()),
                                    BigDecimal::add
                            )
                    ));

            for (String usedReceiptId : usedReceiptIdList) {
                totalUsedPayMoney = totalUsedPayMoney.add(usedReceiptMap.get(usedReceiptId)).subtract(totalDiscountMap.get(usedReceiptId));
            }
            return totalUsedPayMoney;
        } catch (Exception e) {
            log.error("[getTotalUsedReceiptPayMoney] error, orderId is {}, exception is", unifiedOrderId, e);
            return totalUsedPayMoney;
        }

    }

    public BigDecimal getReceiptMoney(String unifiedOrderId, String receiptId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.getReceiptMoney(java.lang.String,java.lang.String)");
        BigDecimal receiptMoney = new BigDecimal(0);
        try {
            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);

            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError("订单分摊金额查询失败", new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return receiptMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            UnifiedOrderWithId unifiedOrder = unifiedOrderAclService.getOrder(unifiedOrderId);

            if (ProductTypeUtils.isStandard(unifiedOrder.getBizType())) {
                // todo 标品分摊金额上没有券id，且标品订单只有一张券，此为临时方案，直接取ReceiptAmount
                receiptMoney = amountShareDTOList.get(0).getReceiptAmount();
            }

            for (UnifiedOrderPaymentAmountShareDTO amountShareDTO : amountShareDTOList) {
                if (amountShareDTO.getReceiptId().equals(receiptId)) {
                    receiptMoney = amountShareDTO.getReceiptAmount();
                    break;
                }
            }
            return receiptMoney;
        } catch (Exception e) {
            log.error("[getReceiptMoney] error, orderId is {}, receiptId is {}, exception is", unifiedOrderId, receiptId, e);
            return receiptMoney;
        }
    }

    public BigDecimal getReceiptsMoney(String unifiedOrderId, List<String> receiptIdList) {
        BigDecimal receiptMoney = new BigDecimal(0);
        try {
            if (CollectionUtils.isEmpty(receiptIdList)) {
                return receiptMoney;
            }
            QueryListResponse<UnifiedOrderPaymentAmountShareDTO> response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);

            int retryCnt = 0;
            int RETRY_CNT = Lion.getInt(Environment.getAppName(), "com.sankuai.medicalcosmetology.distribution.service.order.receipt.retry.cnt", 10);
            while (retryCnt < RETRY_CNT && (CollectionUtils.isEmpty(response.getResultList()))) {
                Thread.sleep(100);
                response = unifiedOrderOnlineQueryService.getPaymentAmountShareByOrderId(unifiedOrderId);
                Cat.logEvent("GroundPromoReceiptMoney", "[orderId:" + unifiedOrderId + "]");
                retryCnt++;
            }

            if (CollectionUtils.isEmpty(response.getResultList())) {
                log.error("订单分摊金额查询失败, orderId is {}", unifiedOrderId);
                Cat.logError("订单分摊金额查询失败", new GroundPromotionException(String.format("订单分摊金额查询失败, orderId: %s", unifiedOrderId)));
                return receiptMoney;
            }
            List<UnifiedOrderPaymentAmountShareDTO> amountShareDTOList = response.getResultList();
            UnifiedOrderWithId unifiedOrder = unifiedOrderAclService.getOrder(unifiedOrderId);

            if (ProductTypeUtils.isStandard(unifiedOrder.getBizType())) {
                // todo 标品分摊金额上没有券id，且标品订单只有一张券，此为临时方案，直接取ReceiptAmount
                receiptMoney = amountShareDTOList.get(0).getReceiptAmount();
            }

            Map<String, BigDecimal> receiptMap = amountShareDTOList.stream()
                    .collect(Collectors.toMap(UnifiedOrderPaymentAmountShareDTO::getReceiptId, UnifiedOrderPaymentAmountShareDTO::getReceiptAmount, (ov, nv) -> ov));

            for (String receiptId : receiptIdList) {
                receiptMoney = receiptMoney.add(receiptMap.get(receiptId));
            }

            return receiptMoney;
        } catch (Exception e) {
            log.error("[getReceiptMoney] error, orderId is {}, receiptIdList is {}, exception is", unifiedOrderId, receiptIdList, e);
            return receiptMoney;
        }
    }

    public List<ReceiptDTO> getTotalReceipt(String unifiedOrderId) {
        return receiptQueryService.findReceiptByUnifiedOrderID(unifiedOrderId);
    }

    public List<ReceiptDTO> getUsedReceipt(String unifiedOrderId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.getUsedReceipt(java.lang.String)");
        List<ReceiptDTO> receiptList = getTotalReceipt(unifiedOrderId);
        return receiptList.stream().filter(receiptDTO -> receiptDTO.getStatus() == ReceiptStatusEnum.AlreadyUsed.getValue()).collect(Collectors.toList());
    }

    public List<ReceiptDTO> getRefundReceipt(String unifiedOrderId) {
        List<ReceiptDTO> receiptList = getTotalReceipt(unifiedOrderId);
        return receiptList.stream().filter(receiptDTO -> receiptDTO.getStatus() == ReceiptStatusEnum.alreadyRefund.getValue()
        || receiptDTO.getStatus() == ReceiptStatusEnum.usedRefund.getValue()).collect(Collectors.toList());
    }

    public List<ReceiptVerifySuccessDTO> getUsedReceiptVerifyRecord(String unifiedOrderId) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_1", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.getUsedReceiptVerifyRecord(java.lang.String)");
        List<ReceiptDTO> receiptList = getUsedReceipt(unifiedOrderId);
        List<Long> receiptIds = receiptList.stream().map(ReceiptDTO::getReceiptID).collect(Collectors.toList());
        return receiptRecordService.findReceiptVerifySuccessRecordListByReceiptIDs(receiptIds);
    }

    public Map<Long, ReceiptDTO> findReceiptByIDs(List<Long> receiptIds){
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl.findReceiptByIDs(java.util.List)");
        List<ReceiptDTO> receiptsByIds = receiptQueryService.findReceiptsByIds(receiptIds);
        if (CollectionUtils.isEmpty(receiptsByIds)) {
            return Maps.newHashMap();
        }
        return receiptsByIds.stream().collect(Collectors.toMap(ReceiptDTO::getReceiptID, Function.identity(),(v1,v2)->v1));
    }
}
