package com.sankuai.carnation.distribution.distributionplan.repository;

import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanPageQuery;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.domain.model.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionPlanRepository {

    /**
     * 添加分销计划
     * @param distributionPlanDo
     * @return
     */
    Long addDistributionPlan(DistributionPlanDO distributionPlanDo);

    /**
     * 更新分销计划
     * @param distributionPlanDo
     * @return
     */
    Boolean updateDistributionPlan(DistributionPlanDO distributionPlanDo);

    /**
     * 更新分销计划状态
     * @param distributionPlanDo
     * @return
     */
    Boolean updateDistributionPlanStatus(DistributionPlanDO distributionPlanDo);

    /**
     * 查询分销计划
     * @param planId
     * @return
     */
    DistributionPlanDO loadDistributionPlanById(Long planId);

    /**
     * 查询分销计划
     * @param planIds
     * @return
     */
    List<DistributionPlanDO> queryDistributionPlanByIds(List<Long> planIds);

    /**
     * 查询分销计划
     * @param request
     * @return
     */
    List<DistributionPlanDO> queryDistributionPlan(DistributionPlanQueryRequest request);

    /**
     * 分页查询分销计划
     * @param pageQuery
     * @return
     */
    PageResult<DistributionPlanDO> pageQueryDistributionPlan(DistributionPlanPageQuery pageQuery);
}
