package com.sankuai.carnation.distribution.privatelive.consultant.repository.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PrivateLiveOrderIntentionLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public PrivateLiveOrderIntentionLogExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public PrivateLiveOrderIntentionLogExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public PrivateLiveOrderIntentionLogExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public PrivateLiveOrderIntentionLogExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdIsNull() {
            addCriterion("long_order_id is null");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdIsNotNull() {
            addCriterion("long_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdEqualTo(Long value) {
            addCriterion("long_order_id =", value, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdNotEqualTo(Long value) {
            addCriterion("long_order_id <>", value, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdGreaterThan(Long value) {
            addCriterion("long_order_id >", value, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("long_order_id >=", value, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdLessThan(Long value) {
            addCriterion("long_order_id <", value, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("long_order_id <=", value, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdIn(List<Long> values) {
            addCriterion("long_order_id in", values, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdNotIn(List<Long> values) {
            addCriterion("long_order_id not in", values, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdBetween(Long value1, Long value2) {
            addCriterion("long_order_id between", value1, value2, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andLongOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("long_order_id not between", value1, value2, "longOrderId");
            return (Criteria) this;
        }

        public Criteria andFormerCodeIsNull() {
            addCriterion("former_code is null");
            return (Criteria) this;
        }

        public Criteria andFormerCodeIsNotNull() {
            addCriterion("former_code is not null");
            return (Criteria) this;
        }

        public Criteria andFormerCodeEqualTo(String value) {
            addCriterion("former_code =", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeNotEqualTo(String value) {
            addCriterion("former_code <>", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeGreaterThan(String value) {
            addCriterion("former_code >", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("former_code >=", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeLessThan(String value) {
            addCriterion("former_code <", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeLessThanOrEqualTo(String value) {
            addCriterion("former_code <=", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeLike(String value) {
            addCriterion("former_code like", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeNotLike(String value) {
            addCriterion("former_code not like", value, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeIn(List<String> values) {
            addCriterion("former_code in", values, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeNotIn(List<String> values) {
            addCriterion("former_code not in", values, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeBetween(String value1, String value2) {
            addCriterion("former_code between", value1, value2, "formerCode");
            return (Criteria) this;
        }

        public Criteria andFormerCodeNotBetween(String value1, String value2) {
            addCriterion("former_code not between", value1, value2, "formerCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeIsNull() {
            addCriterion("distributor_code is null");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeIsNotNull() {
            addCriterion("distributor_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeEqualTo(String value) {
            addCriterion("distributor_code =", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeNotEqualTo(String value) {
            addCriterion("distributor_code <>", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeGreaterThan(String value) {
            addCriterion("distributor_code >", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_code >=", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeLessThan(String value) {
            addCriterion("distributor_code <", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeLessThanOrEqualTo(String value) {
            addCriterion("distributor_code <=", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeLike(String value) {
            addCriterion("distributor_code like", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeNotLike(String value) {
            addCriterion("distributor_code not like", value, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeIn(List<String> values) {
            addCriterion("distributor_code in", values, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeNotIn(List<String> values) {
            addCriterion("distributor_code not in", values, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeBetween(String value1, String value2) {
            addCriterion("distributor_code between", value1, value2, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andDistributorCodeNotBetween(String value1, String value2) {
            addCriterion("distributor_code not between", value1, value2, "distributorCode");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdIsNull() {
            addCriterion("consultant_task_id is null");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdIsNotNull() {
            addCriterion("consultant_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdEqualTo(Long value) {
            addCriterion("consultant_task_id =", value, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdNotEqualTo(Long value) {
            addCriterion("consultant_task_id <>", value, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdGreaterThan(Long value) {
            addCriterion("consultant_task_id >", value, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("consultant_task_id >=", value, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdLessThan(Long value) {
            addCriterion("consultant_task_id <", value, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("consultant_task_id <=", value, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdIn(List<Long> values) {
            addCriterion("consultant_task_id in", values, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdNotIn(List<Long> values) {
            addCriterion("consultant_task_id not in", values, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdBetween(Long value1, Long value2) {
            addCriterion("consultant_task_id between", value1, value2, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andConsultantTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("consultant_task_id not between", value1, value2, "consultantTaskId");
            return (Criteria) this;
        }

        public Criteria andLiveIdIsNull() {
            addCriterion("live_id is null");
            return (Criteria) this;
        }

        public Criteria andLiveIdIsNotNull() {
            addCriterion("live_id is not null");
            return (Criteria) this;
        }

        public Criteria andLiveIdEqualTo(String value) {
            addCriterion("live_id =", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotEqualTo(String value) {
            addCriterion("live_id <>", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdGreaterThan(String value) {
            addCriterion("live_id >", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdGreaterThanOrEqualTo(String value) {
            addCriterion("live_id >=", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLessThan(String value) {
            addCriterion("live_id <", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLessThanOrEqualTo(String value) {
            addCriterion("live_id <=", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdLike(String value) {
            addCriterion("live_id like", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotLike(String value) {
            addCriterion("live_id not like", value, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdIn(List<String> values) {
            addCriterion("live_id in", values, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotIn(List<String> values) {
            addCriterion("live_id not in", values, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdBetween(String value1, String value2) {
            addCriterion("live_id between", value1, value2, "liveId");
            return (Criteria) this;
        }

        public Criteria andLiveIdNotBetween(String value1, String value2) {
            addCriterion("live_id not between", value1, value2, "liveId");
            return (Criteria) this;
        }

        public Criteria andCalResultIsNull() {
            addCriterion("cal_result is null");
            return (Criteria) this;
        }

        public Criteria andCalResultIsNotNull() {
            addCriterion("cal_result is not null");
            return (Criteria) this;
        }

        public Criteria andCalResultEqualTo(Integer value) {
            addCriterion("cal_result =", value, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultNotEqualTo(Integer value) {
            addCriterion("cal_result <>", value, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultGreaterThan(Integer value) {
            addCriterion("cal_result >", value, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("cal_result >=", value, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultLessThan(Integer value) {
            addCriterion("cal_result <", value, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultLessThanOrEqualTo(Integer value) {
            addCriterion("cal_result <=", value, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultIn(List<Integer> values) {
            addCriterion("cal_result in", values, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultNotIn(List<Integer> values) {
            addCriterion("cal_result not in", values, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultBetween(Integer value1, Integer value2) {
            addCriterion("cal_result between", value1, value2, "calResult");
            return (Criteria) this;
        }

        public Criteria andCalResultNotBetween(Integer value1, Integer value2) {
            addCriterion("cal_result not between", value1, value2, "calResult");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdIsNull() {
            addCriterion("related_record_id is null");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdIsNotNull() {
            addCriterion("related_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdEqualTo(Long value) {
            addCriterion("related_record_id =", value, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdNotEqualTo(Long value) {
            addCriterion("related_record_id <>", value, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdGreaterThan(Long value) {
            addCriterion("related_record_id >", value, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("related_record_id >=", value, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdLessThan(Long value) {
            addCriterion("related_record_id <", value, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("related_record_id <=", value, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdIn(List<Long> values) {
            addCriterion("related_record_id in", values, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdNotIn(List<Long> values) {
            addCriterion("related_record_id not in", values, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdBetween(Long value1, Long value2) {
            addCriterion("related_record_id between", value1, value2, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("related_record_id not between", value1, value2, "relatedRecordId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdIsNull() {
            addCriterion("related_modify_id is null");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdIsNotNull() {
            addCriterion("related_modify_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdEqualTo(Long value) {
            addCriterion("related_modify_id =", value, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdNotEqualTo(Long value) {
            addCriterion("related_modify_id <>", value, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdGreaterThan(Long value) {
            addCriterion("related_modify_id >", value, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("related_modify_id >=", value, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdLessThan(Long value) {
            addCriterion("related_modify_id <", value, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdLessThanOrEqualTo(Long value) {
            addCriterion("related_modify_id <=", value, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdIn(List<Long> values) {
            addCriterion("related_modify_id in", values, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdNotIn(List<Long> values) {
            addCriterion("related_modify_id not in", values, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdBetween(Long value1, Long value2) {
            addCriterion("related_modify_id between", value1, value2, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andRelatedModifyIdNotBetween(Long value1, Long value2) {
            addCriterion("related_modify_id not between", value1, value2, "relatedModifyId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}