package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.groundpromotion.dto.PoiDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryPoiAndGoodsReq;
import com.sankuai.carnation.distribution.groundpromotion.enums.ClientTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.enums.GroundPromotionScopeTypeEnum;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * @author: wuweizhen
 * @Date: 2023/6/9 10:51
 * @Description:
 */
@Component
@Slf4j
public class PoiAcl {

    @Resource
    private DzThemeShopService dzThemeShopService;

    @Autowired
    private ShopMapperService shopMapperService;

    public PoiDTO queryPoiInfo(QueryPoiAndGoodsReq queryPoiAndGoodsReq) {
        // 从商户主题拿商户信息
        PoiDTO result = new PoiDTO();
        long dpShopId = queryPoiAndGoodsReq.getDpShopId();
        ShopThemePlanRequest shopThemePlanRequest = this.buildShopThemePlanRequest(queryPoiAndGoodsReq, queryPoiAndGoodsReq.getUserId(), dpShopId);
        ShopThemeResponse shopThemeResponse = dzThemeShopService.queryShopTheme(shopThemePlanRequest);
        Long actualShopId = queryPoiAndGoodsReq.getPlatform() == 1 ? dpShopId : dp2mt(dpShopId);
        if (shopThemeResponse == null || MapUtils.isEmpty(shopThemeResponse.getShopCardDTOMap())
                || null == shopThemeResponse.getShopCardDTOMap().get(actualShopId)) {
            log.info("[PromoQRCode] NewScanBizServiceImpl.queryPoiInfo, shopThemePlanRequest = {}, userId = {}.", shopThemePlanRequest, queryPoiAndGoodsReq.getUserId());
            return null;
        }
        ShopCardDTO shopCardDTO = shopThemeResponse.getShopCardDTOMap().get(actualShopId);
        if (shopCardDTO == null) {
            return null;
        }
        result.setShopIdLong(shopCardDTO.getLongShopid());
        result.setShopuuid(shopCardDTO.getShopuuid());
        result.setDefaultPic(shopCardDTO.getHeadPic());
        result.setShopName(shopCardDTO.getShopName());
        result.setRegionName(shopCardDTO.getBareaName());
        result.setCategoryName(shopCardDTO.getCategoty() != null ? shopCardDTO.getCategoty().getName() : null);
        result.setMainCategoryId(shopCardDTO.getCategoty() != null ? shopCardDTO.getCategoty().getId() : 0);
        result.setCollectShop(shopCardDTO.getCollectDTO() != null ? shopCardDTO.getCollectDTO().isCollect() : false);
        result.setShowUrl(shopCardDTO.getShopUrl());
        result.setShopCityId(shopCardDTO.getCity() == null ? queryPoiAndGoodsReq.getCityId() : shopCardDTO.getCity().getId());
        return result;
    }

    private ShopThemePlanRequest buildShopThemePlanRequest(QueryPoiAndGoodsReq queryPoiAndGoodsReq, long userId, Long dpShopId) {
        ShopThemePlanRequest shopThemePlanRequest = new ShopThemePlanRequest();
        shopThemePlanRequest.setPlanId(queryPoiAndGoodsReq.getPlatform() == 1 ? "10500419" : "10500418");
        shopThemePlanRequest.setUserLat(queryPoiAndGoodsReq.getLat());
        shopThemePlanRequest.setUserLng(queryPoiAndGoodsReq.getLng());
        shopThemePlanRequest.setCityId(queryPoiAndGoodsReq.getCityId());
        shopThemePlanRequest.setPlatform(queryPoiAndGoodsReq.getPlatform());
        ClientTypeEnum type = ClientTypeEnum.codeOf(queryPoiAndGoodsReq.getClientType());
        ClientType clientType = getClientType(type);
        shopThemePlanRequest.setClientType(clientType.getType());
        shopThemePlanRequest.setUserId(userId <= 0 ? 0L : userId);
        shopThemePlanRequest.setNativeClient(clientType.equals(ClientType.APP));
        shopThemePlanRequest.setMiniProgram(clientType.equals(ClientType.MINI_PROGRAM));
        shopThemePlanRequest.setClientVersion(queryPoiAndGoodsReq.getVersion());
        List<Long> allShopIds = Lists.newArrayList();
        allShopIds.add(queryPoiAndGoodsReq.getPlatform() == 1 ? dpShopId.longValue() : dp2mt(dpShopId));
        shopThemePlanRequest.setLongShopIds(allShopIds);
        return shopThemePlanRequest;
    }

    private Long dp2mt(long dpShopId) {
        return shopMapperService.dp2mt(dpShopId);
    }

    private ClientType getClientType(ClientTypeEnum type) {
        switch (type) {
            case WEB:
                return ClientType.PC;
            case NATIVE:
                return ClientType.APP;
            case WEIXIN:
                return ClientType.WEIXIN;
            case MINIPROGRAM:
                return ClientType.MINI_PROGRAM;
            default:
                return ClientType.APP;
        }
    }

    /**
     * 获取poi对应的范围类型 门店or商场
     */
    public int getPoiScopeType(DpPoiDTO dpPoiDTO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.PoiAcl.getPoiScopeType(com.sankuai.sinai.data.api.dto.DpPoiDTO)");
        if (dpPoiDTO == null) {
            return GroundPromotionScopeTypeEnum.UNKNOWN.getCode();
        }
        List<DpPoiBackCategoryDTO> backMainCategoryPath = dpPoiDTO.getBackMainCategoryPath();
        Set<Integer> mallBackCategorySet = getMallBackCategory();
        for (DpPoiBackCategoryDTO dpPoiBackCategoryDTO : backMainCategoryPath) {
            if (mallBackCategorySet.contains(dpPoiBackCategoryDTO.getCategoryId())) {
                return GroundPromotionScopeTypeEnum.MALL.getCode();
            }
        }
        return GroundPromotionScopeTypeEnum.SHOP.getCode();
    }

    private Set<Integer> getMallBackCategory() {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.PoiAcl.getMallBackCategory()");
        List<Integer> backCategoryIdList = Lion.getList("com.sankuai.medicalcosmetology.distribution.service.ground.promotion.mall.back.category", Integer.class);
        return Sets.newHashSet(backCategoryIdList);
    }

}