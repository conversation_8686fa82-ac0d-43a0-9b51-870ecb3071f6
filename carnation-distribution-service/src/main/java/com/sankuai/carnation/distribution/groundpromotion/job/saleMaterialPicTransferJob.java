package com.sankuai.carnation.distribution.groundpromotion.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributor;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundDistributorRepository;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPromotionRepository;
import com.sankuai.carnation.distribution.utils.MaterialPicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/8/17
 * @Description:
 */
@Service
@Slf4j
public class saleMaterialPicTransferJob {

    @Autowired
    private GroundPromotionRepository groundPromotionRepository;

    @Autowired
    private GroundDistributorRepository groundDistributorRepository;

    @Crane("com.sankuai.carnation.distribution.groundpromotion.job.saleMaterialPicTransferJob.picTransfer")
    public void picTransfer() {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.job.saleMaterialPicTransferJob.picTransfer()");
        long groundIdCount = groundPromotionRepository.count();


        long startId = 0;
        long endId = 101;

        List<GroundDistributor> groundDistributorList = groundDistributorRepository.pageQuery(startId,endId);

        while (CollectionUtils.isNotEmpty(groundDistributorList)){
            groundDistributorList = groundDistributorRepository.pageQuery(startId,endId);
            for (GroundDistributor groundDistributor : groundDistributorList) {
                long id = groundDistributor.getId();
                String picUrl = groundDistributor.getDistributorPic();
                if (picUrl.substring(picUrl.length()-3, picUrl.length()).equals("png")) {
                    continue;
                }
                try {
                    String newPicUrl = MaterialPicUtil.reUpload(picUrl);
                    groundDistributorRepository.updatePic(id, newPicUrl);
                } catch (Exception e) {
                    log.error("[picTransfer] error, id is {}, exception is", id, e);
                }
            }
            startId += 100;
            endId += 100;
        }
    }
}
