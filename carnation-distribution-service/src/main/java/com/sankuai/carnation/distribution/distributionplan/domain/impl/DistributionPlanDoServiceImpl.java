package com.sankuai.carnation.distribution.distributionplan.domain.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductReleaseAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.StandardProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.TpfunProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ChannelProductReleaseResult;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductReleaseRequest;
import com.sankuai.carnation.distribution.distributionplan.acl.model.PushProductUnit;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.*;
import com.sankuai.carnation.distribution.distributionplan.dto.*;
import com.sankuai.carnation.distribution.distributionplan.enums.*;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.repository.DistributionPlanOperationLogRepository;
import com.sankuai.carnation.distribution.distributionplan.repository.DistributionPlanRepository;
import com.sankuai.carnation.distribution.distributionplan.service.producer.DistributionPlanDelayTaskProducer;
import com.sankuai.carnation.distribution.distributionplan.service.producer.dto.DistributionPlanDelayMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DistributionPlanDoServiceImpl implements DistributionPlanDoService {

    @Resource
    private DistributionPlanRepository distributionPlanRepository;

    @Resource
    private DistributionPlanOperationLogRepository distributionPlanOperationLogRepository;

    @Resource
    private TpfunProductAcl tpfunProductAcl;

    @Resource
    private DistributionPlanDelayTaskProducer planDelayTaskProducer;

    @Resource
    private ProductReleaseAcl productReleaseAcl;

    @Resource
    private StandardProductAcl standardProductAcl;

    @Override
    public Long addDistributionPlan(DistributionPlanAddRequest request) {
        boolean validateResult = validateProductValid(request.getProductId(), request.getProductType());
        if (!validateResult) {
            throw new DistributionPlanException("商品已下线:productOffline");
        }

        DistributionPlanQueryRequest distributionPlanQueryRequest = DistributionPlanQueryRequest.builder()
                .productIds(Lists.newArrayList(request.getProductId()))
                .productType(request.getProductType())
                .channel(request.getChannel())
                .statusList(Lists.newArrayList(DistributionPlanStatusEnum.INITIALIZE.getCode(), DistributionPlanStatusEnum.EXECUTING.getCode()))
                .build();
        List<DistributionPlanDO> existedDistributionPlanDos = distributionPlanRepository.queryDistributionPlan(distributionPlanQueryRequest);
        if (CollectionUtils.isNotEmpty(existedDistributionPlanDos)) {
            throw new DistributionPlanException("商品存在未结束的推广计划:productExistDistributionPlan");
        }

        DistributionPlanDO distributionPlanDo = DistributionPlanDO.buildByDistributionPlanAddRequest(request);
        distributionPlanRepository.addDistributionPlan(distributionPlanDo);

        planDelayTaskProducer.sendMessage(DistributionPlanDelayMsg.builder().planId(distributionPlanDo.getPlanId())
                .channel(request.getChannel())
                .operateTime(distributionPlanDo.getPreBeginTime().getTime())
                .operateType(DistributionPlanOperationEnum.BEGIN.getType())
                .build());

        DistributionPlanOperationLog distributionPlanOperationLog = DistributionPlanOperationLog.builder()
                .planId(distributionPlanDo.getPlanId())
                .operationType(DistributionPlanOperationLogTypeEnum.ADD_DISTRIBUTION_PLAN)
                .operationContent(DistributionPlanOperationLogTypeEnum.ADD_DISTRIBUTION_PLAN.getDesc())
                .operatorType(DistributionPlanOperatorTypeEnum.from(request.getOperatorType()))
                .operator(request.getOperator())
                .build();
        distributionPlanOperationLogRepository.addDistributionPlanLog(distributionPlanOperationLog);
        return distributionPlanDo.getPlanId();
    }

    private boolean validateProductValid(Long productId, Integer productType) {
        if (productType.equals(ProductTypeEnum.PREPAY.getCode())) {
            return tpfunProductAcl.checkProductValid(productId);
        }

        if (productType.equals(ProductTypeEnum.STANDARD_PRODUCT.getCode())) {
           return standardProductAcl.checkSpuValid(productId);
        }
        return false;
    }

    @Override
    public boolean editDistributionPlan(DistributionPlanEditRequest request) {
        DistributionPlanDO distributionPlanDo = distributionPlanRepository.loadDistributionPlanById(request.getPlanId());
        if (null == distributionPlanDo) {
            throw new RuntimeException("未找到推广计划:distributionPlanNotFound");
        }
        if (!distributionPlanDo.getChannel().getCode().equals(request.getChannel())) {
            throw new RuntimeException("不可跨渠道修改:channelParamIllegal");
        }
        if (!distributionPlanDo.canEdit()) {
            throw new RuntimeException("推广计划不可修改:distributionPlanOperationUnSupport");
        }

        boolean preBeginTimeChanged = !request.getPreBeginTime().equals(distributionPlanDo.getPreBeginTime().getTime());
        distributionPlanDo.edit(request);
        distributionPlanRepository.updateDistributionPlan(distributionPlanDo);
        if (preBeginTimeChanged) {
            planDelayTaskProducer.sendMessage(DistributionPlanDelayMsg.builder().planId(distributionPlanDo.getPlanId())
                    .channel(request.getChannel())
                    .operateTime(distributionPlanDo.getPreBeginTime().getTime())
                    .operateType(DistributionPlanOperationEnum.BEGIN.getType())
                    .build());
        }

        DistributionPlanOperationLog distributionPlanOperationLog = DistributionPlanOperationLog.builder()
                .planId(distributionPlanDo.getPlanId())
                .operationType(DistributionPlanOperationLogTypeEnum.EDIT_DISTRIBUTION_PLAN)
                .operationContent(DistributionPlanOperationLogTypeEnum.EDIT_DISTRIBUTION_PLAN.getDesc() + JSONObject.toJSONString(distributionPlanDo))
                .operatorType(DistributionPlanOperatorTypeEnum.from(request.getOperatorType()))
                .operator(request.getOperator())
                .build();
        distributionPlanOperationLogRepository.addDistributionPlanLog(distributionPlanOperationLog);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean operateDistributionPlan(DistributionPlanOperateRequest request) {
        DistributionPlanDO distributionPlanDo = distributionPlanRepository.loadDistributionPlanById(request.getPlanId());
        if (null == distributionPlanDo) {
            throw new DistributionPlanException("未找到推广计划:distributionPlanNotFound");
        }
        if (!distributionPlanDo.getChannel().getCode().equals(request.getChannel())) {
            throw new DistributionPlanException("不可跨渠道修改:channelParamIllegal");
        }

        boolean checkResult = distributionPlanDo.checkNextStatusOperationLegal(request.getOperation());
        if (!checkResult) {
            throw new DistributionPlanException("推广计划不支持当前操作:distributionPlanOperationUnSupport");
        }

        boolean needReleaseProduct = distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.EXECUTING) && request.getOperation().equals(DistributionPlanOperationEnum.CANCEL.getType());
        String operationContent = String.format("更新分销计划状态，从%s到", distributionPlanDo.getStatus().getDesc());
        distributionPlanDo.changeStatusByOperation(request.getOperation());
        distributionPlanRepository.updateDistributionPlanStatus(distributionPlanDo);
        if (needReleaseProduct) {
            DistributionProductPushTypeEnum pushType = request.getOperation().equals(DistributionPlanOperationEnum.BEGIN.getType()) ?
                    DistributionProductPushTypeEnum.ONLINE : DistributionProductPushTypeEnum.OFFLINE;
            ProductReleaseRequest batchPushRequest = ProductReleaseRequest.builder().pushType(pushType)
                    .pushProductUnit(PushProductUnit.buildByDistributionPlanDo(distributionPlanDo)).build();
            ChannelProductReleaseResult releaseResult = productReleaseAcl.releaseChannelProduct(batchPushRequest);
            if (!releaseResult.getSuccess()) {
                throw new DistributionPlanException("变更推广计划关联商品投放状态变更操作失败:"+releaseResult.getFailMsg());
            }
        }
        DistributionPlanOperationLog distributionPlanOperationLog = DistributionPlanOperationLog.builder()
                .planId(distributionPlanDo.getPlanId())
                .operationType(DistributionPlanOperationLogTypeEnum.UPDATE_DISTRIBUTION_PLAN_STATUS)
                .operationContent(operationContent + distributionPlanDo.getStatus().getDesc())
                .operatorType(DistributionPlanOperatorTypeEnum.from(request.getOperatorType()))
                .operator(request.getOperator())
                .build();
        distributionPlanOperationLogRepository.addDistributionPlanLog(distributionPlanOperationLog);
        return true;
    }

    @Override
    public boolean updateDistributionPlanStatus(DistributionPlanStatusUpdateRequest request) {
        DistributionPlanDO distributionPlanDo = distributionPlanRepository.loadDistributionPlanById(request.getPlanId());
        if (distributionPlanDo == null || !distributionPlanDo.getChannel().equals(request.getChannel())) {
            throw new RuntimeException("未找到推广计划:distributionPlanNotFound");
        }

        if (request.getOperation().equals(DistributionPlanOperationEnum.BEGIN)) {
            if (distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.INITIALIZE) && request.getOperationTime().equals(distributionPlanDo.getPreBeginTime().getTime())) {
                boolean checkResult = validateProductValid(distributionPlanDo.getProductId(), distributionPlanDo.getProductType().getCode());
                String operationContent = String.format("更新分销计划状态，从%s到", distributionPlanDo.getStatus().getDesc());
                if (checkResult) {
                    distributionPlanDo.changeStatus(DistributionPlanStatusEnum.EXECUTING);
                    ProductReleaseRequest batchPushRequest = ProductReleaseRequest.builder().pushType(DistributionProductPushTypeEnum.ONLINE)
                            .pushProductUnit(PushProductUnit.buildByDistributionPlanDo(distributionPlanDo)).build();
                    productReleaseAcl.releaseChannelProduct(batchPushRequest);
                    planDelayTaskProducer.sendMessage(DistributionPlanDelayMsg.builder().planId(distributionPlanDo.getPlanId())
                            .channel(distributionPlanDo.getChannel().getCode())
                            .operateTime(distributionPlanDo.getPreEndTime().getTime())
                            .operateType(DistributionPlanOperationEnum.END.getType())
                            .build());
                } else {
                    distributionPlanDo.changeStatus(DistributionPlanStatusEnum.CANCELED);
                    operationContent = "商品已下架" + operationContent;
                }

                distributionPlanRepository.updateDistributionPlanStatus(distributionPlanDo);
                DistributionPlanOperationLog distributionPlanOperationLog = DistributionPlanOperationLog.builder()
                        .planId(distributionPlanDo.getPlanId())
                        .operationType(DistributionPlanOperationLogTypeEnum.UPDATE_DISTRIBUTION_PLAN_STATUS)
                        .operationContent(operationContent + distributionPlanDo.getStatus().getDesc())
                        .operatorType(DistributionPlanOperatorTypeEnum.SYSTEM_SCHEDULER)
                        .operator(DistributionPlanOperatorTypeEnum.SYSTEM_SCHEDULER.getDesc())
                        .build();
                distributionPlanOperationLogRepository.addDistributionPlanLog(distributionPlanOperationLog);
                return true;
            }
        }
        if (request.getOperation().equals(DistributionPlanOperationEnum.END)) {
            if (distributionPlanDo.getStatus().equals(DistributionPlanStatusEnum.EXECUTING) && request.getOperationTime().equals(distributionPlanDo.getPreEndTime().getTime())) {
                String operationContent = String.format("更新分销计划状态，从%s到", distributionPlanDo.getStatus().getDesc());
                distributionPlanDo.changeStatus(DistributionPlanStatusEnum.END);
                ProductReleaseRequest batchPushRequest = ProductReleaseRequest.builder().pushType(DistributionProductPushTypeEnum.OFFLINE)
                        .pushProductUnit(PushProductUnit.buildByDistributionPlanDo(distributionPlanDo)).build();
                productReleaseAcl.releaseChannelProduct(batchPushRequest);

                distributionPlanRepository.updateDistributionPlanStatus(distributionPlanDo);
                DistributionPlanOperationLog distributionPlanOperationLog = DistributionPlanOperationLog.builder()
                        .planId(distributionPlanDo.getPlanId())
                        .operationType(DistributionPlanOperationLogTypeEnum.UPDATE_DISTRIBUTION_PLAN_STATUS)
                        .operationContent(operationContent + distributionPlanDo.getStatus().getDesc())
                        .operatorType(DistributionPlanOperatorTypeEnum.SYSTEM_SCHEDULER)
                        .operator(DistributionPlanOperatorTypeEnum.SYSTEM_SCHEDULER.getDesc())
                        .build();
                distributionPlanOperationLogRepository.addDistributionPlanLog(distributionPlanOperationLog);
                return true;
            }
        }

        Cat.logEvent("UnexpectedDistributionPlanStatusReversal", "request:"+request);
        log.info("DistributionPlanDoService.updateDistributionPlanStatus found unexpected distributionPlan status, request:{}, distributionPlanDo:{}", request, distributionPlanDo);
        return false;
    }

    @Override
    public Optional<DistributionPlanDO> loadDistributionPlan(Long planId) {
        return Optional.ofNullable(distributionPlanRepository.loadDistributionPlanById(planId));
    }

    @Override
    public Map<Long, DistributionPlanDO> queryDistributionPlanByIds(List<Long> planIds) {
        List<DistributionPlanDO> distributionPlanDos = distributionPlanRepository.queryDistributionPlanByIds(planIds);
        if (CollectionUtils.isEmpty(distributionPlanDos)) {
            return Maps.newHashMap();
        }
        return distributionPlanDos.stream().collect(Collectors.toMap(DistributionPlanDO::getPlanId, Function.identity()));
    }

    @Override
    public PageResult<DistributionPlanDO> pageQueryDistributionPlan(DistributionPlanPageQuery pageQuery) {
        return distributionPlanRepository.pageQueryDistributionPlan(pageQuery);
    }

    @Override
    public List<DistributionPlanDO> batchQueryDistributionPlans(DistributionPlanQueryRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getProductIds())) {
            return Lists.newArrayList();
        }

        return distributionPlanRepository.queryDistributionPlan(request);
    }
}