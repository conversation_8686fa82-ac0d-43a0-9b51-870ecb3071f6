package com.sankuai.carnation.distribution.distributionplan.service.impl.operate;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.gb.audit.platform.api.dto.User;
import com.dianping.gb.audit.platform.api.enums.UserTypeEnum;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.tp.audit.dto.HistoryDTO;
import com.dianping.tp.audit.dto.OperationType;
import com.dianping.tp.audit.dto.UserDTO;
import com.dianping.tp.audit.dto.UserType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.carnation.distribution.common.acl.AmazonS3AclService;
import com.sankuai.carnation.distribution.common.acl.MediaUploadAclService;
import com.sankuai.carnation.distribution.common.bo.MediaUploadSignBO;
import com.sankuai.carnation.distribution.common.bo.MediaUploadSignReq;
import com.sankuai.carnation.distribution.distributionplan.acl.*;
import com.sankuai.carnation.distribution.distributionplan.acl.model.BuInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.EmpInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.GeneralCategoryTree;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.RequisitionAuditNodeStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.request.operate.*;
import com.sankuai.carnation.distribution.distributionplan.service.model.PlatDistributionChannel;
import com.sankuai.carnation.distribution.distributionplan.service.operate.PlatChannelDistributionPlanRequisitionService;
import com.sankuai.carnation.distribution.distributionplan.service.validate.PlatChannelDistributionPlanRequisitionValidator;
import com.sankuai.carnation.distribution.distributionplan.utils.DateUtils;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.distributionplan.vo.operate.*;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import com.sankuai.dzusergrowth.common.api.enums.DistributionChannelEnum;
import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.*;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.*;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanPageQueryRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionDetailLoadRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanSubjectField;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionCommandService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import com.sankuai.meituan.uac.sdk.entity.UacGeneralAuthEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@MdpPigeonServer
@Slf4j
public class PlatChannelDistributionPlanRequisitionServiceImpl implements PlatChannelDistributionPlanRequisitionService {

    private static final String CAT_TYPE = PlatChannelDistributionPlanRequisitionService.class.getSimpleName();

    @Autowired
    private GeneralCategoryAcl generalCategoryAcl;

    @Autowired
    private MediaUploadAclService mediaUploadAclService;

    @Autowired
    private AmazonS3AclService amazonS3AclService;

    @Resource
    private RequisitionCommandService requisitionCommandService;

    @Resource
    private RequisitionQueryService requisitionQueryService;

    @Resource
    private DistributionPlanQueryService distributionPlanQueryService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private PlatformAuditAcl platformAuditAcl;

    @Autowired
    private OrgAcl orgAcl;

    @Autowired
    private UacAcl uacAcl;

    private static final String BUCKET_NAME = "dzusergrowth-distribution-core";

    /**
     * 2099年12月31日 23:59:59
     */
    private static final long END_TIME_OF_PERMANENT_EFFECTIVE = 4102415999000L;

    private static final String EFFECTIVE_PERMANENTLY = "永久有效";

    private static final String UAC_RESOURCE_NAME_LION_KEY = "uac.resource.name";

    private static final String BU_PREFIX = "bu_";

    private static final String TRADE_TYPE_PREFIX = "tradeType_";

    private static final String SYSTEM_USER_CONTEXT = "机审自动通过";

    private static final String UNKNOWN_USER_TYPE_CONTEXT = "未知用户类型";

    private static final String REJECT = "驳回";

    private static final Integer CREATE_NODE_LEVEL = 0;

    private Boolean validateSubmitter(String ssoId, Integer tradeType, Integer bu) {
        List<String> resourceName = Lion.getList(MdpContextUtils.getAppKey(), UAC_RESOURCE_NAME_LION_KEY, String.class);
        List<UacGeneralAuthEntity> uacGeneralAuthEntities = uacAcl.batchGetUserAuth(ssoId, resourceName);
        if (CollectionUtils.isEmpty(uacGeneralAuthEntities)) {
            return false;
        }
        if (checkAuth(BU_PREFIX + bu, uacGeneralAuthEntities)) {
            return false;
        }
        if (checkAuth(TRADE_TYPE_PREFIX + tradeType, uacGeneralAuthEntities)) {
            return false;
        }
        return true;
    }

    private Boolean checkAuth(String resourceCode, List<UacGeneralAuthEntity> uacGeneralAuthEntities) {
        List<UacGeneralAuthEntity> result = uacGeneralAuthEntities.stream()
                .filter(UacGeneralAuthEntity::getAuthResult)
                .flatMap(entity -> entity.getItems().stream())
                .filter(e -> e.getAuthResult() && e.getGeneralResourceCode().equals(resourceCode))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(result);
    }

    @Override
    public RemoteResponse submitRequisition(RequisitionSubmitRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "submitRequisition");
        try {
            if (!validateSubmitter(request.getSsoId(), request.getTradeType(), request.getBu())) {
                return RemoteResponse.fail("用户无操作权限");
            }
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateRequisitionSubmitRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.submitRequisition illegal argument:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            Date nowDate = new Date();
            if (silentPeriodValidate(nowDate)) {
                transaction.setStatus("当前时段不支持提交，请明日0点后重试");
                return RemoteResponse.fail("当前时段不支持提交，请明日0点后重试");
            }

            int lastSlashIndex = request.getDetailFile().lastIndexOf("/");
            if (lastSlashIndex == -1) {
                throw new DistributionPlanException("文件链接不合法");
            }
            String fileName = request.getDetailFile().substring( lastSlashIndex+ 1);

            com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionSubmitRequest submitRequest = com.sankuai.dzusergrowth.distribution.plan.api.request
                    .RequisitionSubmitRequest.builder()
                    .requisitionType(request.getRequisitionType())
                    .channel(request.getChannel())
                    .background(request.getBackground())
                    .title(request.getTitle())
                    .fileName(fileName)
                    .tradeType(request.getTradeType())
                    .creatorId(request.getCreatorId())
                    .bu(request.getBu())
                    .creatorName(request.getCreatorName())
                    .ssoId(request.getSsoId())
                    .build();

            Response<Boolean> response = redisLockService.tryLock(String.format("addPlatRequisition_%s_%s", request.getTradeType(), request.getChannel()),
                    () -> requisitionCommandService.submitRequisition(submitRequest), 3);
            if (response.respFailed()) {
                throw new DistributionPlanException(response.getMessage());
            }
            transaction.setSuccessStatus();
            return RemoteResponse.success(true);
        } catch (DistributionPlanException de) {
            Cat.logEvent("submitRequisitionBizError", "PlatChannelDistributionPlanRequisitionService submitRequisition biz failed");
            log.error("PlatChannelDistributionPlanRequisitionService.submitRequisition biz failed,request:{}", JsonUtil.toJson(request), de);
            transaction.setStatus(de);
            return RemoteResponse.fail(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("submitRequisitionError", "PlatChannelDistributionPlanRequisitionService submitRequisition failed");
            log.error("PlatChannelDistributionPlanRequisitionService.submitRequisition failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    /**
     * 静默期校验，平台端晚上11点30到11点59不允许提交
     * @param nowDate
     */
    private boolean silentPeriodValidate(Date nowDate) {
        Map<String, String> map = Lion.getMap(Environment.getAppName(), "platform.channel.submit.distribution.plan.silent.time", String.class);
        Calendar silentStartTime = com.sankuai.carnation.distribution.utils.DateUtils.convertStrToCalendar(map.get("silentStartTime"));
        Calendar silentEndTime = com.sankuai.carnation.distribution.utils.DateUtils.convertStrToCalendar(map.get("silentEndTime"));
        if (Objects.nonNull(silentStartTime) && Objects.nonNull(silentEndTime)) {
            Calendar nowCalendar = Calendar.getInstance();
            nowCalendar.setTime(nowDate);
            // 检查当前时间是否在静默期内
            if (nowCalendar.after(silentStartTime) && nowCalendar.before(silentEndTime)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public RemoteResponse<Boolean> validateRequisitionSubmit(RequisitionSubmitRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "validateRequisitionSubmit");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateRequisitionSubmitRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.validateRequisitionSubmit illegal argument:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            Date nowDate = new Date();
            if (silentPeriodValidate(nowDate)) {
                transaction.setStatus("当前时段不支持提交，请明日0点后重试");
                return RemoteResponse.fail("当前时段不支持提交，请明日0点后重试");
            }

            int lastSlashIndex = request.getDetailFile().lastIndexOf("/");
            if (lastSlashIndex == -1) {
                throw new DistributionPlanException("文件链接不合法");
            }
            String fileName = request.getDetailFile().substring( lastSlashIndex+ 1);

            com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionSubmitRequest submitRequest = com.sankuai.dzusergrowth.distribution.plan.api.request
                    .RequisitionSubmitRequest.builder()
                    .requisitionType(request.getRequisitionType())
                    .channel(request.getChannel())
                    .background(request.getBackground())
                    .title(request.getTitle())
                    .fileName(fileName)
                    .tradeType(request.getTradeType())
                    .creatorId(request.getCreatorId()).build();

            Response<Boolean> response = requisitionCommandService.validateRequisitionSubmit(submitRequest);
            if (response.respFailed()) {
                throw new DistributionPlanException(response.getMessage());
            }
            transaction.setSuccessStatus();
            return RemoteResponse.success(response.getData());
        } catch (DistributionPlanException de) {
            Cat.logEvent("validateRequisitionSubmitBizError", "validateRequisitionSubmit biz failed");
            log.error("PlatChannelDistributionPlanRequisitionService.validateRequisitionSubmit biz failed,request:{}", JsonUtil.toJson(request), de);
            transaction.setStatus(de);
            return RemoteResponse.fail(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("validateRequisitionSubmitError", "validateRequisitionSubmit failed");
            log.error("PlatChannelDistributionPlanRequisitionService.validateRequisitionSubmit failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public PaginationRemoteResponse<RequisitionVO> pageQueryRequisition(RequisitionPageQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pageQueryRequisition");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateRequisitionPageQueryRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.pageQueryRequisition illegal argument:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return PaginationRemoteResponse.failure(errorMsg.toString());
            }
            com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionPageQueryRequest pageQueryRequest =
                    new com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionPageQueryRequest();
            pageQueryRequest.setRequisitionType(request.getRequisitionType());
            pageQueryRequest.setChannel(request.getChannel());
            pageQueryRequest.setPageNo(request.getPageNo());
            pageQueryRequest.setPageSize(request.getPageSize());
            if (StringUtils.isNotBlank(request.getCreator())) {
                pageQueryRequest.setCreator(request.getCreator());
            }
            if (Objects.nonNull(request.getSubmitBeginTime()) && Objects.nonNull(request.getSubmitEndTime())) {
                pageQueryRequest.setSubmitBeginTime(request.getSubmitBeginTime());
                pageQueryRequest.setSubmitEndTime(request.getSubmitEndTime());
            }
            if (Objects.nonNull(request.getStatus())) {
                pageQueryRequest.setStatus(request.getStatus());
            }
            Response<PageResult<RequisitionDTO>> response = requisitionQueryService.pageQueryRequisition(pageQueryRequest);
            if (response.respFailed() || Objects.isNull(response.getData())) {
                throw new DistributionPlanException("分页查询申请记录失败，请稍后重试");
            }

            if (CollectionUtils.isEmpty(response.getData().getData())) {
                Cat.logEvent("RequisitionEmptyPage", "PlatChannelDistributionPlanRequisitionService PageQueryRequisition query empty page");
                return PaginationRemoteResponse.success(Lists.newArrayList(), 0L);
            }
            PageResult<RequisitionDTO> pageResult = response.getData();
            List<RequisitionVO> voList = pageResult.getData().stream().filter(Objects::nonNull).map(this::convertDto2Vo).collect(Collectors.toList());
            transaction.setSuccessStatus();
            return PaginationRemoteResponse.success(voList, pageResult.getTotalCount());
        } catch (DistributionPlanException de) {
            Cat.logEvent("pageQueryRequisitionBizError", "pageQueryRequisition biz failed");
            log.error("PlatChannelDistributionPlanRequisitionService.pageQueryRequisition biz failed,request:{}", JsonUtil.toJson(request), de);
            transaction.setStatus(de);
            return PaginationRemoteResponse.failure(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("pageQueryRequisitionError", "pageQueryRequisition failed");
            log.error("PlatChannelDistributionPlanRequisitionService.pageQueryRequisition failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return PaginationRemoteResponse.failure(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<RequisitionDetailVO> loadRequisitionDetail(RequisitionDetailRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "loadRequisitionDetail");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateRequisitionDetailLoadRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.loadRequisitionDetail 请求参数异常:{}", errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }
            RequisitionDetailLoadRequest detailLoadRequest = new RequisitionDetailLoadRequest();
            detailLoadRequest.setRequisitionId(Long.parseLong(request.getRequisitionId()));
            // 查询数据库申请表信息
            Response<RequisitionDetailDTO> response = requisitionQueryService.loadRequisitionDetail(detailLoadRequest);
            if (response.respFailed()) {
                throw new DistributionPlanException(response.getMessage());
            }

            if (Objects.isNull(response.getData())) {
                Cat.logEvent("RequisitionDetailNotFound", "PlatChannelDistributionPlanRequisitionService loadRequisitionDetail result is null");
                return RemoteResponse.success(null);
            }
            RequisitionDetailDTO requisitionDetailDTO = response.getData();

            // 查询uap侧审核流程信息
            List<HistoryDTO> historyDTOS = platformAuditAcl.fetchHistory(requisitionDetailDTO.getRequisitionId());

            Response<List<RequisitionAuditNodeDTO>> nodeDetailRes = requisitionQueryService.queryAllAuditNodeDetail();
            if (response == null ||response.respFailed()) {
                log.error("查询审批节点信息失败");
                return RemoteResponse.fail("查询审批节点信息失败");
            }
            List<RequisitionAuditNodeDTO> auditNodeDetailDTOList = nodeDetailRes.getData();
            if (CollectionUtils.isEmpty(auditNodeDetailDTOList)) {
                log.error("审批节点信息未配置", new DistributionPlanException("审批节点未配置"));
                return RemoteResponse.fail("审批节点未配置");
            }

            transaction.setSuccessStatus();
            return RemoteResponse.success(convertRequisitionDetailDto2Vo(requisitionDetailDTO, historyDTOS, auditNodeDetailDTOList));
        } catch (DistributionPlanException de) {
            Cat.logEvent("loadRequisitionDetailBizError", "loadRequisitionDetail biz failed");
            log.error("PlatChannelDistributionPlanRequisitionService.loadRequisitionDetail biz failed,request:{}", JsonUtil.toJson(request), de);
            transaction.setStatus(de);
            return RemoteResponse.fail(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("loadRequisitionDetailError", "loadRequisitionDetail failed");
            log.error("PlatChannelDistributionPlanRequisitionService.loadRequisitionDetail failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<Boolean> cancelRequisition(RequisitionCancelRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "cancelRequisition");
        try {
            if (request.getRequisitionId() == null) {
                throw new IllegalArgumentException("申请单ID为空");
            }
            if (request.getCreatorId() == null) {
                throw new IllegalArgumentException("申请人ID为空");
            }
            com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionCancelRequest cancelRequest =
                    com.sankuai.dzusergrowth.distribution.plan.api.request.RequisitionCancelRequest.builder()
                    .requisitionId(request.getRequisitionId())
                    .creatorId(request.getCreatorId())
                    .build();
            Response<Boolean> response = requisitionCommandService.cancelRequisition(cancelRequest);
            if (response.respFailed()) {
                return RemoteResponse.fail(response.getMessage());
            }
            return RemoteResponse.success(response.getData());
        } catch(Exception e) {
            log.error("PlatChannelDistributionPlanRequisitionService.cancelRequisition failed, request: {}", request, e);
            Cat.logError(e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private List<RequisitionAuditNodeDetail> buildAuditNodeDetailList(List<HistoryDTO> historyDTOS,
                                                                      Integer requisitionStatus,
                                                                      List<RequisitionAuditNodeDTO> auditNodeDetailDTOList) {
        if (CollectionUtils.isEmpty(historyDTOS)) {
            log.error("uap侧流程查询为空", new DistributionPlanException("uap侧流程查询为空"));
            return null;
        }

        List<RequisitionAuditNodeDetail> requisitionAuditNodeDetailList = new ArrayList<>();
        // 已审核节点处理
        historyDTOS.forEach(historyDTO -> {
            if (checkBuildAuditNodeDetail(historyDTO, auditNodeDetailDTOList)) {
                requisitionAuditNodeDetailList.add(buildAuditNodeDetail(historyDTO, auditNodeDetailDTOList, requisitionStatus));
            }
        });

        // 未审核节点处理
        // 获取最高审批层级
        Optional<RequisitionAuditNodeDTO> maxOp = auditNodeDetailDTOList.stream()
                .max(Comparator.comparingInt(RequisitionAuditNodeDTO::getLevel));

        if (!maxOp.isPresent()) {
            return requisitionAuditNodeDetailList;
        }
        Integer maxLevel = maxOp.get().getLevel();

        // 获取已审批的节点层级
        RequisitionAuditNodeDetail detail = requisitionAuditNodeDetailList.get(requisitionAuditNodeDetailList.size() - 1);
        Integer level = detail.getLevel();

        // 添加未审批节点信息
        for (int i = level + 1; i <= maxLevel ; i++) {
            int finalI = i;
            Optional<RequisitionAuditNodeDTO> nodeDTOOp = auditNodeDetailDTOList.stream()
                    .filter(e -> e.getLevel() == finalI)
                    .findFirst();
            if (!nodeDTOOp.isPresent()) {
                continue;
            }
            RequisitionAuditNodeDTO nodeDTO = nodeDTOOp.get();
            RequisitionAuditNodeDetail nodeDetail = RequisitionAuditNodeDetail.builder()
                    .nodeName(nodeDTO.getNodeName())
                    .level(nodeDTO.getLevel())
                    .status(RequisitionAuditNodeStatusEnum.AUDITING.getCode())
                    .build();
            requisitionAuditNodeDetailList.add(nodeDetail);
        }

        // 将nodeDetail按照level从小到大排序
        return requisitionAuditNodeDetailList.stream()
                .sorted(Comparator.comparingInt(RequisitionAuditNodeDetail::getLevel))
                .collect(Collectors.toList());
    }

    private Boolean checkBuildAuditNodeDetail(HistoryDTO historyDTO, List<RequisitionAuditNodeDTO> auditNodeDetailDTOList) {
        if (historyDTO == null) {
            log.error("当前historyDTO为空");
            return false;
        }

        // 如果是创建节点, 直接返回
        if (historyDTO.getOperation().equals(OperationType.CREATE)) {
            return true;
        }

        // 判断节点名称
        // 如果非创建节点, 节点名称不为空
        if (StringUtils.isBlank(historyDTO.getNodeName())) {
            log.error("historyDTO的nodeName为空");
            return false;
        }
        Optional<RequisitionAuditNodeDTO> nodeDTO = auditNodeDetailDTOList.stream()
                .filter(e -> e.getNodeName().equals(historyDTO.getNodeName()))
                .findFirst();
        if (!nodeDTO.isPresent()) {
            return false;
        }
        return true;
    }

    private RequisitionAuditNodeDetail buildAuditNodeDetail(HistoryDTO historyDTO,
                                                            List<RequisitionAuditNodeDTO> auditNodeDetailDTOList,
                                                            Integer requisitionStatus) {
        return RequisitionAuditNodeDetail.builder()
                .auditor(buildAuditor(historyDTO.getUser()))
                .auditTime(DateUtils.format(historyDTO.getAddTime(), DateUtils.DEFAULT_FULL_PATTERN))
                .status(buildAuditNameStatus(historyDTO.getOperation(), historyDTO.getDesc()))
                .nodeName(historyDTO.getOperation().equals(OperationType.CREATE) ? "" : historyDTO.getNodeName())
                .level(historyDTO.getOperation().equals(OperationType.CREATE) ? CREATE_NODE_LEVEL : buildLevel(historyDTO.getNodeName(), auditNodeDetailDTOList))
                .rejectReason(buildRejectReason(historyDTO, requisitionStatus))
                .build();
    }

    private String buildRejectReason(HistoryDTO historyDTO, Integer requisitionStatus) {
        // 如果当前申请单已经关闭, 在创建节点填入固定的驳回文案
        if (historyDTO.getOperation().equals(OperationType.CREATE) &&
                requisitionStatus.equals(DistributionRequisitionStatusEnum.CANCEL.getCode())) {
            return DistributionRequisitionStatusEnum.CANCEL.getDesc();
        }

        // 如果当前申请单已经超时, 在创建节点填入固定的驳回文案
        if (historyDTO.getOperation().equals(OperationType.CREATE) &&
                requisitionStatus.equals(DistributionRequisitionStatusEnum.TIMEOUT.getCode())) {
            return DistributionRequisitionStatusEnum.TIMEOUT.getDesc();
        }

        // 节点驳回
        if (historyDTO.getOperation().equals(OperationType.FLOW) && historyDTO.getDesc().contains(REJECT) && StringUtils.isNotBlank(historyDTO.getMemo())) {
            return historyDTO.getMemo();
        }
        return null;
    }

    private Integer buildLevel(String nodeName, List<RequisitionAuditNodeDTO> auditNodeDetailDTOList) {
        Optional<RequisitionAuditNodeDTO> auditNodeDetail = auditNodeDetailDTOList.stream()
                .filter(e -> e.getNodeName().equals(nodeName))
                .findFirst();

        if (!auditNodeDetail.isPresent()) {
            log.error("未找到相应节点的层级信息", new DistributionPlanException("未找到相应节点的层级信息"));
            return null;
        }
        return auditNodeDetail.get().getLevel();
    }

    private Integer buildAuditNameStatus(OperationType operationType, String desc) {
        if (StringUtils.isBlank(desc)) {
            log.error("desc为空");
            return null;
        }
        // 节点创建
        if (operationType.equals(OperationType.CREATE)) {
            return RequisitionAuditNodeStatusEnum.CREATE.getCode();
        }
        // 节点驳回
        if (operationType.equals(OperationType.FLOW) && desc.contains(REJECT)) {
            return RequisitionAuditNodeStatusEnum.REJECT.getCode();
        }
        // 节点通过
        if (operationType.equals(OperationType.FLOW) && !desc.contains(REJECT)) {
            return RequisitionAuditNodeStatusEnum.APPROVE.getCode();
        }
        // 节点关闭
        if (operationType.equals(OperationType.CLOSE)) {
            return RequisitionAuditNodeStatusEnum.CANCEL.getCode();
        }
        log.error("未知状态, operationType: {}, desc: {}", operationType, desc);
        return null;
    }

    private String buildAuditor(UserDTO user) {
        // 系统用户代表机审, 否则是人审
        if (user.getUserType().equals(UserType.SYSTEM_USER)) {
            return SYSTEM_USER_CONTEXT;
        } else if (user.getUserType().equals(UserType.SSO_USER)) {
            EmpInfo empInfo = orgAcl.queryEmpInfo((long) user.getUserID());
            if (empInfo == null) {
                return "未知用户";
            }
            return empInfo.getName() + "/" + empInfo.getMis();
        } else {
            return UNKNOWN_USER_TYPE_CONTEXT;
        }
    }

    @Override
    public PaginationRemoteResponse<PlatChannelDistributionPlanVO> pageQueryDistributionPlan(PlatChannelDistributionPlanPageQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pageQueryDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validatePlatChannelDistributionPlanPageQueryRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.pageQueryDistributionPlan illegal argument:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return PaginationRemoteResponse.failure(errorMsg.toString());
            }

            DistributionPlanPageQueryRequest pageQueryRequest = new DistributionPlanPageQueryRequest();
            pageQueryRequest.setPageNo(request.getPageNo());
            pageQueryRequest.setPageSize(request.getPageSize());
            pageQueryRequest.setSceneCode(DistributionPlanSceneCodeEnum.PLATFORM_SUBSIDY_CHANNEL.getCode());
            pageQueryRequest.setEffectivePeriodType(DistributionPlanEffectivePeriodTypeEnum.CURRENT_PERIOD.getCode());
            if (CollectionUtils.isNotEmpty(request.getChannelList())) {
                pageQueryRequest.setChannelList(request.getChannelList());
            }
            if (CollectionUtils.isNotEmpty(request.getProductCategoryIdList())) {
                List<DistributionPlanSubjectField> subjectFieldList = request.getProductCategoryIdList().stream()
                        .map(productCategoryId -> DistributionPlanSubjectField.builder()
                                .subjectType(DistributionPlanSubjectTypeEnum.PRODUCT_CATEGORY.getCode())
                                .field1(String.valueOf(request.getTradeType()))
                                .field2(String.valueOf(productCategoryId)).build()).collect(Collectors.toList());
                pageQueryRequest.setSubjectFieldList(subjectFieldList);
            } else {
                pageQueryRequest.setSubjectFieldList(Lists.newArrayList(DistributionPlanSubjectField.builder()
                        .subjectType(DistributionPlanSubjectTypeEnum.PRODUCT_CATEGORY.getCode())
                        .field1(String.valueOf(request.getTradeType())).build()));
            }
            if (StringUtils.isNotBlank(request.getCommissionRate())) {
                Map<Integer, BigDecimal> commissionRateMap = Maps.newHashMap();
                commissionRateMap.put(DistributionCommissionTypeEnum.PLAT_CHANNEL_COMMISSION.getCode()
                        , new BigDecimal(request.getCommissionRate()).divide(new BigDecimal(100)));
                pageQueryRequest.setCommissionRateMap(commissionRateMap);
            }

            Response<PageResult<DistributionPlanDTO>> response = distributionPlanQueryService.pageQueryDistributionPlan(pageQueryRequest);
            if (response.respFailed() || Objects.isNull(response.getData())) {
                throw new DistributionPlanException("分页查询平台分销计划失败，请稍后重试");
            }

            if (CollectionUtils.isEmpty(response.getData().getData())) {
                Cat.logEvent("PlatDistributionPlanEmptyPage", "PlatChannelDistributionPlanService query empty page");
                return PaginationRemoteResponse.success(Lists.newArrayList(), 0L);
            }
            PageResult<DistributionPlanDTO> pageResult = response.getData();
            List<DistributionPlanDTO> data = pageResult.getData();
            List<Integer> tradeTypeList = data.stream().map(planDTO -> {
                if (Objects.isNull(planDTO.getSubject())) {
                    return null;
                }
                if (Objects.isNull(planDTO.getSubject().getField1())) {
                    return null;
                }
                return Integer.valueOf(planDTO.getSubject().getField1());
            }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<GeneralCategoryTree> categoryTreeList = tradeTypeList.stream()
                    .flatMap(tradeType -> generalCategoryAcl.getCategoryTreeByTradeType(tradeType).stream())
                    .collect(Collectors.toList());
            Map<Long, GeneralCategoryTree> categoryTreeMap = categoryTreeList.stream()
                    .filter(categoryTree -> CollectionUtils.isNotEmpty(categoryTree.getChildList()))
                    .flatMap(parentTree -> parentTree.getChildList().stream()
                            .filter(Objects::nonNull)
                            .peek(child -> {
                                // 填充父级的类目ID和名称
                                child.setParentPlatformCategoryId(parentTree.getPlatformCategoryId());
                                child.setParentCategoryName(parentTree.getCategoryName());
                            }))
                    .collect(Collectors.toMap(
                            GeneralCategoryTree::getPlatformCategoryId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
            List<PlatChannelDistributionPlanVO> voList = data.stream()
                    .filter(Objects::nonNull)
                    .map(planDTO -> this.convertDto2Vo(planDTO, categoryTreeMap))
                    .collect(Collectors.toList());
            transaction.setSuccessStatus();
            return PaginationRemoteResponse.success(voList, pageResult.getTotalCount());
        } catch (Exception e) {
            Cat.logEvent("pageQueryDistributionPlanFailed", "PlatChannelDistributionPlanRequisitionService pageQueryDistributionPlan failed");
            log.error("PlatChannelDistributionPlanRequisitionService.pageQueryDistributionPlan failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return PaginationRemoteResponse.failure(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private PlatChannelDistributionPlanVO convertDto2Vo(DistributionPlanDTO planDTO, Map<Long, GeneralCategoryTree> categoryTreeMap) {

        PlatChannelDistributionPlanVO.PlatChannelDistributionPlanVOBuilder voBuilder = PlatChannelDistributionPlanVO.builder()
                .planId(planDTO.getPlanId())
                .channel(planDTO.getChannel())
                .beginTime(DateUtils.format(planDTO.getBeginTime(), DateUtils.YEARS_MONTH_DAY_PATTERN) + " 起")
                .tradeType(planDTO.getSubject().getField1())
                .productCategoryId(Long.valueOf(planDTO.getSubject().getField2()))
                .creator(planDTO.getCreator().getCreatorId());
        if (Objects.nonNull(planDTO.getSubject().getField2())) {
            Long categoryId = Long.valueOf(planDTO.getSubject().getField2());
            if (categoryTreeMap.containsKey(categoryId)) {
                GeneralCategoryTree tree = categoryTreeMap.get(categoryId);
                voBuilder.productCategoryName(tree.getParentCategoryName() + "/" + tree.getCategoryName());
            }
        }

        BigDecimal commissionRate = planDTO.getCommissionRateMap()
                .getOrDefault(DistributionCommissionTypeEnum.PLAT_CHANNEL_COMMISSION.getCode(), null);
        if (Objects.nonNull(commissionRate)) {
            voBuilder.commissionRate(commissionRate.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        }
        return voBuilder.build();
    }

    @Override
    public PaginationRemoteResponse<PlatChannelHistoryDistributionPlanVO> pageQueryHistoryDistributionPlan(DistributionPlanHistoryPageQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pageQueryHistoryDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateDistributionPlanHistoryPageQueryRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.pageQueryHistoryDistributionPlan illegal argument:{}, errorMsg:{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return PaginationRemoteResponse.failure(errorMsg.toString());
            }
            com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanHistoryPageQueryRequest pageQueryRequest = new com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanHistoryPageQueryRequest();
            pageQueryRequest.setPlanId(request.getPlanId());
            pageQueryRequest.setPageNo(request.getPageNo());
            pageQueryRequest.setPageSize(request.getPageSize());
            Response<PageResult<DistributionPlanDTO>> response = distributionPlanQueryService.pageQueryDistributionPlanHistory(pageQueryRequest);
            if (response.respFailed() || Objects.isNull(response.getData())) {
                throw new DistributionPlanException("查询历史分销计划失败，请稍后重试");
            }

            if (CollectionUtils.isEmpty(response.getData().getData())) {
                Cat.logEvent("PageQueryHistoryDistributionPlan", "PageQueryHistoryDistributionPlan query empty page");
                return PaginationRemoteResponse.success(Lists.newArrayList(), 0L);
            }
            PageResult<DistributionPlanDTO> pageResult = response.getData();
            List<Long> planIdList = pageResult.getData().stream().map(DistributionPlanDTO::getPlanId).collect(Collectors.toList());
            Response<List<DistributionPlanRelatedRequisitionDTO>> taskResponse = requisitionQueryService.queryRequisitionByPlanIds(planIdList);
            if (taskResponse.respFailed() || CollectionUtils.isEmpty(taskResponse.getData())) {
                throw new DistributionPlanException("查询分销计划申请单关联信息，请稍后重试");
            }

            Map<Long, DistributionPlanRelatedRequisitionDTO> planId2TaskDtoMap = taskResponse.getData().stream()
                    .collect(Collectors.toMap(DistributionPlanRelatedRequisitionDTO::getPlanId, Function.identity()));

            Date now = new Date();
            List<PlatChannelHistoryDistributionPlanVO> voList = pageResult.getData().stream().filter(Objects::nonNull)
                    .map(plan -> this.convertDto2Vo(plan, planId2TaskDtoMap.get(plan.getPlanId()), now))
                    .collect(Collectors.toList());

            transaction.setSuccessStatus();
            return PaginationRemoteResponse.success(voList, pageResult.getTotalCount());
        } catch (Exception e) {
            Cat.logEvent("pageQueryHistoryDistributionPlanFailed", "pageQueryHistoryDistributionPlan failed");
            log.error("PlatChannelDistributionPlanRequisitionService.pageQueryHistoryDistributionPlan failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return PaginationRemoteResponse.failure(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }


    private PlatChannelHistoryDistributionPlanVO convertDto2Vo(DistributionPlanDTO planDto, DistributionPlanRelatedRequisitionDTO taskDto, Date now) {
        PlatChannelHistoryDistributionPlanVO.PlatChannelHistoryDistributionPlanVOBuilder voBuilder = PlatChannelHistoryDistributionPlanVO.builder()
                .planId(planDto.getPlanId())
                .beginTime(DateUtils.format(planDto.getBeginTime(), DateUtils.YEARS_MONTH_DAY_PATTERN))
                .endTime(DateUtils.format(planDto.getEndTime(), DateUtils.YEARS_MONTH_DAY_PATTERN));
        if (planDto.getEndTime().getTime() == END_TIME_OF_PERMANENT_EFFECTIVE) {
            voBuilder.endTime(EFFECTIVE_PERMANENTLY);
        }
        BigDecimal commissionRate = planDto.getCommissionRateMap()
                .getOrDefault(DistributionCommissionTypeEnum.PLAT_CHANNEL_COMMISSION.getCode(), null);
        if (Objects.nonNull(commissionRate)) {
            voBuilder.commissionRate(commissionRate.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(planDto.getCreator())) {
            voBuilder.creator(planDto.getCreator().getCreatorId());
        }
        if (Objects.nonNull(taskDto)) {
            voBuilder.requisitionId(taskDto.getRequisitionId()!= null ? String.valueOf(taskDto.getRequisitionId()) : StringUtils.EMPTY);
            voBuilder.requisitionBackground(taskDto.getRequisitionBackground());
        }
        if (planDto.getEndTime().before(planDto.getBeginTime())) {
            voBuilder.status(MerchantChannelDistributionPlanStatusEnum.CANCELED.getCode());
            voBuilder.statusDesc(MerchantChannelDistributionPlanStatusEnum.CANCELED.getDesc());
            voBuilder.endTime(EFFECTIVE_PERMANENTLY);
            return voBuilder.build();
        }

        //待生效的计划
        if (planDto.getBeginTime().getTime() >= now.getTime()) {
            voBuilder.status(MerchantChannelDistributionPlanStatusEnum.WAIT_EFFECTIVE.getCode());
            voBuilder.statusDesc(MerchantChannelDistributionPlanStatusEnum.WAIT_EFFECTIVE.getDesc());
            return voBuilder.build();
        }

        //已结束的计划
        if (planDto.getEndTime() != null && planDto.getEndTime().getTime() <= now.getTime()) {
            voBuilder.status(MerchantChannelDistributionPlanStatusEnum.FINISHED.getCode());
            voBuilder.statusDesc(MerchantChannelDistributionPlanStatusEnum.FINISHED.getDesc());
            return voBuilder.build();
        }

        //生效中的计划，只有未来时间为空的时候才可操作，不然说明本身已经被修改过了
        voBuilder.status(MerchantChannelDistributionPlanStatusEnum.EXECUTING.getCode());
        voBuilder.statusDesc(MerchantChannelDistributionPlanStatusEnum.EXECUTING.getDesc());
        return voBuilder.build();
    }

    @Override
    public RemoteResponse<RequisitionTemplateDownloadLinkVO> queryRequisitionTemplateDownloadLink(RequisitionTemplateDownloadLinkRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryRequisitionTemplateDownloadLink");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateRequisitionTemplateDownloadLinkRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.queryRequisitionTemplateDownloadLink 请求参数异常:{}", errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            Map<String, String> templateLinkMap = Lion.getMap(Environment.getAppName(), "requisition.template.download.file.key", String.class);
            String fileKey = templateLinkMap.get(String.valueOf(request.getRequisitionType()));
            if (!amazonS3AclService.doesObjectExist(BUCKET_NAME, fileKey)) {
                throw new DistributionPlanException("模板文件不存在");
            }

            String templateUrl = amazonS3AclService.getS3DownloadUrl(BUCKET_NAME, fileKey);
            transaction.setSuccessStatus();
            return RemoteResponse.success(RequisitionTemplateDownloadLinkVO.builder().templateUrl(templateUrl).build());
        } catch (Exception e) {
            Cat.logEvent("queryRequisitionTemplateDownloadLinkFailed", "queryRequisitionTemplateDownloadLink failed");
            log.error("PlatChannelDistributionPlanRequisitionService.queryRequisitionTemplateDownloadLink failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<List<ProductCategoryTreeVO>> queryProductCategoryTree(ProductCategoryTreeQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryProductCategoryTree");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateProductCategoryTreeQueryRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.queryProductCategoryTree 请求参数异常:{}", errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            List<GeneralCategoryTree> categoryTreeList = generalCategoryAcl.getCategoryTreeByTradeType(request.getTradeType());
            List<ProductCategoryTreeVO> productCategoryTreeVOList = convertToProductCategoryTreeVOList(categoryTreeList);
            transaction.setSuccessStatus();
            return RemoteResponse.success(productCategoryTreeVOList);
        } catch (Exception e) {
            Cat.logEvent("queryOperateTradeTypeListFailed", "queryOperateTradeTypeList failed");
            log.error("PlatChannelDistributionPlanRequisitionService.queryOperateTradeTypeList failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<List<DistributionProductTradeTypeVO>> queryDistributionProductTradeTypeList() {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryOperateTradeTypeList");
        try {
            Map<String, String> tradeTypeMap = Lion.getMap(Environment.getAppName(), "plat.operate.distribution.product.trade.type", String.class);
            List<DistributionProductTradeTypeVO> voList = tradeTypeMap.keySet().stream().map(tradeType -> {
                TradeTypeEnum tradeTypeEnum = TradeTypeEnum.of(Integer.valueOf(tradeType));
                if (Objects.isNull(tradeTypeEnum)) {
                    return null;
                }
                return DistributionProductTradeTypeVO.builder().tradeType(Integer.valueOf(tradeType))
                        .tradeTypeDesc(tradeTypeMap.get(tradeType)).build();
            }).filter(Objects::nonNull).collect(Collectors.toList());
            transaction.setSuccessStatus();
            return RemoteResponse.success(voList);
        } catch (Exception e) {
            Cat.logEvent("queryOperateTradeTypeListFailed", "queryOperateTradeTypeList failed");
            log.error("PlatChannelDistributionPlanRequisitionService.queryOperateTradeTypeList failed", e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<List<DistributionChannelVO>> queryDistributionChannelList() {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryOperateChannelList");
        try {
            List<PlatDistributionChannel> channelConfigList = Lion.getList(Environment.getAppName(), "plat.operate.distribution.channel.config.list", PlatDistributionChannel.class);
            List<DistributionChannelVO> operateChannelList = channelConfigList.stream()
                    .filter(channelConfig -> !DistributionChannelEnum.UNKNOWN.equals(DistributionChannelEnum.fromCode(channelConfig.getCode())))
                    .map(channelConfig -> DistributionChannelVO.builder()
                            .channel(channelConfig.getCode())
                            .channelName(channelConfig.getName())
                            .build())
                    .collect(Collectors.toList());
            transaction.setSuccessStatus();
            return RemoteResponse.success(operateChannelList);
        } catch (Exception e) {
            Cat.logEvent("queryOperateChannelListFailed", "queryOperateChannelList failed");
            log.error("PlatChannelDistributionPlanRequisitionService.queryOperateChannelList failed", e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<RequisitionUploadVerifySignVO> queryRequisitionUploadVerifySign(RequisitionUploadVerifySignRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryRequisitionUploadVerifySign");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!PlatChannelDistributionPlanRequisitionValidator.validateRequisitionUploadVerifySignRequest(request, errorMsg)) {
                log.error("PlatChannelDistributionPlanRequisitionService.queryRequisitionUploadVerifySign illegal param:{}, errorMsg;{}", request, errorMsg);
                transaction.setStatus(errorMsg.toString());
                return RemoteResponse.fail(errorMsg.toString());
            }

            Integer expireMinutes = Lion.getInt(Environment.getAppName(), "distribution.plan.requisition.upload.verify.sign.expire.minutes", 3);
            MediaUploadSignReq signReq = MediaUploadSignReq.builder().expireMinutes(expireMinutes).build();
            MediaUploadSignBO uploadSign = mediaUploadAclService.getUploadSign(signReq);
            RequisitionUploadVerifySignVO signVO = RequisitionUploadVerifySignVO.builder()
                    .key(uploadSign.getKey())
                    .accessKey(uploadSign.getAwsAccessKeyId())
                    .policy(uploadSign.getPolicy())
                    .signature(uploadSign.getSignature()).build();
            transaction.setSuccessStatus();
            return RemoteResponse.success(signVO);
        } catch (Exception e) {
            Cat.logEvent("queryRequisitionUploadVerifySignFailed", "queryRequisitionUploadVerifySign failed");
            log.error("PlatChannelDistributionPlanRequisitionService.queryRequisitionUploadVerifySign failed,request:{}", JsonUtil.toJson(request), e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<List<BuInfoVO>> queryBuInfoByTradeType(BuInfoQueryRequest buInfoQueryRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryBuInfoByTradeType");
        try {
            if (buInfoQueryRequest == null) {
                return RemoteResponse.fail("请求参数不能为空");
            }
            if (buInfoQueryRequest.getTradeType() == null) {
                return RemoteResponse.fail("交易类型不能为空");
            }
            Response<List<BuDetailDTO>> response = requisitionQueryService.queryBuDetailList(buInfoQueryRequest.getTradeType());
            if (response == null || response.respFailed() || response.getData() == null) {
                return RemoteResponse.fail("查询bu信息失败");
            }
            List<BuInfo> buInfos = new ArrayList<>();
            response.getData().forEach(e -> buInfos.add(convert(e)));
            transaction.setSuccessStatus();
            return RemoteResponse.success(convertDto2Vo(buInfos));
        } catch (Exception e) {
            Cat.logEvent(CAT_TYPE, "queryBuInfoByTradeType failed");
            log.error("PlatChannelDistributionPlanRequisitionService.queryBuInfoByTradeType failed,request:{}", JsonUtil.toJson(buInfoQueryRequest), e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        }
    }

    private BuInfo convert(BuDetailDTO buDetailDTO) {
        return BuInfo.builder()
                .buId(buDetailDTO.getBuId())
                .buName(buDetailDTO.getBuName())
                .build();
    }

    private List<BuInfoVO> convertDto2Vo(List<BuInfo> buInfos) {
        List<BuInfoVO> buInfoVOS = new ArrayList<>();
        buInfos.forEach(e -> {
            buInfoVOS.add(BuInfoVO.builder()
                    .buId(e.getBuId())
                    .buName(e.getBuName())
                    .build()
            );
        });
        return buInfoVOS;
    }

    private RequisitionVO convertDto2Vo(RequisitionDTO dto) {
        return RequisitionVO.builder()
                .requisitionId(String.valueOf(dto.getRequisitionId()))
                .requisitionType(dto.getRequisitionType())
                .channel(dto.getChannel())
                .creator(dto.getCreatorId())
                .title(dto.getTitle())
                .submitTime(DateUtils.format(dto.getSubmitDate(), DateUtils.DEFAULT_FULL_PATTERN))
                .status(dto.getStatus())
                .build();
    }

    private RequisitionDetailVO convertRequisitionDetailDto2Vo(RequisitionDetailDTO dto,
                                                               List<HistoryDTO> historyDTOS,
                                                               List<RequisitionAuditNodeDTO> auditNodeDetailDTOList) {
        if (Objects.isNull(dto)) {
            return RequisitionDetailVO.builder().build();
        }
        RequisitionDetailVO.RequisitionDetailVOBuilder voBuilder = RequisitionDetailVO.builder()
                .requisitionId(String.valueOf(dto.getRequisitionId()))
                .background(dto.getBackground())
                .title(dto.getTitle())
                .requisitionType(dto.getRequisitionType())
                .channel(dto.getChannel())
                .tradeType(dto.getTradeType())
                .auditStatus(dto.getStatus());

        // 填充文件信息
        if (Objects.nonNull(dto.getDetailFile())) {
            String detailFileUrl = amazonS3AclService.getS3DownloadUrl(BUCKET_NAME, dto.getDetailFile().getDetailFileName());
            voBuilder.detailFileOverviewVo(RequisitionDetailFileOverviewVO.builder()
                    .detailFileUrl(detailFileUrl)
                    .totalCount(dto.getDetailFile().getTotalCount())
                    .succeedCount(dto.getDetailFile().getSucceedCount())
                    .failedCount(dto.getDetailFile().getFailedCount())
                    .importingCount(dto.getDetailFile().getImportingCount()).build());
        }

        // 构建审批流程节点
        List<RequisitionAuditNodeDetail> details = buildAuditNodeDetailList(historyDTOS, dto.getStatus(), auditNodeDetailDTOList);
        if (CollectionUtils.isEmpty(details)) {
            return voBuilder.build();
        }

        voBuilder.requisitionAuditNodeDetailList(details);
        return voBuilder.build();
    }


    private List<ProductCategoryTreeVO> convertToProductCategoryTreeVOList(List<GeneralCategoryTree> categoryTreeList) {
        return categoryTreeList.stream().filter(Objects::nonNull)
                .map(this::convertToProductCategoryTreeVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ProductCategoryTreeVO convertToProductCategoryTreeVO(GeneralCategoryTree categoryTree) {
        if (Objects.isNull(categoryTree)) {
            return null;
        }
        if (categoryTree.getLevel() == 1 && CollectionUtils.isEmpty(categoryTree.getChildList())) {
            return null;
        }
        if (categoryTree.getLevel() >= 3) {
            return null;
        }
        return ProductCategoryTreeVO.builder()
                .platCategoryId(categoryTree.getPlatformCategoryId())
                .categoryName(categoryTree.getCategoryName())
                .level(categoryTree.getLevel())
                .seq(categoryTree.getSeq())
                .childList(CollectionUtils.isNotEmpty(categoryTree.getChildList()) ?
                        convertToProductCategoryTreeVOList(categoryTree.getChildList()) : null)
                .build();
    }
}