package com.sankuai.carnation.distribution.privatelive.consultant.converter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.tp.deal.data.dto.DealDTO;
import com.dianping.tp.deal.data.dto.DealGroupComponentDTO;
import com.dianping.tp.deal.data.dto.DealGroupQueryDTO;
import com.dianping.tpfun.publish.api.sku.entity.ProductEntity;
import com.jayway.jsonpath.JsonPath;
import com.meituan.medicine.common.exception.utils.ExceptionUtils;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.OrderPerformanceDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.PrivateLiveConsultantOrderInfoDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.ProductDetailsDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantOrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderPinTuanStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.utils.DateUtils;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @data 2024/1/15 10:51
 */
@NoArgsConstructor
public class OrderPerformanceDTOConverter {

    private static final String PRE_PAY_PRICE = "prepayPrice";

    private static final String FINAL_PAY_PRICE = "finalPayPrice";

    private static final String HEAD_IMAGE = "headImage";

    private static final String HEAD_PICTURES = "headPictures";

    public static OrderPerformanceDTO converter(PrivateLiveConsultantOrderInfoDTO orderInfo) {
        OrderPerformanceDTO orderPerformanceDTO = new OrderPerformanceDTO();
        orderPerformanceDTO.setOrderId(String.valueOf(orderInfo.getLongOrderId()));
        orderPerformanceDTO.setOrderStatus(orderInfo.getStatus());
        double orderPrice = Objects.nonNull(orderInfo.getTotalMoney()) ? (double) orderInfo.getTotalMoney() / 100 : 0;
        orderPerformanceDTO.setOrderPrice(BigDecimal.valueOf(orderPrice).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        orderPerformanceDTO.setProductNum(orderInfo.getProductNum());
        orderPerformanceDTO.setOrderTime(DateUtils.format(orderInfo.getAddTime()));
        orderPerformanceDTO.setMtUserId(orderInfo.getMtUserId());
        orderPerformanceDTO.setOrderGroupStatus(orderInfo.getOrderGroupStatus());
        orderPerformanceDTO.setProductDetails(ProductDetailsDTO.builder().productId(orderInfo.getMtProductId())
                .productType(orderInfo.getProductType()).build());

        switch (ConsultantOrderStatusEnum.from(orderInfo.getStatus())) {
            case UNPAID:
                orderPerformanceDTO.setTipsText("转发该商品到微信，询问理由并促进支付");
                break;
            case PAID:
                orderPerformanceDTO.setTipsText("转发满单/满额礼海报到微信，帮助客户升单");
                break;
            case REFUND:
                orderPerformanceDTO.setTipsText("用户已退款成功，可微信/电话联系客户");
                break;
            default:
        }
        return orderPerformanceDTO;
    }

    public static void converterByPrepaid(List<ProductEntity> products, List<OrderPerformanceDTO> orderPerformanceDTOList) {
        products.forEach(product -> orderPerformanceDTOList.forEach(orderPerformanceDTO -> {
            if (product.getId() == orderPerformanceDTO.getProductDetails().getProductId()) {
                // 商品名称前缀增加拼团标识
                Optional.ofNullable(orderPerformanceDTO.getOrderGroupStatus())
                        .map(OrderPinTuanStatusEnum::from)
                        .map(statusEnum -> "【" + statusEnum.getShowText() + "】" + product.getName())
                        .ifPresent(title -> orderPerformanceDTO.getProductDetails().setOrderTitle(title));

                if (Objects.isNull(orderPerformanceDTO.getProductDetails().getOrderTitle())) {
                    orderPerformanceDTO.getProductDetails().setOrderTitle(product.getName());
                }

                orderPerformanceDTO.getProductDetails().setProductType(ProductTypeEnum.PREPAID.getValue());
                if (CollectionUtils.isNotEmpty(product.getItems())) {
                    // 平台价
                    product.getItems().stream().findFirst().ifPresent(item -> orderPerformanceDTO.getProductDetails()
                            .setPrice(item.getPrice().toPlainString()));
                    // 市场价
                    product.getItems().stream().findFirst().ifPresent(item -> orderPerformanceDTO.getProductDetails()
                            .setMarketPrice(item.getMarketPrice().toPlainString()));

                    product.getItems().stream().filter(item -> product.getId() == item.getProductId()).findFirst()
                            .flatMap(item -> Optional.ofNullable(item.getItemAttrs())).ifPresent(attrs -> {
                                // 预付金
                                attrs.stream().filter(attr -> PRE_PAY_PRICE.equals(attr.getAttrName())).findFirst()
                                        .ifPresent(attr -> orderPerformanceDTO.getProductDetails().setPrePayPrice(attr.getAttrValue()));
                                // 尾款
                                attrs.stream().filter(attr -> FINAL_PAY_PRICE.equals(attr.getAttrName())).findFirst()
                                        .ifPresent(attr -> orderPerformanceDTO.getProductDetails().setFinalPayPrice(attr.getAttrValue()));
                            });
                }
                if (CollectionUtils.isNotEmpty(product.getProductAttrEntities())) {
                    product.getProductAttrEntities().stream().filter(attr -> HEAD_PICTURES.equals(attr.getAttrName()))
                            .findFirst().ifPresent(attr -> {
                                List<String> pictures = JSONArray.parseArray(attr.getAttrValue()).toJavaList(String.class);
                                Optional.ofNullable(pictures).filter(CollectionUtils::isNotEmpty).flatMap(pictureList ->
                                        pictureList.stream().findFirst()).ifPresent(pictureUrl ->
                                        orderPerformanceDTO.getProductDetails().setProductPicture(determineEnvironment() + pictureUrl));
                            });
                }
            }
        }));
    }

    public static void converterByGroupBuy(List<DealGroupQueryDTO> dealGroupQueryDTOList, List<OrderPerformanceDTO> orderPerformanceDTOList) {
        dealGroupQueryDTOList.forEach(dealGroupQueryDTO -> orderPerformanceDTOList.forEach(orderPerformanceDTO -> {
            if (dealGroupQueryDTO.getId() == orderPerformanceDTO.getProductDetails().getProductId()) {
                // 商品名称前缀增加拼团标识
                Optional.ofNullable(orderPerformanceDTO.getOrderGroupStatus())
                        .map(OrderPinTuanStatusEnum::from)
                        .map(statusEnum -> "【" + statusEnum.getShowText() + "】" + dealGroupQueryDTO.getProductName().orNull())
                        .ifPresent(title -> orderPerformanceDTO.getProductDetails().setOrderTitle(title));

                if (Objects.isNull(orderPerformanceDTO.getProductDetails().getOrderTitle())) {
                    orderPerformanceDTO.getProductDetails().setOrderTitle(dealGroupQueryDTO.getProductName().orNull());
                }
                orderPerformanceDTO.getProductDetails().setProductType(ProductTypeEnum.GROUP_BUY.getValue());
                List<DealDTO> dealDTOList = dealGroupQueryDTO.getDeals().orNull();
                Optional.ofNullable(dealDTOList).flatMap(deals -> deals.stream().findFirst()).ifPresent(deal -> {
                    Optional.ofNullable(deal.getRetailPrice().orNull()).ifPresent(price -> orderPerformanceDTO.getProductDetails().setPrice(price.toPlainString()));
                    Optional.ofNullable(deal.getOriginalPrice().orNull()).ifPresent(price -> orderPerformanceDTO.getProductDetails().setMarketPrice(price.toPlainString()));
                });

                List<DealGroupComponentDTO> componentDTOList = dealGroupQueryDTO.getComponents().orNull();
                if (CollectionUtils.isNotEmpty(componentDTOList)) {
                    componentDTOList.stream().filter(component -> HEAD_IMAGE.equals(component.getComponentType())).findFirst()
                            .flatMap(component -> Optional.ofNullable(component.getData())).ifPresent(data -> {
                                String picture = null;
                                try {
                                    picture = JsonPath.read(data, "$.content[0].data[0].path");
                                } catch (Exception e) {
                                    ExceptionUtils.logError(OrderPerformanceDTOConverter.class.getSimpleName() + ".converterByGroupBuy", e, JSONObject.toJSONString(dealGroupQueryDTOList));
                                }
                                orderPerformanceDTO.getProductDetails().setProductPicture(picture);
                            });
                }
            }
        }));
    }

    public static String determineEnvironment() {
        // 根据环境判断返回的图片头
        boolean isTestEnvironment = determineTestEnvironment(); // 判断是否为测试环境
        if (isTestEnvironment) {
            return "https://p0.inf.test.sankuai.com";
        } else {
            return "https://p1.meituan.net";
        }
    }

    private static boolean determineTestEnvironment() {
        // 判断是否为测试环境的逻辑
        // 返回true表示是测试环境，返回false表示是生产环境
        return Environment.isTestEnv() ? true : false;
    }
}
