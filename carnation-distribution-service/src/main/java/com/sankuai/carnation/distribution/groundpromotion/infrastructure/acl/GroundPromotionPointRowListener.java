package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.groundpromotion.constants.enums.ExcelParseResultEnum;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPointRowStrBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/8/22
 * @Description:
 */
@Slf4j
public class GroundPromotionPointRowListener implements ReadListener<GroundPromotionPointRowStrBO> {

    @Getter
    private List<GroundPromotionPointRowStrBO> cachedDataList = Lists.newArrayList();

    private final int MAX_POINT_NAME = 30;

    @Override
    public void invoke(GroundPromotionPointRowStrBO groundPromotionPointRowStrBO, AnalysisContext analysisContext) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionPointRowListener.invoke(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPointRowStrBO,com.alibaba.excel.context.AnalysisContext)");
        if (!checkFormat(groundPromotionPointRowStrBO)) {
            groundPromotionPointRowStrBO.setLoadResult(ExcelParseResultEnum.FAIL.getDesc());
            groundPromotionPointRowStrBO.setFailReason("上传格式不正确");
        }
        // 默认值
        if (groundPromotionPointRowStrBO.getShowType() == null) {
            groundPromotionPointRowStrBO.setShowType("1");
        }
        if (groundPromotionPointRowStrBO.getMisIdList() == null) {
            groundPromotionPointRowStrBO.setMisIdList("");
        }
        cachedDataList.add(groundPromotionPointRowStrBO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionPointRowListener.doAfterAllAnalysed(com.alibaba.excel.context.AnalysisContext)");
    }

    private boolean checkFormat(GroundPromotionPointRowStrBO groundPromotionPointRowStrBO) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionPointRowListener.checkFormat(com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPointRowStrBO)");
        try {
            // cityId必须为数字
            Integer.parseInt(groundPromotionPointRowStrBO.getDpCityId());
            // 点位名称和门店id不能为空，且点位名称长度不能大于30
            if (StringUtils.isEmpty(groundPromotionPointRowStrBO.getPointName()) ||
                    groundPromotionPointRowStrBO.getPointName().length() > MAX_POINT_NAME ||
                    !groundPromotionPointRowStrBO.getDpShopIdList().matches("([\\d\\n])+")) {
                throw new RuntimeException();
            }
            // 门店id必须为数字
            Arrays.stream(groundPromotionPointRowStrBO.getDpShopIdList().split("\\r?\\n")).map(Long::parseLong);
            // 落地页展示方式必须为数字
            if (groundPromotionPointRowStrBO.getShowType() != null) {
                Integer.parseInt(groundPromotionPointRowStrBO.getShowType());
            }
            if (groundPromotionPointRowStrBO.getMisIdList() != null) {
                // 销售mis号必须以,分割
                Lists.newArrayList(groundPromotionPointRowStrBO.getMisIdList().split(","));
            }
        }catch (Exception e) {
            log.error("GroundPromotionPointRowStrBO param is invalid, param is {}, exception is {}", JSONObject.toJSON(groundPromotionPointRowStrBO), e);
            return false;
        }
        return true;
    }
}
