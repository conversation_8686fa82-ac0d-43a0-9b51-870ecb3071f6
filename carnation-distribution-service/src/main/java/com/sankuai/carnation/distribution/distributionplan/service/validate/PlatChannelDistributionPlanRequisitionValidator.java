package com.sankuai.carnation.distribution.distributionplan.service.validate;

import com.sankuai.carnation.distribution.distributionplan.enums.RequisitionTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.RequisitionUploadSceneEnum;
import com.sankuai.carnation.distribution.distributionplan.request.operate.*;
import com.sankuai.dzusergrowth.common.api.enums.DistributionChannelEnum;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@NoArgsConstructor
public class PlatChannelDistributionPlanRequisitionValidator {

    private static final int TITLE_MAX_LENGTH = 50;

    private static final int BACKGROUND_MAX_LENGTH = 500;

    private static final int CREATOR_NAME_MAX_LENGTH = 50;

    public static boolean validateRequisitionSubmitRequest(RequisitionSubmitRequest request, StringBuilder errorMsg) {
        if (!validateRequestBase(request, errorMsg)) {
            return false;
        }
        if (StringUtils.isBlank(request.getTitle())) {
            errorMsg.append("申请单标题不能为空");
            return false;
        }
        if (request.getTitle().length() > TITLE_MAX_LENGTH) {
            errorMsg.append("申请单标题不能超过50个字符");
            return false;
        }
        if (StringUtils.isBlank(request.getDetailFile())) {
            errorMsg.append("详情文件不能为空");
            return false;
        }
        if (StringUtils.isBlank(request.getBackground())) {
            errorMsg.append("申请单背景不能为空");
            return false;
        }
        if (request.getBackground().length() > BACKGROUND_MAX_LENGTH) {
            errorMsg.append("申请单背景不能超过500个字符");
            return false;
        }
        return validateChannel(request.getChannel(), errorMsg) &&
                validateRequisitionType(request.getRequisitionType(), errorMsg) &&
                validateTradeType(request.getTradeType(), errorMsg);
    }

    public static boolean validateRequisitionPageQueryRequest(RequisitionPageQueryRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validatePageParameters(request.getPageNo(), request.getPageSize(), errorMsg) &&
                validateChannel(request.getChannel(), errorMsg) &&
                validateRequisitionType(request.getRequisitionType(), errorMsg) &&
                validateCreator(request.getCreator(), errorMsg) &&
                validateSubmitTime(request.getSubmitBeginTime(), request.getSubmitEndTime(), errorMsg);
    }

    public static boolean validateRequisitionTemplateDownloadLinkRequest(RequisitionTemplateDownloadLinkRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validateRequisitionType(request.getRequisitionType(), errorMsg);
    }

    public static boolean validateRequisitionDetailLoadRequest(RequisitionDetailRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validateRequisitionId(request.getRequisitionId(), errorMsg);
    }

    public static boolean validateProductCategoryTreeQueryRequest(ProductCategoryTreeQueryRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validateTradeType(request.getTradeType(), errorMsg);
    }

    public static boolean validatePlatChannelDistributionPlanPageQueryRequest(PlatChannelDistributionPlanPageQueryRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validateChannelList(request.getChannelList(), errorMsg) &&
                validatePageParameters(request.getPageNo(), request.getPageSize(), errorMsg) &&
                validateTradeType(request.getTradeType(), errorMsg);
    }

    public static boolean validateDistributionPlanHistoryPageQueryRequest(DistributionPlanHistoryPageQueryRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validatePlanId(request.getPlanId(), errorMsg) &&
                validatePageParameters(request.getPageNo(), request.getPageSize(), errorMsg);
    }

    public static boolean validateRequisitionUploadVerifySignRequest(RequisitionUploadVerifySignRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg) &&
                validateUploadScene(request.getUploadScene(), errorMsg);
    }

    private static boolean validateRequestBase(Object request, StringBuilder errorMsg) {
        if (Objects.isNull(request)) {
            errorMsg.append("请求参数不能为空");
            return false;
        }
        return true;
    }

    public static boolean validateChannel(String channel, StringBuilder errorMsg) {
        if (StringUtils.isBlank(channel)) {
            errorMsg.append("分销渠道不能为空");
            return false;
        }

        DistributionChannelEnum distributionChannelEnum = DistributionChannelEnum.fromCode(channel);
        if (distributionChannelEnum.equals(DistributionChannelEnum.UNKNOWN)) {
            errorMsg.append("无效的分销渠道");
            return false;
        }
        if (!distributionChannelEnum.equals(DistributionChannelEnum.QUAN_QUAN)) {
            errorMsg.append("分销渠道暂不支持");
            return false;
        }
        return true;
    }

    public static boolean validateChannelList(List<String> channelList, StringBuilder errorMsg) {
        if (CollectionUtils.isEmpty(channelList)) {
            errorMsg.append("分销渠道不能为空");
            return false;
        }
        List<DistributionChannelEnum> distributionChannelEnumList = channelList.stream()
                .map(DistributionChannelEnum::fromCode).collect(Collectors.toList());
        for (DistributionChannelEnum distributionChannelEnum : distributionChannelEnumList) {
            if (distributionChannelEnum.equals(DistributionChannelEnum.UNKNOWN)) {
                errorMsg.append("分销渠道不存在");
                return false;
            }
        }
        return true;
    }


    public static boolean validatePageParameters(Integer pageNo, Integer pageSize, StringBuilder errorMsg) {
        if (Objects.isNull(pageNo) || pageNo <= 0) {
            errorMsg.append("分页参数错误");
            return false;
        }
        if (Objects.isNull(pageSize) || pageSize <= 0) {
            errorMsg.append("分页参数错误");
            return false;
        }
        return true;
    }

    public static boolean validateRequisitionId(String requisitionId, StringBuilder errorMsg) {
        if (StringUtils.isBlank(requisitionId) || "0".equals(requisitionId)) {
            errorMsg.append("申请单ID不能为空");
            return false;
        }
        return true;
    }

    public static boolean validateRequisitionType(Integer requisitionType, StringBuilder errorMsg) {
        if (Objects.isNull(requisitionType)) {
            errorMsg.append("申请单类型不能为空");
            return false;
        }

        RequisitionTypeEnum requisitionTypeEnum = RequisitionTypeEnum.fromCode(requisitionType);
        if (Objects.isNull(requisitionTypeEnum)) {
            errorMsg.append("无效的申请单类型");
            return false;
        }
        return true;
    }

    public static boolean validateTradeType(Integer tradeType, StringBuilder errorMsg) {
        if (Objects.isNull(tradeType)) {
            errorMsg.append("交易类型不能为空");
            return false;
        }

        TradeTypeEnum tradeTypeEnum = TradeTypeEnum.of(tradeType);
        if (Objects.isNull(tradeTypeEnum)) {
            errorMsg.append("交易类型不存在");
            return false;
        }
        if (!tradeTypeEnum.equals(TradeTypeEnum.GROUPBUY_PAY)) {
            errorMsg.append("交易类型暂不支持");
            return false;
        }
        return true;
    }

    public static boolean validatePlanId(Long planId, StringBuilder errorMsg) {
        if (Objects.isNull(planId) || planId <= 0) {
            errorMsg.append("分销计划ID不能为空");
            return false;
        }
        return true;
    }

    private static boolean validateUploadScene(Integer uploadScene, StringBuilder errorMsg) {
        if (Objects.isNull(uploadScene)) {
            errorMsg.append("上传场景不能为空");
            return false;
        }
        if (Objects.isNull(RequisitionUploadSceneEnum.fromCode(uploadScene))) {
            errorMsg.append("无效的上传场景");
            return false;
        }
        return true;
    }

    private static boolean validateCreator(String creator, StringBuilder errorMsg) {
        if (StringUtils.isBlank(creator)) {
            return true;
        }
        if (creator.length() > CREATOR_NAME_MAX_LENGTH) {
            errorMsg.append("申请人名称不能超过50个字符");
            return false;
        }
        return true;
    }

    private static boolean validateSubmitTime(Long submitBeginTime, Long submitEndTime, StringBuilder errorMsg) {
        if (Objects.isNull(submitBeginTime) && Objects.isNull(submitEndTime)) {
            return true;
        }
        if (Objects.isNull(submitBeginTime) || Objects.isNull(submitEndTime)) {
            errorMsg.append("申请开始时间与结束时间需同时填写");
            return false;
        }

        if (submitBeginTime > submitEndTime) {
            errorMsg.append("申请开始时间不能大于结束时间");
            return false;
        }
        return true;
    }
}