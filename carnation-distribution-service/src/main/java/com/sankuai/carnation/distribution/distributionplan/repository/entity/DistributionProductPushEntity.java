package com.sankuai.carnation.distribution.distributionplan.repository.entity;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class DistributionProductPushEntity {

    /**
     * 方案id
     */
    private Long id;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 计划结束时间
     */
    private Date onlineTime;

    /**
     * 结束时间
     */
    private Date offlineTime;

    /**
     * 推送结果
     * 上层应该是弄一个Map<String, String>
     */
    private String pushResult;

    /**
     * 商品推送状态
     */
    private Integer status;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}