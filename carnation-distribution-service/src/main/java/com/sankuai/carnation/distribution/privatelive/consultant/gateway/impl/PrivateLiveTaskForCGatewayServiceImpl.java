package com.sankuai.carnation.distribution.privatelive.consultant.gateway.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.beauty.arch.fundamental.threadpool.ResizableCapacityThreadPoolExecutorFactory;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.customer.PrivateSphereUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.service.PrivateLiveAnchorAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.constant.CacheCategoryConstant;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.*;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.account.WeChatLoginInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PrivateLiveConsultantBaseRequest;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.ConsultantTaskSummaryDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.RankConditionDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.RankDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.ConsultantTaskApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.LiveStatusMapEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.TaskDataDetailModuleEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.WxType;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.ConsultantUserInvalidException;
import com.sankuai.carnation.distribution.privatelive.consultant.exception.WeChatUserInvalidException;
import com.sankuai.carnation.distribution.privatelive.consultant.gateway.PrivateLiveTaskForCGatewayService;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserTypeCount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveUserIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService;
import com.sankuai.carnation.distribution.privatelive.consultant.service.WeChatUserService;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.UserAuthorizeUtil;
import com.sankuai.dz.srcm.pchat.service.ScrmLiveManageService;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTagStatDTO;
import com.sankuai.dz.srcm.user.dto.CustomerCapitalTagStatRequest;
import com.sankuai.dz.srcm.user.dto.PrivateSphereCustomerCapitalOverview;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomsQueryRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.live.LiveStatusEnum;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.enums.saasconfig.TraceAbilityTypeEnum;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginCustomerInfo;
import com.sankuai.dzrtc.privatelive.operation.api.dto.LoginUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/11
 * @Description:
 */
@Slf4j
@Service
public class PrivateLiveTaskForCGatewayServiceImpl implements PrivateLiveTaskForCGatewayService {

    @Autowired
    private UserAuthorizeUtil userAuthorizeUtil;

    @Autowired
    private PrivateLiveConsultantAccountRepository accountRepository;

    @Autowired
    private PrivateLiveConsultantTaskRepository taskRepository;

    @Autowired
    private PrivateLiveConsultantSummaryService summaryService;

    @Autowired
    private PrivateSphereUserAclService privateSphereUserAclService;

    @Autowired
    private RedisStoreClient redisStoreClient;

    @Autowired
    private PrivateLiveUserIntentionResultRepository userIntentionResultRepository;

    @Autowired
    private PrivateLiveAnchorAclService privateLiveAnchorAclService;

    @Autowired
    private WeChatUserService weChatUserService;

    @Autowired
    private PrivateLiveAclService privateLiveAclService;

    @Autowired
    private ScrmLiveManageService scrmLiveManageService;

    private static final ThreadPoolExecutor EXECUTOR = ResizableCapacityThreadPoolExecutorFactory
            .create("PrivateLiveRankingForCGatewayService_Pool", 100, 500, 10, TimeUnit.SECONDS, 1000);

    /**
     *
     * @param request
     * @return
     */
    @Override
    public RemoteResponse<PrivateLiveTaskBaseInfoDTO> queryBaseInfo(PrivateLiveConsultantBaseRequest request,
            String wxToken) {
        if (request == null || request.getAnchorId() == null
                || StringUtils.isBlank(wxToken)) {
            log.error("queryBaseInfo 参数错误");
            throw new IllegalArgumentException("参数错误");
        }
        RemoteResponse<WeChatLoginInfo> weChatUserInfo = weChatUserService.getWeChatUserInfoByToken(wxToken);
        if (weChatUserInfo == null || weChatUserInfo.getData() == null) {
            log.error("queryBaseInfo 获取用户信息失败");
            return RemoteResponse.fail("获取用户信息失败");
        }
        StoreKey storeKey = new StoreKey(CacheCategoryConstant.ANCHOR_INFO_CATEGORY, request.getAnchorId());
        LoginUserInfo loginUserInfoCache = redisStoreClient.get(storeKey);
        if (loginUserInfoCache == null) {
            preloadShopInfo(request.getAnchorId(), storeKey);
        }
        CompletableFuture<LoginUserInfo> loginUserInfoCompletableFuture = loginUserInfoCache == null
                ? privateLiveAnchorAclService.queryAnchorInfoByIdWithoutShop(request.getAnchorId())
                : CompletableFuture.completedFuture(loginUserInfoCache);
        // 分页限制20条，根据
        CompletableFuture<List<LiveRoomInfo>> liveRoomsCompletableFuture = ObjectUtils.isEmpty(request)?
                 CompletableFuture.completedFuture(Lists.newArrayList()):privateLiveAclService
                .queryLiveRooms(privateLiveConsultantBaseRequestToLiveRoomsQueryRequest(request));
        CompletableFuture.allOf(loginUserInfoCompletableFuture, liveRoomsCompletableFuture).join();
        try {
            PrivateLiveTaskBaseInfoDTO privateLiveTaskBaseInfoDTO = new PrivateLiveTaskBaseInfoDTO();
            LoginUserInfo loginUserInfo = loginUserInfoCompletableFuture.join();
            LoginCustomerInfo loginCustomerInfo = loginUserInfo.getCustomerInfo();
            if (loginCustomerInfo != null) {
                PrivateLiveEnterpriseInfoDTO enterpriseInfoDTO = new PrivateLiveEnterpriseInfoDTO();
                enterpriseInfoDTO.setEnterpriseId(String.valueOf(loginCustomerInfo.getCustomerId()));
                enterpriseInfoDTO.setEnterpriseName(loginCustomerInfo.getCustomerName());
                // 单独提供接口读缓存
//                    enterpriseInfoDTO.setShops(loginCustomerInfo.getShopInfo().stream().map(lc -> {
//                        PrivateLiveShopDTO shopDTO = new PrivateLiveShopDTO();
//                        shopDTO.setDpShopId(lc.getShopId());
//                        shopDTO.setShopName(lc.getShopName());
//                        return shopDTO;
//                    }).collect(Collectors.toList()));
                privateLiveTaskBaseInfoDTO.setEnterpriseInfo(enterpriseInfoDTO);
            }
            List<LiveRoomInfo> liveRoomInfos = liveRoomsCompletableFuture.join().stream()
                    .filter(ll -> ll.getLiveId().equals(request.getLiveId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(liveRoomInfos)) {
                LiveRoomInfo liveRoomInfo = CollectionUtil.getFirst(liveRoomInfos);
                privateLiveTaskBaseInfoDTO.setLiveId(liveRoomInfo.getLiveId());
                privateLiveTaskBaseInfoDTO.setLiveName(liveRoomInfo.getTitle());
            }
            log.info("queryBaseInfo 查询成功 privateLiveTaskBaseInfoDTO:{}", JSONObject.toJSONString(privateLiveTaskBaseInfoDTO));
            return RemoteResponse.success(privateLiveTaskBaseInfoDTO);
        } catch (Exception e) {
            Cat.logError("queryBaseInfo 查询失败", e);
            log.error("queryBaseInfo 查询失败", e);
        }
        return RemoteResponse.fail("查询失败");
    }

    /**
     * 查询或获取默认的私密直播任务详情
     *
     * @param taskId 任务ID
     * @param wxToken 微信令牌
     * @return 远程响应对象，包含私密直播任务详情
     */
    @Override
    public RemoteResponse<PrivateLiveTaskDetailDTO> queryOrDefaultPrivateLiveTaskDetail(Long taskId, String wxToken) {
        log.info("queryOrDefaultPrivateLiveTaskDetail taskId:{}, wxToken:{}", taskId, wxToken);
        try {
            PrivateLiveTaskDetailDTO privateLiveTaskDetailDTO = new PrivateLiveTaskDetailDTO();
            if (taskId == null || taskId <= 0) {
                taskId = getDefaultConsultantTaskId(wxToken);
            }
            PrivateLiveConsultantApplicantDTO applicantDTO = userAuthorizeUtil.authorizeAndGetConsultantInfo(taskId,
                    wxToken);
            if (applicantDTO != null
                    && ConsultantTaskApproveStatusEnum.PASS.getCode().equals(applicantDTO.getApproveStatus())) {
                BeanUtils.copyProperties(applicantDTO, privateLiveTaskDetailDTO);
                privateLiveTaskDetailDTO.setCurrent(Objects.equals(taskId, applicantDTO.getConsultantTaskId()));
                privateLiveTaskDetailDTO.setFlag(
                        Objects.requireNonNull(LiveStatusMapEnum.getByCode(applicantDTO.getStatus())).getFlag());
                TraceAbilityTypeEnum traceConfig = privateLiveAclService.queryLiveTraceConfig(applicantDTO.getLiveId());
                // 个人溯源
                if (traceConfig != null && TraceAbilityTypeEnum.PERSON.equals(traceConfig)) {
                    privateLiveTaskDetailDTO.setTraceType(traceConfig.getCode());
                    privateLiveTaskDetailDTO.setWeChatPersonalPicUrl(Lion.getString(Environment.getAppName(), "person.guide.pic", "https://img.meituan.net/dpmobile/7fb5f78efff1508ea9300d2839f675cf94761.png.webp"));
                } else {
                    privateLiveTaskDetailDTO.setTraceType(TraceAbilityTypeEnum.GROUP.getCode());
                    // 社群溯源
                    try{
                        CompletableFuture<String> groupQrCodeUrlFuture = queryGroupCodeUrl(applicantDTO.getConsultantTaskId(),
                                applicantDTO.getLiveId());
                        groupQrCodeUrlFuture.thenAccept(privateLiveTaskDetailDTO::setWeChatGroupQrCodeUrl);
                    } catch (Exception e) {
                        log.error("queryOrDefaultPrivateLiveTaskDetail queryGroupCodeUrl error", e);
                    }
                }
            } else {
                if (applicantDTO != null) {
                    BeanUtils.copyProperties(applicantDTO, privateLiveTaskDetailDTO);
                    privateLiveTaskDetailDTO.setCurrent(Objects.equals(taskId, applicantDTO.getConsultantTaskId()));
                    privateLiveTaskDetailDTO.setFlag(
                            Objects.requireNonNull(LiveStatusMapEnum.getByCode(applicantDTO.getStatus())).getFlag());

                }
                privateLiveTaskDetailDTO
                        .setApproveStatus(applicantDTO != null && applicantDTO.getApproveStatus() != null
                                ? applicantDTO.getApproveStatus() : ConsultantTaskApproveStatusEnum.UN_APPLY.getCode());
            }
            log.info("queryOrDefaultPrivateLiveTaskDetail privateLiveTaskDetailDTO:{}", JSONObject.toJSONString(privateLiveTaskDetailDTO));
            return RemoteResponse.success(privateLiveTaskDetailDTO);
        } catch (WeChatUserInvalidException we) {
            log.error("查询或获取默认的私密直播任务详情失败", we);
            return RemoteResponse.<PrivateLiveTaskDetailDTO>custom().setCode(300) // 设置自定义的code
                    .setMsg(we.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        } catch (ConsultantUserInvalidException ce) {
            log.error("查询或获取默认的私密直播任务详情失败", ce);
            return RemoteResponse.<PrivateLiveTaskDetailDTO>custom().setCode(301) // 设置自定义的code
                    .setMsg(ce.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            return RemoteResponse.fail("获取任务详情失败");
        }
    }

    private void preloadShopInfo(Long anchorId, StoreKey storeKey) {
        // 预加载门店信息
        privateLiveAnchorAclService.queryAnchorInfoById(anchorId);
    }

    private LiveRoomsQueryRequest
            privateLiveConsultantBaseRequestToLiveRoomsQueryRequest(PrivateLiveConsultantBaseRequest request) {
        LiveRoomsQueryRequest liveRoomsQueryRequest = new LiveRoomsQueryRequest();
        liveRoomsQueryRequest.setMainAnchorId(request.getAnchorId());
        liveRoomsQueryRequest.setStatus(Arrays.asList(LiveStatusEnum.values()));
        liveRoomsQueryRequest.setPageNo(1);
        liveRoomsQueryRequest.setPageSize(20);
        liveRoomsQueryRequest.setLiveId(request.getLiveId());
        return liveRoomsQueryRequest;
    }

    /**
     * 查询私域直播任务数据详情
     *
     * @param taskId 任务ID
     * @param wxToken 微信令牌
     * @return 远程响应对象，包含私域直播任务数据详情
     */
    @Override
    public RemoteResponse<List<PrivateLiveConsultantTaskDataDetailDTO>>
            queryPrivateLiveConsultantTaskDataDetail(Long taskId, String wxToken) {
        try {
            if (taskId == null || taskId <= 0 || StringUtils.isBlank(wxToken)) {
                return RemoteResponse.fail("参数错误");
            }
            PrivateLiveConsultantApplicantDTO applicantDTO = userAuthorizeUtil.authorize(taskId, wxToken);
            return RemoteResponse.success(getDataDetailListConditional(applicantDTO));
        } catch (WeChatUserInvalidException we) {
            log.error("查询私域直播任务数据详情失败", we);
            return RemoteResponse.<List<PrivateLiveConsultantTaskDataDetailDTO>>custom().setCode(300) // 设置自定义的code
                    .setMsg(we.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        } catch (ConsultantUserInvalidException ce) {
            log.error("查询私域直播任务数据详情失败", ce);
            return RemoteResponse.<List<PrivateLiveConsultantTaskDataDetailDTO>>custom().setCode(301) // 设置自定义的code
                    .setMsg(ce.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        } catch (Exception e) {
            log.error("获取活动数据详情异常", e);
            return RemoteResponse.fail("获取活动数据详情异常");
        }
    }

    /**
     * 查询咨询师任务门店列表（支持门店名称模糊查询）
     *
     * @param anchorId 主播ID
     * @param filter 排序字段
     * @param wxToken 微信令牌
     * @return 远程响应对象
     */
    @Override
    public RemoteResponse<List<PrivateLiveShopDTO>> queryShopListByTaskWithFilter(Long anchorId, String filter,
            String wxToken) {
        try {
            if (anchorId == null || StringUtils.isEmpty(wxToken)) {
                return RemoteResponse.fail("主播ID或微信令牌不能为空");
            }
            WeChatLoginInfo weChatLoginInfo = userAuthorizeUtil.authorizeWeChatInfo(wxToken);
            if (weChatLoginInfo == null) {
                return RemoteResponse.fail("无效的微信令牌");
            }
            StoreKey storeKey = new StoreKey(CacheCategoryConstant.ANCHOR_INFO_CATEGORY, anchorId);
            LoginUserInfo userInfo = redisStoreClient.get(storeKey);
            CompletableFuture<LoginUserInfo> userInfoFuture = userInfo == null
                    ? privateLiveAnchorAclService.queryAnchorInfoById(anchorId)
                    : CompletableFuture.completedFuture(userInfo);
            CompletableFuture.allOf(userInfoFuture);
            if (userInfoFuture.join() == null || userInfoFuture.join().getCustomerInfo() == null
                    || userInfoFuture.join().getCustomerInfo().getShopInfo() == null) {
                log.error("门店信息列表为空 LoginUserInfo = {}", JSONObject.toJSONString(userInfoFuture.join()));
                return RemoteResponse.success(new ArrayList<>());
            }
            List<PrivateLiveShopDTO> shopInfoDTOS = userInfoFuture.join().getCustomerInfo().getShopInfo().stream()
                    .filter(s -> {
                        if (StringUtils.isEmpty(filter)) {
                            return true;
                        } else {
                            return s.getShopName().contains(filter);
                        }
                    }).map(shopInfo -> {
                        PrivateLiveShopDTO shopInfoDTO = new PrivateLiveShopDTO();
                        shopInfoDTO.setDpShopId(shopInfo.getShopId());
                        shopInfoDTO.setShopName(shopInfo.getShopName());
                        return shopInfoDTO;
                    }).collect(Collectors.toList());
            return RemoteResponse.success(shopInfoDTOS);
        } catch (WeChatUserInvalidException weChatUserInvalidException) {
            log.error("PrivateLivePermissionGatewayService#queryConsultantTaskApplicantListPage.error,",
                    weChatUserInvalidException);
            return RemoteResponse.<List<PrivateLiveShopDTO>>custom().setCode(300) // 设置自定义的code
                    .setMsg(weChatUserInvalidException.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        } catch (Exception e) {
            log.error("获取门店信息失败", e);
            return RemoteResponse.fail("获取门店信息失败");
        }
    }

    private CompletableFuture<List<PrivateLiveConsultantTask>> queryConsultantTaskByLiveId(String liveId) {
        return CompletableFuture.completedFuture(taskRepository.pageLoadByLiveId(liveId, 0, 10000))
                .handle((result, throwable) -> {
                    if (throwable != null || CollectionUtils.isEmpty(result)) {
                        return Collections.emptyList();
                    }
                    return result.stream()
                            .filter(r -> ConsultantTaskApproveStatusEnum.PASS.getCode().equals(r.getStatus()))
                            .collect(Collectors.toList());
                });
    }

    private void setRanking(List<PrivateLiveConsultantRankDTO> rankDTOList) {
        int index = 0;
        for (PrivateLiveConsultantRankDTO rankDTO : rankDTOList) {
            index++;
            rankDTO.setRanking(index);
        }
    }

    public CompletableFuture<RemoteResponse<RankDTO>> queryRankByConditionAsync(RankConditionDTO rankConditionDTO) {
        return CompletableFuture.supplyAsync(() -> summaryService.queryRankByCondition(rankConditionDTO), EXECUTOR)
                .exceptionally(throwable -> {
                    log.error("查询排行榜数据错误", throwable);
                    return RemoteResponse.fail(throwable.getMessage());
                });
    }

    /**
     * 点击下挂横幅
     *
     * @param taskId 任务ID
     * @param wxToken 微信令牌
     * @param followTipId 关注提示ID
     * @return 远程响应对象，包含点击关注提示的结果
     */
    @Override
    public RemoteResponse<Boolean> followTipClick(Long taskId, String wxToken, Integer followTipId, String count) {
        try {
            if (!TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum.containId(followTipId)
                    || !NumberUtils.isDigits(count)) {
                return RemoteResponse.fail("参数错误");
            }
            PrivateLiveConsultantApplicantDTO applicantDTO = userAuthorizeUtil.authorize(taskId, wxToken);
            if (applicantDTO == null) {
                return RemoteResponse.fail("用户未授权");
            }
            return RemoteResponse.success(redisStoreClient.set(getClickStoreKey(taskId, followTipId), count));
        } catch (WeChatUserInvalidException we) {
            log.error("PrivateLivePermissionGatewayService#followTipClick.error,", we);
            return RemoteResponse.<Boolean>custom().setCode(300) // 设置自定义的code
                    .setMsg(we.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        } catch (ConsultantUserInvalidException ce) {
            log.error("PrivateLivePermissionGatewayService#followTipClick.error,", ce);
            return RemoteResponse.<Boolean>custom().setCode(301) // 设置自定义的code
                    .setMsg(ce.getMessage()) // 设置消息
                    .setData(null) // 设置数据
                    .build();
        }
    }

    @NotNull
    private static StoreKey getClickStoreKey(Long taskId, Integer followTipId) {
        return new StoreKey(CacheCategoryConstant.CONSULTANT_TASK_FOLLOW_TIP_CLICK_CATEGORY, taskId, followTipId);
    }

    public List<PrivateLiveConsultantTaskDataDetailDTO>
            getDataDetailListConditional(PrivateLiveConsultantApplicantDTO applicantDTO) {
        if (applicantDTO.getStatus() != null && applicantDTO.getConsultantTaskId() != null
                && StringUtils.isNotBlank(applicantDTO.getLiveId())) {
            CompletableFuture<RemoteResponse<ConsultantTaskSummaryDTO>> taskSummaryDTORemoteResponse = querySummaryByConsultantTaskIdAsync(
                    applicantDTO.getConsultantTaskId());
            CompletableFuture<List<PrivateLiveUserTypeCount>> userTypeCountcompletableFuture = queryConsultantTaskUserTypeCountAsync(
                    applicantDTO.getLiveId(), null);
            RankConditionDTO rankConditionDTO = new RankConditionDTO();
            rankConditionDTO.setLiveId(applicantDTO.getLiveId());
            CompletableFuture<RemoteResponse<RankDTO>> rankCompleteFuture = queryRankByConditionAsync(rankConditionDTO);
            CompletableFuture<String> hotProductFuture = queryHotProductByLiveIdAsync(applicantDTO.getLiveId());
            CompletableFuture<PrivateSphereCustomerCapitalOverview> overviewCompletableFuture = privateSphereUserAclService
                    .queryCustomerCapitalOverview(applicantDTO.getConsultantTaskId(), applicantDTO.getLiveId());
            CompletableFuture.allOf(taskSummaryDTORemoteResponse, userTypeCountcompletableFuture, rankCompleteFuture,
                    hotProductFuture, overviewCompletableFuture);
            LiveStatusMapEnum liveStatusMapEnum = LiveStatusMapEnum.getByCode(applicantDTO.getStatus());
            ConsultantTaskSummaryDTO summaryDTO = taskSummaryDTORemoteResponse.join() == null ? null
                    : taskSummaryDTORemoteResponse.join().getData();
            List<PrivateLiveUserTypeCount> userTypeCountList = userTypeCountcompletableFuture.join();
            RankDTO rankDTO = rankCompleteFuture.join() == null ? null : rankCompleteFuture.join().getData();
            switch (liveStatusMapEnum) {
                case NOT_START:
                    return getPrivateLiveConsultantTaskDataDetailDTOS(applicantDTO, liveStatusMapEnum, summaryDTO,
                            userTypeCountList, rankDTO, overviewCompletableFuture,
                            Comparator.comparing(TaskDataDetailModuleEnum::getBeforeOrder), hotProductFuture);
                case LIVE:
                    return getPrivateLiveConsultantTaskDataDetailDTOS(applicantDTO, liveStatusMapEnum, summaryDTO,
                            userTypeCountList, rankDTO, overviewCompletableFuture,
                            Comparator.comparing(TaskDataDetailModuleEnum::getLiveOrder), hotProductFuture);
                case END:
                    return TaskDataDetailModuleEnum.getTaskDataDetailModuleEnumsOnConditionSorted(liveStatusMapEnum)
                            .stream().sorted(Comparator.comparing(TaskDataDetailModuleEnum::getAfterOrder))
                            .map(moduleEnum -> consultantTaskDataDetailDTO(applicantDTO, liveStatusMapEnum, moduleEnum,
                                    summaryDTO, null, userTypeCountList, rankDTO, hotProductFuture.join()))
                            .filter(Objects::nonNull).collect(Collectors.toList());
                case EXPIRED:
                    return TaskDataDetailModuleEnum.getTaskDataDetailModuleEnumsOnConditionSorted(LiveStatusMapEnum.END)
                            .stream().sorted(Comparator.comparing(TaskDataDetailModuleEnum::getAfterOrder))
                            .map(moduleEnum -> consultantTaskDataDetailDTO(applicantDTO, liveStatusMapEnum, moduleEnum,
                                    summaryDTO, null, userTypeCountList, rankDTO, hotProductFuture.join()))
                            .filter(Objects::nonNull).collect(Collectors.toList());
                default:
                    return null;
            }
        } else {
            return Collections.emptyList();
        }
    }

    @NotNull
    private List<PrivateLiveConsultantTaskDataDetailDTO> getPrivateLiveConsultantTaskDataDetailDTOS(
            PrivateLiveConsultantApplicantDTO applicantDTO, LiveStatusMapEnum liveStatusMapEnum,
            ConsultantTaskSummaryDTO summaryDTO, List<PrivateLiveUserTypeCount> userTypeCountList, RankDTO rankDTO,
            CompletableFuture<PrivateSphereCustomerCapitalOverview> overviewLiveCompletableFuture,
            Comparator<TaskDataDetailModuleEnum> comparing, CompletableFuture<String> hotProductFuture) {
        return TaskDataDetailModuleEnum.getTaskDataDetailModuleEnumsOnConditionSorted(liveStatusMapEnum).stream()
                .sorted(comparing)
                .map(moduleEnum -> consultantTaskDataDetailDTO(applicantDTO, liveStatusMapEnum, moduleEnum, summaryDTO,
                        overviewLiveCompletableFuture.join(), userTypeCountList, rankDTO, hotProductFuture.join()))
                .collect(Collectors.toList());
    }

    private Long getDefaultConsultantTaskId(String wxToken) {
        WeChatLoginInfo weChatLoginInfo = userAuthorizeUtil.authorizeWeChatInfo(wxToken);
        if (weChatLoginInfo != null) {
            PrivateLiveConsultantAccount account = accountRepository.loadByUnionId(weChatLoginInfo.getUnionId());
            if (account == null) {
                return null;
            } else {
                List<PrivateLiveConsultantTask> taskDTOList = taskRepository.loadByConsultantId(account.getId());
                if (CollectionUtils.isNotEmpty(taskDTOList)) {
                    Optional<PrivateLiveConsultantTask> taskOptional = taskDTOList.stream()
                            .filter(t -> ConsultantTaskApproveStatusEnum.PASS.getCode().equals(t.getStatus()))
                            .max(Comparator.comparing(PrivateLiveConsultantTask::getUpdateTime));
                    return taskOptional.map(PrivateLiveConsultantTask::getId).orElse(null);
                }
            }
        }
        return null;
    }

    private PrivateLiveConsultantTaskDataDetailDTO consultantTaskDataDetailDTO(
            PrivateLiveConsultantApplicantDTO applicantDTO, LiveStatusMapEnum liveStatusMapEnum,
            TaskDataDetailModuleEnum moduleEnum, ConsultantTaskSummaryDTO summaryDTO,
            PrivateSphereCustomerCapitalOverview capitalOverview,
            List<PrivateLiveUserTypeCount> privateLiveUserTypeCount, RankDTO rankDTO, String hotProductFuture) {
        PrivateLiveConsultantTaskDataDetailDTO privateLiveConsultantTaskDataDetailDTO = new PrivateLiveConsultantTaskDataDetailDTO();
        privateLiveConsultantTaskDataDetailDTO.setModuleTitle(moduleEnum.getTitle());
        privateLiveConsultantTaskDataDetailDTO.setClickText(moduleEnum.getClickText());
        privateLiveConsultantTaskDataDetailDTO.setModuleId(moduleEnum.getModuleId());
        if (CollectionUtils.isNotEmpty(moduleEnum.getFollowEnums())) {
            privateLiveConsultantTaskDataDetailDTO.setFollowTips(moduleEnum.getFollowEnums().stream()
                    .map(followTips -> constructFollowTips(followTips, summaryDTO, capitalOverview, applicantDTO,
                            privateLiveUserTypeCount, hotProductFuture))
                    .filter(Objects::nonNull).collect(Collectors.toList()));
        }
        switch (moduleEnum) {
            case FOLLOW_ORDER:
                // 追单升单-我的排名
                String gmv = "0";
                String orderAmt = "0";
                String unPaid = "0";
                if (rankDTO != null && CollectionUtils.isNotEmpty(rankDTO.getRankItemDTOList())) {
                    Optional<ConsultantTaskSummaryDTO> summaryDTOOptional = rankDTO.getRankItemDTOList().stream()
                            .filter(r -> applicantDTO.getConsultantTaskId().equals(r.getConsultantTaskId()))
                            .findFirst();
                    if (summaryDTOOptional.isPresent()) {
                        privateLiveConsultantTaskDataDetailDTO
                                .setModuleSubTitle(String.valueOf(summaryDTOOptional.get().getRankNum()));
                        gmv = summaryDTO.getGmvAmt() != null ? calcMoneyDivideHundred(summaryDTO.getGmvAmt()) : "0";
                        orderAmt = summaryDTO.getPayOrderCnt() != null ? summaryDTO.getPayOrderCnt().toString() : "0";
                        unPaid = String.valueOf((summaryDTO.getWaitToPayOrderCnt() != null
                                ? summaryDTO.getWaitToPayOrderCnt() : 0)
                                + (summaryDTO.getCancelOrderCnt() != null ? summaryDTO.getCancelOrderCnt() : 0));
                    }
                }
                privateLiveConsultantTaskDataDetailDTO
                        .setDataDetails(TaskDataDetailModuleEnum.getDataDetailList(moduleEnum, gmv, orderAmt, unPaid));
                break;
            case FOLLOW_CUSTOMER:
                String hingIntention = (capitalOverview != null && capitalOverview.getHighIntentionSize() != null)
                        ? capitalOverview.getHighIntentionSize().toString() : "0";
                String midIntention = (capitalOverview != null && capitalOverview.getMidIntentionSize() != null)
                        ? capitalOverview.getMidIntentionSize().toString() : "0";
                String lowIntention = (capitalOverview != null && capitalOverview.getLowIntentionSize() != null)
                        ? capitalOverview.getLowIntentionSize().toString() : "0";
                privateLiveConsultantTaskDataDetailDTO.setModuleSubTitle(moduleEnum.getSubTitle());
                privateLiveConsultantTaskDataDetailDTO.setDataDetails(TaskDataDetailModuleEnum
                        .getDataDetailList(moduleEnum, hingIntention, midIntention, lowIntention));
                break;
            case INVITE_GROUP:
                List<PrivateLiveUserTypeCount> myCount = privateLiveUserTypeCount == null ? Collections.emptyList()
                        : privateLiveUserTypeCount.stream()
                                .filter(c -> c.getConsultantTaskId().equals(applicantDTO.getConsultantTaskId()))
                                .collect(Collectors.toList());
                // 根据ConsultantTaskId对myCount进行聚合操作，相同ConsultantTaskId的count进行相加，返回新的PrivateLiveUserTypeCount
                Map<Long, Integer> totalMap = privateLiveUserTypeCount == null ? new HashMap<>()
                        : privateLiveUserTypeCount.stream()
                                .collect(Collectors.groupingBy(PrivateLiveUserTypeCount::getConsultantTaskId,
                                        Collectors.reducing(0, PrivateLiveUserTypeCount::getCount, Integer::sum)));
                CustomerCapitalTagStatRequest request = new CustomerCapitalTagStatRequest();
                request.setLiveId(applicantDTO.getLiveId());
                request.setConsultantTaskId(applicantDTO.getConsultantTaskId());
                CustomerCapitalTagStatDTO statDTO = privateSphereUserAclService.queryLiveAppointNum(request);
                // 对totalMap进行降序排序，并根据ConsultantTaskId获取在totalMap的排名
                List<Map.Entry<Long, Integer>> entryList = totalMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder())).collect(Collectors.toList());
                Integer rank = totalMap.getOrDefault(applicantDTO.getConsultantTaskId(), null);
                if (rank != null) {
                    rank = entryList.indexOf(new AbstractMap.SimpleEntry<>(applicantDTO.getConsultantTaskId(), rank))
                            + 1;
                }
                privateLiveConsultantTaskDataDetailDTO.setModuleSubTitle(rank == null ? "0" : rank.toString());
                Integer total = totalMap.get(applicantDTO.getConsultantTaskId());
                PrivateLiveUserTypeCount seedCount = myCount.stream().filter(c -> c.getType() == 1).findFirst()
                        .orElse(null);
                // 邀人进群总人数、种子人数、预约人数
                privateLiveConsultantTaskDataDetailDTO.setDataDetails(
                        TaskDataDetailModuleEnum.getDataDetailList(moduleEnum, total == null ? "0" : total.toString(),
                                seedCount == null ? "0" : seedCount.getCount().toString(), statDTO == null ? "0" : String.valueOf(statDTO.getUserCount())));
                break;
            case FOLLOW_PERFORM:
                privateLiveConsultantTaskDataDetailDTO.setModuleSubTitle(moduleEnum.getSubTitle());
                Integer verifyOrderCnt = summaryDTO == null || summaryDTO.getVerifyOrderCnt() == null ? 0 : summaryDTO.getVerifyOrderCnt();
                Integer refundCount = (((summaryDTO == null || summaryDTO.getRefundOrderCnt() == null) ? 0
                        : summaryDTO.getRefundOrderCnt())
                        + (summaryDTO == null || summaryDTO.getInRefundOrderCnt() == null ? 0
                        : summaryDTO.getInRefundOrderCnt()));
                int payOrderCount = (summaryDTO == null || summaryDTO.getPayOrderCnt() == null) ? 0 : Math.max(summaryDTO.getPayOrderCnt() - (verifyOrderCnt + refundCount), 0);
                privateLiveConsultantTaskDataDetailDTO
                        .setDataDetails(TaskDataDetailModuleEnum.getDataDetailList(moduleEnum,
                                Integer.toString(payOrderCount),
                                verifyOrderCnt.toString(),
                                String.valueOf(refundCount)));
                break;
            default:
                privateLiveConsultantTaskDataDetailDTO = null;
        }
        return privateLiveConsultantTaskDataDetailDTO;
    }

    private PrivateLiveConsultantTaskDataDetailDTO.FollowTips constructFollowTips(
            TaskDataDetailModuleEnum.TaskDataDetailModuleFollowEnum taskDataDetailModuleFollowEnum,
            ConsultantTaskSummaryDTO summaryDTO, PrivateSphereCustomerCapitalOverview capitalOverview,
            PrivateLiveConsultantApplicantDTO applicantDTO, List<PrivateLiveUserTypeCount> privateLiveUserTypeCount,
            String hotProduct) {
        PrivateLiveConsultantTaskDataDetailDTO.FollowTips followTips = new PrivateLiveConsultantTaskDataDetailDTO.FollowTips();
        String cacheCount = summaryDTO == null ? null : redisStoreClient
                .get(getClickStoreKey(summaryDTO.getConsultantTaskId(), taskDataDetailModuleFollowEnum.getId()));
        int cacheCountValue = cacheCount != null ? Integer.parseInt(cacheCount) : 0;
        switch (taskDataDetailModuleFollowEnum) {
            case LOST_FOLLOW:
                if (summaryDTO == null
                        || ((summaryDTO.getWaitToPayOrderCnt() == null || summaryDTO.getWaitToPayOrderCnt() <= 0)
                                && (summaryDTO.getCancelOrderCnt() == null || summaryDTO.getCancelOrderCnt() <= 0))) {
                    return null;
                }
                int unPaidNum = (summaryDTO.getWaitToPayOrderCnt() != null ? summaryDTO.getWaitToPayOrderCnt() : 0)
                        + (summaryDTO.getCancelOrderCnt() != null ? summaryDTO.getCancelOrderCnt() : 0);
                followTips.setFollowTitle(String.format(taskDataDetailModuleFollowEnum.getTitle(), unPaidNum,
                        calcMoneyDivideHundred(
                                (summaryDTO.getWaitToPayGmvAmt() == null ? 0 : summaryDTO.getWaitToPayGmvAmt())
                                        + (summaryDTO.getCancelGmvAmt() == null ? 0 : summaryDTO.getCancelGmvAmt()))));
                followTips.setId(taskDataDetailModuleFollowEnum.getId());
                followTips.setDataDesc(taskDataDetailModuleFollowEnum.getDataDesc());
                // 图片
                followTips.setUserPics(null);
                followTips.setButtonText(taskDataDetailModuleFollowEnum.getButtonText());
                followTips.setCurrentTotalCount(unPaidNum);
                followTips.setFollowCount(Math.max(unPaidNum - cacheCountValue, 0));
                followTips.setFollowType(taskDataDetailModuleFollowEnum.getFollowType());
                break;
            case PAID_FOLLOW:
                if (summaryDTO == null || summaryDTO.getPayOrderCnt() == null || summaryDTO.getPayOrderCnt() <= 0) {
                    return null;
                }
                followTips.setFollowTitle(
                        String.format(taskDataDetailModuleFollowEnum.getTitle(), summaryDTO.getPayOrderCnt()));
                followTips.setId(taskDataDetailModuleFollowEnum.getId());
                followTips.setDataDesc(taskDataDetailModuleFollowEnum.getDataDesc());
                // 图片
                followTips.setUserPics(null);
                followTips.setButtonText(taskDataDetailModuleFollowEnum.getButtonText());
                followTips.setCurrentTotalCount(summaryDTO.getPayOrderCnt());
                followTips.setFollowCount(Math.max(summaryDTO.getPayOrderCnt() - cacheCountValue, 0));
                followTips.setFollowType(taskDataDetailModuleFollowEnum.getFollowType());
                break;
            case HOT_FOLLOW:
                if (StringUtils.isBlank(hotProduct)) {
                    return null;
                }
                followTips.setFollowTitle(taskDataDetailModuleFollowEnum.getTitle());
                followTips.setId(taskDataDetailModuleFollowEnum.getId());
                // 热卖商品
                followTips.setDataDesc(hotProduct);
                followTips.setUserPics(null);
                followTips.setButtonText(taskDataDetailModuleFollowEnum.getButtonText());
                followTips.setFollowType(taskDataDetailModuleFollowEnum.getFollowType());
                break;
            case HIGH_INTENTION_FOLLOW:
                if (capitalOverview == null || capitalOverview.getHighIntentionSize() == null
                        || capitalOverview.getHighIntentionSize() <= 0) {
                    return null;
                }
                followTips.setFollowTitle(taskDataDetailModuleFollowEnum.getTitle());
                followTips.setId(taskDataDetailModuleFollowEnum.getId());
                followTips.setDataDesc(String.format(taskDataDetailModuleFollowEnum.getDataDesc(),
                        capitalOverview.getHighIntentionSize()));
                followTips.setUserPics(null);
                followTips.setButtonText(taskDataDetailModuleFollowEnum.getButtonText());
                followTips.setFollowType(taskDataDetailModuleFollowEnum.getFollowType());
                followTips.setCurrentTotalCount(capitalOverview.getHighIntentionSize().intValue());
                followTips.setFollowCount(
                        Math.max(capitalOverview.getHighIntentionSize().intValue() - cacheCountValue, 0));
                break;
            case SEED_FOLLOW:
                Optional<PrivateLiveUserTypeCount> seedCountOptional = privateLiveUserTypeCount.stream().filter(
                        c -> c.getConsultantTaskId().equals(applicantDTO.getConsultantTaskId()) && c.getType() == 1)
                        .findFirst();
                followTips.setFollowTitle(taskDataDetailModuleFollowEnum.getTitle());
                followTips.setId(taskDataDetailModuleFollowEnum.getId());
                // 种子人数
                followTips.setDataDesc(String.format(taskDataDetailModuleFollowEnum.getDataDesc(),
                        seedCountOptional.isPresent() ? seedCountOptional.get().getCount() : 0));
                followTips.setUserPics(null);
                followTips.setButtonText(taskDataDetailModuleFollowEnum.getButtonText());
                followTips.setFollowType(taskDataDetailModuleFollowEnum.getFollowType());
                followTips.setCurrentTotalCount(seedCountOptional.isPresent() ? seedCountOptional.get().getCount() : 0);
                // 种子人数
                followTips.setFollowCount(Math.max(
                        (seedCountOptional.isPresent() ? seedCountOptional.get().getCount() : 0) - cacheCountValue, 0));
                int wxType = queryPrivateLiveWxType(applicantDTO.getLiveId());
                followTips.setWxType(wxType);
                break;
            case UN_VERIFY_FOLLOW:
                if (summaryDTO == null || summaryDTO.getWaitToVerifyOrderCnt() == null
                        || summaryDTO.getWaitToVerifyOrderCnt() <= 0) {
                    return null;
                }
                followTips.setFollowTitle(taskDataDetailModuleFollowEnum.getTitle());
                followTips.setId(taskDataDetailModuleFollowEnum.getId());
                followTips.setDataDesc(String.format(taskDataDetailModuleFollowEnum.getDataDesc(),
                        summaryDTO.getWaitToVerifyOrderCnt()));
                followTips.setUserPics(null);
                followTips.setButtonText(taskDataDetailModuleFollowEnum.getButtonText());
                followTips.setFollowType(taskDataDetailModuleFollowEnum.getFollowType());
                followTips.setCurrentTotalCount(summaryDTO.getWaitToVerifyOrderCnt());
                followTips.setFollowCount(Math.max(summaryDTO.getWaitToVerifyOrderCnt() - cacheCountValue, 0));
                break;
            default:
                followTips = null;
                break;
        }
        return followTips;
    }

    private CompletableFuture<String> queryHotProductByLiveIdAsync(String liveId) {
        return CompletableFuture.completedFuture(summaryService.queryHotProductByLiveId(liveId).getData())
                .exceptionally(throwable -> {
                    log.error("queryHotProductByLiveIdAsync error", throwable);
                    return null;
                });
    }

    private CompletableFuture<String> queryGroupCodeUrl(Long taskId, String liveId) {
        return CompletableFuture
                .completedFuture(privateSphereUserAclService.getGroupInfoByConsultantTaskId(taskId, liveId))
                .handle((result, throwable) -> {

                    try {
                        if (throwable != null) {
                            log.error("queryGroupCodeUrl error", throwable);
                        } else if (result != null && result.get() != null) {
                            return result.get().getQrCodeUrl();
                        }
                    } catch (Exception  e) {
                        log.error("queryGroupCodeUrl error", e);
                        return null;
                    }
                    return null;
                });
    }

    private CompletableFuture<List<PrivateLiveUserTypeCount>> queryConsultantTaskUserTypeCountAsync(String liveId,
            List<Long> taskIdList) {
        return CompletableFuture
                .supplyAsync(() -> userIntentionResultRepository.countUserTypeByConsultantTaskId(liveId, taskIdList),
                        EXECUTOR)
                .exceptionally(throwable -> {
                    if (throwable != null) {
                        log.error("queryUserCount error", throwable);
                    }
                    return null;
                });
    }

    private CompletableFuture<RemoteResponse<ConsultantTaskSummaryDTO>>
            querySummaryByConsultantTaskIdAsync(Long consultantTaskId) {
        return CompletableFuture
                .supplyAsync(() -> summaryService.querySummaryByConsultantTaskId(consultantTaskId), EXECUTOR)
                .exceptionally(throwable -> {
                    if (throwable != null) {
                        log.error("querySummaryByConsultantTaskId error", throwable);
                    }
                    return null;
                });
    }

    public static String calcMoneyDivideHundred(Long money) {
        if (money == null) {
            return null;
        }
        return String.format("%.2f", (double)money / 100);
    }

    private int queryPrivateLiveWxType(String liveId) {
        int defaultWxType = WxType.PERSONAL_WECHAT.getCode();
        if (StringUtils.isEmpty(liveId)) {
            return defaultWxType;
        }
        try {
            RemoteResponse<Integer> response = scrmLiveManageService.queryLiveWxType(liveId);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("queryPrivateLiveWxType error, liveId={}", liveId, e);
        }
        return defaultWxType;
    }

}
