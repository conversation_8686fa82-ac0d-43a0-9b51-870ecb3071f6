package com.sankuai.carnation.distribution.privatelive.consultant.repository.service;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.privatelive.consultant.acl.wechat.WechatUserAclService;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantAccountExample;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantAccountMapper;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.miniprogram.WechatUserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/12/30
 **/
@Slf4j
@Service
public class PrivateLiveConsultantAccountRepository {

    @Autowired
    private PrivateLiveConsultantAccountMapper privateLiveConsultantAccountMapper;

    @Autowired
    @Qualifier("medicalRedisClient")
    private RedisStoreClient redisStoreClient;

    @Autowired
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    @Autowired
    private WechatUserAclService wechatUserAclService;

    private static final String CONSULTANT_USER_INFO_REDIS_KEY = "consultant_user_info";

    private static final int CONSULTANT_USER_INFO_EXPIRE_TIME = 30 * 24 * 60 * 60;

    public long insert(PrivateLiveConsultantAccount privateLiveConsultantAccount) {
        privateLiveConsultantAccountMapper.insertSelective(privateLiveConsultantAccount);
        return privateLiveConsultantAccount.getId();
    }

    public long update(PrivateLiveConsultantAccount privateLiveConsultantAccount) {
        privateLiveConsultantAccountMapper.updateByPrimaryKeySelective(privateLiveConsultantAccount);
        return privateLiveConsultantAccount.getId();
    }

    public PrivateLiveConsultantAccount loadByUnionId(String unionId) {
        PrivateLiveConsultantAccountExample privateLiveConsultantAccountExample = new PrivateLiveConsultantAccountExample();
        privateLiveConsultantAccountExample.createCriteria().andUnionIdEqualTo(unionId);
        List<PrivateLiveConsultantAccount> list = privateLiveConsultantAccountMapper.selectByExample(privateLiveConsultantAccountExample);
        return list.isEmpty() ? null : list.get(0);
    }

    public PrivateLiveConsultantAccount loadByUnionIdAndOenId(String unionId,String openId) {
        PrivateLiveConsultantAccountExample privateLiveConsultantAccountExample = new PrivateLiveConsultantAccountExample();
        privateLiveConsultantAccountExample.createCriteria()
                .andUnionIdEqualTo(unionId)
                .andOpenIdEqualTo(openId)
        ;
        List<PrivateLiveConsultantAccount> list = privateLiveConsultantAccountMapper.selectByExample(privateLiveConsultantAccountExample);
        return list.isEmpty() ? null : list.get(0);
    }

    public PrivateLiveConsultantAccount loadById(long consultantId) {
        PrivateLiveConsultantAccountExample privateLiveConsultantAccountExample = new PrivateLiveConsultantAccountExample();
        privateLiveConsultantAccountExample.createCriteria().andIdEqualTo(consultantId);
        List<PrivateLiveConsultantAccount> list = privateLiveConsultantAccountMapper.selectByExample(privateLiveConsultantAccountExample);
        return list.isEmpty() ? null : list.get(0);
    }

    public List<PrivateLiveConsultantAccount> batchLoadById(List<Long> consultantIds) {
        PrivateLiveConsultantAccountExample privateLiveConsultantAccountExample = new PrivateLiveConsultantAccountExample();
        privateLiveConsultantAccountExample.createCriteria().andIdIn(consultantIds);
        return privateLiveConsultantAccountMapper.selectByExample(privateLiveConsultantAccountExample);
    }

    public PrivateLiveConsultantAccount loadOpenId(String unionId) {
        PrivateLiveConsultantAccountExample privateLiveConsultantAccountExample = new PrivateLiveConsultantAccountExample();
        privateLiveConsultantAccountExample.createCriteria().andOpenIdEqualTo(unionId);
        List<PrivateLiveConsultantAccount> list = privateLiveConsultantAccountMapper.selectByExample(privateLiveConsultantAccountExample);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 获取咨询师相关的userId信息
     */
    public Map<Long, Long> batchLoadConsultantUserId(String liveId) {
        try {
            // 查询这个直播间所有的咨询师
            List<PrivateLiveConsultantTask> consultantTasks = privateLiveConsultantTaskRepository.loadLiveAllConsultant(liveId);
            if (CollectionUtils.isEmpty(consultantTasks)) {
                return Maps.newHashMap();
            }

            // 从缓存中捞取之前查询到的咨询师userId的信息
            Map<Long, Long> hasExistConsultantUser = loadConsultantUserIdFromRedis(liveId);
            List<Long> needSearchConsultantIds = consultantTasks.stream()
                    .map(PrivateLiveConsultantTask::getConsultantId)
                    .collect(Collectors.toList());
            if (MapUtils.isNotEmpty(hasExistConsultantUser)) {
                needSearchConsultantIds = needSearchConsultantIds
                        .stream()
                        .filter(e -> !hasExistConsultantUser.containsKey(e))
                        .collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(needSearchConsultantIds)) {
                return hasExistConsultantUser;
            }

            // 获取咨询师的unionId信息
            List<PrivateLiveConsultantAccount> consultantAccounts = batchLoadById(needSearchConsultantIds);
            List<String> unionIds = consultantAccounts.stream()
                    .map(PrivateLiveConsultantAccount::getUnionId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(unionIds)) {
                return hasExistConsultantUser;
            }

            // 对于不在缓存中的咨询师信息，根据unionId获取userId信息
            Map<Long, Long> needUpdateConsultantUserInfo = loadConsultantUserIdFromRpc(unionIds, consultantAccounts);

            // 更新缓存
            Map<Long, Long> newestConsultantUserInfo = Maps.newHashMap();
            newestConsultantUserInfo.putAll(needUpdateConsultantUserInfo);
            if (MapUtils.isNotEmpty(hasExistConsultantUser)) {
                newestConsultantUserInfo.putAll(hasExistConsultantUser);
            }

            // 更新缓存
            if (!refreshConsultantUserIdInRedis(liveId, newestConsultantUserInfo)) {
                log.error("batchLoadConsultantUserId, ser redis fail, liveId: {}", liveId, new RuntimeException("缓存更新失败"));
            }
            return newestConsultantUserInfo;
        } catch (Exception e) {
            log.error("batchLoadConsultantUserId, ser redis fail, liveId: {}", liveId, e);
            return Maps.newHashMap();
        }
    }

    private Map<Long, Long> loadConsultantUserIdFromRedis(String liveId) {
        StoreKey storeKey = new StoreKey(CONSULTANT_USER_INFO_REDIS_KEY, liveId);
        return redisStoreClient.get(storeKey);
    }

    private Map<Long, Long> loadConsultantUserIdFromRpc(List<String> unionIds, List<PrivateLiveConsultantAccount> consultantAccounts) {
        List<WechatUserInfoDTO> wechatUserInfoDTOS = wechatUserAclService.batchQueryByUnionIds(unionIds);
        Map<String, Long> unionId2UserId = wechatUserInfoDTOS.stream()
                .collect(Collectors.toMap(WechatUserInfoDTO::getUnionId,WechatUserInfoDTO::getMtUserId, (v1, v2) -> v1));
        Map<Long, String> consultantId2UnionId = consultantAccounts.stream()
                .collect(Collectors.toMap(PrivateLiveConsultantAccount::getId, PrivateLiveConsultantAccount::getUnionId, (v1, v2) -> v1));

        Map<Long, Long> consultantUserInfo = Maps.newHashMap();
        for (Long consultantId : consultantId2UnionId.keySet()) {
            if (StringUtils.isNotEmpty(consultantId2UnionId.get(consultantId)) && unionId2UserId.get(consultantId2UnionId.get(consultantId)) != null) {
                consultantUserInfo.put(consultantId, unionId2UserId.get(consultantId2UnionId.get(consultantId)));
            }
        }
        return  consultantUserInfo;
    }

    private boolean  refreshConsultantUserIdInRedis(String liveId, Map<Long, Long> latestConsultantUserId) {
        StoreKey storeKey = new StoreKey(CONSULTANT_USER_INFO_REDIS_KEY, liveId);
        return redisStoreClient.set(storeKey, latestConsultantUserId, CONSULTANT_USER_INFO_EXPIRE_TIME);
    }


}
