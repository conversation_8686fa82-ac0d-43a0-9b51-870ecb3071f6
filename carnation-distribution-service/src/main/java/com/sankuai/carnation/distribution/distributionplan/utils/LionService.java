package com.sankuai.carnation.distribution.distributionplan.utils;

import com.dianping.lion.client.Lion;
import com.dianping.lion.client.fileconfig.FileConfigClient;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Component
public class LionService {

    /**
     * 从lion文件配置上读取流文件
     * @param fileName
     * todo 这个io异常上层需要直接感知到io异常吗，还是用业务异常包装更合适
     * @return
     * @throws IOException
     */
    public Workbook getFile(String fileName) throws IOException {
        //创建时需指定文件配置所属appkey
        FileConfigClient fileConfigClient = Lion.createFileConfigClient("com.sankuai.medicalcosmetology.distribution.service");

        //指定文件名获取文件内容（UTF-8编码的文本文件）
        InputStream fileStream = fileConfigClient.getFileStream(fileName);

        return WorkbookFactory.create(fileStream);
    }
}
