package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.deal.flow.MountService;
import com.dianping.deal.flow.dto.PoiMountDTO;
import com.dianping.deal.flow.request.PoiMountQueryRequest;
import com.dianping.deal.flow.response.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.distributionplan.acl.PoiMountAcl;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PoiMountAclImpl implements PoiMountAcl {

    private final static String CAT_TYPE = PoiMountAcl.class.getSimpleName();

    @Resource
    private MountService mountService;

    private static final int MT_POI_ID_LIST_MAX_SIZE = 50;

    private static final Map<DistributionBusinessChannelEnum, Long> DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP = Maps.newHashMap();

    static {
        DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.put(DistributionBusinessChannelEnum.KUAISHOU_E_COMMERCE, 10011L);
    }

    @Override
    public Map<Long, String> batchGetMtPoiMountPoiId(List<Long> mtPoiIds, DistributionBusinessChannelEnum channel) {
        Map<Long, String> mtPoiIdToMountPoiIdMap = Maps.newHashMap();
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "batchGetMtPoiMountPoiId");
        try {
            if (CollectionUtils.isEmpty(mtPoiIds) || !DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.containsKey(channel)) {
                throw new RuntimeException("IllegalParam");
            }

            List<List<Long>> mtPoiIdFragment = Lists.partition(mtPoiIds, MT_POI_ID_LIST_MAX_SIZE);

            PoiMountQueryRequest request = new PoiMountQueryRequest();
            request.setChannelId(DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.get(channel));
            for (List<Long> poiIds : mtPoiIdFragment) {
                request.setMtPoiIds(poiIds);
                Response<List<PoiMountDTO>> response = mountService.getAuditedPoiMountByMtPoiIds(request);
                if (null == response || !response.isSuccess()) {
                    log.error("PoiMountAcl.batchGetMtPoiMountStatus invoke failed, poiIds:{}, channel:{}", poiIds, channel);
                    throw new RuntimeException("InvokeGetPoiMountByMtPoiIdsFailed");
                }
                if (CollectionUtils.isEmpty(response.getData())) {
                    Cat.logEvent("GetPoiMountByMtPoiIdsEmpty", "返回为空,poiIds:" + poiIds);
                    log.info("PoiMountAcl.batchGetMtPoiMountStatus found empty result, poiIds:{}, channel:{}", poiIds, channel);
                    continue;
                }
                response.getData().stream().filter(Objects::nonNull).forEach(poiMount -> mtPoiIdToMountPoiIdMap.put(poiMount.getMtPoiId(), poiMount.getThirdPartyPoiId()));
            }

            return mtPoiIdToMountPoiIdMap;
        } catch (Exception e) {
            log.error("PoiMountAcl.getAuditedPoiInfoByMtPoiIds failed, mtPoiIds:{}, channel:{}", mtPoiIds, channel, e);
            transaction.setStatus(e);
            return mtPoiIdToMountPoiIdMap;
        } finally {
            transaction.complete();
        }
    }
}
