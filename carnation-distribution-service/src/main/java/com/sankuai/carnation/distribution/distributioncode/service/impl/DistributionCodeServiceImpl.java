package com.sankuai.carnation.distribution.distributioncode.service.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.carnation.distribution.distributioncode.dto.*;
import com.sankuai.carnation.distribution.distributioncode.facade.WeiBoDistributionCodeFacade;
import com.sankuai.carnation.distribution.distributioncode.facade.model.WeiBoDistributionCode;
import com.sankuai.carnation.distribution.distributioncode.service.DistributionCodeService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@MdpPigeonServer
@Slf4j
public class DistributionCodeServiceImpl implements DistributionCodeService {

    private static final String CAT_TYPE = DistributionCodeService.class.getSimpleName();

    @Resource
    private WeiBoDistributionCodeFacade weiBoDistributionCodeFacade;

    private static final Map<String, String> THIRD_PARTY_CHANNEL_TO_BUSINESS_CHANNEL_MAP = Maps.newHashMap();

    static {
        THIRD_PARTY_CHANNEL_TO_BUSINESS_CHANNEL_MAP.put("WEIBO", DistributionBusinessChannelEnum.WEI_BO.getCode());
    }

    @Override
    public RemoteResponse<CpsInfo> generateCpsChain(ProductCpsChainGenerateRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "generateCpsChain");
        try {
            if (null == request || StringUtils.isBlank(request.getPid()) || StringUtils.isBlank(request.getUrl())
                    || StringUtils.isBlank(THIRD_PARTY_CHANNEL_TO_BUSINESS_CHANNEL_MAP.get(request.getChannel()))) {
                log.error("DistributionCodeService.generateCpsChain param error, request:{}", request);
                Cat.logEvent("generateCpsChainParamError", String.format("request:%s", request));
                transaction.setStatus("generateCpsChainParamError");
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }

            CpsInfo cpsInfo = weiBoDistributionCodeFacade.generateCpsChain(request);
            return RemoteResponse.success(cpsInfo);
        } catch (RuntimeException re) {
            Cat.logEvent("generateCpsChainFailed", String.format("%s,request:%s", re.getMessage(), request));
            log.info("DistributionCodeService.generateCpsChain failed, request:{}, errorMsg:{}", request, re.getMessage(), re);
            transaction.setStatus(re);
            return RemoteResponse.fail(re.getMessage());
        } catch (Exception e) {
            log.error("DistributionCodeService.generateCpsChain failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<Boolean> checkDistributionCodeValid(DistributionCodeCheckRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "checkDistributionCodeValid");
        try {
            if (null == request || StringUtils.isBlank(request.getDistributionCode())
                    || null == request.getChannel() || null == request.getProductId() || request.getProductId() <= 0L
                    || null == request.getProductType()) {
                Cat.logEvent("checkDistributionCodeParamError", String.format("request:%s", request));
                log.error("DistributionCodeService.checkProductDistributionPlanValid param error, request:{}", request);
                transaction.setStatus("checkDistributionCodeParamError");
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }
            boolean checkResult = weiBoDistributionCodeFacade.checkDistributionCodeValid(request);
            if (!checkResult) {
                transaction.setStatus("InvalidDistributionCode");
            }
            return RemoteResponse.success(checkResult);
        } catch (Exception e) {
            log.error("DistributionCodeService.checkDistributionCodeValid failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<List<DistributionCommissionStrategyDTO>> batchQueryByDistributionCodes(DistributionCodeParseRequest request) {
        try {
            if (null == request || StringUtils.isBlank(request.getChannel()) || !request.getChannel().equals(DistributionBusinessChannelEnum.WEI_BO.getCode())
                    || CollectionUtils.isEmpty(request.getDistributionCodes())) {
                Cat.logEvent("batchQueryByDistributionCodesParamError", String.format("request:%s", request));
                log.error("DistributionCodeService.batchQueryByDistributionCodes param error, request:{}", request);
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }

            List<WeiBoDistributionCode> weiBoDistributionCodes = weiBoDistributionCodeFacade.batchParseDistributionCode(request.getDistributionCodes());
            if (CollectionUtils.isEmpty(weiBoDistributionCodes)) {
                throw new RuntimeException("distributionCodesAllNotFound");
            }

            return RemoteResponse.success(weiBoDistributionCodes.stream().map(this::buildDistributionCommissionStrategyDto).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("DistributionCodeService.checkDistributionCodeValid failed, request:{}", request, e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        }
    }

    private DistributionCommissionStrategyDTO buildDistributionCommissionStrategyDto(WeiBoDistributionCode weiBoDistributionCode) {
        DistributionCommissionStrategyDTO distributionCommissionStrategy = new DistributionCommissionStrategyDTO();
        distributionCommissionStrategy.setDistributionCode(weiBoDistributionCode.getDistributionCode());
        distributionCommissionStrategy.setDistributorCommissionRate(DistributionPlanDO.calCommissionRateToThousandths(weiBoDistributionCode.getCommissionStrategy().getDistributorCommissionRate()));
        distributionCommissionStrategy.setThirdChannelCommissionRate(DistributionPlanDO.calCommissionRateToThousandths(weiBoDistributionCode.getCommissionStrategy().getChannelCommissionRate()));
        return distributionCommissionStrategy;
    }
}