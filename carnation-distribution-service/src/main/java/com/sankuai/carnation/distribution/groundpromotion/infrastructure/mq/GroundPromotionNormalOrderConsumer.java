package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.dztrade.dto.tradeEvent.GeneralEventNotifyDTO;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.common.beans.OrderStatusMessageDTO;
import com.dianping.pay.order.common.enums.AmountType;
import com.dianping.pay.order.domain.beans.OrderPaymentDetailDTO;
import com.dianping.pay.order.domain.enums.OrderStatusAction;
import com.dianping.pay.order.domain.enums.ProductEnum;
import com.dianping.pay.unifiedorder.api.common.enums.UnifiedOrderField;
import com.dianping.pay.unifiedorder.process.create.api.dto.CreateOrderSKU;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.swallow.common.message.Destination;
import com.dianping.swallow.common.message.Message;
import com.dianping.swallow.consumer.Consumer;
import com.dianping.swallow.consumer.ConsumerConfig;
import com.dianping.swallow.consumer.impl.ConsumerFactoryImpl;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.ProductTypeUtils;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelTaskStatusEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.running.OrderChannelTaskRunner;
import com.sankuai.carnation.distribution.intention.domain.calculate.utils.ProductTypeAnalyser;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelRunningTaskDataService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderOperateEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.MathUtils;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveOrderIntentionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/20
 * @Description: 订单状态变化（支持团购、预订，预付，拼团，电商，次卡）
 */
@Component
@Slf4j
public class GroundPromotionNormalOrderConsumer implements InitializingBean {

    @Autowired
    private OrderChannelRunningTaskDataService runningTaskDataService;

    @Autowired
    private RedisStoreClient redisStoreClient;

    @Autowired
    private OrderChannelTaskRunner taskRunner;

    @Autowired
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Autowired
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    private static final int RETRY_TASK_TYPE = 999;
    private static final long RETRY_DELAY_MS = 12000;
    private static final long RETRY_INTERVAL_MS = 2000;
    private static final int REDIS_TTL_SECONDS = 20;

    private final ThreadPoolExecutor taskRunningPool = new ThreadPoolExecutor(20, 40, 1, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(1000), new NamedThreadFactory(getClass().getSimpleName() + ".taskRunningPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    private static Map<String, OrderStatusEnum> privateLiveOrderStatusMap = Maps.newHashMap();

    @Resource
    private RedisLockService redisLockService;

    @Override
    public void afterPropertiesSet() throws Exception {
        ConsumerConfig config = new ConsumerConfig();
        config.setThreadPoolSize(1);
        Consumer consumer = ConsumerFactoryImpl.getInstance().createConsumer(
                Destination.topic("paycenter_paymentNotify_order_status"),
                "dz.order.distribute.normal.order.status.consumer",
                config
        );
        consumer.setListener(message -> process(message));
        consumer.start();
    }


    private void process(Message message) {
        log.info("[{}] messageId: {}", getClass().getSimpleName(), message.getMessageId());
        try {
            OrderStatusMessageDTO orderStatusMessageDTO = JSON.parseObject(message.getContent(), OrderStatusMessageDTO.class);
            if (orderStatusMessageDTO == null || StringUtils.isEmpty(orderStatusMessageDTO.getUnifiedOrderId())) {
                return;
            }
            if (ProductEnum.toProductEnum(orderStatusMessageDTO.getProductType()).productCode == 61
                    || ProductEnum.toProductEnum(orderStatusMessageDTO.getProductType()).productCode == 1) {
                // 过滤预订、团购消息
                return;
            }

            if (orderStatusMessageDTO.getOrderStatusAction().equals(OrderStatusAction.create.name())) {
                // 过滤非私域直播订单
                String distributionCode = getDistributionCode(orderStatusMessageDTO);
                if (StringUtils.isNotEmpty(distributionCode) && distributionCode.matches("^syzb.+")) {
                    // 订单创建，未支付。进行私域直播订单归因。
                    PrivateLiveOrderInfo privateLiveOrderInfo = initPrivateLiveOrderInfo(orderStatusMessageDTO);
                    // 分布式锁，防止重复消费
                    StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderStatusMessageDTO.getUnifiedOrderId(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.intentionCalc(privateLiveOrderInfo), taskRunningPool);
                    }
                } else if (orderStatusMessageDTO.getPlatform() == PayPlatform.mt_new_shanbo_api.getCode()) {
                    Cat.logEvent("private_live_order_code_invalid", String.format("orderId: %s, distributionCode: %s", orderStatusMessageDTO.getUnifiedOrderId(), distributionCode));
                }
            }

            if (ProductTypeUtils.isPrepay(orderStatusMessageDTO.getProductType())
                    && orderStatusMessageDTO.getOrderStatusAction().equals(OrderStatusAction.paySuccess.name())
                    && orderStatusMessageDTO.isFullyPaid()
                    && orderStatusMessageDTO.getPayBatchNum() == 2) {
                // 预付订单，付尾款时需要更新已支付金额
                PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderStatusMessageDTO.getUnifiedOrderId());
                if (orderIntentionResult != null) {
                    // 分布式锁，防止重复消费
                    StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderStatusMessageDTO.getUnifiedOrderId() + orderStatusMessageDTO.getOrderStatusAction() + orderStatusMessageDTO.getActionTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        // 更新数据
                        CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                                .orderId(orderIntentionResult.getOrderId())
                                .operateType(OrderOperateEnum.PAY_FINAL_SUCCESS.getCode())
                                .build()), taskRunningPool);
                    }
                }
            }

            if (orderStatusMessageDTO.getOrderStatusAction().equals(OrderStatusAction.cancel.name())
                    || orderStatusMessageDTO.getOrderStatusAction().equals(OrderStatusAction.fail.name())) {
                // 订单取消或购买失败。进行私域直播订单归因。
                PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderStatusMessageDTO.getUnifiedOrderId());
                if (orderIntentionResult != null && !OrderStatusEnum.REFUND.equals(OrderStatusEnum.from(orderIntentionResult.getStatus()))) {
                    processCancelOrder(orderIntentionResult.getOrderId(), orderStatusMessageDTO);
                } else if (orderIntentionResult == null) {
                    handleOrderCancelRetry(orderStatusMessageDTO.getUnifiedOrderId(), orderStatusMessageDTO);
                }
            }

            // 过滤核销消息
            if (orderStatusMessageDTO.getOrderStatusAction().equals(OrderStatusAction.consume.name())) {
                // 调用归因计算任务
                DistributionOrderChannelCalRunningTaskWithBLOBs task = runningTaskDataService.forceGetTaskByOrderAndType(DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode(), orderStatusMessageDTO.getUnifiedOrderId(), OrderChannelCalculateTaskTypeEnum.GROUND_PROMOTION_INDIRECT_INTENTION.getCode());
                if (task != null && task.getStatus() == OrderChannelTaskStatusEnum.RUNNING.getCode()) {
                    // 分布式锁，防止任务计算完成之前，又有核销消息进入
                    StoreKey storeKey = new StoreKey("GroundPromotionOrderCalLock", orderStatusMessageDTO.getUnifiedOrderId(), OrderChannelCalculateTaskTypeEnum.GROUND_PROMOTION_INDIRECT_INTENTION.getCode());
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        IntentionCalculateTaskParamBO taskParam = buildTaskParam(orderStatusMessageDTO);
                        CompletableFuture.runAsync(() -> taskRunner.executeTask(task.getTaskType(), task.getCalTaskId(), taskParam), taskRunningPool);
                    }
                }
            }

        } catch (Exception e) {
            log.error("GroundPromotionNormalOrderConsumer error msgBody:{}", message.getContent(), e);
        }
    }

    private void handleOrderCancelRetry(String orderId, OrderStatusMessageDTO orderStatusMessageDTO) {
        // 使用Redis实现延迟重试机制
        // 999=消息延迟重试
        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId, RETRY_TASK_TYPE);
        // 12秒后过期
        long expirationTime = System.currentTimeMillis() + RETRY_DELAY_MS;

        // 20秒TTL，防止死锁
        if (redisStoreClient.setnx(storeKey, expirationTime, REDIS_TTL_SECONDS)) {
            CompletableFuture.runAsync(() -> retryProcessCancelOrder(orderId, orderStatusMessageDTO, expirationTime), taskRunningPool);
        }
    }

    private void retryProcessCancelOrder(String orderId, OrderStatusMessageDTO orderStatusMessageDTO, Long expirationTime) {
        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderStatusMessageDTO.getUnifiedOrderId() + orderStatusMessageDTO.getOrderStatusAction() + orderStatusMessageDTO.getActionTime().toString()
                , OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
        try {
            while (System.currentTimeMillis() < expirationTime) {
                if (orderIntentionResultRepository.forceGetByOrderId(orderId) != null) {
                    // 分布式锁，防止重复消费
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        // 更新数据
                        privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                                .orderId(orderId)
                                .operateType(OrderOperateEnum.CANCEL.getCode())
                                .build());
                    }
                    return;
                }
                // 每2秒检查一次
                Thread.sleep(RETRY_INTERVAL_MS);
            }
            log.info("Retry timeout for cancel message, orderId: {}", orderId);
        } catch (InterruptedException e) {
            log.warn("Retry interrupted for orderId: {}", orderId, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("Error processing retry for orderId: {}", orderId, e);
        } finally {
            redisStoreClient.delete(storeKey);
        }
    }

    private PrivateLiveOrderInfo initPrivateLiveOrderInfo(OrderStatusMessageDTO orderStatusMessageDTO) {
        PrivateLiveOrderInfo privateLiveOrderInfo = new PrivateLiveOrderInfo();
        privateLiveOrderInfo.setOrderId(orderStatusMessageDTO.getUnifiedOrderId());
        privateLiveOrderInfo.setLongOrderId(orderStatusMessageDTO.getLongOrderId());
        privateLiveOrderInfo.setDistributorCode(orderStatusMessageDTO.getOrderExtraFields().get(UnifiedOrderField.distributionParam.fieldKey));
        boolean isMt = PayPlatform.isMtPlatform(orderStatusMessageDTO.getPlatform());
        privateLiveOrderInfo.setUserId(isMt ? orderStatusMessageDTO.getMtUserId() : orderStatusMessageDTO.getUserId());
        privateLiveOrderInfo.setPlatform(isMt ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
        List<CreateOrderSKU> skus = orderStatusMessageDTO.getSkus();
        privateLiveOrderInfo.setSkuId(CollectionUtils.isNotEmpty(skus) ? Long.parseLong(skus.get(0).getSkuId()) : 0L);
        privateLiveOrderInfo.setProductId(orderStatusMessageDTO.getProductGroupId());
        privateLiveOrderInfo.setProductType(ProductTypeAnalyser.fromOrderBizType(orderStatusMessageDTO.getProductType()).getCode());
        privateLiveOrderInfo.setQuantity(orderStatusMessageDTO.getQuantity());
        privateLiveOrderInfo.setStatus(privateLiveOrderStatusMap.get(orderStatusMessageDTO.getOrderStatusAction()).getCode());

        //BigDecimal thirdPartyAmount = BigDecimal.ZERO;
        //for (OrderPaymentDetailDTO paymentDetail : orderStatusMessageDTO.getPaymentDetails()) {
        //    if (paymentDetail.getAmountType() == AmountType.MEITUANPAY.value
        //            || paymentDetail.getAmountType() == AmountType.MERCHANT_MEITUANPAY.value
        //            || paymentDetail.getAmountType() == AmountType.FINANCIAL_INSTALLMENT.value) {
        //        thirdPartyAmount = thirdPartyAmount.add(paymentDetail.getAmount());
        //    }
        //}
        //privateLiveOrderInfo.setPayMoney(MathUtils.bigDecimalToLong(thirdPartyAmount));
        privateLiveOrderInfo.setTotalMoney(MathUtils.bigDecimalToLong(orderStatusMessageDTO.getTotalAmount()));
        privateLiveOrderInfo.setAddTime(orderStatusMessageDTO.getAddTime());
        //privateLiveOrderInfo.setPayTime(orderStatusMessageDTO.getSuccessTime());
        return privateLiveOrderInfo;
    }

    private void processCancelOrder(String orderId, OrderStatusMessageDTO orderStatusMessageDTO) {
        // 分布式锁，防止重复消费
        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderStatusMessageDTO.getUnifiedOrderId() + orderStatusMessageDTO.getOrderStatusAction() + orderStatusMessageDTO.getActionTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
        if (redisStoreClient.setnx(storeKey, true, 60)) {
            // 更新数据
            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                    .orderId(orderId)
                    .operateType(OrderOperateEnum.CANCEL.getCode())
                    .build()), taskRunningPool);
        }
    }

    private BigDecimal getPayMoney(List<OrderPaymentDetailDTO> paymentDetailDTOList) {
        BigDecimal thirdPartyAmount = BigDecimal.ZERO;
        for (OrderPaymentDetailDTO paymentDetail : paymentDetailDTOList) {
            if (paymentDetail.getAmountType() == AmountType.MEITUANPAY.value
                    || paymentDetail.getAmountType() == AmountType.MERCHANT_MEITUANPAY.value
                    || paymentDetail.getAmountType() == AmountType.FINANCIAL_INSTALLMENT.value) {
                thirdPartyAmount = thirdPartyAmount.add(paymentDetail.getAmount());
            }
        }
        return thirdPartyAmount;
    }

    private IntentionCalculateTaskParamBO buildTaskParam(OrderStatusMessageDTO orderStatusMessageDTO) {
        IntentionCalculateTaskParamBO taskParam = IntentionCalculateTaskParamBO.builder()
                .orderType(DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode())
                .orderId(orderStatusMessageDTO.getUnifiedOrderId())
                .distributionCode(orderStatusMessageDTO.getOrderExtraFields().get(60078))
                .build();
        return taskParam;
    }

    private String getDistributionCode(OrderStatusMessageDTO orderStatusMessageDTO) {
        return orderStatusMessageDTO.getOrderExtraFields().get(UnifiedOrderField.distributionParam.fieldKey);
    }

    static {
        privateLiveOrderStatusMap.put(OrderStatusAction.create.name(), OrderStatusEnum.CREATE);
        privateLiveOrderStatusMap.put(OrderStatusAction.cancel.name(), OrderStatusEnum.CANCEL);
    }

}
