package com.sankuai.carnation.distribution.distributionplan.domain.model;


import lombok.*;

import java.util.Objects;


/**
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductUniqueUnit {

    private Long mtProductId;

    private Integer productType;

    private String channel;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ProductUniqueUnit)) {
            return false;
        }
        ProductUniqueUnit that = (ProductUniqueUnit) o;
        return mtProductId.equals(that.mtProductId) &&
                productType.equals(that.productType) &&
                channel.equals(that.channel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getMtProductId(), getProductType(), getChannel());
    }
}
