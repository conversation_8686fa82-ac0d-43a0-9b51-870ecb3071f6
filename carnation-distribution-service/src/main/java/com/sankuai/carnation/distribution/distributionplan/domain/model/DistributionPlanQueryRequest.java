package com.sankuai.carnation.distribution.distributionplan.domain.model;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class DistributionPlanQueryRequest {

    /**
     * 商品id
     * 必须传递
     */
    private List<Long> productIds;

    /**
     * 渠道
     * 必须传递
     * @see com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum
     */
    private String channel;

    /**
     * 商品类型
     * 必须传递
     * @see com.sankuai.carnation.distribution.common.enums.ProductTypeEnum
     */
    private Integer productType;

    /**
     * 计划状态
     * 可选
     * @see com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanStatusEnum
     */
    private List<Integer> statusList;
}
