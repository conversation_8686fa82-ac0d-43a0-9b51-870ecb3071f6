package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.deal.flow.release.ProductReleaseService;
import com.dianping.deal.flow.release.ReleasedProductQueryService;
import com.dianping.deal.flow.release.dto.ProductReleaseOperateDTO;
import com.dianping.deal.flow.release.dto.ReleasedProductDTO;
import com.dianping.deal.flow.release.enums.MountStatusEnum;
import com.dianping.deal.flow.release.enums.ReleaseOperateTypeEnum;
import com.dianping.deal.flow.release.enums.ReleaseProductTypeEnum;
import com.dianping.deal.flow.release.enums.ReleaseStatusEnum;
import com.dianping.deal.flow.release.request.ReleaseProductRequest;
import com.dianping.deal.flow.release.request.ReleasedProductQueryRequest;
import com.dianping.deal.flow.response.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductReleaseAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ChannelProductReleaseResult;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductReleaseRequest;
import com.sankuai.carnation.distribution.distributionplan.acl.model.PushProductUnit;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ReleasedProductInfo;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductReleaseStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionProductPushTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductReleaseAclImpl implements ProductReleaseAcl {

    @Resource
    private ProductReleaseService productReleaseService;

    @Resource
    private ReleasedProductQueryService releasedProductQueryService;

    private static final String CAT_TYPE = ProductReleaseAcl.class.getSimpleName();

    private static final Map<DistributionBusinessChannelEnum, Long> DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP = Maps.newHashMap();

    private static final Map<ProductTypeEnum, ReleaseProductTypeEnum> DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP = Maps.newHashMap();

    static {
        DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.put(DistributionBusinessChannelEnum.WEI_BO, 10007L);
        DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.put(DistributionBusinessChannelEnum.KUAISHOU_E_COMMERCE, 10011L);

        DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.put(ProductTypeEnum.MT_DEAL_GROUP, ReleaseProductTypeEnum.DEALGROUP);
        DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.put(ProductTypeEnum.PREPAY, ReleaseProductTypeEnum.UNIFIED_PRODUCT);
        DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.put(ProductTypeEnum.STANDARD_PRODUCT, ReleaseProductTypeEnum.SPU);
    }

    @Override
    public ChannelProductReleaseResult releaseChannelProduct(ProductReleaseRequest productReleaseRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "releaseChannelProduct");
        try {
            validate(productReleaseRequest);
            PushProductUnit pushProductUnit = productReleaseRequest.getPushProductUnit();

            ReleaseProductRequest request = new ReleaseProductRequest();
            request.setOperateType(productReleaseRequest.getPushType().equals(DistributionProductPushTypeEnum.ONLINE) ?
                    ReleaseOperateTypeEnum.ENABLE.getCode() : ReleaseOperateTypeEnum.DISABLE.getCode());
            ProductReleaseOperateDTO productReleaseOperateDto = new ProductReleaseOperateDTO();
            productReleaseOperateDto.setProductType(DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.get(pushProductUnit.getProductType()).getCode());
            productReleaseOperateDto.setMtProductId(pushProductUnit.getMtProductId());
            productReleaseOperateDto.setChannelId(DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.get(pushProductUnit.getChannel()));
            request.setOperateProducts(Lists.newArrayList(productReleaseOperateDto));

            Response<Boolean> response = productReleaseService.releaseProduct(request);
            if (null == response) {
                throw new DistributionPlanException(pushProductUnit.getChannel(), "商品侧投放接口返回为空");
            }
            if (!response.isSuccess()) {
                log.error("ProductReleaseAcl.releaseChannelProduct invoke productReleaseService.releaseProduct failed, productBatchPushRequest:{}, response:{}", productReleaseRequest, JSON.toJSONString(response));
                throw new DistributionPlanException(pushProductUnit.getChannel(), "商品侧投放接口返回失败:"+response.getMessage());
            }
            if (null == response.getData() || !response.getData()) {
                log.error("ProductReleaseAcl.releaseChannelProduct releaseProduct failed, productBatchPushRequest:{}, response:{}", productReleaseRequest, JSON.toJSONString(response));
                throw new DistributionPlanException(pushProductUnit.getChannel(), "商品侧投放接口返回投放失败");
            }
            return ChannelProductReleaseResult.releaseSucceedResult(pushProductUnit);
        } catch (DistributionPlanException de) {
            log.error("ProductReleaseAcl.releaseChannelProduct biz failed, productReleaseRequest:{}", productReleaseRequest, de);
            transaction.setStatus(de);
            return ChannelProductReleaseResult.releaseFailedResult(productReleaseRequest.getPushProductUnit(), de.getMessage());
        } catch (Exception e) {
            log.error("ProductReleaseAcl.releaseChannelProduct failed, productReleaseRequest:{}", productReleaseRequest, e);
            transaction.setStatus(e);
            return ChannelProductReleaseResult.releaseFailedResult(productReleaseRequest.getPushProductUnit(), "业务逻辑异常");
        } finally {
            transaction.complete();
        }
    }

    private void validate(ProductReleaseRequest request) {
        if (null == request) {
            throw new DistributionPlanException("商品批量推送请求为空");
        }
        if (request.getPushType() == null) {
            throw new DistributionPlanException("商品推送类型为空");
        }
        if (null == request.getPushProductUnit()) {
            throw new DistributionPlanException("推送商品为空");
        }

        if (request.getPushProductUnit().getMtProductId() == null) {
            throw new DistributionPlanException("美团商品id不能为空");
        }
        if (request.getPushProductUnit().getChannel() == null
                || !DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.containsKey(request.getPushProductUnit().getChannel())) {
            throw new DistributionPlanException("渠道目前只支持微博、快手");
        }
        if (request.getPushProductUnit().getProductType() == null
                || !DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.containsKey(request.getPushProductUnit().getProductType())) {
            throw new DistributionPlanException("商品类型只支持预付、美团团购、标品");
        }
    }

    @Override
    public List<ReleasedProductInfo> batchQueryReleasedProduct(ProductTypeEnum productType, List<Long> mtProductIds, DistributionBusinessChannelEnum channel) {
        try {
            if (null == productType || !DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.containsKey(productType)
                    || CollectionUtils.isEmpty(mtProductIds) || !DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.containsKey(channel)) {
                log.error("ProductReleaseAcl.batchQueryReleasedProduct param error, productType:{}, mtProductIds:{}, channel:{}", productType, mtProductIds, channel);
                return Lists.newArrayList();
            }

            ReleasedProductQueryRequest request = new ReleasedProductQueryRequest();
            request.setMtProductIds(mtProductIds);
            request.setProductType(DISTRIBUTION_PRODUCT_TO_RELEASE_PRODUCT_TYPE_MAP.get(productType).getCode());
            request.setChannelIds(Lists.newArrayList(DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP.get(channel)));
            Response<List<ReleasedProductDTO>> response = releasedProductQueryService.queryReleasedProduct(request);
            if (null == response || !response.isSuccess()) {
                Cat.logEvent("InvokeReleaseProductQueryFailed", "nullResponse,request:" + request.toString()) ;
                throw new RuntimeException("请求商品接口失败");
            }
            if (CollectionUtils.isEmpty(response.getData())) {
                Cat.logEvent("ReleasedProductNotFound", productType + "releasedProduct:" + mtProductIds + "not found");
                return Lists.newArrayList();
            }

            return buildReleasedProductInfoList(response.getData(), productType);
        } catch (Exception e) {
            log.error("ProductReleaseAcl.batchQueryReleasedProduct failed, productType:{}, mtProductIds:{}", productType, mtProductIds, e);
            return Lists.newArrayList();
        }
    }

    private List<ReleasedProductInfo> buildReleasedProductInfoList(List<ReleasedProductDTO> releasedProducts, ProductTypeEnum productType) {

        Map<Long, DistributionBusinessChannelEnum> tradeToDistributionChannelMap = MapUtils.invertMap(DISTRIBUTION_BUSINESS_CHANNEL_TO_TRADE_CHANNEL_MAP);

        List<ReleasedProductInfo> releasedProductInfos = Lists.newArrayList();
        releasedProducts.forEach(releasedProduct -> {
            ReleasedProductInfo releasedProductInfo = new ReleasedProductInfo();
            releasedProductInfo.setMtProductId(releasedProduct.getMtProductId());
            releasedProductInfo.setChannel(tradeToDistributionChannelMap.getOrDefault(releasedProduct.getChannelId(), DistributionBusinessChannelEnum.UNKNOWN));
            releasedProductInfo.setThirdPartyProductId(releasedProduct.getThirdPartyProductId());
            releasedProductInfo.setProductType(productType);
            releasedProductInfo.setPushResult(convertToDistributionProductPushResultEnum(releasedProduct.getReleaseStatus(), releasedProduct.getMountStatus()));

            releasedProductInfos.add(releasedProductInfo);
        });
        return releasedProductInfos;
    }

    private DistributionProductReleaseStatusEnum convertToDistributionProductPushResultEnum(int releaseStatus, int mountStatus) {
        if (releaseStatus == ReleaseStatusEnum.ENABLE.getCode() && mountStatus == MountStatusEnum.AUDIT_PASSED.getCode()) {
            return DistributionProductReleaseStatusEnum.ONLINE_SUCCEED_AUDIT_SUCCEED;
        }
        if (releaseStatus == ReleaseStatusEnum.ENABLE.getCode() && mountStatus == MountStatusEnum.WAIT_AUDIT.getCode()) {
            return DistributionProductReleaseStatusEnum.ONLINE_SUCCEED_AUDIT_WAITING;
        }
        if (releaseStatus == ReleaseStatusEnum.ENABLE.getCode() && mountStatus == MountStatusEnum.AUDIT_IN_PROCESS.getCode()) {
            return DistributionProductReleaseStatusEnum.ONLINE_SUCCEED_AUDITING;
        }
        if (releaseStatus == ReleaseStatusEnum.ENABLE.getCode() && mountStatus == MountStatusEnum.AUDIT_FAILED.getCode()) {
            return DistributionProductReleaseStatusEnum.ONLINE_SUCCEED_AUDIT_UNAPPROVED;
        }
        if (releaseStatus == ReleaseStatusEnum.ENABLE.getCode() && mountStatus == MountStatusEnum.AUDIT_EXCEPTION.getCode()) {
            return DistributionProductReleaseStatusEnum.ONLINE_SUCCEED_AUDIT_FAILED;
        }

        if (releaseStatus == ReleaseStatusEnum.DISABLE.getCode()) {
            return DistributionProductReleaseStatusEnum.OFFLINE_SUCCEED;
        }
        return DistributionProductReleaseStatusEnum.UNKNOW;
    }
}