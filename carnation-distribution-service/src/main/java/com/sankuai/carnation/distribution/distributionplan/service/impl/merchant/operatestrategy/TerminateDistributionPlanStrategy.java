package com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy;

import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.service.model.MerchantChannelDistributionPlanOperateContext;
import com.sankuai.carnation.distribution.distributionplan.utils.DateUtils;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionPlanOperateTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanOperateRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanOperator;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class TerminateDistributionPlanStrategy implements MerchantChannelDistributionPlanOperateStrategy{

    @Resource
    private DistributionPlanCommandService distributionPlanCommandService;

    @Resource
    private RedisLockService redisLockService;

    @PostConstruct
    public void init() {
        MerchantChannelDistributionPlanOperateStrategyFactory.register(MerchantChannelDistributionPlanOperateTypeEnum.TERMINATE, this);
    }

    @Override
    public void validatePlanCanOperate(MerchantChannelDistributionPlanOperateContext context) {
        if (context.getDistributionPlan().getEndTime().before(context.getDistributionPlan().getBeginTime())) {
            throw new IllegalArgumentException("当前商品佣金配置已取消");
        }
        if (context.getDistributionPlan().getEndTime().getTime() < context.getOperateDate().getTime()) {
            throw new IllegalArgumentException("当前商品佣金配置已结束");
        }
        if (DateUtils.isSameDay(context.getOperateDate(), context.getDistributionPlan().getEndTime())) {
            throw new IllegalArgumentException("当前商品佣金配置今天凌晨前即将结束，不支持再修改佣金率");
        }
    }

    @Override
    public void operate(MerchantChannelDistributionPlanOperateContext context) {
        DistributionPlanOperateRequest distributionPlanOperateRequest = new DistributionPlanOperateRequest();
        distributionPlanOperateRequest.setPlanId(context.getDistributionPlan().getPlanId());
        distributionPlanOperateRequest.setOperator(DistributionPlanOperator.builder()
                .operatorId(String.valueOf(context.getMtAccountId()))
                .build());
        distributionPlanOperateRequest.setSceneCode(context.getDistributionPlan().getSceneCode());
        distributionPlanOperateRequest.setChannel(context.getDistributionPlan().getChannel());
        Date operateToDayNight = DateUtils.getToDayNight(context.getOperateDate());
        if (operateToDayNight.getTime() < context.getDistributionPlan().getBeginTime().getTime()) {
            Cat.logEvent("CancelMerchantDistributionPlan", "MerchantChannelDistributionPlanOperateStrategy.cancel distribution plan");
            distributionPlanOperateRequest.setOperateType(DistributionPlanOperateTypeEnum.CANCEL.getCode());
        } else {
            Cat.logEvent("SuspendMerchantDistributionPlan", "MerchantChannelDistributionPlanOperateStrategy.cancel distribution plan");
            distributionPlanOperateRequest.setOperateType(DistributionPlanOperateTypeEnum.TERMINATE.getCode());
        }
        distributionPlanOperateRequest.setTerminateTime(operateToDayNight.getTime());
        Response response = redisLockService.tryLock("operateMerchantDistributionPlan_" + context.getDistributionPlan().getPlanId(),
                () -> distributionPlanCommandService.operateDistributionPlan(distributionPlanOperateRequest));
        if (response.respFailed()) {
            throw new DistributionPlanException(response.getMessage());
        }
    }
}