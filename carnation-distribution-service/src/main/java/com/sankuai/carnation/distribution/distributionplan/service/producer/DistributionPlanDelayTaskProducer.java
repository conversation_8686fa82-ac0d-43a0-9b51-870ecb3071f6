package com.sankuai.carnation.distribution.distributionplan.service.producer;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.carnation.distribution.distributionplan.service.producer.dto.DistributionPlanDelayMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DistributionPlanDelayTaskProducer implements InitializingBean {

    private IProducerProcessor producer;

    /**
     * 最长支持730天
     */
    private static final long TIME_MILLIS_OF_THIRTY_DAY = 730 * 24 * 60 * 60 * 1000L;

    /**
     * 最短不能少于5000ms
     */
    private static final long MIN_DELAY_MILLIS = 5000L;

    public void sendMessage(DistributionPlanDelayMsg msg) {
        try {
            long delayTime = Math.subtractExact(msg.getOperateTime(), System.currentTimeMillis());
            if (delayTime <= MIN_DELAY_MILLIS) {
                delayTime = MIN_DELAY_MILLIS;
            }
            if (delayTime > TIME_MILLIS_OF_THIRTY_DAY) {
                log.info("DistributionPlanDelayTaskProducer.sendMessage delayTime too late,msg{}", msg);
                Cat.logEvent("DistributionPlanDelayTimeTooLate", "msg:"+JSONObject.toJSONString(msg));
                return;
            }

            ProducerResult result = producer.sendDelayMessage(JSONObject.toJSONString(msg), delayTime);
            if (result == null || result.getProducerStatus() != ProducerStatus.SEND_OK) {
                Cat.logEvent("DistributionPlanDelayTaskSendFailed", "msg:"+JSONObject.toJSONString(msg) + "result:" + JSONObject.toJSONString(result));
            }
            log.info("DistributionPlanDelayTaskProducer.sendMessage msg:{}, delayTime:{}, result:{}", JSONObject.toJSONString(msg), delayTime, JSONObject.toJSONString(result));
        } catch (Exception e) {
            Cat.logEvent("DistributionPlanDelayTaskProduceFailed", "msg:"+JSONObject.toJSONString(msg));
            log.error("DistributionPlanDelayTaskProducer.sendMessage failed, msg:{}", msg, e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.distribution.service");
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "3");
        properties.setProperty(ConsumerConstants.MafkaDelayServerTimeOut, "3000");
        properties.setProperty(ConsumerConstants.MafkaDelayServerConnTimeout, "3000");
        producer = MafkaClient.buildDelayProduceFactory(properties, "distribution.plan.status.change.delay.task");
    }
}