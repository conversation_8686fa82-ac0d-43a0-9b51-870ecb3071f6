package com.sankuai.carnation.distribution.distributioncode.domain;

import com.sankuai.carnation.distribution.distributioncode.domain.model.DistributionCodeParamMappingDO;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionCodeDoService {

    /**
     * 生成分销码
     * @param distributionCodeParamMappingDo
     * @return
     */
    void generateDistributionCode(DistributionCodeParamMappingDO distributionCodeParamMappingDo);

    /**
     * 解析分销码
     * @param distributionCode
     * @param channel
     * @return
     */
    DistributionCodeParamMappingDO parseDistributionCode(String distributionCode, DistributionBusinessChannelEnum channel);

    /**
     * 批量解析分销码
     * @param list
     * @param channel
     * @return
     */
    List<DistributionCodeParamMappingDO> batchParseDistributionCode(List<String> list, DistributionBusinessChannelEnum channel);
}
