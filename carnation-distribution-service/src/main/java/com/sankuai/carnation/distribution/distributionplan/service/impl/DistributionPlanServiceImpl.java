package com.sankuai.carnation.distribution.distributionplan.service.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.acl.PoiMountAcl;
import com.sankuai.carnation.distribution.distributionplan.constant.LionConstant;
import com.sankuai.carnation.distribution.distributionplan.domain.DistributionPlanDoService;
import com.sankuai.carnation.distribution.distributionplan.domain.impl.DistributionProductPushService;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanDO;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanPageQuery;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.domain.model.PageResult;
import com.sankuai.carnation.distribution.distributionplan.dto.*;
import com.sankuai.carnation.distribution.distributionplan.enums.*;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.service.DistributionPlanService;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@MdpPigeonServer
@Slf4j
public class DistributionPlanServiceImpl implements DistributionPlanService {

    private static final int MAX_COMMISSION_RATE = 100;

    private static final int MIN_COMMISSION_RATE = 0;

    private static final int DISTRIBUTION_COMMISSION_STRATEGY_PRODUCT_MAX_QUERY_SIZE = 50;

    private static final int DISTRIBUTION_PLAN_NAME_MAX_LENGTH = 50;

    private static final int DISTRIBUTION_PRODUCT_PUSH_LIST_MAX_SIZE = 10000;

    private static final String CAT_TYPE = DistributionPlanService.class.getSimpleName();

    @Resource
    private DistributionPlanDoService distributionPlanDoService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private DistributionProductPushService distributionProductPushService;

    @Resource
    private PoiMountAcl poiMountAcl;

    @Override
    public RemoteResponse batchAddDistributionPlan(DistributionPlanBatchAddRequest batchRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "batchAddDistributionPlan");
        try {
            validateDistributionPlanBatchAddRequest(batchRequest);
            Map<Long, String> productIdToResultMap = Maps.newHashMap();

            List<DistributionPlanAddRequest> distinctProductRequests = filterDuplicateProduct(batchRequest);
            if (CollectionUtils.isEmpty(distinctProductRequests)) {
                throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "商品id去重复后的商品列表为空");
            }
            distinctProductRequests.forEach(unit -> {
                Transaction unitTransaction = Cat.newTransaction(CAT_TYPE, "unitAddDistributionPlan");
                try {
                    DistributionPlanAddRequest addRequest = DistributionPlanAddRequest.builder()
                            .productId(unit.getProductId())
                            .productType(unit.getProductType())
                            .industryType(DistributionPlanIndustryTypeEnum.BEAUTY.getType())
                            .channel(DistributionBusinessChannelEnum.WEI_BO.getCode())
                            .commissionRate(batchRequest.getCommissionRate())
                            .preBeginTime(batchRequest.getPreBeginTime())
                            .preEndTime(batchRequest.getPreEndTime())
                            .planType(DistributionPlanTypeEnum.UNIVERSAL_PLAN.getType())
                            .planName(unit.getPlanName())
                            .operator("batchImport")
                            .operatorType(DistributionPlanOperatorTypeEnum.OPERATIONS_STAFF.getType())
                            .build();
                    Long planId = distributionPlanDoService.addDistributionPlan(addRequest);
                    productIdToResultMap.put(unit.getProductId(), String.valueOf(planId));
                } catch (DistributionPlanException de) {
                    log.error("DistributionPlanService.batchAddDistributionPlan addDistributionPlan not Allowed, unitRequest:{}", unit, de);
                    unitTransaction.setStatus(de);
                    productIdToResultMap.put(unit.getProductId(), de.getMessage());
                } catch (Exception e) {
                    log.error("DistributionPlanService.batchAddDistributionPlan addDistributionPlan failed, unitRequest:{}", unit, e);
                    unitTransaction.setStatus(e);
                    productIdToResultMap.put(unit.getProductId(), e.getMessage());
                }
            });
            return RemoteResponse.success(productIdToResultMap);
        } catch (Exception e) {
            Cat.logEvent("batchAddDistributionPlanFailed", "batchAddDistributionPlan failed");
            log.error("DistributionPlanService.batchAddDistributionPlan failed, batchRequest:{}", batchRequest, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private List<DistributionPlanAddRequest> filterDuplicateProduct(DistributionPlanBatchAddRequest batchRequest) {
        List<DistributionPlanAddRequest> distinctProductRequests = Lists.newArrayList();
        Set<Long> spuProductIds = Sets.newHashSet();
        Set<Long> prepayProductIds = Sets.newHashSet();
        batchRequest.getProductRequests().forEach(request -> {
            if (request.getProductType().equals(ProductTypeEnum.PREPAY.getCode()) && !prepayProductIds.contains(request.getProductId())) {
                prepayProductIds.add(request.getProductId());
                distinctProductRequests.add(request);
                return;
            }

            if (request.getProductType().equals(ProductTypeEnum.STANDARD_PRODUCT.getCode()) && !spuProductIds.contains(request.getProductId())) {
                spuProductIds.add(request.getProductId());
                distinctProductRequests.add(request);
            }
        });
        return distinctProductRequests;
    }

    private void validateDistributionPlanBatchAddRequest(DistributionPlanBatchAddRequest batchRequest) {
        if (null == batchRequest) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "请求参数不能为空");
        }
        if (CollectionUtils.isEmpty(batchRequest.getProductRequests())) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "商品列表不能为空");
        }
        batchRequest.getProductRequests().forEach(request -> {
            if (null == request.getProductId() || request.getProductId() <= 0L) {
                throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "存在非法的商品id");
            }
            if (StringUtils.isBlank(request.getPlanName()) || request.getPlanName().length() > DISTRIBUTION_PLAN_NAME_MAX_LENGTH) {
                throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "计划名称不能为空或者过长");
            }
            ProductTypeEnum productType = ProductTypeEnum.fromCode(request.getProductType());
            if (productType == null) {
                throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "部分商品类型不存在");
            }
            if (!productType.equals(ProductTypeEnum.STANDARD_PRODUCT) && !productType.equals(ProductTypeEnum.PREPAY)) {
                throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "商品类型目前暂不支持");
            }
        });

        if (batchRequest.getCommissionRate() == null) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "非法的佣金比例");
        }
        if (batchRequest.getCommissionRate().compareTo(BigDecimal.valueOf(MIN_COMMISSION_RATE)) <= 0) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "佣金比例不能小于等于0");
        }
        if (batchRequest.getCommissionRate().compareTo(BigDecimal.valueOf(MAX_COMMISSION_RATE)) >= 0) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "佣金比例不能大于等于100");
        }
        BigDecimal proceedPrecision = batchRequest.getCommissionRate().setScale(1, RoundingMode.DOWN);
        if (proceedPrecision.compareTo(batchRequest.getCommissionRate()) != 0) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "佣金比例的精度最多支持到小数点后一位数，比如16.8%");
        }
        MathContext mathContext = new MathContext(2, RoundingMode.DOWN);
        BigDecimal platformCommissionRate = new BigDecimal(Lion.getString(MdpContextUtils.getAppKey(), LionConstant.PLATFORM_COMMISSION_RATE, "4.0"), mathContext);
        Map<String, String> channelTypeToCommissionRateMap = Lion.getMap(MdpContextUtils.getAppKey(), LionConstant.CHANNEL_COMMISSION_RATE_MAP, String.class, Maps.newHashMap());
        BigDecimal channelCommissionRate = new BigDecimal(channelTypeToCommissionRateMap.getOrDefault(String.valueOf(DistributionBusinessChannelEnum.WEI_BO.getCode()), "4.0"), mathContext);
        BigDecimal minCommissionRate = platformCommissionRate.add(channelCommissionRate);
        if (batchRequest.getCommissionRate().compareTo(minCommissionRate) <= 0) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "佣金比例设置过低，请进行调整");
        }
        if (batchRequest.getPreBeginTime() == null || batchRequest.getPreBeginTime() <= System.currentTimeMillis()) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "非法的开始时间");
        }
        if (batchRequest.getPreEndTime() == null || batchRequest.getPreBeginTime() >= batchRequest.getPreEndTime()) {
            throw new DistributionPlanException(DistributionBusinessChannelEnum.WEI_BO, "非法的结束时间");
        }
    }

    @Override
    public RemoteResponse addDistributionPlan(DistributionPlanAddRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "addDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!validateDistributionPlanAddRequest(request, errorMsg)) {
                log.error("DistributionPlanDoService.addDistributionPlan 请求参数异常, errorMsg:{}, request:{}", errorMsg.toString(), request);
                return RemoteResponse.fail("请求参数异常:"+errorMsg.toString());
            }

            Long planId = redisLockService.tryLock(String.format("addDistributionPlan_%s_%s", request.getProductType(), request.getProductId()),
                    () -> distributionPlanDoService.addDistributionPlan(request), 3);
            return RemoteResponse.success(planId);
        } catch (DistributionPlanException de) {
            Cat.logEvent("addDistributionPlanNotAllowed", "addDistributionPlan not allowed");
            log.error("DistributionPlanService.addDistributionPlan not Allowed, request:{}", request, de);
            transaction.setStatus(de);
            return RemoteResponse.fail(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("addDistributionPlanFailed", "addDistributionPlan failed");
            log.error("DistributionPlanService.addDistributionPlan failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    public boolean validateDistributionPlanAddRequest(DistributionPlanAddRequest request, StringBuilder sb) {
        if (null == request) {
            sb.append("请求参数不能为空");
            return false;
        }
        if (request.getProductId() == null || request.getProductId() <=0L) {
            sb.append("非法的商品id");
            return false;
        }

        ProductTypeEnum productType = ProductTypeEnum.fromCode(request.getProductType());
        if (productType == null) {
            sb.append("商品类型不存在");
            return false;
        }
        if (!DistributionPlanIndustryTypeEnum.isExistCurrentType(request.getIndustryType())) {
            sb.append("行业类型不存在");
            return false;
        }
        if (null == request.getChannel() || !request.getChannel().equals(DistributionBusinessChannelEnum.WEI_BO.getCode())) {
            sb.append("该渠道暂不支持");
            return false;
        }
        if (!DistributionPlanTypeEnum.isExistCurrentType(request.getPlanType())) {
            sb.append("计划类型不存在");
            return false;
        }
        if (StringUtils.isBlank(request.getPlanName()) || request.getPlanName().length() > DISTRIBUTION_PLAN_NAME_MAX_LENGTH) {
            sb.append("计划名称不能为空或者过长");
            return false;
        }
        if (request.getCommissionRate() == null) {
            sb.append("非法的佣金比例");
            return false;
        }
        if (request.getCommissionRate().compareTo(BigDecimal.valueOf(MIN_COMMISSION_RATE)) <= 0) {
            sb.append("佣金比例不能小于等于0");
            return false;
        }
        if (request.getCommissionRate().compareTo(BigDecimal.valueOf(MAX_COMMISSION_RATE)) >= 0) {
            sb.append("佣金比例不能大于等于100");
            return false;
        }
        BigDecimal proceedPrecision = request.getCommissionRate().setScale(1, RoundingMode.DOWN);
        if (proceedPrecision.compareTo(request.getCommissionRate()) != 0) {
            sb.append("佣金比例的精度最多支持到小数点后一位数，比如16.8%");
            return false;
        }
        MathContext mathContext = new MathContext(2, RoundingMode.DOWN);
        BigDecimal platformCommissionRate = new BigDecimal(Lion.getString(MdpContextUtils.getAppKey(), LionConstant.PLATFORM_COMMISSION_RATE, "4.0"), mathContext);
        Map<String, String> channelTypeToCommissionRateMap = Lion.getMap(MdpContextUtils.getAppKey(), LionConstant.CHANNEL_COMMISSION_RATE_MAP, String.class, Maps.newHashMap());
        BigDecimal channelCommissionRate = new BigDecimal(channelTypeToCommissionRateMap.getOrDefault(String.valueOf(request.getChannel()), "4.0"), mathContext);
        BigDecimal minCommissionRate = platformCommissionRate.add(channelCommissionRate);
        if (request.getCommissionRate().compareTo(minCommissionRate) <= 0) {
            sb.append("佣金比例设置过低，请进行调整");
            return false;
        }
        if (request.getPreBeginTime() == null || request.getPreBeginTime() <= System.currentTimeMillis()) {
            sb.append("非法的开始时间");
            return false;
        }
        if (request.getPreEndTime() == null || request.getPreBeginTime() >= request.getPreEndTime()) {
            sb.append("非法的结束时间");
            return false;
        }
        if (!DistributionPlanOperatorTypeEnum.isExistCurrentType(request.getOperatorType())) {
            sb.append("操作人类型不存在");
            return false;
        }
        if (StringUtils.isBlank(request.getOperator())) {
            sb.append("操作人信息为空");
            return false;
        }

        return true;
    }

    @Override
    public RemoteResponse editDistributionPlan(DistributionPlanEditRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "editDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!validateDistributionPlanEditRequest(request, errorMsg)) {
                log.error("DistributionPlanDoService.editDistributionPlan 请求参数异常, errorMsg:{}, request:{}", errorMsg.toString(), request);
                return RemoteResponse.fail("请求参数异常:"+errorMsg.toString());
            }

            redisLockService.tryLock(String.format("addDistributionPlan_%s", request.getPlanId()),
                    () -> distributionPlanDoService.editDistributionPlan(request), 3);

            return RemoteResponse.success(true);
        } catch (DistributionPlanException de) {
            Cat.logEvent("editDistributionPlanNotAllowed", "DistributionPlanService.editDistributionPlan not allowed");
            log.error("DistributionPlanService.editDistributionPlan not allowed, request:{}", request, de);
            transaction.setStatus(de);
            return RemoteResponse.fail(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("editDistributionPlanFailed", "DistributionPlanService.editDistributionPlan failed");
            log.error("DistributionPlanService.editDistributionPlan failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private boolean validateDistributionPlanEditRequest(DistributionPlanEditRequest request, StringBuilder errorMsg) {
        if (null == request) {
            errorMsg.append("请求参数不能为空");
            return false;
        }
        if (request.getPlanId() == null || request.getPlanId() <= 0L) {
            errorMsg.append("非法的分销计划id");
            return false;
        }
        if (StringUtils.isBlank(request.getPlanName()) || request.getPlanName().length() > DISTRIBUTION_PLAN_NAME_MAX_LENGTH) {
            errorMsg.append("计划名称不能为空或者过长");
            return false;
        }
        if (StringUtils.isBlank(request.getChannel()) || !DistributionBusinessChannelEnum.WEI_BO.getCode().equals(request.getChannel())) {
            errorMsg.append("当前渠道的计划不支持编辑");
            return false;
        }
        if (request.getCommissionRate() == null) {
            errorMsg.append("非法的佣金比例");
            return false;
        }
        if (request.getCommissionRate().compareTo(BigDecimal.valueOf(MIN_COMMISSION_RATE)) <= 0) {
            errorMsg.append("佣金比例不能小于等于0");
            return false;
        }
        if (request.getCommissionRate().compareTo(BigDecimal.valueOf(MAX_COMMISSION_RATE)) >= 0) {
            errorMsg.append("佣金比例不能大于等于100");
            return false;
        }
        BigDecimal proceedPrecision = request.getCommissionRate().setScale(1, RoundingMode.DOWN);
        if (proceedPrecision.compareTo(request.getCommissionRate()) != 0) {
            errorMsg.append("佣金比例的精度最多支持到小数点后一位数，比如16.8%");
            return false;
        }
        MathContext mathContext = new MathContext(2, RoundingMode.DOWN);
        BigDecimal platformCommissionRate = new BigDecimal(Lion.getString(MdpContextUtils.getAppKey(), LionConstant.PLATFORM_COMMISSION_RATE, "4.0"), mathContext);
        Map<String, String> channelTypeToCommissionRateMap = Lion.getMap(MdpContextUtils.getAppKey(), LionConstant.CHANNEL_COMMISSION_RATE_MAP, String.class, Maps.newHashMap());
        BigDecimal channelCommissionRate = new BigDecimal(channelTypeToCommissionRateMap.getOrDefault(String.valueOf(request.getChannel()), "4.0"), mathContext);
        BigDecimal minCommissionRate = platformCommissionRate.add(channelCommissionRate);
        if (request.getCommissionRate().compareTo(minCommissionRate) <= 0) {
            errorMsg.append("佣金比例设置过低，请进行调整");
            return false;
        }
        if (request.getPreBeginTime() == null || request.getPreBeginTime() <= System.currentTimeMillis()) {
            errorMsg.append("计划开始时间需晚于当前时刻");
            return false;
        }
        if (request.getPreEndTime() == null || request.getPreBeginTime() >= request.getPreEndTime()) {
            errorMsg.append("计划结束时间需晚于开始时间");
            return false;
        }
        if (!DistributionPlanOperatorTypeEnum.isExistCurrentType(request.getOperatorType())) {
            errorMsg.append("操作人类型不存在");
            return false;
        }
        if (StringUtils.isBlank(request.getOperator())) {
            errorMsg.append("操作人信息为空");
            return false;
        }
        return true;
    }

    @Override
    public RemoteResponse operateDistributionPlan(DistributionPlanOperateRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "operateDistributionPlan");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!validateDistributionPlanOperateRequest(request, errorMsg)) {
                log.error("DistributionPlanDoService.operateDistributionPlan 请求参数异常, errorMsg:{}, request:{}", errorMsg.toString(), request);
                return RemoteResponse.fail("请求参数异常:"+errorMsg.toString());
            }

            redisLockService.tryLock(String.format("addDistributionPlan_%s", request.getPlanId()),
                    () -> distributionPlanDoService.operateDistributionPlan(request), 3);

            return RemoteResponse.success(true);
        } catch (DistributionPlanException de) {
            Cat.logEvent("operateDistributionPlanNotAllowed", "DistributionPlanService.operateDistributionPlan not allowed");
            log.error("DistributionPlanService.operateDistributionPlan not Allowed, request:{}", request, de);
            transaction.setStatus(de);
            return RemoteResponse.fail(de.getMessage());
        } catch (Exception e) {
            Cat.logEvent("operateDistributionPlanFailed", "DistributionPlanService.operateDistributionPlan failed");
            log.error("DistributionPlanService.operateDistributionPlan failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private boolean validateDistributionPlanOperateRequest(DistributionPlanOperateRequest request, StringBuilder errorMsg) {
        if (null == request) {
            errorMsg.append("请求参数不能为空");
            return false;
        }
        if (request.getPlanId() == null || request.getPlanId() <= 0L) {
            errorMsg.append("非法的分销计划id");
            return false;
        }
        if (StringUtils.isBlank(request.getChannel()) || !DistributionBusinessChannelEnum.WEI_BO.getCode().equals(request.getChannel())) {
            errorMsg.append("当前渠道的计划不支持操作");
            return false;
        }

        DistributionPlanOperationEnum operationEnum = DistributionPlanOperationEnum.from(request.getOperation());
        if (null == operationEnum || !operationEnum.equals(DistributionPlanOperationEnum.CANCEL)) {
            errorMsg.append("计划不支持进行当前操作");
            return false;
        }
        if (!DistributionPlanOperatorTypeEnum.isExistCurrentType(request.getOperatorType())) {
            errorMsg.append("操作人类型不存在");
            return false;
        }
        if (StringUtils.isBlank(request.getOperator())) {
            errorMsg.append("操作人信息为空");
            return false;
        }

        return true;
    }

    @Override
    public PaginationRemoteResponse<DistributionPlanDTO> pageQueryDistributionPlan(DistributionPlanPageQueryRequest request) {
        try {
            if (null == request || request.getPageNo() == null || request.getPageNo() <= 0L
                    || request.getPageSize() == null || request.getPageSize() <= 0L || request.getPageSize() > 50L
                    || !DistributionBusinessChannelEnum.fromCode(request.getChannel()).equals(DistributionBusinessChannelEnum.WEI_BO)) {
                log.error("DistributionPlanService.pageQueryDistributionPlan param error, request:{}", request);
                return PaginationRemoteResponse.failure(ResponseEnum.INVALID_PARAM.desc);
            }

            DistributionPlanPageQuery pageQuery = DistributionPlanPageQuery.builder()
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize())
                    .productId(request.getProductId())
                    .productType(request.getProductType())
                    .channel(request.getChannel())
                    .build();
            PageResult<DistributionPlanDO> distributionPlanDoPageResult = distributionPlanDoService.pageQueryDistributionPlan(pageQuery);
            if (distributionPlanDoPageResult == null || CollectionUtils.isEmpty(distributionPlanDoPageResult.getRecords())) {
                return PaginationRemoteResponse.success(Lists.newArrayList(), distributionPlanDoPageResult == null ? 0L : distributionPlanDoPageResult.getTotalCount());
            }
            return PaginationRemoteResponse.success(convertDistributionPlanDosToDtos(distributionPlanDoPageResult.getRecords()), distributionPlanDoPageResult.getTotalCount());
        } catch (Exception e) {
            log.error("DistributionPlanService.pageQueryDistributionPlan failed, request:{}", request, e);
            return PaginationRemoteResponse.failure(ResponseEnum.INNER_FAILURE.desc);
        }
    }

    @Override
    public RemoteResponse<DistributionPlanDTO> loadDistributionPlan(DistributionPlanLoadRequest request) {
        try {
            if (null == request || request.getPlanId() == null || request.getPlanId() <= 0L) {
                log.error("DistributionPlanService.loadDistributionPlan param error, request:{}", request);
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }
            Optional<DistributionPlanDO> distributionPlanDoOptional = distributionPlanDoService.loadDistributionPlan(request.getPlanId());
            if (!distributionPlanDoOptional.isPresent()) {
                log.error("DistributionPlanService.loadDistributionPlan plan not found, request:{}", request);
                return RemoteResponse.fail("计划未找到");
            }

            return RemoteResponse.success(convertDistributionPlanDoToDto(distributionPlanDoOptional.get()));
        } catch (Exception e) {
            log.error("DistributionPlanService.loadDistributionPlan failed, request:{}", request, e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        }
    }

    @Override
    public RemoteResponse<List<DistributionProductTypeDTO>> queryDistributionProductTypes() {
        List<DistributionProductTypeDTO> distributionProductTypes = Lists.newArrayList();
        distributionProductTypes.add(DistributionProductTypeDTO.builder()
                .productType(ProductTypeEnum.PREPAY.getCode())
                .productTypeDesc(ProductTypeEnum.PREPAY.getDesc())
                .build());
        distributionProductTypes.add(DistributionProductTypeDTO.builder()
                .productType(ProductTypeEnum.STANDARD_PRODUCT.getCode())
                .productTypeDesc(ProductTypeEnum.STANDARD_PRODUCT.getDesc())
                .build());

        return RemoteResponse.success(distributionProductTypes);
    }

    @Override
    public RemoteResponse<List<DistributionPlanCommissionStrategyDTO>> queryDistributionCommissionStrategy(DistributionCommissionStrategyQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryDistributionCommissionStrategy");
        try {
            StringBuilder errorMsg = new StringBuilder();
            if (!validateDistributionCommissionStrategyQueryRequest(request, errorMsg)) {
                log.error("DistributionPlanDoService.queryDistributionCommissionStrategy 请求参数异常, errorMsg:{}, request:{}", errorMsg.toString(), request);
                return RemoteResponse.fail("请求参数异常:"+errorMsg.toString());
            }

            DistributionPlanQueryRequest distributionPlanQueryRequest = DistributionPlanQueryRequest.builder()
                    .productIds(request.getProductIds())
                    .productType(request.getProductType().getCode())
                    .channel(request.getChannel().getCode())
                    .statusList(Lists.newArrayList(DistributionPlanStatusEnum.EXECUTING.getCode()))
                    .build();
            List<DistributionPlanDO> distributionPlanDos = distributionPlanDoService.batchQueryDistributionPlans(distributionPlanQueryRequest);
            return RemoteResponse.success(convertToCommissionStrategyDtoList(distributionPlanDos));
        } catch (Exception e) {
            log.error("DistributionPlanService.queryDistributionCommissionStrategy failed, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private List<DistributionPlanCommissionStrategyDTO> convertToCommissionStrategyDtoList(List<DistributionPlanDO> distributionPlanDos) {
        if (CollectionUtils.isEmpty(distributionPlanDos)) {
            return Lists.newArrayList();
        }
        List<DistributionPlanCommissionStrategyDTO> commissionStrategyDtoList = Lists.newArrayList();
        distributionPlanDos.forEach(distributionPlanDo -> {
            DistributionPlanCommissionStrategyDTO strategyDto = new DistributionPlanCommissionStrategyDTO();
            strategyDto.setProductId(distributionPlanDo.getProductId());
            strategyDto.setProductType(distributionPlanDo.getProductType());
            strategyDto.setChannel(distributionPlanDo.getChannel());
            strategyDto.setDistributorCommissionRate(DistributionPlanDO.calCommissionRateToThousandths(distributionPlanDo.getCommissionStrategyVo().getDistributorCommissionRate()));
            commissionStrategyDtoList.add(strategyDto);
        });

        return commissionStrategyDtoList;
    }

    private boolean validateDistributionCommissionStrategyQueryRequest(DistributionCommissionStrategyQueryRequest request, StringBuilder errorMsg) {
        if (null == request) {
            errorMsg.append("请求参数不能为空");
            return false;
        }
        if (request.getChannel() == null) {
            errorMsg.append("渠道参数不能为空");
            return false;
        }
        if (request.getProductType() == null) {
            errorMsg.append("商品类型参数不能为空");
            return false;
        }
        if (CollectionUtils.isEmpty(request.getProductIds())) {
            errorMsg.append("商品id列表参数不能为空");
            return false;
        }
        if (request.getProductIds().size() > DISTRIBUTION_COMMISSION_STRATEGY_PRODUCT_MAX_QUERY_SIZE) {
            errorMsg.append("商品id列表最大不能超过50");
            return false;
        }
        return true;
    }

    private DistributionPlanDTO convertDistributionPlanDoToDto(DistributionPlanDO distributionPlanDo) {
        DistributionPlanDTO distributionPlanDto = new DistributionPlanDTO();
        distributionPlanDto.setPlanId(distributionPlanDo.getPlanId());
        distributionPlanDto.setProductId(distributionPlanDo.getProductId());
        distributionPlanDto.setProductType(distributionPlanDo.getProductType().getCode());
        distributionPlanDto.setPlanName(distributionPlanDo.getPlanName());
        distributionPlanDto.setPreBeginTime(distributionPlanDo.getPreBeginTime());
        distributionPlanDto.setPreEndTime(distributionPlanDo.getPreEndTime());
        distributionPlanDto.setStatus(distributionPlanDo.getStatus().getCode());
        distributionPlanDto.setCommissionRate(distributionPlanDo.getCommissionStrategyVo().getCommissionRate());

        return distributionPlanDto;
    }

    private List<DistributionPlanDTO> convertDistributionPlanDosToDtos(List<DistributionPlanDO> distributionPlanDos) {
        if (CollectionUtils.isEmpty(distributionPlanDos)) {
            return Lists.newArrayList();
        }

        return distributionPlanDos.stream().map(this::convertDistributionPlanDoToDto).collect(Collectors.toList());
    }

    @Override
    public RemoteResponse<Integer> uploadDistributionProduct(String excelName, DistributionBusinessChannelEnum channel) {
        try {
            if (StringUtils.isBlank(excelName) || null == channel) {
                log.error("DistributionPlanService.parseProductListExcel param error, excelName:{}, channel:{}", excelName, channel);
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }

            int uploadRowNum = redisLockService.tryLock("uploadDistributionProduct",
                    () -> distributionProductPushService.uploadDistributionProduct(excelName, channel));
            return RemoteResponse.success(uploadRowNum);
        } catch (Exception e) {
            log.error("DistributionPlanService.parseProductListExcel failed, excelName:{}, channel:{}", excelName, channel, e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        }
    }

    @Override
    public RemoteResponse<Map<Long, DistributionProductPushResultDTO>> pushDistributionProduct(ProductPushRequest pushRequest) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pushDistributionProduct");
        try {
            validateProductPushRequest(pushRequest);
            return RemoteResponse.success(distributionProductPushService.pushDistributionProduct(pushRequest));
        } catch (Exception e) {
            log.error("DistributionPlanService.pushDistributionProduct failed, pushRequest:{}", pushRequest, e);
            transaction.setStatus(e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        } finally {
            transaction.complete();
        }
    }

    private void validateProductPushRequest(ProductPushRequest pushRequest) {
        if (null == pushRequest) {
            throw new DistributionPlanException("请求参数不能为空");
        }
        if (CollectionUtils.isEmpty(pushRequest.getProductIds())) {
            throw new DistributionPlanException("商品id列表不能为空");
        }
        if (pushRequest.getProductIds().stream().anyMatch(productId -> productId == null || productId <= 0L)) {
            throw new DistributionPlanException("商品id列表中存在为空或者非自然数的商品id");
        }
        if (pushRequest.getProductIds().size() > Lion.getInt(MdpContextUtils.getAppKey(), "distributionProduct.pushable.list.max.size", DISTRIBUTION_PRODUCT_PUSH_LIST_MAX_SIZE)) {
            throw new DistributionPlanException("商品id数量超出单次推品上限");
        }
        if (DistributionBusinessChannelEnum.fromCode(pushRequest.getChannel()) != DistributionBusinessChannelEnum.KUAISHOU_E_COMMERCE) {
            throw new DistributionPlanException("目前只支持快手渠道分销");
        }
        if (pushRequest.getProductType() == null || ProductTypeEnum.fromCode(pushRequest.getProductType()) == ProductTypeEnum.UNKNOWN) {
            throw new DistributionPlanException("未知的商品类型");
        }
        if (!DistributionProductPushTypeEnum.isExistCurrentType(pushRequest.getPushType())) {
            throw new DistributionPlanException("未知的商品投放类型");
        }
    }

    @Override
    public RemoteResponse<List<ProductDistributionStatusDTO>> batchQueryChannelProductPushResult(ChannelProductPushResultQueryRequest request) {
        try {
            if (null == request || null == request.getChannel() || null == request.getProductType()
                    || CollectionUtils.isEmpty(request.getProductIds()) || request.getProductIds().size() > 50) {
                log.error("DistributionPlanService.batchQueryChannelProductPushResult param error, request:{}", request);
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }

            List<ProductDistributionStatusDTO> distributionProductPushChannelResults = distributionProductPushService.batchQueryChannelProductPushResult(request);
            return RemoteResponse.success(distributionProductPushChannelResults);
        } catch (Exception e) {
            log.error("DistributionPlanService.batchQueryChannelProductPushResult failed, request:{}", request, e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        }
    }

    @Override
    public RemoteResponse<List<ChannelPoiMountDTO>> batchQueryChannelPoiMountResult(ChannelPoiMountResultQueryRequest request) {
        try {
            if (null == request || null == request.getChannel() || CollectionUtils.isEmpty(request.getPoiIds()) || request.getPoiIds().size() > 5000) {
                log.error("DistributionPlanService.batchQueryChannelPoiMountResult param error, request:{}", request);
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }

            List<Long> distinctMtPoiIds = request.getPoiIds().stream().filter(Objects::nonNull).filter(poiId -> poiId > 0L).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(distinctMtPoiIds)) {
                return RemoteResponse.fail(ResponseEnum.INVALID_PARAM.desc);
            }
            Map<Long, String> mtPoiToMountPoiIdMap = poiMountAcl.batchGetMtPoiMountPoiId(distinctMtPoiIds , request.getChannel());
            if (MapUtils.isEmpty(mtPoiToMountPoiIdMap)) {
                return RemoteResponse.success(Lists.newArrayList());
            }

            List<ChannelPoiMountDTO> channelPoiMountDtoList = Lists.newArrayList();
            distinctMtPoiIds.forEach(poiId -> channelPoiMountDtoList.add(ChannelPoiMountDTO.builder()
                    .mtPoiId(poiId)
                    .channelPoiId(mtPoiToMountPoiIdMap.get(poiId)).build()));
            return RemoteResponse.success(channelPoiMountDtoList);
        } catch (Exception e) {
            log.error("DistributionPlanService.batchQueryChannelProductPushResult failed, request:{}", request, e);
            return RemoteResponse.fail(ResponseEnum.INNER_FAILURE.desc);
        }
    }
}
