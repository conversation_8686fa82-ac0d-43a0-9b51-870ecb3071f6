package com.sankuai.carnation.distribution.commisson.repository.example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CommRebateAclAnnualExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CommRebateAclAnnualExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CommRebateAclAnnualExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CommRebateAclAnnualExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CommRebateAclAnnualExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIsNull() {
            addCriterion("mt_shop_id is null");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIsNotNull() {
            addCriterion("mt_shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andMtShopIdEqualTo(Long value) {
            addCriterion("mt_shop_id =", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotEqualTo(Long value) {
            addCriterion("mt_shop_id <>", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdGreaterThan(Long value) {
            addCriterion("mt_shop_id >", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mt_shop_id >=", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdLessThan(Long value) {
            addCriterion("mt_shop_id <", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdLessThanOrEqualTo(Long value) {
            addCriterion("mt_shop_id <=", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIn(List<Long> values) {
            addCriterion("mt_shop_id in", values, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotIn(List<Long> values) {
            addCriterion("mt_shop_id not in", values, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdBetween(Long value1, Long value2) {
            addCriterion("mt_shop_id between", value1, value2, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotBetween(Long value1, Long value2) {
            addCriterion("mt_shop_id not between", value1, value2, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdIsNull() {
            addCriterion("out_biz_id is null");
            return (Criteria) this;
        }

        public Criteria andOutBizIdIsNotNull() {
            addCriterion("out_biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andOutBizIdEqualTo(String value) {
            addCriterion("out_biz_id =", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdNotEqualTo(String value) {
            addCriterion("out_biz_id <>", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdGreaterThan(String value) {
            addCriterion("out_biz_id >", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("out_biz_id >=", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdLessThan(String value) {
            addCriterion("out_biz_id <", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdLessThanOrEqualTo(String value) {
            addCriterion("out_biz_id <=", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdLike(String value) {
            addCriterion("out_biz_id like", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdNotLike(String value) {
            addCriterion("out_biz_id not like", value, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdIn(List<String> values) {
            addCriterion("out_biz_id in", values, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdNotIn(List<String> values) {
            addCriterion("out_biz_id not in", values, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdBetween(String value1, String value2) {
            addCriterion("out_biz_id between", value1, value2, "outBizId");
            return (Criteria) this;
        }

        public Criteria andOutBizIdNotBetween(String value1, String value2) {
            addCriterion("out_biz_id not between", value1, value2, "outBizId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(Long value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(Long value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(Long value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(Long value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(Long value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(Long value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<Long> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<Long> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(Long value1, Long value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(Long value1, Long value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNull() {
            addCriterion("group_name is null");
            return (Criteria) this;
        }

        public Criteria andGroupNameIsNotNull() {
            addCriterion("group_name is not null");
            return (Criteria) this;
        }

        public Criteria andGroupNameEqualTo(String value) {
            addCriterion("group_name =", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotEqualTo(String value) {
            addCriterion("group_name <>", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThan(String value) {
            addCriterion("group_name >", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameGreaterThanOrEqualTo(String value) {
            addCriterion("group_name >=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThan(String value) {
            addCriterion("group_name <", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLessThanOrEqualTo(String value) {
            addCriterion("group_name <=", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameLike(String value) {
            addCriterion("group_name like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotLike(String value) {
            addCriterion("group_name not like", value, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameIn(List<String> values) {
            addCriterion("group_name in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotIn(List<String> values) {
            addCriterion("group_name not in", values, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameBetween(String value1, String value2) {
            addCriterion("group_name between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andGroupNameNotBetween(String value1, String value2) {
            addCriterion("group_name not between", value1, value2, "groupName");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNull() {
            addCriterion("bu_id is null");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNotNull() {
            addCriterion("bu_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuIdEqualTo(Integer value) {
            addCriterion("bu_id =", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotEqualTo(Integer value) {
            addCriterion("bu_id <>", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThan(Integer value) {
            addCriterion("bu_id >", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bu_id >=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThan(Integer value) {
            addCriterion("bu_id <", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThanOrEqualTo(Integer value) {
            addCriterion("bu_id <=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdIn(List<Integer> values) {
            addCriterion("bu_id in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotIn(List<Integer> values) {
            addCriterion("bu_id not in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdBetween(Integer value1, Integer value2) {
            addCriterion("bu_id between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bu_id not between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andAwardTypeIsNull() {
            addCriterion("award_type is null");
            return (Criteria) this;
        }

        public Criteria andAwardTypeIsNotNull() {
            addCriterion("award_type is not null");
            return (Criteria) this;
        }

        public Criteria andAwardTypeEqualTo(Integer value) {
            addCriterion("award_type =", value, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeNotEqualTo(Integer value) {
            addCriterion("award_type <>", value, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeGreaterThan(Integer value) {
            addCriterion("award_type >", value, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("award_type >=", value, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeLessThan(Integer value) {
            addCriterion("award_type <", value, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeLessThanOrEqualTo(Integer value) {
            addCriterion("award_type <=", value, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeIn(List<Integer> values) {
            addCriterion("award_type in", values, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeNotIn(List<Integer> values) {
            addCriterion("award_type not in", values, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeBetween(Integer value1, Integer value2) {
            addCriterion("award_type between", value1, value2, "awardType");
            return (Criteria) this;
        }

        public Criteria andAwardTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("award_type not between", value1, value2, "awardType");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateIsNull() {
            addCriterion("promo_commission_rate is null");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateIsNotNull() {
            addCriterion("promo_commission_rate is not null");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateEqualTo(BigDecimal value) {
            addCriterion("promo_commission_rate =", value, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateNotEqualTo(BigDecimal value) {
            addCriterion("promo_commission_rate <>", value, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateGreaterThan(BigDecimal value) {
            addCriterion("promo_commission_rate >", value, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("promo_commission_rate >=", value, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateLessThan(BigDecimal value) {
            addCriterion("promo_commission_rate <", value, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("promo_commission_rate <=", value, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateIn(List<BigDecimal> values) {
            addCriterion("promo_commission_rate in", values, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateNotIn(List<BigDecimal> values) {
            addCriterion("promo_commission_rate not in", values, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("promo_commission_rate between", value1, value2, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoCommissionRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("promo_commission_rate not between", value1, value2, "promoCommissionRate");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeIsNull() {
            addCriterion("promo_effect_start_time is null");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeIsNotNull() {
            addCriterion("promo_effect_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeEqualTo(Date value) {
            addCriterion("promo_effect_start_time =", value, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeNotEqualTo(Date value) {
            addCriterion("promo_effect_start_time <>", value, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeGreaterThan(Date value) {
            addCriterion("promo_effect_start_time >", value, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("promo_effect_start_time >=", value, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeLessThan(Date value) {
            addCriterion("promo_effect_start_time <", value, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("promo_effect_start_time <=", value, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeIn(List<Date> values) {
            addCriterion("promo_effect_start_time in", values, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeNotIn(List<Date> values) {
            addCriterion("promo_effect_start_time not in", values, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeBetween(Date value1, Date value2) {
            addCriterion("promo_effect_start_time between", value1, value2, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoEffectStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("promo_effect_start_time not between", value1, value2, "promoEffectStartTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeIsNull() {
            addCriterion("promo_order_place_end_time is null");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeIsNotNull() {
            addCriterion("promo_order_place_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeEqualTo(Date value) {
            addCriterion("promo_order_place_end_time =", value, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeNotEqualTo(Date value) {
            addCriterion("promo_order_place_end_time <>", value, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeGreaterThan(Date value) {
            addCriterion("promo_order_place_end_time >", value, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("promo_order_place_end_time >=", value, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeLessThan(Date value) {
            addCriterion("promo_order_place_end_time <", value, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("promo_order_place_end_time <=", value, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeIn(List<Date> values) {
            addCriterion("promo_order_place_end_time in", values, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeNotIn(List<Date> values) {
            addCriterion("promo_order_place_end_time not in", values, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeBetween(Date value1, Date value2) {
            addCriterion("promo_order_place_end_time between", value1, value2, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderPlaceEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("promo_order_place_end_time not between", value1, value2, "promoOrderPlaceEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeIsNull() {
            addCriterion("promo_order_verify_end_time is null");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeIsNotNull() {
            addCriterion("promo_order_verify_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeEqualTo(Date value) {
            addCriterion("promo_order_verify_end_time =", value, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeNotEqualTo(Date value) {
            addCriterion("promo_order_verify_end_time <>", value, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeGreaterThan(Date value) {
            addCriterion("promo_order_verify_end_time >", value, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("promo_order_verify_end_time >=", value, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeLessThan(Date value) {
            addCriterion("promo_order_verify_end_time <", value, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("promo_order_verify_end_time <=", value, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeIn(List<Date> values) {
            addCriterion("promo_order_verify_end_time in", values, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeNotIn(List<Date> values) {
            addCriterion("promo_order_verify_end_time not in", values, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeBetween(Date value1, Date value2) {
            addCriterion("promo_order_verify_end_time between", value1, value2, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andPromoOrderVerifyEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("promo_order_verify_end_time not between", value1, value2, "promoOrderVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationIsNull() {
            addCriterion("full_return_ration is null");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationIsNotNull() {
            addCriterion("full_return_ration is not null");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationEqualTo(BigDecimal value) {
            addCriterion("full_return_ration =", value, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationNotEqualTo(BigDecimal value) {
            addCriterion("full_return_ration <>", value, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationGreaterThan(BigDecimal value) {
            addCriterion("full_return_ration >", value, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("full_return_ration >=", value, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationLessThan(BigDecimal value) {
            addCriterion("full_return_ration <", value, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("full_return_ration <=", value, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationIn(List<BigDecimal> values) {
            addCriterion("full_return_ration in", values, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationNotIn(List<BigDecimal> values) {
            addCriterion("full_return_ration not in", values, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("full_return_ration between", value1, value2, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andFullReturnRationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("full_return_ration not between", value1, value2, "fullReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueIsNull() {
            addCriterion("part_return_promo_value is null");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueIsNotNull() {
            addCriterion("part_return_promo_value is not null");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueEqualTo(BigDecimal value) {
            addCriterion("part_return_promo_value =", value, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueNotEqualTo(BigDecimal value) {
            addCriterion("part_return_promo_value <>", value, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueGreaterThan(BigDecimal value) {
            addCriterion("part_return_promo_value >", value, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("part_return_promo_value >=", value, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueLessThan(BigDecimal value) {
            addCriterion("part_return_promo_value <", value, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("part_return_promo_value <=", value, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueIn(List<BigDecimal> values) {
            addCriterion("part_return_promo_value in", values, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueNotIn(List<BigDecimal> values) {
            addCriterion("part_return_promo_value not in", values, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("part_return_promo_value between", value1, value2, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnPromoValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("part_return_promo_value not between", value1, value2, "partReturnPromoValue");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationIsNull() {
            addCriterion("part_return_ration is null");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationIsNotNull() {
            addCriterion("part_return_ration is not null");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationEqualTo(BigDecimal value) {
            addCriterion("part_return_ration =", value, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationNotEqualTo(BigDecimal value) {
            addCriterion("part_return_ration <>", value, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationGreaterThan(BigDecimal value) {
            addCriterion("part_return_ration >", value, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("part_return_ration >=", value, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationLessThan(BigDecimal value) {
            addCriterion("part_return_ration <", value, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationLessThanOrEqualTo(BigDecimal value) {
            addCriterion("part_return_ration <=", value, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationIn(List<BigDecimal> values) {
            addCriterion("part_return_ration in", values, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationNotIn(List<BigDecimal> values) {
            addCriterion("part_return_ration not in", values, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("part_return_ration between", value1, value2, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andPartReturnRationNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("part_return_ration not between", value1, value2, "partReturnRation");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeIsNull() {
            addCriterion("rebate_verify_begin_time is null");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeIsNotNull() {
            addCriterion("rebate_verify_begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeEqualTo(Date value) {
            addCriterion("rebate_verify_begin_time =", value, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeNotEqualTo(Date value) {
            addCriterion("rebate_verify_begin_time <>", value, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeGreaterThan(Date value) {
            addCriterion("rebate_verify_begin_time >", value, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("rebate_verify_begin_time >=", value, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeLessThan(Date value) {
            addCriterion("rebate_verify_begin_time <", value, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeLessThanOrEqualTo(Date value) {
            addCriterion("rebate_verify_begin_time <=", value, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeIn(List<Date> values) {
            addCriterion("rebate_verify_begin_time in", values, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeNotIn(List<Date> values) {
            addCriterion("rebate_verify_begin_time not in", values, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeBetween(Date value1, Date value2) {
            addCriterion("rebate_verify_begin_time between", value1, value2, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyBeginTimeNotBetween(Date value1, Date value2) {
            addCriterion("rebate_verify_begin_time not between", value1, value2, "rebateVerifyBeginTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeIsNull() {
            addCriterion("rebate_verify_end_time is null");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeIsNotNull() {
            addCriterion("rebate_verify_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeEqualTo(Date value) {
            addCriterion("rebate_verify_end_time =", value, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeNotEqualTo(Date value) {
            addCriterion("rebate_verify_end_time <>", value, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeGreaterThan(Date value) {
            addCriterion("rebate_verify_end_time >", value, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("rebate_verify_end_time >=", value, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeLessThan(Date value) {
            addCriterion("rebate_verify_end_time <", value, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("rebate_verify_end_time <=", value, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeIn(List<Date> values) {
            addCriterion("rebate_verify_end_time in", values, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeNotIn(List<Date> values) {
            addCriterion("rebate_verify_end_time not in", values, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeBetween(Date value1, Date value2) {
            addCriterion("rebate_verify_end_time between", value1, value2, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andRebateVerifyEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("rebate_verify_end_time not between", value1, value2, "rebateVerifyEndTime");
            return (Criteria) this;
        }

        public Criteria andFinalstatusIsNull() {
            addCriterion("finalstatus is null");
            return (Criteria) this;
        }

        public Criteria andFinalstatusIsNotNull() {
            addCriterion("finalstatus is not null");
            return (Criteria) this;
        }

        public Criteria andFinalstatusEqualTo(String value) {
            addCriterion("finalstatus =", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusNotEqualTo(String value) {
            addCriterion("finalstatus <>", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusGreaterThan(String value) {
            addCriterion("finalstatus >", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusGreaterThanOrEqualTo(String value) {
            addCriterion("finalstatus >=", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusLessThan(String value) {
            addCriterion("finalstatus <", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusLessThanOrEqualTo(String value) {
            addCriterion("finalstatus <=", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusLike(String value) {
            addCriterion("finalstatus like", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusNotLike(String value) {
            addCriterion("finalstatus not like", value, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusIn(List<String> values) {
            addCriterion("finalstatus in", values, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusNotIn(List<String> values) {
            addCriterion("finalstatus not in", values, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusBetween(String value1, String value2) {
            addCriterion("finalstatus between", value1, value2, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andFinalstatusNotBetween(String value1, String value2) {
            addCriterion("finalstatus not between", value1, value2, "finalstatus");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}