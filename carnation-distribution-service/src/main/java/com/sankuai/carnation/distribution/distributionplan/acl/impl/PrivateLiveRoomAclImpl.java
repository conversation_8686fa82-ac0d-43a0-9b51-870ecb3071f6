package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.carnation.distribution.distributionplan.acl.PrivateLiveRoomAcl;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.common.ResponseDTO;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomInfo;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.dto.liveroomadmin.LiveRoomsQueryByLiveIdsRequest;
import com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrivateLiveRoomAclImpl implements PrivateLiveRoomAcl {

   @Autowired
    private LiveRoomRpcService liveRoomRpcService;

    @Override
    public Date queryLivePreBeginTime(String liveId) {
        Transaction transaction = Cat.newTransaction("PrivateLiveRoomAcl", "queryLivePreBeginTime");
        try {
            LiveRoomsQueryByLiveIdsRequest  request = new LiveRoomsQueryByLiveIdsRequest();
            request.setLiveIds(Lists.newArrayList(liveId));
            request.setNeedAttr(false);
            ResponseDTO<List<LiveRoomInfo>> responseDto = liveRoomRpcService.queryLiveRoomsByLiveIds(request);
            if (responseDto == null || !responseDto.isSuccess()) {
                log.error("PrivateLiveRoomAcl.queryLivePreBeginTime invoke liveRoomRpcService.queryLiveRoomsByLiveIds failed, liveId:{}", liveId);
                throw new DistributionPlanException("查询直播信息失败");
            }

            if (CollectionUtils.isEmpty(responseDto.getData())) {
                throw new DistributionPlanException("查询直播信息列表为空");
            }

            LiveRoomInfo liveRoomInfo = responseDto.getData().get(0);
            return liveRoomInfo.getStartTime();
        } catch (Exception e) {
            log.error("PrivateLiveRoomAcl.queryLivePreBeginTime error, liveId:{}", liveId, e);
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }
}
