package com.sankuai.carnation.distribution.privatelive.distribution.domain.model;

import com.sankuai.carnation.distribution.privatelive.account.enums.PrivateLiveTaskTypeEnum;
import lombok.*;

import java.io.Serializable;

/**
 * @Author: bi<PERSON><PERSON>han
 * @CreateTime: 2024-08-19 15:16
 * @Description:
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PrivateLiveTaskCmd implements Serializable {

    /**
     *   字段: consultant_id
     *   说明: 咨询师账号ID
     */
    private Long accountId;

    /**
     *   字段: group_id
     *   说明: 绑定社群ID
     */
    private String groupId;

    /**
     *   字段: live_id
     *   说明: 直播计划ID
     */
    private String liveId;

    /**
     *   字段: anchor_id
     *   说明: 主播ID
     */
    private Long anchorId;

    /**
     *   字段: nickname
     *   说明: 昵称
     */
    private String nickname;

    /**
     *   字段: avatar_url
     *   说明: 头像
     */
    private String avatarUrl;

    /**
     *   字段: share_name
     *   说明: 分享名称
     */
    private String shareName;

    /**
     *   字段: actual_name
     *   说明: 真实姓名
     */
    private String actualName;

    /**
     *   字段: phone_number
     *   说明: 手机号
     */
    private String phoneNumber;

    /**
     *   字段: shop_id
     *   说明: 点评门店ID
     */
    private Long shopId;

    /**
     *   字段: wechat_number
     *   说明: 微信号
     */
    private String wechatNumber;

    /**
     *   字段: task_type
     *   说明: 1 代表咨询师，2 代表分销人（团员）
     */
    private PrivateLiveTaskTypeEnum taskType;


    private boolean autoApprove;

}
