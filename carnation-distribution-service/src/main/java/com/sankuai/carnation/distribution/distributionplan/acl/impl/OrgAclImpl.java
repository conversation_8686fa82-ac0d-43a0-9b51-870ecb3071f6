package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.aspose.words.Run;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.carnation.distribution.distributionplan.acl.OrgAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.EmpInfo;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.queryservice.exception.MDMThriftException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.omg.SendingContext.RunTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrgAclImpl implements OrgAcl {

    private static final String CAT_TYPE = OrgAcl.class.getSimpleName();

    @Autowired
    private EmpService empService;

    @Override
    public EmpInfo queryEmpInfo(Long empId) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "_queryEmpInfo");
        try {
            if (empId == null) {
                throw new IllegalArgumentException("empId为空");
            }
            Emp emp = empService.query(String.valueOf(empId), null);
            return buildEmpInfo(emp);
        } catch(MDMThriftException me) {
            log.error("EmpService.queryEmpInfo fail, empId : {}", empId, me);
            Cat.logError(me);
            return null;
        } finally {
            transaction.complete();
        }
    }

    private EmpInfo buildEmpInfo(Emp emp) {
        if (emp == null) {
            return null;
        }
        if (StringUtils.isBlank(emp.getMis())) {
            return null;
        }
        if (StringUtils.isBlank(emp.getName())) {
            return null;
        }
        return EmpInfo.builder()
                .mis(emp.getMis())
                .name(emp.getName())
                .build();
    }
}
