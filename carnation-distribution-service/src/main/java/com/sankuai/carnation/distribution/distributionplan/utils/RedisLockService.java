package com.sankuai.carnation.distribution.distributionplan.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.meituan.hotel.dlm.lock.Lock;
import com.meituan.hotel.dlm.service.IDistributedLockManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisLockService {

    private static final String CAT_TYPE = RedisLockService.class.getSimpleName();

    /**
     * 默认过期时间为一分钟
     */
    public static final int DEFAULT_EXPIRY_TIME_MILLIS = 60;

    @Resource
    private IDistributedLockManager distributedLockManager;

    /**
     * 默认等待时间0秒，未获取到锁直接抛出异常
     * 获取锁失败将抛出异常，可以自己做异常处理
     */
    public void tryLock(String key, Func func) {
        tryLock(key, func, 0);
    }

    /**
     * @param timeout 获取锁等待超时时间，单位：秒
     */
    public void tryLock(String key, Func func, int timeout) {
        tryLock(key, () -> {
            func.invoke();
            return "Hello World!";
        }, timeout);
    }

    /**
     * 带返回值的
     */
    public <T> T tryLock(String key, Func1<T> func1) {
        return tryLock(key, func1, 0);
    }

    /**
     * 带返回值的
     */
    public <T> T tryLock(String key, Func1<T> func1, int timeout) {
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("创建分布式锁时未传入key");
        }
        Lock lock = distributedLockManager.getReentrantLock(key, DEFAULT_EXPIRY_TIME_MILLIS);
        try {
            if (lock.tryLock(timeout, TimeUnit.SECONDS)) {
                try {
                    log.info("Locker-tryLock success,key:{}", Environment.getAppName()+"."+key);
                    return func1.invoke();
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("Locker-tryLock fail,key:{}",key);
                Cat.logEvent(CAT_TYPE, "tryLock duplicate key:" + key);
                throw new RuntimeException("存在重复提交，请稍后再试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            Cat.logEvent(CAT_TYPE, "tryLock interrupted, key:" + key);
        }
        return null;
    }
}
