package com.sankuai.carnation.distribution.distributor.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.distributor.converter.DistributorAttrConverter;
import com.sankuai.carnation.distribution.distributor.converter.DistributorConverter;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorAttrBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorBindBO;
import com.sankuai.carnation.distribution.distributor.domain.bo.DistributorGroupBO;
import com.sankuai.carnation.distribution.distributor.dto.request.PageQueryDistributorRequest;
import com.sankuai.carnation.distribution.distributor.repository.DistributorGroupRepository;
import com.sankuai.carnation.distribution.distributor.repository.DistributorRepository;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorAttrMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupBindMapper;
import com.sankuai.carnation.distribution.distributor.repository.dao.DistributorMapper;
import com.sankuai.carnation.distribution.distributor.repository.db.*;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageDataDTO;
import com.sankuai.carnation.distribution.privatelive.consultant.dto.base.PageInfoDTO;
import com.sankuai.carnation.distribution.privatelive.distribution.eums.DistributionApproveStatusEnum;
import com.sankuai.carnation.distribution.privatelive.distribution.utils.NullableUtils;
import com.sankuai.carnation.distribution.privatelive.distribution.utils.PageHelperUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: bianzhan
 * @CreateTime: 2024-08-07 20:28
 * @Description:
 */
@Service
public class DistributorRepositoryImpl implements DistributorRepository {

    @Resource
    private DistributorGroupBindMapper bindMapper;

    @Resource
    private DistributorMapper distributorMapper;

    @Resource
    private DistributorConverter distributorConverter;

    @Resource
    private DistributorAttrConverter attrConverter;
    @Resource
    private DistributorAttrMapper attrMapper;

    @Resource
    private DistributorRootService distributorRootService;

    @Resource
    private DistributorGroupRepository distributorGroupRepository;


    @Override
    public DistributorBO queryByDistributorId(Long distributorId) {
        Distributor distributor = distributorMapper.selectByPrimaryKey(distributorId);
        if (ObjectUtils.isEmpty(distributor)) {
            return null;
        }
        DistributorBO distributorBO = distributorConverter.toEntity(distributor);
        if (!ObjectUtils.isEmpty(distributorBO)) {
            List<DistributorAttr> distributorAttrList = Optional.ofNullable(getDistributorAttrs(distributorId)).orElse(Lists.newArrayList());
            List<DistributorAttrBO> distributorAttrBOList = attrConverter.toEntityList(distributorAttrList);
            distributorBO.setDistributorAttrList(distributorAttrBOList);
        }
        Map<Long, DistributorBindBO> distributorGroupBOMap = queryRelationDistributorGroupList(Collections.singletonList(distributorId));
        DistributorBindBO distributorBindBO = distributorGroupBOMap.get(distributorId);
        DistributorGroupBO distributorGroupBO = NullableUtils.nullableOption(distributorBindBO, DistributorBindBO::getDistributorGroupBO);
        distributorBO.setDistributorGroup(distributorGroupBO);
        distributorBO.setDistributorGroupId(NullableUtils.nullableOption(distributorGroupBO, DistributorGroupBO::getGroupId));
        return distributorBO;
    }

    @Override
    public DistributorBindBO queryBindByDistributorId(Long distributorId) {
        Distributor distributor = distributorMapper.selectByPrimaryKey(distributorId);
        if (ObjectUtils.isEmpty(distributor)) {
            return null;
        }
        DistributorBO distributorBO = buildDistributorBO(distributor);
        Map<Long, DistributorBindBO> distributorGroupBOMap = queryRelationDistributorGroupList(Collections.singletonList(distributorId));
        DistributorBindBO distributorBindBO = Optional.ofNullable(distributorGroupBOMap.get(distributorId)).orElseGet(()-> {
          return DistributorBindBO.builder()
                  .status(DistributionApproveStatusEnum.UN_APPLY)
                  .distributorId(distributorId)
                  .build();
        });
        DistributorGroupBO distributorGroupBO = NullableUtils.nullableOption(distributorBindBO, DistributorBindBO::getDistributorGroupBO);
        distributorBO.setDistributorGroup(distributorGroupBO);
        distributorBO.setDistributorGroupId(NullableUtils.nullableOption(distributorGroupBO, DistributorGroupBO::getGroupId));
        distributorBindBO.setDistributorBO(distributorBO);
        return distributorBindBO;
    }

    public List<DistributorBO> queryByDistributorIdList(List<Long> distributorIdList) {
        if(ObjectUtils.isEmpty(distributorIdList)) {
            return Lists.newArrayList();
        }
        DistributorCriteria criteria = new DistributorCriteria();
        criteria.createCriteria()
               .andIdIn(distributorIdList)
               .andStatusEqualTo(DistributionStatusEnum.VALID.getCode());
        ;
        List<Distributor> list = distributorMapper.selectByExample(criteria);
        if (ObjectUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        Map<Long,Distributor> distributorMap = list.stream().collect(Collectors.toMap(Distributor::getId, Function.identity(),(o, n) -> o));

       return distributorIdList.stream().map(distributorId->{
            Distributor distributor = distributorMap.get(distributorId);
            if(ObjectUtils.isEmpty(distributor)) {
               return null;
            }
           return buildDistributorBO(distributor);
       }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DistributorBO buildDistributorBO(Distributor distributor) {
        DistributorBO distributorBO = distributorConverter.toEntity(distributor);
        Map<Long, List<DistributorAttrBO>> distributeMaps =
                queryDistributorAttrMap(Collections.singletonList(distributor.getId()));
        distributorBO.setDistributorAttrList(distributeMaps.getOrDefault(distributorBO.getDistributorId(), Lists.newArrayList()));
        return distributorBO;
    }



    public List<DistributorBindBO> queryBindByDistributorIdList(List<Long> distributorIdList) {
       List<DistributorBO> distributorBOList = queryByDistributorIdList(distributorIdList);
        Map<Long, DistributorBindBO> distributorGroupBOMap = queryRelationDistributorGroupList(distributorBOList.stream()
                .map(DistributorBO::getDistributorId).collect(Collectors.toList()));
       return fillDistributorBindBO(distributorBOList,distributorGroupBOMap);
    }

    private Map<Long, DistributorBindBO> queryRelationDistributorGroupList(List<Long> distributorGroupId) {
        return Optional.ofNullable(distributorGroupRepository.queryRelationDistributorGroupList(distributorGroupId))
                .orElse(Maps.newHashMap());
    }

    private List<DistributorAttr> getDistributorAttrs(Long distributorId) {
        DistributorAttrExample distributorAttrExample = new DistributorAttrExample();
        distributorAttrExample.createCriteria().andDistributorIdEqualTo(distributorId);
        return attrMapper.selectByExample(distributorAttrExample);
    }

    private Map<Long, List<DistributorAttrBO>> queryDistributorAttrMap(List<Long> distributorIdList) {
        if (CollectionUtils.isEmpty(distributorIdList)) {
            return Maps.newHashMap();
        }
        DistributorAttrExample distributorAttrExample = new DistributorAttrExample();
        distributorAttrExample.createCriteria().andDistributorIdIn(distributorIdList);
        List<DistributorAttr> distributorAttrList = attrMapper.selectByExample(distributorAttrExample);
        return distributorAttrList.stream().collect(Collectors.groupingBy(DistributorAttr::getDistributorId, Collectors.mapping(
                attrConverter::toEntity, Collectors.toList()
        )));
    }

    @Override
    public long save(DistributorBO distributorBO) {
        return distributorRootService.setDistributor(distributorBO);
    }

    @Override
    public PageDataDTO<DistributorBindBO> pageQueryDistributor(PageQueryDistributorRequest request) {
        Page<DistributorGroupBind> page = PageHelperUtils.pageQuery(request.getPageNum(), request.getPageSize(), () -> {
            DistributorGroupBindCriteria criteria = new DistributorGroupBindCriteria();
            DistributorGroupBindCriteria.Criteria criteria2 = criteria.createCriteria()
                    .andDistributorGroupEqualTo(NullableUtils.safeNullableOption(request.getDistributorGroupId(), Long::intValue));
            criteria.setOrderByClause("add_time desc,id desc");
            if (!ObjectUtils.isEmpty(request.getStatus())) {
                criteria2.andStatusEqualTo(request.getStatus());
            }
            return bindMapper.selectByExample(criteria);

        });
        return PageDataDTO.<DistributorBindBO>builder()
                .pageInfoDTO(PageInfoDTO.builder()
                        .currentPageNum(request.getPageNum())
                        .pageSize(request.getPageSize())
                        .totalCount(NullableUtils.safeNullableOption(page.getTotal(), Long::intValue))
                        .build())
                .list(Optional.ofNullable(page.getResult())
                        .map(this::createDistributorBindBOList)
                        .orElse(Lists.newArrayList()))
                .build();
    }

    @Override
    public List<DistributorBindBO> queryDistributorByAccountIdList(Integer userType, List<Long> accountId) {
        DistributorCriteria criteria = new DistributorCriteria();
        criteria.createCriteria()
                .andUserIdIn(accountId)
                .andUserTypeEqualTo(userType)
                .andStatusEqualTo(DistributionStatusEnum.VALID.getCode());
        ;
        List<Distributor> list = distributorMapper.selectByExample(criteria);
        if (ObjectUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<DistributorBO> distributorBOList = list.stream().map(this::buildDistributorBO).collect(Collectors.toList());
        Map<Long, DistributorBindBO> distributorGroupBOMap = queryRelationDistributorGroupList(distributorBOList.stream()
                .map(DistributorBO::getDistributorId).collect(Collectors.toList()));
        return fillDistributorBindBO(distributorBOList,distributorGroupBOMap);
    }


    @Override
    public DistributorBindBO queryDistributorByAccountId(Integer userType, Long accountId) {
        List<DistributorBindBO> distributorBOList = queryDistributorByAccountIdList(userType, Collections.singletonList(accountId));
        return Optional.ofNullable(distributorBOList).map(CollectionUtil::getFirst)
                .orElse(null);
    }

    @Override
    public List<DistributorBO> queryDistributorByGroupIds(List<Long> distributorGroupIds) {
        if (CollectionUtils.isEmpty(distributorGroupIds)) {
            return Lists.newArrayList();
        }
        List<Integer> groupIds = distributorGroupIds.stream()
                .map(e -> NullableUtils.safeNullableOption(e, Long::intValue))
                .collect(Collectors.toList());
        DistributorGroupBindCriteria criteria = new DistributorGroupBindCriteria();
        criteria.createCriteria()
                .andDistributorGroupIn(groupIds);
        List<DistributorGroupBind> distributorGroupBinds = bindMapper.selectByExample(criteria);
        return Optional.ofNullable(distributorGroupBinds)
                .map(this::createDistributorBOList)
                .orElse(Lists.newArrayList());
    }

    private List<DistributorBO> createDistributorBOList(List<DistributorGroupBind> list) {
        return list.stream().map(this::buildDistributorBOForBind).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<DistributorBindBO> fillDistributorBindBO(List<DistributorBO> distributorBOList,Map<Long, DistributorBindBO> distributorGroupBOMap) {
        return distributorBOList.stream().map(distributorBO -> {
            DistributorBindBO distributorBindBO = Optional.ofNullable(distributorGroupBOMap.get(distributorBO.getDistributorId()))
                    .orElse(new DistributorBindBO());
            distributorBindBO.setDistributorId(distributorBO.getDistributorId());
            distributorBindBO.setDistributorBO(distributorBO);
            distributorBindBO.setDistributorGroupBO(NullableUtils.nullableOption(distributorBindBO, DistributorBindBO::getDistributorGroupBO));
            DistributorGroupBO distributorGroupBO = NullableUtils.nullableOption(distributorBindBO, DistributorBindBO::getDistributorGroupBO);
            distributorBO.setDistributorGroup(distributorGroupBO);
            distributorBO.setDistributorGroupId(NullableUtils.safeNullableOption(distributorGroupBO, DistributorGroupBO::getGroupId));
            return distributorBindBO;
        }).collect(Collectors.toList());
    }


    private List<DistributorBindBO> createDistributorBindBOList(List<DistributorGroupBind> list) {
        if(ObjectUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return queryBindByDistributorIdList(list.stream().map(DistributorGroupBind::getDistributor).collect(Collectors.toList()));
    }

    private DistributorBO buildDistributorBOForBind(DistributorGroupBind distributorGroupBind) {
        DistributorBO distributorBO = queryByDistributorId(distributorGroupBind.getDistributor());
        if (Objects.isNull(distributorBO)) {
            return null;
        }
        distributorBO.setDistributorGroupId(distributorGroupBind.getDistributorGroup());
        return distributorBO;
    }
}
