package com.sankuai.carnation.distribution.distributionplan.acl.model;

import lombok.*;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategory {

    private Long platCategoryId;

    private String categoryName;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProductCategory that = (ProductCategory) o;
        return Objects.equals(platCategoryId, that.platCategoryId) && Objects.equals(categoryName, that.categoryName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(platCategoryId, categoryName);
    }
}
