package com.sankuai.carnation.distribution.distributionplan.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.carnation.distribution.distributionplan.acl.DistributorGroupAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.PrivateLiveRoomAcl;
import com.sankuai.carnation.distribution.distributionplan.enums.PrivateLiveDistributionPlanOperateTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.PrivateLiveDistributionPlanStatusEnum;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.request.PrivateLiveDistributionPlanCreateRequest;
import com.sankuai.carnation.distribution.distributionplan.utils.DateUtils;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.carnation.distribution.distributionplan.vo.PrivateLiveDistributionPlanVO;
import com.sankuai.carnation.distribution.distributionplan.request.PrivateLiveDistributionPlanOperateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.PrivateLiveDistributionPlanQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.service.PrivateLiveDistributionPlanService;
import com.sankuai.dzrtc.privatelive.auth.sdk.AnchorAuthUtils;
import com.sankuai.dzrtc.privatelive.operation.api.common.ResponseDTO;
import com.sankuai.dzrtc.privatelive.operation.api.enums.AuthCodeEnum;
import com.sankuai.dzusergrowth.common.api.response.PageResult;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.dto.DistributionPlanDistributorDTO;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.*;
import com.sankuai.dzusergrowth.distribution.plan.api.request.*;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanCreator;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanOperator;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanSubjectField;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService;
import com.sankuai.dzusergrowth.common.api.enums.DistributionChannelEnum;
import com.sankuai.dzusergrowth.common.api.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@MdpPigeonServer(url = "com.sankuai.carnation.distribution.distributionplan.service.PrivateLiveDistributionPlanService")
@Slf4j
public class PrivateLiveDistributionPlanServiceImpl implements PrivateLiveDistributionPlanService {

    private static final String CAT_TYPE = PrivateLiveDistributionPlanService.class.getSimpleName();

    private static final Integer MAX_COMMISSION_RATE = 50;

    private static final long MILLISECONDS_PER_HOUR = 60 * 60 * 1000;

    private static final long MIN_HOUR_INTERVAL = 2160L;

    private static final int DISTRIBUTOR_GROUP_LIST_MAX_SIZE = 10;

    private static final String ONE_HUNDRED = "100";

    /**
     * 重启最大次数的默认值
     */
    private static final int RESTART_MAX_COUNT_DEFAULT = 3;

    /**
     * 分销计划最长生效日
     */
    private static final int DISTRIBUTION_PLAN_MAX_EFFECTIVE_DAY = 121;

    @Resource
    private DistributionPlanCommandService distributionPlanCommandService;

    @Resource
    private DistributionPlanQueryService distributionPlanQueryService;

    @Resource
    private DistributorGroupAcl distributorGroupAcl;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private PrivateLiveRoomAcl privateLiveRoomAcl;

    @Override
    public RemoteResponse createDistributionPlan(PrivateLiveDistributionPlanCreateRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "createDistributionPlan");
        try {
            Long loginAnchorId = AnchorAuthUtils.getMainAnchorId();
            if (loginAnchorId == null) {
               throw new IllegalArgumentException("登陆主播id为空");
            }
            validatePrivateLiveDistributionPlanCreateRequest(request);

            Date livePreEndDate = privateLiveRoomAcl.queryLivePreBeginTime(request.getLiveId());
            if (livePreEndDate == null) {
                throw new IllegalArgumentException("查询直播预计结束时间为空");
            }
            validatePreEndTime(request.getPreEndTime(), livePreEndDate);

            ResponseDTO<Boolean> authResponse =  AnchorAuthUtils.checkAuth(AuthCodeEnum.DISTRIBUTOR_GROUP_CODE.getCode(), request.getLiveId());
            if (null == authResponse || !authResponse.isSuccess() || authResponse.getData() == null || !authResponse.getData()) {
                throw new IllegalArgumentException("登陆主播id无权限");
            }
            boolean distributorGroupBindLiveIdCheckResult = distributorGroupAcl.checkDistributorGroupBindLiveId(request.getDistributorGroupId(), request.getLiveId());
            if (!distributorGroupBindLiveIdCheckResult) {
                throw new IllegalArgumentException("分销商与直播之间并未关联");
            }

            Map<Integer, BigDecimal> commissionRateMap = Maps.newHashMap();
            commissionRateMap.put(DistributionCommissionTypeEnum.DISTRIBUTOR_GROUP_COMMISSION.getCode(), new BigDecimal(request.getCommissionRate()).divide(new BigDecimal(ONE_HUNDRED)));

            DistributionPlanSubjectField planSubjectField = DistributionPlanSubjectField.builder()
                    .subjectType(DistributionPlanSubjectTypeEnum.PRIVATE_LIVE.getCode())
                    .field1(String.valueOf(request.getLiveId()))
                    .build();

            DistributionPlanDistributorDTO distributor = DistributionPlanDistributorDTO.builder()
                    .distributorId(String.valueOf(request.getDistributorGroupId()))
                    .build();

            DistributionPlanCreateRequest distributionPlanCreateRequest = DistributionPlanCreateRequest.builder()
                    .sceneCode(DistributionPlanSceneCodeEnum.DEFAULT_COMMISSION_CONFIG.getCode())
                    .channel(DistributionChannelEnum.PRIVATE_LIVE_CONSULTANT.getCode())
                    .planType(DistributionPlanTypeEnum.EXCLUSIVE_PLAN.getType())
                    .beginTime(System.currentTimeMillis())
                    .endTime(request.getPreEndTime())
                    .commissionRateMap(commissionRateMap)
                    .subjectField(planSubjectField)
                    .distributorList(Lists.newArrayList(distributor))
                    .creator(DistributionPlanCreator.builder().creatorId(String.valueOf(loginAnchorId)).build())
                    .operator(DistributionPlanOperator.builder().operatorId(String.valueOf(loginAnchorId)).build())
                    .build();
            Response response = redisLockService.tryLock(String.format("createPrivateLiveDistributionPlan_%s_%s", request.getLiveId(), request.getDistributorGroupId()),
                    () -> distributionPlanCommandService.createDistributionPlan(distributionPlanCreateRequest), 3);
            if (response.respFailed()) {
                log.error("PrivateLiveDistributionPlanService.createDistributionPlan invoke distributionPlanCommandService.createDistributionPlan failed,request={}, response:{}", request, JSONObject.toJSONString(response));
                throw new DistributionPlanException(response.getMessage());
            }
            MetricHelper.build().name("createPrivateLiveDistributionPlan").tag("result", "success").count();
            return RemoteResponse.success(null);
        } catch (IllegalArgumentException ie) {
            log.error("PrivateLiveDistributionPlanServiceImpl.createDistributionPlan illegal error,request={}", request, ie);
            transaction.setStatus(ie);
            return RemoteResponse.fail(ie.getMessage());
        } catch (DistributionPlanException de) {
            log.error("PrivateLiveDistributionPlanServiceImpl.createDistributionPlan biz error,request={}", request, de);
            transaction.setStatus(de);
            return RemoteResponse.fail("创建分销计划失败");
        } catch (Exception e) {
            log.error("PrivateLiveDistributionPlanServiceImpl.createDistributionPlan error,request={}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail("创建分销计划失败");
        } finally {
            transaction.complete();
        }
    }

    private void validatePreEndTime(Long preEndTime, Date livePreEndDate) {
        long dateInterval = preEndTime - livePreEndDate.getTime();
        if (dateInterval < MIN_HOUR_INTERVAL * MILLISECONDS_PER_HOUR) {
            throw new IllegalArgumentException("预计结束时间与直播预计结束时间的间隔需大于等于2160个小时");
        }

        Date planPreEndDate = new Date(preEndTime);
        Date maxEndDate = DateUtils.addDay(planPreEndDate, Lion.getInt(MdpContextUtils.getAppKey(), "private.live.distribution.plan.max.effective.day", DISTRIBUTION_PLAN_MAX_EFFECTIVE_DAY));
        if (planPreEndDate.after(maxEndDate)) {
            throw new IllegalArgumentException("生效时间最长到直播预计结束时间后的120天");
        }
    }

    private void validatePrivateLiveDistributionPlanCreateRequest(PrivateLiveDistributionPlanCreateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }

        if (request.getDistributorGroupId() == null) {
            throw new IllegalArgumentException("分销商id不能为空");
        }

        if (StringUtils.isEmpty(request.getLiveId())) {
            throw new IllegalArgumentException("直播id不能为空");
        }

        if (request.getPreEndTime() == null) {
            throw new IllegalArgumentException("预计结束时间不能为空");
        }

        if (StringUtils.isEmpty(request.getCommissionRate())) {
            throw new IllegalArgumentException("佣金率不能为空");
        }

        int commissionRate = Integer.parseInt(request.getCommissionRate());
        if (commissionRate < 0 || commissionRate > MAX_COMMISSION_RATE) {
            throw new IllegalArgumentException("请填写0-50之间的整数，包含0和50");
        }
    }

    @Override
    public RemoteResponse operateDistributionPlan(PrivateLiveDistributionPlanOperateRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "operateDistributionPlan");
        try {
            Long loginAnchorId = AnchorAuthUtils.getMainAnchorId();
            if (loginAnchorId == null) {
                throw new IllegalArgumentException("登陆主播id为空");
            }
            validatePrivateLiveDistributionPlanOperateRequest(request);
            int maxRestartCount = Lion.getInt(MdpContextUtils.getAppKey(), "private.live.distribution.plan.max.restart.count", RESTART_MAX_COUNT_DEFAULT);

            PageResult<DistributionPlanDTO> historyDistributionPlanPageResult = pageQueryHistoryDistributionPlan(request.getDistributionPlanId(), maxRestartCount);
            Optional<DistributionPlanDTO> distributionPlanDtoOptional = historyDistributionPlanPageResult.getData().stream().filter(dto -> dto.getPlanId().equals(request.getDistributionPlanId())).findFirst();
            if (!distributionPlanDtoOptional.isPresent()) {
                throw new IllegalArgumentException("最新的分销计划未找到，请刷新后重试");
            }

            if (!distributionPlanDtoOptional.get().getSceneCode().equals(DistributionPlanSceneCodeEnum.DEFAULT_COMMISSION_CONFIG.getCode()) ||
                !distributionPlanDtoOptional.get().getChannel().equals(DistributionChannelEnum.PRIVATE_LIVE_CONSULTANT.getCode())) {
                throw new IllegalArgumentException("无权限变更非私域直播的分销计划");
            }

            String liveId = distributionPlanDtoOptional.get().getSubject().getField1();
            ResponseDTO<Boolean> authResponse =  AnchorAuthUtils.checkAuth(AuthCodeEnum.DISTRIBUTOR_GROUP_CODE.getCode(), liveId);
            if (null == authResponse || !authResponse.isSuccess() || authResponse.getData() == null || !authResponse.getData()) {
                throw new IllegalArgumentException("登陆主播id无权限");
            }

            if (historyDistributionPlanPageResult.getTotalCount() > maxRestartCount && request.getOperateType().equals(PrivateLiveDistributionPlanOperateTypeEnum.ENABLE.getCode())) {
                throw new IllegalArgumentException("重启次数超过上限");
            }
            Long operateTime = System.currentTimeMillis();
            if (request.getOperateType().equals(PrivateLiveDistributionPlanOperateTypeEnum.ENABLE.getCode())) {
                validatePrivateLiveDistributionPlanCanEnable(distributionPlanDtoOptional.get(), operateTime);
                DistributionPlanCreateRequest distributionPlanCreateRequest = buildDistributionPlanCreateRequest(distributionPlanDtoOptional.get(), loginAnchorId, operateTime);
                Response response = redisLockService.tryLock(String.format("createPrivateLiveDistributionPlan_%s_%s", liveId, distributionPlanDtoOptional.get().getDistributor().getDistributorId()),
                        () -> distributionPlanCommandService.createDistributionPlan(distributionPlanCreateRequest), 3);
                if (response.respFailed()) {
                    throw new DistributionPlanException(response.getMessage());
                }
                return RemoteResponse.success(null);
            }

            DistributionPlanOperateRequest distributionPlanOperateRequest = DistributionPlanOperateRequest.builder()
                    .planId(request.getDistributionPlanId())
                    .sceneCode(DistributionPlanSceneCodeEnum.DEFAULT_COMMISSION_CONFIG.getCode())
                    .channel(DistributionChannelEnum.PRIVATE_LIVE_CONSULTANT.getCode())
                    .operateType(DistributionPlanOperateTypeEnum.TERMINATE.getCode())
                    .terminateTime(operateTime)
                    .operator(DistributionPlanOperator.builder().operatorId(String.valueOf(loginAnchorId)).build())
                    .build();
            Response response = redisLockService.tryLock(String.format("operatePrivateLiveDistributionPlan_%s", request.getDistributionPlanId()),
                    () -> distributionPlanCommandService.operateDistributionPlan(distributionPlanOperateRequest), 3);
            if (response.respFailed()) {
                throw new DistributionPlanException(response.getMessage());
            }
            return RemoteResponse.success(null);
        } catch (IllegalArgumentException ie) {
            log.error("PrivateLiveDistributionPlanServiceImpl.operateDistributionPlan illegal error,request={}", request, ie);
            transaction.setStatus(ie);
            return RemoteResponse.fail(ie.getMessage());
        } catch (DistributionPlanException de) {
            log.error("PrivateLiveDistributionPlanServiceImpl.operateDistributionPlan biz error,request={}", request, de);
            transaction.setStatus(de);
            return RemoteResponse.fail("更新分销计划失败");
        } catch (Exception e) {
            log.error("PrivateLiveDistributionPlanServiceImpl.operateDistributionPlan error,request={}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail("更新分销计划失败");
        } finally {
            transaction.complete();
        }
    }

    private PageResult<DistributionPlanDTO> pageQueryHistoryDistributionPlan(Long distributionPlanId, int maxRestartCount) {
        // 重启三次后该主体会关联4个分销计划，因为pageSize需要➕1
        DistributionPlanHistoryPageQueryRequest pageQueryRequest = DistributionPlanHistoryPageQueryRequest.builder()
                .planId(distributionPlanId)
                .pageNo(1)
                .pageSize(maxRestartCount+1).build();
        Response<PageResult<DistributionPlanDTO>> distributionPlanResponse = distributionPlanQueryService.pageQueryDistributionPlanHistory(pageQueryRequest);
        if (distributionPlanResponse.respFailed() || distributionPlanResponse.getData() == null) {
            throw new DistributionPlanException("查询分销计划失败");
        }
        if (CollectionUtils.isEmpty(distributionPlanResponse.getData().getData())) {
            throw new DistributionPlanException("分销计划列表为空");
        }
        return distributionPlanResponse.getData();
    }

    private DistributionPlanCreateRequest buildDistributionPlanCreateRequest(DistributionPlanDTO distributionPlan, Long loginAnchorId, Long operateTime) {
        Map<Integer, BigDecimal> commissionRateMap = Maps.newHashMap();
        commissionRateMap.putAll(distributionPlan.getCommissionRateMap());

        DistributionPlanSubjectField planSubjectField = DistributionPlanSubjectField.builder()
                .subjectType(distributionPlan.getSubject().getSubjectType())
                .field1(distributionPlan.getSubject().getField1())
                .build();

        DistributionPlanDistributorDTO distributor = DistributionPlanDistributorDTO.builder()
                .distributorId(distributionPlan.getDistributor().getDistributorId())
                .build();

        Long preEndTime = Long.valueOf(distributionPlan.getExtFieldMap().get(DistributionPlanExtFieldEnum.PRE_END_TIME.getFieldId()));

        return  DistributionPlanCreateRequest.builder()
                .sceneCode(DistributionPlanSceneCodeEnum.DEFAULT_COMMISSION_CONFIG.getCode())
                .channel(DistributionChannelEnum.PRIVATE_LIVE_CONSULTANT.getCode())
                .planType(DistributionPlanTypeEnum.EXCLUSIVE_PLAN.getType())
                .beginTime(operateTime)
                .endTime(preEndTime)
                .commissionRateMap(commissionRateMap)
                .subjectField(planSubjectField)
                .distributorList(Lists.newArrayList(distributor))
                .creator(DistributionPlanCreator.builder().creatorId(String.valueOf(loginAnchorId)).build())
                .operator(DistributionPlanOperator.builder().operatorId(String.valueOf(loginAnchorId)).build())
                .build();
    }

    private void validatePrivateLiveDistributionPlanCanEnable(DistributionPlanDTO distributionPlan, Long operateTime) {
        if (distributionPlan.getEndTime().before(distributionPlan.getBeginTime())) {
            throw new IllegalArgumentException("分销计划已取消");
        }

        Long preEndTime = Long.valueOf(distributionPlan.getExtFieldMap().get(DistributionPlanExtFieldEnum.PRE_END_TIME.getFieldId()));
        if (preEndTime <= operateTime) {
            throw new IllegalArgumentException("分销计划已过期");
        }

        if (distributionPlan.getBeginTime().getTime() > operateTime) {
            throw new IllegalArgumentException("分销计划还未生效");
        }

        if (distributionPlan.getEndTime().getTime() == preEndTime) {
            throw new IllegalArgumentException("分销计划生效中，暂不支持启用操作");
        }
    }

    private void validatePrivateLiveDistributionPlanOperateRequest(PrivateLiveDistributionPlanOperateRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求为空");
        }

        if (request.getDistributionPlanId() == null) {
            throw new IllegalArgumentException("分销计划id为空");
        }

        PrivateLiveDistributionPlanOperateTypeEnum operateType = PrivateLiveDistributionPlanOperateTypeEnum.fromCode(request.getOperateType());
        if (operateType == null) {
            throw new IllegalArgumentException("无效的操作类型");
        }
    }

    @Override
    public RemoteResponse<List<PrivateLiveDistributionPlanVO>> queryDistributionPlan(PrivateLiveDistributionPlanQueryRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryDistributionPlan");
        try {
            validatePrivateLiveDistributionPlanQueryRequest(request);

            List<DistributionPlanDistributorDTO> distributors = Lists.newArrayList();
            request.getDistributorGroupIds().forEach(distributionGroupId -> {
                DistributionPlanDistributorDTO distributor = DistributionPlanDistributorDTO.builder()
                        .distributorId(String.valueOf(distributionGroupId))
                        .build();
                distributors.add(distributor);
            });

            DistributionPlanQueryRequest distributionPlanQueryRequest = DistributionPlanQueryRequest.builder()
                    .sceneCode(DistributionPlanSceneCodeEnum.DEFAULT_COMMISSION_CONFIG.getCode())
                    .channel(DistributionChannelEnum.PRIVATE_LIVE_CONSULTANT.getCode())
                    .subjectFieldList(Lists.newArrayList(DistributionPlanSubjectField.builder()
                            .subjectType(DistributionPlanSubjectTypeEnum.PRIVATE_LIVE.getCode())
                            .field1(request.getLiveId())
                            .build()))
                    .distributors(distributors)
                    .build();

            Response<List<DistributionPlanDTO>> response = distributionPlanQueryService.queryLatestDistributionPlan(distributionPlanQueryRequest);
            if (response.respFailed()) {
                throw new DistributionPlanException(response.getMessage());
            }

            Map<DistributionPlanDistributorDTO, DistributionPlanDTO> distributor2PlanMap = Maps.newHashMap();
            response.getData().forEach(distributionPlanDTO -> distributor2PlanMap.put(distributionPlanDTO.getDistributor(), distributionPlanDTO));

            List<PrivateLiveDistributionPlanVO> planVoList = Lists.newArrayList();
            request.getDistributorGroupIds().forEach(distributionGroupId -> {
                DistributionPlanDistributorDTO distributor = DistributionPlanDistributorDTO.builder()
                        .distributorId(String.valueOf(distributionGroupId))
                        .build();
                DistributionPlanDTO distributionPlanDTO = distributor2PlanMap.get(distributor);
                if (distributionPlanDTO == null) {
                    PrivateLiveDistributionPlanVO unConfiguredPlanVo = buildUnConfigDistributionPlan(request, distributionGroupId);
                    planVoList.add(unConfiguredPlanVo);
                    return;
                }

                planVoList.add(buildPrivateLiveDistributionPlanVO(distributionPlanDTO));
            });
            return RemoteResponse.success(planVoList);
        } catch (IllegalArgumentException ie) {
            log.error("PrivateLiveDistributionPlanServiceImpl.queryDistributionPlan illegal error,request={}", request, ie);
            transaction.setStatus(ie);
            return RemoteResponse.fail(ie.getMessage());
        } catch (Exception e) {
            log.error("PrivateLiveDistributionPlanServiceImpl.queryDistributionPlan error,request={}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.fail("查询分销计划失败");
        } finally {
            transaction.complete();
        }
    }

    private PrivateLiveDistributionPlanVO buildUnConfigDistributionPlan(PrivateLiveDistributionPlanQueryRequest request, Long distributionGroupId) {
        PrivateLiveDistributionPlanVO planVO = new PrivateLiveDistributionPlanVO();
        planVO.setDistributionPlanId(null);
        planVO.setDistributionGroupId(String.valueOf(distributionGroupId));
        planVO.setLiveId(request.getLiveId());
        planVO.setStatus(PrivateLiveDistributionPlanStatusEnum.UNCONFIGURED.getCode());
        planVO.setCommissionRate(null);
        planVO.setEndTime(null);
        return planVO;
    }

    private PrivateLiveDistributionPlanVO buildPrivateLiveDistributionPlanVO(DistributionPlanDTO distributionPlan) {
        BigDecimal commissionRate = distributionPlan.getCommissionRateMap().get(DistributionCommissionTypeEnum.DISTRIBUTOR_GROUP_COMMISSION.getCode());

        PrivateLiveDistributionPlanVO planVO = new PrivateLiveDistributionPlanVO();
        planVO.setDistributionPlanId(distributionPlan.getPlanId());
        planVO.setDistributionGroupId(distributionPlan.getDistributor().getDistributorId());
        planVO.setLiveId(distributionPlan.getSubject().getField1());
        planVO.setStatus(PrivateLiveDistributionPlanStatusEnum.UNCONFIGURED.getCode());
        if (null != commissionRate) {
            planVO.setCommissionRate(commissionRate.multiply(new BigDecimal(ONE_HUNDRED)).stripTrailingZeros());
        }
        planVO.setEndTime(distributionPlan.getEndTime());
        Date now = new Date();
        long preEndTime = Long.parseLong(distributionPlan.getExtFieldMap().get(DistributionPlanExtFieldEnum.PRE_END_TIME.getFieldId()));

        if (preEndTime < now.getTime()) {
            planVO.setStatus(PrivateLiveDistributionPlanStatusEnum.EXPIRED.getCode());
            planVO.setEndTime(new Date(preEndTime));
        } else if (distributionPlan.getEndTime().before(now)) {
            planVO.setStatus(PrivateLiveDistributionPlanStatusEnum.SUSPENDED.getCode());
            planVO.setEndTime(new Date(preEndTime));
        } else if (distributionPlan.getEndTime().getTime() == preEndTime) {
            planVO.setStatus(PrivateLiveDistributionPlanStatusEnum.IN_EFFECT.getCode());
            planVO.setEndTime(distributionPlan.getEndTime());
        }
        return planVO;
    }

    private void validatePrivateLiveDistributionPlanQueryRequest(PrivateLiveDistributionPlanQueryRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        if (StringUtils.isEmpty(request.getLiveId())) {
            throw new IllegalArgumentException("直播id不能为空");
        }
        if (CollectionUtils.isEmpty(request.getDistributorGroupIds())) {
            throw new IllegalArgumentException("分销商id列表不能为空");
        }
        if (request.getDistributorGroupIds().size() > DISTRIBUTOR_GROUP_LIST_MAX_SIZE) {
            throw new IllegalArgumentException("分销商id列表不能超过10个");
        }
        if (request.getAnchorId() == null) {
            throw new IllegalArgumentException("主播id不能为空");
        }
    }

}