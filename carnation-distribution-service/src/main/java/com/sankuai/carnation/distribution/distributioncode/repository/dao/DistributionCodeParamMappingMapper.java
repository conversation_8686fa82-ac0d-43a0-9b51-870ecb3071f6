package com.sankuai.carnation.distribution.distributioncode.repository.dao;

import com.sankuai.carnation.distribution.distributioncode.repository.entity.DistributionCodeParamMappingEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionCodeParamMappingMapper {

    /**
     * 新增分销码参数映射
     * @param entity
     * @return
     */
    int insertDistributionCodeParamMapping(DistributionCodeParamMappingEntity entity);

    /**
     * 更新分销码的有效性
     * @param entity
     * @return
     */
    int updateDistributionCodeParamMapping(DistributionCodeParamMappingEntity entity);

    /**
     * 基于渠道和分销码查询参数映射关系
     * @param channel
     * @param distributionCode
     * @return
     */
    DistributionCodeParamMappingEntity loadByChannelAndDistributionCode(@Param("channel") String channel, @Param("distributionCode") String distributionCode);


    /**
     * 基于渠道和分销码查询参数映射关系
     * @param channel
     * @param distributionCodes
     * @return
     */
    List<DistributionCodeParamMappingEntity> queryByChannelAndDistributionCodes(@Param("channel") String channel, @Param("distributionCodes") List<String> distributionCodes);

    /**
     * 查询分销码
     * @param entity
     * @return
     */
    List<DistributionCodeParamMappingEntity> queryDistributionCodeByChannelAndBizParams(DistributionCodeParamMappingEntity entity);
}
