package com.sankuai.carnation.distribution.privatelive.consultant.domain.account;

import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantAccount;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantAccountRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveConsultantTaskRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveDistributorCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/12/30
 **/
@Slf4j
@Service
public class ConsultantAccountDomainService {

    @Autowired
    private UniversalQRCodeGeneratorService universalQRCodeGeneratorService;

    @Autowired
    private PrivateLiveConsultantAccountRepository privateLiveConsultantAccountRepository;

    @Autowired
    private PrivateLiveDistributorCodeService privateLiveDistributorCodeService;

    @Autowired
    private PrivateLiveConsultantTaskRepository privateLiveConsultantTaskRepository;

    private static final String MT_TEST_DOMAIN = "https://test.i.meituan.com/";

    private static final String MT_PROD_DOMAIN = "https://flashlive.meituan.com/";

    /**
     * 咨询师小程序申请页面路径
     */
    private static final String APPLY_PATH = "mp/consultant?anchorId=%s&liveId=%s";

    public RemoteResponse<String> queryOrCreateRegisterConsultantQrCode(String liveId, Long anchorId) {
        if (StringUtils.isBlank(liveId) || null == anchorId) {
            log.error("queryOrCreateRegisterConsultantQrCode#liveId or anchorId is null,liveId={},anchorId={}", liveId,
                    anchorId);
            return RemoteResponse.fail("参数非法");
        }
        try {
            RemoteResponse<QRCodeConfigDTO> qrCodeConfigDTORemoteResponse = universalQRCodeGeneratorService
                    .queryQRCode(liveId, BizTypeEnum.PRIVATE_LIVE_CONSULTANT.getType());
            if (qrCodeConfigDTORemoteResponse != null && qrCodeConfigDTORemoteResponse.getData() != null) {
                log.info(
                        "queryOrCreateRegisterConsultantQrCode#queryQrCode success,liveId={},anchorId={},qrCodeConfigDTO={}",
                        liveId, anchorId, qrCodeConfigDTORemoteResponse.getData());
                return RemoteResponse.success(qrCodeConfigDTORemoteResponse.getData().getImageUrl());
            } else {
                String applyPath = Lion.getString(Environment.getAppName(), "register.consultant.apply.path", APPLY_PATH);
                QRCodeConfigDTO qrCodeConfigDTO = new QRCodeConfigDTO();
                qrCodeConfigDTO.setCodeUrl((Environment.isTestEnv() ? MT_TEST_DOMAIN : MT_PROD_DOMAIN)
                        + String.format(applyPath, anchorId, liveId));
                qrCodeConfigDTO.setBizId(0L);
                qrCodeConfigDTO.setBizType(BizTypeEnum.PRIVATE_LIVE_CONSULTANT.getType());
                qrCodeConfigDTO.setWidth(1000);
                qrCodeConfigDTO.setBizIdStr(liveId);
                RemoteResponse<QRCodeConfigDTO> qrCodeConfig = universalQRCodeGeneratorService
                        .insertQRCode(qrCodeConfigDTO);
                log.info(
                        "queryOrCreateRegisterConsultantQrCode#createQrCode success,liveId={},anchorId={},qrCodeConfigDTO={}",
                        liveId, anchorId, qrCodeConfig.getData());
                return RemoteResponse.success(qrCodeConfig.getData().getImageUrl());
            }
        } catch (Exception e) {
            log.error("queryOrCreateRegisterConsultantQrCode#createQrCode.error,liveId={},anchorId={}", liveId,
                    anchorId, e);
            return RemoteResponse.fail("查询咨询师添加二维码失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void registerTransactional(PrivateLiveConsultantAccount account, PrivateLiveConsultantTask task) {
        log.info("registerTransactional#start,account={},task={}", account, task);
        // 注册咨询师账号
        PrivateLiveConsultantAccount privateLiveConsultantAccount = privateLiveConsultantAccountRepository
                .loadByUnionId(account.getUnionId());
        long consultantId;
        if (privateLiveConsultantAccount == null) {
            consultantId = privateLiveConsultantAccountRepository.insert(account);
        } else {
            consultantId = privateLiveConsultantAccount.getId();
        }
        if (consultantId <= 0) {
            throw new RuntimeException("Failed to create consultantId");
        }

        // 注册任务
        task.setConsultantId(consultantId);
        long taskId = privateLiveConsultantTaskRepository.insert(task);

        if (taskId <= 0) {
            throw new RuntimeException("Failed to create consultantTask");
        }
        MetricHelper.build().name("account_register").tag("task_consultant_register","task_consultant_register").count();
        // 创建分销码
        RemoteResponse<String> remoteResponse = privateLiveDistributorCodeService.createCodeByConsultantTaskId(taskId,
                task.getLiveId());
        if (!remoteResponse.isSuccess()) {
            throw new RuntimeException("Failed to create distributor code");
        }
    }

    public Long getAccountIdByOpenId(String openId) {
        if (StringUtils.isBlank(openId)) {
            return null;
        }
        PrivateLiveConsultantAccount privateLiveConsultantAccount = privateLiveConsultantAccountRepository
                .loadOpenId(openId);
        if (privateLiveConsultantAccount == null) {
            return null;
        }
        return privateLiveConsultantAccount.getId();
    }

    public String getOpenIdByAccountId(Long accountId) {
        if (null == accountId) {
            return null;
        }
        if (accountId == null || accountId <= 0) {
            return null;
        }
        PrivateLiveConsultantAccount privateLiveConsultantAccount = privateLiveConsultantAccountRepository
                .loadById(accountId);
        if (privateLiveConsultantAccount == null) {
            return null;
        }
        return privateLiveConsultantAccount.getOpenId();
    }

}
