package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.dianping.cat.Cat;
import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gis.remote.service.CityInfoService;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/26
 * @description 点评城市信息查询
 */
@Slf4j
@Service
public class CityInfoAclService {

    private static final int CITY_INFO_QUERY_SIZE = 1000;

    private final ThreadPoolExecutor cityQueryPool = new ThreadPoolExecutor(10, 20, 1, TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(100), new NamedThreadFactory("CityInfoAclService.CityQueryPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private CityInfoService cityInfoService;

    public Map<Integer, CityInfoDTO> batchQueryCityInfo(List<Integer> cityIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CityInfoAclService.batchQueryCityInfo(java.util.List)");
        if (CollectionUtils.isEmpty(cityIdList)) {
            return Maps.newHashMap();
        }
        cityIdList = cityIdList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cityIdList)) {
            return Maps.newHashMap();
        }
        List<CompletableFuture<List<CityInfoDTO>>> futureList = Lists.partition(cityIdList, CITY_INFO_QUERY_SIZE).stream()
                .map(partList -> CompletableFuture.supplyAsync(() -> queryCityInfo(partList), cityQueryPool))
                .collect(Collectors.toList());
        try {
            return CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).thenApply(
                    val -> futureList.stream()
                            .map(CompletableFuture::join)
                            .filter(CollectionUtils::isNotEmpty)
                            .map(list -> list.stream()
                                    .filter(Objects::nonNull)
                                    .filter(city -> city.getCityId() > 0)
                                    .collect(Collectors.toMap(CityInfoDTO::getCityId, Function.identity(), (ov, nv) -> nv))
                            )
                            .reduce((resultMap, itemMap) -> {
                                resultMap.putAll(itemMap);
                                return resultMap;
                            })
                            .orElse(Maps.newHashMap())
            ).get();
        } catch (Exception e) {
            log.error("[{}.batchQueryCityInfo] cityIdList: {}", getClass().getSimpleName(), cityIdList, e);
            return Maps.newHashMap();
        }
    }

    private List<CityInfoDTO> queryCityInfo(List<Integer> cityIdList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.CityInfoAclService.queryCityInfo(java.util.List)");
        try {
            return cityInfoService.findCities(cityIdList);
        } catch (Exception e) {
            log.error("[{}.queryCityInfo] cityIdList: {}", getClass().getSimpleName(), cityIdList, e);
            return Lists.newArrayList();
        }
    }

}
