package com.sankuai.carnation.distribution.distributionplan.service.producer.dto;

import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanOperationEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributionPlanDelayMsg {

    private Long planId;


    private String channel;

    /**
     * 1-方案开始
     * 2-方案结束
     * @see DistributionPlanOperationEnum
     *
     */
    private Integer operateType;

    /**
     * 延迟时间，单位ms
     */
    private Long operateTime;
}
