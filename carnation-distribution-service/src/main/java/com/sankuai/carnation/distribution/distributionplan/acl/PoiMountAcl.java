package com.sankuai.carnation.distribution.distributionplan.acl;

import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PoiMountAcl {

    /**
     * 获取poi是否处于融合成功的状态
     * @param mtPoiIds
     * @param channel
     * @return
     */
    Map<Long, String> batchGetMtPoiMountPoiId(List<Long> mtPoiIds, DistributionBusinessChannelEnum channel);
}