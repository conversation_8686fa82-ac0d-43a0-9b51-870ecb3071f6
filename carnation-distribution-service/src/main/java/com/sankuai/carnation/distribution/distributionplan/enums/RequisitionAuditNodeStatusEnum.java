package com.sankuai.carnation.distribution.distributionplan.enums;

public enum RequisitionAuditNodeStatusEnum {
    //“1:审核通过”、“2:审核驳回”、“3:待审核”、“4:创建佣金规则”、“5:审核超时”、“6:审核撤回”

    APPROVE(1, "审核通过"),
    REJECT(2, "审核驳回"),
    AUDITING(3, "待审核"),
    CREATE(4, "创建佣金规则"),
    TIMEOUT(5, "审核超时"),
    CANCEL(6, "审核撤回")
    ;

    private int code;

    private String desc;

    RequisitionAuditNodeStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
