package com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.operatestrategy;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributionplan.acl.DealProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.ProductCommissionBasisAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealProductInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisInfo;
import com.sankuai.carnation.distribution.distributionplan.acl.model.ProductCommissionBasisQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.distributionplan.service.impl.merchant.MerchantChannelDistributionPlanServiceImpl;
import com.sankuai.carnation.distribution.distributionplan.service.model.MerchantChannelDistributionPlanOperateContext;
import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.utils.DateUtils;
import com.sankuai.carnation.distribution.distributionplan.utils.RedisLockService;
import com.sankuai.dzusergrowth.common.api.response.Response;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionCommissionTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.enums.DistributionPlanOperateTypeEnum;
import com.sankuai.dzusergrowth.distribution.plan.api.request.DistributionPlanOperateRequest;
import com.sankuai.dzusergrowth.distribution.plan.api.request.field.DistributionPlanOperator;
import com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Component
public class ModifyDistributionPlanCommissionStrategy implements MerchantChannelDistributionPlanOperateStrategy{

    @Resource
    private DistributionPlanCommandService distributionPlanCommandService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private ProductCommissionBasisAcl productCommissionBasisAcl;

    @Resource
    private DealProductAcl dealProductAcl;

    @PostConstruct
    public void init() {
        MerchantChannelDistributionPlanOperateStrategyFactory.register(MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION, this);
    }

    @Override
    public void validatePlanCanOperate(MerchantChannelDistributionPlanOperateContext context) {
        if (context.getDistributionPlan().getEndTime().before(context.getDistributionPlan().getBeginTime())) {
            throw new IllegalArgumentException("当前商品佣金配置已取消");
        }

        if (context.getDistributionPlan().getEndTime().getTime() <= context.getOperateDate().getTime()) {
            throw new IllegalArgumentException("当前商品佣金配置已结束");
        }

        if (DateUtils.isSameDay(context.getOperateDate(), context.getDistributionPlan().getEndTime())) {
            throw new IllegalArgumentException("当前商品佣金配置今天凌晨前即将结束，不支持再修改佣金率");
        }

        Long mtProductId = Long.valueOf(context.getDistributionPlan().getSubject().getField1());
        Map<Long, DealProductInfo> dealProductInfoMap = dealProductAcl.queryMtDealProductToMap(Lists.newArrayList(mtProductId));
        if (Objects.isNull(dealProductInfoMap) || !dealProductInfoMap.containsKey(mtProductId)) {
            throw new DistributionPlanException("团购信息查询失败");
        }
        DealProductInfo dealProductInfo = dealProductInfoMap.get(mtProductId);
        ProductCommissionBasisQueryRequest productCommissionBasisQueryRequest = ProductCommissionBasisQueryRequest.builder()
                .dpProductId(dealProductInfo.getDpProductId()).build();
        List<ProductCommissionBasisInfo> commissionBasisInfoList = productCommissionBasisAcl.batchQueryProductCommissionBasis(Lists.newArrayList(productCommissionBasisQueryRequest));
        if (CollectionUtils.isEmpty(commissionBasisInfoList)) {
            throw new DistributionPlanException("商品计佣基数类型查询失败");
        }

        if (MerchantChannelDistributionPlanServiceImpl.isCommissionRateOutOfRange(context.getCommissionRate(), commissionBasisInfoList.get(0))) {
            throw new IllegalArgumentException("商品佣金率超出规定范围");
        }
    }

    @Override
    public void operate(MerchantChannelDistributionPlanOperateContext context) {
        Map<Integer, BigDecimal> commissionRateMap = new HashMap<>();
        commissionRateMap.put(DistributionCommissionTypeEnum.MERCHANT_CHANNEL_COMMISSION.getCode(), context.getCommissionRate());
        DistributionPlanOperateRequest distributionPlanOperateRequest = new DistributionPlanOperateRequest();
        distributionPlanOperateRequest.setPlanId(context.getDistributionPlan().getPlanId());
        distributionPlanOperateRequest.setSceneCode(context.getDistributionPlan().getSceneCode());
        distributionPlanOperateRequest.setChannel(context.getDistributionPlan().getChannel());
        distributionPlanOperateRequest.setOperateType(DistributionPlanOperateTypeEnum.EDIT_COMMISSION_RATE.getCode());
        distributionPlanOperateRequest.setTerminateTime(DateUtils.getToDayNight(context.getOperateDate()).getTime());
        distributionPlanOperateRequest.setCommissionRateMap(commissionRateMap);
        distributionPlanOperateRequest.setOperator(DistributionPlanOperator.builder()
                .operatorId(String.valueOf(context.getMtAccountId()))
                .build());
        Response response = redisLockService.tryLock("operateMerchantDistributionPlan_" + context.getDistributionPlan().getPlanId(),
                () -> distributionPlanCommandService.operateDistributionPlan(distributionPlanOperateRequest));
        if (response.respFailed()) {
            throw new DistributionPlanException(response.getMessage());
        }
    }
}