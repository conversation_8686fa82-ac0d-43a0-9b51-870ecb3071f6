package com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.dz.coupon.base.dto.CouponDTO;
import com.dianping.dztrade.dto.tradeEvent.GeneralEventNotifyDTO;
import com.dianping.dztrade.dto.tradeEvent.OrderBaseInfo;
import com.dianping.dztrade.dto.tradeEvent.OrderProductBaseInfo;
import com.dianping.dztrade.enums.RelatedOrderTypeEnum;
import com.dianping.dztrade.enums.TradeEventEnum;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.domain.enums.ProductEnum;
import com.dianping.pay.order.service.query.GetUnifiedOrderService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.api.common.enums.UnifiedOrderField;
import com.dianping.pigeon.threadpool.NamedThreadFactory;
import com.dianping.receipt.query.dto.ReceiptDTO;
import com.dianping.refund.platform.api.model.enums.RefundProcessTemplate;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.nibtp.trade.client.buy.enums.OrderExtraFieldEnum;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.distributor.domain.DistributorRootService;
import com.sankuai.carnation.distribution.distributor.domain.bo.BizDistributorBO;
import com.sankuai.carnation.distribution.groundpromotion.dommain.part.time.OrderOperateNotifyInfo;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.BizCodeUtils;
import com.sankuai.carnation.distribution.groundpromotion.dommain.part.time.GroundPromotionPartTimeOrderCalService;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DateUtils;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderCouponAcl;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.OrderReceiptAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.IntentionCalculateTaskParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.common.PrepayInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelCalculateTaskTypeEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.enums.OrderChannelTaskStatusEnum;
import com.sankuai.carnation.distribution.intention.domain.calculate.task.running.OrderChannelTaskRunner;
import com.sankuai.carnation.distribution.intention.domain.calculate.utils.BizCodeAnalyser;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.IntentionTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult;
import com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelResultDataService;
import com.sankuai.carnation.distribution.intention.repository.service.OrderChannelRunningTaskDataService;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderUpdateInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderOperateEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.SubOrderTypeEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.MathUtils;
import com.sankuai.carnation.distribution.privatelive.consultant.domain.order.PrivateLiveOrderInfo;
import com.sankuai.carnation.distribution.privatelive.consultant.enums.OrderStatusEnum;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult;
import com.sankuai.carnation.distribution.privatelive.consultant.repository.service.PrivateLiveOrderIntentionResultRepository;
import com.sankuai.carnation.distribution.privatelive.consultant.service.impl.PrivateLiveOrderIntentionServiceImpl;
import com.sankuai.technician.trade.common.enums.OrderTypeEnum;
import com.sankuai.technician.trade.types.enums.OrderLogBizTypeEnum;
import com.sankuai.technician.trade.types.enums.OrderLogOptTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/11/20
 * @Description: 交易事件（支持团购、预订、标品、部分预付）
 */
@Component
@Slf4j
public class GroundPromotionGroupOrderConsumer implements InitializingBean {

    private IConsumerProcessor consumer;

    @Autowired
    private OrderChannelRunningTaskDataService runningTaskDataService;

    @Autowired
    private OrderChannelResultDataService resultDataService;

    @Autowired
    private RedisStoreClient redisStoreClient;

    @Autowired
    private OrderChannelTaskRunner taskRunner;

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private GetUnifiedOrderService getUnifiedOrderService;

    @Autowired
    private OrderReceiptAcl orderReceiptAcl;

    @Autowired
    private OrderCouponAcl orderCouponAcl;

    @Autowired
    private GroundPromotionPartTimeOrderCalService groundPromotionPartTimeOrderCalService;

    @Autowired
    private PrivateLiveOrderIntentionServiceImpl privateLiveOrderIntentionService;

    @Autowired
    private PrivateLiveOrderIntentionResultRepository orderIntentionResultRepository;

    @Autowired
    private DistributorRootService distributorRootService;

    private static final int RETRY_TASK_TYPE = 999;
    private static final long RETRY_DELAY_MS = 12000;
    private static final long RETRY_INTERVAL_MS = 2000;
    private static final int REDIS_TTL_SECONDS = 20;

    private final ThreadPoolExecutor taskRunningPool = new ThreadPoolExecutor(20, 40, 1, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(1000), new NamedThreadFactory(getClass().getSimpleName() + ".taskRunningPool"), new ThreadPoolExecutor.CallerRunsPolicy());

    private ConsumeStatus handle(MafkaMessage message, MessagetContext context) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_5", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq.GroundPromotionGroupOrderConsumer.handle(MafkaMessage,MessagetContext)");
        log.info("[{}] messageId: {}", getClass().getSimpleName(), message.getMessageID());
        if (message.getBody() == null || StringUtils.isBlank(message.getBody().toString())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            GeneralEventNotifyDTO generalEventNotifyDTO = JSON.parseObject(message.getBody().toString(), GeneralEventNotifyDTO.class);

            if (!BizCodeUtils.isGroupon(generalEventNotifyDTO.getBizCode()) && !BizCodeUtils.isBook(generalEventNotifyDTO.getBizCode())
                    && !BizCodeUtils.isStandard(generalEventNotifyDTO.getBizCode())) {
                // 过滤 非团购、预订、标品订单的消息
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            String orderId = "";
            boolean isSecondStandard = BizCodeUtils.isSecondStandard(generalEventNotifyDTO.getBizCode());
            if (isSecondStandard) {
                // 标品在购买成功后会新建一个订单，原始订单用related_order_id关联
                orderId = generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get("related_order_id");
            } else {
                orderId = generalEventNotifyDTO.getUnifiedOrderId();
                if (StringUtils.isEmpty(orderId)) {
                    orderId = generalEventNotifyDTO.getOrderBaseInfo().getOrderId();
                }
            }
            if (StringUtils.isEmpty(orderId)) {
                log.error("GroundPromotionGroupOrderConsumer error, orderId is empty, msgBody:{}", message.getBody().toString());
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            if (generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.ORDER_CREATE.getEventCode()) && !isSecondStandard) {
                // 过滤非私域直播订单
                String distributionCode = getDistributionCode(generalEventNotifyDTO);
                if (StringUtils.isNotEmpty(distributionCode) && distributionCode.matches("^syzb.+")) {
                    // 订单创建，未支付。且非第二笔标品订单。进行私域直播订单归因。
                    PrivateLiveOrderInfo privateLiveOrderInfo = initPrivateLiveOrderInfo(generalEventNotifyDTO, orderId);
                    // 分布式锁，防止重复消费
                    StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId, OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.intentionCalc(privateLiveOrderInfo), taskRunningPool);
                    }
                } else if (generalEventNotifyDTO.getOrderBaseInfo().getPlatform() == PayPlatform.mt_new_shanbo_api.getCode()) {
                    Cat.logEvent("private_live_order_code_invalid", String.format("orderId: %s, distributionCode: %s", orderId, distributionCode));
                }
            }

            if ((generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.ORDER_CANCEL.getEventCode())
                    || generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.ORDER_FAIL.getEventCode()))
                    && !isSecondStandard) {
                // 订单取消或购买失败，且非第二笔标品订单。进行私域直播订单归因。
                PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
                if (orderIntentionResult != null) {
                    processCancelOrder(orderId, generalEventNotifyDTO);
                } else {
                    handleOrderCancelRetry(orderId, generalEventNotifyDTO);
                }
            }

            if (generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.RECEIPT_CONSUME.getEventCode())) {
                // 核销消息 仅第一次核销需要计算

                // 调用归因计算任务
                DistributionOrderChannelCalRunningTaskWithBLOBs task = runningTaskDataService.forceGetTaskByOrderAndType(
                        isSecondStandard ? DistributionOrderTypeEnum.DZ_LONG_ORDER_START_WITH_CHAR_4.getCode() : DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode(),
                        orderId, OrderChannelCalculateTaskTypeEnum.GROUND_PROMOTION_INDIRECT_INTENTION.getCode());
                // 标品和预付核销时，手艺人会再次调用归因任务，因此这里无需重新调用
                if (task != null && task.getStatus() == OrderChannelTaskStatusEnum.RUNNING.getCode()) {
                    // 分布式锁，防止任务计算完成之前，又有核销消息进入
                    StoreKey storeKey = new StoreKey("GroundPromotionOrderCalLock", orderId, OrderChannelCalculateTaskTypeEnum.GROUND_PROMOTION_INDIRECT_INTENTION.getCode());
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        IntentionCalculateTaskParamBO taskParam = buildTaskParam(isSecondStandard, generalEventNotifyDTO);
                        CompletableFuture.runAsync(() -> taskRunner.executeTask(task.getTaskType(), task.getCalTaskId(), taskParam), taskRunningPool);
                    }
                }
            }

            if (generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.REFUND_FUND_COMPLETE.getEventCode())
                    && !generalEventNotifyDTO.getRefundBaseInfo().getRefundTemplateId().equals(RefundProcessTemplate.BUY_FAIL_REFUND.getTemplateID())
                    && !generalEventNotifyDTO.getRefundBaseInfo().getRefundTemplateId().equals(RefundProcessTemplate.REPEAT_PAY_REFUND.getTemplateID())) {
                // 退款消息 且 不能是购买失败退款和重复支付退款

                // 只取已经归因
                DistributionOrderChannelCalResult result = resultDataService.getCalResultByOrder(
                        isSecondStandard ? DistributionOrderTypeEnum.DZ_LONG_ORDER_START_WITH_CHAR_4.getCode() : DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode(),
                        orderId);
                if (result != null && result.getChannel().equals(DistributionBusinessChannelEnum.GROUND_PROMOTION.getCode())) {
                    String distributionCode = result.getDistributionCode();
                    distributionCode = URLDecoder.decode(distributionCode);
                    BizDistributorBO bizDistributorBO = distributorRootService.getBizDistributor(distributionCode);
                    if (bizDistributorBO.getBizType().equals(DistributorBizTypeEnum.GROUND_PART_TIME_PROMOTION.getCode())) {
                        long dpShopId = Long.parseLong(generalEventNotifyDTO.getOrderBaseInfo().getDpShopId());
                        if (dpShopId == 0L) {
                            dpShopId = shopMapperService.mt2dp(Long.parseLong(generalEventNotifyDTO.getOrderBaseInfo().getMtShopId()));
                        }
                        if (distributionCode.matches("^dt.+") && Long.parseLong(distributionCode.substring(distributionCode.indexOf("$$") + 2)) == dpShopId) {
                            // 命中弱归因兼职订单
                            // 防止重复消费 key为订单id+事件类型+事件唯一凭证+时间时间
                            StoreKey storeKey = new StoreKey("GroundPromotionOrderCalLock", orderId + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getReferIds().toString() + generalEventNotifyDTO.getEventTime().toString(), OrderChannelCalculateTaskTypeEnum.GROUND_PROMOTION_INDIRECT_INTENTION.getCode());
                            if (redisStoreClient.setnx(storeKey, true, 60)) {
                                // 组装消息并发送
                                List<OrderOperateNotifyInfo> notifyList = buildCancelNotify(generalEventNotifyDTO, orderId, distributionCode);
                                CompletableFuture.runAsync(() -> groundPromotionPartTimeOrderCalService.calculate(notifyList), taskRunningPool);
                            }
                        }
                    }
                }
                // 私域直播退款订单处理开关，逻辑处理迁移至收单系统
                Boolean refundHandleSwitch = Lion.getBoolean(Environment.getAppName(), "privatelive.trade.order.refund.handle.switch", true);
                if (refundHandleSwitch) {
                    PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
                    if (orderIntentionResult != null) {
                        // 分布式锁，防止重复消费
                        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getReferIds().toString() + generalEventNotifyDTO.getEventTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
                        if (redisStoreClient.setnx(storeKey, true, 60)) {
                            // 更新数据
                            String finalOrderId = orderId;
                            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                                    .orderId(finalOrderId)
                                    .operateType(OrderOperateEnum.REFUND_SUCCESS.getCode())
                                    .receiptIdList(generalEventNotifyDTO.getRefundBaseInfo().getReceiptIdList())
                                    .build()), taskRunningPool);
                        }
                    }
                }
            }

            //// 发起退款申请
            //if (generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.REFUND_APPLY.getEventCode())) {
            //    PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
            //    if (orderIntentionResult != null) {
            //        // 分布式锁，防止重复消费
            //        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getReferIds().toString() + generalEventNotifyDTO.getEventTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
            //        if (redisStoreClient.setnx(storeKey, true, 60)) {
            //            // 更新数据
            //            String finalOrderId = orderId;
            //            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(finalOrderId, OrderOperateEnum.REFUND_CREATE.getCode(), generalEventNotifyDTO.getRefundBaseInfo().getReceiptIdList()), taskRunningPool);
            //        }
            //    }
            //}
            //
            //// 退款取消or退款失败，需要回退到之前的状态
            //if (generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.REFUND_CANCEL.getEventCode())
            //        || generalEventNotifyDTO.getEventCode().equals(TradeEventEnum.REFUND_AUDIT_REJECT.getEventCode())) {
            //    PrivateLiveOrderIntentionResult orderIntentionResult = orderIntentionResultRepository.forceGetByOrderId(orderId);
            //    if (orderIntentionResult != null) {
            //        // 分布式锁，防止重复消费
            //        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getReferIds().toString() + generalEventNotifyDTO.getEventTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
            //        if (redisStoreClient.setnx(storeKey, true, 60)) {
            //            // 更新数据
            //            String finalOrderId = orderId;
            //            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(finalOrderId, OrderOperateEnum.REFUND_FAIL.getCode(), Lists.newArrayList()), taskRunningPool);
            //        }
            //    }
            //}
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("GroundPromotionGroupOrderConsumer error msgBody:{}", message.getBody().toString(), e);
            return ConsumeStatus.CONSUME_FAILURE;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        taskRunningPool.allowCoreThreadTimeOut(true);
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "pingtai");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, Environment.getAppName());
        properties.setProperty(ConsumerConstants.SubscribeGroup, "dz.order.distribute.group.order.status.consumer");
        consumer = MafkaClient.buildConsumerFactory(properties, "com.sankuai.mptrade.general.event.notify");
        consumer.recvMessageWithParallel(String.class, this::handle);
    }

    private void handleOrderCancelRetry(String orderId, GeneralEventNotifyDTO generalEventNotifyDTO) {
        // 使用Redis实现延迟重试机制
        // 999=消息延迟重试
        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId, RETRY_TASK_TYPE);
        // 12秒后过期
        long expirationTime = System.currentTimeMillis() + RETRY_DELAY_MS;

        // 20秒TTL，防止死锁
        if (redisStoreClient.setnx(storeKey, expirationTime, REDIS_TTL_SECONDS)) {
            CompletableFuture.runAsync(() -> retryProcessCancelOrder(orderId, generalEventNotifyDTO, expirationTime), taskRunningPool);
        }
    }

    private void retryProcessCancelOrder(String orderId, GeneralEventNotifyDTO generalEventNotifyDTO, Long expirationTime) {
        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getEventTime().toString()
                , OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
        try {
            while (System.currentTimeMillis() < expirationTime) {
                if (orderIntentionResultRepository.forceGetByOrderId(orderId) != null) {
                    // 分布式锁，防止重复消费
                    if (redisStoreClient.setnx(storeKey, true, 60)) {
                        // 更新数据
                        privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                                .orderId(orderId)
                                .operateType(OrderOperateEnum.CANCEL.getCode())
                                .build());
                    }
                    return;
                }
                // 每2秒检查一次
                Thread.sleep(RETRY_INTERVAL_MS);
            }
            log.info("Retry timeout for cancel message, orderId: {}", orderId);
        } catch (InterruptedException e) {
            log.warn("Retry interrupted for orderId: {}", orderId, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("Error processing retry for orderId: {}", orderId, e);
        } finally {
            redisStoreClient.delete(storeKey);
        }
    }

    private void processCancelOrder(String orderId, GeneralEventNotifyDTO generalEventNotifyDTO) {
        // 分布式锁，防止重复消费
        StoreKey storeKey = new StoreKey("PrivateLiveOrderIntentionTask", orderId + generalEventNotifyDTO.getEventCode() + generalEventNotifyDTO.getEventTime().toString(), OrderChannelCalculateTaskTypeEnum.PRIVATE_LIVE_CONSULTANT_INTENTION.getCode());
        if (redisStoreClient.setnx(storeKey, true, 60)) {
            // 更新数据
            CompletableFuture.runAsync(() -> privateLiveOrderIntentionService.updateIntentionResult(PrivateLiveOrderUpdateInfo.builder()
                    .orderId(orderId)
                    .operateType(OrderOperateEnum.CANCEL.getCode())
                    .build()), taskRunningPool);
        }
    }

    private List<OrderOperateNotifyInfo> buildCancelNotify(GeneralEventNotifyDTO generalEventNotifyDTO, String orderId, String distributionCode) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_4", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.mq.GroundPromotionGroupOrderConsumer.buildCancelNotify(GeneralEventNotifyDTO,String,String)");
        List<OrderOperateNotifyInfo> notifyList = Lists.newArrayList();

        UnifiedOrderWithId unifiedOrder = getUnifiedOrderService.getByUnifiedOrderId(orderId);

        List<String> receiptIDList = Lists.newArrayList();
        if (unifiedOrder.getBizType() == ProductEnum.promotion_card.value) {
            // 标品时，买笔订单退款mq，消息上没有券id。因为标品订单只有一张券，因此这里直接查通用券订单所对应的券
            receiptIDList = orderCouponAcl.getTotalCoupon(orderId, unifiedOrder.getUserId()).stream()
                    .map(CouponDTO::getCouponId)
                    .map(String::valueOf).collect(Collectors.toList());
        } else {
            receiptIDList = generalEventNotifyDTO.getRefundBaseInfo().getReceiptIdList();
        }

        long poiId = PayPlatform.isMtPlatform(unifiedOrder.getPlatform()) ? unifiedOrder.getLongMtShopId() : shopMapperService.dp2mt(unifiedOrder.getLongShopId());
        boolean buyMagicMemberCoupon = false;
        if (unifiedOrder.getNibExtraFields() != null && unifiedOrder.getNibExtraFields().containsKey(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey())) {
            buyMagicMemberCoupon = "true".equals(unifiedOrder.getNibExtraFields().get(OrderExtraFieldEnum.MAGIC_MEMBER_COUPON_COMBINE_FLAG.getKey()));
        }
        if (BizCodeUtils.isGroupon(generalEventNotifyDTO.getBizCode())) {
            Map<Long, ReceiptDTO> receiptDTOMap = orderReceiptAcl.findReceiptByIDs(receiptIDList.stream().map(Long::parseLong).collect(Collectors.toList()));
            for (String receiptId : receiptIDList) {
                OrderOperateNotifyInfo notifyInfo = new OrderOperateNotifyInfo();

                ReceiptDTO receiptDTO = receiptDTOMap.get(Long.parseLong(receiptId));
                notifyInfo.setProductType(BizCodeAnalyser.fromOrderBizCode(generalEventNotifyDTO.getBizCode()).getCode());
                notifyInfo.setProductId(Long.parseLong(unifiedOrder.getSpugId()));
                notifyInfo.setOrderId(orderId);
                notifyInfo.setOrderType(OrderTypeEnum.TRADE_ORDER.getId());
                notifyInfo.setOrderIdLong(unifiedOrder.getLongOrderId());
                notifyInfo.setOperateType(OrderLogOptTypeEnum.CANCEL.getCode());
                notifyInfo.setBizId(receiptId);
                notifyInfo.setBizType(OrderLogBizTypeEnum.DEAL_GROUP_COUPON.getCode());
                notifyInfo.setExtKey(receiptDTO.getSerialNumber());
                notifyInfo.setMtShopId(poiId);
                notifyInfo.setActionTime(DateUtils.toDate(generalEventNotifyDTO.getEventTime()));
                notifyInfo.setExtInfo(new HashMap<String, String>() {{
                    put("flag", distributionCode);
                }});
                notifyInfo.setUserId(unifiedOrder.getUserId());
                notifyInfo.setMtUserId(unifiedOrder.getMtUserId());
                notifyInfo.setPlatform(PayPlatform.isMtPlatform(unifiedOrder.getPlatform()) ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
                notifyInfo.setTotalAmount(unifiedOrder.getTotalAmount());
                notifyInfo.setAddTime(unifiedOrder.getAddTime());
                notifyInfo.setPayTime(unifiedOrder.getPaySuccessTime());
                notifyInfo.setBuyMagicMemberCoupons(buyMagicMemberCoupon ? 1 : 0);
                notifyList.add(notifyInfo);
            }
        } else {
            Map<Long, CouponDTO> couponDTOMap = orderCouponAcl.findCouponByIDs(receiptIDList.stream().map(Long::parseLong).collect(Collectors.toList()));
            for (String receiptId : receiptIDList) {
                OrderOperateNotifyInfo notifyInfo = new OrderOperateNotifyInfo();

                CouponDTO couponDTO = couponDTOMap.get(Long.parseLong(receiptId));
                notifyInfo.setProductType(BizCodeAnalyser.fromOrderBizCode(generalEventNotifyDTO.getBizCode()).getCode());
                notifyInfo.setProductId(Long.parseLong(unifiedOrder.getSpugId()));
                notifyInfo.setOrderId(orderId);
                notifyInfo.setOrderType(OrderTypeEnum.TRADE_ORDER.getId());
                notifyInfo.setOrderIdLong(unifiedOrder.getLongOrderId());
                notifyInfo.setOperateType(OrderLogOptTypeEnum.CANCEL.getCode());
                notifyInfo.setBizId(receiptId);
                notifyInfo.setBizType(OrderLogBizTypeEnum.FUN_COUPON.getCode());
                notifyInfo.setExtKey(couponDTO.getCouponValue());
                notifyInfo.setMtShopId(poiId);
                notifyInfo.setActionTime(DateUtils.toDate(generalEventNotifyDTO.getEventTime()));
                notifyInfo.setExtInfo(new HashMap<String, String>() {{
                    put("flag", distributionCode);
                }});
                notifyInfo.setUserId(unifiedOrder.getUserId());
                notifyInfo.setMtUserId(unifiedOrder.getMtUserId());
                notifyInfo.setPlatform(PayPlatform.isMtPlatform(unifiedOrder.getPlatform()) ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
                notifyInfo.setTotalAmount(unifiedOrder.getTotalAmount());
                notifyInfo.setAddTime(unifiedOrder.getAddTime());
                notifyInfo.setPayTime(unifiedOrder.getPaySuccessTime());
                notifyInfo.setBuyMagicMemberCoupons(buyMagicMemberCoupon ? 1 : 0);
                notifyList.add(notifyInfo);
            }
        }
        return notifyList;
    }

    private IntentionCalculateTaskParamBO buildTaskParam(boolean isSecondStandard, GeneralEventNotifyDTO generalEventNotifyDTO) {
        String orderId;
        if (isSecondStandard) {
            // 标品在购买成功后会新建一个订单，原始订单用related_order_id关联
            orderId = generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get("related_order_id");
        } else {
            orderId = generalEventNotifyDTO.getUnifiedOrderId();
            if (StringUtils.isEmpty(orderId)) {
                orderId = generalEventNotifyDTO.getOrderBaseInfo().getOrderId();
            }
        }
        IntentionCalculateTaskParamBO taskParam = IntentionCalculateTaskParamBO.builder()
                .orderType(isSecondStandard ? DistributionOrderTypeEnum.DZ_LONG_ORDER_START_WITH_CHAR_4.getCode() : DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode())
                .orderId(orderId)
                .distributionCode(generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get("60078"))
                .build();
        return taskParam;
    }

    private PrivateLiveOrderInfo initPrivateLiveOrderInfo(GeneralEventNotifyDTO generalEventNotifyDTO, String orderId) {
        PrivateLiveOrderInfo privateLiveOrderInfo = new PrivateLiveOrderInfo();
        //UnifiedOrderWithId unifiedOrder = getUnifiedOrderService.getByUnifiedOrderId(orderId);
        OrderBaseInfo orderBaseInfo = generalEventNotifyDTO.getOrderBaseInfo();
        privateLiveOrderInfo.setOrderId(orderId);
        privateLiveOrderInfo.setLongOrderId(Long.parseLong(orderBaseInfo.getOrderId()));
        if (BizCodeUtils.isStandard(generalEventNotifyDTO.getBizCode())) {
            privateLiveOrderInfo.setDistributorCode(generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get("distribution_param"));
        } else {
            privateLiveOrderInfo.setDistributorCode(generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get(String.valueOf(UnifiedOrderField.distributionParam.fieldKey)));
        }

        boolean isMt = PayPlatform.isMtPlatform(orderBaseInfo.getPlatform());
        privateLiveOrderInfo.setUserId(isMt ? Long.parseLong(orderBaseInfo.getMtUserId()) : Long.parseLong(orderBaseInfo.getDpUserId()));
        privateLiveOrderInfo.setPlatform(isMt ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
        List<OrderProductBaseInfo> orderProductBaseInfoList = orderBaseInfo.getOrderProductBaseInfoList();
        privateLiveOrderInfo.setSkuId(CollectionUtils.isNotEmpty(orderProductBaseInfoList) ? orderProductBaseInfoList.get(0).getSkuId() : 0L);
        privateLiveOrderInfo.setProductId(CollectionUtils.isNotEmpty(orderProductBaseInfoList) ? orderProductBaseInfoList.get(0).getProductId() : 0L);
        privateLiveOrderInfo.setProductType(BizCodeAnalyser.fromOrderBizCode(generalEventNotifyDTO.getBizCode()).getCode());
        int count = orderProductBaseInfoList.stream().mapToInt(OrderProductBaseInfo::getCount).sum();
        privateLiveOrderInfo.setQuantity(count);
        privateLiveOrderInfo.setStatus(OrderStatusEnum.CREATE.getCode());
        //privateLiveOrderInfo.setPayMoney(MathUtils.bigDecimalToLong(orderBaseInfo.getActualAmount()));
        // 兼容团购预付订单，团购预付订单总金额取数
        String prepayInfoStr = orderBaseInfo.getExtraMap().get("general.ext.prepayInfo");
        log.info("【initPrivateLiveOrderInfo】orderNo：{},prepayInfoStr：{}", orderId, prepayInfoStr);
        if (StringUtils.isBlank(prepayInfoStr)) {
            privateLiveOrderInfo.setTotalMoney(MathUtils.bigDecimalToLong(orderBaseInfo.getTotalAmount()));
        } else {
            PrepayInfoBO prepayInfoBO = JSON.parseObject(prepayInfoStr, PrepayInfoBO.class);
            privateLiveOrderInfo.setTotalMoney(prepayInfoBO.getTotalAmount().longValue());
            log.info("【initPrivateLiveOrderInfo】orderNo：{},prepayInfo：{}", orderId, JsonUtil.toJson(prepayInfoBO));
        }

        String extraOrderTypeStr = orderBaseInfo.getExtraMap().get("general.ext.extraOrderType");
        if (StringUtils.isNotBlank(extraOrderTypeStr)) {
            int extraOrderType = Integer.parseInt(extraOrderTypeStr);
            privateLiveOrderInfo.setSubOrderType(RelatedOrderTypeEnum.DEAL_GROUP_PREPAY_ORDER.getId() == extraOrderType
                    ? SubOrderTypeEnum.PREPAY.getCode() : SubOrderTypeEnum.NORMAL.getCode());
            log.info("【initPrivateLiveOrderInfo】orderNo：{},general.ext.extraOrderType：{}", orderId, extraOrderTypeStr);
        }

        privateLiveOrderInfo.setAddTime(DateUtils.toDate(orderBaseInfo.getCreateTime()));
        //privateLiveOrderInfo.setPayTime(unifiedOrder.getPaySuccessTime());
        return privateLiveOrderInfo;
    }

    private String getDistributionCode(GeneralEventNotifyDTO generalEventNotifyDTO) {
        if (BizCodeUtils.isStandard(generalEventNotifyDTO.getBizCode())) {
            return generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get("distribution_param");
        } else {
            return generalEventNotifyDTO.getOrderBaseInfo().getExtraMap().get(String.valueOf(UnifiedOrderField.distributionParam.fieldKey));
        }
    }
}
