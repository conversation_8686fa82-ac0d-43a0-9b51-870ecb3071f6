package com.sankuai.carnation.distribution.live.repository;

import com.dianping.cat.Cat;
import com.sankuai.carnation.distribution.live.repository.dao.QrLiveShareInfoMapper;
import com.sankuai.carnation.distribution.live.repository.db.QrLiveShareInfo;
import com.sankuai.carnation.distribution.live.repository.example.QrLiveShareInfoExample;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/5
 */
@Repository
public class LiveShareQRCodeRepository {

    @Autowired
    private QrLiveShareInfoMapper qrLiveShareInfoMapper;

    public int saveQrLiveShareInfo(Long liveId, Long zxsId, String qrCodeUrl, String shortUrl) {
        if (liveId == null || liveId <= 0 || zxsId == null || zxsId <= 0 || StringUtils.isBlank(qrCodeUrl)
                || StringUtils.isBlank(shortUrl)) {
            return 0;
        }
        QrLiveShareInfo qrLiveShareInfo = QrLiveShareInfo.builder().liveId(liveId).zxsId(zxsId).qrCodeUrl(qrCodeUrl)
                .shortUrl(shortUrl).build();
        QrLiveShareInfo qrLiveShareInfoDo = queryQrLiveShareInfo(liveId, zxsId);
        if (qrLiveShareInfoDo != null) {
            qrLiveShareInfo.setId(qrLiveShareInfoDo.getId());
            return qrLiveShareInfoMapper.updateByPrimaryKey(qrLiveShareInfo);
        }
        return qrLiveShareInfoMapper.insertSelective(qrLiveShareInfo);
    }

    public QrLiveShareInfo queryQrLiveShareInfo(Long liveId, Long zxsId) {
        if (liveId == null || liveId <= 0 || zxsId == null || zxsId <= 0) {
            return null;
        }
        QrLiveShareInfoExample qrLiveShareInfoExample = new QrLiveShareInfoExample();
        qrLiveShareInfoExample.createCriteria().andLiveIdEqualTo(liveId).andZxsIdEqualTo(zxsId);
        List<QrLiveShareInfo> qrLiveShareInfos = qrLiveShareInfoMapper.selectByExample(qrLiveShareInfoExample);
        if (CollectionUtils.isEmpty(qrLiveShareInfos)) {
            return null;
        }
        return qrLiveShareInfos.get(0);
    }

    public int updateShortUrlById(Long id, String shortUrl) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.live.repository.LiveShareQRCodeRepository.updateShortUrlById(java.lang.Long,java.lang.String)");
        if (id == null || id <= 0 || StringUtils.isBlank(shortUrl)) {
            return 0;
        }
        QrLiveShareInfo qrLiveShareInfo = QrLiveShareInfo.builder().id(id).shortUrl(shortUrl).build();
        return qrLiveShareInfoMapper.updateByPrimaryKeySelective(qrLiveShareInfo);
    }
}
