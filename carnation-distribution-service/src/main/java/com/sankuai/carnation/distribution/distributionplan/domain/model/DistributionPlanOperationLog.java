package com.sankuai.carnation.distribution.distributionplan.domain.model;

import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanOperationLogTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.enums.DistributionPlanOperatorTypeEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DistributionPlanOperationLog {

    private Long logId;

    private Long planId;

    private DistributionPlanOperationLogTypeEnum operationType;

    private String operationContent;

    private DistributionPlanOperatorTypeEnum operatorType;

    private String operator;


}