package com.sankuai.carnation.distribution.promocode.task.repository.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class QrShopPromoteTradeDataExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public QrShopPromoteTradeDataExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public QrShopPromoteTradeDataExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public QrShopPromoteTradeDataExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public QrShopPromoteTradeDataExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIsNull() {
            addCriterion("partition_date is null");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIsNotNull() {
            addCriterion("partition_date is not null");
            return (Criteria) this;
        }

        public Criteria andPartitionDateEqualTo(String value) {
            addCriterion("partition_date =", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotEqualTo(String value) {
            addCriterion("partition_date <>", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateGreaterThan(String value) {
            addCriterion("partition_date >", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateGreaterThanOrEqualTo(String value) {
            addCriterion("partition_date >=", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLessThan(String value) {
            addCriterion("partition_date <", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLessThanOrEqualTo(String value) {
            addCriterion("partition_date <=", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateLike(String value) {
            addCriterion("partition_date like", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotLike(String value) {
            addCriterion("partition_date not like", value, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateIn(List<String> values) {
            addCriterion("partition_date in", values, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotIn(List<String> values) {
            addCriterion("partition_date not in", values, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateBetween(String value1, String value2) {
            addCriterion("partition_date between", value1, value2, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andPartitionDateNotBetween(String value1, String value2) {
            addCriterion("partition_date not between", value1, value2, "partitionDate");
            return (Criteria) this;
        }

        public Criteria andDpShopIdIsNull() {
            addCriterion("dp_shop_id is null");
            return (Criteria) this;
        }

        public Criteria andDpShopIdIsNotNull() {
            addCriterion("dp_shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andDpShopIdEqualTo(Long value) {
            addCriterion("dp_shop_id =", value, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdNotEqualTo(Long value) {
            addCriterion("dp_shop_id <>", value, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdGreaterThan(Long value) {
            addCriterion("dp_shop_id >", value, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dp_shop_id >=", value, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdLessThan(Long value) {
            addCriterion("dp_shop_id <", value, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdLessThanOrEqualTo(Long value) {
            addCriterion("dp_shop_id <=", value, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdIn(List<Long> values) {
            addCriterion("dp_shop_id in", values, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdNotIn(List<Long> values) {
            addCriterion("dp_shop_id not in", values, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdBetween(Long value1, Long value2) {
            addCriterion("dp_shop_id between", value1, value2, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andDpShopIdNotBetween(Long value1, Long value2) {
            addCriterion("dp_shop_id not between", value1, value2, "dpShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIsNull() {
            addCriterion("mt_shop_id is null");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIsNotNull() {
            addCriterion("mt_shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andMtShopIdEqualTo(Long value) {
            addCriterion("mt_shop_id =", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotEqualTo(Long value) {
            addCriterion("mt_shop_id <>", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdGreaterThan(Long value) {
            addCriterion("mt_shop_id >", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mt_shop_id >=", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdLessThan(Long value) {
            addCriterion("mt_shop_id <", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdLessThanOrEqualTo(Long value) {
            addCriterion("mt_shop_id <=", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIn(List<Long> values) {
            addCriterion("mt_shop_id in", values, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotIn(List<Long> values) {
            addCriterion("mt_shop_id not in", values, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdBetween(Long value1, Long value2) {
            addCriterion("mt_shop_id between", value1, value2, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotBetween(Long value1, Long value2) {
            addCriterion("mt_shop_id not between", value1, value2, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andCat0IdIsNull() {
            addCriterion("cat0_id is null");
            return (Criteria) this;
        }

        public Criteria andCat0IdIsNotNull() {
            addCriterion("cat0_id is not null");
            return (Criteria) this;
        }

        public Criteria andCat0IdEqualTo(Integer value) {
            addCriterion("cat0_id =", value, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdNotEqualTo(Integer value) {
            addCriterion("cat0_id <>", value, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdGreaterThan(Integer value) {
            addCriterion("cat0_id >", value, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdGreaterThanOrEqualTo(Integer value) {
            addCriterion("cat0_id >=", value, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdLessThan(Integer value) {
            addCriterion("cat0_id <", value, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdLessThanOrEqualTo(Integer value) {
            addCriterion("cat0_id <=", value, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdIn(List<Integer> values) {
            addCriterion("cat0_id in", values, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdNotIn(List<Integer> values) {
            addCriterion("cat0_id not in", values, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdBetween(Integer value1, Integer value2) {
            addCriterion("cat0_id between", value1, value2, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0IdNotBetween(Integer value1, Integer value2) {
            addCriterion("cat0_id not between", value1, value2, "cat0Id");
            return (Criteria) this;
        }

        public Criteria andCat0NameIsNull() {
            addCriterion("cat0_name is null");
            return (Criteria) this;
        }

        public Criteria andCat0NameIsNotNull() {
            addCriterion("cat0_name is not null");
            return (Criteria) this;
        }

        public Criteria andCat0NameEqualTo(String value) {
            addCriterion("cat0_name =", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameNotEqualTo(String value) {
            addCriterion("cat0_name <>", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameGreaterThan(String value) {
            addCriterion("cat0_name >", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameGreaterThanOrEqualTo(String value) {
            addCriterion("cat0_name >=", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameLessThan(String value) {
            addCriterion("cat0_name <", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameLessThanOrEqualTo(String value) {
            addCriterion("cat0_name <=", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameLike(String value) {
            addCriterion("cat0_name like", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameNotLike(String value) {
            addCriterion("cat0_name not like", value, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameIn(List<String> values) {
            addCriterion("cat0_name in", values, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameNotIn(List<String> values) {
            addCriterion("cat0_name not in", values, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameBetween(String value1, String value2) {
            addCriterion("cat0_name between", value1, value2, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat0NameNotBetween(String value1, String value2) {
            addCriterion("cat0_name not between", value1, value2, "cat0Name");
            return (Criteria) this;
        }

        public Criteria andCat1IdIsNull() {
            addCriterion("cat1_id is null");
            return (Criteria) this;
        }

        public Criteria andCat1IdIsNotNull() {
            addCriterion("cat1_id is not null");
            return (Criteria) this;
        }

        public Criteria andCat1IdEqualTo(Integer value) {
            addCriterion("cat1_id =", value, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdNotEqualTo(Integer value) {
            addCriterion("cat1_id <>", value, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdGreaterThan(Integer value) {
            addCriterion("cat1_id >", value, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdGreaterThanOrEqualTo(Integer value) {
            addCriterion("cat1_id >=", value, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdLessThan(Integer value) {
            addCriterion("cat1_id <", value, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdLessThanOrEqualTo(Integer value) {
            addCriterion("cat1_id <=", value, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdIn(List<Integer> values) {
            addCriterion("cat1_id in", values, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdNotIn(List<Integer> values) {
            addCriterion("cat1_id not in", values, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdBetween(Integer value1, Integer value2) {
            addCriterion("cat1_id between", value1, value2, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1IdNotBetween(Integer value1, Integer value2) {
            addCriterion("cat1_id not between", value1, value2, "cat1Id");
            return (Criteria) this;
        }

        public Criteria andCat1NameIsNull() {
            addCriterion("cat1_name is null");
            return (Criteria) this;
        }

        public Criteria andCat1NameIsNotNull() {
            addCriterion("cat1_name is not null");
            return (Criteria) this;
        }

        public Criteria andCat1NameEqualTo(String value) {
            addCriterion("cat1_name =", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameNotEqualTo(String value) {
            addCriterion("cat1_name <>", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameGreaterThan(String value) {
            addCriterion("cat1_name >", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameGreaterThanOrEqualTo(String value) {
            addCriterion("cat1_name >=", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameLessThan(String value) {
            addCriterion("cat1_name <", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameLessThanOrEqualTo(String value) {
            addCriterion("cat1_name <=", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameLike(String value) {
            addCriterion("cat1_name like", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameNotLike(String value) {
            addCriterion("cat1_name not like", value, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameIn(List<String> values) {
            addCriterion("cat1_name in", values, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameNotIn(List<String> values) {
            addCriterion("cat1_name not in", values, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameBetween(String value1, String value2) {
            addCriterion("cat1_name between", value1, value2, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat1NameNotBetween(String value1, String value2) {
            addCriterion("cat1_name not between", value1, value2, "cat1Name");
            return (Criteria) this;
        }

        public Criteria andCat2IdIsNull() {
            addCriterion("cat2_id is null");
            return (Criteria) this;
        }

        public Criteria andCat2IdIsNotNull() {
            addCriterion("cat2_id is not null");
            return (Criteria) this;
        }

        public Criteria andCat2IdEqualTo(Integer value) {
            addCriterion("cat2_id =", value, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdNotEqualTo(Integer value) {
            addCriterion("cat2_id <>", value, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdGreaterThan(Integer value) {
            addCriterion("cat2_id >", value, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdGreaterThanOrEqualTo(Integer value) {
            addCriterion("cat2_id >=", value, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdLessThan(Integer value) {
            addCriterion("cat2_id <", value, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdLessThanOrEqualTo(Integer value) {
            addCriterion("cat2_id <=", value, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdIn(List<Integer> values) {
            addCriterion("cat2_id in", values, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdNotIn(List<Integer> values) {
            addCriterion("cat2_id not in", values, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdBetween(Integer value1, Integer value2) {
            addCriterion("cat2_id between", value1, value2, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2IdNotBetween(Integer value1, Integer value2) {
            addCriterion("cat2_id not between", value1, value2, "cat2Id");
            return (Criteria) this;
        }

        public Criteria andCat2NameIsNull() {
            addCriterion("cat2_name is null");
            return (Criteria) this;
        }

        public Criteria andCat2NameIsNotNull() {
            addCriterion("cat2_name is not null");
            return (Criteria) this;
        }

        public Criteria andCat2NameEqualTo(String value) {
            addCriterion("cat2_name =", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameNotEqualTo(String value) {
            addCriterion("cat2_name <>", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameGreaterThan(String value) {
            addCriterion("cat2_name >", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameGreaterThanOrEqualTo(String value) {
            addCriterion("cat2_name >=", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameLessThan(String value) {
            addCriterion("cat2_name <", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameLessThanOrEqualTo(String value) {
            addCriterion("cat2_name <=", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameLike(String value) {
            addCriterion("cat2_name like", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameNotLike(String value) {
            addCriterion("cat2_name not like", value, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameIn(List<String> values) {
            addCriterion("cat2_name in", values, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameNotIn(List<String> values) {
            addCriterion("cat2_name not in", values, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameBetween(String value1, String value2) {
            addCriterion("cat2_name between", value1, value2, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andCat2NameNotBetween(String value1, String value2) {
            addCriterion("cat2_name not between", value1, value2, "cat2Name");
            return (Criteria) this;
        }

        public Criteria andShopTypeIsNull() {
            addCriterion("shop_type is null");
            return (Criteria) this;
        }

        public Criteria andShopTypeIsNotNull() {
            addCriterion("shop_type is not null");
            return (Criteria) this;
        }

        public Criteria andShopTypeEqualTo(String value) {
            addCriterion("shop_type =", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeNotEqualTo(String value) {
            addCriterion("shop_type <>", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeGreaterThan(String value) {
            addCriterion("shop_type >", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeGreaterThanOrEqualTo(String value) {
            addCriterion("shop_type >=", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeLessThan(String value) {
            addCriterion("shop_type <", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeLessThanOrEqualTo(String value) {
            addCriterion("shop_type <=", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeLike(String value) {
            addCriterion("shop_type like", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeNotLike(String value) {
            addCriterion("shop_type not like", value, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeIn(List<String> values) {
            addCriterion("shop_type in", values, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeNotIn(List<String> values) {
            addCriterion("shop_type not in", values, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeBetween(String value1, String value2) {
            addCriterion("shop_type between", value1, value2, "shopType");
            return (Criteria) this;
        }

        public Criteria andShopTypeNotBetween(String value1, String value2) {
            addCriterion("shop_type not between", value1, value2, "shopType");
            return (Criteria) this;
        }

        public Criteria andDimCityRankIsNull() {
            addCriterion("dim_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andDimCityRankIsNotNull() {
            addCriterion("dim_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andDimCityRankEqualTo(String value) {
            addCriterion("dim_city_rank =", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankNotEqualTo(String value) {
            addCriterion("dim_city_rank <>", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankGreaterThan(String value) {
            addCriterion("dim_city_rank >", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankGreaterThanOrEqualTo(String value) {
            addCriterion("dim_city_rank >=", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankLessThan(String value) {
            addCriterion("dim_city_rank <", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankLessThanOrEqualTo(String value) {
            addCriterion("dim_city_rank <=", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankLike(String value) {
            addCriterion("dim_city_rank like", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankNotLike(String value) {
            addCriterion("dim_city_rank not like", value, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankIn(List<String> values) {
            addCriterion("dim_city_rank in", values, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankNotIn(List<String> values) {
            addCriterion("dim_city_rank not in", values, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankBetween(String value1, String value2) {
            addCriterion("dim_city_rank between", value1, value2, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andDimCityRankNotBetween(String value1, String value2) {
            addCriterion("dim_city_rank not between", value1, value2, "dimCityRank");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewIsNull() {
            addCriterion("mt_shop_fivescore_new is null");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewIsNotNull() {
            addCriterion("mt_shop_fivescore_new is not null");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewEqualTo(BigDecimal value) {
            addCriterion("mt_shop_fivescore_new =", value, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewNotEqualTo(BigDecimal value) {
            addCriterion("mt_shop_fivescore_new <>", value, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewGreaterThan(BigDecimal value) {
            addCriterion("mt_shop_fivescore_new >", value, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("mt_shop_fivescore_new >=", value, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewLessThan(BigDecimal value) {
            addCriterion("mt_shop_fivescore_new <", value, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewLessThanOrEqualTo(BigDecimal value) {
            addCriterion("mt_shop_fivescore_new <=", value, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewIn(List<BigDecimal> values) {
            addCriterion("mt_shop_fivescore_new in", values, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewNotIn(List<BigDecimal> values) {
            addCriterion("mt_shop_fivescore_new not in", values, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("mt_shop_fivescore_new between", value1, value2, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andMtShopFivescoreNewNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("mt_shop_fivescore_new not between", value1, value2, "mtShopFivescoreNew");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreIsNull() {
            addCriterion("dp_shop_fivescore is null");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreIsNotNull() {
            addCriterion("dp_shop_fivescore is not null");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreEqualTo(BigDecimal value) {
            addCriterion("dp_shop_fivescore =", value, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreNotEqualTo(BigDecimal value) {
            addCriterion("dp_shop_fivescore <>", value, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreGreaterThan(BigDecimal value) {
            addCriterion("dp_shop_fivescore >", value, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("dp_shop_fivescore >=", value, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreLessThan(BigDecimal value) {
            addCriterion("dp_shop_fivescore <", value, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreLessThanOrEqualTo(BigDecimal value) {
            addCriterion("dp_shop_fivescore <=", value, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreIn(List<BigDecimal> values) {
            addCriterion("dp_shop_fivescore in", values, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreNotIn(List<BigDecimal> values) {
            addCriterion("dp_shop_fivescore not in", values, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dp_shop_fivescore between", value1, value2, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpShopFivescoreNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("dp_shop_fivescore not between", value1, value2, "dpShopFivescore");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdIsNull() {
            addCriterion("dp_region_id is null");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdIsNotNull() {
            addCriterion("dp_region_id is not null");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdEqualTo(String value) {
            addCriterion("dp_region_id =", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdNotEqualTo(String value) {
            addCriterion("dp_region_id <>", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdGreaterThan(String value) {
            addCriterion("dp_region_id >", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("dp_region_id >=", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdLessThan(String value) {
            addCriterion("dp_region_id <", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdLessThanOrEqualTo(String value) {
            addCriterion("dp_region_id <=", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdLike(String value) {
            addCriterion("dp_region_id like", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdNotLike(String value) {
            addCriterion("dp_region_id not like", value, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdIn(List<String> values) {
            addCriterion("dp_region_id in", values, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdNotIn(List<String> values) {
            addCriterion("dp_region_id not in", values, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdBetween(String value1, String value2) {
            addCriterion("dp_region_id between", value1, value2, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionIdNotBetween(String value1, String value2) {
            addCriterion("dp_region_id not between", value1, value2, "dpRegionId");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameIsNull() {
            addCriterion("dp_region_name is null");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameIsNotNull() {
            addCriterion("dp_region_name is not null");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameEqualTo(String value) {
            addCriterion("dp_region_name =", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameNotEqualTo(String value) {
            addCriterion("dp_region_name <>", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameGreaterThan(String value) {
            addCriterion("dp_region_name >", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("dp_region_name >=", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameLessThan(String value) {
            addCriterion("dp_region_name <", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameLessThanOrEqualTo(String value) {
            addCriterion("dp_region_name <=", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameLike(String value) {
            addCriterion("dp_region_name like", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameNotLike(String value) {
            addCriterion("dp_region_name not like", value, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameIn(List<String> values) {
            addCriterion("dp_region_name in", values, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameNotIn(List<String> values) {
            addCriterion("dp_region_name not in", values, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameBetween(String value1, String value2) {
            addCriterion("dp_region_name between", value1, value2, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andDpRegionNameNotBetween(String value1, String value2) {
            addCriterion("dp_region_name not between", value1, value2, "dpRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdIsNull() {
            addCriterion("mt_region_id is null");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdIsNotNull() {
            addCriterion("mt_region_id is not null");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdEqualTo(String value) {
            addCriterion("mt_region_id =", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdNotEqualTo(String value) {
            addCriterion("mt_region_id <>", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdGreaterThan(String value) {
            addCriterion("mt_region_id >", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("mt_region_id >=", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdLessThan(String value) {
            addCriterion("mt_region_id <", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdLessThanOrEqualTo(String value) {
            addCriterion("mt_region_id <=", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdLike(String value) {
            addCriterion("mt_region_id like", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdNotLike(String value) {
            addCriterion("mt_region_id not like", value, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdIn(List<String> values) {
            addCriterion("mt_region_id in", values, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdNotIn(List<String> values) {
            addCriterion("mt_region_id not in", values, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdBetween(String value1, String value2) {
            addCriterion("mt_region_id between", value1, value2, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionIdNotBetween(String value1, String value2) {
            addCriterion("mt_region_id not between", value1, value2, "mtRegionId");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameIsNull() {
            addCriterion("mt_region_name is null");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameIsNotNull() {
            addCriterion("mt_region_name is not null");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameEqualTo(String value) {
            addCriterion("mt_region_name =", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameNotEqualTo(String value) {
            addCriterion("mt_region_name <>", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameGreaterThan(String value) {
            addCriterion("mt_region_name >", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("mt_region_name >=", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameLessThan(String value) {
            addCriterion("mt_region_name <", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameLessThanOrEqualTo(String value) {
            addCriterion("mt_region_name <=", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameLike(String value) {
            addCriterion("mt_region_name like", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameNotLike(String value) {
            addCriterion("mt_region_name not like", value, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameIn(List<String> values) {
            addCriterion("mt_region_name in", values, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameNotIn(List<String> values) {
            addCriterion("mt_region_name not in", values, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameBetween(String value1, String value2) {
            addCriterion("mt_region_name between", value1, value2, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtRegionNameNotBetween(String value1, String value2) {
            addCriterion("mt_region_name not between", value1, value2, "mtRegionName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdIsNull() {
            addCriterion("mt_district_id is null");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdIsNotNull() {
            addCriterion("mt_district_id is not null");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdEqualTo(Long value) {
            addCriterion("mt_district_id =", value, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdNotEqualTo(Long value) {
            addCriterion("mt_district_id <>", value, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdGreaterThan(Long value) {
            addCriterion("mt_district_id >", value, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mt_district_id >=", value, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdLessThan(Long value) {
            addCriterion("mt_district_id <", value, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdLessThanOrEqualTo(Long value) {
            addCriterion("mt_district_id <=", value, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdIn(List<Long> values) {
            addCriterion("mt_district_id in", values, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdNotIn(List<Long> values) {
            addCriterion("mt_district_id not in", values, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdBetween(Long value1, Long value2) {
            addCriterion("mt_district_id between", value1, value2, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictIdNotBetween(Long value1, Long value2) {
            addCriterion("mt_district_id not between", value1, value2, "mtDistrictId");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameIsNull() {
            addCriterion("mt_district_name is null");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameIsNotNull() {
            addCriterion("mt_district_name is not null");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameEqualTo(String value) {
            addCriterion("mt_district_name =", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameNotEqualTo(String value) {
            addCriterion("mt_district_name <>", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameGreaterThan(String value) {
            addCriterion("mt_district_name >", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameGreaterThanOrEqualTo(String value) {
            addCriterion("mt_district_name >=", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameLessThan(String value) {
            addCriterion("mt_district_name <", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameLessThanOrEqualTo(String value) {
            addCriterion("mt_district_name <=", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameLike(String value) {
            addCriterion("mt_district_name like", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameNotLike(String value) {
            addCriterion("mt_district_name not like", value, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameIn(List<String> values) {
            addCriterion("mt_district_name in", values, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameNotIn(List<String> values) {
            addCriterion("mt_district_name not in", values, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameBetween(String value1, String value2) {
            addCriterion("mt_district_name between", value1, value2, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andMtDistrictNameNotBetween(String value1, String value2) {
            addCriterion("mt_district_name not between", value1, value2, "mtDistrictName");
            return (Criteria) this;
        }

        public Criteria andTotalScoreIsNull() {
            addCriterion("total_score is null");
            return (Criteria) this;
        }

        public Criteria andTotalScoreIsNotNull() {
            addCriterion("total_score is not null");
            return (Criteria) this;
        }

        public Criteria andTotalScoreEqualTo(BigDecimal value) {
            addCriterion("total_score =", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreNotEqualTo(BigDecimal value) {
            addCriterion("total_score <>", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreGreaterThan(BigDecimal value) {
            addCriterion("total_score >", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_score >=", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreLessThan(BigDecimal value) {
            addCriterion("total_score <", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_score <=", value, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreIn(List<BigDecimal> values) {
            addCriterion("total_score in", values, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreNotIn(List<BigDecimal> values) {
            addCriterion("total_score not in", values, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_score between", value1, value2, "totalScore");
            return (Criteria) this;
        }

        public Criteria andTotalScoreNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_score not between", value1, value2, "totalScore");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntIsNull() {
            addCriterion("shopcode_order_cnt is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntIsNotNull() {
            addCriterion("shopcode_order_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntEqualTo(Long value) {
            addCriterion("shopcode_order_cnt =", value, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntNotEqualTo(Long value) {
            addCriterion("shopcode_order_cnt <>", value, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntGreaterThan(Long value) {
            addCriterion("shopcode_order_cnt >", value, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntGreaterThanOrEqualTo(Long value) {
            addCriterion("shopcode_order_cnt >=", value, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntLessThan(Long value) {
            addCriterion("shopcode_order_cnt <", value, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntLessThanOrEqualTo(Long value) {
            addCriterion("shopcode_order_cnt <=", value, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntIn(List<Long> values) {
            addCriterion("shopcode_order_cnt in", values, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntNotIn(List<Long> values) {
            addCriterion("shopcode_order_cnt not in", values, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntBetween(Long value1, Long value2) {
            addCriterion("shopcode_order_cnt between", value1, value2, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCntNotBetween(Long value1, Long value2) {
            addCriterion("shopcode_order_cnt not between", value1, value2, "shopcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntIsNull() {
            addCriterion("techcode_order_cnt is null");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntIsNotNull() {
            addCriterion("techcode_order_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntEqualTo(Long value) {
            addCriterion("techcode_order_cnt =", value, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntNotEqualTo(Long value) {
            addCriterion("techcode_order_cnt <>", value, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntGreaterThan(Long value) {
            addCriterion("techcode_order_cnt >", value, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntGreaterThanOrEqualTo(Long value) {
            addCriterion("techcode_order_cnt >=", value, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntLessThan(Long value) {
            addCriterion("techcode_order_cnt <", value, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntLessThanOrEqualTo(Long value) {
            addCriterion("techcode_order_cnt <=", value, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntIn(List<Long> values) {
            addCriterion("techcode_order_cnt in", values, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntNotIn(List<Long> values) {
            addCriterion("techcode_order_cnt not in", values, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntBetween(Long value1, Long value2) {
            addCriterion("techcode_order_cnt between", value1, value2, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andTechcodeOrderCntNotBetween(Long value1, Long value2) {
            addCriterion("techcode_order_cnt not between", value1, value2, "techcodeOrderCnt");
            return (Criteria) this;
        }

        public Criteria andGtvRankIsNull() {
            addCriterion("gtv_rank is null");
            return (Criteria) this;
        }

        public Criteria andGtvRankIsNotNull() {
            addCriterion("gtv_rank is not null");
            return (Criteria) this;
        }

        public Criteria andGtvRankEqualTo(Long value) {
            addCriterion("gtv_rank =", value, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankNotEqualTo(Long value) {
            addCriterion("gtv_rank <>", value, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankGreaterThan(Long value) {
            addCriterion("gtv_rank >", value, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankGreaterThanOrEqualTo(Long value) {
            addCriterion("gtv_rank >=", value, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankLessThan(Long value) {
            addCriterion("gtv_rank <", value, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankLessThanOrEqualTo(Long value) {
            addCriterion("gtv_rank <=", value, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankIn(List<Long> values) {
            addCriterion("gtv_rank in", values, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankNotIn(List<Long> values) {
            addCriterion("gtv_rank not in", values, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankBetween(Long value1, Long value2) {
            addCriterion("gtv_rank between", value1, value2, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andGtvRankNotBetween(Long value1, Long value2) {
            addCriterion("gtv_rank not between", value1, value2, "gtvRank");
            return (Criteria) this;
        }

        public Criteria andShopGtvIsNull() {
            addCriterion("shop_gtv is null");
            return (Criteria) this;
        }

        public Criteria andShopGtvIsNotNull() {
            addCriterion("shop_gtv is not null");
            return (Criteria) this;
        }

        public Criteria andShopGtvEqualTo(BigDecimal value) {
            addCriterion("shop_gtv =", value, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvNotEqualTo(BigDecimal value) {
            addCriterion("shop_gtv <>", value, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvGreaterThan(BigDecimal value) {
            addCriterion("shop_gtv >", value, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("shop_gtv >=", value, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvLessThan(BigDecimal value) {
            addCriterion("shop_gtv <", value, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvLessThanOrEqualTo(BigDecimal value) {
            addCriterion("shop_gtv <=", value, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvIn(List<BigDecimal> values) {
            addCriterion("shop_gtv in", values, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvNotIn(List<BigDecimal> values) {
            addCriterion("shop_gtv not in", values, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shop_gtv between", value1, value2, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andShopGtvNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shop_gtv not between", value1, value2, "shopGtv");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNull() {
            addCriterion("city_id is null");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNotNull() {
            addCriterion("city_id is not null");
            return (Criteria) this;
        }

        public Criteria andCityIdEqualTo(Long value) {
            addCriterion("city_id =", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotEqualTo(Long value) {
            addCriterion("city_id <>", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThan(Long value) {
            addCriterion("city_id >", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThanOrEqualTo(Long value) {
            addCriterion("city_id >=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThan(Long value) {
            addCriterion("city_id <", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThanOrEqualTo(Long value) {
            addCriterion("city_id <=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdIn(List<Long> values) {
            addCriterion("city_id in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotIn(List<Long> values) {
            addCriterion("city_id not in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdBetween(Long value1, Long value2) {
            addCriterion("city_id between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotBetween(Long value1, Long value2) {
            addCriterion("city_id not between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankIsNull() {
            addCriterion("gtv_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankIsNotNull() {
            addCriterion("gtv_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankEqualTo(Long value) {
            addCriterion("gtv_city_rank =", value, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankNotEqualTo(Long value) {
            addCriterion("gtv_city_rank <>", value, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankGreaterThan(Long value) {
            addCriterion("gtv_city_rank >", value, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("gtv_city_rank >=", value, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankLessThan(Long value) {
            addCriterion("gtv_city_rank <", value, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankLessThanOrEqualTo(Long value) {
            addCriterion("gtv_city_rank <=", value, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankIn(List<Long> values) {
            addCriterion("gtv_city_rank in", values, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankNotIn(List<Long> values) {
            addCriterion("gtv_city_rank not in", values, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankBetween(Long value1, Long value2) {
            addCriterion("gtv_city_rank between", value1, value2, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andGtvCityRankNotBetween(Long value1, Long value2) {
            addCriterion("gtv_city_rank not between", value1, value2, "gtvCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankIsNull() {
            addCriterion("shopcode_order_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankIsNotNull() {
            addCriterion("shopcode_order_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankEqualTo(Long value) {
            addCriterion("shopcode_order_city_rank =", value, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankNotEqualTo(Long value) {
            addCriterion("shopcode_order_city_rank <>", value, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankGreaterThan(Long value) {
            addCriterion("shopcode_order_city_rank >", value, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("shopcode_order_city_rank >=", value, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankLessThan(Long value) {
            addCriterion("shopcode_order_city_rank <", value, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankLessThanOrEqualTo(Long value) {
            addCriterion("shopcode_order_city_rank <=", value, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankIn(List<Long> values) {
            addCriterion("shopcode_order_city_rank in", values, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankNotIn(List<Long> values) {
            addCriterion("shopcode_order_city_rank not in", values, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankBetween(Long value1, Long value2) {
            addCriterion("shopcode_order_city_rank between", value1, value2, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderCityRankNotBetween(Long value1, Long value2) {
            addCriterion("shopcode_order_city_rank not between", value1, value2, "shopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andOpTypeIsNull() {
            addCriterion("op_type is null");
            return (Criteria) this;
        }

        public Criteria andOpTypeIsNotNull() {
            addCriterion("op_type is not null");
            return (Criteria) this;
        }

        public Criteria andOpTypeEqualTo(Integer value) {
            addCriterion("op_type =", value, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeNotEqualTo(Integer value) {
            addCriterion("op_type <>", value, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeGreaterThan(Integer value) {
            addCriterion("op_type >", value, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("op_type >=", value, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeLessThan(Integer value) {
            addCriterion("op_type <", value, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeLessThanOrEqualTo(Integer value) {
            addCriterion("op_type <=", value, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeIn(List<Integer> values) {
            addCriterion("op_type in", values, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeNotIn(List<Integer> values) {
            addCriterion("op_type not in", values, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeBetween(Integer value1, Integer value2) {
            addCriterion("op_type between", value1, value2, "opType");
            return (Criteria) this;
        }

        public Criteria andOpTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("op_type not between", value1, value2, "opType");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankIsNull() {
            addCriterion("kacka_shopcode_order_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankIsNotNull() {
            addCriterion("kacka_shopcode_order_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_city_rank =", value, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankNotEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_city_rank <>", value, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankGreaterThan(Long value) {
            addCriterion("kacka_shopcode_order_city_rank >", value, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_city_rank >=", value, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankLessThan(Long value) {
            addCriterion("kacka_shopcode_order_city_rank <", value, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankLessThanOrEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_city_rank <=", value, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankIn(List<Long> values) {
            addCriterion("kacka_shopcode_order_city_rank in", values, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankNotIn(List<Long> values) {
            addCriterion("kacka_shopcode_order_city_rank not in", values, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankBetween(Long value1, Long value2) {
            addCriterion("kacka_shopcode_order_city_rank between", value1, value2, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderCityRankNotBetween(Long value1, Long value2) {
            addCriterion("kacka_shopcode_order_city_rank not between", value1, value2, "kackaShopcodeOrderCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtIsNull() {
            addCriterion("shopcode_order_amt is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtIsNotNull() {
            addCriterion("shopcode_order_amt is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtEqualTo(BigDecimal value) {
            addCriterion("shopcode_order_amt =", value, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtNotEqualTo(BigDecimal value) {
            addCriterion("shopcode_order_amt <>", value, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtGreaterThan(BigDecimal value) {
            addCriterion("shopcode_order_amt >", value, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("shopcode_order_amt >=", value, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtLessThan(BigDecimal value) {
            addCriterion("shopcode_order_amt <", value, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("shopcode_order_amt <=", value, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtIn(List<BigDecimal> values) {
            addCriterion("shopcode_order_amt in", values, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtNotIn(List<BigDecimal> values) {
            addCriterion("shopcode_order_amt not in", values, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shopcode_order_amt between", value1, value2, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shopcode_order_amt not between", value1, value2, "shopcodeOrderAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankIsNull() {
            addCriterion("shopcode_order_amt_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankIsNotNull() {
            addCriterion("shopcode_order_amt_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankEqualTo(Long value) {
            addCriterion("shopcode_order_amt_city_rank =", value, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankNotEqualTo(Long value) {
            addCriterion("shopcode_order_amt_city_rank <>", value, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankGreaterThan(Long value) {
            addCriterion("shopcode_order_amt_city_rank >", value, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("shopcode_order_amt_city_rank >=", value, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankLessThan(Long value) {
            addCriterion("shopcode_order_amt_city_rank <", value, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankLessThanOrEqualTo(Long value) {
            addCriterion("shopcode_order_amt_city_rank <=", value, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankIn(List<Long> values) {
            addCriterion("shopcode_order_amt_city_rank in", values, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankNotIn(List<Long> values) {
            addCriterion("shopcode_order_amt_city_rank not in", values, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankBetween(Long value1, Long value2) {
            addCriterion("shopcode_order_amt_city_rank between", value1, value2, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeOrderAmtCityRankNotBetween(Long value1, Long value2) {
            addCriterion("shopcode_order_amt_city_rank not between", value1, value2, "shopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankIsNull() {
            addCriterion("kacka_shopcode_order_amt_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankIsNotNull() {
            addCriterion("kacka_shopcode_order_amt_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_amt_city_rank =", value, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankNotEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_amt_city_rank <>", value, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankGreaterThan(Long value) {
            addCriterion("kacka_shopcode_order_amt_city_rank >", value, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_amt_city_rank >=", value, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankLessThan(Long value) {
            addCriterion("kacka_shopcode_order_amt_city_rank <", value, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankLessThanOrEqualTo(Long value) {
            addCriterion("kacka_shopcode_order_amt_city_rank <=", value, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankIn(List<Long> values) {
            addCriterion("kacka_shopcode_order_amt_city_rank in", values, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankNotIn(List<Long> values) {
            addCriterion("kacka_shopcode_order_amt_city_rank not in", values, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankBetween(Long value1, Long value2) {
            addCriterion("kacka_shopcode_order_amt_city_rank between", value1, value2, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andKackaShopcodeOrderAmtCityRankNotBetween(Long value1, Long value2) {
            addCriterion("kacka_shopcode_order_amt_city_rank not between", value1, value2, "kackaShopcodeOrderAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtIsNull() {
            addCriterion("shopcode_actual_pay_amt is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtIsNotNull() {
            addCriterion("shopcode_actual_pay_amt is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_pay_amt =", value, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtNotEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_pay_amt <>", value, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtGreaterThan(BigDecimal value) {
            addCriterion("shopcode_actual_pay_amt >", value, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_pay_amt >=", value, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtLessThan(BigDecimal value) {
            addCriterion("shopcode_actual_pay_amt <", value, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_pay_amt <=", value, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtIn(List<BigDecimal> values) {
            addCriterion("shopcode_actual_pay_amt in", values, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtNotIn(List<BigDecimal> values) {
            addCriterion("shopcode_actual_pay_amt not in", values, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shopcode_actual_pay_amt between", value1, value2, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shopcode_actual_pay_amt not between", value1, value2, "shopcodeActualPayAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtIsNull() {
            addCriterion("shopcode_actual_consume_amt is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtIsNotNull() {
            addCriterion("shopcode_actual_consume_amt is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_consume_amt =", value, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtNotEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_consume_amt <>", value, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtGreaterThan(BigDecimal value) {
            addCriterion("shopcode_actual_consume_amt >", value, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_consume_amt >=", value, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtLessThan(BigDecimal value) {
            addCriterion("shopcode_actual_consume_amt <", value, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("shopcode_actual_consume_amt <=", value, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtIn(List<BigDecimal> values) {
            addCriterion("shopcode_actual_consume_amt in", values, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtNotIn(List<BigDecimal> values) {
            addCriterion("shopcode_actual_consume_amt not in", values, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shopcode_actual_consume_amt between", value1, value2, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("shopcode_actual_consume_amt not between", value1, value2, "shopcodeActualConsumeAmt");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankIsNull() {
            addCriterion("shopcode_actual_pay_amt_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankIsNotNull() {
            addCriterion("shopcode_actual_pay_amt_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankEqualTo(Long value) {
            addCriterion("shopcode_actual_pay_amt_city_rank =", value, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankNotEqualTo(Long value) {
            addCriterion("shopcode_actual_pay_amt_city_rank <>", value, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankGreaterThan(Long value) {
            addCriterion("shopcode_actual_pay_amt_city_rank >", value, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("shopcode_actual_pay_amt_city_rank >=", value, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankLessThan(Long value) {
            addCriterion("shopcode_actual_pay_amt_city_rank <", value, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankLessThanOrEqualTo(Long value) {
            addCriterion("shopcode_actual_pay_amt_city_rank <=", value, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankIn(List<Long> values) {
            addCriterion("shopcode_actual_pay_amt_city_rank in", values, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankNotIn(List<Long> values) {
            addCriterion("shopcode_actual_pay_amt_city_rank not in", values, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankBetween(Long value1, Long value2) {
            addCriterion("shopcode_actual_pay_amt_city_rank between", value1, value2, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualPayAmtCityRankNotBetween(Long value1, Long value2) {
            addCriterion("shopcode_actual_pay_amt_city_rank not between", value1, value2, "shopcodeActualPayAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankIsNull() {
            addCriterion("shopcode_actual_consume_amt_city_rank is null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankIsNotNull() {
            addCriterion("shopcode_actual_consume_amt_city_rank is not null");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankEqualTo(Long value) {
            addCriterion("shopcode_actual_consume_amt_city_rank =", value, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankNotEqualTo(Long value) {
            addCriterion("shopcode_actual_consume_amt_city_rank <>", value, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankGreaterThan(Long value) {
            addCriterion("shopcode_actual_consume_amt_city_rank >", value, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankGreaterThanOrEqualTo(Long value) {
            addCriterion("shopcode_actual_consume_amt_city_rank >=", value, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankLessThan(Long value) {
            addCriterion("shopcode_actual_consume_amt_city_rank <", value, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankLessThanOrEqualTo(Long value) {
            addCriterion("shopcode_actual_consume_amt_city_rank <=", value, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankIn(List<Long> values) {
            addCriterion("shopcode_actual_consume_amt_city_rank in", values, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankNotIn(List<Long> values) {
            addCriterion("shopcode_actual_consume_amt_city_rank not in", values, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankBetween(Long value1, Long value2) {
            addCriterion("shopcode_actual_consume_amt_city_rank between", value1, value2, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }

        public Criteria andShopcodeActualConsumeAmtCityRankNotBetween(Long value1, Long value2) {
            addCriterion("shopcode_actual_consume_amt_city_rank not between", value1, value2, "shopcodeActualConsumeAmtCityRank");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}