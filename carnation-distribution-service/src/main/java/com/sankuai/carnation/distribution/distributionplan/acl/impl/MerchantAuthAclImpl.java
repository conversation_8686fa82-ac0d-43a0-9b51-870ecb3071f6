package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.carnation.distribution.distributionplan.acl.MerchantAuthAcl;
import com.sankuai.carnation.distribution.distributionplan.exception.DistributionPlanException;
import com.sankuai.carnation.distribution.privatelive.consultant.utils.JsonUtil;
import com.sankuai.nibmp.infra.amp.attribute.lib.IAccountService;
import com.sankuai.nibmp.infra.amp.attribute.lib.param.AccountIdParam;
import com.sankuai.nibmp.infra.amp.attribute.lib.result.CustomerIdResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Service
@Slf4j
public class MerchantAuthAclImpl implements MerchantAuthAcl {

    private final static String CAT_TYPE = MerchantAuthAcl.class.getSimpleName();

    @Resource
    private IAccountService newAccountService;

    @Override
    public Optional<Long> getCustomerIdByAccountId(Long mtAccountId) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "getCustomerIdByAccountId");
        try {
            if (Objects.isNull(mtAccountId) || mtAccountId <= 0) {
                throw new IllegalArgumentException("无效的美团账号id");
            }
            AccountIdParam accountIdParam = new AccountIdParam();
            accountIdParam.setAccountId(mtAccountId);
            accountIdParam.setBizLine(2);
            CustomerIdResult result = newAccountService.getCustomerIdByAccountId(accountIdParam);
            if (Objects.isNull(result)) {
                throw new DistributionPlanException("根据账号ID查询客户ID接口返回为空");
            }
            if (Objects.isNull(result.getCustomerId())) {
                log.error("MerchantAuthAcl invoke IAccountService.getCustomerIdByAccountId failed, mtAccountId:{}, response:{}", mtAccountId, JsonUtil.toJson(result));
                throw new DistributionPlanException("根据账号ID查询客户ID接口接口返回失败,response:" + JsonUtil.toJson(result));
            }
            transaction.setSuccessStatus();
            return Optional.ofNullable(result.getCustomerId());
        } catch (Exception e) {
            log.error("MerchantAuthAclImpl.getCustomerIdByAccountId failed, mtAccountId:{}", mtAccountId, e);
            transaction.setStatus(e);
            return Optional.empty();
        } finally {
            transaction.complete();
        }
    }
}