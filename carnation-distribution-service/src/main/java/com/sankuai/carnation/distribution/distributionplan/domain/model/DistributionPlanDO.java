package com.sankuai.carnation.distribution.distributionplan.domain.model;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.dto.DistributionPlanAddRequest;
import com.sankuai.carnation.distribution.distributionplan.dto.DistributionPlanEditRequest;
import com.sankuai.carnation.distribution.distributionplan.enums.*;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributionPlanDO {

    /**
     * 方案id
     */
    private Long planId;

    /**
     * 行业类型
     * @see DistributionPlanIndustryTypeEnum
     */
    private DistributionPlanIndustryTypeEnum industryType;

    /**
     * 计划类型
     * @see DistributionPlanTypeEnum
     */
    private DistributionPlanTypeEnum planType;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 渠道
     * @see DistributionBusinessChannelEnum
     */
    private DistributionBusinessChannelEnum channel;

    /**
     * 状态
     */
    private DistributionPlanStatusEnum status;

    /**
     * 计划开始时间
     */
    private Date preBeginTime;

    /**
     * 计划结束时间
     */
    private Date preEndTime;

    /**
     * 真实结束时间
     */
    private Date endTime;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品类型
     * @see ProductTypeEnum
     */
    private ProductTypeEnum productType;

    /**
     * 提成策略值对象
     */
    private CommissionStrategyVO commissionStrategyVo;

    public static DistributionPlanDO buildByDistributionPlanAddRequest(DistributionPlanAddRequest request) {
        CommissionStrategyVO commissionStrategyVo = CommissionStrategyVO.buildByCommissionRate(request.getCommissionRate(), DistributionBusinessChannelEnum.fromCode(request.getChannel()));

        return DistributionPlanDO.builder()
                .industryType(DistributionPlanIndustryTypeEnum.from(request.getIndustryType()))
                .planName(request.getPlanName())
                .planType(DistributionPlanTypeEnum.from(request.getPlanType()))
                .channel(DistributionBusinessChannelEnum.fromCode(request.getChannel()))
                .status(DistributionPlanStatusEnum.INITIALIZE)
                .preBeginTime(new Date(request.getPreBeginTime()))
                .preEndTime(new Date(request.getPreEndTime()))
                .productId(request.getProductId())
                .productType(ProductTypeEnum.fromCode(request.getProductType()))
                .commissionStrategyVo(commissionStrategyVo)
                .build();
    }

    public boolean canEdit() {
        if (!this.getStatus().equals(DistributionPlanStatusEnum.INITIALIZE)) {
            return false;
        }
        return true;
    }

    public void edit(DistributionPlanEditRequest request) {
        CommissionStrategyVO commissionStrategyVo = CommissionStrategyVO.buildByCommissionRate(request.getCommissionRate(), DistributionBusinessChannelEnum.fromCode(request.getChannel()));
        this.setPlanName(request.getPlanName());
        this.setCommissionStrategyVo(commissionStrategyVo);
        this.setPreBeginTime(new Date(request.getPreBeginTime()));
        this.setPreEndTime(new Date(request.getPreEndTime()));
    }

    public boolean checkNextStatusOperationLegal(Integer operation) {
        DistributionPlanOperationEnum operationEnum = DistributionPlanOperationEnum.from(operation);
        if (operationEnum.equals(DistributionPlanOperationEnum.CANCEL)) {
            return Lists.newArrayList(DistributionPlanStatusEnum.INITIALIZE, DistributionPlanStatusEnum.EXECUTING).contains(this.status);
        }

        return false;
    }

    public void changeStatusByOperation(Integer operation) {
        DistributionPlanOperationEnum operationEnum = DistributionPlanOperationEnum.from(operation);
        if (operationEnum.equals(DistributionPlanOperationEnum.CANCEL)) {
            this.setStatus(DistributionPlanStatusEnum.CANCELED);
            this.setEndTime(new Date());
        }
    }

    public void changeStatus(DistributionPlanStatusEnum status) {
        this.setStatus(status);
        if (status.equals(DistributionPlanStatusEnum.CANCELED) || status.equals(DistributionPlanStatusEnum.END)) {
            this.setEndTime(new Date());
        }
    }

    public static String calCommissionRateToThousandths(BigDecimal commissionRate) {
        BigDecimal oneHundred = new BigDecimal("100.0");
        return commissionRate.divide(oneHundred, 3, RoundingMode.DOWN).toString();
    }

}
