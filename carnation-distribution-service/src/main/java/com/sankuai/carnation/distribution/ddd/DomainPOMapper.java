package com.sankuai.carnation.distribution.ddd;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;

/**
 * @Author: bian<PERSON>han
 * @CreateTime: 2022-04-29 15:16
 * @Description:
 */

public interface DomainPOMapper<Po,PoExample,Entity> {

    default  int insertSelective(Po record) {
        return getBaseMapper().insertSelective(record);
    }

   default boolean isUpdateSuccess(Po delta, Entity entity) {
      return getBaseMapper().updateByExampleSelective(delta,createPoExample(entity)) == 1;
   }

    PoExample createPoExample(Entity entity);

    default Po findByPrimaryKey(Long id) {
        return getBaseMapper().selectByPrimaryKey(id);
    }

    MybatisBaseMapper<Po, PoExample, Long> getBaseMapper();

}
