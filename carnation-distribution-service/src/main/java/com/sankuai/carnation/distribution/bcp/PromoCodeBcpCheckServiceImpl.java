package com.sankuai.carnation.distribution.bcp;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.event.api.distribution.dto.distributor.RebateActivityRuleDTO;
import com.dianping.gmkt.event.api.distribution.service.DistributorActivityService;
import com.dianping.gmkt.event.api.model.CommonResponse;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.rebate.enums.RebateSettleRuleType;
import com.dianping.gmkt.event.api.scan.UserScanRecordService;
import com.dianping.gmkt.event.api.scan.request.UserScanRecordDTO;
import com.dianping.gmkt.event.api.scan.request.UserScanRequest;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.order.common.enums.AmountType;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.carnation.distribution.commission.dto.CommissionVerifyCalculationRequest;
import com.sankuai.carnation.distribution.commission.enums.RebateEventSourceEnum;
import com.sankuai.carnation.distribution.commission.enums.RebateValidTimeEnum;
import com.sankuai.carnation.distribution.commission.settle.enums.OrderVerifyRebateSourceEnum;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.CommissionRateGateRuleGroupBO;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountCalcRequest;
import com.sankuai.carnation.distribution.commisson.domain.factory.commission.bo.RebateAmountResult;
import com.sankuai.carnation.distribution.commisson.exceptions.CommissionCalculateException;
import com.sankuai.carnation.distribution.commisson.limit.RebateRuleLimitInterface;
import com.sankuai.carnation.distribution.commisson.limit.domain.RebateLimitResult;
import com.sankuai.carnation.distribution.commisson.settle.rule.ShopCommissionRuleDomainService;
import com.sankuai.carnation.distribution.commisson.settle.rule.bo.ShopPredictEffectCommissionRuleBO;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderPaymentDetailBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderSkuBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.order.tech.TechnicianDistributionExtParamBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.ProductDetailAclService;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductDetailBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductSkuBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.request.ProductDetailRequest;
import com.sankuai.carnation.distribution.promocode.acl.ShopApolloBuAclService;
import com.sankuai.carnation.distribution.promocode.acl.TechCateAndIndustryAclService;
import com.sankuai.carnation.distribution.promocode.acl.enums.BusinessDepartmentIdEnum;
import com.sankuai.carnation.distribution.promocode.rebate.domain.TechPromoCodeRebateDomainService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.technician.trade.api.settle.tradereturn.enums.ReturnBizLineEnum;
import com.sankuai.technician.trade.common.enums.OrderTypeEnum;
import com.sankuai.technician.trade.order.dto.model.DistributeActivityModel;
import com.sankuai.technician.trade.order.dto.model.OrderReceiptDto;
import com.sankuai.technician.trade.order.dto.model.OrderVerifyReceiptDto;
import com.sankuai.technician.trade.order.service.OrderReceiptQueryService;
import com.sankuai.technician.trade.order.service.OrderVerifyReceiptQueryService;
import com.sankuai.technician.trade.types.enums.OrderDistributionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
@Service
@Slf4j
public class PromoCodeBcpCheckServiceImpl implements PromoCodeBcpCheckService {

    private static final String REBATE_LIMIT_HOUR_KEY = "com.sankuai.medicalcosmetology.distribution.service.promo.code.rebate.limit.hour.category";
    private static final String TRANSFER_INSTRUCT_CHANNEL_LIST = "transfer.instruct.channel.list.config";
    private static final Integer ORDER_VERIFIED = 1;  // 收券表是核销态
    private static final Integer NOTIFY_VERIFY = 2; // 消息是核销态
    @Autowired
    private List<RebateRuleLimitInterface> limitInterfaceList;
    @Autowired
    private OrderInfoAdaptor orderInfoAdaptor;
    @Autowired
    private ShopCommissionRuleDomainService shopCommissionRuleDomainService;
    @Autowired
    private TechCateAndIndustryAclService techCateAndIndustryAclService;
    @Autowired
    private ShopApolloBuAclService shopApolloBuAclService;
    @Autowired
    private ShopMapperService shopMapperService;
    @Autowired
    private ShopAcl shopAcl;
    @Autowired
    private UserScanRecordService userScanRecordService;
    @Autowired
    private DistributorActivityService distributorActivityService;
    @Autowired
    private ProductDetailAclService productDetailAclService;
    @Autowired
    private OrderReceiptQueryService orderReceiptQueryService;
    @Autowired
    private OrderVerifyReceiptQueryService orderVerifyReceiptQueryService;
    @Autowired
    private TechPromoCodeRebateDomainService techPromoCodeRebateDomainService;
    @Autowired
    private RbOrderVerifyRebateCommissionDataService rbOrderVerifyRebateCommissionDataService;

    public String checkRebateByOrder(String orderId,Integer orderType,String couponId){
        try {
            TechnicianResp<OrderReceiptDto> respOrderReceipt = orderReceiptQueryService.queryOrderReceipt(orderId, orderType);
            if (!respOrderReceipt.respSuccess() || respOrderReceipt.getData() == null) return "订单查询失败 or 不存在订单";

            TechnicianResp<OrderVerifyReceiptDto> respOrderVerifyReceipt = orderVerifyReceiptQueryService
                    .queryOrderVerifyReceiptByOrderIdAndCouponId(orderId,
                            orderType, couponId);

            if (respOrderVerifyReceipt == null || !respOrderVerifyReceipt.respSuccess() || respOrderVerifyReceipt.getData() == null)
                return "核销凭证查询失败 or 不存在核销凭证";

            OrderOperateNotifyBcpDTO orderOperateNotify = buildOrderOperateNotify(respOrderReceipt.getData(), respOrderVerifyReceipt.getData());
            Integer result = checkRebateAmount(orderOperateNotify);

            return result == null ? "核对查询失败" : (result == -1 ? "核对金额一致" : "核对金额不一致");
        }catch (Exception exception){
            log.error("PromoCodeBcpCheckService-checkRebateByOrder-error: ",exception);
            return "核对异常";
        }
    }
    public OrderOperateNotifyBcpDTO buildOrderOperateNotify(OrderReceiptDto orderReceiptDto, OrderVerifyReceiptDto orderVerifyReceiptDto){
        OrderOperateNotifyBcpDTO orderOperateNotify = new OrderOperateNotifyBcpDTO();

        orderOperateNotify.setProductType(orderReceiptDto.getProductType());
        orderOperateNotify.setProductId(orderReceiptDto.getProductId());
        orderOperateNotify.setOrderId(orderReceiptDto.getOrderId());
        orderOperateNotify.setOrderType(orderReceiptDto.getOrderType());
        //orderOperateNotify.setOrderIdLong(orderReceiptDto.get());
        orderOperateNotify.setOperateType(orderVerifyReceiptDto.getStatus() == ORDER_VERIFIED ? NOTIFY_VERIFY : null);   // OperateType对应的券的状态，只验证核销态
        orderOperateNotify.setBizId(orderVerifyReceiptDto.getCouponId());
        //orderOperateNotify.setBizType();
        //orderOperateNotify.setExtKey();
        //orderOperateNotify.setProductName();
        //orderOperateNotify.setMtShopId(orderVerifyReceiptDto.getShopId()); // ?
        //orderOperateNotify.setShopName();
        // orderOperateNotify.setQty(orderVerifyReceiptDto.getQuantity()); // ?
        orderOperateNotify.setChannel(orderReceiptDto.getChannel());
        orderOperateNotify.setActionTime(orderVerifyReceiptDto.getActionTime().getTime());
        orderOperateNotify.setExtInfo(orderReceiptDto.getExtInfo());

        return orderOperateNotify;
    }


    // 计算返利佣金
    public Integer checkRebateAmount(OrderOperateNotifyBcpDTO orderOperateNotify) {
        try {

            if (!promoCodeFilter(orderOperateNotify)) return -1; // 过滤非职人订单

            CommissionVerifyCalculationRequest request = buildCommissionVerifyCalculationRequest(orderOperateNotify);
            if (request == null){
                return -1;
            }
            if (request.getChannel() == null || !request.getChannel().equals("promo_code")) return -1; // 分销渠道为空或非职人码

            OrderInfoBO orderInfo = orderInfoAdaptor.getOrder(request.getOrderType(), request.getOrderId());
            CommissionRateGateRuleGroupBO rateRuleGroup = calculateCommissionRateRuleGroup(request);
            ShopPredictEffectCommissionRuleBO frameRule = shopCommissionRuleDomainService.getLowestCommissionRule(request.getDpShopId(), 0, orderInfo.getPayTime(), request.getVerifyTime(), Lists.newArrayList(OrderVerifyRebateSourceEnum.ANNUAL_FRAME.getCode(), OrderVerifyRebateSourceEnum.MANUAL_SHOP_COMMISSION.getCode()));
            if (rateRuleGroup != null && frameRule != null) return -1; // 过滤掉年框返利

            long rebateAmount = calculateNoFrameRebateAmount(request);
            if (rebateAmount == -1) return -1;
            // 查询返利记录进行核对
            request.setUesrId(orderInfo.getUserId());
            request.setPlatform(orderInfo.getPlatform());
            RebateAmountCalcRequest rebateAmountCalcRequest = getRebateAmountCalcRequest(request, orderInfo);
            List<RbOrderVerifyRebateCommission> rbOrderVerifyRebateCommissions = rbOrderVerifyRebateCommissionDataService.loadByActivityIdAndUser(rebateAmountCalcRequest.getTechApplyRecordId(), rebateAmountCalcRequest.getUserId(), rebateAmountCalcRequest.getPlatform(), null);
            if (rbOrderVerifyRebateCommissions == null) return -1;

            rbOrderVerifyRebateCommissions = rbOrderVerifyRebateCommissions.stream().filter
                    (record -> Objects.equals(record.getOrderId(), orderOperateNotify.getOrderId()) && Objects.equals(record.getCouponId(), orderOperateNotify.getBizId()))
                    .collect(Collectors.toList());
            if(rbOrderVerifyRebateCommissions.isEmpty()) return -1;
            rbOrderVerifyRebateCommissions.sort(Comparator.comparing(RbOrderVerifyRebateCommission::getAddTime).reversed());
            RbOrderVerifyRebateCommission rbOrderVerifyRebateCommission = rbOrderVerifyRebateCommissions.get(0);
            Long rebateAmountActual = rbOrderVerifyRebateCommission.getFinalRebateCent() != null ? rbOrderVerifyRebateCommission.getFinalRebateCent() : rbOrderVerifyRebateCommission.getPredictRebateCent();
            if(rebateAmountActual == null)    return -1;


            if (commissionRateSynchronized(request.getVerifyTime())) {
                boolean sdOrder = techPromoCodeRebateDomainService.isTechSdOrder(request.getOrderId(), request.getCouponVerifyId(), request.getTechId());
                if (sdOrder) rebateAmount = 0; // 低佣单佣金为0
            }
            return rebateAmount == rebateAmountActual ? -1 : 1;

        }catch (Exception exception){
            log.error("PromoCodeBcpCheckService-checkRebateAmount-error:",exception);
            return -1;
        }
    }
    // 构建返利佣金请求
    public CommissionVerifyCalculationRequest buildCommissionVerifyCalculationRequest(OrderOperateNotifyBcpDTO orderOperateNotify){
        try {
            CommissionVerifyCalculationRequest request = new CommissionVerifyCalculationRequest();
            TechnicianResp<OrderReceiptDto> resp = orderReceiptQueryService.queryOrderReceipt(orderOperateNotify.getOrderId(), orderOperateNotify.getOrderType());
            if (!resp.respSuccess() || resp.getData() == null) return null; // 订单凭证不存在 or 订单查询失败

            TechnicianResp<OrderVerifyReceiptDto> resp1 = orderVerifyReceiptQueryService
                    .queryOrderVerifyReceiptByOrderIdAndCouponId(orderOperateNotify.getOrderId(),
                            orderOperateNotify.getOrderType(), orderOperateNotify.getBizId());
            if (resp1 == null || !resp1.respSuccess() || resp1.getData() == null) return null; // 核销凭证获取失败

            OrderReceiptDto orderReceiptDto = resp.getData();
            OrderVerifyReceiptDto orderVerifyReceiptDto = resp1.getData();
            BcpCheckRule bcpCheckRule = BcpCheckRuleHelper.getRule(ReturnBizLineEnum.TECH_PROMO_CODE);

            if (orderReceiptDto.getIsCheat() && SettleBillUtils.needTradeRiskControl(bcpCheckRule))
                return null; // 是风控订单

            if (orderVerifyReceiptDto.getStatus() != 1) return null; // 优惠券不是核销态

            request.setOrderId(orderReceiptDto.getOrderId());
            request.setOrderType(orderReceiptDto.getOrderType());
            request.setProductId(orderReceiptDto.getProductId());
            request.setProductType(orderReceiptDto.getProductType());
            request.setCouponVerifyId(orderOperateNotify.getBizId());
            request.setCouponVerifyCode(orderOperateNotify.getExtKey());
            request.setVerifyTime(new Date(orderOperateNotify.getActionTime()));
            request.setChannel(orderReceiptDto.getExtInfo() == null ? null : orderReceiptDto.getExtInfo().get("channel"));
            request.setDpShopId(orderVerifyReceiptDto.getShopId());
            request.setDistributeProductId(orderReceiptDto.getExtInfo() == null ? 0 : NumberUtils.toLong(orderReceiptDto.getExtInfo().get("distributionProductId")));
            request.setDistributorId(orderReceiptDto.getExtInfo() == null ? 0 : NumberUtils.toInt(orderReceiptDto.getExtInfo().get("distributorId")));
            request.setDistributorGroupId(orderReceiptDto.getExtInfo() == null ? 0 : NumberUtils.toInt(orderReceiptDto.getExtInfo().get("distributorGroupId")));

            DistributeActivityModel distributeActivityModel = getDistributeActivity(orderReceiptDto.getExtInfo());
            if (distributeActivityModel != null) {
                request.setTechId(distributeActivityModel.getTechId());
                request.setStaffCodeId(distributeActivityModel.getStaffCodeId());
                request.setTechApplyRecordId(distributeActivityModel.getTechRecordId());
                request.setEventSource(distributeActivityModel.getEventSource());
            }
            return request;
        }catch (Exception exception){
            log.error("PromoCodeBcpCheckService-buildCommissionVerifyCalculationRequest-error:",exception);
            return null;
        }
    }
    // 过滤非职人订单
    public boolean promoCodeFilter(OrderOperateNotifyBcpDTO orderOperateNotify){
        try {
            if (settledBaseInstruct(orderOperateNotify)) return false; // 基于结算指令的计费过滤

            DistributeActivityModel distributeActivity = getDistributeActivity(orderOperateNotify.getExtInfo());
            if (distributeActivity == null || distributeActivity.getTechId() <= 0
                    || distributeActivity.getEventSource() != RebateEventSourceEnum.PROMO_CODE_REBATE_EVENT.getCode()) { // 过滤非优惠码
                return false;
            }
            // 自定义settleBillRule规则，但消息命令不在有效命令内过滤掉
            BcpCheckRule settleBillRule = BcpCheckRuleHelper.getRule(ReturnBizLineEnum.TECH_PROMO_CODE);
            List<Integer> validCommandList = settleBillRule != null ? settleBillRule.getValidCommandList() : null;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(validCommandList) && !validCommandList.contains(1)) {
                return false;
            }
            // 核销态过滤
            if (orderOperateNotify.getOperateType() != 2) return false;
            return true;
        }catch (Exception exception){
            log.error("PromoCodeBcpCheckService-PromoCodeFilter-error:",exception);
            return false;
        }
    }
    // 非年框佣金计算
    public long calculateNoFrameRebateAmount(CommissionVerifyCalculationRequest request){
        try {
            OrderInfoBO orderInfo = orderInfoAdaptor.getOrder(DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode(), request.getOrderId());
            int rebateLimitHour = getRebateLimitHour(request, orderInfo);
            if (request.getVerifyTime() != null && orderInfo.getOrderTime() != null && request.getVerifyTime().getTime() - orderInfo.getOrderTime().getTime() > (rebateLimitHour * 3600000L))
                return -1; // 时间过滤
            if (!checkUserScanTime(request, orderInfo, rebateLimitHour) && !OrderDistributionTypeEnum.DISCOUNT_CODE.getName().equalsIgnoreCase(getOrderTechChannel(orderInfo)))
                return -1; // 扫码时间过滤

            OrderReceiptDto orderReceipt = getOrderReceipt(request.getOrderId()); // 查询订单信息是否为空
            if (orderReceipt == null) return -1;

            request.setUesrId(orderInfo.getUserId());
            request.setPlatform(orderInfo.getPlatform());
            RebateAmountCalcRequest rebateAmountCalcRequest = getRebateAmountCalcRequest(request, orderInfo);

            if (rebateAmountCalcRequest.getTechApplyRecordId() == 0) return -1; // 下单无有效报名记录
            CommonResponse<RebateActivityRuleDTO> ruleDTOCommonResponse = distributorActivityService.queryActivityRule(rebateAmountCalcRequest.getTechApplyRecordId());
            if (!ruleDTOCommonResponse.isSuccess() || ruleDTOCommonResponse.getData() == null) return -1; // 未查到对应的活动信息

            RebateActivityRuleDTO rebateActivityRuleDTO = ruleDTOCommonResponse.getData();// 获取活动规则
            if (rebateActivityRuleDTO == null)  return -1;
            Integer type = rebateActivityRuleDTO.getRule().getType(); // 返利规则类型
            if (!Objects.equals(type, RebateSettleRuleType.NORMAL.code))  return -1;// 非普通返利
            if(rebateActivityRuleDTO.getRule().getNormalRule().getRebateAmount() == null)  return -1;
            // 佣金取低(活动规则限制)
            RebateAmountResult rebateAmountResult = new RebateAmountResult();
            rebateAmountResult.setOrderId(rebateAmountCalcRequest.getOrderId());
            rebateAmountResult.setOrderPrice(rebateAmountCalcRequest.getPayCent());
            rebateAmountResult.setRebateAmount(rebateActivityRuleDTO.getRule().getNormalRule().getRebateAmount());
            RebateAmountResult limitResult = handleRebateRuleLimit(request,rebateAmountResult);
            long amount = limitResult != null ? limitResult.getRebateAmount() : 0;
            return amount != 0 ? amount : -1; // 计算的返利佣金为0不核对p
        }catch (Exception exception){
            log.error("PromoCodeBcpCheckService-calculateNoFrameRebateAmount-error:",exception);
            return -1;
        }
    }
    // 过滤基于结算指令计费
    public boolean settledBaseInstruct(OrderOperateNotifyBcpDTO orderOperateNotify) {
        List<Integer> channelList = Lion.getList(MdpContextUtils.getAppKey(),TRANSFER_INSTRUCT_CHANNEL_LIST,Integer.class, Collections.emptyList());
        if (org.springframework.util.CollectionUtils.isEmpty(channelList)) {
            return false;
        }
        return channelList.contains(orderOperateNotify.getChannel());
    }
    public CommissionRateGateRuleGroupBO calculateCommissionRateRuleGroup(CommissionVerifyCalculationRequest request) {
        List<CommissionRateGateRuleGroupBO> ruleGroupList = getCommissionRateRuleGroupList();
        if (CollectionUtils.isEmpty(ruleGroupList)) {
            return null;
        }
        OrderInfoBO order = orderInfoAdaptor.getOrder(request.getOrderType(), request.getOrderId());
        Date orderTime = Optional.ofNullable(order).map(OrderInfoBO::getOrderTime).orElse(null);
        int techId = request.getTechId();
        int techIndustry = techCateAndIndustryAclService.getTechIndustry(techId);
        BusinessDepartmentIdEnum businessDepartment = shopApolloBuAclService.getBusinessDepartment(request.getDpShopId());
        return ruleGroupList.stream()
                .filter(Objects::nonNull)
                .filter(ruleGroup -> {
                    if (ruleGroup.getStartTime() != null && orderTime != null && ruleGroup.getStartTime().after(orderTime)) {
                        return false;
                    }
                    if (ruleGroup.getEndTime() != null && orderTime != null && ruleGroup.getEndTime().before(orderTime)) {
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(ruleGroup.getTechIdList()) && !ruleGroup.getTechIdList().contains(techId)) {
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(ruleGroup.getTechIndustryList()) && !ruleGroup.getTechIndustryList().contains(techIndustry)) {
                        return false;
                    }
                    if (CollectionUtils.isNotEmpty(ruleGroup.getShopDepartmentList()) && businessDepartment != null
                            && !ruleGroup.getShopDepartmentList().contains(businessDepartment.getBuId())) {
                        return false;
                    }
                    return true;
                })
                .max(Comparator.comparing(CommissionRateGateRuleGroupBO::getPriority))
                .orElse(null);
    }
    public List<CommissionRateGateRuleGroupBO> getCommissionRateRuleGroupList() {
        return Lion.getList(Environment.getAppName(), "promo.code.tech.predict.rebate.commission.rule-list", CommissionRateGateRuleGroupBO.class);
    }
    public int getRebateLimitHour(CommissionVerifyCalculationRequest request, OrderInfoBO orderInfo) {
        Long dpShopId;
        if (orderInfo.getShopId() != null && orderInfo.getShopId() != 0L) {
            dpShopId = orderInfo.getShopId();
            if (orderInfo.getPlatform() == PlatformEnum.MT.getCode()) {
                dpShopId = shopMapperService.mt2dp(orderInfo.getShopId());
            }
        } else {
            dpShopId = request.getDpShopId();
        }
        int productRebateLimitHour = Environment.isTestEnv() ? 1 : RebateValidTimeEnum.fromCode(orderInfo.getProductType()).getHours();
        return Math.max(productRebateLimitHour, getRebateLimitHour(dpShopId));
    }
    public int getRebateLimitHour(Long dpShopId) {
        Map<String, Integer> categoryMap = Lion.getMap(Environment.getAppName(), REBATE_LIMIT_HOUR_KEY, Integer.class, ImmutableMap.of("-1", 24));
        if (dpShopId == null || dpShopId == 0L) {
            return categoryMap.getOrDefault("-1", 24);
        }

        List<DpPoiDTO> dpPoiDTOList = shopAcl.getDpPoiDTOList(Lists.newArrayList(dpShopId));
        if (CollectionUtils.isNotEmpty(dpPoiDTOList)) {
            DpPoiDTO dpPoiDTO = dpPoiDTOList.get(0);
            List<DpPoiBackCategoryDTO> backCategoryDTOList = dpPoiDTO.getBackMainCategoryPath();
            // 按照类目level由小到大，优先根据一级类目来，其次二级……
            backCategoryDTOList = backCategoryDTOList.stream()
                    .sorted(Comparator.comparing(DpPoiBackCategoryDTO::getCategoryLevel)).collect(Collectors.toList());

            for (DpPoiBackCategoryDTO dpPoiBackCategoryDTO : backCategoryDTOList) {
                String categoryId = String.valueOf(dpPoiBackCategoryDTO.getCategoryId());
                if (categoryMap.containsKey(categoryId)) {
                    return categoryMap.get(categoryId);
                }
            }
        }
        return categoryMap.getOrDefault("-1", 24);
    }
    public boolean checkUserScanTime(CommissionVerifyCalculationRequest verifyCalculationRequest, OrderInfoBO orderInfo, int rebateLimitHour) {
        Date verifyTime = Optional.ofNullable(verifyCalculationRequest.getVerifyTime()).orElse(new Date());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(Optional.ofNullable(verifyCalculationRequest.getVerifyTime()).orElse(new Date()));
        calendar.add(Calendar.HOUR_OF_DAY, -rebateLimitHour);
        calendar.add(Calendar.HOUR_OF_DAY, (Environment.isTestEnv() ? -2 : -6) * 24);
        Date weekAgo = calendar.getTime();
        long verifyShopId = verifyCalculationRequest.getDpShopId();
        if (orderInfo.getPlatform() == PlatformEnum.MT.getCode()) {
            Long mtShopId = shopMapperService.dp2mt(verifyShopId);
            if (mtShopId != null && mtShopId > 0) {
                verifyShopId = mtShopId;
            }
        }
        UserScanRequest request = new UserScanRequest();
        request.setPlatform(orderInfo.getPlatform());
        request.setShopId(verifyShopId);
        request.setUserId(orderInfo.getUserId());
        request.setScanEarliestTime(weekAgo);
        request.setScanLatestTime(verifyTime);
        request.setRows(1);
        PromoQRCodeResponse<List<UserScanRecordDTO>> response = userScanRecordService.queryLatestScanRecord(request);
        if (response == null || !response.isSuccess()) {
            throw new CommissionCalculateException(response == null ? "扫码结果返回空" : response.getMsg());
        }
        List<UserScanRecordDTO> scanRecordList = response.getData().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scanRecordList)) {
            return false;
        }
        return true;
    }
    public String getOrderTechChannel(OrderInfoBO orderInfo) {
        return Optional.of(orderInfo)
                .map(OrderInfoBO::getTechExtParam)
                .map(TechnicianDistributionExtParamBO::getChannel)
                .filter(StringUtils::isNotBlank)
                .orElse("");
    }
    public long getActualPayCent(OrderInfoBO orderInfo) {
        if (CollectionUtils.isEmpty(orderInfo.getPaymentDetailList())) {
            return 0L;
        }
        return orderInfo.getPaymentDetailList().stream()
                .filter(pay -> pay.getAmountType() == AmountType.MEITUANPAY.value)
                .map(OrderPaymentDetailBO::getAmount)
                .reduce(BigDecimal::add)
                .map(val -> val.multiply(BigDecimal.valueOf(100L)).longValue())
                .orElse(0L);
    }
    @NotNull
    public RebateAmountCalcRequest getRebateAmountCalcRequest(CommissionVerifyCalculationRequest request, OrderInfoBO orderInfo) {
        RebateAmountCalcRequest rebateAmountCalcRequest = new RebateAmountCalcRequest();
        rebateAmountCalcRequest.setOrderId(request.getOrderId());
        rebateAmountCalcRequest.setTechApplyRecordId(request.getTechApplyRecordId());
        rebateAmountCalcRequest.setBuySuccessTime(orderInfo.getBuySuccessTime());
        rebateAmountCalcRequest.setOrderVerifyTimes(calculateOrderVerifyTimes(orderInfo));
        rebateAmountCalcRequest.setPayCent(getActualPayCent(orderInfo));
        rebateAmountCalcRequest.setUserId(orderInfo.getUserId());
        rebateAmountCalcRequest.setPlatform(orderInfo.getPlatform());
        rebateAmountCalcRequest.setBizId(getRebateBizId(request));
        rebateAmountCalcRequest.setProductType(orderInfo.getProductType());
        rebateAmountCalcRequest.setProductId(orderInfo.getProductId());
        return rebateAmountCalcRequest;
    }
    public int calculateOrderVerifyTimes(OrderInfoBO orderInfo) {
        int verifyTimes = orderInfo.getSkuList().stream()
                .map(OrderSkuBO::getTotalVerifyCount)
                .reduce(Integer::sum)
                .orElse(1);
        ProductDetailBO productDetail = null;
        if (orderInfo.getProductType() == ProductTypeEnum.TIME_CARD.getCode() || orderInfo.getProductType() == ProductTypeEnum.PREPAY.getCode()) {
            productDetail = getOrderProduct(orderInfo.getProductType(), orderInfo.getProductId(), orderInfo.getPlatform(), orderInfo.getUserId(), 3);
            if (productDetail != null && CollectionUtils.isNotEmpty(productDetail.getSkuList())) {
                Map<Long, Integer> skuTimesMap = productDetail.getSkuList().stream()
                        .collect(Collectors.toMap(ProductSkuBO::getSkuId, ProductSkuBO::getTimes, (ov, nv) -> nv));
                verifyTimes = orderInfo.getSkuList().stream()
                        .filter(Objects::nonNull)
                        .map(orderSkuBO -> orderSkuBO.getQuantity() * Math.max(skuTimesMap.getOrDefault(orderSkuBO.getSkuId(), 1), 1))
                        .reduce(Integer::sum)
                        .orElse(1);
            }
        }
        return verifyTimes;
    }
    public ProductDetailBO getOrderProduct(int productType, long productId, int platform, long userId, int retryTimes) {
        for (int i = 0; i < Math.max(1, retryTimes); i++) {
            ProductDetailBO productDetail = getOrderProduct(productType, productId, platform, userId);
            if (productDetail != null) {
                return productDetail;
            }
        }
        return null;
    }
    public ProductDetailBO getOrderProduct(int productType, long productId, int platform, long userId) {
        ProductDetailRequest request = new ProductDetailRequest();
        request.setProductType(productType);
        request.setProductIdList(Lists.newArrayList(productId));
        request.setPlatform(platform);
        request.setUserId(userId);
        Map<Long, ProductDetailBO> productDetailMap = productDetailAclService.batchGetProduct(request);
        return productDetailMap.get(productId);
    }
    public String getRebateBizId(CommissionVerifyCalculationRequest request) {
        return String.format("o%s_c%s_r%s", request.getOrderId(), request.getCouponVerifyId(), request.getTechApplyRecordId());
    }

    public DistributeActivityModel getDistributeActivity(Map<String, String> extInfo){
        if (MapUtils.isEmpty(extInfo)) {
            return null;
        }
        String activityModelStr = extInfo.get("activityInfo");
        if(org.apache.commons.lang.StringUtils.isEmpty(activityModelStr)){
            return null;
        }

        return JSON.parseObject(activityModelStr, DistributeActivityModel.class);
    }
    public boolean commissionRateSynchronized(Date verifyTime) {
        Calendar calendar = Calendar.getInstance();
        if (Environment.isTestEnv()) {
            calendar.add(Calendar.MINUTE, -1);
        } else {
            calendar.add(Calendar.HOUR_OF_DAY, -18);
        }
        Date syncVerifyTime = calendar.getTime();
        return verifyTime.before(syncVerifyTime);
    }
    public OrderReceiptDto getOrderReceipt(String orderId) {
        TechnicianResp<OrderReceiptDto> orderResponse = orderReceiptQueryService.queryOrderReceipt(orderId, OrderTypeEnum.TRADE_ORDER.getId());
        return Optional.ofNullable(orderResponse)
                .filter(TechnicianResp::respSuccess)
                .map(TechnicianResp::getData)
                .orElse(null);
    }
    public RebateActivityRuleDTO getRebateActivityRule(Long applyRecordId) {
        if (applyRecordId == null || applyRecordId <= 0) {
            return null;
        }
        CommonResponse<RebateActivityRuleDTO> response = distributorActivityService.queryActivityRule(applyRecordId);
        return Optional.ofNullable(response).map(CommonResponse::getData).orElse(null);
    }
    public RebateAmountResult handleRebateRuleLimit(CommissionVerifyCalculationRequest request, RebateAmountResult rebateAmountResult) {
        try {
            if (rebateAmountResult == null) {
                return null;
            }
            RebateActivityRuleDTO rule = getRebateActivityRule(request.getTechApplyRecordId());
            if (rule == null || rule.getRule() == null || rule.getRule().getRebateSettleLimitRule() == null) {
                log.info("handleRebateRuleLimit have no limit rule, request = {}.", request);
                return rebateAmountResult;
            }
            RebateLimitResult result = getRebateLimitResult(request, rebateAmountResult, rule);
            log.info("handleRebateRuleLimit result = {}, order = {}.", result, rebateAmountResult);
            if (result.isResult()) {
                rebateAmountResult.setRebateAmount(result.getRebateAmount());
                rebateAmountResult.setReason(result.getReason());
            }
            return rebateAmountResult;
        } catch (Exception e) {
            log.error("handleRebateRuleLimit request = {}, rebateAmountResult = {}.", request, rebateAmountResult, e);
            return rebateAmountResult;
        }
    }
    public RebateLimitResult getRebateLimitResult(CommissionVerifyCalculationRequest request,
                                                   RebateAmountResult rebateAmountResult, RebateActivityRuleDTO rule) {
        RebateLimitResult result = new RebateLimitResult();
        if (CollectionUtils.isEmpty(limitInterfaceList)) {
            return result;
        }
        List<RebateLimitResult> rebateLimitResultList = new ArrayList<>();
        for (RebateRuleLimitInterface ruleLimitInterface : limitInterfaceList) {
            RebateLimitResult handleResult = ruleLimitInterface.handleResult(request, rebateAmountResult, rule);
            log.info("getRebateLimitResult request = {}, handleResult = {}.", request, handleResult);
            rebateLimitResultList.add(handleResult);
        }
        return rebateLimitResultList.stream().filter(RebateLimitResult::isResult)
                .min(Comparator.comparing(RebateLimitResult::getRebateAmount)).orElse(result);
    }

}

