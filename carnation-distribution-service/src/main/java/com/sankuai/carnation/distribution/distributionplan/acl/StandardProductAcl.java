package com.sankuai.carnation.distribution.distributionplan.acl;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface StandardProductAcl {

    /**
     * 校验spu是否有效
     * 有效的判断逻辑是：
     * 1.spu本身有效
     * 2.spu存在有效的商家商品id
     * @param spuId
     * @return
     */
    boolean checkSpuValid(Long spuId);

    /**
     * 基于平台商品id查询标品id
     * @param platformProductIds
     * @return
     */
    Map<Long, Long> loadPlatformProductId2SpuIdMap (List<Long> platformProductIds);
}
