package com.sankuai.carnation.distribution.commisson.repository;

import com.dianping.gmkt.event.datapools.api.utils.DateUtils;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryDTO;
import com.sankuai.carnation.distribution.commission.dto.rebatequery.RebateQueryRequest;
import com.sankuai.carnation.distribution.commission.settle.enums.OrderVerifyRebateSourceEnum;
import com.sankuai.carnation.distribution.commisson.repository.dao.CommRebateAclAnnualMapper;
import com.sankuai.carnation.distribution.commisson.repository.db.CommRebateAclAnnual;
import com.sankuai.carnation.distribution.commisson.repository.example.CommRebateAclAnnualExample;
import com.sankuai.carnation.distribution.commisson.settle.platform.bo.OrderVerifySettleCommissionBO;
import com.sankuai.carnation.distribution.commisson.settle.rule.ShopCommissionRuleDomainService;
import com.sankuai.carnation.distribution.commisson.settle.rule.bo.ShopPredictEffectCommissionRuleBO;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.ShopAcl;
import com.sankuai.carnation.distribution.intention.domain.calculate.bo.OrderInfoBO;
import com.sankuai.carnation.distribution.intention.domain.calculate.handler.order.OrderInfoAdaptor;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.ProductDetailAclService;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.bo.ProductDetailBO;
import com.sankuai.carnation.distribution.product.bargain.medical.acl.product.request.ProductDetailRequest;
import com.sankuai.carnation.distribution.promocode.rebate.bo.RbOrderVerifyRebateCommissionBO;
import com.sankuai.carnation.distribution.promocode.rebate.bo.TechRbOrderVerifyRebateExtInfoBO;
import com.sankuai.carnation.distribution.promocode.rebate.enums.PromoCodeRebateBizTypeEnum;
import com.sankuai.carnation.distribution.promocode.rebate.repository.RbOrderVerifyRebateCommissionDataService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;

@Slf4j
@Service
public class QueryRebateForOrderOrShopIdService {

    @Autowired
    private RbOrderVerifyRebateCommissionDataService rebateCommissionDataService;

    @Autowired
    private OrderInfoAdaptor orderInfoAdaptor;

    @Autowired
    private ProductDetailAclService productDetailAclService;

    @Autowired
    private ShopAcl shopAcl;

    @Autowired
    private ShopCategoryInfoService shopCategoryInfoService;

    @Autowired
    private CommRebateAclAnnualMapper mapper;

    @Autowired
    private OrderVerifyCommissionDataService orderVerifyCommissionDataService;

    @Autowired
    private ShopCommissionRuleDomainService shopCommissionRuleDomainService;

    public List<RebateQueryDTO> queryRebateForOrder(RebateQueryRequest request) {
        if (request == null || request.getOrderId() == null) {
            return new ArrayList<>();
        }
        RebateQueryDTO rebateQuery = new RebateQueryDTO();
        RbOrderVerifyRebateCommissionBO rebateCommissionBO = rebateCommissionDataService.queryByOrderId(
                PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode(), request.getOrderId(),
                TechRbOrderVerifyRebateExtInfoBO.class);
        if (rebateCommissionBO == null) {
            return new ArrayList<>();
        }
        // 判断是否为年框商户
        boolean isYearFrame = false;
        TechRbOrderVerifyRebateExtInfoBO extInfo = null;
        if (rebateCommissionBO.getExtInfo() != null) {
            extInfo = (TechRbOrderVerifyRebateExtInfoBO)(rebateCommissionBO.getExtInfo());
            if (extInfo.getRateFilter() != null && extInfo.getRateFilter().getFrameRule() != null) {
                isYearFrame = true;
            }
        }
        // rebateQuery.setDpShopId(request.getDpShopId());
        Long dpShopId = null;
        rebateQuery.setOrderId(rebateCommissionBO.getOrderId());
        OrderInfoBO order = orderInfoAdaptor.getOrder(rebateCommissionBO.getOrderType(),
                rebateCommissionBO.getOrderId());
        if (order != null) {
            rebateQuery.setGroupOrderId(order.getProductId());
            ProductDetailRequest productDetailRequest = new ProductDetailRequest();
            productDetailRequest.setProductType(order.getProductType());
            productDetailRequest.setProductIdList(Lists.newArrayList(order.getProductId()));
            productDetailRequest.setPlatform(order.getPlatform());
            productDetailRequest.setUserId(order.getUserId());
            Map<Long, ProductDetailBO> productDetail = productDetailAclService.batchGetProduct(productDetailRequest);
            if (MapUtils.isNotEmpty(productDetail) && productDetail.containsKey(order.getProductId())) {
                rebateQuery.setGroupOrderName(productDetail.get(order.getProductId()).getProductName());
            }
        }

        if (isYearFrame) {
            rebateQuery.setShopType("年框商户");
            ShopPredictEffectCommissionRuleBO frameRule = extInfo.getRateFilter().getFrameRule();
            DecimalFormat df = new DecimalFormat("0.00%");
            rebateQuery.setFrameCommissionRate(df.format(frameRule.getFinalCommission()));
            rebateQuery.setCommissionAmount(df.format(frameRule.getFinalCommission()));
            if (order != null) {
                rebateQuery.setFrameValidityPeriod(DateUtils.format(order.getPayTime(), DateUtils.YMD));
            }
            dpShopId = frameRule.getDpShopId();
            String shopCategory = shopCategoryInfoService.getShopCategoryInfo(dpShopId);
            if (StringUtils.isNotBlank(shopCategory)) {
                rebateQuery.setIndustryCategory(shopCategory);
            }
            // 设置年框名称和年框id
            if (frameRule.getRuleSource() != null) {
                if (frameRule.getRuleSource().equals(OrderVerifyRebateSourceEnum.MANUAL_SHOP_COMMISSION.getCode())) {
                    rebateQuery.setFrameName("一客一案");
                } else if (frameRule.getRuleSource().equals(OrderVerifyRebateSourceEnum.ANNUAL_FRAME.getCode())) {
                    if (CollectionUtils.isNotEmpty(frameRule.getSyncRuleIdList())) {
                        CommRebateAclAnnual annualFrame = getAnnualFrame(frameRule.getSyncRuleIdList().get(0));
                        if (annualFrame != null) {
                            rebateQuery.setFrameId(annualFrame.getActivityId());
                            rebateQuery.setFrameName(annualFrame.getGroupName());
                        }
                    }
                }
            }
        } else {
            rebateQuery.setShopType("非年框商户");
            List<OrderVerifySettleCommissionBO> orderVerifyCommission = orderVerifyCommissionDataService.getOrderVerifyCommission(rebateCommissionBO.getOrderId());
            if (CollectionUtils.isNotEmpty(orderVerifyCommission)) {
                dpShopId = orderVerifyCommission.get(0).getDpShopId();
                DecimalFormat df = new DecimalFormat("0.00%");
                rebateQuery.setCommissionAmount(df.format(orderVerifyCommission.get(0).getCommissionRate()));
            }
        }
        rebateQuery.setDpShopId(dpShopId);
        List<DpPoiDTO> dpPoiDTOList = shopAcl
                .getDpPoiDTOList(dpShopId == null ? Collections.emptyList() : new ArrayList<>(Arrays.asList(dpShopId)));
        if (CollectionUtils.isNotEmpty(dpPoiDTOList)) {
            rebateQuery.setShopName(dpPoiDTOList.get(0).getShopName());
        }
        return new ArrayList<>(Arrays.asList(rebateQuery));
    }

    public List<RebateQueryDTO> queryRebateForShopId(RebateQueryRequest request) {
        if (request == null || request.getDpShopId() == null || request.getStartTime() == null
                || request.getEndTime() == null) {
            return new ArrayList<>();
        }
        List<ShopPredictEffectCommissionRuleBO> lowestCommissionRuleList = shopCommissionRuleDomainService
                .getLowestCommissionRuleList(request.getDpShopId(), 0, new Date(request.getStartTime()),
                        new Date(request.getEndTime()),
                        Lists.newArrayList(OrderVerifyRebateSourceEnum.ANNUAL_FRAME.getCode(),
                                OrderVerifyRebateSourceEnum.MANUAL_SHOP_COMMISSION.getCode()));
        // 获取门店名称
        List<DpPoiDTO> dpPoiDTOList = shopAcl.getDpPoiDTOList(new ArrayList<>(Arrays.asList(request.getDpShopId())));
        String shopName = CollectionUtils.isNotEmpty(dpPoiDTOList) ? dpPoiDTOList.get(0).getShopName() : null;

        if (CollectionUtils.isEmpty(lowestCommissionRuleList)) {
            // 非年框商户
            RebateQueryDTO rebateQueryDTO = new RebateQueryDTO();
            rebateQueryDTO.setDpShopId(request.getDpShopId());
            rebateQueryDTO.setShopType("非年框商户");
            rebateQueryDTO.setShopName(shopName);
            return new ArrayList<>(Arrays.asList(rebateQueryDTO));
        }
        // 年框商户
        List<RebateQueryDTO> rebateQueryList = new ArrayList<>(lowestCommissionRuleList.size());
        // 行业类目
        String shopCategory = shopCategoryInfoService.getShopCategoryInfo(request.getDpShopId());
        for (ShopPredictEffectCommissionRuleBO ruleBO : lowestCommissionRuleList) {
            RebateQueryDTO rb = new RebateQueryDTO();
            rb.setDpShopId(request.getDpShopId());
            rb.setShopName(shopName);
            rb.setShopType("年框商户");
            rb.setIndustryCategory(shopCategory);
            rb.setFrameId(ruleBO.getActivityId());
            rb.setFrameName(ruleBO.getGroupName());
            if(ruleBO.getPromoOrderPlaceEndTime() != null){
                rb.setFrameValidityPeriod(DateUtils.format(ruleBO.getPromoOrderPlaceEndTime(), DateUtils.YMD));
            }
            DecimalFormat df = new DecimalFormat("0.00%");
            rb.setFrameCommissionRate(df.format(ruleBO.getFinalCommission()));
            rebateQueryList.add(rb);
        }
        return rebateQueryList;
    }

    private CommRebateAclAnnual getAnnualFrame(Long primaryKey) {
        CommRebateAclAnnualExample example = new CommRebateAclAnnualExample();
        CommRebateAclAnnualExample.Criteria criteria = example.createCriteria().andIdEqualTo(primaryKey);
        return mapper.selectByPrimaryKey(primaryKey);
    }

    public int getProductTypeByOrderId(String orderId) {
        if(StringUtils.isBlank(orderId)){
            return ProductTypeEnum.UNKNOWN.getCode();
        }
        RbOrderVerifyRebateCommissionBO rebateCommissionBO = rebateCommissionDataService.queryByOrderId(
                PromoCodeRebateBizTypeEnum.TECH_REBATE.getCode(), orderId,
                TechRbOrderVerifyRebateExtInfoBO.class);
        if (rebateCommissionBO == null) {
            return ProductTypeEnum.UNKNOWN.getCode();
        }
        OrderInfoBO order = orderInfoAdaptor.getOrder(rebateCommissionBO.getOrderType(),
                rebateCommissionBO.getOrderId());
        return order.getProductType();
    }
}
