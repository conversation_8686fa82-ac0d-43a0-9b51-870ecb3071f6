package com.sankuai.carnation.distribution.distributionplan.enums;

/**
 * <AUTHOR>
 */

public enum DistributionProductReleaseStatusEnum {

    UNKNOW(0, "未知"),
    ONLINE_SUCCEED_AUDIT_SUCCEED(1001, "上架成功且审核通过"),
    ONLINE_SUCCEED_AUDIT_WAITING(1002, "上架成功但待审核"),
    ONLINE_SUCCEED_AUDITING(1003, "上架成功但审核中"),
    ONLINE_SUCCEED_AUDIT_UNAPPROVED(1004, "上架成功但审核不通过"),
    ONLINE_SUCCEED_AUDIT_FAILED(1005, "上架成功但审核异常失败"),
    OFFLINE_SUCCEED(2001, "下架成功"),
    ;
    private int code;

    private String desc;

    DistributionProductReleaseStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isExistCurrentStatus(Integer code) {
        if (code == null) {
            return false;
        }

        for (DistributionProductReleaseStatusEnum pushResult : DistributionProductReleaseStatusEnum.values()) {
            if (code.equals(pushResult.code)) {
                return true;
            }
        }

        return false;
    }

    public static DistributionProductReleaseStatusEnum from(Integer code) {
        for (DistributionProductReleaseStatusEnum status : DistributionProductReleaseStatusEnum.values()) {
            if (code.equals(status.code)) {
                return status;
            }
        }

        return UNKNOW;
    }
}
