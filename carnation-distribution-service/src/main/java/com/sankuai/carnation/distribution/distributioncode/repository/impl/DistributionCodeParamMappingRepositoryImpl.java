package com.sankuai.carnation.distribution.distributioncode.repository.impl;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.distributioncode.domain.model.DistributionCodeParamMappingDO;
import com.sankuai.carnation.distribution.distributioncode.repository.DistributionCodeParamMappingRepository;
import com.sankuai.carnation.distribution.distributioncode.repository.dao.DistributionCodeParamMappingMapper;
import com.sankuai.carnation.distribution.distributioncode.repository.entity.DistributionCodeParamMappingEntity;
import com.sankuai.carnation.distribution.intention.enums.DistributionBusinessChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class DistributionCodeParamMappingRepositoryImpl implements DistributionCodeParamMappingRepository {

    @Resource
    private DistributionCodeParamMappingMapper distributionCodeParamMappingMapper;

    @Override
    public boolean addDistributionCodeParamMapping(DistributionCodeParamMappingDO distributionCodeParamMappingDo) {
        DistributionCodeParamMappingEntity entity = convertDoToEntity(distributionCodeParamMappingDo);
        int insertResult = distributionCodeParamMappingMapper.insertDistributionCodeParamMapping(entity);
        if (insertResult <= 0) {
            throw new RuntimeException("新增分销码参数映射失败");
        }
        return true;
    }

    @Override
    public Optional<DistributionCodeParamMappingDO> loadDistributionCodeByBizParams(DistributionCodeParamMappingDO distributionCodeParamMappingDo) {
        DistributionCodeParamMappingEntity entity = convertDoToEntity(distributionCodeParamMappingDo);
        List<DistributionCodeParamMappingEntity> entities = distributionCodeParamMappingMapper.queryDistributionCodeByChannelAndBizParams(entity);
        if (CollectionUtils.isNotEmpty(entities)) {
            return Optional.of(convertEntityToDo(entities.get(0)));
        }
        return Optional.empty();
    }

    private DistributionCodeParamMappingEntity convertDoToEntity(DistributionCodeParamMappingDO distributionCodeParamMappingDo) {
        DistributionCodeParamMappingEntity entity = new DistributionCodeParamMappingEntity();
        entity.setDistributionCode(distributionCodeParamMappingDo.getDistributionCode());
        entity.setChannel(distributionCodeParamMappingDo.getChannel().getCode());
        entity.setBizParam1(distributionCodeParamMappingDo.getBizParam1());
        entity.setBizParam2(distributionCodeParamMappingDo.getBizParam2());
        entity.setBizParam3(distributionCodeParamMappingDo.getBizParam3());
        entity.setBizParam4(distributionCodeParamMappingDo.getBizParam4());
        return entity;
    }

    @Override
    public DistributionCodeParamMappingDO loadByChannelAndDistributionCode(DistributionBusinessChannelEnum channel, String distributionCode) {
        DistributionCodeParamMappingEntity entity = distributionCodeParamMappingMapper.loadByChannelAndDistributionCode(channel.getCode(), distributionCode);
        if (entity == null) {
            return null;
        }
        return convertEntityToDo(entity);
    }

    @Override
    public List<DistributionCodeParamMappingDO> queryByChannelAndDistributionCodes(DistributionBusinessChannelEnum channel, List<String> distributionCodes) {
        List<DistributionCodeParamMappingEntity> entities = distributionCodeParamMappingMapper.queryByChannelAndDistributionCodes(channel.getCode(), distributionCodes);
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return entities.stream().map(this::convertEntityToDo).collect(Collectors.toList());
    }

    private DistributionCodeParamMappingDO convertEntityToDo(DistributionCodeParamMappingEntity entity) {
        DistributionCodeParamMappingDO distributionCodeParamMappingDo = new DistributionCodeParamMappingDO();
        distributionCodeParamMappingDo.setMappingId(entity.getId());
        distributionCodeParamMappingDo.setChannel(DistributionBusinessChannelEnum.fromCode(entity.getChannel()));
        distributionCodeParamMappingDo.setDistributionCode(entity.getDistributionCode());
        distributionCodeParamMappingDo.setBizParam1(entity.getBizParam1());
        distributionCodeParamMappingDo.setBizParam2(entity.getBizParam2());
        distributionCodeParamMappingDo.setBizParam3(entity.getBizParam3());
        distributionCodeParamMappingDo.setBizParam4(entity.getBizParam4());

        return distributionCodeParamMappingDo;
    }
}
