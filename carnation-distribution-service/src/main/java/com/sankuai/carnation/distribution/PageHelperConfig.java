package com.sankuai.carnation.distribution;

import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2021/12/31
 **/
@Configuration
public class PageHelperConfig {

    @Bean(name = "zebraPage")
    public Interceptor page(){
        Properties props = new Properties();
        props.put("dialectClass", "com.dianping.zebra.dao.dialect.MySQLDialect");
        //如果是 ShardDataSource，且数据库是 mysql 时需要增加如下代码，否则可能会有空指针异常
        //props.put("helperDialect", "mysql");
        PageInterceptor interceptor = new PageInterceptor();
        interceptor.setProperties(props);
        return interceptor;
    }
}
