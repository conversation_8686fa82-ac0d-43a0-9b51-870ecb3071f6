package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.EasyExcel;
import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionPointRowStrBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/8/22
 * @Description:
 */
@Component
@Slf4j
public class GroundPromotionPointExcelUtils {

    public List<GroundPromotionPointRowStrBO> getExcelDataByParseFile(String url, int headRowNumber) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionPointExcelUtils.getExcelDataByParseFile(java.lang.String,int)");
        List<GroundPromotionPointRowStrBO> excelDataList = parseFile(url, headRowNumber);
        return excelDataList;
    }

    private List<GroundPromotionPointRowStrBO> parseFile(String url, int headRowNumber) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionPointExcelUtils.parseFile(java.lang.String,int)");
        try {
            HttpsURLConnection conn = (HttpsURLConnection) new URL(url).openConnection();
            InputStream inputStream = conn.getInputStream();

            GroundPromotionPointRowListener groundPromotionPointRowListener = new GroundPromotionPointRowListener();
            EasyExcel.read(inputStream, GroundPromotionPointRowStrBO.class, groundPromotionPointRowListener).sheet(0).headRowNumber(headRowNumber).doRead();
            return groundPromotionPointRowListener.getCachedDataList();
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".parseFile error, exception is", e);
            return Lists.newArrayList();
        }
    }

    public String generateFile(String fileName, List<GroundPromotionPointRowStrBO> groundPromotionPointRowStrBOList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionPointExcelUtils.generateFile(java.lang.String,java.util.List)");
        try {
            Map<String, List<List<String>>> sheetData = Maps.newHashMap();
            List<List<String>> rowData = Lists.newArrayList();
            rowData.add(Lists.newArrayList("*点评城市id",
                    "*地推点位名称",
                    "门店ID",
                    "*落地页展示方式",
                    "销售Mis号",
                    "经纬度",
                    "POIID",
                    "导入结果",
                    "失败原因"));
            for (GroundPromotionPointRowStrBO pointRowStrBO : groundPromotionPointRowStrBOList) {
                List<String> cellData = Lists.newArrayList();
                cellData.add(pointRowStrBO.getDpCityId());
                cellData.add(pointRowStrBO.getPointName());
                cellData.add(pointRowStrBO.getDpShopIdList());
                cellData.add(pointRowStrBO.getShowType());
                cellData.add(pointRowStrBO.getMisIdList());
                cellData.add(pointRowStrBO.getLngAndLat());
                cellData.add("");
                cellData.add(pointRowStrBO.getLoadResult());
                cellData.add(pointRowStrBO.getFailReason());
                rowData.add(cellData);
            }
            sheetData.put("result", rowData);
            FileBody fileBody = FileBodyBuilder.buildExcelFileBody(fileName, sheetData);
            return fileBody.getUrl();
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".generateFile Error", e);
            Cat.logEvent(getClass().getSimpleName() + ".generateFile Error", e.getMessage());
            return "";
        }
    }
}
