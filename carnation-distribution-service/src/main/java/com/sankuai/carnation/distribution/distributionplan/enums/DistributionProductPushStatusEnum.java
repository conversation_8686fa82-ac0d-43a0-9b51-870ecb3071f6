package com.sankuai.carnation.distribution.distributionplan.enums;

/**
 * <AUTHOR>
 */

public enum DistributionProductPushStatusEnum {
    VALIDATE_FAILED(-1, "前置校验失败"),
    INITIALIZE(0, "初始化"),
    PUSH_ONLINE_SUCCEED(1, "推上线成功"),
    PUSH_ONLINE_FAILED(2, "推上线失败"),
    PUSH_OFFLINE_SUCCEED(3, "推下线成功"),
    PUSH_OFFLINE_FAILED(4, "推下线失败"),
    ;
    private int code;

    private String desc;

    DistributionProductPushStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isExistCurrentStatus(Integer code) {
        if (code == null) {
            return false;
        }

        for (DistributionProductPushStatusEnum pushStatus : DistributionProductPushStatusEnum.values()) {
            if (code.equals(pushStatus.code)) {
                return true;
            }
        }

        return false;
    }

    public static DistributionProductPushStatusEnum from(Integer code) {
        for (DistributionProductPushStatusEnum status : DistributionProductPushStatusEnum.values()) {
            if (code.equals(status.code)) {
                return status;
            }
        }

        return null;
    }
}
