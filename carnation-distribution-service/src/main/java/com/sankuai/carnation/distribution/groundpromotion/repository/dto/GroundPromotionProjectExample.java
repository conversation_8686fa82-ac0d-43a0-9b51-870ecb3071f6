package com.sankuai.carnation.distribution.groundpromotion.repository.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GroundPromotionProjectExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public GroundPromotionProjectExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public GroundPromotionProjectExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public GroundPromotionProjectExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public GroundPromotionProjectExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNull() {
            addCriterion("city_id is null");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNotNull() {
            addCriterion("city_id is not null");
            return (Criteria) this;
        }

        public Criteria andCityIdEqualTo(Integer value) {
            addCriterion("city_id =", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotEqualTo(Integer value) {
            addCriterion("city_id <>", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThan(Integer value) {
            addCriterion("city_id >", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("city_id >=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThan(Integer value) {
            addCriterion("city_id <", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThanOrEqualTo(Integer value) {
            addCriterion("city_id <=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdIn(List<Integer> values) {
            addCriterion("city_id in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotIn(List<Integer> values) {
            addCriterion("city_id not in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdBetween(Integer value1, Integer value2) {
            addCriterion("city_id between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotBetween(Integer value1, Integer value2) {
            addCriterion("city_id not between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andScopeTypeIsNull() {
            addCriterion("scope_type is null");
            return (Criteria) this;
        }

        public Criteria andScopeTypeIsNotNull() {
            addCriterion("scope_type is not null");
            return (Criteria) this;
        }

        public Criteria andScopeTypeEqualTo(Integer value) {
            addCriterion("scope_type =", value, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeNotEqualTo(Integer value) {
            addCriterion("scope_type <>", value, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeGreaterThan(Integer value) {
            addCriterion("scope_type >", value, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("scope_type >=", value, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeLessThan(Integer value) {
            addCriterion("scope_type <", value, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("scope_type <=", value, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeIn(List<Integer> values) {
            addCriterion("scope_type in", values, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeNotIn(List<Integer> values) {
            addCriterion("scope_type not in", values, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeBetween(Integer value1, Integer value2) {
            addCriterion("scope_type between", value1, value2, "scopeType");
            return (Criteria) this;
        }

        public Criteria andScopeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("scope_type not between", value1, value2, "scopeType");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNull() {
            addCriterion("creator_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNotNull() {
            addCriterion("creator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisEqualTo(String value) {
            addCriterion("creator_mis =", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotEqualTo(String value) {
            addCriterion("creator_mis <>", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThan(String value) {
            addCriterion("creator_mis >", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("creator_mis >=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThan(String value) {
            addCriterion("creator_mis <", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThanOrEqualTo(String value) {
            addCriterion("creator_mis <=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLike(String value) {
            addCriterion("creator_mis like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotLike(String value) {
            addCriterion("creator_mis not like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIn(List<String> values) {
            addCriterion("creator_mis in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotIn(List<String> values) {
            addCriterion("creator_mis not in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisBetween(String value1, String value2) {
            addCriterion("creator_mis between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotBetween(String value1, String value2) {
            addCriterion("creator_mis not between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoIsNull() {
            addCriterion("arrangement_info is null");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoIsNotNull() {
            addCriterion("arrangement_info is not null");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoEqualTo(String value) {
            addCriterion("arrangement_info =", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoNotEqualTo(String value) {
            addCriterion("arrangement_info <>", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoGreaterThan(String value) {
            addCriterion("arrangement_info >", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoGreaterThanOrEqualTo(String value) {
            addCriterion("arrangement_info >=", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoLessThan(String value) {
            addCriterion("arrangement_info <", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoLessThanOrEqualTo(String value) {
            addCriterion("arrangement_info <=", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoLike(String value) {
            addCriterion("arrangement_info like", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoNotLike(String value) {
            addCriterion("arrangement_info not like", value, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoIn(List<String> values) {
            addCriterion("arrangement_info in", values, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoNotIn(List<String> values) {
            addCriterion("arrangement_info not in", values, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoBetween(String value1, String value2) {
            addCriterion("arrangement_info between", value1, value2, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andArrangementInfoNotBetween(String value1, String value2) {
            addCriterion("arrangement_info not between", value1, value2, "arrangementInfo");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdIsNull() {
            addCriterion("audit_node_id is null");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdIsNotNull() {
            addCriterion("audit_node_id is not null");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdEqualTo(Long value) {
            addCriterion("audit_node_id =", value, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdNotEqualTo(Long value) {
            addCriterion("audit_node_id <>", value, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdGreaterThan(Long value) {
            addCriterion("audit_node_id >", value, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("audit_node_id >=", value, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdLessThan(Long value) {
            addCriterion("audit_node_id <", value, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdLessThanOrEqualTo(Long value) {
            addCriterion("audit_node_id <=", value, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdIn(List<Long> values) {
            addCriterion("audit_node_id in", values, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdNotIn(List<Long> values) {
            addCriterion("audit_node_id not in", values, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdBetween(Long value1, Long value2) {
            addCriterion("audit_node_id between", value1, value2, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andAuditNodeIdNotBetween(Long value1, Long value2) {
            addCriterion("audit_node_id not between", value1, value2, "auditNodeId");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNull() {
            addCriterion("bu_id is null");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNotNull() {
            addCriterion("bu_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuIdEqualTo(Integer value) {
            addCriterion("bu_id =", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotEqualTo(Integer value) {
            addCriterion("bu_id <>", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThan(Integer value) {
            addCriterion("bu_id >", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bu_id >=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThan(Integer value) {
            addCriterion("bu_id <", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThanOrEqualTo(Integer value) {
            addCriterion("bu_id <=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdIn(List<Integer> values) {
            addCriterion("bu_id in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotIn(List<Integer> values) {
            addCriterion("bu_id not in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdBetween(Integer value1, Integer value2) {
            addCriterion("bu_id between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bu_id not between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andTerminateMisIsNull() {
            addCriterion("terminate_mis is null");
            return (Criteria) this;
        }

        public Criteria andTerminateMisIsNotNull() {
            addCriterion("terminate_mis is not null");
            return (Criteria) this;
        }

        public Criteria andTerminateMisEqualTo(String value) {
            addCriterion("terminate_mis =", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisNotEqualTo(String value) {
            addCriterion("terminate_mis <>", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisGreaterThan(String value) {
            addCriterion("terminate_mis >", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisGreaterThanOrEqualTo(String value) {
            addCriterion("terminate_mis >=", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisLessThan(String value) {
            addCriterion("terminate_mis <", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisLessThanOrEqualTo(String value) {
            addCriterion("terminate_mis <=", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisLike(String value) {
            addCriterion("terminate_mis like", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisNotLike(String value) {
            addCriterion("terminate_mis not like", value, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisIn(List<String> values) {
            addCriterion("terminate_mis in", values, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisNotIn(List<String> values) {
            addCriterion("terminate_mis not in", values, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisBetween(String value1, String value2) {
            addCriterion("terminate_mis between", value1, value2, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateMisNotBetween(String value1, String value2) {
            addCriterion("terminate_mis not between", value1, value2, "terminateMis");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeIsNull() {
            addCriterion("terminate_time is null");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeIsNotNull() {
            addCriterion("terminate_time is not null");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeEqualTo(Date value) {
            addCriterion("terminate_time =", value, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeNotEqualTo(Date value) {
            addCriterion("terminate_time <>", value, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeGreaterThan(Date value) {
            addCriterion("terminate_time >", value, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("terminate_time >=", value, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeLessThan(Date value) {
            addCriterion("terminate_time <", value, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeLessThanOrEqualTo(Date value) {
            addCriterion("terminate_time <=", value, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeIn(List<Date> values) {
            addCriterion("terminate_time in", values, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeNotIn(List<Date> values) {
            addCriterion("terminate_time not in", values, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeBetween(Date value1, Date value2) {
            addCriterion("terminate_time between", value1, value2, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andTerminateTimeNotBetween(Date value1, Date value2) {
            addCriterion("terminate_time not between", value1, value2, "terminateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}