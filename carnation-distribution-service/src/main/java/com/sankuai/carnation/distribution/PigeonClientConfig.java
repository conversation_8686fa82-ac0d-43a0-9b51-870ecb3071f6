package com.sankuai.carnation.distribution;

import com.dianping.general.category.service.GeneralCategoryService;
import com.dianping.general.unified.search.api.dealgroupsearch.GeneralDealGroupSearchService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.srcm.pchat.service.ScrmLiveManageService;
import com.sankuai.dz.srcm.automatedmanagement.service.ScrmCouponAttributionService;
import com.sankuai.dz.srcm.user.service.PrivateSphereUserPortraitService;
import com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.*;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeForCService;
import com.sankuai.medicalcosmetology.offline.code.api.service.staffbargain.StaffBargainService;
import com.sankuai.technician.trade.api.settle.service.CommissionSettlementService;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PigeonClientConfig {

    @MdpPigeonClient(remoteAppKey = "com.sankuai.medicalcosmetology.scrm.core", timeout = 5000)
    private PrivateSphereUserPortraitService privateSphereUserPortraitService;

    @MdpPigeonClient(remoteAppKey = "com.sankuai.medicalcosmetology.scrm.core", timeout = 2000)
    private ScrmCouponAttributionService scrmCouponAttributionService;
    @MdpPigeonClient(remoteAppKey = "com.sankuai.medicalcosmetology.scrm.core", timeout = 1000)
    private ScrmLiveManageService scrmLiveManageService;


    @MdpPigeonClient(remoteAppKey = "com.sankuai.medicalcosmetology.offlinecode.service", timeout = 1000,url = "com.sankuai.medicalcosmetology.offline.code.service.StaffBargainService")
    private StaffBargainService staffBargainService;

    @MdpPigeonClient(remoteAppKey = "com.sankuai.medicalcosmetology.offlinecode.service", timeout = 1000,url = "com.sankuai.medicalcosmetology.offline.code.service.PromoCodeForCService")
    private PromoCodeForCService promoCodeForCService;

    @MdpPigeonClient(timeout = 2000, url="com.sankuai.technician.trade.api.settle.service.CommissionSettlementService")
    private CommissionSettlementService commissionSettlementService;

    @MdpPigeonClient(timeout = 500, testTimeout = 5000, remoteAppKey = "tuangou-category-service")
    private GeneralCategoryService generalCategoryService;

    @MdpPigeonClient(url = "com.dianping.general.unified.search.api.dealgroupsearch.GeneralDealGroupSearchService", timeout = 3000)
    private GeneralDealGroupSearchService generalDealGroupSearchService;

    @MdpPigeonClient(url = "com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveConsultantAccountService", timeout = 3000)
    private DzPrivateLiveConsultantAccountService dzPrivateLiveConsultantAccountService;

    @MdpPigeonClient(url = "com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveConsultantSummaryService", timeout = 3000)
    private DzPrivateLiveConsultantSummaryService dzPrivateLiveConsultantSummaryService;

    @MdpPigeonClient(url = "com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveConsultantVerifyService", timeout = 3000)
    private DzPrivateLiveConsultantVerifyService dzPrivateLiveConsultantVerifyService;

    @MdpPigeonClient(url = "com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveIntentionExpireService", timeout = 3000)
    private DzPrivateLiveIntentionExpireService dzPrivateLiveIntentionExpireService;

    @MdpPigeonClient(url = "com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveOrderIntentionService", timeout = 3000)
    private DzPrivateLiveOrderIntentionService dzPrivateLiveOrderIntentionService;

    @MdpPigeonClient(url = "com.sankuai.dzusergrowth.growth.privatelive.api.consultant.service.DzPrivateLiveUserIntentionService", timeout = 3000)
    private DzPrivateLiveUserIntentionService dzPrivateLiveUserIntentionService;
}
