package com.sankuai.carnation.distribution.empty.code.bind.service.impl;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.provider.config.annotation.Service;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.empty.code.bind.domain.bean.transfer.EmptyCodeBindInfoTransfer;
import com.sankuai.carnation.distribution.empty.code.bind.repository.service.EmptyCodeBindInfoDataService;
import com.sankuai.carnation.distribution.empty.code.prebind.dto.EmptyCodeBindInfoDTO;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeBindKeyTypeEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeBizTypeEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeOperatorTypeEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeSourceEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.request.EmptyCodeBindRequest;
import com.sankuai.carnation.distribution.empty.code.prebind.service.EmptyCodeBindService;
import com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 功能描述: 空码绑定信息服务
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Service
@Slf4j
public class EmptyCodeBindServiceImpl implements EmptyCodeBindService {

    @Autowired
    private EmptyCodeBindInfoDataService bindInfoDataService;

    @Override
    public RemoteResponse<Boolean> bindEmptyCode(EmptyCodeBindRequest bindRequest) {
        try {
            checkBindRequest(bindRequest);
            EmptyCodeBindInfo bindInfo = bindInfoDataService.getBindInfoByCode(bindRequest.getCodeSource(), bindRequest.getCodeKey(), true);
            if (bindInfo != null) {
                if (!bindInfo.getBizType().equals(bindRequest.getBizType())
                        || !bindInfo.getBizId().equals(bindRequest.getBizId())
                        || !bindInfo.getBindKeyType().equals(bindRequest.getBindKeyType())
                        || !bindInfo.getBindKey().equals(bindRequest.getBindKey())) {
                    return RemoteResponse.fail("码已绑定其它" + EmptyCodeBindKeyTypeEnum.fromCode(bindInfo.getBindKeyType()).getDesc());
                }
                return RemoteResponse.success(true);
            }
            long id = bindInfoDataService.addEmptyCodeBindInfo(bindRequest);
            return id > 0 ? RemoteResponse.success(true) : RemoteResponse.fail("未绑定成功");
        } catch (IllegalArgumentException argEx) {
            log.error("[{}.bindEmptyCode.IllegalArgumentException], request: {}", getClass().getSimpleName(), bindRequest, argEx);
            return RemoteResponse.fail(argEx.getMessage());
        } catch (Exception e) {
            log.error("[{}.bindEmptyCode], request: {}", getClass().getSimpleName(), bindRequest, e);
            return RemoteResponse.fail("绑定失败");
        }
    }

    @Override
    public RemoteResponse<List<EmptyCodeBindInfoDTO>> getValidBindInfoByBindKey(int bizType, long bizId, int bindKeyType, String bindKey) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.empty.code.bind.service.impl.EmptyCodeBindServiceImpl.getValidBindInfoByBindKey(int,long,int,java.lang.String)");
        try {
            if (EmptyCodeBizTypeEnum.UNKNOWN.equals(EmptyCodeBizTypeEnum.fromCode(bizType))) {
                throw new IllegalArgumentException("业务类型未设置");
            }
            if (EmptyCodeBindKeyTypeEnum.UNKNOWN.equals(EmptyCodeBindKeyTypeEnum.fromCode(bindKeyType))) {
                throw new IllegalArgumentException("数据类型未设置");
            }
            if (StringUtils.isBlank(bindKey)) {
                throw new IllegalArgumentException("数据主键未设置");
            }
            List<EmptyCodeBindInfo> list = bindInfoDataService.getValidBindInfoByBindKey(bizType, bizId, bindKeyType, bindKey);
            return RemoteResponse.success(EmptyCodeBindInfoTransfer.poList2dtoList(list));
        } catch (IllegalArgumentException argEx) {
            log.error("[{}.getValidBindInfoByBindKey.IllegalArgumentException], bizType: {}, bizId: {}, bindKeyType: {}, bindKey: {}", getClass().getSimpleName(), bizType, bizId, bindKeyType, bindKey, argEx);
            return RemoteResponse.fail(argEx.getMessage());
        } catch (Exception e) {
            log.error("[{}.getValidBindInfoByBindKey], bizType: {}, bizId: {}, bindKeyType: {}, bindKey: {}", getClass().getSimpleName(), bizType, bizId, bindKeyType, bindKey, e);
            return RemoteResponse.fail("查询失败");
        }
    }

    @Override
    public RemoteResponse<Map<String, List<EmptyCodeBindInfoDTO>>> batchGetValidBindInfoByBindKey(int bizType, long bizId, int bindKeyType, List<String> bindKeyList) {
        try {
            if (EmptyCodeBizTypeEnum.UNKNOWN.equals(EmptyCodeBizTypeEnum.fromCode(bizType))) {
                throw new IllegalArgumentException("业务类型未设置");
            }
            if (EmptyCodeBindKeyTypeEnum.UNKNOWN.equals(EmptyCodeBindKeyTypeEnum.fromCode(bindKeyType))) {
                throw new IllegalArgumentException("数据类型未设置");
            }
            if (CollectionUtils.isEmpty(bindKeyList)) {
                return RemoteResponse.success(Maps.newHashMap());
            }
            if (bindKeyList.size() > 100) {
                throw new IllegalArgumentException("单次请求不能超过100条");
            }
            Map<String, List<EmptyCodeBindInfo>> poMap = bindInfoDataService.batchGetValidBindInfoByBindKey(bizType, bizId, bindKeyType, bindKeyList, false);
            Map<String, List<EmptyCodeBindInfoDTO>> dtoMap = poMap.entrySet().stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> EmptyCodeBindInfoTransfer.poList2dtoList(entry.getValue())));
            return RemoteResponse.success(dtoMap);
        } catch (IllegalArgumentException argEx) {
            log.error("[{}.batchGetValidBindInfoByBindKey.IllegalArgumentException], bizType: {}, bizId: {}, bindKeyType: {}, bindKey: {}", getClass().getSimpleName(), bizType, bizId, bindKeyType, bindKeyList, argEx);
            return RemoteResponse.fail(argEx.getMessage());
        } catch (Exception e) {
            log.error("[{}.batchGetValidBindInfoByBindKey], bizType: {}, bizId: {}, bindKeyType: {}, bindKey: {}", getClass().getSimpleName(), bizType, bizId, bindKeyType, bindKeyList, e);
            return RemoteResponse.fail("查询失败");
        }
    }

    @Override
    public RemoteResponse<EmptyCodeBindInfoDTO> loadByCodeSourceAndKey(int codeSource, String codeKey) {
        if (EmptyCodeSourceEnum.UNKNOWN.equals(EmptyCodeSourceEnum.fromCode(codeSource))) {
            return RemoteResponse.fail("码来源无法识别");
        }
        if (StringUtils.isBlank(codeKey)) {
            return RemoteResponse.fail("码唯一键未设置");
        }
        try {
            EmptyCodeBindInfo bindInfo = bindInfoDataService.getBindInfoByCode(codeSource, codeKey);
            return RemoteResponse.success(EmptyCodeBindInfoTransfer.po2dto(bindInfo));
        } catch (Exception e) {
            log.error("[{}.loadByCodeSourceAndKey], codeSource: {}, codeKey: {}", getClass().getSimpleName(), codeSource, codeKey, e);
            return RemoteResponse.fail("查询码绑定信息失败");
        }
    }

    @Override
    public RemoteResponse<Boolean> updateBindEmptyCode(EmptyCodeBindRequest bindRequest) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_2", "com.sankuai.carnation.distribution.empty.code.bind.service.impl.EmptyCodeBindServiceImpl.updateBindEmptyCode(com.sankuai.carnation.distribution.empty.code.prebind.request.EmptyCodeBindRequest)");
        bindInfoDataService.updateEmptyCodeBindInfo(bindRequest);
        return RemoteResponse.success(true);
    }

    @Override
    public RemoteResponse<String> loadImageUrl(int bizType, long bizId, int bindKeyType, String bindKey) {
        com.meituan.mdp.pamela.sdk.PamelaReporter.report("INVALID_METHOD_3", "com.sankuai.carnation.distribution.empty.code.bind.service.impl.EmptyCodeBindServiceImpl.loadImageUrl(int,long,int,java.lang.String)");
        List<EmptyCodeBindInfo> emptyCodeBindInfos = bindInfoDataService.batchGetValidBindInfoByBindKey(bizType, bizId, bindKeyType, Lists.newArrayList(bindKey), true).getOrDefault(bindKey, Lists.newArrayList());
        String codeImage = emptyCodeBindInfos.stream().map(EmptyCodeBindInfo::getCodeImage).findFirst().orElse(null);
        return RemoteResponse.success(codeImage);
    }

    @Override
    public RemoteResponse<EmptyCodeBindInfoDTO> queryOrCreateValidMedicalPromoCodeByDpShopId(Long dpShopId) {
        if (Objects.isNull(dpShopId) || dpShopId <= 0) {
            throw new IllegalArgumentException("门店ID不能为空");
        }

        try {
            EmptyCodeBindInfo bindInfo = bindInfoDataService.queryOrCreateValidMedicalPromoCodeByDpShopId(dpShopId);
            return RemoteResponse.success(EmptyCodeBindInfoTransfer.po2dto(bindInfo));
        } catch (Exception e) {
            log.error("[{}.queryOrCreateValidMedicalPromoCodeByDpShopId], dpShopId: {}", getClass().getSimpleName(), dpShopId, e);
            return RemoteResponse.fail(String.format("查询码绑定信息失败，原因：%s", e.getMessage()));
        }
    }

    private void checkBindRequest(EmptyCodeBindRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("添加绑定关系数据不能为空");
        }
        if (request.getBizType() == null || EmptyCodeBizTypeEnum.UNKNOWN.equals(EmptyCodeBizTypeEnum.fromCode(request.getBizType()))) {
            throw new IllegalArgumentException("绑定业务类型未设置");
        }
        if (request.getBizId() == null) {
            throw new IllegalArgumentException("绑定业务id未设置");
        }
        if (request.getCodeSource() == null || EmptyCodeSourceEnum.UNKNOWN.equals(EmptyCodeSourceEnum.fromCode(request.getCodeSource()))) {
            throw new IllegalArgumentException("码无法识别");
        }
        if (StringUtils.isBlank(request.getCodeKey())) {
            throw new IllegalArgumentException("码唯一键未设置");
        }
        if (request.getBindKeyType() == null || EmptyCodeBindKeyTypeEnum.UNKNOWN.equals(EmptyCodeBindKeyTypeEnum.fromCode(request.getBindKeyType()))) {
            throw new IllegalArgumentException("绑定数据类型未设置");
        }
        if (StringUtils.isBlank(request.getBindKey())) {
            throw new IllegalArgumentException("绑定数据主键未设置");
        }
        if (request.getOperatorType() == null || EmptyCodeOperatorTypeEnum.UNKNOWN.equals(EmptyCodeOperatorTypeEnum.fromCode(request.getOperatorType()))) {
            throw new IllegalArgumentException("绑码操作人类型未设置");
        }
        if (StringUtils.isBlank(request.getOperatorId())) {
            throw new IllegalArgumentException("绑码操作人未设置");
        }
    }
}
