package com.sankuai.carnation.distribution.distributionplan.repository;

import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPush;
import com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPushQueryRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionProductPushRepository {

    /**
     * 批量添加分销商品推送
     * @param pushDistributionProductList
     * @return 新增的分销商品数量
     */
    int batchAddDistributionProductPush(List<DistributionProductPush> pushDistributionProductList);

    /**
     * 查询分销商品
     * @param request
     * @return
     */
    List<DistributionProductPush> queryDistributionProductPush(DistributionProductPushQueryRequest request);

    /**
     * 更新商品推送状态
     * @param pushableDistributionProductList
     * @return
     */
    boolean batchUpdateDistributionProductPushStatus(List<DistributionProductPush> pushableDistributionProductList);
}