package com.sankuai.carnation.distribution.distributionplan.acl.model;

import com.sankuai.carnation.distribution.distributionplan.enums.CommissionBasisTypeEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/9/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProductCommissionBasisInfo {

    /**
     * 商品ID
     */
    private Long dpProductId;

    /**
     * 商促是否收佣 1-收佣 2-不收佣
     * 收佣表示商品的计费基数是：商品原价
     * 不收佣表示商品的计费基数是：商品原价-商家优惠
     * @see com.sankuai.carnation.distribution.distributionplan.enums.CommissionBasisTypeEnum
     */
    private CommissionBasisTypeEnum commissionBasisType;
}