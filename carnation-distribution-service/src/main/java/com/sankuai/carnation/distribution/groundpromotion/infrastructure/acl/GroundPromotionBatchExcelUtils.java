package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.excel.EasyExcel;
import com.dianping.beauty.ibot.dto.FileBody;
import com.dianping.beauty.ibot.tools.FileBodyBuilder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.carnation.distribution.groundpromotion.repository.bo.GroundPromotionBatchRowStrBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: chenhaoyang02
 * @date: 2023/7/24
 */
@Component
@Slf4j
public class GroundPromotionBatchExcelUtils {

    public List<GroundPromotionBatchRowStrBo> getExcelDataByParseFile(String url) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionBatchExcelUtils.getExcelDataByParseFile(java.lang.String)");
        List<GroundPromotionBatchRowStrBo> excelDataList = parseFile(url);
        return excelDataList;
    }

    private List<GroundPromotionBatchRowStrBo> parseFile(String url) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionBatchExcelUtils.parseFile(java.lang.String)");
        try {
            HttpsURLConnection conn = (HttpsURLConnection) new URL(url).openConnection();
            InputStream inputStream = conn.getInputStream();

            GroundPromotionBatchRowListener groundPromotionBatchRowListener = new GroundPromotionBatchRowListener();
            EasyExcel.read(inputStream, GroundPromotionBatchRowStrBo.class, groundPromotionBatchRowListener).sheet(0).headRowNumber(2).doRead();
            return groundPromotionBatchRowListener.getCachedDataList();
        } catch (Exception e) {
            log.error("[parseFile] error, exception is", e);
            return Lists.newArrayList();
        }
    }

    public String generateFile(String fileName, List<GroundPromotionBatchRowStrBo> groundPromotionBatchRowStrBoList) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.GroundPromotionBatchExcelUtils.generateFile(java.lang.String,java.util.List)");
        try {
            Map<String, List<List<String>>> sheetData = Maps.newHashMap();
            List<List<String>> rowData = Lists.newArrayList();
            rowData.add(Lists.newArrayList("*点评城市id",
                    "*门店Shopid",
                    "*团单点评ID",
                    "销售Mis号",
                    "导入结果",
                    "失败原因"));
            for (GroundPromotionBatchRowStrBo batchRowStrBo : groundPromotionBatchRowStrBoList) {
                List<String> cellData = Lists.newArrayList();
                cellData.add(batchRowStrBo.getDpCityId());
                cellData.add(batchRowStrBo.getDpShopId());
                cellData.add(batchRowStrBo.getDpDealId());
                cellData.add(batchRowStrBo.getMisIdList());
                cellData.add(batchRowStrBo.getLoadResult());
                cellData.add(batchRowStrBo.getFailReason());
                rowData.add(cellData);
            }
            sheetData.put("result", rowData);
            FileBody fileBody = FileBodyBuilder.buildExcelFileBody(fileName, sheetData);
            return fileBody.getUrl();
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".generateFile Error", e);
            Cat.logEvent(getClass().getSimpleName() + ".generateFile Error", e.getMessage());
            return "";
        }
    }
}
