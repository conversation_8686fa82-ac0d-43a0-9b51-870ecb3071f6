package com.sankuai.carnation.distribution;

import com.dianping.zsts.commission.unify.api.UnifiedCommissionQueryService;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.conch.certify.tokenaccess.thrift.TokenAccessThriftService;
import com.sankuai.nibmp.infra.amp.authorize.lib.service.AccountPoiSearchService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: GuangYuJie
 * @Date: 2021/12/27 4:15 下午
 */
@Configuration
public class ThriftClientConfig {

    @MdpThriftClient(remoteAppKey = "com.sankuai.xm.pubapi", timeout = 5000)
    private PushMessageServiceI.Iface pushMessageService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.conch.certify.tokenaccess", timeout = 1000)
    private TokenAccessThriftService.Iface tokenAccessService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.wpt.user.retrieve", timeout = 5000)
    private RpcUserRetrieveService.Iface userRetrieveService;

    @MdpThriftClient(remoteAppKey = "zsts-commission-service", remoteServerPort = 8090, timeout = 3000)
    private UnifiedCommissionQueryService unifiedCommissionQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.nibmerchant.amp.authorize", remoteServerPort = 9000, timeout = 5000)
    private AccountPoiSearchService accountPoiSearchService;
}
