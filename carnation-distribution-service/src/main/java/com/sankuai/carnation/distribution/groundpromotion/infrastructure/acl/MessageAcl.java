package com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.sankuai.carnation.distribution.xm.dto.BaseXMMessageDTO;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PushType;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import com.sankuai.xm.udb.thrift.AppTenantEnum;
import com.sankuai.xm.udb.thrift.UdbOpenThriftBeans;
import com.sankuai.xm.udb.thrift.UdbOpenThriftClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: wuweizhen
 * @Date: 2023/6/5 10:17
 * @Description:
 */
@Component
@Slf4j
public class MessageAcl {

    private final static String XM_KEY = Environment.isTestEnv() ?
            "2022221142801115" : "5921202315sv8602";

    private final static String XM_TOKEN = Environment.isTestEnv() ?
            "79b4e9b214de27c4bd83c84a84eba1ef" : "056768460bc434aad71ac2f26b8f1411";

    private final static long XM_PUB_ID = Environment.isTestEnv() ?
            137439122875L : 137934265628L;

    @Autowired
    private PushMessageServiceI.Iface pushMessageService;

    public static final UdbOpenThriftClient thriftClient = UdbOpenThriftBeans.get(UdbOpenThriftClient.class);

    /**
     * 消息格式见https://km.sankuai.com/page/109202211#id-11.%E5%9B%BE%E6%96%87%E6%B6%88%E6%81%AF(link)*
     *
     * @param messageDTO
     * @param misIds
     */
    public void pushMessage(BaseXMMessageDTO messageDTO, List<String> misIds) {
        try {
            PusherInfo pusherInfo = new PusherInfo();
            pusherInfo.setAppkey(XM_KEY);
            pusherInfo.setToken(XM_TOKEN);
            pusherInfo.setFromUid(XM_PUB_ID);
            pusherInfo.setAppId((short) 1);
            pusherInfo.setChannelId((short) 0);
            pusherInfo.setPushType(PushType.PUSH_ONLINE);
            Map<String, Long> uid = getUUIDs(misIds);
            String result = pushMessageService.pushExtensionMessageWithUids(
                    System.currentTimeMillis(),
                    messageDTO.getType().getXMParam(),
                    messageDTO.toJSONString(),
                    null,
                    new ArrayList<>(uid.values()),
                    pusherInfo
            );
            JSONObject jsonObject = JSON.parseObject(result);
            if (jsonObject.getIntValue("rescode") != 0) {
                throw new RpcException("推送失败,msg:" + jsonObject.getJSONObject("data").getString("message"));
            }
        } catch (Exception e) {
            log.error("XMNotifyUtils.pushMessage", e, messageDTO, misIds);
        }
    }

    private final static AppTenantEnum APP_TENANT_ENUM = Environment.isTestEnv() ?
            AppTenantEnum.APPID_1_SANKUAI : AppTenantEnum.APPID_1_MEITUAN;

    public Map<String, Long> getUUIDs(List<String> misIds) throws Exception {
        return thriftClient.getUids(misIds, APP_TENANT_ENUM);
    }
}