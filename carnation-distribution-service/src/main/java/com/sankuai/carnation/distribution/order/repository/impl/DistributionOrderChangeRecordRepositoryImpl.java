package com.sankuai.carnation.distribution.order.repository.impl;

import com.sankuai.carnation.distribution.order.repository.DistributionOrderChangeRecordRepository;
import com.sankuai.carnation.distribution.order.repository.dao.DistributionOrderChangeRecordMapper;
import com.sankuai.carnation.distribution.order.repository.dao.ExtDistributionOrderChangeRecordMapper;
import com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord;
import com.sankuai.carnation.distribution.order.repository.example.DistributionOrderChangeRecordCriteria;
import com.sankuai.carnation.distribution.order.repository.model.DistributionOrderChangeRecordPageQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
@Slf4j
public class DistributionOrderChangeRecordRepositoryImpl implements DistributionOrderChangeRecordRepository {

    @Autowired
    private DistributionOrderChangeRecordMapper distributionOrderChangeRecordMapper;

    @Resource
    private ExtDistributionOrderChangeRecordMapper extDistributionOrderChangeRecordMapper;

    @Override
    public Boolean saveDistributionOrderChangeRecord(DistributionOrderChangeRecord changeRecord) {
        DistributionOrderChangeRecordCriteria example = new DistributionOrderChangeRecordCriteria();
        example.createCriteria().andOrderIdEqualTo(changeRecord.getOrderId());
        example.createCriteria().andChannelEqualTo(changeRecord.getChannel());
        example.createCriteria().andIsDeletedEqualTo(0L);
        List<DistributionOrderChangeRecord> orderChangeRecords = distributionOrderChangeRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(orderChangeRecords)) {
            return distributionOrderChangeRecordMapper.insert(changeRecord) > 0;
        }
        DistributionOrderChangeRecord existRecord = orderChangeRecords.get(0);
        if (changeRecord.getActionTime().after(existRecord.getActionTime())) {
            existRecord.setActionTime(changeRecord.getActionTime());
            return distributionOrderChangeRecordMapper.updateByPrimaryKeySelective(existRecord) > 0;
        }
        return true;
    }

    @Override
    public List<DistributionOrderChangeRecord> pageQueryDistributionOrderChange(DistributionOrderChangeRecordPageQuery query) {
        return extDistributionOrderChangeRecordMapper.pageQueryDistributionOrderChange(
                query.getChannel(),
                query.getActionStartTime(),
                query.getActionEndTime(),
                query.getCursorId(),
                query.getPageSize());
    }
}
