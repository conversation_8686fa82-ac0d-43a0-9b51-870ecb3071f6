package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.carnation.distribution.distributionplan.acl.DealProductAcl;
import com.sankuai.carnation.distribution.distributionplan.acl.model.DealProductInfo;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DealProductAclImpl implements DealProductAcl {

    @Resource
    private DealGroupQueryService dealGroupQueryService;

    private static final int MAX_BATCH_SIZE = 20;

    @Override
    public List<DealProductInfo> queryMtDealProductByIds(List<Long> mtProductIdList) {
        try {
            if (CollectionUtils.isEmpty(mtProductIdList)) {
                return Lists.newArrayList();
            }

            List<List<Long>> mtProductIdsList = Lists.partition(mtProductIdList, MAX_BATCH_SIZE);
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(), IdTypeEnum.MT)
                    .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                    .displayShop(DealGroupDisplayShopBuilder.builder().mtDisplayShopIds())
                    .category(DealGroupCategoryBuilder.builder().categoryId().serviceTypeId())
                    .build();

            List<DealProductInfo> dealProductInfoList = Lists.newArrayList();
            mtProductIdsList.forEach(mtProductIds -> {
                request.setDealGroupIds(Sets.newHashSet(mtProductIdList));
                try {
                    QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                    if (response != null && response.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                        List<DealProductInfo> dealProducts = convertToDealProducts(response.getData());
                        dealProductInfoList.addAll(dealProducts);
                        return;
                    }
                    Cat.logEvent("QueryDealProductFailed", "queryMtDealProductByIds failed");
                } catch (TException te) {
                    log.error("DealProductAcl.queryMtDealProductByIds failed, mtProductIds:{}", mtProductIds, te);
                }
            });

            return dealProductInfoList;
        } catch (Exception e) {
            log.error("DealProductAcl.queryMtDealProductByIds failed, mtProductIdList:{}", mtProductIdList, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<DealProductInfo> queryDpDealProductByIds(List<Long> dpProductIdList) {
        try {
            if (CollectionUtils.isEmpty(dpProductIdList)) {
                return Lists.newArrayList();
            }

            List<List<Long>> dpProductIdsList = Lists.partition(dpProductIdList, MAX_BATCH_SIZE);
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(), IdTypeEnum.DP)
                    .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                    .dealGroupPrice(DealGroupPriceBuilder.builder().all())
                    .dealGroupId(DealGroupIdBuilder.builder().all())
                    .customer(DealGroupCustomerBuilder.builder().platformCustomerId().all())
                    .image(DealGroupImageBuilder.builder().all())
                    .build();

            List<DealProductInfo> dealProductInfoList = Lists.newArrayList();
            dpProductIdsList.forEach(dpProductIds -> {
                request.setDealGroupIds(Sets.newHashSet(dpProductIds));
                try {
                    QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                    if (response != null && response.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                        List<DealProductInfo> dealProducts = convertToDealProducts(response.getData());
                        dealProductInfoList.addAll(dealProducts);
                        return;
                    }
                    Cat.logEvent("QueryDealProductFailed", "queryDpDealProductByIds failed");
                } catch (TException te) {
                    log.error("DealProductAcl.queryDpDealProductByIds failed, dpProductIds:{}", dpProductIds, te);
                }
            });

            return dealProductInfoList;
        } catch (Exception e) {
            log.error("DealProductAcl.queryDpDealProductByIds failed, dpProductIdList:{}", dpProductIdList, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public Map<Long, DealProductInfo> queryMtDealProductToMap(List<Long> mtProductIdList) {
        try {
            if (CollectionUtils.isEmpty(mtProductIdList)) {
                return Maps.newHashMap();
            }

            List<List<Long>> mtProductIdsList = Lists.partition(mtProductIdList, MAX_BATCH_SIZE);
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(), IdTypeEnum.MT)
                    .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                    .dealGroupId(DealGroupIdBuilder.builder().all())
                    //todo 这里需要查询展示门店吗
                    .displayShop(DealGroupDisplayShopBuilder.builder().mtDisplayShopIds())
                    .category(DealGroupCategoryBuilder.builder().categoryId().serviceTypeId())
                    .build();

            AtomicReference<Map<Long, DealProductInfo>> dealProductInfoMap = new AtomicReference<>(Maps.newHashMap());
            mtProductIdsList.forEach(mtProductIds -> {
                request.setDealGroupIds(Sets.newHashSet(mtProductIdList));
                try {
                    QueryDealGroupListResponse response = dealGroupQueryService.queryByDealGroupIds(request);
                    if (response != null && response.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                        List<DealProductInfo> dealProducts = convertToDealProducts(response.getData());
                        dealProductInfoMap.getAndSet(dealProducts.stream().collect(Collectors
                                .toMap(DealProductInfo::getMtProductId, Function.identity())));
                        return;
                    }
                    Cat.logEvent("QueryDealProductFailed", "queryMtDealProductToMap queryByDealGroupIds failed");
                } catch (TException te) {
                    log.error("DealProductAcl.queryMtDealProductByIds failed, mtProductIds:{}", mtProductIds, te);
                }
            });

            return dealProductInfoMap.get();
        } catch (Exception e) {
            log.error("DealProductAcl.queryMtDealProductToMap failed, mtProductIdList:{}", mtProductIdList, e);
            return Maps.newHashMap();
        }
    }

    private List<DealProductInfo> convertToDealProducts(QueryDealGroupListResult dealGroupListResult) {
        if (null == dealGroupListResult || CollectionUtils.isEmpty(dealGroupListResult.getList())) {
            return Lists.newArrayList();
        }

        HashSet<Integer> onlineProductStatus = Sets.newHashSet(DealGroupStatusEnum.VISIBLE_ONLINE.getCode(), DealGroupStatusEnum.NOT_VISIBLE_ONLINE.getCode());
        List<DealProductInfo> dealProductInfoList = Lists.newArrayList();
        dealGroupListResult.getList().stream()
                .filter(Objects::nonNull)
                .forEach(dealGroupDto -> {
                    DealProductInfo dealProductInfo = DealProductInfo.builder()
                            .dpProductId(dealGroupDto.getDpDealGroupId())
                            .mtProductId(dealGroupDto.getMtDealGroupId())
                            .productName("")
                            .onlineStatus(false)
                            .applyMtShopIds(Lists.newArrayList())
                            .build();
                    if (null != dealGroupDto.getBasic() && dealGroupDto.getBasic().getStatus() != null) {
                        dealProductInfo.setProductName(dealGroupDto.getBasic().getTitle());
                        dealProductInfo.setOnlineStatus(onlineProductStatus.contains(dealGroupDto.getBasic().getStatus()));
                        dealProductInfo.setTradeType(dealGroupDto.getBasic().getTradeType());
                        dealProductInfo.setEndSaleDate(dealGroupDto.getBasic().getEndSaleDate());
                    }
                    if (null != dealGroupDto.getCategory()) {
                        dealProductInfo.setCategoryId(dealGroupDto.getCategory().getCategoryId());
                        dealProductInfo.setLeafCategoryId(dealGroupDto.getCategory().getServiceTypeId());
                    }
                    if (dealGroupDto.getDisplayShopInfo() != null) {
                        dealProductInfo.setApplyMtShopIds(dealGroupDto.getDisplayShopInfo().getMtDisplayShopIds());
                    }
                    if (Objects.nonNull(dealGroupDto.getPrice())) {
                        dealProductInfo.setSalePrice(dealGroupDto.getPrice().getSalePrice());
                        dealProductInfo.setMarketPrice(dealGroupDto.getPrice().getMarketPrice());
                    }
                    if (Objects.nonNull(dealGroupDto.getImage())) {
                        dealProductInfo.setPicPath(dealGroupDto.getImage().getDefaultPicPath());
                    }
                    if (Objects.nonNull(dealGroupDto.getCustomer())) {
                        dealProductInfo.setPlatformCustomerId(dealGroupDto.getCustomer().getPlatformCustomerId());
                    }
                    dealProductInfoList.add(dealProductInfo);
                });

        return dealProductInfoList;
    }
}