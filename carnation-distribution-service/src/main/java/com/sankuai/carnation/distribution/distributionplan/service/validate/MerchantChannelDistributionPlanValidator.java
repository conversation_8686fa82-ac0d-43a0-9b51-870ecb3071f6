package com.sankuai.carnation.distribution.distributionplan.service.validate;

import com.sankuai.carnation.distribution.distributionplan.enums.MerchantChannelDistributionPlanOperateTypeEnum;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.DistributionProductPageQueryRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanCreateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanOperateRequest;
import com.sankuai.carnation.distribution.distributionplan.request.merchant.MerchantChannelDistributionPlanPageQueryRequest;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@NoArgsConstructor
public class MerchantChannelDistributionPlanValidator {

    public static boolean validateDistributionProductPageQueryRequest(DistributionProductPageQueryRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg)
                && validateDpAccountId(request.getAccountId(), errorMsg)
                && validatePageParameters(request.getPageNo(), request.getPageSize(), errorMsg);
    }

    public static boolean validateDistributionPlanPageQueryRequest(MerchantChannelDistributionPlanPageQueryRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg)
                && validateDpAccountId(request.getAccountId(), errorMsg)
                && validatePageParameters(request.getPageNo(), request.getPageSize(), errorMsg);
    }

    public static boolean validateDistributionPlanCreateRequest(MerchantChannelDistributionPlanCreateRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg)
                && validateDpAccountId(request.getAccountId(), errorMsg)
                && validateProduct(request.getProductId(), request.getProductType(), errorMsg)
                && validateCommissionRate(request.getCommissionRate(), errorMsg);
    }

    public static boolean validateDistributionPlanOperateRequest(MerchantChannelDistributionPlanOperateRequest request, StringBuilder errorMsg) {
        return validateRequestBase(request, errorMsg)
                && validateDpAccountId(request.getAccountId(), errorMsg)
                && validatePlanId(request.getPlanId(), errorMsg)
                && validateOperateType(request.getOperateType(), request.getCommissionRate(), errorMsg);
    }

    private static boolean validatePlanId(Long planId, StringBuilder errorMsg) {
        if (planId == null || planId <= 0L) {
            errorMsg.append("分销计划ID不能为空或者小于等于0");
            return false;
        }
        return true;
    }

    private static boolean validateRequestBase(Object request, StringBuilder errorMsg) {
        if (Objects.isNull(request)) {
            errorMsg.append("请求参数不能为空");
            return false;
        }
        return true;
    }

    private static boolean validateDpAccountId(Long dpAccountId, StringBuilder errorMsg) {
        if (dpAccountId == null || dpAccountId <= 0L) {
            errorMsg.append("账号ID不能为空");
            return false;
        }
        return true;
    }

    private static boolean validateProduct(Long productId, Integer productType, StringBuilder errorMsg) {
        if (productId == null || productId <= 0L) {
            errorMsg.append("商品ID不能为空");
            return false;
        }
        if (productType == null) {
            errorMsg.append("商品类型不能为空");
            return false;
        }
        if (!productType.equals(1)) {
            errorMsg.append("目前只支持团购");
            return false;
        }
        return true;
    }

    private static boolean validatePageParameters(Integer pageNo, Integer pageSize, StringBuilder errorMsg) {
        if (pageNo == null || pageNo <= 0) {
            errorMsg.append("分页页码参数错误");
            return false;
        }
        if (pageSize == null || pageSize <= 0) {
            errorMsg.append("分页大小参数错误");
            return false;
        }
        return true;
    }

    private static boolean validateCommissionRate(String commissionRate, StringBuilder errorMsg) {
        if (StringUtils.isBlank(commissionRate)) {
            errorMsg.append("佣金率不能为空");
            return false;
        }
        if (!NumberUtils.isDigits(commissionRate)) {
            errorMsg.append("佣金率要求是整数");
            return false;
        }
        return true;
    }

    private static boolean validateOperateType(Integer operateType, String commissionRate, StringBuilder errorMsg) {
        if (Objects.isNull(operateType)) {
            errorMsg.append("操作类型不能为空");
            return false;
        }
        if (Objects.isNull(MerchantChannelDistributionPlanOperateTypeEnum.fromCode(operateType))) {
            errorMsg.append("无效操作类型");
            return false;
        }
        if (MerchantChannelDistributionPlanOperateTypeEnum.MODIFY_COMMISSION.equals(MerchantChannelDistributionPlanOperateTypeEnum.fromCode(operateType))) {
            if (StringUtils.isBlank(commissionRate)) {
                errorMsg.append("佣金率不能为空");
                return false;
            }
            if (!NumberUtils.isDigits(commissionRate)) {
                errorMsg.append("佣金率要求是整数");
                return false;
            }
        }
        return true;
    }
}