package com.sankuai.carnation.distribution.distributionplan.acl.model;

import lombok.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GeneralDealGroupSearchResult {

    /**
     * 商品ID集
     */
    private List<Long> productIdList;

    /**
     * 数据总数
     */
    private Long totalHits;

    public static GeneralDealGroupSearchResult emptyResult() {
        return new GeneralDealGroupSearchResult(Collections.emptyList(), 0L);
    }
}