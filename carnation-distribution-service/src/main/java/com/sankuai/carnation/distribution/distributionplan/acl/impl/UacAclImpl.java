package com.sankuai.carnation.distribution.distributionplan.acl.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.carnation.distribution.distributionplan.acl.UacAcl;
import com.sankuai.meituan.uac.sdk.entity.UacGeneralAuthEntity;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class UacAclImpl implements UacAcl {
    private static final String CAT_TYPE = UacAcl.class.getSimpleName();

    @Autowired
    private UacAuthRemoteService uacAuthRemoteService;

    @Override
    public List<UacGeneralAuthEntity> batchGetUserAuth(String uid, List<String> generalResourceCodes) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "batchGetUserAuth");
        try {
            if (StringUtils.isBlank(uid)) {
                throw new IllegalArgumentException("uid为空");
            }
            if (CollectionUtils.isEmpty(generalResourceCodes)) {
                throw new IllegalArgumentException("generalResourceCodes为空");
            }
            return uacAuthRemoteService.batchGetUserGeneralResource(
                    uid,
                    generalResourceCodes,
                    true
            );
        } catch (Exception e) {
            log.error("UacAuthRemoteService.batchGetUserAuth failed, uid: {}, generalResourceCodes: {}", uid, generalResourceCodes, e);
            Cat.logError(e);
            throw e;
        } finally {
            transaction.complete();
        }
    }
}
