package com.sankuai.carnation.distribution.distributionplan.enums;

/**
 * <AUTHOR>
 */

public enum DistributionPlanOperationLogTypeEnum {

    ADD_DISTRIBUTION_PLAN(1, "新增分销计划"),
    EDIT_DISTRIBUTION_PLAN(2, "编辑分销计划"),
    UPDATE_DISTRIBUTION_PLAN_STATUS(3, "更新分销计划状态"),


    ;
    private int type;

    private String desc;

    DistributionPlanOperationLogTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static boolean isExistCurrentType(Integer type) {
        if (type == null) {
            return false;
        }

        for (DistributionPlanOperationLogTypeEnum operationType : DistributionPlanOperationLogTypeEnum.values()) {
            if (type.equals(operationType.type)) {
                return true;
            }
        }

        return false;
    }

    public static DistributionPlanOperationLogTypeEnum from(Integer type) {
        for (DistributionPlanOperationLogTypeEnum operationType : DistributionPlanOperationLogTypeEnum.values()) {
            if (type.equals(operationType.type)) {
                return operationType;
            }
        }

        return null;
    }
}
