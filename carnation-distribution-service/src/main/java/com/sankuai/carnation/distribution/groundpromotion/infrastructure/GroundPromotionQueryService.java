package com.sankuai.carnation.distribution.groundpromotion.infrastructure;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.tpfun.product.api.sku.common.enums.FunClientType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.arch.fundamental.threadpool.ResizableCapacityThreadPoolExecutorFactory;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.sankuai.carnation.distribution.groundpromotion.dto.DealProductDTO;
import com.sankuai.carnation.distribution.groundpromotion.dto.GroundPromotionPointRequest;
import com.sankuai.carnation.distribution.groundpromotion.dto.QueryPoiAndGoodsReq;
import com.sankuai.carnation.distribution.groundpromotion.infrastructure.acl.DealAcl;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint;
import com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal;
import com.sankuai.carnation.distribution.groundpromotion.repository.service.GroundPromotionPointRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2023/8/31
 * @Description:
 */
@Slf4j
@Service
public class GroundPromotionQueryService {

    @Autowired
    private GroundPromotionPointRepository groundPromotionPointRepository;

    @Autowired
    private DealAcl dealAcl;

    private static final ThreadPoolExecutor EXECUTOR =
            ResizableCapacityThreadPoolExecutorFactory.create("GroundPromotionQueryService_Pool", 2, 5, 10, TimeUnit.SECONDS,
                    1000);

    public List<GroundPromotionPoint> queryPoint(List<Long> pointIdList) {
        if (CollectionUtils.isEmpty(pointIdList)) {
            return Lists.newArrayList();
        }
        GroundPromotionPointRequest request = new GroundPromotionPointRequest();
        request.setPointIdList(pointIdList);
        return groundPromotionPointRepository.query(request);
    }

    public Map<Long, List<DealProductDTO>> batchQueryDeals(Map<Long, List<PromotionalDeal>> shopId2PromotionDeals, int cityId, String appVersion) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.GroundPromotionQueryService.batchQueryDeals(java.util.Map,int,java.lang.String)");
        try {
            EXECUTOR.setCorePoolSize(50);
            EXECUTOR.setMaximumPoolSize(100);
            List<Future<Map<Long, List<DealProductDTO>>>> dealFutureList = new LinkedList<>();
            for (Map.Entry<Long, List<PromotionalDeal>> entry : shopId2PromotionDeals.entrySet()) {
                List<PromotionalDeal> shopPromotionDeals = entry.getValue();
                List<Long> dealIds = CollectionUtils.isEmpty(shopPromotionDeals) ? Lists.newArrayList() :
                        shopPromotionDeals.stream().map(PromotionalDeal::getDealId).collect(Collectors.toList());
                CompletableFuture<Map<Long, List<DealProductDTO>>> dealFuture = CompletableFuture.supplyAsync(() ->
                        queryDeal(entry.getKey(), dealIds, cityId, appVersion), EXECUTOR);
                dealFutureList.add(dealFuture);
            }
            Map<Long, List<DealProductDTO>> shopId2DealProducts = new LinkedHashMap<>();
            for (Future<Map<Long, List<DealProductDTO>>> future : dealFutureList) {
                Map<Long, List<DealProductDTO>> map = future.get(2, TimeUnit.SECONDS);
                if (Objects.nonNull(map)) {
                    shopId2DealProducts.putAll(map);
                }
            }
            EXECUTOR.setCorePoolSize(2);
            EXECUTOR.setMaximumPoolSize(5);
            return shopId2DealProducts;
        } catch (Exception e) {
            log.error("[cmQueryDistribution] error, shopId2PromotionDeals is {}, exception is ", JSONObject.toJSONString(shopId2PromotionDeals), e);
            return Maps.newHashMap();
        }
    }

    public Map<Long, List<DealProductDTO>> queryDeal(Long shopId, List<Long> dealIdList, Integer cityId, String appVersion) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.carnation.distribution.groundpromotion.infrastructure.GroundPromotionQueryService.queryDeal(java.lang.Long,java.util.List,java.lang.Integer,java.lang.String)");
        if (dealIdList.contains(0L)) {
            Map<Long, List<DealProductDTO>> map = new HashMap<>();
            DealProductDTO dealProductDTO = new DealProductDTO();
            dealProductDTO.setProductId(0);
            dealProductDTO.setDetailJumpUrl("");
            dealProductDTO.setTitle("全部在线团单");
            dealProductDTO.setSalePrice("");
            map.put(shopId, Lists.newArrayList(dealProductDTO));
            return map;
        }
        QueryPoiAndGoodsReq queryPoiAndGoodsReq = new QueryPoiAndGoodsReq();
        queryPoiAndGoodsReq.setClientType(FunClientType.WEB.code);
        queryPoiAndGoodsReq.setCityId(cityId);
        queryPoiAndGoodsReq.setDealIdList(dealIdList);
        queryPoiAndGoodsReq.setPlatform(Platform.DP.getCode());
        queryPoiAndGoodsReq.setDpShopId(shopId);
        queryPoiAndGoodsReq.setVersion(appVersion);
        return dealAcl.loadDealProduct(queryPoiAndGoodsReq);
    }

}
