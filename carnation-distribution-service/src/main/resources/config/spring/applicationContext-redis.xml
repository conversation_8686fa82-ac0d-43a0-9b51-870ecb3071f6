<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
               http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="medicalRedisClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="redis-medical-shanghai"></property>
        <!--读取超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"></property>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-only"></property>
        <!--连接redis节点的连接池配置，选填-->
        <property name="poolMaxIdle" value="30"></property>
        <property name="poolMaxTotal" value="50"></property>
        <property name="poolWaitMillis" value="500"></property>
    </bean>

    <!--  分布式锁  -->
    <bean id="distributedLockManager"
          class="com.meituan.hotel.dlm.service.impl.DistributedLockManager"
          init-method="init" destroy-method="destroy" scope="singleton">
        <!-- 可选value为："zk"、"tair"、"squirrel"；可配多个，按先后顺序第一个为主引擎，其他为备用引擎 -->
        <constructor-arg value="squirrel"/>
        <!-- 必填。可选值：online/offline 表示接入业务的机器环境为线上还是线下 -->
        <property name="environment" value="online"/>
        <!-- 必填。业务方设置的appKey，用于区分业务唯一的分布式锁，建议用octo的appKey -->
        <property name="appKey" value="com.sankuai.dzhealth.medical"/>
        <!-- 必填。Cerberus授权码，由Cerberus方根据业务方提供的唯一授权码。 -->
        <property name="secret" value="C99D6498C2900FB4BBBC819F8DE87D3A"/>
        <property name="squirrelConfigModel" ref="squirrelConfigModel"/>
    </bean>
    <!-- squirrel集群配置 -->
    <bean id="squirrelConfigModel" class="com.meituan.hotel.dlm.model.SquirrelConfigModel">
        <property name="redisStoreClient" ref="medicalRedisClient"/>
        <!-- 业务自定义category，注意命名模板请设置为{0}，类型为string -->
        <property name="category" value="distribution_plan_lock_key"/>
    </bean>
</beans>