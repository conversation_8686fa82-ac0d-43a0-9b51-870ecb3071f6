<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:pigeon="http://code.dianping.com/schema/pigeon"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd http://code.dianping.com/schema/pigeon http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd">

    <!-- 小丽在线 -->
    <pigeon:reference id="beautiBotUserService"
                      timeout="1000"
                      url="beauty.biz.beautiBotUserService"
                      interface="com.dianping.beauty.ibot.service.BeautiBotUserService"/>

    <!--大象-->
    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="3000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="udbThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.xm.udb.common.UdbServiceI"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.xm.udb"/>
    </bean>
    <!--大象-->
    <!-- 商品（团购、标品、泛商品）价格、优惠服务，来自商品平台 start -->
    <pigeon:reference id="priceDisplayService"
                      timeout="2000"
                      interface="com.sankuai.dealuser.price.display.api.PriceDisplayService"
                      url="com.sankuai.dealuser.price.display.api.PriceDisplayService"/>
    <!-- 商品（团购、标品、泛商品）价格、优惠服务，来自商品平台 end -->

    <!-- 团购的商品信息查询  start -->
    <pigeon:reference id="dealShopQueryService"
                      timeout="1000"
                      interface="com.dianping.deal.shop.DealShopQueryService"
                      url="http://service.dianping.com/tuangou/dealShopService/dealShopQueryService_1.0.0"/>
    <!-- 商品（团购、标品、泛商品）价格、优惠服务，来自商品平台 end -->

    <!-- 标品基础信息 start -->
    <bean id="productListService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dzviewscene.poisummary.rpc.ProductListService"/>
        <property name="interfaceName" value="com.sankuai.productsummary.ProductListService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <!-- 标品基础信息 end -->

    <!-- 短链服务 start -->
    <bean id="operateService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/memberOSSService/OperateService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.mobileossapi.service.operate.OperateService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <!-- 短链服务 end -->

    <!-- 订单数量查询服务 start -->
    <bean id="orderReceiptQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.technician.trade.order.service.OrderReceiptQueryService"/>
        <property name="interfaceName" value="com.sankuai.technician.trade.order.service.OrderReceiptQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <!-- 订单数量查询服务 end -->

    <!-- 订单核销查询服务 start -->
    <bean id="orderVerifyReceiptQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.technician.trade.order.service.OrderVerifyReceiptQueryService"/>
        <property name="interfaceName"
                  value="com.sankuai.technician.trade.order.service.OrderVerifyReceiptQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <!-- 订单核销查询服务 end -->

    <!-- 美团联盟 -->
    <bean id="orderAttributionCalculateThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.cps.union.unionbase.OrderAttributionCalculateThriftService"/>
        <property name="remoteAppkey" value="com.sankuai.cps.union.datacenter"/>
        <property name="remoteServerPort" value="7101"/>
        <property name="timeout" value="1000"/>
    </bean>
    <!-- 美团联盟 -->

    <!-- 到综订单 -->
    <pigeon:reference id="getUnifiedOrderService"
                      timeout="1000"
                      interface="com.dianping.pay.order.service.query.GetUnifiedOrderService"
                      url="http://service.dianping.com/orderService/query/getUnifiedOrderService_1.0.0"/>
    <pigeon:reference id="getOnlineUnifiedOrderService"
                      timeout="1000"
                      interface="com.dianping.pay.order.service.query.GetOnlineUnifiedOrderService"
                      url="http://service.dianping.com/orderService/query/getOnlineUnifiedOrderService_1.0.0"/>

    <pigeon:reference id="getNibOrderProxyService"
                      timeout="1000"
                      interface="com.dianping.pay.order.service.query.GetNibOrderProxyService"
                      url="http://service.dianping.com/orderService/query/getNibOrderProxyService_1.0.0"/>

    <!-- 手艺人订单服务 -->
    <pigeon:reference id="orderReceiptUpdateService"
                      timeout="1000"
                      interface="com.sankuai.technician.trade.order.service.OrderReceiptUpdateService"
                      url="com.sankuai.technician.trade.order.service.OrderReceiptUpdateService"/>

    <!-- 手艺人分销码解析 -->
    <pigeon:reference id="techFenYongService"
                      timeout="1000"
                      interface="com.dianping.technician.service.TechFenYongService"
                      url="com.dianping.technician.service.TechFenYongService"/>

    <!--Product服务-->
    <bean id="productService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tpfunService/skuProductService_1.0.0"/>
        <property name="iface" value="com.dianping.tpfun.product.api.sku.ProductService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- 商品归属查询 -->
    <pigeon:reference id="productOwnerQueryService"
                      timeout="1000"
                      interface="com.dianping.tpfun.sku.dzproduct.productowner.ProductOwnerQueryService"
                      url="http://service.dianping.com/tpfunService/productOwnerQueryService_1.0.0"/>

    <!-- 客户券 -->
    <pigeon:reference id="tgcShopCouponQueryService"
                      timeout="1000"
                      interface="com.dianping.tgc.open.v2.TGCShopCouponQueryService"
                      url="http://service.dianping.com/tgcOpenService/v2/tgcShopCouponQueryService_1.0.0"/>

    <!-- 发券 -->
    <pigeon:reference id="issueCouponService"
                      timeout="1000"
                      interface="com.sankuai.mpmkt.coupon.issue.api.IssueCouponService"
                      url="http://service.sankuai.com/mpmkt/coupon/issue/IssueCouponService_1.0.0"/>

    <!-- 鉴权 -->
    <bean id="kmsAuthDataSource" class="com.meituan.service.inf.kms.client.KmsAuthDataSource">
        <property name="appkey" value="com.sankuai.medicalcosmetology.distribution.service"/>
    </bean>
    <bean id="defaultSignHandler" class="com.meituan.service.mobile.mtthrift.auth.DefaultSignHandler">
        <property name="authDataSource" ref="kmsAuthDataSource"/>
    </bean>

    <bean id="authenticationService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.nibmp.infra.amp.authenticate.lib.service.platform.StandardAuthenticationService"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.nibmerchant.amp.authenticate"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="7000"/>
        <property name="timeout" value="3000"/>
    </bean>
    <!-- 鉴权 -->

    <!-- 团单 -->
    <pigeon:reference id="dealGroupBaseService"
                      timeout="1000"
                      interface="com.dianping.deal.base.DealGroupBaseService"
                      url="http://service.dianping.com/tuangou/dealService/dealGroupBaseService_1.0.0"/>

    <pigeon:reference id="dealBaseBaseService"
                      timeout="1000"
                      interface="com.dianping.deal.base.DealBaseService"
                      url="http://service.dianping.com/tuangou/dealService/dealBaseService_1.0.0"/>

    <pigeon:reference id="dealGroupCustomerQueryService"
                      timeout="1000"
                      interface="com.dianping.deal.voucher.query.api.DealGroupCustomerQueryService"
                      url="com.dianping.deal.voucher.query.api.DealGroupCustomerQueryService"/>
    <!-- 团单 end -->

    <!-- 创券服务 -->
    <pigeon:reference id="activityManageService"
                      timeout="5000"
                      interface="com.dianping.tgc.service.ActivityManageService"
                      url="http://service.dianping.com/tgcManagementService/activityManageService_1.0.0"/>
    <!-- 创券服务 end -->

    <!-- 券服务 -->
    <pigeon:reference id="activityListService"
                      timeout="1000"
                      interface="com.dianping.tgc.service.ActivityListService"
                      url="http://service.dianping.com/tgcManagementService/activityListService_1.0.0"/>

    <pigeon:reference id="activityBizService"
                      timeout="3000"
                      interface="com.dianping.tgc.service.ActivityBizService"
                      url="http://service.dianping.com/tgcManagementService/activityBizService_1.0.0"/>
    <!-- 券服务 end -->

    <bean id="dealGroupQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.general.product.query.center.client.service.DealGroupQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.productuser.query.center"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1000"/>
    </bean>

    <!--权限系统-账号相关-->
    <bean id="newAccountService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.nibmp.infra.amp.attribute.lib.IAccountService"/>
        <property name="remoteAppkey" value="com.sankuai.nibmerchant.amp.attribute"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/>
        <property name="remoteServerPort" value="9002"/>
        <property name="timeout" value="1000"/>
        <property name="signHandler" ref="defaultSignHandler"/>
    </bean>

    <bean id="merchantSearchService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.dianping.tp.search.service.MerchantSearchService"/>
        <property name="iface" value="com.dianping.tp.search.service.MerchantSearchService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="5000"/>
    </bean>

    <!-- 到综医美内容商品 -->
    <bean id="dzBeautyContentProductService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="beautycontent.function.dzproduct.DzProductService"/>
        <property name="interfaceName" value="com.sankuai.beautycontent.dzproduct.api.DzProductService"/>
        <property name="timeout" value="3000"/>
        <property name="callType" value="sync"/>
        <property name="serialize" value="thrift"/>
        <property name="remoteAppKey" value="com.sankuai.beautycontent.function"/>
    </bean>

    <!-- 商品选品中心 商品投放服务 start -->
    <pigeon:reference id="productReleaseService"
                      timeout="5000"
                      interface="com.dianping.deal.flow.release.ProductReleaseService"
                      url="com.dianping.deal.flow.release.ProductReleaseService"/>

    <!-- 商品选品中心 商品投放查询服务 start -->
    <pigeon:reference id="releasedProductQueryService"
                      timeout="5000"
                      interface="com.dianping.deal.flow.release.ReleasedProductQueryService"
                      url="com.dianping.deal.flow.release.ReleasedProductQueryService"/>

    <!-- 商品选品中心 poi/商品挂载服务 start -->
    <pigeon:reference id="mountService"
                      timeout="5000"
                      interface="com.dianping.deal.flow.MountService"
                      url="com.dianping.deal.flow.MountService"/>

    <!-- 优惠码 start -->
    <pigeon:reference id="userScanRecordService"
                      timeout="1000"
                      interface="com.dianping.gmkt.event.api.scan.UserScanRecordService"
                      url="http://service.dianping.com/gmkt_event_manage_service/userScanRecordService_0.0.1"/>

    <pigeon:reference id="userScanRecordServiceV2"
                      timeout="1000"
                      interface="com.sankuai.medicalcosmetology.offline.code.api.service.UserScanRecordService"
                      url="com.sankuai.medicalcosmetology.offline.code.service.UserScanRecordService"/>

    <pigeon:reference id="promoQRCodeOrderService"
                      timeout="1000"
                      interface="com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeOrderService"
                      url="com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeOrderService"/>

    <pigeon:reference id="promoQRCodeCService"
                      timeout="1000"
                      interface="com.dianping.gmkt.event.api.promoqrcode.service.PromoQRCodeCService"
                      url="http://service.dianping.com/gmkt_event_manage_service/promoQRCodeCService_0.0.1"/>

    <pigeon:reference id="rebateActivityManageService"
                      timeout="1000"
                      interface="com.dianping.gmkt.event.api.rebate.service.RebateActivityManageService"
                      url="http://service.dianping.com/gmkt_event_manage_service/rebateActivityManageService_0.0.1"/>

    <pigeon:reference id="rebateRuleCheckService"
                      timeout="1000"
                      interface="com.dianping.gmkt.event.api.rebate.service.RebateRuleCheckService"
                      url="com.dianping.gmkt.event.api.rebate.service.RebateRuleCheckService"/>
    <!-- 优惠码 end -->

    <pigeon:reference id="itemManagementService"
                      timeout="2000"
                      interface="com.dianping.gmkt.event.datapools.api.service.itemmanagement.service.ItemManagementService"
                      url="http://service.dianping.com/gmkt_event_datapools_service/ItemManagementService_0.0.1"/>

    <pigeon:reference id="itemDataService"
                      timeout="2000"
                      interface="com.dianping.gmkt.event.datapools.api.service.itemmanagement.service.ItemDataService"
                      url="http://service.dianping.com/gmkt_event_datapools_service/ItemDataService_0.0.1"/>

    <bean id="sinaiDpPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.DpPoiService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
    </bean>

    <!-- 美团侧接口 -->
    <bean id="sinaiMtPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sinai.data.api.service.MtPoiService"/>
        <property name="remoteAppkey" value="com.sankuai.sinai.data.query"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="1000"/>
        <!-- <property name="remoteServerPort" value="7101"/> -->
    </bean>

    <pigeon:reference id="cityInfoService"
                      timeout="1000"
                      interface="com.dianping.gis.remote.service.CityInfoService"
                      url="http://service.dianping.com/gisService/cityInfoService_1.0.0"/>


    <bean id="dealProductService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.sankuai.dztheme.dealproduct.DealProductService"/>
        <property name="interfaceName" value="com.sankuai.dztheme.deal.DealProductService"/>
        <property name="timeout" value="1000"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
    </bean>

    <bean id="dealIdMapperService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/dealIdMapperService/dealIdMapperService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.idmapper.api.DealIdMapperService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="5000"/>
    </bean>

    <!-- 商户主题-->
    <bean id="dzThemeShopService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.sankuai.dztheme.shop.service.DzThemeShopService"/>
        <property name="iface" value="com.sankuai.dztheme.shop.service.DzThemeShopService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- 商户主题-->
    <bean id="accountServices" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="com.meituan.beauty.sakura.account.service.AccountServices"/>
        <property name="iface" value="com.meituan.beauty.sakura.account.service.AccountServices"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="10000"/>
    </bean>

    <pigeon:reference id="rpcInvestmentActivityRuleQueryService" timeout="5000"
                      url="http://service.dianping.com/gmm-investment-activity-rule-service/InvestmentActivityRuleQueryService_1.0.0"
                      interface="com.dianping.gmm.investment.activity.rule.api.service.InvestmentActivityRuleQueryService">
    </pigeon:reference>

    <pigeon:reference id="investmentActivityQueryService" timeout="5000"
                      url="http://service.dianping.com/gmm-investment-activity-rule-service/InvestmentActivityQueryService_1.0.0"
                      interface="com.dianping.gmm.investment.activity.rule.api.service.InvestmentActivityQueryService">
    </pigeon:reference>

    <bean id="dealGroupShopService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="http://service.dianping.com/tuangou/dealShopService/dealGroupShopService_1.0.0"/>
        <property name="iface" value="com.dianping.deal.shop.DealGroupShopService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <bean id="dealMetaTagQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="com.dianping.deal.tag.DealMetaTagQueryService"/>
        <property name="iface" value="com.dianping.deal.tag.DealMetaTagQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <pigeon:reference id="investmentActivityEnrollService" timeout="5000"
                      url="http://service.dianping.com/gm-marketing-investment-service/InvestmentActivityEnrollService_1.0.0"
                      interface="com.dianping.gm.marketing.investment.api.service.InvestmentActivityEnrollService">
    </pigeon:reference>

    <pigeon:reference id="productStaticDetailBySpuServiceV2"
                      timeout="1000"
                      interface="com.dianping.tpfun.product.api.sku.aggregate.ProductStaticDetailBySpuServiceV2"
                      url="http://service.dianping.com/tpfunService/productstaticdetailbyspuservicev2_1.0.0"/>

    <!-- 券批次查询 -->
    <bean id="unifiedCouponGroupQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.sankuai.com/mpmkt/coupon/execute/UnifiedCouponGroupQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.unified.coupon.manage.api.UnifiedCouponGroupQueryService"/>
        <property name="cell" value="gray-release-nibmkt-coupon-dz-trade"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="dpProxyReviewServiceV2" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="UGCProxyService.DpProxyReviewServiceV2"/>
        <property name="interfaceName" value="com.dianping.ugc.proxyService.remote.dp.DpProxyReviewServiceV2"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="eventRpcService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/gmkt_event_manage_service/eventManageRpcService_0.0.1"/>
        <property name="interfaceName" value="com.dianping.gmkt.event.api.api.EventRpcService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- 订单查询服务 -->
    <bean id="batchQueryOrderService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="http://service.dianping.com/orderService/query/batchQueryOrderService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.order.service.query.BatchQueryOrderService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="receiptQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/receiptQueryService/receiptQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.receipt.query.api.ReceiptQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="1500"/>
    </bean>
    <bean id="receiptRecordService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url"
                  value="http://service.dianping.com/receiptRecordRemoteService/receiptRecordRemoteService_2.0.0"/>
        <property name="interfaceName" value="com.dianping.tuangou.receipt.record.api.ReceiptRecordService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="2500"/>
    </bean>


    <!-- 货架查询服务 -->
    <pigeon:reference id="generalShelfQueryService"
                      timeout="2000"
                      url="com.dianping.product.shelf.query.api.GeneralShelfQueryService"
                      interface="com.dianping.product.shelf.query.api.GeneralShelfQueryService"/>

    <!-- 省份查询服务 -->
    <bean id="provinceInfoService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/gisService/provinceInfoService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.gis.remote.service.ProvinceInfoService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="3000"/>
    </bean>

    <!-- 职人报名 -->
    <pigeon:reference id="distributorActivityService"
                      timeout="2000"
                      url="http://service.dianping.com/gmkt_event_manage_service/distributorActivityService_0.0.1"
                      interface="com.dianping.gmkt.event.api.distribution.service.DistributorActivityService"/>

    <bean id="salarySettleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.flexiblework.salary.client.settle.thrift.service.SettleThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="remoteAppkey" value="com.sankuai.flexiblework.salary"/>
        <property name="remoteServerPort" value="8426"/>
    </bean>

    <!-- 次卡 -->
    <pigeon:reference id="timesCardQueryService"
                      timeout="1000"
                      url="http://service.dianping.com/TimesCardQueryService/TimesCardQueryService_1.0.0"
                      interface="com.sankuai.merchantcard.timescard.exposure.TimesCardQueryService"/>
    <!-- 次卡 end -->

    <!-- 手艺人查询服务 -->
    <pigeon:reference id="technicianService"
                      timeout="1000"
                      interface="com.dianping.technician.biz.service.TechnicianService"
                      url="technician.biz.baseTechnicianService"/>
    <!-- 餐地推系统 货架查询服务-->
    <bean id="qrCodeShelfService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.foodtrade.rebate.qrcode.api.service.QRCodeShelfService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.foodtrade.rebate.qrcode"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="10015"/>
        <property name="timeout" value="1000"/>
    </bean>

    <bean id="poiCooperationService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.foodtrade.rebate.qrcode.api.service.PoiCooperationService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.foodtrade.rebate.qrcode"/>  <!-- 目标Server Appkey  -->
        <property name="remoteServerPort" value="10015"/>
        <property name="timeout" value="1000"/>
    </bean>


    <!-- 泛商品券查询服务-->
    <bean id="couponQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/dzCouponQueryService/couponQueryService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.dz.coupon.query.api.CouponQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="5000"/>
    </bean>

    <!-- 查询订单分摊金额-->
    <bean id="unifiedOrderOnlineQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="http://service.dianping.com/unifiedOrder/unifiedOrderOnlineQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.unifiedorder.onlinequery.UnifiedOrderOnlineQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="3000"/>
    </bean>

    <!-- USS新客服务-->
    <bean id="ussClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.data.uss.thrift.USSServiceV2"/> <!-- 注意, 这里和V1版接口不一样, 完全兼容v1接口 -->
        <property name="remoteAppkey"
                  value="com.sankuai.data.dm.uss.sh"/> <!-- com.sankuai.data.uss(北京侧) com.sankuai.data.dm.uss.sh(上海侧) -->
        <property name="appKey"
                  value="com.sankuai.medicalcosmetology.distribution.service"/> <!-- check一下业务方AppKey标识！！！ 请填写接入方的服务标识 -->
        <property name="timeout" value="10000"/> <!--thrift rpc 超时时间（毫秒） -->
        <property name="nettyIO" value="true"/> <!--客户端务必设置nettyIO，减少服务端的连接开销-->
        <property name="filterByServiceName" value="true"/> <!-- 默认false  https://km.sankuai.com/page/28385322 -->
    </bean>
    <pigeon:reference id="spuThemeQueryService"
                      timeout="3000"
                      interface="com.sankuai.dztheme.spuproduct.SpuThemeQueryService"
                      url="com.sankuai.dztheme.spuproduct.SpuThemeQueryService"/>

    <pigeon:reference id="settleBillRemoteService"
                      timeout="1000"
                      interface="com.sankuai.technician.trade.billing.SettleBillRemoteService"
                      url="com.sankuai.technician.trade.billing.SettleBillRemoteService"/>
    <!-- 口令注册-->
    <bean id="clipboardService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="com.dianping.cip.growth.acquisition.api.service.ClipboardService"/>
        <property name="interfaceName" value="com.dianping.cip.growth.acquisition.api.service.ClipboardService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="500"/>
    </bean>

    <!-- 手艺人制作查询 -->
    <pigeon:reference id="techAccountAuthQueryService"
                      timeout="1000"
                      interface="com.sankuai.technician.account.service.TechAccountAuthQueryService"
                      url="com.sankuai.technician.account.service.TechAccountAuthQueryService"/>

    <!-- 商家账号映射 -->
    <bean id="bizAccountDpAdapterThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.sjst.ecom.epassportdpadapter.client.service.BizAccountDpAdapterThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="remoteAppkey" value="com.sankuai.epassport.dpadapter"/>
        <property name="remoteServerPort" value="9002"/>
    </bean>
    <pigeon:reference id="flowEntryWxMaterialService"
                      timeout="1000"
                      interface="com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService"
                      url="com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService"/>
    <pigeon:reference id="couponService"
                      timeout="1000"
                      interface="com.sankuai.dz.srcm.coupon.service.CouponService"
                      url="com.sankuai.dz.srcm.coupon.service.CouponService"/>

    <bean id="leafIdGen" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.inf.leaf.thrift.IDGen"/> <!--leaf的 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.leaf.common.default"/>  <!-- leaf Appkey  -->
        <property name="timeout" value="500"/> <!--超时时间为500ms-->
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
        <property name="serialize" value="thrift"/>
    </bean>

    <!-- 手艺人制作查询 -->
    <pigeon:reference id="technicianCenterService"
                      timeout="1000"
                      interface="com.dianping.technician.service.TechnicianCenterService"
                      url="com.dianping.technician.service.TechnicianCenterService"/>

    <pigeon:reference id="makingTechQueryService"
                      timeout="1000"
                      interface="com.sankuai.technician.info.make.service.MakingTechQueryService"
                      url="com.sankuai.technician.info.make.service.MakingTechQueryService"/>
    <!-- 社群服务 查询unionId -->
    <pigeon:reference id="userUnionIdService"
                      timeout="1000"
                      interface="com.sankuai.dz.srcm.user.service.UserUnionIdService"
                      url="com.sankuai.dz.srcm.user.service.UserUnionIdService"/>

    <!-- 结算计费查询服务 -->
    <pigeon:reference id="settleBillQueryRemoteService"
                      timeout="1000"
                      interface="com.sankuai.technician.trade.api.settle.service.SettleBillQueryRemoteService"
                      url="com.sankuai.technician.trade.api.settle.service.SettleBillQueryRemoteService"/>
    <!-- 获取龙抬头任务权益 -->
    <pigeon:reference id="shopCampaignEventAIOService"
                      timeout="2000"
                      url="com.sankuai.technician.arch.service.ShopCampaignEventAIOService"
                      interface="com.sankuai.technician.arch.service.ShopCampaignEventAIOService"/>
    <!-- 大促活动查询 -->
    <pigeon:reference id="campaignEventQueryService"
                      timeout="2000"
                      url="com.sankuai.technician.arch.service.CampaignEventQueryService"
                      interface="com.sankuai.technician.arch.service.CampaignEventQueryService"/>

    <!-- 手艺人职人服务 -->
    <pigeon:reference id="techCategoryQueryService"
                      timeout="1000"
                      url="com.sankuai.technician.category.service.TechCategoryQueryService"
                      interface="com.sankuai.technician.category.service.TechCategoryQueryService"/>

    <bean id="interactWithPartnerCommonApi" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy">
        <property name="serviceInterface"
                  value="com.sankuai.connectivity.adapter.platform.client.api.InteractWithPartnerCommonApi"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/>
        <property name="remoteAppkey" value="com.sankuai.connectivity.adapter.platform"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="4500"/>
    </bean>
    <!-- 职人分享赚状态查询 -->
    <pigeon:reference id="techSignUpQueryService"
                      timeout="5000"
                      url="com.sankuai.technician.process.distribute.service.TechDistributionSignUpQueryActicityService"
                      interface="com.sankuai.technician.process.distribute.service.TechDistributionSignUpQueryActicityService"/>
    <pigeon:reference id="commonProductQueryService"
                      timeout="5000"
                      url="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonProductQueryService"
                      interface="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonProductQueryService"/>
    <pigeon:reference id="productActionService"
                      timeout="5000"
                      url="com.sankuai.medicalcosmetology.product.selectify.api.service.ProductActionService"
                      interface="com.sankuai.medicalcosmetology.product.selectify.api.service.ProductActionService"/>
    <!--微信零钱发放 -->
    <bean id="transferService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="timeout" value="2000"/> <!-- thrift rpc 超时时间（毫秒） -->
        <property name="retryRequest" value="true"/> <!-- 请求失败后是否重试, 默认为 true -->
        <property name="appKey"
                  value="com.sankuai.medicalcosmetology.distribution.service"/> <!-- 本地 appkey, MIX/OCTO 模式下必须配置 -->
        <property name="remoteAppkey"
                  value="com.sankuai.payment.fundstransfer"/>  <!-- 目标 Server Appkey, MIX/OCTO 模式下必须配置  -->
        <property name="serviceInterface"
                  value="com.meituan.payment.fundstransfer.thrift.idl.TransferService"/> <!-- service接口名, ZK/MIX 模式下不需配置 -->
        <property name="remoteServerPort" value="9001"/>
    </bean>

    <!-- 门店所属业务线判断 -->
    <bean id="territoryShopService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean"
          init-method="init">
        <property name="url" value="http://service.dianping.com/rotate/territory/TerritoryShopService_0.0.1"/>
        <property name="interfaceName" value="com.dianping.rotate.territory.api.TerritoryShopService"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- 职人门店关系 -->
    <pigeon:reference id="techShopRelationQueryService"
                      timeout="1000"
                      interface="com.sankuai.technician.techshop.service.TechShopRelationQueryService"
                      url="com.sankuai.technician.techshop.service.TechShopRelationQueryService"/>

    <!-- 商户后台类目 poi升级 -->
    <bean id="newPoiShopCategoryQueryService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName"
                  value="http://service.dianping.com/poi-shopcateprop-service/newPoiShopCategoryQueryService_1.0.0"/>
        <property name="iface" value="com.dianping.poi.shopcateprop.api.service.NewPoiShopCategoryQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <!-- 交易订单更新扩展字段 -->
    <bean id="apiGroupBuyOrderService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.mptrade.groupbuy.thrift.api.service.ApiGroupBuyOrderService"/>
        <property name="remoteAppkey" value="com.sankuai.mptrade.groupbuy.order"/>
        <property name="remoteServerPort" value="8410"/>
        <property name="timeout" value="1000"/>
    </bean>
    <!--计费域接口-->
    <pigeon:reference id="privateLiveSettleService"
                      timeout="5000"
                      interface="com.sankuai.technician.trade.api.settle.solution.privatelive.PrivateLiveSettleService"
                      url="com.sankuai.technician.trade.api.settle.solution.privatelive.PrivateLiveSettleService"/>
<!--    分销计划域接口-->
    <pigeon:reference id="distributionPlanQueryService"
                      timeout="5000"
                      interface="com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService"
                      url="com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanQueryService"/>


    <pigeon:reference id="distributionPlanCommandService"
                      timeout="5000"
                      interface="com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService"
                      url="com.sankuai.dzusergrowth.distribution.plan.api.service.DistributionPlanCommandService"/>


    <!-- 收单 -->
    <pigeon:reference id="orderSyncService"
                      timeout="1000"
                      interface="com.sankuai.technician.trade.api.order.application.service.OrderSyncService"
                      url="com.sankuai.technician.trade.api.order.application.service.OrderSyncService"/>

    <pigeon:reference id="orderCommissionRateQueryService"
                      timeout="1000"
                      interface="com.dianping.gmkt.event.api.commission.service.OrderCommissionRateQueryService"
                      url="com.dianping.gmkt.event.api.commission.service.OrderCommissionRateQueryService"/>

    <!-- 彩虹楼层查询服务 -->
    <bean id="dataPoolImportQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/mpmktdata/dataPoolQueryService_1.0.0"/>
        <property name="interfaceName" value="com.meituan.mpmkt.data.pool.api.service.DataPoolImportQueryService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>

    <!-- uap规则流转历史查询   -->
    <bean id="processService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/gb-audit-platform-longprocessservice_1.0.0" />
        <property name="interfaceName" value="com.dianping.gb.audit.platform.biz.longtypeservice.ProcessService" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
        <property name="timeout" value="5000" />
    </bean>



    <!--计费域接口-->
    <pigeon:reference id="privateLiveSettleService"
                      timeout="5000"
                      interface="com.sankuai.technician.trade.api.settle.solution.privatelive.PrivateLiveSettleService"
                      url="com.sankuai.technician.trade.api.settle.solution.privatelive.PrivateLiveSettleService"/>
    <!-- 收单 -->
    <pigeon:reference id="orderSyncService"
                      timeout="1000"
                      interface="com.sankuai.technician.trade.api.order.application.service.OrderSyncService"
                      url="com.sankuai.technician.trade.api.order.application.service.OrderSyncService"/>

    <pigeon:reference id="requisitionCommandService"
                      timeout="5000"
                      interface="com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionCommandService"
                      url="com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionCommandService"/>

    <pigeon:reference id="requisitionQueryService"
                      timeout="5000"
                      interface="com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService"
                      url="com.sankuai.dzusergrowth.distribution.plan.api.service.RequisitionQueryService"/>

<!--    uac-->
    <bean id="uacAuthRemoteService" class="com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService">
        <property name="uacHost" ref="uacHost" />
        <property name="uacClientId" ref="uacClientId" />
        <property name="uacSecret" ref="uacSecret" />
    </bean>

    <bean id="uacHost" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service" />
        <property name="name" value="uacHost" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="uacClientId" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service" />
        <property name="name" value="uacClientId" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="uacSecret" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service" />
        <property name="name" value="uacSecret" />
        <property name="retryCount" value="10" />
    </bean>
    <bean id="mwallletProxyClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="timeout" value="3000"/>
        <property name="retryRequest" value="true"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/>
        <property name="remoteAppkey" value="com.sankuai.pay.merchantproduct.mwallet"/>
        <property name="remoteServerPort" value="3405"/>
        <property name="serviceInterface" value="com.meituan.pay.mwallet.proxy.thrift.MwalletProxyService"/>
        <property name="isImplFacebookService" value="true" />
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>
    <bean id="statementQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="timeout" value="3000"/>
        <property name="retryRequest" value="true"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.distribution.service"/>
        <property name="remoteAppkey" value="com.sankuai.dzusergrowth.distribution.settle"/>
        <property name="serviceInterface" value="com.sankuai.dzusergrowth.distribution.settle.api.service.StatementQueryService"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>
</beans>