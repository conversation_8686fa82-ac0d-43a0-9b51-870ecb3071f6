<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:pigeon="http://code.dianping.com/schema/pigeon"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:crane="http://code.dianping.com/schema/crane"
       xsi:schemaLocation="
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://code.dianping.com/schema/pigeon http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd
        http://code.dianping.com/schema/crane
        http://code.dianping.com/schema/crane/crane-1.0.xsd">

    <context:annotation-config/>
    <!-- 启用aop -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <crane:annotation-driven />

    <bean name="placeholder" lazy-init="false" class="com.dianping.lion.client.SpringConfig">
        <property name="propertiesPath" value="config/applicationContext.properties"/>
    </bean>

    <import resource="applicationContext-client.xml"/>
    <import resource="applicationContext-redis.xml"/>
    <import resource="applicationContext-dao.xml"/>
    <import resource="classpath*:/spring/beauty/arch/user/beauty-user.xml"/>
    <import resource="classpath*:/spring/beauty/talos/beauty-talos.xml"/>
    <import resource="classpath*:/spring/beauty/arch/idmapper/beauty-idmapper.xml"/>

    <pigeon:annotation package="com.sankuai.carnation.distribution"/>

    <context:component-scan base-package="com.sankuai.carnation.distribution"/>
    <context:component-scan base-package="com.meituan.beauty.fundamental.crow"/>
    <context:component-scan base-package="com.dianping.haima,com.dianping.appkit"/>
</beans>
