<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroup">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="bind_user_type" jdbcType="INTEGER" property="bindUserType" />
    <result column="bind_user_id" jdbcType="BIGINT" property="bindUserId" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="bind_strategy" jdbcType="INTEGER" property="bindStrategy" />
    <result column="principal" jdbcType="VARCHAR" property="principal" />
    <result column="principal_mobile" jdbcType="VARCHAR" property="principalMobile" />
    <result column="contract" jdbcType="VARCHAR" property="contract" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="license" jdbcType="VARCHAR" property="license" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, type, group_code, bind_user_type, bind_user_id, pay_type, bind_strategy, 
    principal, principal_mobile, contract, company_name, credit_code, license, status, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_group
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from distributor_group
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupCriteria">
    delete from distributor_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroup">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_group (name, type, group_code, 
      bind_user_type, bind_user_id, pay_type, 
      bind_strategy, principal, principal_mobile, 
      contract, company_name, credit_code, 
      license, status, update_time
      )
    values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{groupCode,jdbcType=VARCHAR}, 
      #{bindUserType,jdbcType=INTEGER}, #{bindUserId,jdbcType=BIGINT}, #{payType,jdbcType=INTEGER}, 
      #{bindStrategy,jdbcType=INTEGER}, #{principal,jdbcType=VARCHAR}, #{principalMobile,jdbcType=VARCHAR}, 
      #{contract,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{creditCode,jdbcType=VARCHAR}, 
      #{license,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroup">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="groupCode != null">
        group_code,
      </if>
      <if test="bindUserType != null">
        bind_user_type,
      </if>
      <if test="bindUserId != null">
        bind_user_id,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="bindStrategy != null">
        bind_strategy,
      </if>
      <if test="principal != null">
        principal,
      </if>
      <if test="principalMobile != null">
        principal_mobile,
      </if>
      <if test="contract != null">
        contract,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="creditCode != null">
        credit_code,
      </if>
      <if test="license != null">
        license,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="groupCode != null">
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="bindUserType != null">
        #{bindUserType,jdbcType=INTEGER},
      </if>
      <if test="bindUserId != null">
        #{bindUserId,jdbcType=BIGINT},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="bindStrategy != null">
        #{bindStrategy,jdbcType=INTEGER},
      </if>
      <if test="principal != null">
        #{principal,jdbcType=VARCHAR},
      </if>
      <if test="principalMobile != null">
        #{principalMobile,jdbcType=VARCHAR},
      </if>
      <if test="contract != null">
        #{contract,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="license != null">
        #{license,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupCriteria" resultType="java.lang.Long">
    select count(*) from distributor_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.groupCode != null">
        group_code = #{record.groupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bindUserType != null">
        bind_user_type = #{record.bindUserType,jdbcType=INTEGER},
      </if>
      <if test="record.bindUserId != null">
        bind_user_id = #{record.bindUserId,jdbcType=BIGINT},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=INTEGER},
      </if>
      <if test="record.bindStrategy != null">
        bind_strategy = #{record.bindStrategy,jdbcType=INTEGER},
      </if>
      <if test="record.principal != null">
        principal = #{record.principal,jdbcType=VARCHAR},
      </if>
      <if test="record.principalMobile != null">
        principal_mobile = #{record.principalMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.contract != null">
        contract = #{record.contract,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.creditCode != null">
        credit_code = #{record.creditCode,jdbcType=VARCHAR},
      </if>
      <if test="record.license != null">
        license = #{record.license,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_group
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      group_code = #{record.groupCode,jdbcType=VARCHAR},
      bind_user_type = #{record.bindUserType,jdbcType=INTEGER},
      bind_user_id = #{record.bindUserId,jdbcType=BIGINT},
      pay_type = #{record.payType,jdbcType=INTEGER},
      bind_strategy = #{record.bindStrategy,jdbcType=INTEGER},
      principal = #{record.principal,jdbcType=VARCHAR},
      principal_mobile = #{record.principalMobile,jdbcType=VARCHAR},
      contract = #{record.contract,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      credit_code = #{record.creditCode,jdbcType=VARCHAR},
      license = #{record.license,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroup">
    update distributor_group
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="groupCode != null">
        group_code = #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="bindUserType != null">
        bind_user_type = #{bindUserType,jdbcType=INTEGER},
      </if>
      <if test="bindUserId != null">
        bind_user_id = #{bindUserId,jdbcType=BIGINT},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="bindStrategy != null">
        bind_strategy = #{bindStrategy,jdbcType=INTEGER},
      </if>
      <if test="principal != null">
        principal = #{principal,jdbcType=VARCHAR},
      </if>
      <if test="principalMobile != null">
        principal_mobile = #{principalMobile,jdbcType=VARCHAR},
      </if>
      <if test="contract != null">
        contract = #{contract,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        credit_code = #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="license != null">
        license = #{license,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroup">
    update distributor_group
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      group_code = #{groupCode,jdbcType=VARCHAR},
      bind_user_type = #{bindUserType,jdbcType=INTEGER},
      bind_user_id = #{bindUserId,jdbcType=BIGINT},
      pay_type = #{payType,jdbcType=INTEGER},
      bind_strategy = #{bindStrategy,jdbcType=INTEGER},
      principal = #{principal,jdbcType=VARCHAR},
      principal_mobile = #{principalMobile,jdbcType=VARCHAR},
      contract = #{contract,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      credit_code = #{creditCode,jdbcType=VARCHAR},
      license = #{license,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>