<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupBindMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="distributor_group" jdbcType="INTEGER" property="distributorGroup" />
    <result column="distributor" jdbcType="BIGINT" property="distributor" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, distributor_group, distributor, status, update_time, add_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBindCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_group_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_group_bind
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distributor_group_bind
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBindCriteria">
    delete from distributor_group_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind" useGeneratedKeys="true">
    insert into distributor_group_bind (distributor_group, distributor, status, 
      update_time, add_time)
    values (#{distributorGroup,jdbcType=INTEGER}, #{distributor,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind" useGeneratedKeys="true">
    insert into distributor_group_bind
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributorGroup != null">
        distributor_group,
      </if>
      <if test="distributor != null">
        distributor,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributorGroup != null">
        #{distributorGroup,jdbcType=INTEGER},
      </if>
      <if test="distributor != null">
        #{distributor,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBindCriteria" resultType="java.lang.Long">
    select count(*) from distributor_group_bind
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_group_bind
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.distributorGroup != null">
        distributor_group = #{record.distributorGroup,jdbcType=INTEGER},
      </if>
      <if test="record.distributor != null">
        distributor = #{record.distributor,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_group_bind
    set id = #{record.id,jdbcType=BIGINT},
      distributor_group = #{record.distributorGroup,jdbcType=INTEGER},
      distributor = #{record.distributor,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind">
    update distributor_group_bind
    <set>
      <if test="distributorGroup != null">
        distributor_group = #{distributorGroup,jdbcType=INTEGER},
      </if>
      <if test="distributor != null">
        distributor = #{distributor,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupBind">
    update distributor_group_bind
    set distributor_group = #{distributorGroup,jdbcType=INTEGER},
      distributor = #{distributor,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>