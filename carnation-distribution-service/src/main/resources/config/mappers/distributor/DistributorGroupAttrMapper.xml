<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupAttrMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="attr_name" jdbcType="VARCHAR" property="attrName" />
    <result column="attr_value_type" jdbcType="INTEGER" property="attrValueType" />
    <result column="attr_value" jdbcType="VARCHAR" property="attrValue" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_id, attr_name, attr_value_type, attr_value, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttrCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_group_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_group_attr
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distributor_group_attr
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttrCriteria">
    delete from distributor_group_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into distributor_group_attr
    (group_id, attr_name, attr_value_type,
    attr_value, status, add_time,
    update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groupId,jdbcType=INTEGER}, #{item.attrName,jdbcType=VARCHAR}, #{item.attrValueType,jdbcType=INTEGER},
      #{item.attrValue,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_group_attr (group_id, attr_name, attr_value_type, 
      attr_value, status, add_time, 
      update_time)
    values (#{groupId,jdbcType=INTEGER}, #{attrName,jdbcType=VARCHAR}, #{attrValueType,jdbcType=INTEGER}, 
      #{attrValue,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_group_attr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        group_id,
      </if>
      <if test="attrName != null">
        attr_name,
      </if>
      <if test="attrValueType != null">
        attr_value_type,
      </if>
      <if test="attrValue != null">
        attr_value,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="attrName != null">
        #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrValueType != null">
        #{attrValueType,jdbcType=INTEGER},
      </if>
      <if test="attrValue != null">
        #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttrCriteria" resultType="java.lang.Long">
    select count(*) from distributor_group_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_group_attr
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=INTEGER},
      </if>
      <if test="record.attrName != null">
        attr_name = #{record.attrName,jdbcType=VARCHAR},
      </if>
      <if test="record.attrValueType != null">
        attr_value_type = #{record.attrValueType,jdbcType=INTEGER},
      </if>
      <if test="record.attrValue != null">
        attr_value = #{record.attrValue,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_group_attr
    set id = #{record.id,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=INTEGER},
      attr_name = #{record.attrName,jdbcType=VARCHAR},
      attr_value_type = #{record.attrValueType,jdbcType=INTEGER},
      attr_value = #{record.attrValue,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr">
    update distributor_group_attr
    <set>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="attrName != null">
        attr_name = #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrValueType != null">
        attr_value_type = #{attrValueType,jdbcType=INTEGER},
      </if>
      <if test="attrValue != null">
        attr_value = #{attrValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupAttr">
    update distributor_group_attr
    set group_id = #{groupId,jdbcType=INTEGER},
      attr_name = #{attrName,jdbcType=VARCHAR},
      attr_value_type = #{attrValueType,jdbcType=INTEGER},
      attr_value = #{attrValue,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>