<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributor.repository.dao.ConsultantAccountMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributor.repository.db.ConsultantAccount">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="consultant_account_name" jdbcType="VARCHAR" property="consultantAccountName" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="origin_customer_id" jdbcType="BIGINT" property="originCustomerId" />
        <result column="account_id" jdbcType="BIGINT" property="accountId" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, consultant_account_name, customer_id,origin_customer_id, account_id, shop_id, distributor_id, mobile, status, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.ConsultantAccount" useGeneratedKeys="true" keyProperty="id">
        insert into consultant_account
        (<include refid="Base_Column_List" />)
        values (#{id,jdbcType=BIGINT}, #{consultantAccountName,jdbcType=VARCHAR}, #{customerId,jdbcType=BIGINT},
        #{originCustomerId,jdbcType=BIGINT},#{accountId,jdbcType=BIGINT},#{shopId,jdbcType=BIGINT},
         #{distributorId,jdbcType=BIGINT},#{mobile,jdbcType=VARCHAR},#{status,jdbcType=INTEGER},
         #{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>


    <select id="selectByShopId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from consultant_account
        where customer_id = #{customerId,jdbcType=BIGINT} and shop_id = #{shopId,jdbcType=BIGINT} and status = #{status,jdbcType=INTEGER}
        order by id limit #{startNum,jdbcType=INTEGER},#{size,jdbcType=INTEGER}
    </select>

    <select id="selectAllByShopId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from consultant_account
        where shop_id = #{shopId,jdbcType=BIGINT}
    </select>

    <select id="countByShopIdAndName" resultType="java.lang.Integer">
        select count(*) from consultant_account
        where shop_id = #{shopId,jdbcType=BIGINT} and consultant_account_name = #{consultantAccountName,jdbcType=VARCHAR} and status = #{status,jdbcType=INTEGER}
    </select>

    <select id="countByShopId" resultType="java.lang.Integer" >
        select count(*) from consultant_account
        where customer_id = #{customerId,jdbcType=BIGINT} and shop_id = #{shopId,jdbcType=BIGINT} and status = #{status,jdbcType=INTEGER}
    </select>

    <select id="selectByDistributorId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from consultant_account
        where distributor_id = #{distributorId,jdbcType=BIGINT}
    </select>

    <update id="updateByDistributorId" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.ConsultantAccount">
        update consultant_account
        <set>
            <if test="consultantAccountName != null">
                consultant_account_name = #{consultantAccountName,jdbcType=VARCHAR},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where distributor_id = #{distributorId,jdbcType=BIGINT}
    </update>

    <delete id="deleteByDistributorId" parameterType="java.lang.Long">
    delete from consultant_account
    where distributor_id = #{distributorId,jdbcType=BIGINT}
  </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from consultant_account
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from consultant_account
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            (#{id})
        </foreach>
        order by id
    </select>
</mapper>