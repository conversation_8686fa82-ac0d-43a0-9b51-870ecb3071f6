<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributor.repository.dao.DistributorBindWhiteListMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteList">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="distributor_group" jdbcType="INTEGER" property="distributorGroup" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, distributor_group, user_type, user_id, status, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteListCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_bind_white_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_bind_white_list
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from distributor_bind_white_list
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteListCriteria">
    delete from distributor_bind_white_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_bind_white_list (distributor_group, user_type, user_id, 
      status, update_time)
    values (#{distributorGroup,jdbcType=INTEGER}, #{userType,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_bind_white_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributorGroup != null">
        distributor_group,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributorGroup != null">
        #{distributorGroup,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteListCriteria" resultType="java.lang.Long">
    select count(*) from distributor_bind_white_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_bind_white_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.distributorGroup != null">
        distributor_group = #{record.distributorGroup,jdbcType=INTEGER},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_bind_white_list
    set id = #{record.id,jdbcType=INTEGER},
      distributor_group = #{record.distributorGroup,jdbcType=INTEGER},
      user_type = #{record.userType,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteList">
    update distributor_bind_white_list
    <set>
      <if test="distributorGroup != null">
        distributor_group = #{distributorGroup,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorBindWhiteList">
    update distributor_bind_white_list
    set distributor_group = #{distributorGroup,jdbcType=INTEGER},
      user_type = #{userType,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>