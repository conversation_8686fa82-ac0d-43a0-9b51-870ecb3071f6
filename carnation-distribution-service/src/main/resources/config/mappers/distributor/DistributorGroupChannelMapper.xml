<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributor.repository.dao.DistributorGroupChannelMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannel">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="distributor_group_id" jdbcType="INTEGER" property="distributorGroupId" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, distributor_group_id, channel, status, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannelCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_group_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_group_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from distributor_group_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannelCriteria">
    delete from distributor_group_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_group_channel (distributor_group_id, channel, status, 
      update_time)
    values (#{distributorGroupId,jdbcType=INTEGER}, #{channel,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannel">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distributor_group_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributorGroupId != null">
        distributor_group_id,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributorGroupId != null">
        #{distributorGroupId,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannelCriteria" resultType="java.lang.Long">
    select count(*) from distributor_group_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_group_channel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.distributorGroupId != null">
        distributor_group_id = #{record.distributorGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_group_channel
    set id = #{record.id,jdbcType=INTEGER},
      distributor_group_id = #{record.distributorGroupId,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannel">
    update distributor_group_channel
    <set>
      <if test="distributorGroupId != null">
        distributor_group_id = #{distributorGroupId,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.distributor.repository.db.DistributorGroupChannel">
    update distributor_group_channel
    set distributor_group_id = #{distributorGroupId,jdbcType=INTEGER},
      channel = #{channel,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>