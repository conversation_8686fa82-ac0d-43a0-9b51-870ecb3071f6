<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributionplan.repository.dao.DistributionProductPushMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionProductPushEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_type" jdbcType="TINYINT" property="productType" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="online_time" jdbcType="TIMESTAMP" property="onlineTime" />
    <result column="offline_time" jdbcType="TIMESTAMP" property="offlineTime" />
    <result column="push_result" jdbcType="VARCHAR" property="pushResult" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, product_id, product_type, channel, online_time, offline_time, push_result, status
  </sql>

  <insert id="insertDistributionProductPush" parameterType="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionProductPushEntity">
    insert into distribution_product_push
    (product_id, product_type, channel, online_time, offline_time, push_result, status)
    values
    (#{productId}, #{productType}, #{channel}, #{onlineTime}, #{offlineTime}, #{pushResult}, #{status})
  </insert>

    <insert id="batchInsertDistributionProductPush">
      insert into distribution_product_push
      (product_id, product_type, channel, online_time, offline_time, push_result, status)
      values
      <foreach collection="entities" item="entity" separator=",">
        (#{entity.productId}, #{entity.productType}, #{entity.channel}, #{entity.onlineTime}, #{entity.offlineTime}, #{entity.pushResult}, #{entity.status})
      </foreach>
    </insert>

    <update id="updateDistributionProductPush" parameterType="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionProductPushEntity">
    update distribution_product_push
      <set>
        <if test="onlineTime != null">
          online_time = #{onlineTime},
        </if>
        <if test="offlineTime != null">
          offline_time = #{offlineTime},
        </if>
        <if test="pushResult != null and pushResult != ''">
          push_result = #{pushResult},
        </if>
        <if test="status != null">
          status = #{status},
        </if>
      </set>
      where id = #{id}
    </update>

    <update id="batchUpdateDistributionProductPushStatus">
      <foreach collection="entities" separator=";" item="entity">
        update
            distribution_product_push
        <set>
          <if test="entity.onlineTime != null">
            online_time = #{entity.onlineTime},
          </if>
          <if test="entity.offlineTime != null">
            offline_time = #{entity.offlineTime},
          </if>
          <if test="entity.pushResult != null and entity.pushResult != ''">
            push_result = #{entity.pushResult},
          </if>
          <if test="entity.status != null">
            status = #{entity.status},
          </if>
        </set>
        where id = #{entity.id}
      </foreach>
    </update>

    <select id="loadByDistributionProductPush" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from distribution_product_push
    where product_id = #{productId}
    and product_type = #{productType}
    and channel = #{channel}
  </select>

  <select id="queryDistributionProductPush" resultMap="BaseResultMap"
          parameterType="com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionProductPushQueryRequest">
    select
        <include refid="Base_Column_List"/>
    from distribution_product_push
    where product_id in
    <foreach collection="productIds" separator="," open="(" close=")" item="productId">
      #{productId}
    </foreach>
    <if test="channelList != null and channelList.size > 0">
      and channel in
      <foreach collection="channelList" separator="," open="(" close=")" item="channel">
        #{channel}
      </foreach>
    </if>
    <if test="productTypeList != null and productTypeList.size > 0">
      and product_type in
      <foreach collection="productTypeList" separator="," open="(" close=")" item="productType">
        #{productType}
      </foreach>
    </if>
  </select>
</mapper>