<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributionplan.repository.dao.DistributionPlanMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanEntity">
    <id column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="industry_type" jdbcType="INTEGER" property="industryType" />
    <result column="plan_type" jdbcType="INTEGER" property="planType" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="pre_begin_time" jdbcType="TIMESTAMP" property="preBeginTime" />
    <result column="pre_end_time" jdbcType="TIMESTAMP" property="preEndTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="commission_strategy" jdbcType="VARCHAR" property="commissionStrategy" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    plan_id, industry_type, plan_type, plan_name, channel, status, pre_begin_time, pre_end_time, end_time, product_id, product_type, commission_strategy
  </sql>

  <insert id="insertDistributionPlan" parameterType="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanEntity"
          keyProperty="planId" useGeneratedKeys="true" keyColumn="plan_id">
    insert into distribution_plan
    (industry_type, plan_type, plan_name, channel, status,
    pre_begin_time, pre_end_time, end_time, product_id, product_type, commission_strategy)
    values
    (#{industryType}, #{planType}, #{planName}, #{channel}, #{status},
    #{preBeginTime}, #{preEndTime},  #{endTime}, #{productId}, #{productType}, #{commissionStrategy})
  </insert>

  <update id="updateDistributionPlanStatus" parameterType="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanEntity">
    update distribution_plan
    <set>
        <if test="endTime != null">
            end_time = #{endTime},
        </if>
        status = #{status}
    </set>
    where is_deleted = 0
    and plan_id = #{planId}
  </update>

  <update id="updateDistributionPlan" parameterType="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanEntity">
    update distribution_plan
    <set>
      <if test="planName != null">
        plan_name = #{planName},
      </if>
      <if test="preBeginTime != null">
        pre_begin_time = #{preBeginTime},
      </if>
      <if test="preEndTime != null">
        pre_end_time = #{preEndTime},
      </if>
      <if test="commissionStrategy != null and commissionStrategy != ''">
        commission_strategy = #{commissionStrategy}
      </if>
    </set>
    where is_deleted = 0
    and plan_id = #{planId}
  </update>

  <select id="pageQueryDistributionPlan" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_plan
    where is_deleted = 0
    and channel = #{pageQuery.channel}
    <if test="pageQuery.productId != null">
      and product_id = #{pageQuery.productId}
    </if>
    <if test="pageQuery.productType != null">
      and product_type = #{pageQuery.productType}
    </if>
    <if test="pageQuery.statusList != null and pageQuery.statusList.size > 0">
      and status in
      <foreach collection="pageQuery.statusList" separator="," close=")" open="(" item="item">
        #{item}
      </foreach>
    </if>
    order by plan_id desc
    limit #{limit}
    offset #{offset}
  </select>

  <select id="loadDistributionPlanByPlanId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_plan
    where is_deleted = 0
    and plan_id = #{planId}
  </select>

  <select id="queryDistributionPlanByPlanIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_plan
    where is_deleted = 0
    and plan_id in
    <foreach collection="planIds" item="planId" open="(" close=")" separator=",">
      #{planId}
    </foreach>
  </select>

  <select id="queryDistributionPlanByRequest" resultMap="BaseResultMap" parameterType="com.sankuai.carnation.distribution.distributionplan.domain.model.DistributionPlanQueryRequest">
    select
    <include refid="Base_Column_List"/>
    from distribution_plan
    where is_deleted = 0
    and channel = #{channel}
    and product_type = #{productType}
    and product_id in
    <foreach collection="productIds" separator="," close=")" open="(" item="productId">
      #{productId}
    </foreach>
    <if test="statusList != null and statusList.size > 0">
      and status in
      <foreach collection="statusList" separator="," close=")" open="(" item="item">
         #{item}
      </foreach>
    </if>
  </select>

  <select id="countDistributionPlan" resultType="java.lang.Long">
    select count(1)
    from distribution_plan
    where is_deleted = 0
    and channel = #{channel}
    <if test="productId != null">
      and product_id = #{productId}
    </if>
    <if test="productType != null">
      and product_type = #{productType}
    </if>
    <if test="statusList != null and statusList.size > 0">
      and status in
      <foreach collection="statusList" separator="," close=")" open="(" item="item">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>