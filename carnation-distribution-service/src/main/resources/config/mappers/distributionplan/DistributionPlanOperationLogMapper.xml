<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributionplan.repository.dao.DistributionPlanOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanOperationLogEntity">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="plan_id" jdbcType="BIGINT" property="planId"/>
    <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
    <result column="operation_content" jdbcType="INTEGER" property="operationContent"/>
    <result column="operator_type" jdbcType="INTEGER" property="operatorType"/>
    <result column="operator" jdbcType="INTEGER" property="operator"/>
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, plan_id, operation_type, operation_content, operatorType, operator
  </sql>

  <insert id="insertDistributionPlanOperationLog" parameterType="com.sankuai.carnation.distribution.distributionplan.repository.entity.DistributionPlanOperationLogEntity">
    insert into distribution_plan_operation_log
    (plan_id, operation_type, operation_content, operator_type, operator, add_time, update_time)
    values
    (#{planId}, #{operationType}, #{operationContent}, #{operatorType}, #{operator}, now(), now())
  </insert>
</mapper>