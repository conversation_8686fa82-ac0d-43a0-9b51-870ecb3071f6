<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.commisson.repository.dao.CommManualShopFinalCommissionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.commisson.repository.db.CommManualShopFinalCommission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="income_commission" jdbcType="DECIMAL" property="incomeCommission" />
    <result column="rebate_commission" jdbcType="DECIMAL" property="rebateCommission" />
    <result column="final_commission" jdbcType="DECIMAL" property="finalCommission" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="place_order_end_time" jdbcType="TIMESTAMP" property="placeOrderEndTime" />
    <result column="coupon_verify_end_time" jdbcType="TIMESTAMP" property="couponVerifyEndTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dp_shop_id, income_commission, rebate_commission, final_commission, start_time, 
    place_order_end_time, coupon_verify_end_time, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommManualShopFinalCommissionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from comm_manual_shop_final_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_manual_shop_final_commission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_manual_shop_final_commission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommManualShopFinalCommissionExample">
    delete from comm_manual_shop_final_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommManualShopFinalCommission" useGeneratedKeys="true">
    insert into comm_manual_shop_final_commission (dp_shop_id, income_commission, rebate_commission, 
      final_commission, start_time, place_order_end_time, 
      coupon_verify_end_time, status, add_time, 
      update_time)
    values (#{dpShopId,jdbcType=BIGINT}, #{incomeCommission,jdbcType=DECIMAL}, #{rebateCommission,jdbcType=DECIMAL}, 
      #{finalCommission,jdbcType=DECIMAL}, #{startTime,jdbcType=TIMESTAMP}, #{placeOrderEndTime,jdbcType=TIMESTAMP}, 
      #{couponVerifyEndTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommManualShopFinalCommission" useGeneratedKeys="true">
    insert into comm_manual_shop_final_commission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="incomeCommission != null">
        income_commission,
      </if>
      <if test="rebateCommission != null">
        rebate_commission,
      </if>
      <if test="finalCommission != null">
        final_commission,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="placeOrderEndTime != null">
        place_order_end_time,
      </if>
      <if test="couponVerifyEndTime != null">
        coupon_verify_end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="incomeCommission != null">
        #{incomeCommission,jdbcType=DECIMAL},
      </if>
      <if test="rebateCommission != null">
        #{rebateCommission,jdbcType=DECIMAL},
      </if>
      <if test="finalCommission != null">
        #{finalCommission,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="placeOrderEndTime != null">
        #{placeOrderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="couponVerifyEndTime != null">
        #{couponVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommManualShopFinalCommissionExample" resultType="java.lang.Long">
    select count(*) from comm_manual_shop_final_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update comm_manual_shop_final_commission
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.incomeCommission != null">
        income_commission = #{record.incomeCommission,jdbcType=DECIMAL},
      </if>
      <if test="record.rebateCommission != null">
        rebate_commission = #{record.rebateCommission,jdbcType=DECIMAL},
      </if>
      <if test="record.finalCommission != null">
        final_commission = #{record.finalCommission,jdbcType=DECIMAL},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.placeOrderEndTime != null">
        place_order_end_time = #{record.placeOrderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.couponVerifyEndTime != null">
        coupon_verify_end_time = #{record.couponVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update comm_manual_shop_final_commission
    set id = #{record.id,jdbcType=BIGINT},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      income_commission = #{record.incomeCommission,jdbcType=DECIMAL},
      rebate_commission = #{record.rebateCommission,jdbcType=DECIMAL},
      final_commission = #{record.finalCommission,jdbcType=DECIMAL},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      place_order_end_time = #{record.placeOrderEndTime,jdbcType=TIMESTAMP},
      coupon_verify_end_time = #{record.couponVerifyEndTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommManualShopFinalCommission">
    update comm_manual_shop_final_commission
    <set>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="incomeCommission != null">
        income_commission = #{incomeCommission,jdbcType=DECIMAL},
      </if>
      <if test="rebateCommission != null">
        rebate_commission = #{rebateCommission,jdbcType=DECIMAL},
      </if>
      <if test="finalCommission != null">
        final_commission = #{finalCommission,jdbcType=DECIMAL},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="placeOrderEndTime != null">
        place_order_end_time = #{placeOrderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="couponVerifyEndTime != null">
        coupon_verify_end_time = #{couponVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommManualShopFinalCommission">
    update comm_manual_shop_final_commission
    set dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      income_commission = #{incomeCommission,jdbcType=DECIMAL},
      rebate_commission = #{rebateCommission,jdbcType=DECIMAL},
      final_commission = #{finalCommission,jdbcType=DECIMAL},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      place_order_end_time = #{placeOrderEndTime,jdbcType=TIMESTAMP},
      coupon_verify_end_time = #{couponVerifyEndTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into comm_manual_shop_final_commission
    (dp_shop_id, income_commission, rebate_commission, final_commission, start_time, 
      place_order_end_time, coupon_verify_end_time, status, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dpShopId,jdbcType=BIGINT}, #{item.incomeCommission,jdbcType=DECIMAL}, #{item.rebateCommission,jdbcType=DECIMAL}, 
        #{item.finalCommission,jdbcType=DECIMAL}, #{item.startTime,jdbcType=TIMESTAMP}, 
        #{item.placeOrderEndTime,jdbcType=TIMESTAMP}, #{item.couponVerifyEndTime,jdbcType=TIMESTAMP}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>