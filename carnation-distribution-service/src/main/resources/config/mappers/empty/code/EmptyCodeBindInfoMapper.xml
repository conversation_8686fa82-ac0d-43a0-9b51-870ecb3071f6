<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.empty.code.bind.repository.dao.EmptyCodeBindInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="code_source" jdbcType="INTEGER" property="codeSource" />
    <result column="code_key" jdbcType="VARCHAR" property="codeKey" />
    <result column="code_link" jdbcType="VARCHAR" property="codeLink" />
    <result column="code_image" jdbcType="VARCHAR" property="codeImage" />
    <result column="bind_key_type" jdbcType="INTEGER" property="bindKeyType" />
    <result column="bind_key" jdbcType="VARCHAR" property="bindKey" />
    <result column="bind_ext_info" jdbcType="VARCHAR" property="bindExtInfo" />
    <result column="operator_type" jdbcType="INTEGER" property="operatorType" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_type, biz_id, code_source, code_key, code_link, code_image, bind_key_type, 
    bind_key, bind_ext_info, operator_type, operator_id, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfoCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from empty_code_bind_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from empty_code_bind_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from empty_code_bind_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfoCriteria">
    delete from empty_code_bind_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into empty_code_bind_info (biz_type, biz_id, code_source, 
      code_key, code_link, code_image, 
      bind_key_type, bind_key, bind_ext_info, 
      operator_type, operator_id, status, 
      add_time, update_time)
    values (#{bizType,jdbcType=INTEGER}, #{bizId,jdbcType=BIGINT}, #{codeSource,jdbcType=INTEGER}, 
      #{codeKey,jdbcType=VARCHAR}, #{codeLink,jdbcType=VARCHAR}, #{codeImage,jdbcType=VARCHAR}, 
      #{bindKeyType,jdbcType=INTEGER}, #{bindKey,jdbcType=VARCHAR}, #{bindExtInfo,jdbcType=VARCHAR}, 
      #{operatorType,jdbcType=INTEGER}, #{operatorId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into empty_code_bind_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="codeSource != null">
        code_source,
      </if>
      <if test="codeKey != null">
        code_key,
      </if>
      <if test="codeLink != null">
        code_link,
      </if>
      <if test="codeImage != null">
        code_image,
      </if>
      <if test="bindKeyType != null">
        bind_key_type,
      </if>
      <if test="bindKey != null">
        bind_key,
      </if>
      <if test="bindExtInfo != null">
        bind_ext_info,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="codeSource != null">
        #{codeSource,jdbcType=INTEGER},
      </if>
      <if test="codeKey != null">
        #{codeKey,jdbcType=VARCHAR},
      </if>
      <if test="codeLink != null">
        #{codeLink,jdbcType=VARCHAR},
      </if>
      <if test="codeImage != null">
        #{codeImage,jdbcType=VARCHAR},
      </if>
      <if test="bindKeyType != null">
        #{bindKeyType,jdbcType=INTEGER},
      </if>
      <if test="bindKey != null">
        #{bindKey,jdbcType=VARCHAR},
      </if>
      <if test="bindExtInfo != null">
        #{bindExtInfo,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfoCriteria" resultType="java.lang.Long">
    select count(*) from empty_code_bind_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update empty_code_bind_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.codeSource != null">
        code_source = #{record.codeSource,jdbcType=INTEGER},
      </if>
      <if test="record.codeKey != null">
        code_key = #{record.codeKey,jdbcType=VARCHAR},
      </if>
      <if test="record.codeLink != null">
        code_link = #{record.codeLink,jdbcType=VARCHAR},
      </if>
      <if test="record.codeImage != null">
        code_image = #{record.codeImage,jdbcType=VARCHAR},
      </if>
      <if test="record.bindKeyType != null">
        bind_key_type = #{record.bindKeyType,jdbcType=INTEGER},
      </if>
      <if test="record.bindKey != null">
        bind_key = #{record.bindKey,jdbcType=VARCHAR},
      </if>
      <if test="record.bindExtInfo != null">
        bind_ext_info = #{record.bindExtInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorType != null">
        operator_type = #{record.operatorType,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update empty_code_bind_info
    set id = #{record.id,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      code_source = #{record.codeSource,jdbcType=INTEGER},
      code_key = #{record.codeKey,jdbcType=VARCHAR},
      code_link = #{record.codeLink,jdbcType=VARCHAR},
      code_image = #{record.codeImage,jdbcType=VARCHAR},
      bind_key_type = #{record.bindKeyType,jdbcType=INTEGER},
      bind_key = #{record.bindKey,jdbcType=VARCHAR},
      bind_ext_info = #{record.bindExtInfo,jdbcType=VARCHAR},
      operator_type = #{record.operatorType,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfo">
    update empty_code_bind_info
    <set>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="codeSource != null">
        code_source = #{codeSource,jdbcType=INTEGER},
      </if>
      <if test="codeKey != null">
        code_key = #{codeKey,jdbcType=VARCHAR},
      </if>
      <if test="codeLink != null">
        code_link = #{codeLink,jdbcType=VARCHAR},
      </if>
      <if test="codeImage != null">
        code_image = #{codeImage,jdbcType=VARCHAR},
      </if>
      <if test="bindKeyType != null">
        bind_key_type = #{bindKeyType,jdbcType=INTEGER},
      </if>
      <if test="bindKey != null">
        bind_key = #{bindKey,jdbcType=VARCHAR},
      </if>
      <if test="bindExtInfo != null">
        bind_ext_info = #{bindExtInfo,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.empty.code.bind.repository.db.EmptyCodeBindInfo">
    update empty_code_bind_info
    set biz_type = #{bizType,jdbcType=INTEGER},
      biz_id = #{bizId,jdbcType=BIGINT},
      code_source = #{codeSource,jdbcType=INTEGER},
      code_key = #{codeKey,jdbcType=VARCHAR},
      code_link = #{codeLink,jdbcType=VARCHAR},
      code_image = #{codeImage,jdbcType=VARCHAR},
      bind_key_type = #{bindKeyType,jdbcType=INTEGER},
      bind_key = #{bindKey,jdbcType=VARCHAR},
      bind_ext_info = #{bindExtInfo,jdbcType=VARCHAR},
      operator_type = #{operatorType,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>