<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.shop.common.repository.dao.ShopPortraitBasicInfoExtMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="cat0_id" jdbcType="INTEGER" property="cat0Id" />
    <result column="cat0_name" jdbcType="VARCHAR" property="cat0Name" />
    <result column="cat1_id" jdbcType="INTEGER" property="cat1Id" />
    <result column="cat1_name" jdbcType="VARCHAR" property="cat1Name" />
    <result column="cat2_id" jdbcType="INTEGER" property="cat2Id" />
    <result column="cat2_name" jdbcType="VARCHAR" property="cat2Name" />
    <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
    <result column="dim_city_rank" jdbcType="VARCHAR" property="dimCityRank" />
    <result column="dp_front_city_id" jdbcType="BIGINT" property="dpFrontCityId" />
    <result column="dp_front_city_name" jdbcType="VARCHAR" property="dpFrontCityName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, update_time, partition_date, poi_id, dp_shop_id, mt_shop_id, cat0_id, cat0_name, 
    cat1_id, cat1_name, cat2_id, cat2_name, shop_type, dim_city_rank, dp_front_city_id,
    dp_front_city_name
  </sql>
  <sql id="sql_selectLatestPartitionDate">
    select partition_date from shop_portrait_basic_info order by partition_date desc limit 1
  </sql>

  <select id="selectInLatestPartitionByExample" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfoCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_portrait_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    and partition_date = (
    <include refid="sql_selectLatestPartitionDate"/>
    )
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
</mapper>