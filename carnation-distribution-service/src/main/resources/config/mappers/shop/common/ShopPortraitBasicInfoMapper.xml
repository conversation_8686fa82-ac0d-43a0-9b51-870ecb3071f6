<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.shop.common.repository.dao.ShopPortraitBasicInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="cat0_id" jdbcType="INTEGER" property="cat0Id" />
    <result column="cat0_name" jdbcType="VARCHAR" property="cat0Name" />
    <result column="cat1_id" jdbcType="INTEGER" property="cat1Id" />
    <result column="cat1_name" jdbcType="VARCHAR" property="cat1Name" />
    <result column="cat2_id" jdbcType="INTEGER" property="cat2Id" />
    <result column="cat2_name" jdbcType="VARCHAR" property="cat2Name" />
    <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
    <result column="dim_city_rank" jdbcType="VARCHAR" property="dimCityRank" />
    <result column="dp_front_city_id" jdbcType="BIGINT" property="dpFrontCityId" />
    <result column="dp_front_city_name" jdbcType="VARCHAR" property="dpFrontCityName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, update_time, partition_date, poi_id, dp_shop_id, mt_shop_id, cat0_id, cat0_name, 
    cat1_id, cat1_name, cat2_id, cat2_name, shop_type, dim_city_rank, dp_front_city_id, 
    dp_front_city_name
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfoCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_portrait_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shop_portrait_basic_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shop_portrait_basic_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfoCriteria">
    delete from shop_portrait_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfo" useGeneratedKeys="true">
    insert into shop_portrait_basic_info (update_time, partition_date, poi_id, 
      dp_shop_id, mt_shop_id, cat0_id, 
      cat0_name, cat1_id, cat1_name, 
      cat2_id, cat2_name, shop_type, 
      dim_city_rank, dp_front_city_id, dp_front_city_name
      )
    values (#{updateTime,jdbcType=TIMESTAMP}, #{partitionDate,jdbcType=VARCHAR}, #{poiId,jdbcType=BIGINT}, 
      #{dpShopId,jdbcType=BIGINT}, #{mtShopId,jdbcType=BIGINT}, #{cat0Id,jdbcType=INTEGER}, 
      #{cat0Name,jdbcType=VARCHAR}, #{cat1Id,jdbcType=INTEGER}, #{cat1Name,jdbcType=VARCHAR}, 
      #{cat2Id,jdbcType=INTEGER}, #{cat2Name,jdbcType=VARCHAR}, #{shopType,jdbcType=VARCHAR}, 
      #{dimCityRank,jdbcType=VARCHAR}, #{dpFrontCityId,jdbcType=BIGINT}, #{dpFrontCityName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfo" useGeneratedKeys="true">
    insert into shop_portrait_basic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="partitionDate != null">
        partition_date,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="cat0Id != null">
        cat0_id,
      </if>
      <if test="cat0Name != null">
        cat0_name,
      </if>
      <if test="cat1Id != null">
        cat1_id,
      </if>
      <if test="cat1Name != null">
        cat1_name,
      </if>
      <if test="cat2Id != null">
        cat2_id,
      </if>
      <if test="cat2Name != null">
        cat2_name,
      </if>
      <if test="shopType != null">
        shop_type,
      </if>
      <if test="dimCityRank != null">
        dim_city_rank,
      </if>
      <if test="dpFrontCityId != null">
        dp_front_city_id,
      </if>
      <if test="dpFrontCityName != null">
        dp_front_city_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="partitionDate != null">
        #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="cat0Id != null">
        #{cat0Id,jdbcType=INTEGER},
      </if>
      <if test="cat0Name != null">
        #{cat0Name,jdbcType=VARCHAR},
      </if>
      <if test="cat1Id != null">
        #{cat1Id,jdbcType=INTEGER},
      </if>
      <if test="cat1Name != null">
        #{cat1Name,jdbcType=VARCHAR},
      </if>
      <if test="cat2Id != null">
        #{cat2Id,jdbcType=INTEGER},
      </if>
      <if test="cat2Name != null">
        #{cat2Name,jdbcType=VARCHAR},
      </if>
      <if test="shopType != null">
        #{shopType,jdbcType=VARCHAR},
      </if>
      <if test="dimCityRank != null">
        #{dimCityRank,jdbcType=VARCHAR},
      </if>
      <if test="dpFrontCityId != null">
        #{dpFrontCityId,jdbcType=BIGINT},
      </if>
      <if test="dpFrontCityName != null">
        #{dpFrontCityName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfoCriteria" resultType="java.lang.Long">
    select count(*) from shop_portrait_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update shop_portrait_basic_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.partitionDate != null">
        partition_date = #{record.partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.mtShopId != null">
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="record.cat0Id != null">
        cat0_id = #{record.cat0Id,jdbcType=INTEGER},
      </if>
      <if test="record.cat0Name != null">
        cat0_name = #{record.cat0Name,jdbcType=VARCHAR},
      </if>
      <if test="record.cat1Id != null">
        cat1_id = #{record.cat1Id,jdbcType=INTEGER},
      </if>
      <if test="record.cat1Name != null">
        cat1_name = #{record.cat1Name,jdbcType=VARCHAR},
      </if>
      <if test="record.cat2Id != null">
        cat2_id = #{record.cat2Id,jdbcType=INTEGER},
      </if>
      <if test="record.cat2Name != null">
        cat2_name = #{record.cat2Name,jdbcType=VARCHAR},
      </if>
      <if test="record.shopType != null">
        shop_type = #{record.shopType,jdbcType=VARCHAR},
      </if>
      <if test="record.dimCityRank != null">
        dim_city_rank = #{record.dimCityRank,jdbcType=VARCHAR},
      </if>
      <if test="record.dpFrontCityId != null">
        dp_front_city_id = #{record.dpFrontCityId,jdbcType=BIGINT},
      </if>
      <if test="record.dpFrontCityName != null">
        dp_front_city_name = #{record.dpFrontCityName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update shop_portrait_basic_info
    set id = #{record.id,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      partition_date = #{record.partitionDate,jdbcType=VARCHAR},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      cat0_id = #{record.cat0Id,jdbcType=INTEGER},
      cat0_name = #{record.cat0Name,jdbcType=VARCHAR},
      cat1_id = #{record.cat1Id,jdbcType=INTEGER},
      cat1_name = #{record.cat1Name,jdbcType=VARCHAR},
      cat2_id = #{record.cat2Id,jdbcType=INTEGER},
      cat2_name = #{record.cat2Name,jdbcType=VARCHAR},
      shop_type = #{record.shopType,jdbcType=VARCHAR},
      dim_city_rank = #{record.dimCityRank,jdbcType=VARCHAR},
      dp_front_city_id = #{record.dpFrontCityId,jdbcType=BIGINT},
      dp_front_city_name = #{record.dpFrontCityName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfo">
    update shop_portrait_basic_info
    <set>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="partitionDate != null">
        partition_date = #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="cat0Id != null">
        cat0_id = #{cat0Id,jdbcType=INTEGER},
      </if>
      <if test="cat0Name != null">
        cat0_name = #{cat0Name,jdbcType=VARCHAR},
      </if>
      <if test="cat1Id != null">
        cat1_id = #{cat1Id,jdbcType=INTEGER},
      </if>
      <if test="cat1Name != null">
        cat1_name = #{cat1Name,jdbcType=VARCHAR},
      </if>
      <if test="cat2Id != null">
        cat2_id = #{cat2Id,jdbcType=INTEGER},
      </if>
      <if test="cat2Name != null">
        cat2_name = #{cat2Name,jdbcType=VARCHAR},
      </if>
      <if test="shopType != null">
        shop_type = #{shopType,jdbcType=VARCHAR},
      </if>
      <if test="dimCityRank != null">
        dim_city_rank = #{dimCityRank,jdbcType=VARCHAR},
      </if>
      <if test="dpFrontCityId != null">
        dp_front_city_id = #{dpFrontCityId,jdbcType=BIGINT},
      </if>
      <if test="dpFrontCityName != null">
        dp_front_city_name = #{dpFrontCityName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.shop.common.repository.model.ShopPortraitBasicInfo">
    update shop_portrait_basic_info
    set update_time = #{updateTime,jdbcType=TIMESTAMP},
      partition_date = #{partitionDate,jdbcType=VARCHAR},
      poi_id = #{poiId,jdbcType=BIGINT},
      dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      cat0_id = #{cat0Id,jdbcType=INTEGER},
      cat0_name = #{cat0Name,jdbcType=VARCHAR},
      cat1_id = #{cat1Id,jdbcType=INTEGER},
      cat1_name = #{cat1Name,jdbcType=VARCHAR},
      cat2_id = #{cat2Id,jdbcType=INTEGER},
      cat2_name = #{cat2Name,jdbcType=VARCHAR},
      shop_type = #{shopType,jdbcType=VARCHAR},
      dim_city_rank = #{dimCityRank,jdbcType=VARCHAR},
      dp_front_city_id = #{dpFrontCityId,jdbcType=BIGINT},
      dp_front_city_name = #{dpFrontCityName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into shop_portrait_basic_info
    (update_time, partition_date, poi_id, dp_shop_id, mt_shop_id, cat0_id, cat0_name, 
      cat1_id, cat1_name, cat2_id, cat2_name, shop_type, dim_city_rank, dp_front_city_id, 
      dp_front_city_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.updateTime,jdbcType=TIMESTAMP}, #{item.partitionDate,jdbcType=VARCHAR}, #{item.poiId,jdbcType=BIGINT}, 
        #{item.dpShopId,jdbcType=BIGINT}, #{item.mtShopId,jdbcType=BIGINT}, #{item.cat0Id,jdbcType=INTEGER}, 
        #{item.cat0Name,jdbcType=VARCHAR}, #{item.cat1Id,jdbcType=INTEGER}, #{item.cat1Name,jdbcType=VARCHAR}, 
        #{item.cat2Id,jdbcType=INTEGER}, #{item.cat2Name,jdbcType=VARCHAR}, #{item.shopType,jdbcType=VARCHAR}, 
        #{item.dimCityRank,jdbcType=VARCHAR}, #{item.dpFrontCityId,jdbcType=BIGINT}, #{item.dpFrontCityName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>