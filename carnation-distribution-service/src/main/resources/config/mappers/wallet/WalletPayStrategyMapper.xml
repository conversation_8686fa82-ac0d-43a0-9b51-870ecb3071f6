<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletPayStrategyMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletPayStrategy">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="mt_user_id" jdbcType="BIGINT" property="mtUserId" />
    <result column="customet_id" jdbcType="BIGINT" property="custometId" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pay_type, mt_user_id, customet_id, customer_id, appid, openid, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletPayStrategyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_pay_strategy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_pay_strategy
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_pay_strategy
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletPayStrategyExample">
    delete from wallet_pay_strategy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletPayStrategy" useGeneratedKeys="true">
    insert into wallet_pay_strategy (pay_type, mt_user_id, customet_id, 
      customer_id, appid, openid, 
      update_time)
    values (#{payType,jdbcType=INTEGER}, #{mtUserId,jdbcType=BIGINT}, #{custometId,jdbcType=BIGINT}, 
      #{customerId,jdbcType=BIGINT}, #{appid,jdbcType=VARCHAR}, #{openid,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletPayStrategy" useGeneratedKeys="true">
    insert into wallet_pay_strategy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payType != null">
        pay_type,
      </if>
      <if test="mtUserId != null">
        mt_user_id,
      </if>
      <if test="custometId != null">
        customet_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="appid != null">
        appid,
      </if>
      <if test="openid != null">
        openid,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="mtUserId != null">
        #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="custometId != null">
        #{custometId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="appid != null">
        #{appid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletPayStrategyExample" resultType="java.lang.Long">
    select count(*) from wallet_pay_strategy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_pay_strategy
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=INTEGER},
      </if>
      <if test="record.mtUserId != null">
        mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      </if>
      <if test="record.custometId != null">
        customet_id = #{record.custometId,jdbcType=BIGINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=BIGINT},
      </if>
      <if test="record.appid != null">
        appid = #{record.appid,jdbcType=VARCHAR},
      </if>
      <if test="record.openid != null">
        openid = #{record.openid,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_pay_strategy
    set id = #{record.id,jdbcType=BIGINT},
      pay_type = #{record.payType,jdbcType=INTEGER},
      mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      customet_id = #{record.custometId,jdbcType=BIGINT},
      customer_id = #{record.customerId,jdbcType=BIGINT},
      appid = #{record.appid,jdbcType=VARCHAR},
      openid = #{record.openid,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletPayStrategy">
    update wallet_pay_strategy
    <set>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="mtUserId != null">
        mt_user_id = #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="custometId != null">
        customet_id = #{custometId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="appid != null">
        appid = #{appid,jdbcType=VARCHAR},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletPayStrategy">
    update wallet_pay_strategy
    set pay_type = #{payType,jdbcType=INTEGER},
      mt_user_id = #{mtUserId,jdbcType=BIGINT},
      customet_id = #{custometId,jdbcType=BIGINT},
      customer_id = #{customerId,jdbcType=BIGINT},
      appid = #{appid,jdbcType=VARCHAR},
      openid = #{openid,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into wallet_pay_strategy
    (pay_type, mt_user_id, customet_id, customer_id, appid, openid, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.payType,jdbcType=INTEGER}, #{item.mtUserId,jdbcType=BIGINT}, #{item.custometId,jdbcType=BIGINT}, 
        #{item.customerId,jdbcType=BIGINT}, #{item.appid,jdbcType=VARCHAR}, #{item.openid,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>