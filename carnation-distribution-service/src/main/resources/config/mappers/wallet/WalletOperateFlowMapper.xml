<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletOperateFlowMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_item_id" jdbcType="BIGINT" property="dataItemId" />
    <result column="wallet_account_id" jdbcType="BIGINT" property="walletAccountId" />
    <result column="wallet_activity_account_id" jdbcType="BIGINT" property="walletActivityAccountId" />
    <result column="search_id" jdbcType="VARCHAR" property="searchId" />
    <result column="second_search_id" jdbcType="VARCHAR" property="secondSearchId" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="flow_type" jdbcType="INTEGER" property="flowType" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="former_amount" jdbcType="BIGINT" property="formerAmount" />
    <result column="current_amount" jdbcType="BIGINT" property="currentAmount" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, data_item_id, wallet_account_id, wallet_activity_account_id, search_id, second_search_id, 
    operator, operate_type, flow_type, amount, former_amount, current_amount, comment, 
    status, ext_info, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletOperateFlowExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_operate_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_operate_flow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_operate_flow
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletOperateFlowExample">
    delete from wallet_operate_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow" useGeneratedKeys="true">
    insert into wallet_operate_flow (data_item_id, wallet_account_id, wallet_activity_account_id, 
      search_id, second_search_id, operator, 
      operate_type, flow_type, amount, 
      former_amount, current_amount, comment, 
      status, ext_info, add_time, 
      update_time)
    values (#{dataItemId,jdbcType=BIGINT}, #{walletAccountId,jdbcType=BIGINT}, #{walletActivityAccountId,jdbcType=BIGINT}, 
      #{searchId,jdbcType=VARCHAR}, #{secondSearchId,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{operateType,jdbcType=INTEGER}, #{flowType,jdbcType=INTEGER}, #{amount,jdbcType=BIGINT}, 
      #{formerAmount,jdbcType=BIGINT}, #{currentAmount,jdbcType=BIGINT}, #{comment,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{extInfo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow" useGeneratedKeys="true">
    insert into wallet_operate_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataItemId != null">
        data_item_id,
      </if>
      <if test="walletAccountId != null">
        wallet_account_id,
      </if>
      <if test="walletActivityAccountId != null">
        wallet_activity_account_id,
      </if>
      <if test="searchId != null">
        search_id,
      </if>
      <if test="secondSearchId != null">
        second_search_id,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="flowType != null">
        flow_type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="formerAmount != null">
        former_amount,
      </if>
      <if test="currentAmount != null">
        current_amount,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataItemId != null">
        #{dataItemId,jdbcType=BIGINT},
      </if>
      <if test="walletAccountId != null">
        #{walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="walletActivityAccountId != null">
        #{walletActivityAccountId,jdbcType=BIGINT},
      </if>
      <if test="searchId != null">
        #{searchId,jdbcType=VARCHAR},
      </if>
      <if test="secondSearchId != null">
        #{secondSearchId,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="flowType != null">
        #{flowType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="formerAmount != null">
        #{formerAmount,jdbcType=BIGINT},
      </if>
      <if test="currentAmount != null">
        #{currentAmount,jdbcType=BIGINT},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletOperateFlowExample" resultType="java.lang.Long">
    select count(*) from wallet_operate_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_operate_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dataItemId != null">
        data_item_id = #{record.dataItemId,jdbcType=BIGINT},
      </if>
      <if test="record.walletAccountId != null">
        wallet_account_id = #{record.walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.walletActivityAccountId != null">
        wallet_activity_account_id = #{record.walletActivityAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.searchId != null">
        search_id = #{record.searchId,jdbcType=VARCHAR},
      </if>
      <if test="record.secondSearchId != null">
        second_search_id = #{record.secondSearchId,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.flowType != null">
        flow_type = #{record.flowType,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.formerAmount != null">
        former_amount = #{record.formerAmount,jdbcType=BIGINT},
      </if>
      <if test="record.currentAmount != null">
        current_amount = #{record.currentAmount,jdbcType=BIGINT},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_operate_flow
    set id = #{record.id,jdbcType=BIGINT},
      data_item_id = #{record.dataItemId,jdbcType=BIGINT},
      wallet_account_id = #{record.walletAccountId,jdbcType=BIGINT},
      wallet_activity_account_id = #{record.walletActivityAccountId,jdbcType=BIGINT},
      search_id = #{record.searchId,jdbcType=VARCHAR},
      second_search_id = #{record.secondSearchId,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      flow_type = #{record.flowType,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=BIGINT},
      former_amount = #{record.formerAmount,jdbcType=BIGINT},
      current_amount = #{record.currentAmount,jdbcType=BIGINT},
      comment = #{record.comment,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow">
    update wallet_operate_flow
    <set>
      <if test="dataItemId != null">
        data_item_id = #{dataItemId,jdbcType=BIGINT},
      </if>
      <if test="walletAccountId != null">
        wallet_account_id = #{walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="walletActivityAccountId != null">
        wallet_activity_account_id = #{walletActivityAccountId,jdbcType=BIGINT},
      </if>
      <if test="searchId != null">
        search_id = #{searchId,jdbcType=VARCHAR},
      </if>
      <if test="secondSearchId != null">
        second_search_id = #{secondSearchId,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="flowType != null">
        flow_type = #{flowType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="formerAmount != null">
        former_amount = #{formerAmount,jdbcType=BIGINT},
      </if>
      <if test="currentAmount != null">
        current_amount = #{currentAmount,jdbcType=BIGINT},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletOperateFlow">
    update wallet_operate_flow
    set data_item_id = #{dataItemId,jdbcType=BIGINT},
      wallet_account_id = #{walletAccountId,jdbcType=BIGINT},
      wallet_activity_account_id = #{walletActivityAccountId,jdbcType=BIGINT},
      search_id = #{searchId,jdbcType=VARCHAR},
      second_search_id = #{secondSearchId,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=INTEGER},
      flow_type = #{flowType,jdbcType=INTEGER},
      amount = #{amount,jdbcType=BIGINT},
      former_amount = #{formerAmount,jdbcType=BIGINT},
      current_amount = #{currentAmount,jdbcType=BIGINT},
      comment = #{comment,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into wallet_operate_flow
    (data_item_id, wallet_account_id, wallet_activity_account_id, search_id, second_search_id, 
      operator, operate_type, flow_type, amount, former_amount, current_amount, comment, 
      status, ext_info, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dataItemId,jdbcType=BIGINT}, #{item.walletAccountId,jdbcType=BIGINT}, #{item.walletActivityAccountId,jdbcType=BIGINT}, 
        #{item.searchId,jdbcType=VARCHAR}, #{item.secondSearchId,jdbcType=VARCHAR}, #{item.operator,jdbcType=VARCHAR}, 
        #{item.operateType,jdbcType=INTEGER}, #{item.flowType,jdbcType=INTEGER}, #{item.amount,jdbcType=BIGINT}, 
        #{item.formerAmount,jdbcType=BIGINT}, #{item.currentAmount,jdbcType=BIGINT}, #{item.comment,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.extInfo,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>