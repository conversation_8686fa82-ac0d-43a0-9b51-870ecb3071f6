<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletAccountMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="bind_account_type" jdbcType="INTEGER" property="bindAccountType" />
    <result column="bind_account_id" jdbcType="BIGINT" property="bindAccountId" />
    <result column="bind_account_id_str" jdbcType="VARCHAR" property="bindAccountIdStr" />
    <result column="pay_strategy_id" jdbcType="BIGINT" property="payStrategyId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, bind_account_type, bind_account_id, bind_account_id_str, pay_strategy_id, 
    status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletAccountExample">
    delete from wallet_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletAccount" useGeneratedKeys="true">
    insert into wallet_account (biz_code, bind_account_type, bind_account_id, 
      bind_account_id_str, pay_strategy_id, status, 
      add_time, update_time)
    values (#{bizCode,jdbcType=VARCHAR}, #{bindAccountType,jdbcType=INTEGER}, #{bindAccountId,jdbcType=BIGINT}, 
      #{bindAccountIdStr,jdbcType=VARCHAR}, #{payStrategyId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletAccount" useGeneratedKeys="true">
    insert into wallet_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bindAccountType != null">
        bind_account_type,
      </if>
      <if test="bindAccountId != null">
        bind_account_id,
      </if>
      <if test="bindAccountIdStr != null">
        bind_account_id_str,
      </if>
      <if test="payStrategyId != null">
        pay_strategy_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bindAccountType != null">
        #{bindAccountType,jdbcType=INTEGER},
      </if>
      <if test="bindAccountId != null">
        #{bindAccountId,jdbcType=BIGINT},
      </if>
      <if test="bindAccountIdStr != null">
        #{bindAccountIdStr,jdbcType=VARCHAR},
      </if>
      <if test="payStrategyId != null">
        #{payStrategyId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletAccountExample" resultType="java.lang.Long">
    select count(*) from wallet_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bindAccountType != null">
        bind_account_type = #{record.bindAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.bindAccountId != null">
        bind_account_id = #{record.bindAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.bindAccountIdStr != null">
        bind_account_id_str = #{record.bindAccountIdStr,jdbcType=VARCHAR},
      </if>
      <if test="record.payStrategyId != null">
        pay_strategy_id = #{record.payStrategyId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_account
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      bind_account_type = #{record.bindAccountType,jdbcType=INTEGER},
      bind_account_id = #{record.bindAccountId,jdbcType=BIGINT},
      bind_account_id_str = #{record.bindAccountIdStr,jdbcType=VARCHAR},
      pay_strategy_id = #{record.payStrategyId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletAccount">
    update wallet_account
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bindAccountType != null">
        bind_account_type = #{bindAccountType,jdbcType=INTEGER},
      </if>
      <if test="bindAccountId != null">
        bind_account_id = #{bindAccountId,jdbcType=BIGINT},
      </if>
      <if test="bindAccountIdStr != null">
        bind_account_id_str = #{bindAccountIdStr,jdbcType=VARCHAR},
      </if>
      <if test="payStrategyId != null">
        pay_strategy_id = #{payStrategyId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletAccount">
    update wallet_account
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      bind_account_type = #{bindAccountType,jdbcType=INTEGER},
      bind_account_id = #{bindAccountId,jdbcType=BIGINT},
      bind_account_id_str = #{bindAccountIdStr,jdbcType=VARCHAR},
      pay_strategy_id = #{payStrategyId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into wallet_account
    (biz_code, bind_account_type, bind_account_id, bind_account_id_str, pay_strategy_id, 
      status, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizCode,jdbcType=VARCHAR}, #{item.bindAccountType,jdbcType=INTEGER}, #{item.bindAccountId,jdbcType=BIGINT}, 
        #{item.bindAccountIdStr,jdbcType=VARCHAR}, #{item.payStrategyId,jdbcType=BIGINT}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>