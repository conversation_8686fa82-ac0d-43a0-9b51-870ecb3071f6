<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletDataItemMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="wallet_account_id" jdbcType="BIGINT" property="walletAccountId" />
    <result column="wallet_activity_account_id" jdbcType="BIGINT" property="walletActivityAccountId" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="unified_order_id" jdbcType="VARCHAR" property="unifiedOrderId" />
    <result column="search_id" jdbcType="VARCHAR" property="searchId" />
    <result column="second_search_id" jdbcType="VARCHAR" property="secondSearchId" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, wallet_account_id, wallet_activity_account_id, biz_id, unified_order_id, 
    search_id, second_search_id, amount, operate_type, status, ext_info, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletDataItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_data_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_data_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletDataItemExample">
    delete from wallet_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem" useGeneratedKeys="true">
    insert into wallet_data_item (biz_code, wallet_account_id, wallet_activity_account_id, 
      biz_id, unified_order_id, search_id, 
      second_search_id, amount, operate_type, 
      status, ext_info, add_time, 
      update_time)
    values (#{bizCode,jdbcType=VARCHAR}, #{walletAccountId,jdbcType=BIGINT}, #{walletActivityAccountId,jdbcType=BIGINT}, 
      #{bizId,jdbcType=VARCHAR}, #{unifiedOrderId,jdbcType=VARCHAR}, #{searchId,jdbcType=VARCHAR}, 
      #{secondSearchId,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, #{operateType,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{extInfo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem" useGeneratedKeys="true">
    insert into wallet_data_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="walletAccountId != null">
        wallet_account_id,
      </if>
      <if test="walletActivityAccountId != null">
        wallet_activity_account_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="unifiedOrderId != null">
        unified_order_id,
      </if>
      <if test="searchId != null">
        search_id,
      </if>
      <if test="secondSearchId != null">
        second_search_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="walletAccountId != null">
        #{walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="walletActivityAccountId != null">
        #{walletActivityAccountId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="unifiedOrderId != null">
        #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="searchId != null">
        #{searchId,jdbcType=VARCHAR},
      </if>
      <if test="secondSearchId != null">
        #{secondSearchId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletDataItemExample" resultType="java.lang.Long">
    select count(*) from wallet_data_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_data_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.walletAccountId != null">
        wallet_account_id = #{record.walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.walletActivityAccountId != null">
        wallet_activity_account_id = #{record.walletActivityAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedOrderId != null">
        unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.searchId != null">
        search_id = #{record.searchId,jdbcType=VARCHAR},
      </if>
      <if test="record.secondSearchId != null">
        second_search_id = #{record.secondSearchId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_data_item
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      wallet_account_id = #{record.walletAccountId,jdbcType=BIGINT},
      wallet_activity_account_id = #{record.walletActivityAccountId,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      search_id = #{record.searchId,jdbcType=VARCHAR},
      second_search_id = #{record.secondSearchId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem">
    update wallet_data_item
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="walletAccountId != null">
        wallet_account_id = #{walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="walletActivityAccountId != null">
        wallet_activity_account_id = #{walletActivityAccountId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="unifiedOrderId != null">
        unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="searchId != null">
        search_id = #{searchId,jdbcType=VARCHAR},
      </if>
      <if test="secondSearchId != null">
        second_search_id = #{secondSearchId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletDataItem">
    update wallet_data_item
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      wallet_account_id = #{walletAccountId,jdbcType=BIGINT},
      wallet_activity_account_id = #{walletActivityAccountId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=VARCHAR},
      unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      search_id = #{searchId,jdbcType=VARCHAR},
      second_search_id = #{secondSearchId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      operate_type = #{operateType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into wallet_data_item
    (biz_code, wallet_account_id, wallet_activity_account_id, biz_id, unified_order_id, 
      search_id, second_search_id, amount, operate_type, status, ext_info, add_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizCode,jdbcType=VARCHAR}, #{item.walletAccountId,jdbcType=BIGINT}, #{item.walletActivityAccountId,jdbcType=BIGINT}, 
        #{item.bizId,jdbcType=VARCHAR}, #{item.unifiedOrderId,jdbcType=VARCHAR}, #{item.searchId,jdbcType=VARCHAR}, 
        #{item.secondSearchId,jdbcType=VARCHAR}, #{item.amount,jdbcType=BIGINT}, #{item.operateType,jdbcType=INTEGER}, 
        #{item.status,jdbcType=INTEGER}, #{item.extInfo,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>