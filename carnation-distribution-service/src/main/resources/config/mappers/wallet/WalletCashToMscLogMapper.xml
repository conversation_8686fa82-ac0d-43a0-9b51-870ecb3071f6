<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletCashToMscLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToMscLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_key" jdbcType="VARCHAR" property="uniqueKey" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="wall_flow_id" jdbcType="BIGINT" property="wallFlowId" />
    <result column="msc_status" jdbcType="INTEGER" property="mscStatus" />
    <result column="msc_code" jdbcType="INTEGER" property="mscCode" />
    <result column="msc_msg" jdbcType="VARCHAR" property="mscMsg" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unique_key, biz_code, wall_flow_id, msc_status, msc_code, msc_msg, add_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletCashToMscLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_cash_to_msc_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_cash_to_msc_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_cash_to_msc_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletCashToMscLogExample">
    delete from wallet_cash_to_msc_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToMscLog" useGeneratedKeys="true">
    insert into wallet_cash_to_msc_log (unique_key, biz_code, wall_flow_id, 
      msc_status, msc_code, msc_msg, 
      add_time, update_time)
    values (#{uniqueKey,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, #{wallFlowId,jdbcType=BIGINT}, 
      #{mscStatus,jdbcType=INTEGER}, #{mscCode,jdbcType=INTEGER}, #{mscMsg,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToMscLog" useGeneratedKeys="true">
    insert into wallet_cash_to_msc_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null">
        unique_key,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="wallFlowId != null">
        wall_flow_id,
      </if>
      <if test="mscStatus != null">
        msc_status,
      </if>
      <if test="mscCode != null">
        msc_code,
      </if>
      <if test="mscMsg != null">
        msc_msg,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null">
        #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="wallFlowId != null">
        #{wallFlowId,jdbcType=BIGINT},
      </if>
      <if test="mscStatus != null">
        #{mscStatus,jdbcType=INTEGER},
      </if>
      <if test="mscCode != null">
        #{mscCode,jdbcType=INTEGER},
      </if>
      <if test="mscMsg != null">
        #{mscMsg,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletCashToMscLogExample" resultType="java.lang.Long">
    select count(*) from wallet_cash_to_msc_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_cash_to_msc_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.uniqueKey != null">
        unique_key = #{record.uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.wallFlowId != null">
        wall_flow_id = #{record.wallFlowId,jdbcType=BIGINT},
      </if>
      <if test="record.mscStatus != null">
        msc_status = #{record.mscStatus,jdbcType=INTEGER},
      </if>
      <if test="record.mscCode != null">
        msc_code = #{record.mscCode,jdbcType=INTEGER},
      </if>
      <if test="record.mscMsg != null">
        msc_msg = #{record.mscMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_cash_to_msc_log
    set id = #{record.id,jdbcType=BIGINT},
      unique_key = #{record.uniqueKey,jdbcType=VARCHAR},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      wall_flow_id = #{record.wallFlowId,jdbcType=BIGINT},
      msc_status = #{record.mscStatus,jdbcType=INTEGER},
      msc_code = #{record.mscCode,jdbcType=INTEGER},
      msc_msg = #{record.mscMsg,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToMscLog">
    update wallet_cash_to_msc_log
    <set>
      <if test="uniqueKey != null">
        unique_key = #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="wallFlowId != null">
        wall_flow_id = #{wallFlowId,jdbcType=BIGINT},
      </if>
      <if test="mscStatus != null">
        msc_status = #{mscStatus,jdbcType=INTEGER},
      </if>
      <if test="mscCode != null">
        msc_code = #{mscCode,jdbcType=INTEGER},
      </if>
      <if test="mscMsg != null">
        msc_msg = #{mscMsg,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToMscLog">
    update wallet_cash_to_msc_log
    set unique_key = #{uniqueKey,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      wall_flow_id = #{wallFlowId,jdbcType=BIGINT},
      msc_status = #{mscStatus,jdbcType=INTEGER},
      msc_code = #{mscCode,jdbcType=INTEGER},
      msc_msg = #{mscMsg,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>