<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletCashToFsLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToFsLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_key" jdbcType="VARCHAR" property="uniqueKey" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="wall_flow_id" jdbcType="BIGINT" property="wallFlowId" />
    <result column="fs_status" jdbcType="INTEGER" property="fsStatus" />
    <result column="fs_pay_unique_id" jdbcType="BIGINT" property="fsPayUniqueId" />
    <result column="fs_error_code" jdbcType="VARCHAR" property="fsErrorCode" />
    <result column="fs_error_msg" jdbcType="VARCHAR" property="fsErrorMsg" />
    <result column="fs_callback_status" jdbcType="VARCHAR" property="fsCallbackStatus" />
    <result column="fs_callback_reason" jdbcType="VARCHAR" property="fsCallbackReason" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unique_key, biz_code, wall_flow_id, fs_status, fs_pay_unique_id, fs_error_code, 
    fs_error_msg, fs_callback_status, fs_callback_reason, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletCashToFsLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_cash_to_fs_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_cash_to_fs_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_cash_to_fs_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletCashToFsLogExample">
    delete from wallet_cash_to_fs_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToFsLog" useGeneratedKeys="true">
    insert into wallet_cash_to_fs_log (unique_key, biz_code, wall_flow_id, 
      fs_status, fs_pay_unique_id, fs_error_code, 
      fs_error_msg, fs_callback_status, fs_callback_reason, 
      add_time, update_time)
    values (#{uniqueKey,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, #{wallFlowId,jdbcType=BIGINT}, 
      #{fsStatus,jdbcType=INTEGER}, #{fsPayUniqueId,jdbcType=BIGINT}, #{fsErrorCode,jdbcType=VARCHAR}, 
      #{fsErrorMsg,jdbcType=VARCHAR}, #{fsCallbackStatus,jdbcType=VARCHAR}, #{fsCallbackReason,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToFsLog" useGeneratedKeys="true">
    insert into wallet_cash_to_fs_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null">
        unique_key,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="wallFlowId != null">
        wall_flow_id,
      </if>
      <if test="fsStatus != null">
        fs_status,
      </if>
      <if test="fsPayUniqueId != null">
        fs_pay_unique_id,
      </if>
      <if test="fsErrorCode != null">
        fs_error_code,
      </if>
      <if test="fsErrorMsg != null">
        fs_error_msg,
      </if>
      <if test="fsCallbackStatus != null">
        fs_callback_status,
      </if>
      <if test="fsCallbackReason != null">
        fs_callback_reason,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null">
        #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="wallFlowId != null">
        #{wallFlowId,jdbcType=BIGINT},
      </if>
      <if test="fsStatus != null">
        #{fsStatus,jdbcType=INTEGER},
      </if>
      <if test="fsPayUniqueId != null">
        #{fsPayUniqueId,jdbcType=BIGINT},
      </if>
      <if test="fsErrorCode != null">
        #{fsErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="fsErrorMsg != null">
        #{fsErrorMsg,jdbcType=VARCHAR},
      </if>
      <if test="fsCallbackStatus != null">
        #{fsCallbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="fsCallbackReason != null">
        #{fsCallbackReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletCashToFsLogExample" resultType="java.lang.Long">
    select count(*) from wallet_cash_to_fs_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_cash_to_fs_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.uniqueKey != null">
        unique_key = #{record.uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.wallFlowId != null">
        wall_flow_id = #{record.wallFlowId,jdbcType=BIGINT},
      </if>
      <if test="record.fsStatus != null">
        fs_status = #{record.fsStatus,jdbcType=INTEGER},
      </if>
      <if test="record.fsPayUniqueId != null">
        fs_pay_unique_id = #{record.fsPayUniqueId,jdbcType=BIGINT},
      </if>
      <if test="record.fsErrorCode != null">
        fs_error_code = #{record.fsErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fsErrorMsg != null">
        fs_error_msg = #{record.fsErrorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.fsCallbackStatus != null">
        fs_callback_status = #{record.fsCallbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.fsCallbackReason != null">
        fs_callback_reason = #{record.fsCallbackReason,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_cash_to_fs_log
    set id = #{record.id,jdbcType=BIGINT},
      unique_key = #{record.uniqueKey,jdbcType=VARCHAR},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      wall_flow_id = #{record.wallFlowId,jdbcType=BIGINT},
      fs_status = #{record.fsStatus,jdbcType=INTEGER},
      fs_pay_unique_id = #{record.fsPayUniqueId,jdbcType=BIGINT},
      fs_error_code = #{record.fsErrorCode,jdbcType=VARCHAR},
      fs_error_msg = #{record.fsErrorMsg,jdbcType=VARCHAR},
      fs_callback_status = #{record.fsCallbackStatus,jdbcType=VARCHAR},
      fs_callback_reason = #{record.fsCallbackReason,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToFsLog">
    update wallet_cash_to_fs_log
    <set>
      <if test="uniqueKey != null">
        unique_key = #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="wallFlowId != null">
        wall_flow_id = #{wallFlowId,jdbcType=BIGINT},
      </if>
      <if test="fsStatus != null">
        fs_status = #{fsStatus,jdbcType=INTEGER},
      </if>
      <if test="fsPayUniqueId != null">
        fs_pay_unique_id = #{fsPayUniqueId,jdbcType=BIGINT},
      </if>
      <if test="fsErrorCode != null">
        fs_error_code = #{fsErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="fsErrorMsg != null">
        fs_error_msg = #{fsErrorMsg,jdbcType=VARCHAR},
      </if>
      <if test="fsCallbackStatus != null">
        fs_callback_status = #{fsCallbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="fsCallbackReason != null">
        fs_callback_reason = #{fsCallbackReason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletCashToFsLog">
    update wallet_cash_to_fs_log
    set unique_key = #{uniqueKey,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      wall_flow_id = #{wallFlowId,jdbcType=BIGINT},
      fs_status = #{fsStatus,jdbcType=INTEGER},
      fs_pay_unique_id = #{fsPayUniqueId,jdbcType=BIGINT},
      fs_error_code = #{fsErrorCode,jdbcType=VARCHAR},
      fs_error_msg = #{fsErrorMsg,jdbcType=VARCHAR},
      fs_callback_status = #{fsCallbackStatus,jdbcType=VARCHAR},
      fs_callback_reason = #{fsCallbackReason,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>