<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.wallet.fundamental.repository.dao.WalletActivityAccountMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletActivityAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wallet_account_id" jdbcType="BIGINT" property="walletAccountId" />
    <result column="activity_source" jdbcType="INTEGER" property="activitySource" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="pay_strategy_id" jdbcType="BIGINT" property="payStrategyId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wallet_account_id, activity_source, activity_id, amount, pay_strategy_id, status, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletActivityAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wallet_activity_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wallet_activity_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wallet_activity_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletActivityAccountExample">
    delete from wallet_activity_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletActivityAccount" useGeneratedKeys="true">
    insert into wallet_activity_account (wallet_account_id, activity_source, activity_id, 
      amount, pay_strategy_id, status, 
      add_time, update_time)
    values (#{walletAccountId,jdbcType=BIGINT}, #{activitySource,jdbcType=INTEGER}, #{activityId,jdbcType=BIGINT}, 
      #{amount,jdbcType=BIGINT}, #{payStrategyId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletActivityAccount" useGeneratedKeys="true">
    insert into wallet_activity_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="walletAccountId != null">
        wallet_account_id,
      </if>
      <if test="activitySource != null">
        activity_source,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="payStrategyId != null">
        pay_strategy_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="walletAccountId != null">
        #{walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="activitySource != null">
        #{activitySource,jdbcType=INTEGER},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="payStrategyId != null">
        #{payStrategyId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.example.WalletActivityAccountExample" resultType="java.lang.Long">
    select count(*) from wallet_activity_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wallet_activity_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.walletAccountId != null">
        wallet_account_id = #{record.walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="record.activitySource != null">
        activity_source = #{record.activitySource,jdbcType=INTEGER},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.payStrategyId != null">
        pay_strategy_id = #{record.payStrategyId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wallet_activity_account
    set id = #{record.id,jdbcType=BIGINT},
      wallet_account_id = #{record.walletAccountId,jdbcType=BIGINT},
      activity_source = #{record.activitySource,jdbcType=INTEGER},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      amount = #{record.amount,jdbcType=BIGINT},
      pay_strategy_id = #{record.payStrategyId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletActivityAccount">
    update wallet_activity_account
    <set>
      <if test="walletAccountId != null">
        wallet_account_id = #{walletAccountId,jdbcType=BIGINT},
      </if>
      <if test="activitySource != null">
        activity_source = #{activitySource,jdbcType=INTEGER},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="payStrategyId != null">
        pay_strategy_id = #{payStrategyId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.wallet.fundamental.repository.entity.WalletActivityAccount">
    update wallet_activity_account
    set wallet_account_id = #{walletAccountId,jdbcType=BIGINT},
      activity_source = #{activitySource,jdbcType=INTEGER},
      activity_id = #{activityId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=BIGINT},
      pay_strategy_id = #{payStrategyId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into wallet_activity_account
    (wallet_account_id, activity_source, activity_id, amount, pay_strategy_id, status, 
      add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.walletAccountId,jdbcType=BIGINT}, #{item.activitySource,jdbcType=INTEGER}, 
        #{item.activityId,jdbcType=BIGINT}, #{item.amount,jdbcType=BIGINT}, #{item.payStrategyId,jdbcType=BIGINT}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>