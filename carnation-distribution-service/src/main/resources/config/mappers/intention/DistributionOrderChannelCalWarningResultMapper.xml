<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalWarningResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="result_id" jdbcType="BIGINT" property="resultId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="former_code" jdbcType="VARCHAR" property="formerCode" />
    <result column="former_channel" jdbcType="VARCHAR" property="formerChannel" />
    <result column="cal_code" jdbcType="VARCHAR" property="calCode" />
    <result column="cal_channel" jdbcType="VARCHAR" property="calChannel" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, result_id, order_type, order_id, former_code, former_channel, cal_code, cal_channel, 
    level, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResultCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_order_channel_cal_warning_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distribution_order_channel_cal_warning_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_order_channel_cal_warning_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResultCriteria">
    delete from distribution_order_channel_cal_warning_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResult">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_order_channel_cal_warning_result (result_id, order_type, order_id, 
      former_code, former_channel, cal_code, 
      cal_channel, level, update_time
      )
    values (#{resultId,jdbcType=BIGINT}, #{orderType,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, 
      #{formerCode,jdbcType=VARCHAR}, #{formerChannel,jdbcType=VARCHAR}, #{calCode,jdbcType=VARCHAR}, 
      #{calChannel,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResult">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_order_channel_cal_warning_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resultId != null">
        result_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="formerCode != null">
        former_code,
      </if>
      <if test="formerChannel != null">
        former_channel,
      </if>
      <if test="calCode != null">
        cal_code,
      </if>
      <if test="calChannel != null">
        cal_channel,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="resultId != null">
        #{resultId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="formerCode != null">
        #{formerCode,jdbcType=VARCHAR},
      </if>
      <if test="formerChannel != null">
        #{formerChannel,jdbcType=VARCHAR},
      </if>
      <if test="calCode != null">
        #{calCode,jdbcType=VARCHAR},
      </if>
      <if test="calChannel != null">
        #{calChannel,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResultCriteria" resultType="java.lang.Long">
    select count(*) from distribution_order_channel_cal_warning_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_order_channel_cal_warning_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.resultId != null">
        result_id = #{record.resultId,jdbcType=BIGINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.formerCode != null">
        former_code = #{record.formerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.formerChannel != null">
        former_channel = #{record.formerChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.calCode != null">
        cal_code = #{record.calCode,jdbcType=VARCHAR},
      </if>
      <if test="record.calChannel != null">
        cal_channel = #{record.calChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_order_channel_cal_warning_result
    set id = #{record.id,jdbcType=BIGINT},
      result_id = #{record.resultId,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      former_code = #{record.formerCode,jdbcType=VARCHAR},
      former_channel = #{record.formerChannel,jdbcType=VARCHAR},
      cal_code = #{record.calCode,jdbcType=VARCHAR},
      cal_channel = #{record.calChannel,jdbcType=VARCHAR},
      level = #{record.level,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResult">
    update distribution_order_channel_cal_warning_result
    <set>
      <if test="resultId != null">
        result_id = #{resultId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="formerCode != null">
        former_code = #{formerCode,jdbcType=VARCHAR},
      </if>
      <if test="formerChannel != null">
        former_channel = #{formerChannel,jdbcType=VARCHAR},
      </if>
      <if test="calCode != null">
        cal_code = #{calCode,jdbcType=VARCHAR},
      </if>
      <if test="calChannel != null">
        cal_channel = #{calChannel,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalWarningResult">
    update distribution_order_channel_cal_warning_result
    set result_id = #{resultId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      former_code = #{formerCode,jdbcType=VARCHAR},
      former_channel = #{formerChannel,jdbcType=VARCHAR},
      cal_code = #{calCode,jdbcType=VARCHAR},
      cal_channel = #{calChannel,jdbcType=VARCHAR},
      level = #{level,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>