<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.order.repository.dao.ExtDistributionOrderChangeRecordMapper">

  <select id="pageQueryDistributionOrderChange" resultType="com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord" parameterType="map">
    select
        id as id,
        order_id as orderId,
        order_type as orderType,
        change_reason as changeReason,
        channel as channel,
        action_time as actionTime
    from distribution_order_change_record
    where id > #{cursorId,jdbcType=BIGINT}
    and channel = #{channel,jdbcType=VARCHAR}
    and action_time &gt;= #{actionStartTime,jdbcType=TIMESTAMP}
    and action_time &lt;= #{actionEndTime,jdbcType=TIMESTAMP}
    and is_deleted = 0
    order by id asc
    limit #{pageSize}
  </select>
</mapper>