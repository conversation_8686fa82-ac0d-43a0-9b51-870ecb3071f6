<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="cal_result" jdbcType="INTEGER" property="calResult" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="intention_type" jdbcType="INTEGER" property="intentionType" />
    <result column="former_code" jdbcType="VARCHAR" property="formerCode" />
    <result column="distribution_code" jdbcType="VARCHAR" property="distributionCode" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, order_type, order_id, cal_result, channel, intention_type, former_code, 
    distribution_code, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResultCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_order_channel_cal_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distribution_order_channel_cal_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_order_channel_cal_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResultCriteria">
    delete from distribution_order_channel_cal_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_order_channel_cal_result (task_id, order_type, order_id, 
      cal_result, channel, intention_type, 
      former_code, distribution_code, add_time, 
      update_time)
    values (#{taskId,jdbcType=BIGINT}, #{orderType,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, 
      #{calResult,jdbcType=INTEGER}, #{channel,jdbcType=VARCHAR}, #{intentionType,jdbcType=INTEGER}, 
      #{formerCode,jdbcType=VARCHAR}, #{distributionCode,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_order_channel_cal_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="calResult != null">
        cal_result,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="intentionType != null">
        intention_type,
      </if>
      <if test="formerCode != null">
        former_code,
      </if>
      <if test="distributionCode != null">
        distribution_code,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="calResult != null">
        #{calResult,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="intentionType != null">
        #{intentionType,jdbcType=INTEGER},
      </if>
      <if test="formerCode != null">
        #{formerCode,jdbcType=VARCHAR},
      </if>
      <if test="distributionCode != null">
        #{distributionCode,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResultCriteria" resultType="java.lang.Long">
    select count(*) from distribution_order_channel_cal_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_order_channel_cal_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.calResult != null">
        cal_result = #{record.calResult,jdbcType=INTEGER},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.intentionType != null">
        intention_type = #{record.intentionType,jdbcType=INTEGER},
      </if>
      <if test="record.formerCode != null">
        former_code = #{record.formerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.distributionCode != null">
        distribution_code = #{record.distributionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_order_channel_cal_result
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      cal_result = #{record.calResult,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=VARCHAR},
      intention_type = #{record.intentionType,jdbcType=INTEGER},
      former_code = #{record.formerCode,jdbcType=VARCHAR},
      distribution_code = #{record.distributionCode,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult">
    update distribution_order_channel_cal_result
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="calResult != null">
        cal_result = #{calResult,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="intentionType != null">
        intention_type = #{intentionType,jdbcType=INTEGER},
      </if>
      <if test="formerCode != null">
        former_code = #{formerCode,jdbcType=VARCHAR},
      </if>
      <if test="distributionCode != null">
        distribution_code = #{distributionCode,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalResult">
    update distribution_order_channel_cal_result
    set task_id = #{taskId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      cal_result = #{calResult,jdbcType=INTEGER},
      channel = #{channel,jdbcType=VARCHAR},
      intention_type = #{intentionType,jdbcType=INTEGER},
      former_code = #{formerCode,jdbcType=VARCHAR},
      distribution_code = #{distributionCode,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>