<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.order.repository.dao.DistributionOrderChangeRecordMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="action_time" jdbcType="TIMESTAMP" property="actionTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_type, change_reason, channel, action_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.order.repository.example.DistributionOrderChangeRecordCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_order_change_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distribution_order_change_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    update distribution_order_change_record set is_deleted = #{id,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord" useGeneratedKeys="true">
    insert into distribution_order_change_record
        (order_id, order_type, change_reason, channel, action_time)
    values
        (#{orderId,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, #{changeReason,jdbcType=VARCHAR},
         #{channel,jdbcType=VARCHAR}, #{actionTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord" useGeneratedKeys="true">
    insert into distribution_order_change_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="changeReason != null">
        change_reason,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="actionTime != null">
        action_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="changeReason != null">
        #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="actionTime != null">
        #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.order.repository.example.DistributionOrderChangeRecordCriteria" resultType="java.lang.Long">
    select count(*) from distribution_order_change_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_order_change_record
    <set>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.changeReason != null">
        change_reason = #{record.changeReason,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.actionTime != null">
        action_time = #{record.actionTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_order_change_record
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      change_reason = #{record.changeReason,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      action_time = #{record.actionTime,jdbcType=TIMESTAMP},
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord">
    update distribution_order_change_record
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="changeReason != null">
        change_reason = #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="actionTime != null">
        action_time = #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.order.repository.entity.DistributionOrderChangeRecord">
    update distribution_order_change_record
    set order_id = #{orderId,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      change_reason = #{changeReason,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      action_time = #{actionTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>