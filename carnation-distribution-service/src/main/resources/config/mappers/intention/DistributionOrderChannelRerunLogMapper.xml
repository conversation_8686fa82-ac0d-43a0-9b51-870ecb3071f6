<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelRerunLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="running_task_id" jdbcType="BIGINT" property="runningTaskId" />
    <result column="cal_task_id" jdbcType="BIGINT" property="calTaskId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogWithBLOBs">
    <result column="param" jdbcType="LONGVARCHAR" property="param" />
    <result column="result" jdbcType="LONGVARCHAR" property="result" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_id, running_task_id, cal_task_id, order_type, order_id, remark, add_time, 
    update_time
  </sql>
  <sql id="Blob_Column_List">
    param, result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from distribution_order_channel_rerun_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_order_channel_rerun_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from distribution_order_channel_rerun_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_order_channel_rerun_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogExample">
    delete from distribution_order_channel_rerun_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogWithBLOBs" useGeneratedKeys="true">
    insert into distribution_order_channel_rerun_log (biz_id, running_task_id, cal_task_id, 
      order_type, order_id, remark, 
      add_time, update_time, param, 
      result)
    values (#{bizId,jdbcType=VARCHAR}, #{runningTaskId,jdbcType=BIGINT}, #{calTaskId,jdbcType=BIGINT}, 
      #{orderType,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{param,jdbcType=LONGVARCHAR}, 
      #{result,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogWithBLOBs" useGeneratedKeys="true">
    insert into distribution_order_channel_rerun_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="runningTaskId != null">
        running_task_id,
      </if>
      <if test="calTaskId != null">
        cal_task_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="param != null">
        param,
      </if>
      <if test="result != null">
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="runningTaskId != null">
        #{runningTaskId,jdbcType=BIGINT},
      </if>
      <if test="calTaskId != null">
        #{calTaskId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        #{param,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogExample" resultType="java.lang.Long">
    select count(*) from distribution_order_channel_rerun_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_order_channel_rerun_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.runningTaskId != null">
        running_task_id = #{record.runningTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.calTaskId != null">
        cal_task_id = #{record.calTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.param != null">
        param = #{record.param,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update distribution_order_channel_rerun_log
    set id = #{record.id,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      running_task_id = #{record.runningTaskId,jdbcType=BIGINT},
      cal_task_id = #{record.calTaskId,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      param = #{record.param,jdbcType=LONGVARCHAR},
      result = #{record.result,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_order_channel_rerun_log
    set id = #{record.id,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      running_task_id = #{record.runningTaskId,jdbcType=BIGINT},
      cal_task_id = #{record.calTaskId,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogWithBLOBs">
    update distribution_order_channel_rerun_log
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="runningTaskId != null">
        running_task_id = #{runningTaskId,jdbcType=BIGINT},
      </if>
      <if test="calTaskId != null">
        cal_task_id = #{calTaskId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        param = #{param,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLogWithBLOBs">
    update distribution_order_channel_rerun_log
    set biz_id = #{bizId,jdbcType=VARCHAR},
      running_task_id = #{runningTaskId,jdbcType=BIGINT},
      cal_task_id = #{calTaskId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      param = #{param,jdbcType=LONGVARCHAR},
      result = #{result,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelRerunLog">
    update distribution_order_channel_rerun_log
    set biz_id = #{bizId,jdbcType=VARCHAR},
      running_task_id = #{runningTaskId,jdbcType=BIGINT},
      cal_task_id = #{calTaskId,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into distribution_order_channel_rerun_log
    (biz_id, running_task_id, cal_task_id, order_type, order_id, remark, add_time, update_time, 
      param, result)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizId,jdbcType=VARCHAR}, #{item.runningTaskId,jdbcType=BIGINT}, #{item.calTaskId,jdbcType=BIGINT}, 
        #{item.orderType,jdbcType=INTEGER}, #{item.orderId,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.param,jdbcType=LONGVARCHAR}, 
        #{item.result,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>