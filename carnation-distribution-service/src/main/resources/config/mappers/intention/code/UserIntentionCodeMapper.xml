<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.code.dao.UserIntentionCodeMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="intention_code" jdbcType="VARCHAR" property="intentionCode" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ext_info" jdbcType="CHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, biz_id, user_type, user_id, intention_code, order_id, status, add_time, 
    update_time, ext_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCodeCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_intention_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_intention_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from user_intention_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCodeCriteria">
    delete from user_intention_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCode" useGeneratedKeys="true">
    insert into user_intention_code (biz_code, biz_id, user_type, 
      user_id, intention_code, order_id, 
      status, add_time, update_time, 
      ext_info)
    values (#{bizCode,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, #{userType,jdbcType=INTEGER}, 
      #{userId,jdbcType=BIGINT}, #{intentionCode,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{extInfo,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCode" useGeneratedKeys="true">
    insert into user_intention_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="intentionCode != null">
        intention_code,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="intentionCode != null">
        #{intentionCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCodeCriteria" resultType="java.lang.Long">
    select count(*) from user_intention_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_intention_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.intentionCode != null">
        intention_code = #{record.intentionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_intention_code
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      intention_code = #{record.intentionCode,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      ext_info = #{record.extInfo,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCode">
    update user_intention_code
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="intentionCode != null">
        intention_code = #{intentionCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.code.db.UserIntentionCode">
    update user_intention_code
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      intention_code = #{intentionCode,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      ext_info = #{extInfo,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>