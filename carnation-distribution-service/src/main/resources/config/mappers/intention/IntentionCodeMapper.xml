<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.dao.IntentionCodeMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.db.IntentionCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="intention_code" jdbcType="VARCHAR" property="intentionCode" />
    <result column="code_type" jdbcType="INTEGER" property="codeType" />
    <result column="distributor_id" jdbcType="VARCHAR" property="distributorId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="effect_end_time" jdbcType="TIMESTAMP" property="effectEndTime" />
    <result column="stauts" jdbcType="INTEGER" property="stauts" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, biz_id, intention_code, code_type, distributor_id, product_type, product_id, 
    effect_end_time, stauts, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCodeCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from intention_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from intention_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from intention_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCodeCriteria">
    delete from intention_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCode">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into intention_code (biz_code, biz_id, intention_code, 
      code_type, distributor_id, product_type, 
      product_id, effect_end_time, stauts, 
      add_time, update_time)
    values (#{bizCode,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, #{intentionCode,jdbcType=VARCHAR}, 
      #{codeType,jdbcType=INTEGER}, #{distributorId,jdbcType=VARCHAR}, #{productType,jdbcType=INTEGER}, 
      #{productId,jdbcType=BIGINT}, #{effectEndTime,jdbcType=TIMESTAMP}, #{stauts,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCode">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into intention_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="intentionCode != null">
        intention_code,
      </if>
      <if test="codeType != null">
        code_type,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="effectEndTime != null">
        effect_end_time,
      </if>
      <if test="stauts != null">
        stauts,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="intentionCode != null">
        #{intentionCode,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        #{codeType,jdbcType=INTEGER},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="effectEndTime != null">
        #{effectEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stauts != null">
        #{stauts,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCodeCriteria" resultType="java.lang.Long">
    select count(*) from intention_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update intention_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.intentionCode != null">
        intention_code = #{record.intentionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.codeType != null">
        code_type = #{record.codeType,jdbcType=INTEGER},
      </if>
      <if test="record.distributorId != null">
        distributor_id = #{record.distributorId,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.effectEndTime != null">
        effect_end_time = #{record.effectEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.stauts != null">
        stauts = #{record.stauts,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update intention_code
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      intention_code = #{record.intentionCode,jdbcType=VARCHAR},
      code_type = #{record.codeType,jdbcType=INTEGER},
      distributor_id = #{record.distributorId,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      effect_end_time = #{record.effectEndTime,jdbcType=TIMESTAMP},
      stauts = #{record.stauts,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCode">
    update intention_code
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="intentionCode != null">
        intention_code = #{intentionCode,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        code_type = #{codeType,jdbcType=INTEGER},
      </if>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="effectEndTime != null">
        effect_end_time = #{effectEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="stauts != null">
        stauts = #{stauts,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.db.IntentionCode">
    update intention_code
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      intention_code = #{intentionCode,jdbcType=VARCHAR},
      code_type = #{codeType,jdbcType=INTEGER},
      distributor_id = #{distributorId,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      effect_end_time = #{effectEndTime,jdbcType=TIMESTAMP},
      stauts = #{stauts,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>