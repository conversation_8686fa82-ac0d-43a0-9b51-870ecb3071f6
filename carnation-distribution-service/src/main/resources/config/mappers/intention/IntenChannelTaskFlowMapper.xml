<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.inten.dao.IntenChannelTaskFlowMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.inten.po.IntenChannelTaskFlow">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_task_id" jdbcType="BIGINT" property="channelTaskId" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="inten_type" jdbcType="INTEGER" property="intenType" />
    <result column="inten_id" jdbcType="VARCHAR" property="intenId" />
    <result column="predict_end_time" jdbcType="TIMESTAMP" property="predictEndTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="param" jdbcType="VARCHAR" property="param" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, channel_task_id, biz_id, inten_type, inten_id, predict_end_time, status, param, 
    result, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.example.IntenChannelTaskFlowExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from inten_channel_task_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from inten_channel_task_flow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from inten_channel_task_flow
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.example.IntenChannelTaskFlowExample">
    delete from inten_channel_task_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.po.IntenChannelTaskFlow" useGeneratedKeys="true">
    insert into inten_channel_task_flow (channel_task_id, biz_id, inten_type, 
      inten_id, predict_end_time, status, 
      param, result, add_time, 
      update_time)
    values (#{channelTaskId,jdbcType=BIGINT}, #{bizId,jdbcType=VARCHAR}, #{intenType,jdbcType=INTEGER}, 
      #{intenId,jdbcType=VARCHAR}, #{predictEndTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, 
      #{param,jdbcType=VARCHAR}, #{result,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.po.IntenChannelTaskFlow" useGeneratedKeys="true">
    insert into inten_channel_task_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelTaskId != null">
        channel_task_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="intenType != null">
        inten_type,
      </if>
      <if test="intenId != null">
        inten_id,
      </if>
      <if test="predictEndTime != null">
        predict_end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="param != null">
        param,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelTaskId != null">
        #{channelTaskId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="intenType != null">
        #{intenType,jdbcType=INTEGER},
      </if>
      <if test="intenId != null">
        #{intenId,jdbcType=VARCHAR},
      </if>
      <if test="predictEndTime != null">
        #{predictEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="param != null">
        #{param,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.example.IntenChannelTaskFlowExample" resultType="java.lang.Long">
    select count(*) from inten_channel_task_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update inten_channel_task_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.channelTaskId != null">
        channel_task_id = #{record.channelTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.intenType != null">
        inten_type = #{record.intenType,jdbcType=INTEGER},
      </if>
      <if test="record.intenId != null">
        inten_id = #{record.intenId,jdbcType=VARCHAR},
      </if>
      <if test="record.predictEndTime != null">
        predict_end_time = #{record.predictEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.param != null">
        param = #{record.param,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update inten_channel_task_flow
    set id = #{record.id,jdbcType=BIGINT},
      channel_task_id = #{record.channelTaskId,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      inten_type = #{record.intenType,jdbcType=INTEGER},
      inten_id = #{record.intenId,jdbcType=VARCHAR},
      predict_end_time = #{record.predictEndTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      param = #{record.param,jdbcType=VARCHAR},
      result = #{record.result,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.po.IntenChannelTaskFlow">
    update inten_channel_task_flow
    <set>
      <if test="channelTaskId != null">
        channel_task_id = #{channelTaskId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="intenType != null">
        inten_type = #{intenType,jdbcType=INTEGER},
      </if>
      <if test="intenId != null">
        inten_id = #{intenId,jdbcType=VARCHAR},
      </if>
      <if test="predictEndTime != null">
        predict_end_time = #{predictEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="param != null">
        param = #{param,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.inten.po.IntenChannelTaskFlow">
    update inten_channel_task_flow
    set channel_task_id = #{channelTaskId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=VARCHAR},
      inten_type = #{intenType,jdbcType=INTEGER},
      inten_id = #{intenId,jdbcType=VARCHAR},
      predict_end_time = #{predictEndTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      param = #{param,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into inten_channel_task_flow
    (channel_task_id, biz_id, inten_type, inten_id, predict_end_time, status, param, 
      result, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.channelTaskId,jdbcType=BIGINT}, #{item.bizId,jdbcType=VARCHAR}, #{item.intenType,jdbcType=INTEGER}, 
        #{item.intenId,jdbcType=VARCHAR}, #{item.predictEndTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, 
        #{item.param,jdbcType=VARCHAR}, #{item.result,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>