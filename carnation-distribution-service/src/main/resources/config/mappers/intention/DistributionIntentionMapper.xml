<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.dao.DistributionIntentionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntention">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="distributor_id" jdbcType="BIGINT" property="distributorId" />
    <result column="distributor_group_id" jdbcType="INTEGER" property="distributorGroupId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="distribution_product_id" jdbcType="BIGINT" property="distributionProductId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, platform, distributor_id, distributor_group_id, product_id, product_type, 
    distribution_product_id, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntentionCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_intention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distribution_intention
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_intention
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntentionCriteria">
    delete from distribution_intention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntention">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_intention (user_id, platform, distributor_id, 
      distributor_group_id, product_id, product_type, 
      distribution_product_id, status, add_time, 
      update_time)
    values (#{userId,jdbcType=BIGINT}, #{platform,jdbcType=INTEGER}, #{distributorId,jdbcType=BIGINT}, 
      #{distributorGroupId,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, #{productType,jdbcType=INTEGER}, 
      #{distributionProductId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntention">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_intention
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="distributorId != null">
        distributor_id,
      </if>
      <if test="distributorGroupId != null">
        distributor_group_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="distributionProductId != null">
        distribution_product_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="distributorId != null">
        #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorGroupId != null">
        #{distributorGroupId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="distributionProductId != null">
        #{distributionProductId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntentionCriteria" resultType="java.lang.Long">
    select count(*) from distribution_intention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_intention
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.distributorId != null">
        distributor_id = #{record.distributorId,jdbcType=BIGINT},
      </if>
      <if test="record.distributorGroupId != null">
        distributor_group_id = #{record.distributorGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.distributionProductId != null">
        distribution_product_id = #{record.distributionProductId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_intention
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      platform = #{record.platform,jdbcType=INTEGER},
      distributor_id = #{record.distributorId,jdbcType=BIGINT},
      distributor_group_id = #{record.distributorGroupId,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=INTEGER},
      distribution_product_id = #{record.distributionProductId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntention">
    update distribution_intention
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="distributorId != null">
        distributor_id = #{distributorId,jdbcType=BIGINT},
      </if>
      <if test="distributorGroupId != null">
        distributor_group_id = #{distributorGroupId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="distributionProductId != null">
        distribution_product_id = #{distributionProductId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionIntention">
    update distribution_intention
    set user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      distributor_id = #{distributorId,jdbcType=BIGINT},
      distributor_group_id = #{distributorGroupId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      product_type = #{productType,jdbcType=INTEGER},
      distribution_product_id = #{distributionProductId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>