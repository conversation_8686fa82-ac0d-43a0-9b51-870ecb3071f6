<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.dao.DistributionOrderChannelCalRunningTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cal_task_id" jdbcType="BIGINT" property="calTaskId" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="predict_end_time" jdbcType="TIMESTAMP" property="predictEndTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs">
    <result column="param" jdbcType="LONGVARCHAR" property="param" />
    <result column="result" jdbcType="LONGVARCHAR" property="result" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cal_task_id, task_type, order_type, order_id, predict_end_time, status, add_time, 
    update_time
  </sql>
  <sql id="Blob_Column_List">
    param, result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskCriteria" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from distribution_order_channel_cal_running_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_order_channel_cal_running_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from distribution_order_channel_cal_running_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_order_channel_cal_running_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskCriteria">
    delete from distribution_order_channel_cal_running_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_order_channel_cal_running_task (cal_task_id, task_type, order_type, 
      order_id, predict_end_time, status, 
      add_time, update_time, param, 
      result)
    values (#{calTaskId,jdbcType=BIGINT}, #{taskType,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER}, 
      #{orderId,jdbcType=VARCHAR}, #{predictEndTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{param,jdbcType=LONGVARCHAR}, 
      #{result,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into distribution_order_channel_cal_running_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="calTaskId != null">
        cal_task_id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="predictEndTime != null">
        predict_end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="param != null">
        param,
      </if>
      <if test="result != null">
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="calTaskId != null">
        #{calTaskId,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="predictEndTime != null">
        #{predictEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        #{param,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskCriteria" resultType="java.lang.Long">
    select count(*) from distribution_order_channel_cal_running_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_order_channel_cal_running_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.calTaskId != null">
        cal_task_id = #{record.calTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.predictEndTime != null">
        predict_end_time = #{record.predictEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.param != null">
        param = #{record.param,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update distribution_order_channel_cal_running_task
    set id = #{record.id,jdbcType=BIGINT},
      cal_task_id = #{record.calTaskId,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      predict_end_time = #{record.predictEndTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      param = #{record.param,jdbcType=LONGVARCHAR},
      result = #{record.result,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_order_channel_cal_running_task
    set id = #{record.id,jdbcType=BIGINT},
      cal_task_id = #{record.calTaskId,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      predict_end_time = #{record.predictEndTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs">
    update distribution_order_channel_cal_running_task
    <set>
      <if test="calTaskId != null">
        cal_task_id = #{calTaskId,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="predictEndTime != null">
        predict_end_time = #{predictEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        param = #{param,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTaskWithBLOBs">
    update distribution_order_channel_cal_running_task
    set cal_task_id = #{calTaskId,jdbcType=BIGINT},
      task_type = #{taskType,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      predict_end_time = #{predictEndTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      param = #{param,jdbcType=LONGVARCHAR},
      result = #{result,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.db.DistributionOrderChannelCalRunningTask">
    update distribution_order_channel_cal_running_task
    set cal_task_id = #{calTaskId,jdbcType=BIGINT},
      task_type = #{taskType,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      predict_end_time = #{predictEndTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>