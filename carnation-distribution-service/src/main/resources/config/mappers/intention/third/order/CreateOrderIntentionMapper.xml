<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.intention.repository.third.order.dao.CreateOrderIntentionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntention">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_type, biz_id, platform, user_id, shop_id, product_type, product_id, order_id, 
    ext, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntentionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from create_order_intention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from create_order_intention
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from create_order_intention
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntentionExample">
    delete from create_order_intention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntention" useGeneratedKeys="true">
    insert into create_order_intention (biz_type, biz_id, platform, 
      user_id, shop_id, product_type, 
      product_id, order_id, ext, 
      status, add_time, update_time
      )
    values (#{bizType,jdbcType=INTEGER}, #{bizId,jdbcType=VARCHAR}, #{platform,jdbcType=INTEGER}, 
      #{userId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{productType,jdbcType=INTEGER}, 
      #{productId,jdbcType=BIGINT}, #{orderId,jdbcType=VARCHAR}, #{ext,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntention" useGeneratedKeys="true">
    insert into create_order_intention
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntentionExample" resultType="java.lang.Long">
    select count(*) from create_order_intention
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update create_order_intention
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update create_order_intention
    set id = #{record.id,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntention">
    update create_order_intention
    <set>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.intention.repository.third.order.db.CreateOrderIntention">
    update create_order_intention
    set biz_type = #{bizType,jdbcType=INTEGER},
      biz_id = #{bizId,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      product_type = #{productType,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into create_order_intention
    (biz_type, biz_id, platform, user_id, shop_id, product_type, product_id, order_id, 
      ext, status, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizType,jdbcType=INTEGER}, #{item.bizId,jdbcType=VARCHAR}, #{item.platform,jdbcType=INTEGER}, 
        #{item.userId,jdbcType=BIGINT}, #{item.shopId,jdbcType=BIGINT}, #{item.productType,jdbcType=INTEGER}, 
        #{item.productId,jdbcType=BIGINT}, #{item.orderId,jdbcType=VARCHAR}, #{item.ext,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>