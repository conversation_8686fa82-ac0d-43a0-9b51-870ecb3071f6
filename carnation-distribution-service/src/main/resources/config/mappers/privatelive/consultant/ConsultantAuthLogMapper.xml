<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.ConsultantAuthLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.ConsultantAuthLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="consultant_task_id" jdbcType="BIGINT" property="consultantTaskId" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="operate_id" jdbcType="VARCHAR" property="operateId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, consultant_task_id, operate_type, operate_id, reason, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.ConsultantAuthLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from consultant_auth_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from consultant_auth_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from consultant_auth_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.ConsultantAuthLogExample">
    delete from consultant_auth_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.ConsultantAuthLog" useGeneratedKeys="true">
    insert into consultant_auth_log (consultant_task_id, operate_type, operate_id, 
      reason, add_time, update_time
      )
    values (#{consultantTaskId,jdbcType=BIGINT}, #{operateType,jdbcType=INTEGER}, #{operateId,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.ConsultantAuthLog" useGeneratedKeys="true">
    insert into consultant_auth_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="consultantTaskId != null">
        consultant_task_id,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="consultantTaskId != null">
        #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.ConsultantAuthLogExample" resultType="java.lang.Long">
    select count(*) from consultant_auth_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update consultant_auth_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.consultantTaskId != null">
        consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.operateId != null">
        operate_id = #{record.operateId,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update consultant_auth_log
    set id = #{record.id,jdbcType=BIGINT},
      consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      operate_id = #{record.operateId,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.ConsultantAuthLog">
    update consultant_auth_log
    <set>
      <if test="consultantTaskId != null">
        consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operateId != null">
        operate_id = #{operateId,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.ConsultantAuthLog">
    update consultant_auth_log
    set consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      operate_type = #{operateType,jdbcType=INTEGER},
      operate_id = #{operateId,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into consultant_auth_log
    (consultant_task_id, operate_type, operate_id, reason, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.consultantTaskId,jdbcType=BIGINT}, #{item.operateType,jdbcType=INTEGER}, 
        #{item.operateId,jdbcType=VARCHAR}, #{item.reason,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>