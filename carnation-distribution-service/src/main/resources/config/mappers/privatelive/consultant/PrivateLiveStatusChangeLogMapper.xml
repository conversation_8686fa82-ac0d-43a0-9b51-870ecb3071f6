<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveStatusChangeLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveStatusChangeLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="live_id" jdbcType="VARCHAR" property="liveId" />
    <result column="main_anchor_id" jdbcType="BIGINT" property="mainAnchorId" />
    <result column="action_type" jdbcType="INTEGER" property="actionType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, live_id, main_anchor_id, action_type, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveStatusChangeLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_status_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from private_live_status_change_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_status_change_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveStatusChangeLogExample">
    delete from private_live_status_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveStatusChangeLog" useGeneratedKeys="true">
    insert into private_live_status_change_log (live_id, main_anchor_id, action_type, 
      create_time, update_time)
    values (#{liveId,jdbcType=VARCHAR}, #{mainAnchorId,jdbcType=BIGINT}, #{actionType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveStatusChangeLog" useGeneratedKeys="true">
    insert into private_live_status_change_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="liveId != null">
        live_id,
      </if>
      <if test="mainAnchorId != null">
        main_anchor_id,
      </if>
      <if test="actionType != null">
        action_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="liveId != null">
        #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="mainAnchorId != null">
        #{mainAnchorId,jdbcType=BIGINT},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveStatusChangeLogExample" resultType="java.lang.Long">
    select count(*) from private_live_status_change_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_status_change_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=VARCHAR},
      </if>
      <if test="record.mainAnchorId != null">
        main_anchor_id = #{record.mainAnchorId,jdbcType=BIGINT},
      </if>
      <if test="record.actionType != null">
        action_type = #{record.actionType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_status_change_log
    set id = #{record.id,jdbcType=BIGINT},
      live_id = #{record.liveId,jdbcType=VARCHAR},
      main_anchor_id = #{record.mainAnchorId,jdbcType=BIGINT},
      action_type = #{record.actionType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveStatusChangeLog">
    update private_live_status_change_log
    <set>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="mainAnchorId != null">
        main_anchor_id = #{mainAnchorId,jdbcType=BIGINT},
      </if>
      <if test="actionType != null">
        action_type = #{actionType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveStatusChangeLog">
    update private_live_status_change_log
    set live_id = #{liveId,jdbcType=VARCHAR},
      main_anchor_id = #{mainAnchorId,jdbcType=BIGINT},
      action_type = #{actionType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_status_change_log
    (live_id, main_anchor_id, action_type, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.liveId,jdbcType=VARCHAR}, #{item.mainAnchorId,jdbcType=BIGINT}, #{item.actionType,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>