<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantTaskMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="consultant_id" jdbcType="BIGINT" property="consultantId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="live_id" jdbcType="VARCHAR" property="liveId" />
    <result column="anchor_id" jdbcType="BIGINT" property="anchorId" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="avatar_url" jdbcType="VARCHAR" property="avatarUrl" />
    <result column="share_name" jdbcType="VARCHAR" property="shareName" />
    <result column="actual_name" jdbcType="VARCHAR" property="actualName" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="wechat_number" jdbcType="VARCHAR" property="wechatNumber" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, consultant_id, group_id, live_id, anchor_id, nickname, avatar_url, share_name,
    actual_name, phone_number, shop_id, wechat_number, status, add_time, update_time,
    task_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_consultant_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from private_live_consultant_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_consultant_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantTaskExample">
    delete from private_live_consultant_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask" useGeneratedKeys="true">
    insert into private_live_consultant_task (id, consultant_id, group_id,
      live_id, anchor_id, nickname,
      avatar_url, share_name, actual_name,
      phone_number, shop_id, wechat_number, 
      status, add_time, update_time,
      task_type)
    values (#{id,jdbcType=BIGINT}, #{consultantId,jdbcType=BIGINT}, #{groupId,jdbcType=VARCHAR},
      #{liveId,jdbcType=VARCHAR}, #{anchorId,jdbcType=BIGINT}, #{nickname,jdbcType=VARCHAR},
      #{avatarUrl,jdbcType=VARCHAR}, #{shareName,jdbcType=VARCHAR}, #{actualName,jdbcType=VARCHAR},
      #{phoneNumber,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, #{wechatNumber,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{taskType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask" useGeneratedKeys="true">
    insert into private_live_consultant_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="consultantId != null">
        consultant_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="liveId != null">
        live_id,
      </if>
      <if test="anchorId != null">
        anchor_id,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="avatarUrl != null">
        avatar_url,
      </if>
      <if test="shareName != null">
        share_name,
      </if>
      <if test="actualName != null">
        actual_name,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="wechatNumber != null">
        wechat_number,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="consultantId != null">
        #{consultantId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="liveId != null">
        #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="anchorId != null">
        #{anchorId,jdbcType=BIGINT},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="avatarUrl != null">
        #{avatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="shareName != null">
        #{shareName,jdbcType=VARCHAR},
      </if>
      <if test="actualName != null">
        #{actualName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="wechatNumber != null">
        #{wechatNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantTaskExample" resultType="java.lang.Long">
    select count(*) from private_live_consultant_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_consultant_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.consultantId != null">
        consultant_id = #{record.consultantId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=VARCHAR},
      </if>
      <if test="record.anchorId != null">
        anchor_id = #{record.anchorId,jdbcType=BIGINT},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.avatarUrl != null">
        avatar_url = #{record.avatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.shareName != null">
        share_name = #{record.shareName,jdbcType=VARCHAR},
      </if>
      <if test="record.actualName != null">
        actual_name = #{record.actualName,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.wechatNumber != null">
        wechat_number = #{record.wechatNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_consultant_task
    set id = #{record.id,jdbcType=BIGINT},
      consultant_id = #{record.consultantId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      live_id = #{record.liveId,jdbcType=VARCHAR},
      anchor_id = #{record.anchorId,jdbcType=BIGINT},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      avatar_url = #{record.avatarUrl,jdbcType=VARCHAR},
      share_name = #{record.shareName,jdbcType=VARCHAR},
      actual_name = #{record.actualName,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      wechat_number = #{record.wechatNumber,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task_type = #{record.taskType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask">
    update private_live_consultant_task
    <set>
      <if test="consultantId != null">
        consultant_id = #{consultantId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="anchorId != null">
        anchor_id = #{anchorId,jdbcType=BIGINT},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="avatarUrl != null">
        avatar_url = #{avatarUrl,jdbcType=VARCHAR},
      </if>
      <if test="shareName != null">
        share_name = #{shareName,jdbcType=VARCHAR},
      </if>
      <if test="actualName != null">
        actual_name = #{actualName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="wechatNumber != null">
        wechat_number = #{wechatNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantTask">
    update private_live_consultant_task
    set consultant_id = #{consultantId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=VARCHAR},
      live_id = #{liveId,jdbcType=VARCHAR},
      anchor_id = #{anchorId,jdbcType=BIGINT},
      nickname = #{nickname,jdbcType=VARCHAR},
      avatar_url = #{avatarUrl,jdbcType=VARCHAR},
      share_name = #{shareName,jdbcType=VARCHAR},
      actual_name = #{actualName,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      wechat_number = #{wechatNumber,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task_type = #{taskType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_consultant_task
    (id, consultant_id, group_id, live_id, anchor_id, nickname, avatar_url, share_name,
    actual_name, phone_number, shop_id, wechat_number, status, add_time, update_time
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.consultantId,jdbcType=BIGINT}, #{item.groupId,jdbcType=VARCHAR},
      #{item.liveId,jdbcType=VARCHAR}, #{item.anchorId,jdbcType=BIGINT}, #{item.nickname,jdbcType=VARCHAR},
      #{item.avatarUrl,jdbcType=VARCHAR}, #{item.shareName,jdbcType=VARCHAR}, #{item.actualName,jdbcType=VARCHAR},
      #{item.phoneNumber,jdbcType=VARCHAR}, #{item.shopId,jdbcType=BIGINT}, #{item.wechatNumber,jdbcType=VARCHAR},
      #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <select id="searchConsultantTasksPageConditional" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from private_live_consultant_task
    <if test="consultantTask != null or ids != null or keyword != null or (taskStatus != null and taskStatus.size() > 0)">
      <include refid="SearchConditional_Where_Clause" />
    </if>
    <if test="sortMap != null and sortMap.size() > 0">
      order by
      <foreach item="item" index="key" collection="sortMap" separator=",">
        ${key}
        <choose>
          <when test="item == 1">asc</when>
          <otherwise>desc</otherwise>
        </choose>
      </foreach>
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="countConsultantTasksPageConditional" resultType="java.lang.Integer">
    select count(*)
    from private_live_consultant_task
    <if test="consultantTask != null or ids != null or keyword != null or (taskStatus != null and taskStatus.size() > 0)">
      <include refid="SearchConditional_Where_Clause" />
    </if>
  </select>
  <sql id="SearchConditional_Where_Clause">
    <where>
      <if test="ids != null and ids.size() > 0">
        id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
          #{id, jdbcType=ARRAY}
        </foreach>
      </if>
      <if test="consultantTask.liveId != null and consultantTask.liveId != ''">
        and live_id = #{consultantTask.liveId,jdbcType=VARCHAR}
      </if>
      <if test="consultantTask.anchorId != null and consultantTask.anchorId != ''">
        and anchor_id = #{consultantTask.anchorId,jdbcType=BIGINT}
      </if>
      <if test="consultantTask.nickname != null">
        and nickname = #{consultantTask.nickname,jdbcType=VARCHAR}
      </if>
      <if test="consultantTask.shareName != null">
        and share_name = #{consultantTask.shareName,jdbcType=VARCHAR}
      </if>
      <if test="consultantTask.actualName != null">
        and actual_name = #{consultantTask.actualName,jdbcType=VARCHAR}
      </if>
      <if test="consultantTask.wechatNumber != null">
        and wechat_number = #{consultantTask.wechatNumber,jdbcType=VARCHAR}
      </if>
      <if test="consultantTask.status != null">
        and status = #{consultantTask.status,jdbcType=INTEGER}
      </if>
      <choose>
        <when test="taskStatus != null and taskStatus.size() > 0">
          and status IN
          <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status, jdbcType=INTEGER}
          </foreach>
        </when>
        <otherwise>
          and status != 4
        </otherwise>
      </choose>
      <if test="keyword != null and keyword !=''">
        and (
        share_name LIKE concat('%', #{keyword}, '%')
        or wechat_number LIKE concat('%', #{keyword}, '%')
        )
      </if>
    </where>
  </sql>
</mapper>