<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveConsultantUserStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mt_user_id" jdbcType="BIGINT" property="mtUserId" />
    <result column="consultant_task_id" jdbcType="BIGINT" property="consultantTaskId" />
    <result column="live_id" jdbcType="VARCHAR" property="liveId" />
    <result column="consultant_share_name" jdbcType="VARCHAR" property="consultantShareName" />
    <result column="consultant_head_pic" jdbcType="VARCHAR" property="consultantHeadPic" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="trade_product_cnt" jdbcType="INTEGER" property="tradeProductCnt" />
    <result column="trade_order_cnt" jdbcType="INTEGER" property="tradeOrderCnt" />
    <result column="pay_order_cnt" jdbcType="INTEGER" property="payOrderCnt" />
    <result column="wait_to_pay_order_cnt" jdbcType="INTEGER" property="waitToPayOrderCnt" />
    <result column="wait_to_verify_order_cnt" jdbcType="INTEGER" property="waitToVerifyOrderCnt" />
    <result column="verify_order_cnt" jdbcType="INTEGER" property="verifyOrderCnt" />
    <result column="refund_order_cnt" jdbcType="INTEGER" property="refundOrderCnt" />
    <result column="cancel_order_cnt" jdbcType="INTEGER" property="cancelOrderCnt" />
    <result column="actual_pay_amt" jdbcType="BIGINT" property="actualPayAmt" />
    <result column="gmv_amt" jdbcType="BIGINT" property="gmvAmt" />
    <result column="verify_gmv_amt" jdbcType="BIGINT" property="verifyGmvAmt" />
    <result column="refund_gmv_amt" jdbcType="BIGINT" property="refundGmvAmt" />
    <result column="last_order_time" jdbcType="TIMESTAMP" property="lastOrderTime" />
    <result column="last_verify_time" jdbcType="TIMESTAMP" property="lastVerifyTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="wait_to_pay_gmv_amt" jdbcType="BIGINT" property="waitToPayGmvAmt" />
    <result column="in_refund_gmv_amt" jdbcType="BIGINT" property="inRefundGmvAmt" />
    <result column="in_refund_order_cnt" jdbcType="INTEGER" property="inRefundOrderCnt" />
    <result column="cancel_gmv_amt" jdbcType="BIGINT" property="cancelGmvAmt" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mt_user_id, consultant_task_id, live_id, consultant_share_name, consultant_head_pic, 
    distributor_code, trade_product_cnt, trade_order_cnt, pay_order_cnt, wait_to_pay_order_cnt, 
    wait_to_verify_order_cnt, verify_order_cnt, refund_order_cnt, cancel_order_cnt, actual_pay_amt, 
    gmv_amt, verify_gmv_amt, refund_gmv_amt, last_order_time, last_verify_time, add_time, 
    update_time, wait_to_pay_gmv_amt, in_refund_gmv_amt, in_refund_order_cnt, cancel_gmv_amt, 
    status
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantUserStatisticsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_consultant_user_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from private_live_consultant_user_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_consultant_user_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantUserStatisticsExample">
    delete from private_live_consultant_user_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics" useGeneratedKeys="true">
    insert into private_live_consultant_user_statistics (mt_user_id, consultant_task_id, live_id, 
      consultant_share_name, consultant_head_pic, 
      distributor_code, trade_product_cnt, trade_order_cnt, 
      pay_order_cnt, wait_to_pay_order_cnt, wait_to_verify_order_cnt, 
      verify_order_cnt, refund_order_cnt, cancel_order_cnt, 
      actual_pay_amt, gmv_amt, verify_gmv_amt, 
      refund_gmv_amt, last_order_time, last_verify_time, 
      add_time, update_time, wait_to_pay_gmv_amt, 
      in_refund_gmv_amt, in_refund_order_cnt, cancel_gmv_amt, 
      status)
    values (#{mtUserId,jdbcType=BIGINT}, #{consultantTaskId,jdbcType=BIGINT}, #{liveId,jdbcType=VARCHAR}, 
      #{consultantShareName,jdbcType=VARCHAR}, #{consultantHeadPic,jdbcType=VARCHAR}, 
      #{distributorCode,jdbcType=VARCHAR}, #{tradeProductCnt,jdbcType=INTEGER}, #{tradeOrderCnt,jdbcType=INTEGER}, 
      #{payOrderCnt,jdbcType=INTEGER}, #{waitToPayOrderCnt,jdbcType=INTEGER}, #{waitToVerifyOrderCnt,jdbcType=INTEGER}, 
      #{verifyOrderCnt,jdbcType=INTEGER}, #{refundOrderCnt,jdbcType=INTEGER}, #{cancelOrderCnt,jdbcType=INTEGER}, 
      #{actualPayAmt,jdbcType=BIGINT}, #{gmvAmt,jdbcType=BIGINT}, #{verifyGmvAmt,jdbcType=BIGINT}, 
      #{refundGmvAmt,jdbcType=BIGINT}, #{lastOrderTime,jdbcType=TIMESTAMP}, #{lastVerifyTime,jdbcType=TIMESTAMP}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{waitToPayGmvAmt,jdbcType=BIGINT}, 
      #{inRefundGmvAmt,jdbcType=BIGINT}, #{inRefundOrderCnt,jdbcType=INTEGER}, #{cancelGmvAmt,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics" useGeneratedKeys="true">
    insert into private_live_consultant_user_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mtUserId != null">
        mt_user_id,
      </if>
      <if test="consultantTaskId != null">
        consultant_task_id,
      </if>
      <if test="liveId != null">
        live_id,
      </if>
      <if test="consultantShareName != null">
        consultant_share_name,
      </if>
      <if test="consultantHeadPic != null">
        consultant_head_pic,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="tradeProductCnt != null">
        trade_product_cnt,
      </if>
      <if test="tradeOrderCnt != null">
        trade_order_cnt,
      </if>
      <if test="payOrderCnt != null">
        pay_order_cnt,
      </if>
      <if test="waitToPayOrderCnt != null">
        wait_to_pay_order_cnt,
      </if>
      <if test="waitToVerifyOrderCnt != null">
        wait_to_verify_order_cnt,
      </if>
      <if test="verifyOrderCnt != null">
        verify_order_cnt,
      </if>
      <if test="refundOrderCnt != null">
        refund_order_cnt,
      </if>
      <if test="cancelOrderCnt != null">
        cancel_order_cnt,
      </if>
      <if test="actualPayAmt != null">
        actual_pay_amt,
      </if>
      <if test="gmvAmt != null">
        gmv_amt,
      </if>
      <if test="verifyGmvAmt != null">
        verify_gmv_amt,
      </if>
      <if test="refundGmvAmt != null">
        refund_gmv_amt,
      </if>
      <if test="lastOrderTime != null">
        last_order_time,
      </if>
      <if test="lastVerifyTime != null">
        last_verify_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="waitToPayGmvAmt != null">
        wait_to_pay_gmv_amt,
      </if>
      <if test="inRefundGmvAmt != null">
        in_refund_gmv_amt,
      </if>
      <if test="inRefundOrderCnt != null">
        in_refund_order_cnt,
      </if>
      <if test="cancelGmvAmt != null">
        cancel_gmv_amt,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mtUserId != null">
        #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="consultantTaskId != null">
        #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="liveId != null">
        #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="consultantShareName != null">
        #{consultantShareName,jdbcType=VARCHAR},
      </if>
      <if test="consultantHeadPic != null">
        #{consultantHeadPic,jdbcType=VARCHAR},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeProductCnt != null">
        #{tradeProductCnt,jdbcType=INTEGER},
      </if>
      <if test="tradeOrderCnt != null">
        #{tradeOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="payOrderCnt != null">
        #{payOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="waitToPayOrderCnt != null">
        #{waitToPayOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="waitToVerifyOrderCnt != null">
        #{waitToVerifyOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="verifyOrderCnt != null">
        #{verifyOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="refundOrderCnt != null">
        #{refundOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="cancelOrderCnt != null">
        #{cancelOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="actualPayAmt != null">
        #{actualPayAmt,jdbcType=BIGINT},
      </if>
      <if test="gmvAmt != null">
        #{gmvAmt,jdbcType=BIGINT},
      </if>
      <if test="verifyGmvAmt != null">
        #{verifyGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="refundGmvAmt != null">
        #{refundGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="lastOrderTime != null">
        #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastVerifyTime != null">
        #{lastVerifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="waitToPayGmvAmt != null">
        #{waitToPayGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="inRefundGmvAmt != null">
        #{inRefundGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="inRefundOrderCnt != null">
        #{inRefundOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="cancelGmvAmt != null">
        #{cancelGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveConsultantUserStatisticsExample" resultType="java.lang.Long">
    select count(*) from private_live_consultant_user_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_consultant_user_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mtUserId != null">
        mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      </if>
      <if test="record.consultantTaskId != null">
        consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=VARCHAR},
      </if>
      <if test="record.consultantShareName != null">
        consultant_share_name = #{record.consultantShareName,jdbcType=VARCHAR},
      </if>
      <if test="record.consultantHeadPic != null">
        consultant_head_pic = #{record.consultantHeadPic,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeProductCnt != null">
        trade_product_cnt = #{record.tradeProductCnt,jdbcType=INTEGER},
      </if>
      <if test="record.tradeOrderCnt != null">
        trade_order_cnt = #{record.tradeOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.payOrderCnt != null">
        pay_order_cnt = #{record.payOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.waitToPayOrderCnt != null">
        wait_to_pay_order_cnt = #{record.waitToPayOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.waitToVerifyOrderCnt != null">
        wait_to_verify_order_cnt = #{record.waitToVerifyOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.verifyOrderCnt != null">
        verify_order_cnt = #{record.verifyOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.refundOrderCnt != null">
        refund_order_cnt = #{record.refundOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.cancelOrderCnt != null">
        cancel_order_cnt = #{record.cancelOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.actualPayAmt != null">
        actual_pay_amt = #{record.actualPayAmt,jdbcType=BIGINT},
      </if>
      <if test="record.gmvAmt != null">
        gmv_amt = #{record.gmvAmt,jdbcType=BIGINT},
      </if>
      <if test="record.verifyGmvAmt != null">
        verify_gmv_amt = #{record.verifyGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="record.refundGmvAmt != null">
        refund_gmv_amt = #{record.refundGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="record.lastOrderTime != null">
        last_order_time = #{record.lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastVerifyTime != null">
        last_verify_time = #{record.lastVerifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.waitToPayGmvAmt != null">
        wait_to_pay_gmv_amt = #{record.waitToPayGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="record.inRefundGmvAmt != null">
        in_refund_gmv_amt = #{record.inRefundGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="record.inRefundOrderCnt != null">
        in_refund_order_cnt = #{record.inRefundOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="record.cancelGmvAmt != null">
        cancel_gmv_amt = #{record.cancelGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_consultant_user_statistics
    set id = #{record.id,jdbcType=BIGINT},
      mt_user_id = #{record.mtUserId,jdbcType=BIGINT},
      consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      live_id = #{record.liveId,jdbcType=VARCHAR},
      consultant_share_name = #{record.consultantShareName,jdbcType=VARCHAR},
      consultant_head_pic = #{record.consultantHeadPic,jdbcType=VARCHAR},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      trade_product_cnt = #{record.tradeProductCnt,jdbcType=INTEGER},
      trade_order_cnt = #{record.tradeOrderCnt,jdbcType=INTEGER},
      pay_order_cnt = #{record.payOrderCnt,jdbcType=INTEGER},
      wait_to_pay_order_cnt = #{record.waitToPayOrderCnt,jdbcType=INTEGER},
      wait_to_verify_order_cnt = #{record.waitToVerifyOrderCnt,jdbcType=INTEGER},
      verify_order_cnt = #{record.verifyOrderCnt,jdbcType=INTEGER},
      refund_order_cnt = #{record.refundOrderCnt,jdbcType=INTEGER},
      cancel_order_cnt = #{record.cancelOrderCnt,jdbcType=INTEGER},
      actual_pay_amt = #{record.actualPayAmt,jdbcType=BIGINT},
      gmv_amt = #{record.gmvAmt,jdbcType=BIGINT},
      verify_gmv_amt = #{record.verifyGmvAmt,jdbcType=BIGINT},
      refund_gmv_amt = #{record.refundGmvAmt,jdbcType=BIGINT},
      last_order_time = #{record.lastOrderTime,jdbcType=TIMESTAMP},
      last_verify_time = #{record.lastVerifyTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      wait_to_pay_gmv_amt = #{record.waitToPayGmvAmt,jdbcType=BIGINT},
      in_refund_gmv_amt = #{record.inRefundGmvAmt,jdbcType=BIGINT},
      in_refund_order_cnt = #{record.inRefundOrderCnt,jdbcType=INTEGER},
      cancel_gmv_amt = #{record.cancelGmvAmt,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
    update private_live_consultant_user_statistics
    <set>
      <if test="mtUserId != null">
        mt_user_id = #{mtUserId,jdbcType=BIGINT},
      </if>
      <if test="consultantTaskId != null">
        consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="consultantShareName != null">
        consultant_share_name = #{consultantShareName,jdbcType=VARCHAR},
      </if>
      <if test="consultantHeadPic != null">
        consultant_head_pic = #{consultantHeadPic,jdbcType=VARCHAR},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeProductCnt != null">
        trade_product_cnt = #{tradeProductCnt,jdbcType=INTEGER},
      </if>
      <if test="tradeOrderCnt != null">
        trade_order_cnt = #{tradeOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="payOrderCnt != null">
        pay_order_cnt = #{payOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="waitToPayOrderCnt != null">
        wait_to_pay_order_cnt = #{waitToPayOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="waitToVerifyOrderCnt != null">
        wait_to_verify_order_cnt = #{waitToVerifyOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="verifyOrderCnt != null">
        verify_order_cnt = #{verifyOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="refundOrderCnt != null">
        refund_order_cnt = #{refundOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="cancelOrderCnt != null">
        cancel_order_cnt = #{cancelOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="actualPayAmt != null">
        actual_pay_amt = #{actualPayAmt,jdbcType=BIGINT},
      </if>
      <if test="gmvAmt != null">
        gmv_amt = #{gmvAmt,jdbcType=BIGINT},
      </if>
      <if test="verifyGmvAmt != null">
        verify_gmv_amt = #{verifyGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="refundGmvAmt != null">
        refund_gmv_amt = #{refundGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="lastOrderTime != null">
        last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastVerifyTime != null">
        last_verify_time = #{lastVerifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="waitToPayGmvAmt != null">
        wait_to_pay_gmv_amt = #{waitToPayGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="inRefundGmvAmt != null">
        in_refund_gmv_amt = #{inRefundGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="inRefundOrderCnt != null">
        in_refund_order_cnt = #{inRefundOrderCnt,jdbcType=INTEGER},
      </if>
      <if test="cancelGmvAmt != null">
        cancel_gmv_amt = #{cancelGmvAmt,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
    update private_live_consultant_user_statistics
    set mt_user_id = #{mtUserId,jdbcType=BIGINT},
      consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      live_id = #{liveId,jdbcType=VARCHAR},
      consultant_share_name = #{consultantShareName,jdbcType=VARCHAR},
      consultant_head_pic = #{consultantHeadPic,jdbcType=VARCHAR},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      trade_product_cnt = #{tradeProductCnt,jdbcType=INTEGER},
      trade_order_cnt = #{tradeOrderCnt,jdbcType=INTEGER},
      pay_order_cnt = #{payOrderCnt,jdbcType=INTEGER},
      wait_to_pay_order_cnt = #{waitToPayOrderCnt,jdbcType=INTEGER},
      wait_to_verify_order_cnt = #{waitToVerifyOrderCnt,jdbcType=INTEGER},
      verify_order_cnt = #{verifyOrderCnt,jdbcType=INTEGER},
      refund_order_cnt = #{refundOrderCnt,jdbcType=INTEGER},
      cancel_order_cnt = #{cancelOrderCnt,jdbcType=INTEGER},
      actual_pay_amt = #{actualPayAmt,jdbcType=BIGINT},
      gmv_amt = #{gmvAmt,jdbcType=BIGINT},
      verify_gmv_amt = #{verifyGmvAmt,jdbcType=BIGINT},
      refund_gmv_amt = #{refundGmvAmt,jdbcType=BIGINT},
      last_order_time = #{lastOrderTime,jdbcType=TIMESTAMP},
      last_verify_time = #{lastVerifyTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      wait_to_pay_gmv_amt = #{waitToPayGmvAmt,jdbcType=BIGINT},
      in_refund_gmv_amt = #{inRefundGmvAmt,jdbcType=BIGINT},
      in_refund_order_cnt = #{inRefundOrderCnt,jdbcType=INTEGER},
      cancel_gmv_amt = #{cancelGmvAmt,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_consultant_user_statistics
    (mt_user_id, consultant_task_id, live_id, consultant_share_name, consultant_head_pic, 
      distributor_code, trade_product_cnt, trade_order_cnt, pay_order_cnt, wait_to_pay_order_cnt, 
      wait_to_verify_order_cnt, verify_order_cnt, refund_order_cnt, cancel_order_cnt, 
      actual_pay_amt, gmv_amt, verify_gmv_amt, refund_gmv_amt, last_order_time, last_verify_time, 
      add_time, update_time, wait_to_pay_gmv_amt, in_refund_gmv_amt, in_refund_order_cnt, 
      cancel_gmv_amt, status)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mtUserId,jdbcType=BIGINT}, #{item.consultantTaskId,jdbcType=BIGINT}, #{item.liveId,jdbcType=VARCHAR}, 
        #{item.consultantShareName,jdbcType=VARCHAR}, #{item.consultantHeadPic,jdbcType=VARCHAR}, 
        #{item.distributorCode,jdbcType=VARCHAR}, #{item.tradeProductCnt,jdbcType=INTEGER}, 
        #{item.tradeOrderCnt,jdbcType=INTEGER}, #{item.payOrderCnt,jdbcType=INTEGER}, #{item.waitToPayOrderCnt,jdbcType=INTEGER}, 
        #{item.waitToVerifyOrderCnt,jdbcType=INTEGER}, #{item.verifyOrderCnt,jdbcType=INTEGER}, 
        #{item.refundOrderCnt,jdbcType=INTEGER}, #{item.cancelOrderCnt,jdbcType=INTEGER}, 
        #{item.actualPayAmt,jdbcType=BIGINT}, #{item.gmvAmt,jdbcType=BIGINT}, #{item.verifyGmvAmt,jdbcType=BIGINT}, 
        #{item.refundGmvAmt,jdbcType=BIGINT}, #{item.lastOrderTime,jdbcType=TIMESTAMP}, 
        #{item.lastVerifyTime,jdbcType=TIMESTAMP}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.waitToPayGmvAmt,jdbcType=BIGINT}, 
        #{item.inRefundGmvAmt,jdbcType=BIGINT}, #{item.inRefundOrderCnt,jdbcType=INTEGER}, 
        #{item.cancelGmvAmt,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER})
    </foreach>
  </insert>
  <select id="queryRankByCondition" parameterType="java.util.Map" resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
      SELECT CASE WHEN consultant_task_id IS NULL THEN 0 ELSE consultant_task_id END AS consultantTaskId,
      CASE WHEN consultant_head_pic IS NULL THEN '' ELSE consultant_head_pic END AS consultantHeadPic,
      CASE WHEN consultant_share_name IS NULL THEN '' ELSE consultant_share_name END AS consultantShareName,
      CASE WHEN distributor_code IS NULL THEN '' ELSE distributor_code END AS distributorCode,
      SUM(trade_product_cnt) AS tradeProductCnt,
      SUM(trade_order_cnt) AS tradeOrderCnt,
      SUM(pay_order_cnt) AS payOrderCnt,
      SUM(wait_to_pay_order_cnt) AS waitToPayOrderCnt,
      SUM(wait_to_verify_order_cnt) AS waitToVerifyOrderCnt,
      SUM(verify_order_cnt) AS verifyOrderCnt,
      SUM(refund_order_cnt) AS refundOrderCnt,
      SUM(cancel_order_cnt) AS cancelOrderCnt,
      SUM(in_refund_order_cnt) AS inRefundOrderCnt,
      SUM(actual_pay_amt) AS actualPayAmt,
      SUM(in_refund_gmv_amt) AS inRefundGmvAmt,
      SUM(gmv_amt) AS gmvAmt,
      SUM(verify_gmv_amt) AS verifyGmvAmt,
      SUM(wait_to_pay_gmv_amt) AS waitToPayGmvAmt,
      SUM(refund_gmv_amt) AS refundGmvAmt,
      SUM(cancel_gmv_amt) AS cancelGmvAmt
      FROM private_live_consultant_user_statistics
      WHERE live_id = #{paramMap.liveId,jdbcType=VARCHAR}
        AND status = 1
      GROUP BY consultant_task_id
      <if test="paramMap.orderByClause != null">
          ORDER BY ${paramMap.orderByClause}
      </if>
  </select>

  <select id="selectSummaryByLiveId" resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
      SELECT
      SUM(trade_product_cnt) AS tradeProductCnt,
      SUM(trade_order_cnt) AS tradeOrderCnt,
      SUM(pay_order_cnt) AS payOrderCnt,
      SUM(wait_to_pay_order_cnt) AS waitToPayOrderCnt,
      SUM(wait_to_verify_order_cnt) AS waitToVerifyOrderCnt,
      SUM(verify_order_cnt) AS verifyOrderCnt,
      SUM(refund_order_cnt) AS refundOrderCnt,
      SUM(cancel_order_cnt) AS cancelOrderCnt,
      SUM(in_refund_order_cnt) AS inRefundOrderCnt,
      SUM(actual_pay_amt) AS actualPayAmt,
      SUM(in_refund_gmv_amt) AS inRefundGmvAmt,
      SUM(gmv_amt) AS gmvAmt,
      SUM(verify_gmv_amt) AS verifyGmvAmt,
      SUM(wait_to_pay_gmv_amt) AS waitToPayGmvAmt,
      SUM(refund_gmv_amt) AS refundGmvAmt,
      SUM(cancel_gmv_amt) AS cancelGmvAmt
      FROM private_live_consultant_user_statistics
      <if test="_parameter != null">
          <include refid="Example_Where_Clause" />
      </if>
  </select>

  <select id="selectSummaryByConsultantTaskIds" resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
      SELECT CASE WHEN consultant_task_id IS NULL THEN 0 ELSE consultant_task_id END AS consultantTaskId,
      CASE WHEN consultant_head_pic IS NULL THEN '' ELSE consultant_head_pic END AS consultantHeadPic,
      CASE WHEN consultant_share_name IS NULL THEN '' ELSE consultant_share_name END AS consultantShareName,
      CASE WHEN distributor_code IS NULL THEN '' ELSE distributor_code END AS distributorCode,
      mt_user_id AS mtUserId,
      SUM(trade_product_cnt) AS tradeProductCnt,
      SUM(trade_order_cnt) AS tradeOrderCnt,
      SUM(pay_order_cnt) AS payOrderCnt,
      SUM(wait_to_pay_order_cnt) AS waitToPayOrderCnt,
      SUM(wait_to_verify_order_cnt) AS waitToVerifyOrderCnt,
      SUM(verify_order_cnt) AS verifyOrderCnt,
      SUM(refund_order_cnt) AS refundOrderCnt,
      SUM(cancel_order_cnt) AS cancelOrderCnt,
      SUM(in_refund_order_cnt) AS inRefundOrderCnt,
      SUM(actual_pay_amt) AS actualPayAmt,
      SUM(in_refund_gmv_amt) AS inRefundGmvAmt,
      SUM(gmv_amt) AS gmvAmt,
      SUM(verify_gmv_amt) AS verifyGmvAmt,
      SUM(wait_to_pay_gmv_amt) AS waitToPayGmvAmt,
      SUM(refund_gmv_amt) AS refundGmvAmt,
      SUM(cancel_gmv_amt) AS cancelGmvAmt
      FROM private_live_consultant_user_statistics
      <if test="_parameter != null">
          <include refid="Example_Where_Clause" />
      </if>
      GROUP BY consultant_task_id
  </select>

  <select id="selectSummaryBossToolDataByLiveId" resultType="com.sankuai.carnation.distribution.privatelive.consultant.dto.summary.BossToolDataDTO">
      SELECT
          COUNT(DISTINCT pluir.union_id) AS newUserCnt,
          SUM(plcus.gmv_amt) AS newUserGmvAmt,
          SUM(plcus.trade_order_cnt) AS newUserOrderCnt
      FROM
          private_live_user_intention_result pluir
              LEFT JOIN private_live_consultant_user_statistics plcus ON pluir.user_id = plcus.mt_user_id
              and pluir.live_id = plcus.live_id
      WHERE
          pluir.status in (1, 3)
        AND
          pluir.live_id = #{liveId,jdbcType=VARCHAR}
        AND pluir.user_type = 2
        AND plcus.status = 1;
  </select>

  <select id="queryLiveUserStatistic" resultType = "com.sankuai.carnation.distribution.privatelive.consultant.bo.ConsultantUserStatisticBO">
    SELECT mt_user_id AS mtUserId,
           consultant_task_id AS consultantTaskId,
           gmv_amt AS gmvAmt,
           pay_order_cnt AS payOrderCnt
    FROM private_live_consultant_user_statistics
    WHERE live_id = #{liveId,jdbcType=VARCHAR}
      and status = 1
  </select>

  <insert id="batchInsertAndUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
      insert into private_live_consultant_user_statistics
      (mt_user_id, consultant_task_id, live_id, consultant_share_name, consultant_head_pic,
      distributor_code, trade_product_cnt, trade_order_cnt, pay_order_cnt, wait_to_pay_order_cnt,
      wait_to_verify_order_cnt, verify_order_cnt, refund_order_cnt, cancel_order_cnt,
      actual_pay_amt, gmv_amt, verify_gmv_amt, refund_gmv_amt, last_order_time, last_verify_time,
      add_time, update_time, wait_to_pay_gmv_amt,in_refund_order_cnt,in_refund_gmv_amt,cancel_gmv_amt,status)
      values
      <foreach collection="list" item="item" separator=",">
          (#{item.mtUserId,jdbcType=BIGINT}, #{item.consultantTaskId,jdbcType=BIGINT},
          #{item.liveId,jdbcType=VARCHAR},
          #{item.consultantShareName,jdbcType=VARCHAR}, #{item.consultantHeadPic,jdbcType=VARCHAR},
          #{item.distributorCode,jdbcType=VARCHAR}, #{item.tradeProductCnt,jdbcType=INTEGER},
          #{item.tradeOrderCnt,jdbcType=INTEGER}, #{item.payOrderCnt,jdbcType=INTEGER},
          #{item.waitToPayOrderCnt,jdbcType=INTEGER},
          #{item.waitToVerifyOrderCnt,jdbcType=INTEGER}, #{item.verifyOrderCnt,jdbcType=INTEGER},
          #{item.refundOrderCnt,jdbcType=INTEGER}, #{item.cancelOrderCnt,jdbcType=INTEGER},
          #{item.actualPayAmt,jdbcType=BIGINT}, #{item.gmvAmt,jdbcType=BIGINT},
          #{item.verifyGmvAmt,jdbcType=BIGINT},
          #{item.refundGmvAmt,jdbcType=BIGINT}, #{item.lastOrderTime,jdbcType=TIMESTAMP},
          #{item.lastVerifyTime,jdbcType=TIMESTAMP}, #{item.addTime,jdbcType=TIMESTAMP},
          #{item.updateTime,jdbcType=TIMESTAMP}, #{item.waitToPayGmvAmt,jdbcType=BIGINT},
          #{item.inRefundOrderCnt,jdbcType=INTEGER}, #{item.inRefundGmvAmt,jdbcType=BIGINT},
          #{item.cancelGmvAmt,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER})
      </foreach>
      on duplicate key update
      consultant_share_name = VALUES(consultant_share_name),
      consultant_head_pic = VALUES(consultant_head_pic),
      distributor_code = VALUES(distributor_code),
      trade_product_cnt = VALUES(trade_product_cnt),
      trade_order_cnt = VALUES(trade_order_cnt),
      pay_order_cnt = VALUES(pay_order_cnt),
      wait_to_pay_order_cnt = VALUES(wait_to_pay_order_cnt),
      wait_to_verify_order_cnt = VALUES(wait_to_verify_order_cnt),
      verify_order_cnt = VALUES(verify_order_cnt),
      refund_order_cnt = VALUES(refund_order_cnt),
      cancel_order_cnt = VALUES(cancel_order_cnt),
      actual_pay_amt = VALUES(actual_pay_amt),
      gmv_amt = VALUES(gmv_amt),
      verify_gmv_amt = VALUES(verify_gmv_amt),
      refund_gmv_amt = VALUES(refund_gmv_amt),
      last_order_time = VALUES(last_order_time),
      last_verify_time = VALUES(last_verify_time),
      update_time = NOW(),
      wait_to_pay_gmv_amt = VALUES(wait_to_pay_gmv_amt),
      in_refund_order_cnt = VALUES(in_refund_order_cnt),
      in_refund_gmv_amt = VALUES(in_refund_gmv_amt),
      cancel_gmv_amt = VALUES(cancel_gmv_amt),
      status = VALUES(status)
  </insert>

</mapper>