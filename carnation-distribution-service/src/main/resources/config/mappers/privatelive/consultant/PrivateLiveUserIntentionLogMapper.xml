<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveUserIntentionLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="live_id" jdbcType="VARCHAR" property="liveId" />
    <result column="consultant_task_id" jdbcType="BIGINT" property="consultantTaskId" />
    <result column="cal_result" jdbcType="INTEGER" property="calResult" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="related_record_id" jdbcType="BIGINT" property="relatedRecordId" />
    <result column="related_modify_id" jdbcType="BIGINT" property="relatedModifyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wx_id, union_id, user_id, live_id, consultant_task_id, cal_result, remark, type, 
    related_record_id, related_modify_id, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_user_intention_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from private_live_user_intention_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_user_intention_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionLogExample">
    delete from private_live_user_intention_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionLog" useGeneratedKeys="true">
    insert into private_live_user_intention_log (wx_id, union_id, user_id, 
      live_id, consultant_task_id, cal_result, 
      remark, type, related_record_id, 
      related_modify_id, create_time, update_time
      )
    values (#{wxId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{liveId,jdbcType=VARCHAR}, #{consultantTaskId,jdbcType=BIGINT}, #{calResult,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{relatedRecordId,jdbcType=BIGINT}, 
      #{relatedModifyId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionLog" useGeneratedKeys="true">
    insert into private_live_user_intention_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wxId != null">
        wx_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="liveId != null">
        live_id,
      </if>
      <if test="consultantTaskId != null">
        consultant_task_id,
      </if>
      <if test="calResult != null">
        cal_result,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="relatedRecordId != null">
        related_record_id,
      </if>
      <if test="relatedModifyId != null">
        related_modify_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wxId != null">
        #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="liveId != null">
        #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="consultantTaskId != null">
        #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="calResult != null">
        #{calResult,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="relatedRecordId != null">
        #{relatedRecordId,jdbcType=BIGINT},
      </if>
      <if test="relatedModifyId != null">
        #{relatedModifyId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionLogExample" resultType="java.lang.Long">
    select count(*) from private_live_user_intention_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_user_intention_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.wxId != null">
        wx_id = #{record.wxId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=VARCHAR},
      </if>
      <if test="record.consultantTaskId != null">
        consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.calResult != null">
        cal_result = #{record.calResult,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.relatedRecordId != null">
        related_record_id = #{record.relatedRecordId,jdbcType=BIGINT},
      </if>
      <if test="record.relatedModifyId != null">
        related_modify_id = #{record.relatedModifyId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_user_intention_log
    set id = #{record.id,jdbcType=BIGINT},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      live_id = #{record.liveId,jdbcType=VARCHAR},
      consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      cal_result = #{record.calResult,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      related_record_id = #{record.relatedRecordId,jdbcType=BIGINT},
      related_modify_id = #{record.relatedModifyId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionLog">
    update private_live_user_intention_log
    <set>
      <if test="wxId != null">
        wx_id = #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="consultantTaskId != null">
        consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="calResult != null">
        cal_result = #{calResult,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="relatedRecordId != null">
        related_record_id = #{relatedRecordId,jdbcType=BIGINT},
      </if>
      <if test="relatedModifyId != null">
        related_modify_id = #{relatedModifyId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionLog">
    update private_live_user_intention_log
    set wx_id = #{wxId,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      live_id = #{liveId,jdbcType=VARCHAR},
      consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      cal_result = #{calResult,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      related_record_id = #{relatedRecordId,jdbcType=BIGINT},
      related_modify_id = #{relatedModifyId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_user_intention_log
    (wx_id, union_id, user_id, live_id, consultant_task_id, cal_result, remark, type, 
      related_record_id, related_modify_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.wxId,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, 
        #{item.liveId,jdbcType=VARCHAR}, #{item.consultantTaskId,jdbcType=BIGINT}, #{item.calResult,jdbcType=INTEGER}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.relatedRecordId,jdbcType=BIGINT}, 
        #{item.relatedModifyId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>