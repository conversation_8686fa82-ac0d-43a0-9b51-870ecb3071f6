<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveOrderIntentionResultMapper">
    <resultMap id="BaseResultMap"
               type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="long_order_id" jdbcType="BIGINT" property="longOrderId"/>
        <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode"/>
        <result column="live_id" jdbcType="VARCHAR" property="liveId"/>
        <result column="consultant_task_id" jdbcType="BIGINT" property="consultantTaskId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="platform" jdbcType="INTEGER" property="platform"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_type" jdbcType="INTEGER" property="productType"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="pay_money" jdbcType="BIGINT" property="payMoney"/>
        <result column="total_money" jdbcType="BIGINT" property="totalMoney"/>
        <result column="verify_money" jdbcType="BIGINT" property="verifyMoney"/>
        <result column="in_refund_money" jdbcType="BIGINT" property="inRefundMoney"/>
        <result column="refund_money" jdbcType="BIGINT" property="refundMoney"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="sub_order_type" jdbcType="INTEGER" property="subOrderType"/>
        <result column="biz_code" jdbcType="VARCHAR" property="bizCode"/>
        <result column="field1" jdbcType="VARCHAR" property="field1"/>
        <result column="field2" jdbcType="VARCHAR" property="field2"/>
        <result column="field3" jdbcType="VARCHAR" property="field3"/>
        <result column="extra_info" jdbcType="VARCHAR" property="extraInfo"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , order_id, long_order_id, distributor_code, live_id, consultant_task_id, user_id,
    platform, sku_id, product_id, product_type, quantity, status, pay_money, total_money,
    verify_money, in_refund_money, refund_money, add_time, pay_time, create_time, update_time,
    sub_order_type, biz_code, field1, field2, field3, extra_info
    </sql>
    <select id="selectByExample"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveOrderIntentionResultExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from private_live_order_intention_result
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from private_live_order_intention_result
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from private_live_order_intention_result
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveOrderIntentionResultExample">
        delete from private_live_order_intention_result
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult"
            useGeneratedKeys="true">
        insert into private_live_order_intention_result (order_id, long_order_id, distributor_code,
                                                         live_id, consultant_task_id, user_id,
                                                         platform, sku_id, product_id,
                                                         product_type, quantity, status,
                                                         pay_money, total_money, verify_money,
                                                         in_refund_money, refund_money, add_time,
                                                         pay_time, create_time, update_time,
                                                         sub_order_type, biz_code, field1,
                                                         field2, field3, extra_info)
        values (#{orderId,jdbcType=VARCHAR}, #{longOrderId,jdbcType=BIGINT}, #{distributorCode,jdbcType=VARCHAR},
                #{liveId,jdbcType=VARCHAR}, #{consultantTaskId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
                #{platform,jdbcType=INTEGER}, #{skuId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT},
                #{productType,jdbcType=INTEGER}, #{quantity,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
                #{payMoney,jdbcType=BIGINT}, #{totalMoney,jdbcType=BIGINT}, #{verifyMoney,jdbcType=BIGINT},
                #{inRefundMoney,jdbcType=BIGINT}, #{refundMoney,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP},
                #{payTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{subOrderType,jdbcType=INTEGER}, #{bizCode,jdbcType=VARCHAR}, #{field1,jdbcType=VARCHAR},
                #{field2,jdbcType=VARCHAR}, #{field3,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult"
            useGeneratedKeys="true">
        insert into private_live_order_intention_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="longOrderId != null">
                long_order_id,
            </if>
            <if test="distributorCode != null">
                distributor_code,
            </if>
            <if test="liveId != null">
                live_id,
            </if>
            <if test="consultantTaskId != null">
                consultant_task_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="skuId != null">
                sku_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="productType != null">
                product_type,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="payMoney != null">
                pay_money,
            </if>
            <if test="totalMoney != null">
                total_money,
            </if>
            <if test="verifyMoney != null">
                verify_money,
            </if>
            <if test="inRefundMoney != null">
                in_refund_money,
            </if>
            <if test="refundMoney != null">
                refund_money,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="subOrderType != null">
                sub_order_type,
            </if>
            <if test="bizCode != null">
                biz_code,
            </if>
            <if test="field1 != null">
                field1,
            </if>
            <if test="field2 != null">
                field2,
            </if>
            <if test="field3 != null">
                field3,
            </if>
            <if test="extraInfo != null">
                extra_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="longOrderId != null">
                #{longOrderId,jdbcType=BIGINT},
            </if>
            <if test="distributorCode != null">
                #{distributorCode,jdbcType=VARCHAR},
            </if>
            <if test="liveId != null">
                #{liveId,jdbcType=VARCHAR},
            </if>
            <if test="consultantTaskId != null">
                #{consultantTaskId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=BIGINT},
            </if>
            <if test="productType != null">
                #{productType,jdbcType=INTEGER},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="payMoney != null">
                #{payMoney,jdbcType=BIGINT},
            </if>
            <if test="totalMoney != null">
                #{totalMoney,jdbcType=BIGINT},
            </if>
            <if test="verifyMoney != null">
                #{verifyMoney,jdbcType=BIGINT},
            </if>
            <if test="inRefundMoney != null">
                #{inRefundMoney,jdbcType=BIGINT},
            </if>
            <if test="refundMoney != null">
                #{refundMoney,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="subOrderType != null">
                #{subOrderType,jdbcType=INTEGER},
            </if>
            <if test="bizCode != null">
                #{bizCode,jdbcType=VARCHAR},
            </if>
            <if test="field1 != null">
                #{field1,jdbcType=VARCHAR},
            </if>
            <if test="field2 != null">
                #{field2,jdbcType=VARCHAR},
            </if>
            <if test="field3 != null">
                #{field3,jdbcType=VARCHAR},
            </if>
            <if test="extraInfo != null">
                #{extraInfo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveOrderIntentionResultExample"
            resultType="java.lang.Long">
        select count(*) from private_live_order_intention_result
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update private_live_order_intention_result
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.orderId != null">
                order_id = #{record.orderId,jdbcType=VARCHAR},
            </if>
            <if test="record.longOrderId != null">
                long_order_id = #{record.longOrderId,jdbcType=BIGINT},
            </if>
            <if test="record.distributorCode != null">
                distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
            </if>
            <if test="record.liveId != null">
                live_id = #{record.liveId,jdbcType=VARCHAR},
            </if>
            <if test="record.consultantTaskId != null">
                consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
            </if>
            <if test="record.userId != null">
                user_id = #{record.userId,jdbcType=BIGINT},
            </if>
            <if test="record.platform != null">
                platform = #{record.platform,jdbcType=INTEGER},
            </if>
            <if test="record.skuId != null">
                sku_id = #{record.skuId,jdbcType=BIGINT},
            </if>
            <if test="record.productId != null">
                product_id = #{record.productId,jdbcType=BIGINT},
            </if>
            <if test="record.productType != null">
                product_type = #{record.productType,jdbcType=INTEGER},
            </if>
            <if test="record.quantity != null">
                quantity = #{record.quantity,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.payMoney != null">
                pay_money = #{record.payMoney,jdbcType=BIGINT},
            </if>
            <if test="record.totalMoney != null">
                total_money = #{record.totalMoney,jdbcType=BIGINT},
            </if>
            <if test="record.verifyMoney != null">
                verify_money = #{record.verifyMoney,jdbcType=BIGINT},
            </if>
            <if test="record.inRefundMoney != null">
                in_refund_money = #{record.inRefundMoney,jdbcType=BIGINT},
            </if>
            <if test="record.refundMoney != null">
                refund_money = #{record.refundMoney,jdbcType=BIGINT},
            </if>
            <if test="record.addTime != null">
                add_time = #{record.addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.payTime != null">
                pay_time = #{record.payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.subOrderType != null">
                sub_order_type = #{record.subOrderType,jdbcType=INTEGER},
            </if>
            <if test="record.bizCode != null">
                biz_code = #{record.bizCode,jdbcType=VARCHAR},
            </if>
            <if test="record.field1 != null">
                field1 = #{record.field1,jdbcType=VARCHAR},
            </if>
            <if test="record.field2 != null">
                field2 = #{record.field2,jdbcType=VARCHAR},
            </if>
            <if test="record.field3 != null">
                field3 = #{record.field3,jdbcType=VARCHAR},
            </if>
            <if test="record.extraInfo != null">
                extra_info = #{record.extraInfo,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update private_live_order_intention_result
        set id = #{record.id,jdbcType=BIGINT},
        order_id = #{record.orderId,jdbcType=VARCHAR},
        long_order_id = #{record.longOrderId,jdbcType=BIGINT},
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
        live_id = #{record.liveId,jdbcType=VARCHAR},
        consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
        user_id = #{record.userId,jdbcType=BIGINT},
        platform = #{record.platform,jdbcType=INTEGER},
        sku_id = #{record.skuId,jdbcType=BIGINT},
        product_id = #{record.productId,jdbcType=BIGINT},
        product_type = #{record.productType,jdbcType=INTEGER},
        quantity = #{record.quantity,jdbcType=INTEGER},
        status = #{record.status,jdbcType=INTEGER},
        pay_money = #{record.payMoney,jdbcType=BIGINT},
        total_money = #{record.totalMoney,jdbcType=BIGINT},
        verify_money = #{record.verifyMoney,jdbcType=BIGINT},
        in_refund_money = #{record.inRefundMoney,jdbcType=BIGINT},
        refund_money = #{record.refundMoney,jdbcType=BIGINT},
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        sub_order_type = #{record.subOrderType,jdbcType=INTEGER},
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
        field1 = #{record.field1,jdbcType=VARCHAR},
        field2 = #{record.field2,jdbcType=VARCHAR},
        field3 = #{record.field3,jdbcType=VARCHAR},
        extra_info = #{record.extraInfo,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult">
        update private_live_order_intention_result
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="longOrderId != null">
                long_order_id = #{longOrderId,jdbcType=BIGINT},
            </if>
            <if test="distributorCode != null">
                distributor_code = #{distributorCode,jdbcType=VARCHAR},
            </if>
            <if test="liveId != null">
                live_id = #{liveId,jdbcType=VARCHAR},
            </if>
            <if test="consultantTaskId != null">
                consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=BIGINT},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=INTEGER},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="payMoney != null">
                pay_money = #{payMoney,jdbcType=BIGINT},
            </if>
            <if test="totalMoney != null">
                total_money = #{totalMoney,jdbcType=BIGINT},
            </if>
            <if test="verifyMoney != null">
                verify_money = #{verifyMoney,jdbcType=BIGINT},
            </if>
            <if test="inRefundMoney != null">
                in_refund_money = #{inRefundMoney,jdbcType=BIGINT},
            </if>
            <if test="refundMoney != null">
                refund_money = #{refundMoney,jdbcType=BIGINT},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="subOrderType != null">
                sub_order_type = #{subOrderType,jdbcType=INTEGER},
            </if>
            <if test="bizCode != null">
                biz_code = #{bizCode,jdbcType=VARCHAR},
            </if>
            <if test="field1 != null">
                field1 = #{field1,jdbcType=VARCHAR},
            </if>
            <if test="field2 != null">
                field2 = #{field2,jdbcType=VARCHAR},
            </if>
            <if test="field3 != null">
                field3 = #{field3,jdbcType=VARCHAR},
            </if>
            <if test="extraInfo != null">
                extra_info = #{extraInfo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveOrderIntentionResult">
        update private_live_order_intention_result
        set order_id           = #{orderId,jdbcType=VARCHAR},
            long_order_id      = #{longOrderId,jdbcType=BIGINT},
            distributor_code   = #{distributorCode,jdbcType=VARCHAR},
            live_id            = #{liveId,jdbcType=VARCHAR},
            consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
            user_id            = #{userId,jdbcType=BIGINT},
            platform           = #{platform,jdbcType=INTEGER},
            sku_id             = #{skuId,jdbcType=BIGINT},
            product_id         = #{productId,jdbcType=BIGINT},
            product_type       = #{productType,jdbcType=INTEGER},
            quantity           = #{quantity,jdbcType=INTEGER},
            status             = #{status,jdbcType=INTEGER},
            pay_money          = #{payMoney,jdbcType=BIGINT},
            total_money        = #{totalMoney,jdbcType=BIGINT},
            verify_money       = #{verifyMoney,jdbcType=BIGINT},
            in_refund_money    = #{inRefundMoney,jdbcType=BIGINT},
            refund_money       = #{refundMoney,jdbcType=BIGINT},
            add_time           = #{addTime,jdbcType=TIMESTAMP},
            pay_time           = #{payTime,jdbcType=TIMESTAMP},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            sub_order_type     = #{subOrderType,jdbcType=INTEGER},
            biz_code           = #{bizCode,jdbcType=VARCHAR},
            field1             = #{field1,jdbcType=VARCHAR},
            field2             = #{field2,jdbcType=VARCHAR},
            field3             = #{field3,jdbcType=VARCHAR},
            extra_info         = #{extraInfo,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into private_live_order_intention_result
        (order_id, long_order_id, distributor_code, live_id, consultant_task_id, user_id,
        platform, sku_id, product_id, product_type, quantity, status, pay_money, total_money,
        verify_money, in_refund_money, refund_money, add_time, pay_time, create_time, update_time,
        sub_order_type, biz_code, field1, field2, field3, extra_info)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId,jdbcType=VARCHAR}, #{item.longOrderId,jdbcType=BIGINT},
            #{item.distributorCode,jdbcType=VARCHAR},
            #{item.liveId,jdbcType=VARCHAR}, #{item.consultantTaskId,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT},
            #{item.platform,jdbcType=INTEGER}, #{item.skuId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT},
            #{item.productType,jdbcType=INTEGER}, #{item.quantity,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},
            #{item.payMoney,jdbcType=BIGINT}, #{item.totalMoney,jdbcType=BIGINT}, #{item.verifyMoney,jdbcType=BIGINT},
            #{item.inRefundMoney,jdbcType=BIGINT}, #{item.refundMoney,jdbcType=BIGINT},
            #{item.addTime,jdbcType=TIMESTAMP},
            #{item.payTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.subOrderType,jdbcType=INTEGER}, #{item.bizCode,jdbcType=VARCHAR}, #{item.field1,jdbcType=VARCHAR},
            #{item.field2,jdbcType=VARCHAR}, #{item.field3,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectSummaryDataByExample"
            parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveOrderIntentionResultExample"
            resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveConsultantUserStatistics">
        SELECT user_id as mtUserId,
        consultant_task_id as consultantTaskId,
        live_id as liveId,
        distributor_code as distributorCode,
        SUM(quantity) as tradeProductCnt,
        COUNT(order_id) as tradeOrderCnt,
        COUNT(CASE WHEN status = 2 OR status = 3 OR status = 4 THEN 1 END) as payOrderCnt,
        COUNT(IF(status = 1, 1, NULL)) as waitToPayOrderCnt,
        COUNT(IF(status = 2, 1, NULL)) as waitToVerifyOrderCnt,
        COUNT(IF(status = 3, 1, NULL)) as verifyOrderCnt,
        COUNT(IF(status = 4, 1, NULL)) as refundOrderCnt,
        COUNT(IF(status = 5, 1, NULL)) as cancelOrderCnt,
        COUNT(IF(status = 6, 1, NULL)) as inRefundOrderCnt,
        SUM(pay_money) as actualPayAmt,
        SUM(CASE WHEN status = 2 OR status = 3 OR status = 4 THEN total_money ELSE 0 END) as gmvAmt,
        SUM(verify_money) as verifyGmvAmt,
        SUM(refund_money) as refundGmvAmt,
        SUM(in_refund_money) as inRefundGmvAmt,
        MAX(add_time) as lastOrderTime,
        MAX(IF(status = 3, update_time, NULL)) as lastVerifyTime,
        MIN(add_time) as addTime,
        NOW() as update_time,
        SUM(IF(status = 1, total_money, 0)) as waitToPayGmvAmt,
        SUM(IF(status = 5, total_money, 0)) as cancelGmvAmt,
        1 as status
        FROM
        private_live_order_intention_result
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY
        user_id,
        consultant_task_id,
        live_id,
        distributor_code
    </select>

    <select id="selectHotProductByLiveId" parameterType="java.lang.String"
            resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveHotProduct">
        SELECT product_id                                           as productId,
               product_type                                         as productType,
               SUM(quantity)                                        as tradeProductCnt,
               COUNT(CASE WHEN status = 2 OR status = 3 THEN 1 END) as payOrderCnt,
               SUM(pay_money)                                       as actualPayAmt
        FROM private_live_order_intention_result
        WHERE live_id = #{liveId,jdbcType=VARCHAR}
        GROUP BY product_id,
                 product_type
        ORDER BY actualPayAmt desc
    </select>
</mapper>