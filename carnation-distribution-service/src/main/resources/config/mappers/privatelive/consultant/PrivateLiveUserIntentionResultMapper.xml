<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveUserIntentionResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="wx_nick_name" jdbcType="VARCHAR" property="wxNickName" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="live_id" jdbcType="VARCHAR" property="liveId" />
    <result column="consultant_task_id" jdbcType="BIGINT" property="consultantTaskId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="intention_type" jdbcType="INTEGER" property="intentionType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wx_id, union_id, user_id, wx_nick_name, distributor_code, live_id, consultant_task_id, 
    status, user_type, create_time, update_time,intention_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from private_live_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionResultExample">
    delete from private_live_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult" useGeneratedKeys="true">
    insert into private_live_user_intention_result (wx_id, union_id, user_id, 
      wx_nick_name, distributor_code, live_id, 
      consultant_task_id, status, user_type, 
      create_time, update_time,intention_type)
    values (#{wxId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{wxNickName,jdbcType=VARCHAR}, #{distributorCode,jdbcType=VARCHAR}, #{liveId,jdbcType=VARCHAR}, 
      #{consultantTaskId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{userType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{intentionType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult" useGeneratedKeys="true">
    insert into private_live_user_intention_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wxId != null">
        wx_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="wxNickName != null">
        wx_nick_name,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="liveId != null">
        live_id,
      </if>
      <if test="consultantTaskId != null">
        consultant_task_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="intentionType != null">
        intention_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wxId != null">
        #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="wxNickName != null">
        #{wxNickName,jdbcType=VARCHAR},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="liveId != null">
        #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="consultantTaskId != null">
        #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="intentionType != null">
        #{intentionType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveUserIntentionResultExample" resultType="java.lang.Long">
    select count(*) from private_live_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_user_intention_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.wxId != null">
        wx_id = #{record.wxId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.wxNickName != null">
        wx_nick_name = #{record.wxNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=VARCHAR},
      </if>
      <if test="record.consultantTaskId != null">
        consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.intentionType != null">
        intention_type = #{record.intentionType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_user_intention_result
    set id = #{record.id,jdbcType=BIGINT},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      wx_nick_name = #{record.wxNickName,jdbcType=VARCHAR},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      live_id = #{record.liveId,jdbcType=VARCHAR},
      consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      user_type = #{record.userType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      intention_type = #{record.intentionType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult">
    update private_live_user_intention_result
    <set>
      <if test="wxId != null">
        wx_id = #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="wxNickName != null">
        wx_nick_name = #{wxNickName,jdbcType=VARCHAR},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="consultantTaskId != null">
        consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="intentionType != null">
        intention_type = #{intentionType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserIntentionResult">
    update private_live_user_intention_result
    set wx_id = #{wxId,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      wx_nick_name = #{wxNickName,jdbcType=VARCHAR},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      live_id = #{liveId,jdbcType=VARCHAR},
      consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      user_type = #{userType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      intention_type = #{intentionType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_user_intention_result
    (wx_id, union_id, user_id, wx_nick_name, distributor_code, live_id, consultant_task_id, 
      status, user_type, create_time, update_time,intention_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.wxId,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, 
        #{item.wxNickName,jdbcType=VARCHAR}, #{item.distributorCode,jdbcType=VARCHAR}, 
        #{item.liveId,jdbcType=VARCHAR}, #{item.consultantTaskId,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, 
        #{item.userType,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.intentionType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <select id="countUserTypeByConsultantTaskId" resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserTypeCount">
    SELECT
      consultant_task_id as consultantTaskId,
      user_type as type,
      COUNT(user_type) as count
    FROM
      private_live_user_intention_result
    <where>
      status in (1, 3, 4)
      <if test="liveId != null and liveId != ''">
        and live_id = #{liveId,jdbcType=VARCHAR}
      </if>
      <if test="taskIdList != null and taskIdList.size() > 0">
        and consultant_task_id in
        <foreach collection="taskIdList" item="taskId" separator="," open="(" close=")">
          #{taskId, jdbcType=BIGINT}
        </foreach>
      </if>
--       and consultant_task_id != 0
    </where>
    GROUP BY
      consultant_task_id, user_type;
  </select>

  <select id="countUserTypeByLiveId" resultType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveUserTypeBaseCount">
    SELECT
    user_type as type,
    COUNT(user_type) as count
    FROM
    private_live_user_intention_result
    <where>
      status in (1, 3, 4)
      and live_id = #{liveId,jdbcType=VARCHAR}
--       and consultant_task_id != 0
    </where>
    GROUP BY
    user_type;
  </select>
</mapper>