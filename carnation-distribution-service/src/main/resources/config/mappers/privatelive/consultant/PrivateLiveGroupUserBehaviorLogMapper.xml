<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveGroupUserBehaviorLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveGroupUserBehaviorLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="consultant_task_id" jdbcType="BIGINT" property="consultantTaskId" />
    <result column="chatroom_serial_no" jdbcType="VARCHAR" property="chatroomSerialNo" />
    <result column="wx_id" jdbcType="VARCHAR" property="wxId" />
    <result column="union_id" jdbcType="VARCHAR" property="unionId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="wx_nick_name" jdbcType="VARCHAR" property="wxNickName" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="action_type" jdbcType="INTEGER" property="actionType" />
    <result column="action_time" jdbcType="TIMESTAMP" property="actionTime" />
    <result column="liveId" jdbcType="VARCHAR" property="liveid" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, consultant_task_id, chatroom_serial_no, wx_id, union_id, user_id, wx_nick_name, 
    user_type, action_type, action_time, liveId, extra, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveGroupUserBehaviorLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from private_live_group_user_behavior_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_group_user_behavior_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveGroupUserBehaviorLogExample">
    delete from private_live_group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveGroupUserBehaviorLog" useGeneratedKeys="true">
    insert into private_live_group_user_behavior_log (consultant_task_id, chatroom_serial_no, 
      wx_id, union_id, user_id, 
      wx_nick_name, user_type, action_type, 
      action_time, liveId, extra, 
      create_time, update_time)
    values (#{consultantTaskId,jdbcType=BIGINT}, #{chatroomSerialNo,jdbcType=VARCHAR}, 
      #{wxId,jdbcType=VARCHAR}, #{unionId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{wxNickName,jdbcType=VARCHAR}, #{userType,jdbcType=INTEGER}, #{actionType,jdbcType=INTEGER}, 
      #{actionTime,jdbcType=TIMESTAMP}, #{liveid,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveGroupUserBehaviorLog" useGeneratedKeys="true">
    insert into private_live_group_user_behavior_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="consultantTaskId != null">
        consultant_task_id,
      </if>
      <if test="chatroomSerialNo != null">
        chatroom_serial_no,
      </if>
      <if test="wxId != null">
        wx_id,
      </if>
      <if test="unionId != null">
        union_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="wxNickName != null">
        wx_nick_name,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="actionType != null">
        action_type,
      </if>
      <if test="actionTime != null">
        action_time,
      </if>
      <if test="liveid != null">
        liveId,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="consultantTaskId != null">
        #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="chatroomSerialNo != null">
        #{chatroomSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="wxId != null">
        #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="wxNickName != null">
        #{wxNickName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="liveid != null">
        #{liveid,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveGroupUserBehaviorLogExample" resultType="java.lang.Long">
    select count(*) from private_live_group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_group_user_behavior_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.consultantTaskId != null">
        consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.chatroomSerialNo != null">
        chatroom_serial_no = #{record.chatroomSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.wxId != null">
        wx_id = #{record.wxId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionId != null">
        union_id = #{record.unionId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.wxNickName != null">
        wx_nick_name = #{record.wxNickName,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=INTEGER},
      </if>
      <if test="record.actionType != null">
        action_type = #{record.actionType,jdbcType=INTEGER},
      </if>
      <if test="record.actionTime != null">
        action_time = #{record.actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.liveid != null">
        liveId = #{record.liveid,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_group_user_behavior_log
    set id = #{record.id,jdbcType=BIGINT},
      consultant_task_id = #{record.consultantTaskId,jdbcType=BIGINT},
      chatroom_serial_no = #{record.chatroomSerialNo,jdbcType=VARCHAR},
      wx_id = #{record.wxId,jdbcType=VARCHAR},
      union_id = #{record.unionId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      wx_nick_name = #{record.wxNickName,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=INTEGER},
      action_type = #{record.actionType,jdbcType=INTEGER},
      action_time = #{record.actionTime,jdbcType=TIMESTAMP},
      liveId = #{record.liveid,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveGroupUserBehaviorLog">
    update private_live_group_user_behavior_log
    <set>
      <if test="consultantTaskId != null">
        consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="chatroomSerialNo != null">
        chatroom_serial_no = #{chatroomSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="wxId != null">
        wx_id = #{wxId,jdbcType=VARCHAR},
      </if>
      <if test="unionId != null">
        union_id = #{unionId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="wxNickName != null">
        wx_nick_name = #{wxNickName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="actionType != null">
        action_type = #{actionType,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        action_time = #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="liveid != null">
        liveId = #{liveid,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveGroupUserBehaviorLog">
    update private_live_group_user_behavior_log
    set consultant_task_id = #{consultantTaskId,jdbcType=BIGINT},
      chatroom_serial_no = #{chatroomSerialNo,jdbcType=VARCHAR},
      wx_id = #{wxId,jdbcType=VARCHAR},
      union_id = #{unionId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      wx_nick_name = #{wxNickName,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=INTEGER},
      action_type = #{actionType,jdbcType=INTEGER},
      action_time = #{actionTime,jdbcType=TIMESTAMP},
      liveId = #{liveid,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_group_user_behavior_log
    (consultant_task_id, chatroom_serial_no, wx_id, union_id, user_id, wx_nick_name, 
      user_type, action_type, action_time, liveId, extra, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.consultantTaskId,jdbcType=BIGINT}, #{item.chatroomSerialNo,jdbcType=VARCHAR}, 
        #{item.wxId,jdbcType=VARCHAR}, #{item.unionId,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, 
        #{item.wxNickName,jdbcType=VARCHAR}, #{item.userType,jdbcType=INTEGER}, #{item.actionType,jdbcType=INTEGER}, 
        #{item.actionTime,jdbcType=TIMESTAMP}, #{item.liveid,jdbcType=VARCHAR}, #{item.extra,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>