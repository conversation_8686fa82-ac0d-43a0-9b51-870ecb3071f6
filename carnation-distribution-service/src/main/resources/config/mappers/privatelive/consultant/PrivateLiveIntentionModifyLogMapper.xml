<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.privatelive.consultant.repository.mapper.PrivateLiveIntentionModifyLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionModifyLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="modify_type" jdbcType="INTEGER" property="modifyType" />
    <result column="live_id" jdbcType="VARCHAR" property="liveId" />
    <result column="former_consultant_task_id" jdbcType="BIGINT" property="formerConsultantTaskId" />
    <result column="target_consultant_task_id" jdbcType="BIGINT" property="targetConsultantTaskId" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, user_type, modify_type, live_id, former_consultant_task_id, target_consultant_task_id, 
    operator_id, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveIntentionModifyLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from private_live_intention_modify_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from private_live_intention_modify_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from private_live_intention_modify_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveIntentionModifyLogExample">
    delete from private_live_intention_modify_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionModifyLog" useGeneratedKeys="true">
    insert into private_live_intention_modify_log (user_id, user_type, modify_type, 
      live_id, former_consultant_task_id, target_consultant_task_id, 
      operator_id, create_time, update_time
      )
    values (#{userId,jdbcType=VARCHAR}, #{userType,jdbcType=INTEGER}, #{modifyType,jdbcType=INTEGER}, 
      #{liveId,jdbcType=VARCHAR}, #{formerConsultantTaskId,jdbcType=BIGINT}, #{targetConsultantTaskId,jdbcType=BIGINT}, 
      #{operatorId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionModifyLog" useGeneratedKeys="true">
    insert into private_live_intention_modify_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="modifyType != null">
        modify_type,
      </if>
      <if test="liveId != null">
        live_id,
      </if>
      <if test="formerConsultantTaskId != null">
        former_consultant_task_id,
      </if>
      <if test="targetConsultantTaskId != null">
        target_consultant_task_id,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="modifyType != null">
        #{modifyType,jdbcType=INTEGER},
      </if>
      <if test="liveId != null">
        #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="formerConsultantTaskId != null">
        #{formerConsultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="targetConsultantTaskId != null">
        #{targetConsultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.example.PrivateLiveIntentionModifyLogExample" resultType="java.lang.Long">
    select count(*) from private_live_intention_modify_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update private_live_intention_modify_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=INTEGER},
      </if>
      <if test="record.modifyType != null">
        modify_type = #{record.modifyType,jdbcType=INTEGER},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=VARCHAR},
      </if>
      <if test="record.formerConsultantTaskId != null">
        former_consultant_task_id = #{record.formerConsultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.targetConsultantTaskId != null">
        target_consultant_task_id = #{record.targetConsultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update private_live_intention_modify_log
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=INTEGER},
      modify_type = #{record.modifyType,jdbcType=INTEGER},
      live_id = #{record.liveId,jdbcType=VARCHAR},
      former_consultant_task_id = #{record.formerConsultantTaskId,jdbcType=BIGINT},
      target_consultant_task_id = #{record.targetConsultantTaskId,jdbcType=BIGINT},
      operator_id = #{record.operatorId,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionModifyLog">
    update private_live_intention_modify_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="modifyType != null">
        modify_type = #{modifyType,jdbcType=INTEGER},
      </if>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=VARCHAR},
      </if>
      <if test="formerConsultantTaskId != null">
        former_consultant_task_id = #{formerConsultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="targetConsultantTaskId != null">
        target_consultant_task_id = #{targetConsultantTaskId,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.privatelive.consultant.repository.entity.PrivateLiveIntentionModifyLog">
    update private_live_intention_modify_log
    set user_id = #{userId,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=INTEGER},
      modify_type = #{modifyType,jdbcType=INTEGER},
      live_id = #{liveId,jdbcType=VARCHAR},
      former_consultant_task_id = #{formerConsultantTaskId,jdbcType=BIGINT},
      target_consultant_task_id = #{targetConsultantTaskId,jdbcType=BIGINT},
      operator_id = #{operatorId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into private_live_intention_modify_log
    (user_id, user_type, modify_type, live_id, former_consultant_task_id, target_consultant_task_id, 
      operator_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=VARCHAR}, #{item.userType,jdbcType=INTEGER}, #{item.modifyType,jdbcType=INTEGER}, 
        #{item.liveId,jdbcType=VARCHAR}, #{item.formerConsultantTaskId,jdbcType=BIGINT}, 
        #{item.targetConsultantTaskId,jdbcType=BIGINT}, #{item.operatorId,jdbcType=BIGINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>