<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.minicode.repository.dao.ShortSearchCodeConfigMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="bind_user_type" jdbcType="INTEGER" property="bindUserType" />
    <result column="bind_user_id" jdbcType="BIGINT" property="bindUserId" />
    <result column="short_code" jdbcType="VARCHAR" property="shortCode" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="redirect_link" jdbcType="VARCHAR" property="redirectLink" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, biz_id, bind_user_type, bind_user_id, short_code, platform, city_id, 
    redirect_link, status, ext_info, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from short_search_code_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from short_search_code_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from short_search_code_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfigExample">
    delete from short_search_code_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfig" useGeneratedKeys="true">
    insert into short_search_code_config (biz_code, biz_id, bind_user_type, 
      bind_user_id, short_code, platform, 
      city_id, redirect_link, status, 
      ext_info, add_time, update_time
      )
    values (#{bizCode,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, #{bindUserType,jdbcType=INTEGER}, 
      #{bindUserId,jdbcType=BIGINT}, #{shortCode,jdbcType=VARCHAR}, #{platform,jdbcType=INTEGER}, 
      #{cityId,jdbcType=INTEGER}, #{redirectLink,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{extInfo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfig" useGeneratedKeys="true">
    insert into short_search_code_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bindUserType != null">
        bind_user_type,
      </if>
      <if test="bindUserId != null">
        bind_user_id,
      </if>
      <if test="shortCode != null">
        short_code,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="redirectLink != null">
        redirect_link,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="bindUserType != null">
        #{bindUserType,jdbcType=INTEGER},
      </if>
      <if test="bindUserId != null">
        #{bindUserId,jdbcType=BIGINT},
      </if>
      <if test="shortCode != null">
        #{shortCode,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="redirectLink != null">
        #{redirectLink,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfigExample" resultType="java.lang.Long">
    select count(*) from short_search_code_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update short_search_code_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.bindUserType != null">
        bind_user_type = #{record.bindUserType,jdbcType=INTEGER},
      </if>
      <if test="record.bindUserId != null">
        bind_user_id = #{record.bindUserId,jdbcType=BIGINT},
      </if>
      <if test="record.shortCode != null">
        short_code = #{record.shortCode,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.redirectLink != null">
        redirect_link = #{record.redirectLink,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update short_search_code_config
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      bind_user_type = #{record.bindUserType,jdbcType=INTEGER},
      bind_user_id = #{record.bindUserId,jdbcType=BIGINT},
      short_code = #{record.shortCode,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=INTEGER},
      city_id = #{record.cityId,jdbcType=INTEGER},
      redirect_link = #{record.redirectLink,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfig">
    update short_search_code_config
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="bindUserType != null">
        bind_user_type = #{bindUserType,jdbcType=INTEGER},
      </if>
      <if test="bindUserId != null">
        bind_user_id = #{bindUserId,jdbcType=BIGINT},
      </if>
      <if test="shortCode != null">
        short_code = #{shortCode,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="redirectLink != null">
        redirect_link = #{redirectLink,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.minicode.repository.db.ShortSearchCodeConfig">
    update short_search_code_config
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      bind_user_type = #{bindUserType,jdbcType=INTEGER},
      bind_user_id = #{bindUserId,jdbcType=BIGINT},
      short_code = #{shortCode,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=INTEGER},
      city_id = #{cityId,jdbcType=INTEGER},
      redirect_link = #{redirectLink,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into short_search_code_config
    (biz_code, biz_id, bind_user_type, bind_user_id, short_code, platform, city_id, redirect_link, 
      status, ext_info, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizCode,jdbcType=VARCHAR}, #{item.bizId,jdbcType=VARCHAR}, #{item.bindUserType,jdbcType=INTEGER}, 
        #{item.bindUserId,jdbcType=BIGINT}, #{item.shortCode,jdbcType=VARCHAR}, #{item.platform,jdbcType=INTEGER}, 
        #{item.cityId,jdbcType=INTEGER}, #{item.redirectLink,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.extInfo,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>