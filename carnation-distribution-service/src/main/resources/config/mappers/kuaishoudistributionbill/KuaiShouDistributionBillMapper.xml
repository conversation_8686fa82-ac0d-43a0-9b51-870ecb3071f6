<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.kuaishoubill.repository.dao.KuaiShouDistributionBillMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.kuaishoubill.repository.entity.KuaiShouDistributionBillEntity">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="bill_date" jdbcType="VARCHAR" property="billDate"/>
      <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
      <result column="ledger_id" jdbcType="VARCHAR" property="ledgerId"/>
      <result column="ticket_code" jdbcType="INTEGER" property="ticketCode" />
      <result column="scene_type" jdbcType="INTEGER" property="sceneType" />
      <result column="actual_pay" jdbcType="INTEGER" property="actualPay" />
      <result column="ledger_time" jdbcType="BIGINT" property="ledgerTime" />
      <result column="subsidy_amount" jdbcType="INTEGER" property="subsidyAmount" />
      <result column="settled_amount" jdbcType="BIGINT" property="settledAmount" />
      <result column="refund_time" jdbcType="BIGINT" property="refundTime" />
      <result column="deleted" jdbcType="BIGINT" property="deleted" />
      <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, bill_date, order_id, ledger_id, ticket_code, scene_type, actual_pay, ledger_time, subsidy_amount, settled_amount, refund_time
  </sql>

  <insert id="batchInsertBill" parameterType="com.sankuai.carnation.distribution.kuaishoubill.repository.entity.KuaiShouDistributionBillEntity">
    insert into kuaishou_distribution_bill
    (bill_date, order_id, ledger_id, ticket_code, scene_type, actual_pay,
    ledger_time, subsidy_amount, settled_amount, refund_time)
    values
    <foreach collection="entities" item="entity" separator=",">
      (#{entity.billDate}, #{entity.orderId}, #{entity.ledgerId}, #{entity.ticketCode}, #{entity.sceneType}, #{entity.actualPay},
      #{entity.ledgerTime}, #{entity.subsidyAmount}, #{entity.settledAmount}, #{entity.refundTime})
    </foreach>
  </insert>

  <insert id="batchUpdateBill" parameterType="com.sankuai.carnation.distribution.kuaishoubill.repository.entity.KuaiShouDistributionBillEntity">
    <foreach collection="entities" item="entity" separator=";">
        update kuaishou_distribution_bill
        <set>
            <if test="entity.sceneType != null">
              scene_type = #{entity.sceneType},
            </if>
            <if test="entity.actualPay != null">
              actual_pay = #{entity.actualPay},
            </if>
            <if test="entity.ledgerTime != null">
              ledger_time = #{entity.ledgerTime},
            </if>
            <if test="entity.subsidyAmount != null">
              subsidy_amount = #{entity.subsidyAmount},
            </if>
            <if test="entity.settledAmount != null">
              settled_amount = #{entity.settledAmount},
            </if>
            <if test="entity.refundTime != null">
              refund_time = #{entity.refundTime},
            </if>
        </set>
         where ticket_code = #{entity.ticketCode}
         and deleted = 0
    </foreach>
  </insert>

  <select id="queryByKuaiShouBillQueryRequest" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from kuaishou_distribution_bill
    where
    ticket_code in
      <foreach collection="request.ticketCodeList" item="ticketCode" open="(" close=")" separator=",">
         #{ticketCode}
      </foreach>
    and ledger_id in
      <foreach collection="request.ledgerIds" item="ledgerId" open="(" close=")" separator=",">
          #{ledgerId}
      </foreach>
    and deleted = 0
  </select>
</mapper>