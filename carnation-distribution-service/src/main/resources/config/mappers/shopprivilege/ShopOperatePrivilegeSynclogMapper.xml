<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="dp_deal_id" jdbcType="VARCHAR" property="dpDealId" />
    <result column="sync_type" jdbcType="INTEGER" property="syncType" />
    <result column="sync_status" jdbcType="INTEGER" property="syncStatus" />
    <result column="right_id" jdbcType="BIGINT" property="rightId" />
    <result column="item_id" jdbcType="INTEGER" property="itemId" />
    <result column="item_data" jdbcType="VARCHAR" property="itemData" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dt, mt_shop_id, dp_shop_id, dp_deal_id, sync_type, sync_status, right_id, item_id, 
    item_data, add_time, update_time, fail_reason
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_operate_privilege_synclog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shop_operate_privilege_synclog
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shop_operate_privilege_synclog
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclogExample">
    delete from shop_operate_privilege_synclog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclog" useGeneratedKeys="true">
    insert into shop_operate_privilege_synclog (dt, mt_shop_id, dp_shop_id, 
      dp_deal_id, sync_type, sync_status, 
      right_id, item_id, item_data, 
      add_time, update_time, fail_reason
      )
    values (#{dt,jdbcType=VARCHAR}, #{mtShopId,jdbcType=BIGINT}, #{dpShopId,jdbcType=BIGINT}, 
      #{dpDealId,jdbcType=VARCHAR}, #{syncType,jdbcType=INTEGER}, #{syncStatus,jdbcType=INTEGER}, 
      #{rightId,jdbcType=BIGINT}, #{itemId,jdbcType=INTEGER}, #{itemData,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{failReason,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclog" useGeneratedKeys="true">
    insert into shop_operate_privilege_synclog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        dt,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="dpDealId != null">
        dp_deal_id,
      </if>
      <if test="syncType != null">
        sync_type,
      </if>
      <if test="syncStatus != null">
        sync_status,
      </if>
      <if test="rightId != null">
        right_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="itemData != null">
        item_data,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="dpDealId != null">
        #{dpDealId,jdbcType=VARCHAR},
      </if>
      <if test="syncType != null">
        #{syncType,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="rightId != null">
        #{rightId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=INTEGER},
      </if>
      <if test="itemData != null">
        #{itemData,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclogExample" resultType="java.lang.Long">
    select count(*) from shop_operate_privilege_synclog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update shop_operate_privilege_synclog
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.mtShopId != null">
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.dpDealId != null">
        dp_deal_id = #{record.dpDealId,jdbcType=VARCHAR},
      </if>
      <if test="record.syncType != null">
        sync_type = #{record.syncType,jdbcType=INTEGER},
      </if>
      <if test="record.syncStatus != null">
        sync_status = #{record.syncStatus,jdbcType=INTEGER},
      </if>
      <if test="record.rightId != null">
        right_id = #{record.rightId,jdbcType=BIGINT},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=INTEGER},
      </if>
      <if test="record.itemData != null">
        item_data = #{record.itemData,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.failReason != null">
        fail_reason = #{record.failReason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update shop_operate_privilege_synclog
    set id = #{record.id,jdbcType=BIGINT},
      dt = #{record.dt,jdbcType=VARCHAR},
      mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      dp_deal_id = #{record.dpDealId,jdbcType=VARCHAR},
      sync_type = #{record.syncType,jdbcType=INTEGER},
      sync_status = #{record.syncStatus,jdbcType=INTEGER},
      right_id = #{record.rightId,jdbcType=BIGINT},
      item_id = #{record.itemId,jdbcType=INTEGER},
      item_data = #{record.itemData,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      fail_reason = #{record.failReason,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclog">
    update shop_operate_privilege_synclog
    <set>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="dpDealId != null">
        dp_deal_id = #{dpDealId,jdbcType=VARCHAR},
      </if>
      <if test="syncType != null">
        sync_type = #{syncType,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        sync_status = #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="rightId != null">
        right_id = #{rightId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=INTEGER},
      </if>
      <if test="itemData != null">
        item_data = #{itemData,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeSynclog">
    update shop_operate_privilege_synclog
    set dt = #{dt,jdbcType=VARCHAR},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      dp_deal_id = #{dpDealId,jdbcType=VARCHAR},
      sync_type = #{syncType,jdbcType=INTEGER},
      sync_status = #{syncStatus,jdbcType=INTEGER},
      right_id = #{rightId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=INTEGER},
      item_data = #{itemData,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      fail_reason = #{failReason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into shop_operate_privilege_synclog
    (dt, mt_shop_id, dp_shop_id, dp_deal_id, sync_type, sync_status, right_id, item_id, 
      item_data, add_time, update_time, fail_reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dt,jdbcType=VARCHAR}, #{item.mtShopId,jdbcType=BIGINT}, #{item.dpShopId,jdbcType=BIGINT}, 
        #{item.dpDealId,jdbcType=VARCHAR}, #{item.syncType,jdbcType=INTEGER}, #{item.syncStatus,jdbcType=INTEGER}, 
        #{item.rightId,jdbcType=BIGINT}, #{item.itemId,jdbcType=INTEGER}, #{item.itemData,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.failReason,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>