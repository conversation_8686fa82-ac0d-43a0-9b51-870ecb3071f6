<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysqlMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysql">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dt" jdbcType="VARCHAR" property="dt" />
    <result column="right_id" jdbcType="BIGINT" property="rightId" />
    <result column="right_name" jdbcType="VARCHAR" property="rightName" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="right_desc" jdbcType="VARCHAR" property="rightDesc" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dt, right_id, right_name, biz_id, type, begin_time, end_time, right_desc, mt_shop_id, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysqlExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_operate_privilege_hive2mysql
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from shop_operate_privilege_hive2mysql
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from shop_operate_privilege_hive2mysql
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysqlExample">
    delete from shop_operate_privilege_hive2mysql
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysql" useGeneratedKeys="true">
    insert into shop_operate_privilege_hive2mysql (dt, right_id, right_name, 
      biz_id, type, begin_time, 
      end_time, right_desc, mt_shop_id, 
      add_time, update_time)
    values (#{dt,jdbcType=VARCHAR}, #{rightId,jdbcType=BIGINT}, #{rightName,jdbcType=VARCHAR}, 
      #{bizId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{beginTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{rightDesc,jdbcType=VARCHAR}, #{mtShopId,jdbcType=BIGINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysql" useGeneratedKeys="true">
    insert into shop_operate_privilege_hive2mysql
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        dt,
      </if>
      <if test="rightId != null">
        right_id,
      </if>
      <if test="rightName != null">
        right_name,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="rightDesc != null">
        right_desc,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dt != null">
        #{dt,jdbcType=VARCHAR},
      </if>
      <if test="rightId != null">
        #{rightId,jdbcType=BIGINT},
      </if>
      <if test="rightName != null">
        #{rightName,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rightDesc != null">
        #{rightDesc,jdbcType=VARCHAR},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysqlExample" resultType="java.lang.Long">
    select count(*) from shop_operate_privilege_hive2mysql
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update shop_operate_privilege_hive2mysql
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dt != null">
        dt = #{record.dt,jdbcType=VARCHAR},
      </if>
      <if test="record.rightId != null">
        right_id = #{record.rightId,jdbcType=BIGINT},
      </if>
      <if test="record.rightName != null">
        right_name = #{record.rightName,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rightDesc != null">
        right_desc = #{record.rightDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.mtShopId != null">
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update shop_operate_privilege_hive2mysql
    set id = #{record.id,jdbcType=BIGINT},
      dt = #{record.dt,jdbcType=VARCHAR},
      right_id = #{record.rightId,jdbcType=BIGINT},
      right_name = #{record.rightName,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=VARCHAR},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      right_desc = #{record.rightDesc,jdbcType=VARCHAR},
      mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysql">
    update shop_operate_privilege_hive2mysql
    <set>
      <if test="dt != null">
        dt = #{dt,jdbcType=VARCHAR},
      </if>
      <if test="rightId != null">
        right_id = #{rightId,jdbcType=BIGINT},
      </if>
      <if test="rightName != null">
        right_name = #{rightName,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rightDesc != null">
        right_desc = #{rightDesc,jdbcType=VARCHAR},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.shopprivilege.dao.ShopOperatePrivilegeHive2mysql">
    update shop_operate_privilege_hive2mysql
    set dt = #{dt,jdbcType=VARCHAR},
      right_id = #{rightId,jdbcType=BIGINT},
      right_name = #{rightName,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=BIGINT},
      type = #{type,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      right_desc = #{rightDesc,jdbcType=VARCHAR},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into shop_operate_privilege_hive2mysql
    (dt, right_id, right_name, biz_id, type, begin_time, end_time, right_desc, mt_shop_id, 
      add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dt,jdbcType=VARCHAR}, #{item.rightId,jdbcType=BIGINT}, #{item.rightName,jdbcType=VARCHAR}, 
        #{item.bizId,jdbcType=BIGINT}, #{item.type,jdbcType=VARCHAR}, #{item.beginTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.rightDesc,jdbcType=VARCHAR}, #{item.mtShopId,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>