<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.common.repository.dao.CommonQrCodeMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.common.repository.model.CommonQrCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="code_url" jdbcType="VARCHAR" property="codeUrl" />
    <result column="code_image" jdbcType="VARCHAR" property="codeImage" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="dynamic_flag" jdbcType="INTEGER" property="dynamicFlag" />
    <result column="biz_id_str" jdbcType="VARCHAR" property="bizIdStr" />
    <result column="secret_key" jdbcType="VARCHAR" property="secretKey" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_id, biz_type, code_url, code_image, add_time, update_time, dynamic_flag, 
    biz_id_str, secret_key, ext_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.common.repository.example.CommonQrCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from common_qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from common_qr_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from common_qr_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.common.repository.example.CommonQrCodeExample">
    delete from common_qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.common.repository.model.CommonQrCode" useGeneratedKeys="true">
    insert into common_qr_code (biz_id, biz_type, code_url, 
      code_image, add_time, update_time, 
      dynamic_flag, biz_id_str, secret_key, 
      ext_info)
    values (#{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=INTEGER}, #{codeUrl,jdbcType=VARCHAR}, 
      #{codeImage,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{dynamicFlag,jdbcType=INTEGER}, #{bizIdStr,jdbcType=VARCHAR}, #{secretKey,jdbcType=VARCHAR}, 
      #{extInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.common.repository.model.CommonQrCode" useGeneratedKeys="true">
    insert into common_qr_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="codeUrl != null">
        code_url,
      </if>
      <if test="codeImage != null">
        code_image,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dynamicFlag != null">
        dynamic_flag,
      </if>
      <if test="bizIdStr != null">
        biz_id_str,
      </if>
      <if test="secretKey != null">
        secret_key,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="codeUrl != null">
        #{codeUrl,jdbcType=VARCHAR},
      </if>
      <if test="codeImage != null">
        #{codeImage,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dynamicFlag != null">
        #{dynamicFlag,jdbcType=INTEGER},
      </if>
      <if test="bizIdStr != null">
        #{bizIdStr,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.common.repository.example.CommonQrCodeExample" resultType="java.lang.Long">
    select count(*) from common_qr_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update common_qr_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=BIGINT},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.codeUrl != null">
        code_url = #{record.codeUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.codeImage != null">
        code_image = #{record.codeImage,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dynamicFlag != null">
        dynamic_flag = #{record.dynamicFlag,jdbcType=INTEGER},
      </if>
      <if test="record.bizIdStr != null">
        biz_id_str = #{record.bizIdStr,jdbcType=VARCHAR},
      </if>
      <if test="record.secretKey != null">
        secret_key = #{record.secretKey,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update common_qr_code
    set id = #{record.id,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      code_url = #{record.codeUrl,jdbcType=VARCHAR},
      code_image = #{record.codeImage,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      dynamic_flag = #{record.dynamicFlag,jdbcType=INTEGER},
      biz_id_str = #{record.bizIdStr,jdbcType=VARCHAR},
      secret_key = #{record.secretKey,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.common.repository.model.CommonQrCode">
    update common_qr_code
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="codeUrl != null">
        code_url = #{codeUrl,jdbcType=VARCHAR},
      </if>
      <if test="codeImage != null">
        code_image = #{codeImage,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dynamicFlag != null">
        dynamic_flag = #{dynamicFlag,jdbcType=INTEGER},
      </if>
      <if test="bizIdStr != null">
        biz_id_str = #{bizIdStr,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        secret_key = #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.common.repository.model.CommonQrCode">
    update common_qr_code
    set biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=INTEGER},
      code_url = #{codeUrl,jdbcType=VARCHAR},
      code_image = #{codeImage,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      dynamic_flag = #{dynamicFlag,jdbcType=INTEGER},
      biz_id_str = #{bizIdStr,jdbcType=VARCHAR},
      secret_key = #{secretKey,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into common_qr_code
    (biz_id, biz_type, code_url, code_image, add_time, update_time, dynamic_flag, biz_id_str, 
      secret_key, ext_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizId,jdbcType=BIGINT}, #{item.bizType,jdbcType=INTEGER}, #{item.codeUrl,jdbcType=VARCHAR}, 
        #{item.codeImage,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.dynamicFlag,jdbcType=INTEGER}, #{item.bizIdStr,jdbcType=VARCHAR}, #{item.secretKey,jdbcType=VARCHAR}, 
        #{item.extInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>