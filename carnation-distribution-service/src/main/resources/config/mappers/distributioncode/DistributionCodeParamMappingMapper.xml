<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.distributioncode.repository.dao.DistributionCodeParamMappingMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.distributioncode.repository.entity.DistributionCodeParamMappingEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="distribution_code" jdbcType="VARCHAR" property="distributionCode" />
    <result column="biz_param1" jdbcType="VARCHAR" property="bizParam1" />
    <result column="biz_param2" jdbcType="VARCHAR" property="bizParam2" />
    <result column="biz_param3" jdbcType="VARCHAR" property="bizParam3" />
    <result column="biz_param4" jdbcType="VARCHAR" property="bizParam4" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, channel, distribution_code, biz_param1, biz_param2, biz_param3, biz_param4, status
  </sql>

  <insert id="insertDistributionCodeParamMapping" parameterType="com.sankuai.carnation.distribution.distributioncode.repository.entity.DistributionCodeParamMappingEntity">
    insert into distribution_code_param_mapping
    (channel, distribution_code, biz_param1, biz_param2, biz_param3, biz_param4)
    values
    (#{channel}, #{distributionCode}, #{bizParam1}, #{bizParam2}, #{bizParam3}, #{bizParam4})
  </insert>

  <update id="updateDistributionCodeParamMapping" parameterType="com.sankuai.carnation.distribution.distributioncode.repository.entity.DistributionCodeParamMappingEntity">
    update distribution_code_param_mapping
    <set>
      status = #{status}
    </set>
    where id = #{id}
  </update>

  <select id="loadByChannelAndDistributionCode" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from distribution_code_param_mapping
    where channel = #{channel}
    and distribution_code = #{distributionCode}
    and status = 1
  </select>

  <select id="queryByChannelAndDistributionCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from distribution_code_param_mapping
    where channel = #{channel}
    and distribution_code in
    <foreach collection="distributionCodes" item="code" open="(" close=")" separator=",">
      #{code}
    </foreach>
    and status = 1
  </select>

  <select id="queryDistributionCodeByChannelAndBizParams" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from distribution_code_param_mapping
    where channel = #{channel}
    and status = 1
    and biz_param1 = #{bizParam1}
    <if test="bizParam2 != null and bizParam2 != ''">
      and biz_param2 = #{bizParam2}
    </if>
    <if test="bizParam3 != null and bizParam3 != ''">
      and biz_param3 = #{bizParam3}
    </if>
    <if test="bizParam4 != null and bizParam4 != ''">
        and biz_param4 = #{bizParam4}
    </if>
    order by id desc
  </select>
</mapper>