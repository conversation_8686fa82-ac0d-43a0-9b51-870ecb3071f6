<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.commisson.repository.dao.CommOrderVerifyRebateMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyRebate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unified_order_id" jdbcType="VARCHAR" property="unifiedOrderId" />
    <result column="long_order_id" jdbcType="BIGINT" property="longOrderId" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="rebate_source" jdbcType="VARCHAR" property="rebateSource" />
    <result column="rebate_biz_id" jdbcType="VARCHAR" property="rebateBizId" />
    <result column="rebate_type" jdbcType="INTEGER" property="rebateType" />
    <result column="rebate_cent" jdbcType="BIGINT" property="rebateCent" />
    <result column="rebate_rate" jdbcType="DECIMAL" property="rebateRate" />
    <result column="rebate_time" jdbcType="TIMESTAMP" property="rebateTime" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unified_order_id, long_order_id, coupon_id, rebate_source, rebate_biz_id, rebate_type, 
    rebate_cent, rebate_rate, rebate_time, priority, ext_info, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommOrderVerifyRebateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from comm_order_verify_rebate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_order_verify_rebate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_order_verify_rebate
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommOrderVerifyRebateExample">
    delete from comm_order_verify_rebate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyRebate" useGeneratedKeys="true">
    insert into comm_order_verify_rebate (unified_order_id, long_order_id, coupon_id, 
      rebate_source, rebate_biz_id, rebate_type, 
      rebate_cent, rebate_rate, rebate_time, 
      priority, ext_info, status, 
      add_time, update_time)
    values (#{unifiedOrderId,jdbcType=VARCHAR}, #{longOrderId,jdbcType=BIGINT}, #{couponId,jdbcType=VARCHAR}, 
      #{rebateSource,jdbcType=VARCHAR}, #{rebateBizId,jdbcType=VARCHAR}, #{rebateType,jdbcType=INTEGER}, 
      #{rebateCent,jdbcType=BIGINT}, #{rebateRate,jdbcType=DECIMAL}, #{rebateTime,jdbcType=TIMESTAMP}, 
      #{priority,jdbcType=INTEGER}, #{extInfo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyRebate" useGeneratedKeys="true">
    insert into comm_order_verify_rebate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unifiedOrderId != null">
        unified_order_id,
      </if>
      <if test="longOrderId != null">
        long_order_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="rebateSource != null">
        rebate_source,
      </if>
      <if test="rebateBizId != null">
        rebate_biz_id,
      </if>
      <if test="rebateType != null">
        rebate_type,
      </if>
      <if test="rebateCent != null">
        rebate_cent,
      </if>
      <if test="rebateRate != null">
        rebate_rate,
      </if>
      <if test="rebateTime != null">
        rebate_time,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unifiedOrderId != null">
        #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="rebateSource != null">
        #{rebateSource,jdbcType=VARCHAR},
      </if>
      <if test="rebateBizId != null">
        #{rebateBizId,jdbcType=VARCHAR},
      </if>
      <if test="rebateType != null">
        #{rebateType,jdbcType=INTEGER},
      </if>
      <if test="rebateCent != null">
        #{rebateCent,jdbcType=BIGINT},
      </if>
      <if test="rebateRate != null">
        #{rebateRate,jdbcType=DECIMAL},
      </if>
      <if test="rebateTime != null">
        #{rebateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommOrderVerifyRebateExample" resultType="java.lang.Long">
    select count(*) from comm_order_verify_rebate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update comm_order_verify_rebate
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unifiedOrderId != null">
        unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.longOrderId != null">
        long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateSource != null">
        rebate_source = #{record.rebateSource,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateBizId != null">
        rebate_biz_id = #{record.rebateBizId,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateType != null">
        rebate_type = #{record.rebateType,jdbcType=INTEGER},
      </if>
      <if test="record.rebateCent != null">
        rebate_cent = #{record.rebateCent,jdbcType=BIGINT},
      </if>
      <if test="record.rebateRate != null">
        rebate_rate = #{record.rebateRate,jdbcType=DECIMAL},
      </if>
      <if test="record.rebateTime != null">
        rebate_time = #{record.rebateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.priority != null">
        priority = #{record.priority,jdbcType=INTEGER},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update comm_order_verify_rebate
    set id = #{record.id,jdbcType=BIGINT},
      unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      rebate_source = #{record.rebateSource,jdbcType=VARCHAR},
      rebate_biz_id = #{record.rebateBizId,jdbcType=VARCHAR},
      rebate_type = #{record.rebateType,jdbcType=INTEGER},
      rebate_cent = #{record.rebateCent,jdbcType=BIGINT},
      rebate_rate = #{record.rebateRate,jdbcType=DECIMAL},
      rebate_time = #{record.rebateTime,jdbcType=TIMESTAMP},
      priority = #{record.priority,jdbcType=INTEGER},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyRebate">
    update comm_order_verify_rebate
    <set>
      <if test="unifiedOrderId != null">
        unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        long_order_id = #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="rebateSource != null">
        rebate_source = #{rebateSource,jdbcType=VARCHAR},
      </if>
      <if test="rebateBizId != null">
        rebate_biz_id = #{rebateBizId,jdbcType=VARCHAR},
      </if>
      <if test="rebateType != null">
        rebate_type = #{rebateType,jdbcType=INTEGER},
      </if>
      <if test="rebateCent != null">
        rebate_cent = #{rebateCent,jdbcType=BIGINT},
      </if>
      <if test="rebateRate != null">
        rebate_rate = #{rebateRate,jdbcType=DECIMAL},
      </if>
      <if test="rebateTime != null">
        rebate_time = #{rebateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyRebate">
    update comm_order_verify_rebate
    set unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      long_order_id = #{longOrderId,jdbcType=BIGINT},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      rebate_source = #{rebateSource,jdbcType=VARCHAR},
      rebate_biz_id = #{rebateBizId,jdbcType=VARCHAR},
      rebate_type = #{rebateType,jdbcType=INTEGER},
      rebate_cent = #{rebateCent,jdbcType=BIGINT},
      rebate_rate = #{rebateRate,jdbcType=DECIMAL},
      rebate_time = #{rebateTime,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into comm_order_verify_rebate
    (unified_order_id, long_order_id, coupon_id, rebate_source, rebate_biz_id, rebate_type, 
      rebate_cent, rebate_rate, rebate_time, priority, ext_info, status, add_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.unifiedOrderId,jdbcType=VARCHAR}, #{item.longOrderId,jdbcType=BIGINT}, #{item.couponId,jdbcType=VARCHAR}, 
        #{item.rebateSource,jdbcType=VARCHAR}, #{item.rebateBizId,jdbcType=VARCHAR}, #{item.rebateType,jdbcType=INTEGER}, 
        #{item.rebateCent,jdbcType=BIGINT}, #{item.rebateRate,jdbcType=DECIMAL}, #{item.rebateTime,jdbcType=TIMESTAMP}, 
        #{item.priority,jdbcType=INTEGER}, #{item.extInfo,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>