<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.commisson.repository.dao.CommRebateAclAnnualMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.commisson.repository.db.CommRebateAclAnnual">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="out_biz_id" jdbcType="VARCHAR" property="outBizId" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="bu_id" jdbcType="INTEGER" property="buId" />
    <result column="award_type" jdbcType="INTEGER" property="awardType" />
    <result column="promo_commission_rate" jdbcType="DECIMAL" property="promoCommissionRate" />
    <result column="promo_effect_start_time" jdbcType="TIMESTAMP" property="promoEffectStartTime" />
    <result column="promo_order_place_end_time" jdbcType="TIMESTAMP" property="promoOrderPlaceEndTime" />
    <result column="promo_order_verify_end_time" jdbcType="TIMESTAMP" property="promoOrderVerifyEndTime" />
    <result column="full_return_ration" jdbcType="DECIMAL" property="fullReturnRation" />
    <result column="part_return_promo_value" jdbcType="DECIMAL" property="partReturnPromoValue" />
    <result column="part_return_ration" jdbcType="DECIMAL" property="partReturnRation" />
    <result column="rebate_verify_begin_time" jdbcType="TIMESTAMP" property="rebateVerifyBeginTime" />
    <result column="rebate_verify_end_time" jdbcType="TIMESTAMP" property="rebateVerifyEndTime" />
    <result column="finalstatus" jdbcType="VARCHAR" property="finalstatus" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mt_shop_id, out_biz_id, activity_id, group_name, bu_id, award_type, promo_commission_rate, 
    promo_effect_start_time, promo_order_place_end_time, promo_order_verify_end_time, 
    full_return_ration, part_return_promo_value, part_return_ration, rebate_verify_begin_time, 
    rebate_verify_end_time, finalstatus, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommRebateAclAnnualExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from comm_rebate_acl_annual
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_rebate_acl_annual
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_rebate_acl_annual
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommRebateAclAnnualExample">
    delete from comm_rebate_acl_annual
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommRebateAclAnnual" useGeneratedKeys="true">
    insert into comm_rebate_acl_annual (mt_shop_id, out_biz_id, activity_id, 
      group_name, bu_id, award_type, 
      promo_commission_rate, promo_effect_start_time, 
      promo_order_place_end_time, promo_order_verify_end_time, 
      full_return_ration, part_return_promo_value, 
      part_return_ration, rebate_verify_begin_time, 
      rebate_verify_end_time, finalstatus, add_time, 
      update_time)
    values (#{mtShopId,jdbcType=BIGINT}, #{outBizId,jdbcType=VARCHAR}, #{activityId,jdbcType=BIGINT}, 
      #{groupName,jdbcType=VARCHAR}, #{buId,jdbcType=INTEGER}, #{awardType,jdbcType=INTEGER}, 
      #{promoCommissionRate,jdbcType=DECIMAL}, #{promoEffectStartTime,jdbcType=TIMESTAMP}, 
      #{promoOrderPlaceEndTime,jdbcType=TIMESTAMP}, #{promoOrderVerifyEndTime,jdbcType=TIMESTAMP}, 
      #{fullReturnRation,jdbcType=DECIMAL}, #{partReturnPromoValue,jdbcType=DECIMAL}, 
      #{partReturnRation,jdbcType=DECIMAL}, #{rebateVerifyBeginTime,jdbcType=TIMESTAMP}, 
      #{rebateVerifyEndTime,jdbcType=TIMESTAMP}, #{finalstatus,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommRebateAclAnnual" useGeneratedKeys="true">
    insert into comm_rebate_acl_annual
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="outBizId != null">
        out_biz_id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="buId != null">
        bu_id,
      </if>
      <if test="awardType != null">
        award_type,
      </if>
      <if test="promoCommissionRate != null">
        promo_commission_rate,
      </if>
      <if test="promoEffectStartTime != null">
        promo_effect_start_time,
      </if>
      <if test="promoOrderPlaceEndTime != null">
        promo_order_place_end_time,
      </if>
      <if test="promoOrderVerifyEndTime != null">
        promo_order_verify_end_time,
      </if>
      <if test="fullReturnRation != null">
        full_return_ration,
      </if>
      <if test="partReturnPromoValue != null">
        part_return_promo_value,
      </if>
      <if test="partReturnRation != null">
        part_return_ration,
      </if>
      <if test="rebateVerifyBeginTime != null">
        rebate_verify_begin_time,
      </if>
      <if test="rebateVerifyEndTime != null">
        rebate_verify_end_time,
      </if>
      <if test="finalstatus != null">
        finalstatus,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="outBizId != null">
        #{outBizId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="buId != null">
        #{buId,jdbcType=INTEGER},
      </if>
      <if test="awardType != null">
        #{awardType,jdbcType=INTEGER},
      </if>
      <if test="promoCommissionRate != null">
        #{promoCommissionRate,jdbcType=DECIMAL},
      </if>
      <if test="promoEffectStartTime != null">
        #{promoEffectStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promoOrderPlaceEndTime != null">
        #{promoOrderPlaceEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promoOrderVerifyEndTime != null">
        #{promoOrderVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fullReturnRation != null">
        #{fullReturnRation,jdbcType=DECIMAL},
      </if>
      <if test="partReturnPromoValue != null">
        #{partReturnPromoValue,jdbcType=DECIMAL},
      </if>
      <if test="partReturnRation != null">
        #{partReturnRation,jdbcType=DECIMAL},
      </if>
      <if test="rebateVerifyBeginTime != null">
        #{rebateVerifyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rebateVerifyEndTime != null">
        #{rebateVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalstatus != null">
        #{finalstatus,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommRebateAclAnnualExample" resultType="java.lang.Long">
    select count(*) from comm_rebate_acl_annual
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update comm_rebate_acl_annual
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mtShopId != null">
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="record.outBizId != null">
        out_biz_id = #{record.outBizId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.buId != null">
        bu_id = #{record.buId,jdbcType=INTEGER},
      </if>
      <if test="record.awardType != null">
        award_type = #{record.awardType,jdbcType=INTEGER},
      </if>
      <if test="record.promoCommissionRate != null">
        promo_commission_rate = #{record.promoCommissionRate,jdbcType=DECIMAL},
      </if>
      <if test="record.promoEffectStartTime != null">
        promo_effect_start_time = #{record.promoEffectStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.promoOrderPlaceEndTime != null">
        promo_order_place_end_time = #{record.promoOrderPlaceEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.promoOrderVerifyEndTime != null">
        promo_order_verify_end_time = #{record.promoOrderVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fullReturnRation != null">
        full_return_ration = #{record.fullReturnRation,jdbcType=DECIMAL},
      </if>
      <if test="record.partReturnPromoValue != null">
        part_return_promo_value = #{record.partReturnPromoValue,jdbcType=DECIMAL},
      </if>
      <if test="record.partReturnRation != null">
        part_return_ration = #{record.partReturnRation,jdbcType=DECIMAL},
      </if>
      <if test="record.rebateVerifyBeginTime != null">
        rebate_verify_begin_time = #{record.rebateVerifyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.rebateVerifyEndTime != null">
        rebate_verify_end_time = #{record.rebateVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.finalstatus != null">
        finalstatus = #{record.finalstatus,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update comm_rebate_acl_annual
    set id = #{record.id,jdbcType=BIGINT},
      mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      out_biz_id = #{record.outBizId,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      bu_id = #{record.buId,jdbcType=INTEGER},
      award_type = #{record.awardType,jdbcType=INTEGER},
      promo_commission_rate = #{record.promoCommissionRate,jdbcType=DECIMAL},
      promo_effect_start_time = #{record.promoEffectStartTime,jdbcType=TIMESTAMP},
      promo_order_place_end_time = #{record.promoOrderPlaceEndTime,jdbcType=TIMESTAMP},
      promo_order_verify_end_time = #{record.promoOrderVerifyEndTime,jdbcType=TIMESTAMP},
      full_return_ration = #{record.fullReturnRation,jdbcType=DECIMAL},
      part_return_promo_value = #{record.partReturnPromoValue,jdbcType=DECIMAL},
      part_return_ration = #{record.partReturnRation,jdbcType=DECIMAL},
      rebate_verify_begin_time = #{record.rebateVerifyBeginTime,jdbcType=TIMESTAMP},
      rebate_verify_end_time = #{record.rebateVerifyEndTime,jdbcType=TIMESTAMP},
      finalstatus = #{record.finalstatus,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommRebateAclAnnual">
    update comm_rebate_acl_annual
    <set>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="outBizId != null">
        out_biz_id = #{outBizId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="buId != null">
        bu_id = #{buId,jdbcType=INTEGER},
      </if>
      <if test="awardType != null">
        award_type = #{awardType,jdbcType=INTEGER},
      </if>
      <if test="promoCommissionRate != null">
        promo_commission_rate = #{promoCommissionRate,jdbcType=DECIMAL},
      </if>
      <if test="promoEffectStartTime != null">
        promo_effect_start_time = #{promoEffectStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promoOrderPlaceEndTime != null">
        promo_order_place_end_time = #{promoOrderPlaceEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promoOrderVerifyEndTime != null">
        promo_order_verify_end_time = #{promoOrderVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fullReturnRation != null">
        full_return_ration = #{fullReturnRation,jdbcType=DECIMAL},
      </if>
      <if test="partReturnPromoValue != null">
        part_return_promo_value = #{partReturnPromoValue,jdbcType=DECIMAL},
      </if>
      <if test="partReturnRation != null">
        part_return_ration = #{partReturnRation,jdbcType=DECIMAL},
      </if>
      <if test="rebateVerifyBeginTime != null">
        rebate_verify_begin_time = #{rebateVerifyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rebateVerifyEndTime != null">
        rebate_verify_end_time = #{rebateVerifyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalstatus != null">
        finalstatus = #{finalstatus,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommRebateAclAnnual">
    update comm_rebate_acl_annual
    set mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      out_biz_id = #{outBizId,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=BIGINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      bu_id = #{buId,jdbcType=INTEGER},
      award_type = #{awardType,jdbcType=INTEGER},
      promo_commission_rate = #{promoCommissionRate,jdbcType=DECIMAL},
      promo_effect_start_time = #{promoEffectStartTime,jdbcType=TIMESTAMP},
      promo_order_place_end_time = #{promoOrderPlaceEndTime,jdbcType=TIMESTAMP},
      promo_order_verify_end_time = #{promoOrderVerifyEndTime,jdbcType=TIMESTAMP},
      full_return_ration = #{fullReturnRation,jdbcType=DECIMAL},
      part_return_promo_value = #{partReturnPromoValue,jdbcType=DECIMAL},
      part_return_ration = #{partReturnRation,jdbcType=DECIMAL},
      rebate_verify_begin_time = #{rebateVerifyBeginTime,jdbcType=TIMESTAMP},
      rebate_verify_end_time = #{rebateVerifyEndTime,jdbcType=TIMESTAMP},
      finalstatus = #{finalstatus,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into comm_rebate_acl_annual
    (mt_shop_id, out_biz_id, activity_id, group_name, bu_id, award_type, promo_commission_rate, 
      promo_effect_start_time, promo_order_place_end_time, promo_order_verify_end_time, 
      full_return_ration, part_return_promo_value, part_return_ration, rebate_verify_begin_time, 
      rebate_verify_end_time, finalstatus, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mtShopId,jdbcType=BIGINT}, #{item.outBizId,jdbcType=VARCHAR}, #{item.activityId,jdbcType=BIGINT}, 
        #{item.groupName,jdbcType=VARCHAR}, #{item.buId,jdbcType=INTEGER}, #{item.awardType,jdbcType=INTEGER}, 
        #{item.promoCommissionRate,jdbcType=DECIMAL}, #{item.promoEffectStartTime,jdbcType=TIMESTAMP}, 
        #{item.promoOrderPlaceEndTime,jdbcType=TIMESTAMP}, #{item.promoOrderVerifyEndTime,jdbcType=TIMESTAMP}, 
        #{item.fullReturnRation,jdbcType=DECIMAL}, #{item.partReturnPromoValue,jdbcType=DECIMAL}, 
        #{item.partReturnRation,jdbcType=DECIMAL}, #{item.rebateVerifyBeginTime,jdbcType=TIMESTAMP}, 
        #{item.rebateVerifyEndTime,jdbcType=TIMESTAMP}, #{item.finalstatus,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>