<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.commisson.repository.dao.CommOrderVerifyCommissionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyCommission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unified_order_id" jdbcType="VARCHAR" property="unifiedOrderId" />
    <result column="long_order_id" jdbcType="BIGINT" property="longOrderId" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="verify_time" jdbcType="TIMESTAMP" property="verifyTime" />
    <result column="verify_count" jdbcType="INTEGER" property="verifyCount" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="commission_base_amount" jdbcType="DECIMAL" property="commissionBaseAmount" />
    <result column="service_fee" jdbcType="INTEGER" property="serviceFee" />
    <result column="fee_upper_limit_flag" jdbcType="VARCHAR" property="feeUpperLimitFlag" />
    <result column="commission_rate" jdbcType="DECIMAL" property="commissionRate" />
    <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unified_order_id, long_order_id, coupon_id, verify_time, verify_count, dp_shop_id, 
    mt_shop_id, product_type, product_id, sku_id, commission_base_amount, service_fee, 
    fee_upper_limit_flag, commission_rate, commission_amount, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommOrderVerifyCommissionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from comm_order_verify_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from comm_order_verify_commission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from comm_order_verify_commission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommOrderVerifyCommissionExample">
    delete from comm_order_verify_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyCommission" useGeneratedKeys="true">
    insert into comm_order_verify_commission (unified_order_id, long_order_id, coupon_id, 
      verify_time, verify_count, dp_shop_id, 
      mt_shop_id, product_type, product_id, 
      sku_id, commission_base_amount, service_fee, 
      fee_upper_limit_flag, commission_rate, commission_amount, 
      add_time, update_time)
    values (#{unifiedOrderId,jdbcType=VARCHAR}, #{longOrderId,jdbcType=BIGINT}, #{couponId,jdbcType=VARCHAR}, 
      #{verifyTime,jdbcType=TIMESTAMP}, #{verifyCount,jdbcType=INTEGER}, #{dpShopId,jdbcType=BIGINT}, 
      #{mtShopId,jdbcType=BIGINT}, #{productType,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, 
      #{skuId,jdbcType=BIGINT}, #{commissionBaseAmount,jdbcType=DECIMAL}, #{serviceFee,jdbcType=INTEGER}, 
      #{feeUpperLimitFlag,jdbcType=VARCHAR}, #{commissionRate,jdbcType=DECIMAL}, #{commissionAmount,jdbcType=DECIMAL}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyCommission" useGeneratedKeys="true">
    insert into comm_order_verify_commission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unifiedOrderId != null">
        unified_order_id,
      </if>
      <if test="longOrderId != null">
        long_order_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="verifyTime != null">
        verify_time,
      </if>
      <if test="verifyCount != null">
        verify_count,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="commissionBaseAmount != null">
        commission_base_amount,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="feeUpperLimitFlag != null">
        fee_upper_limit_flag,
      </if>
      <if test="commissionRate != null">
        commission_rate,
      </if>
      <if test="commissionAmount != null">
        commission_amount,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unifiedOrderId != null">
        #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="verifyTime != null">
        #{verifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verifyCount != null">
        #{verifyCount,jdbcType=INTEGER},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="commissionBaseAmount != null">
        #{commissionBaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=INTEGER},
      </if>
      <if test="feeUpperLimitFlag != null">
        #{feeUpperLimitFlag,jdbcType=VARCHAR},
      </if>
      <if test="commissionRate != null">
        #{commissionRate,jdbcType=DECIMAL},
      </if>
      <if test="commissionAmount != null">
        #{commissionAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.CommOrderVerifyCommissionExample" resultType="java.lang.Long">
    select count(*) from comm_order_verify_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update comm_order_verify_commission
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unifiedOrderId != null">
        unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.longOrderId != null">
        long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.verifyTime != null">
        verify_time = #{record.verifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.verifyCount != null">
        verify_count = #{record.verifyCount,jdbcType=INTEGER},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.mtShopId != null">
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=BIGINT},
      </if>
      <if test="record.commissionBaseAmount != null">
        commission_base_amount = #{record.commissionBaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceFee != null">
        service_fee = #{record.serviceFee,jdbcType=INTEGER},
      </if>
      <if test="record.feeUpperLimitFlag != null">
        fee_upper_limit_flag = #{record.feeUpperLimitFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.commissionRate != null">
        commission_rate = #{record.commissionRate,jdbcType=DECIMAL},
      </if>
      <if test="record.commissionAmount != null">
        commission_amount = #{record.commissionAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update comm_order_verify_commission
    set id = #{record.id,jdbcType=BIGINT},
      unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      verify_time = #{record.verifyTime,jdbcType=TIMESTAMP},
      verify_count = #{record.verifyCount,jdbcType=INTEGER},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      sku_id = #{record.skuId,jdbcType=BIGINT},
      commission_base_amount = #{record.commissionBaseAmount,jdbcType=DECIMAL},
      service_fee = #{record.serviceFee,jdbcType=INTEGER},
      fee_upper_limit_flag = #{record.feeUpperLimitFlag,jdbcType=VARCHAR},
      commission_rate = #{record.commissionRate,jdbcType=DECIMAL},
      commission_amount = #{record.commissionAmount,jdbcType=DECIMAL},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyCommission">
    update comm_order_verify_commission
    <set>
      <if test="unifiedOrderId != null">
        unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        long_order_id = #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="verifyTime != null">
        verify_time = #{verifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verifyCount != null">
        verify_count = #{verifyCount,jdbcType=INTEGER},
      </if>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="commissionBaseAmount != null">
        commission_base_amount = #{commissionBaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=INTEGER},
      </if>
      <if test="feeUpperLimitFlag != null">
        fee_upper_limit_flag = #{feeUpperLimitFlag,jdbcType=VARCHAR},
      </if>
      <if test="commissionRate != null">
        commission_rate = #{commissionRate,jdbcType=DECIMAL},
      </if>
      <if test="commissionAmount != null">
        commission_amount = #{commissionAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.CommOrderVerifyCommission">
    update comm_order_verify_commission
    set unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      long_order_id = #{longOrderId,jdbcType=BIGINT},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      verify_time = #{verifyTime,jdbcType=TIMESTAMP},
      verify_count = #{verifyCount,jdbcType=INTEGER},
      dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      product_type = #{productType,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      commission_base_amount = #{commissionBaseAmount,jdbcType=DECIMAL},
      service_fee = #{serviceFee,jdbcType=INTEGER},
      fee_upper_limit_flag = #{feeUpperLimitFlag,jdbcType=VARCHAR},
      commission_rate = #{commissionRate,jdbcType=DECIMAL},
      commission_amount = #{commissionAmount,jdbcType=DECIMAL},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into comm_order_verify_commission
    (unified_order_id, long_order_id, coupon_id, verify_time, verify_count, dp_shop_id, 
      mt_shop_id, product_type, product_id, sku_id, commission_base_amount, service_fee, 
      fee_upper_limit_flag, commission_rate, commission_amount, add_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.unifiedOrderId,jdbcType=VARCHAR}, #{item.longOrderId,jdbcType=BIGINT}, #{item.couponId,jdbcType=VARCHAR}, 
        #{item.verifyTime,jdbcType=TIMESTAMP}, #{item.verifyCount,jdbcType=INTEGER}, #{item.dpShopId,jdbcType=BIGINT}, 
        #{item.mtShopId,jdbcType=BIGINT}, #{item.productType,jdbcType=INTEGER}, #{item.productId,jdbcType=BIGINT}, 
        #{item.skuId,jdbcType=BIGINT}, #{item.commissionBaseAmount,jdbcType=DECIMAL}, #{item.serviceFee,jdbcType=INTEGER}, 
        #{item.feeUpperLimitFlag,jdbcType=VARCHAR}, #{item.commissionRate,jdbcType=DECIMAL}, 
        #{item.commissionAmount,jdbcType=DECIMAL}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>