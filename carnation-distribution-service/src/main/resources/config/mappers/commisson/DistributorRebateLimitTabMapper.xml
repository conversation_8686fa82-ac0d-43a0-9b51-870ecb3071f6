<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.commisson.repository.dao.DistributorRebateLimitTabMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.commisson.repository.db.DistributorRebateLimitTab">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="unified_order_id" jdbcType="VARCHAR" property="unifiedOrderId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="tech_id" jdbcType="BIGINT" property="techId" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="rebate_amount" jdbcType="BIGINT" property="rebateAmount" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, config_id, unified_order_id, user_id, platform, tech_id, dp_shop_id, ext_info, 
    order_amount, rebate_amount, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.DistributorRebateLimitTabExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distributor_rebate_limit_tab
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distributor_rebate_limit_tab
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distributor_rebate_limit_tab
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.DistributorRebateLimitTabExample">
    delete from distributor_rebate_limit_tab
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.DistributorRebateLimitTab" useGeneratedKeys="true">
    insert into distributor_rebate_limit_tab (config_id, unified_order_id, user_id, 
      platform, tech_id, dp_shop_id, 
      ext_info, order_amount, rebate_amount, 
      add_time, update_time)
    values (#{configId,jdbcType=BIGINT}, #{unifiedOrderId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{platform,jdbcType=INTEGER}, #{techId,jdbcType=BIGINT}, #{dpShopId,jdbcType=BIGINT}, 
      #{extInfo,jdbcType=VARCHAR}, #{orderAmount,jdbcType=BIGINT}, #{rebateAmount,jdbcType=BIGINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.DistributorRebateLimitTab" useGeneratedKeys="true">
    insert into distributor_rebate_limit_tab
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        config_id,
      </if>
      <if test="unifiedOrderId != null">
        unified_order_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="techId != null">
        tech_id,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="rebateAmount != null">
        rebate_amount,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="unifiedOrderId != null">
        #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="techId != null">
        #{techId,jdbcType=BIGINT},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="rebateAmount != null">
        #{rebateAmount,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.commisson.repository.example.DistributorRebateLimitTabExample" resultType="java.lang.Long">
    select count(*) from distributor_rebate_limit_tab
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distributor_rebate_limit_tab
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.configId != null">
        config_id = #{record.configId,jdbcType=BIGINT},
      </if>
      <if test="record.unifiedOrderId != null">
        unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.techId != null">
        tech_id = #{record.techId,jdbcType=BIGINT},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=BIGINT},
      </if>
      <if test="record.rebateAmount != null">
        rebate_amount = #{record.rebateAmount,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distributor_rebate_limit_tab
    set id = #{record.id,jdbcType=BIGINT},
      config_id = #{record.configId,jdbcType=BIGINT},
      unified_order_id = #{record.unifiedOrderId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      platform = #{record.platform,jdbcType=INTEGER},
      tech_id = #{record.techId,jdbcType=BIGINT},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      order_amount = #{record.orderAmount,jdbcType=BIGINT},
      rebate_amount = #{record.rebateAmount,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.DistributorRebateLimitTab">
    update distributor_rebate_limit_tab
    <set>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="unifiedOrderId != null">
        unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="techId != null">
        tech_id = #{techId,jdbcType=BIGINT},
      </if>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="rebateAmount != null">
        rebate_amount = #{rebateAmount,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.commisson.repository.db.DistributorRebateLimitTab">
    update distributor_rebate_limit_tab
    set config_id = #{configId,jdbcType=BIGINT},
      unified_order_id = #{unifiedOrderId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      tech_id = #{techId,jdbcType=BIGINT},
      dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      rebate_amount = #{rebateAmount,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into distributor_rebate_limit_tab
    (config_id, unified_order_id, user_id, platform, tech_id, dp_shop_id, ext_info, order_amount, 
      rebate_amount, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.configId,jdbcType=BIGINT}, #{item.unifiedOrderId,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, 
        #{item.platform,jdbcType=INTEGER}, #{item.techId,jdbcType=BIGINT}, #{item.dpShopId,jdbcType=BIGINT}, 
        #{item.extInfo,jdbcType=VARCHAR}, #{item.orderAmount,jdbcType=BIGINT}, #{item.rebateAmount,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>