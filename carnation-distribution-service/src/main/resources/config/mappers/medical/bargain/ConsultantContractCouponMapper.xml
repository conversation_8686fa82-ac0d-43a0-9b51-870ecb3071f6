<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.dao.ConsultantContractCouponMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCoupon">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="tgc_biz_type" jdbcType="INTEGER" property="tgcBizType" />
    <result column="dp_coupon_id" jdbcType="VARCHAR" property="dpCouponId" />
    <result column="mt_coupon_id" jdbcType="VARCHAR" property="mtCouponId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_id, account_type, account_id, contract_id, tgc_biz_type, dp_coupon_id, mt_coupon_id, 
    start_time, end_time, status, remark, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCouponCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from consultant_contract_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from consultant_contract_coupon
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from consultant_contract_coupon
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCouponCriteria">
    delete from consultant_contract_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCoupon">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into consultant_contract_coupon (biz_id, account_type, account_id, 
      contract_id, tgc_biz_type, dp_coupon_id, 
      mt_coupon_id, start_time, end_time, 
      status, remark, add_time, 
      update_time)
    values (#{bizId,jdbcType=VARCHAR}, #{accountType,jdbcType=INTEGER}, #{accountId,jdbcType=BIGINT}, 
      #{contractId,jdbcType=VARCHAR}, #{tgcBizType,jdbcType=INTEGER}, #{dpCouponId,jdbcType=VARCHAR}, 
      #{mtCouponId,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCoupon">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into consultant_contract_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="tgcBizType != null">
        tgc_biz_type,
      </if>
      <if test="dpCouponId != null">
        dp_coupon_id,
      </if>
      <if test="mtCouponId != null">
        mt_coupon_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="tgcBizType != null">
        #{tgcBizType,jdbcType=INTEGER},
      </if>
      <if test="dpCouponId != null">
        #{dpCouponId,jdbcType=VARCHAR},
      </if>
      <if test="mtCouponId != null">
        #{mtCouponId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCouponCriteria" resultType="java.lang.Long">
    select count(*) from consultant_contract_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update consultant_contract_coupon
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.contractId != null">
        contract_id = #{record.contractId,jdbcType=VARCHAR},
      </if>
      <if test="record.tgcBizType != null">
        tgc_biz_type = #{record.tgcBizType,jdbcType=INTEGER},
      </if>
      <if test="record.dpCouponId != null">
        dp_coupon_id = #{record.dpCouponId,jdbcType=VARCHAR},
      </if>
      <if test="record.mtCouponId != null">
        mt_coupon_id = #{record.mtCouponId,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update consultant_contract_coupon
    set id = #{record.id,jdbcType=BIGINT},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=BIGINT},
      contract_id = #{record.contractId,jdbcType=VARCHAR},
      tgc_biz_type = #{record.tgcBizType,jdbcType=INTEGER},
      dp_coupon_id = #{record.dpCouponId,jdbcType=VARCHAR},
      mt_coupon_id = #{record.mtCouponId,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCoupon">
    update consultant_contract_coupon
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="tgcBizType != null">
        tgc_biz_type = #{tgcBizType,jdbcType=INTEGER},
      </if>
      <if test="dpCouponId != null">
        dp_coupon_id = #{dpCouponId,jdbcType=VARCHAR},
      </if>
      <if test="mtCouponId != null">
        mt_coupon_id = #{mtCouponId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.medical.bargain.contract.coupon.repository.db.ConsultantContractCoupon">
    update consultant_contract_coupon
    set biz_id = #{bizId,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=BIGINT},
      contract_id = #{contractId,jdbcType=VARCHAR},
      tgc_biz_type = #{tgcBizType,jdbcType=INTEGER},
      dp_coupon_id = #{dpCouponId,jdbcType=VARCHAR},
      mt_coupon_id = #{mtCouponId,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>