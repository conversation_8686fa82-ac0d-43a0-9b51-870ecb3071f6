<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.live.repository.dao.QrLiveShareInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.live.repository.db.QrLiveShareInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="live_id" jdbcType="BIGINT" property="liveId" />
    <result column="zxs_id" jdbcType="BIGINT" property="zxsId" />
    <result column="qr_code_url" jdbcType="VARCHAR" property="qrCodeUrl" />
    <result column="short_url" jdbcType="VARCHAR" property="shortUrl" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, live_id, zxs_id, qr_code_url, short_url, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.live.repository.example.QrLiveShareInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qr_live_share_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qr_live_share_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from qr_live_share_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.live.repository.example.QrLiveShareInfoExample">
    delete from qr_live_share_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.live.repository.db.QrLiveShareInfo" useGeneratedKeys="true">
    insert into qr_live_share_info (live_id, zxs_id, qr_code_url, 
      short_url, add_time, update_time
      )
    values (#{liveId,jdbcType=BIGINT}, #{zxsId,jdbcType=BIGINT}, #{qrCodeUrl,jdbcType=VARCHAR}, 
      #{shortUrl,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.live.repository.db.QrLiveShareInfo" useGeneratedKeys="true">
    insert into qr_live_share_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="liveId != null">
        live_id,
      </if>
      <if test="zxsId != null">
        zxs_id,
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url,
      </if>
      <if test="shortUrl != null">
        short_url,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="liveId != null">
        #{liveId,jdbcType=BIGINT},
      </if>
      <if test="zxsId != null">
        #{zxsId,jdbcType=BIGINT},
      </if>
      <if test="qrCodeUrl != null">
        #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="shortUrl != null">
        #{shortUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.live.repository.example.QrLiveShareInfoExample" resultType="java.lang.Long">
    select count(*) from qr_live_share_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qr_live_share_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=BIGINT},
      </if>
      <if test="record.zxsId != null">
        zxs_id = #{record.zxsId,jdbcType=BIGINT},
      </if>
      <if test="record.qrCodeUrl != null">
        qr_code_url = #{record.qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.shortUrl != null">
        short_url = #{record.shortUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qr_live_share_info
    set id = #{record.id,jdbcType=BIGINT},
      live_id = #{record.liveId,jdbcType=BIGINT},
      zxs_id = #{record.zxsId,jdbcType=BIGINT},
      qr_code_url = #{record.qrCodeUrl,jdbcType=VARCHAR},
      short_url = #{record.shortUrl,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.live.repository.db.QrLiveShareInfo">
    update qr_live_share_info
    <set>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=BIGINT},
      </if>
      <if test="zxsId != null">
        zxs_id = #{zxsId,jdbcType=BIGINT},
      </if>
      <if test="qrCodeUrl != null">
        qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="shortUrl != null">
        short_url = #{shortUrl,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.live.repository.db.QrLiveShareInfo">
    update qr_live_share_info
    set live_id = #{liveId,jdbcType=BIGINT},
      zxs_id = #{zxsId,jdbcType=BIGINT},
      qr_code_url = #{qrCodeUrl,jdbcType=VARCHAR},
      short_url = #{shortUrl,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into qr_live_share_info
    (live_id, zxs_id, qr_code_url, short_url, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.liveId,jdbcType=BIGINT}, #{item.zxsId,jdbcType=BIGINT}, #{item.qrCodeUrl,jdbcType=VARCHAR}, 
        #{item.shortUrl,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>