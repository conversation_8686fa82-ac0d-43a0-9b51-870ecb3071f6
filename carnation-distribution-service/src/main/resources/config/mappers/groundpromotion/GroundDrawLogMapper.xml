<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundDrawLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ground_promotion_id" jdbcType="BIGINT" property="groundPromotionId" />
    <result column="ground_promotion_code" jdbcType="VARCHAR" property="groundPromotionCode" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="TINYINT" property="platform" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="coupon_feed_back_id" jdbcType="VARCHAR" property="couponFeedBackId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ground_promotion_id, ground_promotion_code, user_id, platform, coupon_id, coupon_feed_back_id, 
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_draw_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_draw_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_draw_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLogExample">
    delete from ground_draw_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLog">
    insert into ground_draw_log (id, ground_promotion_id, ground_promotion_code, 
      user_id, platform, coupon_id, 
      coupon_feed_back_id, add_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{groundPromotionId,jdbcType=BIGINT}, #{groundPromotionCode,jdbcType=VARCHAR}, 
      #{userId,jdbcType=BIGINT}, #{platform,jdbcType=TINYINT}, #{couponId,jdbcType=VARCHAR}, 
      #{couponFeedBackId,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLog">
    insert into ground_draw_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="groundPromotionId != null">
        ground_promotion_id,
      </if>
      <if test="groundPromotionCode != null">
        ground_promotion_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="couponFeedBackId != null">
        coupon_feed_back_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="groundPromotionId != null">
        #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="groundPromotionCode != null">
        #{groundPromotionCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="couponFeedBackId != null">
        #{couponFeedBackId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLogExample" resultType="java.lang.Long">
    select count(*) from ground_draw_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_draw_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groundPromotionId != null">
        ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="record.groundPromotionCode != null">
        ground_promotion_code = #{record.groundPromotionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=TINYINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.couponFeedBackId != null">
        coupon_feed_back_id = #{record.couponFeedBackId,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_draw_log
    set id = #{record.id,jdbcType=BIGINT},
      ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      ground_promotion_code = #{record.groundPromotionCode,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      platform = #{record.platform,jdbcType=TINYINT},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      coupon_feed_back_id = #{record.couponFeedBackId,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLog">
    update ground_draw_log
    <set>
      <if test="groundPromotionId != null">
        ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="groundPromotionCode != null">
        ground_promotion_code = #{groundPromotionCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=TINYINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="couponFeedBackId != null">
        coupon_feed_back_id = #{couponFeedBackId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDrawLog">
    update ground_draw_log
    set ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      ground_promotion_code = #{groundPromotionCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=TINYINT},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      coupon_feed_back_id = #{couponFeedBackId,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into ground_draw_log
    (id, ground_promotion_id, ground_promotion_code, user_id, platform, coupon_id, coupon_feed_back_id, 
      add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.groundPromotionId,jdbcType=BIGINT}, #{item.groundPromotionCode,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=BIGINT}, #{item.platform,jdbcType=TINYINT}, #{item.couponId,jdbcType=VARCHAR}, 
        #{item.couponFeedBackId,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>