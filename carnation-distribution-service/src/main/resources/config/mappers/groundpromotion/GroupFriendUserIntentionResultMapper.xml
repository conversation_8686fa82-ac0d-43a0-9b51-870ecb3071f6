<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroupFriendUserIntentionResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_platform" jdbcType="INTEGER" property="userPlatform" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="channel_code_id" jdbcType="BIGINT" property="channelCodeId" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="biz_line" jdbcType="INTEGER" property="bizLine" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="wechat_user_id" jdbcType="VARCHAR" property="wechatUserId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, channel, user_id, user_platform, event_type, event_time, channel_code_id, biz_code, 
    biz_type, biz_line, corp_id, wechat_user_id, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from group_friend_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from group_friend_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from group_friend_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResultExample">
    delete from group_friend_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResult" useGeneratedKeys="true">
    insert into group_friend_user_intention_result (channel, user_id, user_platform, 
      event_type, event_time, channel_code_id, 
      biz_code, biz_type, biz_line, 
      corp_id, wechat_user_id, add_time, 
      update_time)
    values (#{channel,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, #{userPlatform,jdbcType=INTEGER}, 
      #{eventType,jdbcType=INTEGER}, #{eventTime,jdbcType=TIMESTAMP}, #{channelCodeId,jdbcType=BIGINT}, 
      #{bizCode,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, #{bizLine,jdbcType=INTEGER}, 
      #{corpId,jdbcType=VARCHAR}, #{wechatUserId,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResult" useGeneratedKeys="true">
    insert into group_friend_user_intention_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channel != null">
        channel,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userPlatform != null">
        user_platform,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="channelCodeId != null">
        channel_code_id,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizLine != null">
        biz_line,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="wechatUserId != null">
        wechat_user_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelCodeId != null">
        #{channelCodeId,jdbcType=BIGINT},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizLine != null">
        #{bizLine,jdbcType=INTEGER},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="wechatUserId != null">
        #{wechatUserId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResultExample" resultType="java.lang.Long">
    select count(*) from group_friend_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update group_friend_user_intention_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userPlatform != null">
        user_platform = #{record.userPlatform,jdbcType=INTEGER},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=INTEGER},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channelCodeId != null">
        channel_code_id = #{record.channelCodeId,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.bizLine != null">
        biz_line = #{record.bizLine,jdbcType=INTEGER},
      </if>
      <if test="record.corpId != null">
        corp_id = #{record.corpId,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatUserId != null">
        wechat_user_id = #{record.wechatUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update group_friend_user_intention_result
    set id = #{record.id,jdbcType=BIGINT},
      channel = #{record.channel,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_platform = #{record.userPlatform,jdbcType=INTEGER},
      event_type = #{record.eventType,jdbcType=INTEGER},
      event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      channel_code_id = #{record.channelCodeId,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      biz_line = #{record.bizLine,jdbcType=INTEGER},
      corp_id = #{record.corpId,jdbcType=VARCHAR},
      wechat_user_id = #{record.wechatUserId,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResult">
    update group_friend_user_intention_result
    <set>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        user_platform = #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelCodeId != null">
        channel_code_id = #{channelCodeId,jdbcType=BIGINT},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizLine != null">
        biz_line = #{bizLine,jdbcType=INTEGER},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="wechatUserId != null">
        wechat_user_id = #{wechatUserId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserIntentionResult">
    update group_friend_user_intention_result
    set channel = #{channel,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_platform = #{userPlatform,jdbcType=INTEGER},
      event_type = #{eventType,jdbcType=INTEGER},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      channel_code_id = #{channelCodeId,jdbcType=BIGINT},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      biz_line = #{bizLine,jdbcType=INTEGER},
      corp_id = #{corpId,jdbcType=VARCHAR},
      wechat_user_id = #{wechatUserId,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into group_friend_user_intention_result
    (channel, user_id, user_platform, event_type, event_time, channel_code_id, biz_code, 
      biz_type, biz_line, corp_id, wechat_user_id, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.channel,jdbcType=INTEGER}, #{item.userId,jdbcType=BIGINT}, #{item.userPlatform,jdbcType=INTEGER}, 
        #{item.eventType,jdbcType=INTEGER}, #{item.eventTime,jdbcType=TIMESTAMP}, #{item.channelCodeId,jdbcType=BIGINT}, 
        #{item.bizCode,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, #{item.bizLine,jdbcType=INTEGER}, 
        #{item.corpId,jdbcType=VARCHAR}, #{item.wechatUserId,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>