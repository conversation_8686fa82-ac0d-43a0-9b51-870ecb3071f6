<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionProjectMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProject">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="scope_type" jdbcType="INTEGER" property="scopeType" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="arrangement_info" jdbcType="VARCHAR" property="arrangementInfo" />
    <result column="audit_node_id" jdbcType="BIGINT" property="auditNodeId" />
    <result column="bu_id" jdbcType="INTEGER" property="buId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="terminate_mis" jdbcType="VARCHAR" property="terminateMis" />
    <result column="terminate_time" jdbcType="TIMESTAMP" property="terminateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_name, start_time, end_time, city_id, template_id, scope_type, creator_mis, 
    arrangement_info, audit_node_id, bu_id, status, terminate_mis, terminate_time, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_promotion_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_promotion_project
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_promotion_project
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectExample">
    delete from ground_promotion_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProject" useGeneratedKeys="true">
    insert into ground_promotion_project (project_name, start_time, end_time, 
      city_id, template_id, scope_type, 
      creator_mis, arrangement_info, audit_node_id, 
      bu_id, status, terminate_mis, 
      terminate_time, create_time, update_time
      )
    values (#{projectName,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{cityId,jdbcType=INTEGER}, #{templateId,jdbcType=BIGINT}, #{scopeType,jdbcType=INTEGER}, 
      #{creatorMis,jdbcType=VARCHAR}, #{arrangementInfo,jdbcType=VARCHAR}, #{auditNodeId,jdbcType=BIGINT}, 
      #{buId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{terminateMis,jdbcType=VARCHAR}, 
      #{terminateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProject" useGeneratedKeys="true">
    insert into ground_promotion_project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectName != null">
        project_name,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="arrangementInfo != null">
        arrangement_info,
      </if>
      <if test="auditNodeId != null">
        audit_node_id,
      </if>
      <if test="buId != null">
        bu_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="terminateMis != null">
        terminate_mis,
      </if>
      <if test="terminateTime != null">
        terminate_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="arrangementInfo != null">
        #{arrangementInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditNodeId != null">
        #{auditNodeId,jdbcType=BIGINT},
      </if>
      <if test="buId != null">
        #{buId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="terminateMis != null">
        #{terminateMis,jdbcType=VARCHAR},
      </if>
      <if test="terminateTime != null">
        #{terminateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectExample" resultType="java.lang.Long">
    select count(*) from ground_promotion_project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_promotion_project
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.scopeType != null">
        scope_type = #{record.scopeType,jdbcType=INTEGER},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.arrangementInfo != null">
        arrangement_info = #{record.arrangementInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.auditNodeId != null">
        audit_node_id = #{record.auditNodeId,jdbcType=BIGINT},
      </if>
      <if test="record.buId != null">
        bu_id = #{record.buId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.terminateMis != null">
        terminate_mis = #{record.terminateMis,jdbcType=VARCHAR},
      </if>
      <if test="record.terminateTime != null">
        terminate_time = #{record.terminateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_promotion_project
    set id = #{record.id,jdbcType=BIGINT},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      city_id = #{record.cityId,jdbcType=INTEGER},
      template_id = #{record.templateId,jdbcType=BIGINT},
      scope_type = #{record.scopeType,jdbcType=INTEGER},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      arrangement_info = #{record.arrangementInfo,jdbcType=VARCHAR},
      audit_node_id = #{record.auditNodeId,jdbcType=BIGINT},
      bu_id = #{record.buId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      terminate_mis = #{record.terminateMis,jdbcType=VARCHAR},
      terminate_time = #{record.terminateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProject">
    update ground_promotion_project
    <set>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="arrangementInfo != null">
        arrangement_info = #{arrangementInfo,jdbcType=VARCHAR},
      </if>
      <if test="auditNodeId != null">
        audit_node_id = #{auditNodeId,jdbcType=BIGINT},
      </if>
      <if test="buId != null">
        bu_id = #{buId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="terminateMis != null">
        terminate_mis = #{terminateMis,jdbcType=VARCHAR},
      </if>
      <if test="terminateTime != null">
        terminate_time = #{terminateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProject">
    update ground_promotion_project
    set project_name = #{projectName,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      city_id = #{cityId,jdbcType=INTEGER},
      template_id = #{templateId,jdbcType=BIGINT},
      scope_type = #{scopeType,jdbcType=INTEGER},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      arrangement_info = #{arrangementInfo,jdbcType=VARCHAR},
      audit_node_id = #{auditNodeId,jdbcType=BIGINT},
      bu_id = #{buId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      terminate_mis = #{terminateMis,jdbcType=VARCHAR},
      terminate_time = #{terminateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_promotion_project
    (project_name, start_time, end_time, city_id, template_id, scope_type, creator_mis, 
      arrangement_info, audit_node_id, bu_id, status, terminate_mis, terminate_time, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.projectName,jdbcType=VARCHAR}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, 
        #{item.cityId,jdbcType=INTEGER}, #{item.templateId,jdbcType=BIGINT}, #{item.scopeType,jdbcType=INTEGER}, 
        #{item.creatorMis,jdbcType=VARCHAR}, #{item.arrangementInfo,jdbcType=VARCHAR}, 
        #{item.auditNodeId,jdbcType=BIGINT}, #{item.buId,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, 
        #{item.terminateMis,jdbcType=VARCHAR}, #{item.terminateTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>