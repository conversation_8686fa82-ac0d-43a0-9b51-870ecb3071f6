<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionAdvancedInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ground_promotion_id" jdbcType="BIGINT" property="groundPromotionId" />
    <result column="is_issue_coupon" jdbcType="TINYINT" property="isIssueCoupon" />
    <result column="dp_coupon_code" jdbcType="VARCHAR" property="dpCouponCode" />
    <result column="mt_coupon_code" jdbcType="VARCHAR" property="mtCouponCode" />
    <result column="is_join_group" jdbcType="TINYINT" property="isJoinGroup" />
    <result column="join_type" jdbcType="TINYINT" property="joinType" />
    <result column="group_path" jdbcType="VARCHAR" property="groupPath" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_issue_wx_group_coupon" jdbcType="TINYINT" property="isIssueWxGroupCoupon" />
    <result column="wx_group_coupon_info" jdbcType="VARCHAR" property="wxGroupCouponInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ground_promotion_id, is_issue_coupon, dp_coupon_code, mt_coupon_code, is_join_group, 
    join_type, group_path, add_time, update_time, is_issue_wx_group_coupon, wx_group_coupon_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_promotion_advanced_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_promotion_advanced_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_promotion_advanced_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfoExample">
    delete from ground_promotion_advanced_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfo" useGeneratedKeys="true">
    insert into ground_promotion_advanced_info (ground_promotion_id, is_issue_coupon, 
      dp_coupon_code, mt_coupon_code, is_join_group, 
      join_type, group_path, add_time, 
      update_time, is_issue_wx_group_coupon, wx_group_coupon_info
      )
    values (#{groundPromotionId,jdbcType=BIGINT}, #{isIssueCoupon,jdbcType=TINYINT}, 
      #{dpCouponCode,jdbcType=VARCHAR}, #{mtCouponCode,jdbcType=VARCHAR}, #{isJoinGroup,jdbcType=TINYINT}, 
      #{joinType,jdbcType=TINYINT}, #{groupPath,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isIssueWxGroupCoupon,jdbcType=TINYINT}, #{wxGroupCouponInfo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfo" useGeneratedKeys="true">
    insert into ground_promotion_advanced_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        ground_promotion_id,
      </if>
      <if test="isIssueCoupon != null">
        is_issue_coupon,
      </if>
      <if test="dpCouponCode != null">
        dp_coupon_code,
      </if>
      <if test="mtCouponCode != null">
        mt_coupon_code,
      </if>
      <if test="isJoinGroup != null">
        is_join_group,
      </if>
      <if test="joinType != null">
        join_type,
      </if>
      <if test="groupPath != null">
        group_path,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isIssueWxGroupCoupon != null">
        is_issue_wx_group_coupon,
      </if>
      <if test="wxGroupCouponInfo != null">
        wx_group_coupon_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="isIssueCoupon != null">
        #{isIssueCoupon,jdbcType=TINYINT},
      </if>
      <if test="dpCouponCode != null">
        #{dpCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="mtCouponCode != null">
        #{mtCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="isJoinGroup != null">
        #{isJoinGroup,jdbcType=TINYINT},
      </if>
      <if test="joinType != null">
        #{joinType,jdbcType=TINYINT},
      </if>
      <if test="groupPath != null">
        #{groupPath,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isIssueWxGroupCoupon != null">
        #{isIssueWxGroupCoupon,jdbcType=TINYINT},
      </if>
      <if test="wxGroupCouponInfo != null">
        #{wxGroupCouponInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfoExample" resultType="java.lang.Long">
    select count(*) from ground_promotion_advanced_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_promotion_advanced_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groundPromotionId != null">
        ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="record.isIssueCoupon != null">
        is_issue_coupon = #{record.isIssueCoupon,jdbcType=TINYINT},
      </if>
      <if test="record.dpCouponCode != null">
        dp_coupon_code = #{record.dpCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mtCouponCode != null">
        mt_coupon_code = #{record.mtCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isJoinGroup != null">
        is_join_group = #{record.isJoinGroup,jdbcType=TINYINT},
      </if>
      <if test="record.joinType != null">
        join_type = #{record.joinType,jdbcType=TINYINT},
      </if>
      <if test="record.groupPath != null">
        group_path = #{record.groupPath,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isIssueWxGroupCoupon != null">
        is_issue_wx_group_coupon = #{record.isIssueWxGroupCoupon,jdbcType=TINYINT},
      </if>
      <if test="record.wxGroupCouponInfo != null">
        wx_group_coupon_info = #{record.wxGroupCouponInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_promotion_advanced_info
    set id = #{record.id,jdbcType=BIGINT},
      ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      is_issue_coupon = #{record.isIssueCoupon,jdbcType=TINYINT},
      dp_coupon_code = #{record.dpCouponCode,jdbcType=VARCHAR},
      mt_coupon_code = #{record.mtCouponCode,jdbcType=VARCHAR},
      is_join_group = #{record.isJoinGroup,jdbcType=TINYINT},
      join_type = #{record.joinType,jdbcType=TINYINT},
      group_path = #{record.groupPath,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_issue_wx_group_coupon = #{record.isIssueWxGroupCoupon,jdbcType=TINYINT},
      wx_group_coupon_info = #{record.wxGroupCouponInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfo">
    update ground_promotion_advanced_info
    <set>
      <if test="groundPromotionId != null">
        ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="isIssueCoupon != null">
        is_issue_coupon = #{isIssueCoupon,jdbcType=TINYINT},
      </if>
      <if test="dpCouponCode != null">
        dp_coupon_code = #{dpCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="mtCouponCode != null">
        mt_coupon_code = #{mtCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="isJoinGroup != null">
        is_join_group = #{isJoinGroup,jdbcType=TINYINT},
      </if>
      <if test="joinType != null">
        join_type = #{joinType,jdbcType=TINYINT},
      </if>
      <if test="groupPath != null">
        group_path = #{groupPath,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isIssueWxGroupCoupon != null">
        is_issue_wx_group_coupon = #{isIssueWxGroupCoupon,jdbcType=TINYINT},
      </if>
      <if test="wxGroupCouponInfo != null">
        wx_group_coupon_info = #{wxGroupCouponInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionAdvancedInfo">
    update ground_promotion_advanced_info
    set ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      is_issue_coupon = #{isIssueCoupon,jdbcType=TINYINT},
      dp_coupon_code = #{dpCouponCode,jdbcType=VARCHAR},
      mt_coupon_code = #{mtCouponCode,jdbcType=VARCHAR},
      is_join_group = #{isJoinGroup,jdbcType=TINYINT},
      join_type = #{joinType,jdbcType=TINYINT},
      group_path = #{groupPath,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_issue_wx_group_coupon = #{isIssueWxGroupCoupon,jdbcType=TINYINT},
      wx_group_coupon_info = #{wxGroupCouponInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_promotion_advanced_info
    (ground_promotion_id, is_issue_coupon, dp_coupon_code, mt_coupon_code, is_join_group, 
      join_type, group_path, add_time, update_time, is_issue_wx_group_coupon, wx_group_coupon_info
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groundPromotionId,jdbcType=BIGINT}, #{item.isIssueCoupon,jdbcType=TINYINT}, 
        #{item.dpCouponCode,jdbcType=VARCHAR}, #{item.mtCouponCode,jdbcType=VARCHAR}, #{item.isJoinGroup,jdbcType=TINYINT}, 
        #{item.joinType,jdbcType=TINYINT}, #{item.groupPath,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isIssueWxGroupCoupon,jdbcType=TINYINT}, 
        #{item.wxGroupCouponInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>