<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundQrLogExtMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundQrLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="mis_id" jdbcType="VARCHAR" property="misId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_app_new_user" jdbcType="TINYINT" property="isAppNewUser" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="landing_url" jdbcType="VARCHAR" property="landingUrl" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="bu_id" jdbcType="INTEGER" property="buId" />
  </resultMap>
    <resultMap id="KeyValeMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.bo.KeyValue">
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="numValue" jdbcType="BIGINT" property="numValue"/>
    </resultMap>
  <sql id="Base_Column_List">
    id, user_id, platform, distributor_code, mis_id, create_time, update_time, is_app_new_user, 
    biz_type, landing_url, ext_info, bu_id
  </sql>
  <select id="queryLatestScanLogByUserIdAndPlatform" resultMap="BaseResultMap">
      select
      *
      from ground_qr_log
      where user_id = #{userId,jdbcType=BIGINT}
      and platform = #{platform,jdbcType=INTEGER}
      <if test="createTime != null">
          and create_time &gt;= #{createTime,jdbcType=TIMESTAMP}
      </if>
      order by create_time desc
      limit 1
  </select>

    <select id="countScanNumsByDistributorCode" resultMap="KeyValeMap">
        select distributor_code as 'key', count(*) as 'numValue'
        from ground_qr_log
        where distributor_code in
        <foreach collection="distributorCodes" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
        <if test="startTime != null">
            and create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        group by distributor_code;
    </select>
</mapper>