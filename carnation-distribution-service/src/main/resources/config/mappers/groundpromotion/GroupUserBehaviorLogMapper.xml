<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroupUserBehaviorLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_type" jdbcType="INTEGER" property="groupType" />
    <result column="scene_id" jdbcType="INTEGER" property="sceneId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_platform" jdbcType="INTEGER" property="userPlatform" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="is_user_same_scene" jdbcType="BIT" property="isUserSameScene" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog">
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_type, scene_id, user_id, user_platform, event_type, event_time, biz_id, 
    group_id, is_user_same_scene, add_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from group_user_behavior_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from group_user_behavior_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLogExample">
    delete from group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog" useGeneratedKeys="true">
    insert into group_user_behavior_log (group_type, scene_id, user_id, 
      user_platform, event_type, event_time, 
      biz_id, group_id, is_user_same_scene, 
      add_time, update_time, detail
      )
    values (#{groupType,jdbcType=INTEGER}, #{sceneId,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, 
      #{userPlatform,jdbcType=INTEGER}, #{eventType,jdbcType=INTEGER}, #{eventTime,jdbcType=TIMESTAMP}, 
      #{bizId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{isUserSameScene,jdbcType=BIT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{detail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog" useGeneratedKeys="true">
    insert into group_user_behavior_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupType != null">
        group_type,
      </if>
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userPlatform != null">
        user_platform,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="isUserSameScene != null">
        is_user_same_scene,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupType != null">
        #{groupType,jdbcType=INTEGER},
      </if>
      <if test="sceneId != null">
        #{sceneId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="isUserSameScene != null">
        #{isUserSameScene,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLogExample" resultType="java.lang.Long">
    select count(*) from group_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update group_user_behavior_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groupType != null">
        group_type = #{record.groupType,jdbcType=INTEGER},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userPlatform != null">
        user_platform = #{record.userPlatform,jdbcType=INTEGER},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=INTEGER},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.isUserSameScene != null">
        is_user_same_scene = #{record.isUserSameScene,jdbcType=BIT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.detail != null">
        detail = #{record.detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update group_user_behavior_log
    set id = #{record.id,jdbcType=BIGINT},
      group_type = #{record.groupType,jdbcType=INTEGER},
      scene_id = #{record.sceneId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_platform = #{record.userPlatform,jdbcType=INTEGER},
      event_type = #{record.eventType,jdbcType=INTEGER},
      event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      is_user_same_scene = #{record.isUserSameScene,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      detail = #{record.detail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update group_user_behavior_log
    set id = #{record.id,jdbcType=BIGINT},
      group_type = #{record.groupType,jdbcType=INTEGER},
      scene_id = #{record.sceneId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_platform = #{record.userPlatform,jdbcType=INTEGER},
      event_type = #{record.eventType,jdbcType=INTEGER},
      event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      is_user_same_scene = #{record.isUserSameScene,jdbcType=BIT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog">
    update group_user_behavior_log
    <set>
      <if test="groupType != null">
        group_type = #{groupType,jdbcType=INTEGER},
      </if>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        user_platform = #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="isUserSameScene != null">
        is_user_same_scene = #{isUserSameScene,jdbcType=BIT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog">
    update group_user_behavior_log
    set group_type = #{groupType,jdbcType=INTEGER},
      scene_id = #{sceneId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_platform = #{userPlatform,jdbcType=INTEGER},
      event_type = #{eventType,jdbcType=INTEGER},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      biz_id = #{bizId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      is_user_same_scene = #{isUserSameScene,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      detail = #{detail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserBehaviorLog">
    update group_user_behavior_log
    set group_type = #{groupType,jdbcType=INTEGER},
      scene_id = #{sceneId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_platform = #{userPlatform,jdbcType=INTEGER},
      event_type = #{eventType,jdbcType=INTEGER},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      biz_id = #{bizId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      is_user_same_scene = #{isUserSameScene,jdbcType=BIT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into group_user_behavior_log
    (group_type, scene_id, user_id, user_platform, event_type, event_time, biz_id, group_id, 
      is_user_same_scene, add_time, update_time, detail)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groupType,jdbcType=INTEGER}, #{item.sceneId,jdbcType=INTEGER}, #{item.userId,jdbcType=BIGINT}, 
        #{item.userPlatform,jdbcType=INTEGER}, #{item.eventType,jdbcType=INTEGER}, #{item.eventTime,jdbcType=TIMESTAMP}, 
        #{item.bizId,jdbcType=VARCHAR}, #{item.groupId,jdbcType=VARCHAR}, #{item.isUserSameScene,jdbcType=BIT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.detail,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
</mapper>