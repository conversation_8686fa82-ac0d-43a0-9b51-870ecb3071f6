<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="merchants_id" jdbcType="BIGINT" property="merchantsId" />
    <result column="allot_start_time" jdbcType="TIMESTAMP" property="allotStartTime" />
    <result column="allot_end_time" jdbcType="TIMESTAMP" property="allotEndTime" />
    <result column="ground_start_time" jdbcType="TIMESTAMP" property="groundStartTime" />
    <result column="ground_end_time" jdbcType="TIMESTAMP" property="groundEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="scope_type" jdbcType="INTEGER" property="scopeType" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="bu_id" jdbcType="INTEGER" property="buId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, city_id, status, merchants_id, allot_start_time, allot_end_time, ground_start_time, 
    ground_end_time, create_time, update_time, project_name, scope_type, creator_mis, 
    bu_id, project_id, template_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_promotion
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_promotion
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_promotion
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionExample">
    delete from ground_promotion
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion" useGeneratedKeys="true">
    insert into ground_promotion (name, city_id, status, 
      merchants_id, allot_start_time, allot_end_time, 
      ground_start_time, ground_end_time, create_time, 
      update_time, project_name, scope_type, 
      creator_mis, bu_id, project_id, 
      template_id)
    values (#{name,jdbcType=VARCHAR}, #{cityId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{merchantsId,jdbcType=BIGINT}, #{allotStartTime,jdbcType=TIMESTAMP}, #{allotEndTime,jdbcType=TIMESTAMP}, 
      #{groundStartTime,jdbcType=TIMESTAMP}, #{groundEndTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{projectName,jdbcType=VARCHAR}, #{scopeType,jdbcType=INTEGER}, 
      #{creatorMis,jdbcType=VARCHAR}, #{buId,jdbcType=INTEGER}, #{projectId,jdbcType=BIGINT}, 
      #{templateId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion" useGeneratedKeys="true">
    insert into ground_promotion
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="merchantsId != null">
        merchants_id,
      </if>
      <if test="allotStartTime != null">
        allot_start_time,
      </if>
      <if test="allotEndTime != null">
        allot_end_time,
      </if>
      <if test="groundStartTime != null">
        ground_start_time,
      </if>
      <if test="groundEndTime != null">
        ground_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="buId != null">
        bu_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="merchantsId != null">
        #{merchantsId,jdbcType=BIGINT},
      </if>
      <if test="allotStartTime != null">
        #{allotStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allotEndTime != null">
        #{allotEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groundStartTime != null">
        #{groundStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groundEndTime != null">
        #{groundEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="buId != null">
        #{buId,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionExample" resultType="java.lang.Long">
    select count(*) from ground_promotion
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_promotion
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.merchantsId != null">
        merchants_id = #{record.merchantsId,jdbcType=BIGINT},
      </if>
      <if test="record.allotStartTime != null">
        allot_start_time = #{record.allotStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allotEndTime != null">
        allot_end_time = #{record.allotEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groundStartTime != null">
        ground_start_time = #{record.groundStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groundEndTime != null">
        ground_end_time = #{record.groundEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.scopeType != null">
        scope_type = #{record.scopeType,jdbcType=INTEGER},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.buId != null">
        bu_id = #{record.buId,jdbcType=INTEGER},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_promotion
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      city_id = #{record.cityId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      merchants_id = #{record.merchantsId,jdbcType=BIGINT},
      allot_start_time = #{record.allotStartTime,jdbcType=TIMESTAMP},
      allot_end_time = #{record.allotEndTime,jdbcType=TIMESTAMP},
      ground_start_time = #{record.groundStartTime,jdbcType=TIMESTAMP},
      ground_end_time = #{record.groundEndTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      scope_type = #{record.scopeType,jdbcType=INTEGER},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      bu_id = #{record.buId,jdbcType=INTEGER},
      project_id = #{record.projectId,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion">
    update ground_promotion
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="merchantsId != null">
        merchants_id = #{merchantsId,jdbcType=BIGINT},
      </if>
      <if test="allotStartTime != null">
        allot_start_time = #{allotStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="allotEndTime != null">
        allot_end_time = #{allotEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groundStartTime != null">
        ground_start_time = #{groundStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groundEndTime != null">
        ground_end_time = #{groundEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="buId != null">
        bu_id = #{buId,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotion">
    update ground_promotion
    set name = #{name,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      merchants_id = #{merchantsId,jdbcType=BIGINT},
      allot_start_time = #{allotStartTime,jdbcType=TIMESTAMP},
      allot_end_time = #{allotEndTime,jdbcType=TIMESTAMP},
      ground_start_time = #{groundStartTime,jdbcType=TIMESTAMP},
      ground_end_time = #{groundEndTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      project_name = #{projectName,jdbcType=VARCHAR},
      scope_type = #{scopeType,jdbcType=INTEGER},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      bu_id = #{buId,jdbcType=INTEGER},
      project_id = #{projectId,jdbcType=BIGINT},
      template_id = #{templateId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_promotion
    (name, city_id, status, merchants_id, allot_start_time, allot_end_time, ground_start_time, 
      ground_end_time, create_time, update_time, project_name, scope_type, creator_mis, 
      bu_id, project_id, template_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.cityId,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, 
        #{item.merchantsId,jdbcType=BIGINT}, #{item.allotStartTime,jdbcType=TIMESTAMP}, 
        #{item.allotEndTime,jdbcType=TIMESTAMP}, #{item.groundStartTime,jdbcType=TIMESTAMP}, 
        #{item.groundEndTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.projectName,jdbcType=VARCHAR}, #{item.scopeType,jdbcType=INTEGER}, 
        #{item.creatorMis,jdbcType=VARCHAR}, #{item.buId,jdbcType=INTEGER}, #{item.projectId,jdbcType=BIGINT}, 
        #{item.templateId,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>