<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionTemplateMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="material_info" jdbcType="VARCHAR" property="materialInfo" />
    <result column="is_issue_coupon" jdbcType="TINYINT" property="isIssueCoupon" />
    <result column="dp_coupon_code" jdbcType="VARCHAR" property="dpCouponCode" />
    <result column="mt_coupon_code" jdbcType="VARCHAR" property="mtCouponCode" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
    <result column="bu_id" jdbcType="INTEGER" property="buId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_standard_product" jdbcType="TINYINT" property="isStandardProduct" />
    <result column="standard_product_url" jdbcType="VARCHAR" property="standardProductUrl" />
    <result column="is_issue_wx_group_coupon" jdbcType="TINYINT" property="isIssueWxGroupCoupon" />
    <result column="wx_group_coupon_info" jdbcType="VARCHAR" property="wxGroupCouponInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, template_name, material_info, is_issue_coupon, dp_coupon_code, mt_coupon_code, 
    creator_mis, bu_id, status, create_time, update_time, is_standard_product, standard_product_url, 
    is_issue_wx_group_coupon, wx_group_coupon_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_promotion_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_promotion_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_promotion_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplateExample">
    delete from ground_promotion_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplate" useGeneratedKeys="true">
    insert into ground_promotion_template (template_id, template_name, material_info, 
      is_issue_coupon, dp_coupon_code, mt_coupon_code, 
      creator_mis, bu_id, status, 
      create_time, update_time, is_standard_product, 
      standard_product_url, is_issue_wx_group_coupon, 
      wx_group_coupon_info)
    values (#{templateId,jdbcType=BIGINT}, #{templateName,jdbcType=VARCHAR}, #{materialInfo,jdbcType=VARCHAR}, 
      #{isIssueCoupon,jdbcType=TINYINT}, #{dpCouponCode,jdbcType=VARCHAR}, #{mtCouponCode,jdbcType=VARCHAR}, 
      #{creatorMis,jdbcType=VARCHAR}, #{buId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isStandardProduct,jdbcType=TINYINT}, 
      #{standardProductUrl,jdbcType=VARCHAR}, #{isIssueWxGroupCoupon,jdbcType=TINYINT}, 
      #{wxGroupCouponInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplate" useGeneratedKeys="true">
    insert into ground_promotion_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="materialInfo != null">
        material_info,
      </if>
      <if test="isIssueCoupon != null">
        is_issue_coupon,
      </if>
      <if test="dpCouponCode != null">
        dp_coupon_code,
      </if>
      <if test="mtCouponCode != null">
        mt_coupon_code,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
      <if test="buId != null">
        bu_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isStandardProduct != null">
        is_standard_product,
      </if>
      <if test="standardProductUrl != null">
        standard_product_url,
      </if>
      <if test="isIssueWxGroupCoupon != null">
        is_issue_wx_group_coupon,
      </if>
      <if test="wxGroupCouponInfo != null">
        wx_group_coupon_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="materialInfo != null">
        #{materialInfo,jdbcType=VARCHAR},
      </if>
      <if test="isIssueCoupon != null">
        #{isIssueCoupon,jdbcType=TINYINT},
      </if>
      <if test="dpCouponCode != null">
        #{dpCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="mtCouponCode != null">
        #{mtCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="buId != null">
        #{buId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isStandardProduct != null">
        #{isStandardProduct,jdbcType=TINYINT},
      </if>
      <if test="standardProductUrl != null">
        #{standardProductUrl,jdbcType=VARCHAR},
      </if>
      <if test="isIssueWxGroupCoupon != null">
        #{isIssueWxGroupCoupon,jdbcType=TINYINT},
      </if>
      <if test="wxGroupCouponInfo != null">
        #{wxGroupCouponInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplateExample" resultType="java.lang.Long">
    select count(*) from ground_promotion_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_promotion_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialInfo != null">
        material_info = #{record.materialInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.isIssueCoupon != null">
        is_issue_coupon = #{record.isIssueCoupon,jdbcType=TINYINT},
      </if>
      <if test="record.dpCouponCode != null">
        dp_coupon_code = #{record.dpCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mtCouponCode != null">
        mt_coupon_code = #{record.mtCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="record.buId != null">
        bu_id = #{record.buId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isStandardProduct != null">
        is_standard_product = #{record.isStandardProduct,jdbcType=TINYINT},
      </if>
      <if test="record.standardProductUrl != null">
        standard_product_url = #{record.standardProductUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isIssueWxGroupCoupon != null">
        is_issue_wx_group_coupon = #{record.isIssueWxGroupCoupon,jdbcType=TINYINT},
      </if>
      <if test="record.wxGroupCouponInfo != null">
        wx_group_coupon_info = #{record.wxGroupCouponInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_promotion_template
    set id = #{record.id,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      material_info = #{record.materialInfo,jdbcType=VARCHAR},
      is_issue_coupon = #{record.isIssueCoupon,jdbcType=TINYINT},
      dp_coupon_code = #{record.dpCouponCode,jdbcType=VARCHAR},
      mt_coupon_code = #{record.mtCouponCode,jdbcType=VARCHAR},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      bu_id = #{record.buId,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_standard_product = #{record.isStandardProduct,jdbcType=TINYINT},
      standard_product_url = #{record.standardProductUrl,jdbcType=VARCHAR},
      is_issue_wx_group_coupon = #{record.isIssueWxGroupCoupon,jdbcType=TINYINT},
      wx_group_coupon_info = #{record.wxGroupCouponInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplate">
    update ground_promotion_template
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="materialInfo != null">
        material_info = #{materialInfo,jdbcType=VARCHAR},
      </if>
      <if test="isIssueCoupon != null">
        is_issue_coupon = #{isIssueCoupon,jdbcType=TINYINT},
      </if>
      <if test="dpCouponCode != null">
        dp_coupon_code = #{dpCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="mtCouponCode != null">
        mt_coupon_code = #{mtCouponCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
      <if test="buId != null">
        bu_id = #{buId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isStandardProduct != null">
        is_standard_product = #{isStandardProduct,jdbcType=TINYINT},
      </if>
      <if test="standardProductUrl != null">
        standard_product_url = #{standardProductUrl,jdbcType=VARCHAR},
      </if>
      <if test="isIssueWxGroupCoupon != null">
        is_issue_wx_group_coupon = #{isIssueWxGroupCoupon,jdbcType=TINYINT},
      </if>
      <if test="wxGroupCouponInfo != null">
        wx_group_coupon_info = #{wxGroupCouponInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionTemplate">
    update ground_promotion_template
    set template_id = #{templateId,jdbcType=BIGINT},
      template_name = #{templateName,jdbcType=VARCHAR},
      material_info = #{materialInfo,jdbcType=VARCHAR},
      is_issue_coupon = #{isIssueCoupon,jdbcType=TINYINT},
      dp_coupon_code = #{dpCouponCode,jdbcType=VARCHAR},
      mt_coupon_code = #{mtCouponCode,jdbcType=VARCHAR},
      creator_mis = #{creatorMis,jdbcType=VARCHAR},
      bu_id = #{buId,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_standard_product = #{isStandardProduct,jdbcType=TINYINT},
      standard_product_url = #{standardProductUrl,jdbcType=VARCHAR},
      is_issue_wx_group_coupon = #{isIssueWxGroupCoupon,jdbcType=TINYINT},
      wx_group_coupon_info = #{wxGroupCouponInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_promotion_template
    (template_id, template_name, material_info, is_issue_coupon, dp_coupon_code, mt_coupon_code, 
      creator_mis, bu_id, status, create_time, update_time, is_standard_product, standard_product_url, 
      is_issue_wx_group_coupon, wx_group_coupon_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.templateId,jdbcType=BIGINT}, #{item.templateName,jdbcType=VARCHAR}, #{item.materialInfo,jdbcType=VARCHAR}, 
        #{item.isIssueCoupon,jdbcType=TINYINT}, #{item.dpCouponCode,jdbcType=VARCHAR}, 
        #{item.mtCouponCode,jdbcType=VARCHAR}, #{item.creatorMis,jdbcType=VARCHAR}, #{item.buId,jdbcType=INTEGER}, 
        #{item.status,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isStandardProduct,jdbcType=TINYINT}, #{item.standardProductUrl,jdbcType=VARCHAR}, 
        #{item.isIssueWxGroupCoupon,jdbcType=TINYINT}, #{item.wxGroupCouponInfo,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>