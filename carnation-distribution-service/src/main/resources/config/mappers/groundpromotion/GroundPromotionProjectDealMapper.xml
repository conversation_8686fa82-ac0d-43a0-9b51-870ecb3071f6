<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionProjectDealMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDeal">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="scope_type" jdbcType="INTEGER" property="scopeType" />
    <result column="mall_id" jdbcType="BIGINT" property="mallId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="deal_id" jdbcType="VARCHAR" property="dealId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, scope_type, mall_id, shop_id, deal_id, create_time, update_time, 
    product_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDealExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_promotion_project_deal
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_promotion_project_deal
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_promotion_project_deal
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDealExample">
    delete from ground_promotion_project_deal
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDeal" useGeneratedKeys="true">
    insert into ground_promotion_project_deal (project_id, scope_type, mall_id, 
      shop_id, deal_id, create_time, 
      update_time, product_type)
    values (#{projectId,jdbcType=BIGINT}, #{scopeType,jdbcType=INTEGER}, #{mallId,jdbcType=BIGINT}, 
      #{shopId,jdbcType=BIGINT}, #{dealId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{productType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDeal" useGeneratedKeys="true">
    insert into ground_promotion_project_deal
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="mallId != null">
        mall_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="dealId != null">
        deal_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="productType != null">
        product_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="mallId != null">
        #{mallId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="dealId != null">
        #{dealId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDealExample" resultType="java.lang.Long">
    select count(*) from ground_promotion_project_deal
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_promotion_project_deal
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.scopeType != null">
        scope_type = #{record.scopeType,jdbcType=INTEGER},
      </if>
      <if test="record.mallId != null">
        mall_id = #{record.mallId,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.dealId != null">
        deal_id = #{record.dealId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_promotion_project_deal
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      scope_type = #{record.scopeType,jdbcType=INTEGER},
      mall_id = #{record.mallId,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      deal_id = #{record.dealId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      product_type = #{record.productType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDeal">
    update ground_promotion_project_deal
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="mallId != null">
        mall_id = #{mallId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="dealId != null">
        deal_id = #{dealId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionProjectDeal">
    update ground_promotion_project_deal
    set project_id = #{projectId,jdbcType=BIGINT},
      scope_type = #{scopeType,jdbcType=INTEGER},
      mall_id = #{mallId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      deal_id = #{dealId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      product_type = #{productType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_promotion_project_deal
    (project_id, scope_type, mall_id, shop_id, deal_id, create_time, update_time, product_type
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.projectId,jdbcType=BIGINT}, #{item.scopeType,jdbcType=INTEGER}, #{item.mallId,jdbcType=BIGINT}, 
        #{item.shopId,jdbcType=BIGINT}, #{item.dealId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.productType,jdbcType=INTEGER})
    </foreach>
  </insert>
  <update id="batchUpdateDealId" parameterType="map">
    update ground_promotion_project_deal
    set deal_id = #{deal_id,jdbcType=VARCHAR}
    where id in
    <foreach close=")" collection="ids" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>
</mapper>