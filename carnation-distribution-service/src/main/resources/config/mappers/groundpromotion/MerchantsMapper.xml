<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.MerchantsMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.Merchants">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ground_promotion_id" jdbcType="BIGINT" property="groundPromotionId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="mis_id" jdbcType="VARCHAR" property="misId" />
    <result column="merchants_id" jdbcType="BIGINT" property="merchantsId" />
    <result column="merchants_start_time" jdbcType="TIMESTAMP" property="merchantsStartTime" />
    <result column="merchants_end_time" jdbcType="TIMESTAMP" property="merchantsEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ground_promotion_id, project_name, mis_id, merchants_id, merchants_start_time, 
    merchants_end_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.MerchantsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from merchants
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchants
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchants
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.MerchantsExample">
    delete from merchants
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.Merchants">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchants (ground_promotion_id, project_name, mis_id, 
      merchants_id, merchants_start_time, merchants_end_time, 
      create_time)
    values (#{groundPromotionId,jdbcType=BIGINT}, #{projectName,jdbcType=VARCHAR}, #{misId,jdbcType=VARCHAR}, 
      #{merchantsId,jdbcType=BIGINT}, #{merchantsStartTime,jdbcType=TIMESTAMP}, #{merchantsEndTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.Merchants">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into merchants
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        ground_promotion_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="misId != null">
        mis_id,
      </if>
      <if test="merchantsId != null">
        merchants_id,
      </if>
      <if test="merchantsStartTime != null">
        merchants_start_time,
      </if>
      <if test="merchantsEndTime != null">
        merchants_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="misId != null">
        #{misId,jdbcType=VARCHAR},
      </if>
      <if test="merchantsId != null">
        #{merchantsId,jdbcType=BIGINT},
      </if>
      <if test="merchantsStartTime != null">
        #{merchantsStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="merchantsEndTime != null">
        #{merchantsEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.MerchantsExample" resultType="java.lang.Long">
    select count(*) from merchants
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchants
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groundPromotionId != null">
        ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.misId != null">
        mis_id = #{record.misId,jdbcType=VARCHAR},
      </if>
      <if test="record.merchantsId != null">
        merchants_id = #{record.merchantsId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantsStartTime != null">
        merchants_start_time = #{record.merchantsStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.merchantsEndTime != null">
        merchants_end_time = #{record.merchantsEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchants
    set id = #{record.id,jdbcType=BIGINT},
      ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      mis_id = #{record.misId,jdbcType=VARCHAR},
      merchants_id = #{record.merchantsId,jdbcType=BIGINT},
      merchants_start_time = #{record.merchantsStartTime,jdbcType=TIMESTAMP},
      merchants_end_time = #{record.merchantsEndTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.Merchants">
    update merchants
    <set>
      <if test="groundPromotionId != null">
        ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="misId != null">
        mis_id = #{misId,jdbcType=VARCHAR},
      </if>
      <if test="merchantsId != null">
        merchants_id = #{merchantsId,jdbcType=BIGINT},
      </if>
      <if test="merchantsStartTime != null">
        merchants_start_time = #{merchantsStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="merchantsEndTime != null">
        merchants_end_time = #{merchantsEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.Merchants">
    update merchants
    set ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      project_name = #{projectName,jdbcType=VARCHAR},
      mis_id = #{misId,jdbcType=VARCHAR},
      merchants_id = #{merchantsId,jdbcType=BIGINT},
      merchants_start_time = #{merchantsStartTime,jdbcType=TIMESTAMP},
      merchants_end_time = #{merchantsEndTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into merchants
    (ground_promotion_id, project_name, mis_id, merchants_id, merchants_start_time, merchants_end_time, 
      create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groundPromotionId,jdbcType=BIGINT}, #{item.projectName,jdbcType=VARCHAR}, 
        #{item.misId,jdbcType=VARCHAR}, #{item.merchantsId,jdbcType=BIGINT}, #{item.merchantsStartTime,jdbcType=TIMESTAMP},
        #{item.merchantsEndTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>