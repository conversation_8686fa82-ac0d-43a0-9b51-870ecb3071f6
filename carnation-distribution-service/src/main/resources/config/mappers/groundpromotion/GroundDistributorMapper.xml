<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundDistributorMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributor">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ground_promotion_id" jdbcType="BIGINT" property="groundPromotionId" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="mis_id" jdbcType="VARCHAR" property="misId" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="landing_url" jdbcType="VARCHAR" property="landingUrl" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="distributor_pic" jdbcType="VARCHAR" property="distributorPic" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="point_id" jdbcType="BIGINT" property="pointId" />
    <result column="scope_type" jdbcType="INTEGER" property="scopeType" />
    <result column="scope_id" jdbcType="BIGINT" property="scopeId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ground_promotion_id, material_id, shop_id, mis_id, distributor_code, landing_url, 
    qr_code, distributor_pic, create_time, update_time, point_id, scope_type, scope_id, 
    ext_info
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributorExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_distributor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_distributor
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_distributor
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributorExample">
    delete from ground_distributor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributor" useGeneratedKeys="true">
    insert into ground_distributor (ground_promotion_id, material_id, shop_id, 
      mis_id, distributor_code, landing_url, 
      qr_code, distributor_pic, create_time, 
      update_time, point_id, scope_type, 
      scope_id, ext_info)
    values (#{groundPromotionId,jdbcType=BIGINT}, #{materialId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, 
      #{misId,jdbcType=VARCHAR}, #{distributorCode,jdbcType=VARCHAR}, #{landingUrl,jdbcType=VARCHAR}, 
      #{qrCode,jdbcType=VARCHAR}, #{distributorPic,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{pointId,jdbcType=BIGINT}, #{scopeType,jdbcType=INTEGER}, 
      #{scopeId,jdbcType=BIGINT}, #{extInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributor" useGeneratedKeys="true">
    insert into ground_distributor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        ground_promotion_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="misId != null">
        mis_id,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="landingUrl != null">
        landing_url,
      </if>
      <if test="qrCode != null">
        qr_code,
      </if>
      <if test="distributorPic != null">
        distributor_pic,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="pointId != null">
        point_id,
      </if>
      <if test="scopeType != null">
        scope_type,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="misId != null">
        #{misId,jdbcType=VARCHAR},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="landingUrl != null">
        #{landingUrl,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null">
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="distributorPic != null">
        #{distributorPic,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pointId != null">
        #{pointId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributorExample" resultType="java.lang.Long">
    select count(*) from ground_distributor
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_distributor
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groundPromotionId != null">
        ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.misId != null">
        mis_id = #{record.misId,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.landingUrl != null">
        landing_url = #{record.landingUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.qrCode != null">
        qr_code = #{record.qrCode,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorPic != null">
        distributor_pic = #{record.distributorPic,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pointId != null">
        point_id = #{record.pointId,jdbcType=BIGINT},
      </if>
      <if test="record.scopeType != null">
        scope_type = #{record.scopeType,jdbcType=INTEGER},
      </if>
      <if test="record.scopeId != null">
        scope_id = #{record.scopeId,jdbcType=BIGINT},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_distributor
    set id = #{record.id,jdbcType=BIGINT},
      ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      mis_id = #{record.misId,jdbcType=VARCHAR},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      landing_url = #{record.landingUrl,jdbcType=VARCHAR},
      qr_code = #{record.qrCode,jdbcType=VARCHAR},
      distributor_pic = #{record.distributorPic,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      point_id = #{record.pointId,jdbcType=BIGINT},
      scope_type = #{record.scopeType,jdbcType=INTEGER},
      scope_id = #{record.scopeId,jdbcType=BIGINT},
      ext_info = #{record.extInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributor">
    update ground_distributor
    <set>
      <if test="groundPromotionId != null">
        ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="misId != null">
        mis_id = #{misId,jdbcType=VARCHAR},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="landingUrl != null">
        landing_url = #{landingUrl,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null">
        qr_code = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="distributorPic != null">
        distributor_pic = #{distributorPic,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pointId != null">
        point_id = #{pointId,jdbcType=BIGINT},
      </if>
      <if test="scopeType != null">
        scope_type = #{scopeType,jdbcType=INTEGER},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundDistributor">
    update ground_distributor
    set ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      material_id = #{materialId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      mis_id = #{misId,jdbcType=VARCHAR},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      landing_url = #{landingUrl,jdbcType=VARCHAR},
      qr_code = #{qrCode,jdbcType=VARCHAR},
      distributor_pic = #{distributorPic,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      point_id = #{pointId,jdbcType=BIGINT},
      scope_type = #{scopeType,jdbcType=INTEGER},
      scope_id = #{scopeId,jdbcType=BIGINT},
      ext_info = #{extInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_distributor
    (ground_promotion_id, material_id, shop_id, mis_id, distributor_code, landing_url, 
      qr_code, distributor_pic, create_time, update_time, point_id, scope_type, scope_id, 
      ext_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groundPromotionId,jdbcType=BIGINT}, #{item.materialId,jdbcType=BIGINT}, #{item.shopId,jdbcType=BIGINT}, 
        #{item.misId,jdbcType=VARCHAR}, #{item.distributorCode,jdbcType=VARCHAR}, #{item.landingUrl,jdbcType=VARCHAR}, 
        #{item.qrCode,jdbcType=VARCHAR}, #{item.distributorPic,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.pointId,jdbcType=BIGINT}, #{item.scopeType,jdbcType=INTEGER}, 
        #{item.scopeId,jdbcType=BIGINT}, #{item.extInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>