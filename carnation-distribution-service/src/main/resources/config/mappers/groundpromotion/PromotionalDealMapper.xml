<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.PromotionalDealMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deal_id" jdbcType="BIGINT" property="dealId" />
    <result column="promotional_type" jdbcType="VARCHAR" property="promotionalType" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="merchants_id" jdbcType="BIGINT" property="merchantsId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="bind_misid" jdbcType="VARCHAR" property="bindMisid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ground_promotional_id" jdbcType="BIGINT" property="groundPromotionalId" />
    <result column="point_id" jdbcType="BIGINT" property="pointId" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, deal_id, promotional_type, shop_id, merchants_id, status, bind_misid, create_time, 
    update_time, ground_promotional_id, point_id, ext_info, product_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDealExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from promotional_deal
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from promotional_deal
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from promotional_deal
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDealExample">
    delete from promotional_deal
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal" useGeneratedKeys="true">
    insert into promotional_deal (deal_id, promotional_type, shop_id, 
      merchants_id, status, bind_misid, 
      create_time, update_time, ground_promotional_id, 
      point_id, ext_info, product_type
      )
    values (#{dealId,jdbcType=BIGINT}, #{promotionalType,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, 
      #{merchantsId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{bindMisid,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{groundPromotionalId,jdbcType=BIGINT}, 
      #{pointId,jdbcType=BIGINT}, #{extInfo,jdbcType=VARCHAR}, #{productType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal" useGeneratedKeys="true">
    insert into promotional_deal
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dealId != null">
        deal_id,
      </if>
      <if test="promotionalType != null">
        promotional_type,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="merchantsId != null">
        merchants_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="bindMisid != null">
        bind_misid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="groundPromotionalId != null">
        ground_promotional_id,
      </if>
      <if test="pointId != null">
        point_id,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="productType != null">
        product_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dealId != null">
        #{dealId,jdbcType=BIGINT},
      </if>
      <if test="promotionalType != null">
        #{promotionalType,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="merchantsId != null">
        #{merchantsId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="bindMisid != null">
        #{bindMisid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groundPromotionalId != null">
        #{groundPromotionalId,jdbcType=BIGINT},
      </if>
      <if test="pointId != null">
        #{pointId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDealExample" resultType="java.lang.Long">
    select count(*) from promotional_deal
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update promotional_deal
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dealId != null">
        deal_id = #{record.dealId,jdbcType=BIGINT},
      </if>
      <if test="record.promotionalType != null">
        promotional_type = #{record.promotionalType,jdbcType=VARCHAR},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantsId != null">
        merchants_id = #{record.merchantsId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.bindMisid != null">
        bind_misid = #{record.bindMisid,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groundPromotionalId != null">
        ground_promotional_id = #{record.groundPromotionalId,jdbcType=BIGINT},
      </if>
      <if test="record.pointId != null">
        point_id = #{record.pointId,jdbcType=BIGINT},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update promotional_deal
    set id = #{record.id,jdbcType=BIGINT},
      deal_id = #{record.dealId,jdbcType=BIGINT},
      promotional_type = #{record.promotionalType,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      merchants_id = #{record.merchantsId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      bind_misid = #{record.bindMisid,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      ground_promotional_id = #{record.groundPromotionalId,jdbcType=BIGINT},
      point_id = #{record.pointId,jdbcType=BIGINT},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal">
    update promotional_deal
    <set>
      <if test="dealId != null">
        deal_id = #{dealId,jdbcType=BIGINT},
      </if>
      <if test="promotionalType != null">
        promotional_type = #{promotionalType,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="merchantsId != null">
        merchants_id = #{merchantsId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="bindMisid != null">
        bind_misid = #{bindMisid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groundPromotionalId != null">
        ground_promotional_id = #{groundPromotionalId,jdbcType=BIGINT},
      </if>
      <if test="pointId != null">
        point_id = #{pointId,jdbcType=BIGINT},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.PromotionalDeal">
    update promotional_deal
    set deal_id = #{dealId,jdbcType=BIGINT},
      promotional_type = #{promotionalType,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      merchants_id = #{merchantsId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      bind_misid = #{bindMisid,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      ground_promotional_id = #{groundPromotionalId,jdbcType=BIGINT},
      point_id = #{pointId,jdbcType=BIGINT},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into promotional_deal
    (deal_id, promotional_type, shop_id, merchants_id, status, bind_misid, create_time, 
      update_time, ground_promotional_id, point_id, ext_info, product_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dealId,jdbcType=BIGINT}, #{item.promotionalType,jdbcType=VARCHAR}, #{item.shopId,jdbcType=BIGINT}, 
        #{item.merchantsId,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, #{item.bindMisid,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.groundPromotionalId,jdbcType=BIGINT}, 
        #{item.pointId,jdbcType=BIGINT}, #{item.extInfo,jdbcType=VARCHAR}, #{item.productType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <update id="updateBindMisIdWithOptimisticLock" parameterType="map">
    update promotional_deal
    set
      bind_misid = #{targetBindMisId,jdbcType=VARCHAR},
      status     = #{status}
    where id = #{id,jdbcType=BIGINT}
      AND bind_misid= #{expectedBindMisId,jdbcType=VARCHAR}
  </update>
  <update id="batchUpdateBindMisIdAndStatus" parameterType="map">
    update promotional_deal
    set bind_misid = #{bindMisId,jdbcType=VARCHAR},
    status     = #{status}
    where id in
    <foreach close=")" collection="ids" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>
</mapper>