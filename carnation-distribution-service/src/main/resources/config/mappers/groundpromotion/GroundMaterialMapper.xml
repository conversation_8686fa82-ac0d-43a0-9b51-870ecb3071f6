<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundMaterialMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ground_promotion_id" jdbcType="BIGINT" property="groundPromotionId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="is_store_name" jdbcType="TINYINT" property="isStoreName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="is_misId" jdbcType="TINYINT" property="isMisId" />
    <result column="store_x" jdbcType="DECIMAL" property="storeX" />
    <result column="store_y" jdbcType="DECIMAL" property="storeY" />
    <result column="qr_x" jdbcType="DECIMAL" property="qrX" />
    <result column="qr_y" jdbcType="DECIMAL" property="qrY" />
    <result column="qr_width" jdbcType="DECIMAL" property="qrWidth" />
    <result column="qr_height" jdbcType="DECIMAL" property="qrHeight" />
    <result column="picture_path" jdbcType="VARCHAR" property="picturePath" />
    <result column="type_face_size" jdbcType="BIGINT" property="typeFaceSize" />
    <result column="type_face_color" jdbcType="VARCHAR" property="typeFaceColor" />
    <result column="mis_font_size" jdbcType="BIGINT" property="misFontSize" />
    <result column="mis_font_color" jdbcType="VARCHAR" property="misFontColor" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ground_promotion_id, material_name, is_store_name, store_name, is_misId, store_x, 
    store_y, qr_x, qr_y, qr_width, qr_height, picture_path, type_face_size, type_face_color, 
    mis_font_size, mis_font_color, create_time, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterialExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_material
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_material
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterialExample">
    delete from ground_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial" useGeneratedKeys="true">
    insert into ground_material (ground_promotion_id, material_name, is_store_name, 
      store_name, is_misId, store_x, 
      store_y, qr_x, qr_y, qr_width, 
      qr_height, picture_path, type_face_size, 
      type_face_color, mis_font_size, mis_font_color, 
      create_time, update_time, status
      )
    values (#{groundPromotionId,jdbcType=BIGINT}, #{materialName,jdbcType=VARCHAR}, #{isStoreName,jdbcType=TINYINT}, 
      #{storeName,jdbcType=VARCHAR}, #{isMisId,jdbcType=TINYINT}, #{storeX,jdbcType=DECIMAL},
      #{storeY,jdbcType=DECIMAL}, #{qrX,jdbcType=DECIMAL}, #{qrY,jdbcType=DECIMAL}, #{qrWidth,jdbcType=DECIMAL}, 
      #{qrHeight,jdbcType=DECIMAL}, #{picturePath,jdbcType=VARCHAR}, #{typeFaceSize,jdbcType=BIGINT}, 
      #{typeFaceColor,jdbcType=VARCHAR}, #{misFontSize,jdbcType=BIGINT}, #{misFontColor,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial" useGeneratedKeys="true">
    insert into ground_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        ground_promotion_id,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="isStoreName != null">
        is_store_name,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="isMisId != null">
        is_misId,
      </if>
      <if test="storeX != null">
        store_x,
      </if>
      <if test="storeY != null">
        store_y,
      </if>
      <if test="qrX != null">
        qr_x,
      </if>
      <if test="qrY != null">
        qr_y,
      </if>
      <if test="qrWidth != null">
        qr_width,
      </if>
      <if test="qrHeight != null">
        qr_height,
      </if>
      <if test="picturePath != null">
        picture_path,
      </if>
      <if test="typeFaceSize != null">
        type_face_size,
      </if>
      <if test="typeFaceColor != null">
        type_face_color,
      </if>
      <if test="misFontSize != null">
        mis_font_size,
      </if>
      <if test="misFontColor != null">
        mis_font_color,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groundPromotionId != null">
        #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="isStoreName != null">
        #{isStoreName,jdbcType=TINYINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="isMisId != null">
        #{isMisId,jdbcType=TINYINT},
      </if>
      <if test="storeX != null">
        #{storeX,jdbcType=DECIMAL},
      </if>
      <if test="storeY != null">
        #{storeY,jdbcType=DECIMAL},
      </if>
      <if test="qrX != null">
        #{qrX,jdbcType=DECIMAL},
      </if>
      <if test="qrY != null">
        #{qrY,jdbcType=DECIMAL},
      </if>
      <if test="qrWidth != null">
        #{qrWidth,jdbcType=DECIMAL},
      </if>
      <if test="qrHeight != null">
        #{qrHeight,jdbcType=DECIMAL},
      </if>
      <if test="picturePath != null">
        #{picturePath,jdbcType=VARCHAR},
      </if>
      <if test="typeFaceSize != null">
        #{typeFaceSize,jdbcType=BIGINT},
      </if>
      <if test="typeFaceColor != null">
        #{typeFaceColor,jdbcType=VARCHAR},
      </if>
      <if test="misFontSize != null">
        #{misFontSize,jdbcType=BIGINT},
      </if>
      <if test="misFontColor != null">
        #{misFontColor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterialExample" resultType="java.lang.Long">
    select count(*) from ground_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groundPromotionId != null">
        ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="record.materialName != null">
        material_name = #{record.materialName,jdbcType=VARCHAR},
      </if>
      <if test="record.isStoreName != null">
        is_store_name = #{record.isStoreName,jdbcType=TINYINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.isMisId != null">
        is_misId = #{record.isMisId,jdbcType=TINYINT},
      </if>
      <if test="record.storeX != null">
        store_x = #{record.storeX,jdbcType=DECIMAL},
      </if>
      <if test="record.storeY != null">
        store_y = #{record.storeY,jdbcType=DECIMAL},
      </if>
      <if test="record.qrX != null">
        qr_x = #{record.qrX,jdbcType=DECIMAL},
      </if>
      <if test="record.qrY != null">
        qr_y = #{record.qrY,jdbcType=DECIMAL},
      </if>
      <if test="record.qrWidth != null">
        qr_width = #{record.qrWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.qrHeight != null">
        qr_height = #{record.qrHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.picturePath != null">
        picture_path = #{record.picturePath,jdbcType=VARCHAR},
      </if>
      <if test="record.typeFaceSize != null">
        type_face_size = #{record.typeFaceSize,jdbcType=BIGINT},
      </if>
      <if test="record.typeFaceColor != null">
        type_face_color = #{record.typeFaceColor,jdbcType=VARCHAR},
      </if>
      <if test="record.misFontSize != null">
        mis_font_size = #{record.misFontSize,jdbcType=BIGINT},
      </if>
      <if test="record.misFontColor != null">
        mis_font_color = #{record.misFontColor,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_material
    set id = #{record.id,jdbcType=BIGINT},
      ground_promotion_id = #{record.groundPromotionId,jdbcType=BIGINT},
      material_name = #{record.materialName,jdbcType=VARCHAR},
      is_store_name = #{record.isStoreName,jdbcType=TINYINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      is_misId = #{record.isMisId,jdbcType=TINYINT},
      store_x = #{record.storeX,jdbcType=DECIMAL},
      store_y = #{record.storeY,jdbcType=DECIMAL},
      qr_x = #{record.qrX,jdbcType=DECIMAL},
      qr_y = #{record.qrY,jdbcType=DECIMAL},
      qr_width = #{record.qrWidth,jdbcType=DECIMAL},
      qr_height = #{record.qrHeight,jdbcType=DECIMAL},
      picture_path = #{record.picturePath,jdbcType=VARCHAR},
      type_face_size = #{record.typeFaceSize,jdbcType=BIGINT},
      type_face_color = #{record.typeFaceColor,jdbcType=VARCHAR},
      mis_font_size = #{record.misFontSize,jdbcType=BIGINT},
      mis_font_color = #{record.misFontColor,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial">
    update ground_material
    <set>
      <if test="groundPromotionId != null">
        ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="isStoreName != null">
        is_store_name = #{isStoreName,jdbcType=TINYINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="isMisId != null">
        is_misId = #{isMisId,jdbcType=TINYINT},
      </if>
      <if test="storeX != null">
        store_x = #{storeX,jdbcType=DECIMAL},
      </if>
      <if test="storeY != null">
        store_y = #{storeY,jdbcType=DECIMAL},
      </if>
      <if test="qrX != null">
        qr_x = #{qrX,jdbcType=DECIMAL},
      </if>
      <if test="qrY != null">
        qr_y = #{qrY,jdbcType=DECIMAL},
      </if>
      <if test="qrWidth != null">
        qr_width = #{qrWidth,jdbcType=DECIMAL},
      </if>
      <if test="qrHeight != null">
        qr_height = #{qrHeight,jdbcType=DECIMAL},
      </if>
      <if test="picturePath != null">
        picture_path = #{picturePath,jdbcType=VARCHAR},
      </if>
      <if test="typeFaceSize != null">
        type_face_size = #{typeFaceSize,jdbcType=BIGINT},
      </if>
      <if test="typeFaceColor != null">
        type_face_color = #{typeFaceColor,jdbcType=VARCHAR},
      </if>
      <if test="misFontSize != null">
        mis_font_size = #{misFontSize,jdbcType=BIGINT},
      </if>
      <if test="misFontColor != null">
        mis_font_color = #{misFontColor,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundMaterial">
    update ground_material
    set ground_promotion_id = #{groundPromotionId,jdbcType=BIGINT},
      material_name = #{materialName,jdbcType=VARCHAR},
      is_store_name = #{isStoreName,jdbcType=TINYINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      is_misId = #{isMisId,jdbcType=TINYINT},
      store_x = #{storeX,jdbcType=DECIMAL},
      store_y = #{storeY,jdbcType=DECIMAL},
      qr_x = #{qrX,jdbcType=DECIMAL},
      qr_y = #{qrY,jdbcType=DECIMAL},
      qr_width = #{qrWidth,jdbcType=DECIMAL},
      qr_height = #{qrHeight,jdbcType=DECIMAL},
      picture_path = #{picturePath,jdbcType=VARCHAR},
      type_face_size = #{typeFaceSize,jdbcType=BIGINT},
      type_face_color = #{typeFaceColor,jdbcType=VARCHAR},
      mis_font_size = #{misFontSize,jdbcType=BIGINT},
      mis_font_color = #{misFontColor,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_material
    (ground_promotion_id, material_name, is_store_name, store_name, is_misId, store_x, 
      store_y, qr_x, qr_y, qr_width, qr_height, picture_path, type_face_size, type_face_color, 
      mis_font_size, mis_font_color, create_time, update_time, status)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groundPromotionId,jdbcType=BIGINT}, #{item.materialName,jdbcType=VARCHAR}, 
        #{item.isStoreName,jdbcType=TINYINT}, #{item.storeName,jdbcType=VARCHAR}, #{item.isMisId,jdbcType=TINYINT},
        #{item.storeX,jdbcType=DECIMAL}, #{item.storeY,jdbcType=DECIMAL}, #{item.qrX,jdbcType=DECIMAL}, 
        #{item.qrY,jdbcType=DECIMAL}, #{item.qrWidth,jdbcType=DECIMAL}, #{item.qrHeight,jdbcType=DECIMAL}, 
        #{item.picturePath,jdbcType=VARCHAR}, #{item.typeFaceSize,jdbcType=BIGINT}, #{item.typeFaceColor,jdbcType=VARCHAR}, 
        #{item.misFontSize,jdbcType=BIGINT}, #{item.misFontColor,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>