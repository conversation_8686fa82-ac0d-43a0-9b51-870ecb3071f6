<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.AttractActivityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="mis_id" jdbcType="BIGINT" property="misId" />
    <result column="attract_id" jdbcType="BIGINT" property="attractId" />
    <result column="attract_start_time" jdbcType="TIMESTAMP" property="attractStartTime" />
    <result column="attract_end_time" jdbcType="TIMESTAMP" property="attractEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, activity_id, project_name, mis_id, attract_id, attract_start_time, attract_end_time, 
    create_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from attract_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from attract_activity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from attract_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivityExample">
    delete from attract_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into attract_activity (activity_id, project_name, mis_id, 
      attract_id, attract_start_time, attract_end_time, 
      create_time)
    values (#{activityId,jdbcType=BIGINT}, #{projectName,jdbcType=VARCHAR}, #{misId,jdbcType=BIGINT}, 
      #{attractId,jdbcType=BIGINT}, #{attractStartTime,jdbcType=TIMESTAMP}, #{attractEndTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into attract_activity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="misId != null">
        mis_id,
      </if>
      <if test="attractId != null">
        attract_id,
      </if>
      <if test="attractStartTime != null">
        attract_start_time,
      </if>
      <if test="attractEndTime != null">
        attract_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="activityId != null">
        #{activityId,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="misId != null">
        #{misId,jdbcType=BIGINT},
      </if>
      <if test="attractId != null">
        #{attractId,jdbcType=BIGINT},
      </if>
      <if test="attractStartTime != null">
        #{attractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attractEndTime != null">
        #{attractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivityExample" resultType="java.lang.Long">
    select count(*) from attract_activity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update attract_activity
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=BIGINT},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.misId != null">
        mis_id = #{record.misId,jdbcType=BIGINT},
      </if>
      <if test="record.attractId != null">
        attract_id = #{record.attractId,jdbcType=BIGINT},
      </if>
      <if test="record.attractStartTime != null">
        attract_start_time = #{record.attractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.attractEndTime != null">
        attract_end_time = #{record.attractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update attract_activity
    set id = #{record.id,jdbcType=BIGINT},
      activity_id = #{record.activityId,jdbcType=BIGINT},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      mis_id = #{record.misId,jdbcType=BIGINT},
      attract_id = #{record.attractId,jdbcType=BIGINT},
      attract_start_time = #{record.attractStartTime,jdbcType=TIMESTAMP},
      attract_end_time = #{record.attractEndTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivity">
    update attract_activity
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="misId != null">
        mis_id = #{misId,jdbcType=BIGINT},
      </if>
      <if test="attractId != null">
        attract_id = #{attractId,jdbcType=BIGINT},
      </if>
      <if test="attractStartTime != null">
        attract_start_time = #{attractStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attractEndTime != null">
        attract_end_time = #{attractEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AttractActivity">
    update attract_activity
    set activity_id = #{activityId,jdbcType=BIGINT},
      project_name = #{projectName,jdbcType=VARCHAR},
      mis_id = #{misId,jdbcType=BIGINT},
      attract_id = #{attractId,jdbcType=BIGINT},
      attract_start_time = #{attractStartTime,jdbcType=TIMESTAMP},
      attract_end_time = #{attractEndTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into attract_activity
    (activity_id, project_name, mis_id, attract_id, attract_start_time, attract_end_time, 
      create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.activityId,jdbcType=BIGINT}, #{item.projectName,jdbcType=VARCHAR}, #{item.misId,jdbcType=BIGINT}, 
        #{item.attractId,jdbcType=BIGINT}, #{item.attractStartTime,jdbcType=TIMESTAMP}, #{item.attractEndTime,jdbcType=TIMESTAMP},
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>