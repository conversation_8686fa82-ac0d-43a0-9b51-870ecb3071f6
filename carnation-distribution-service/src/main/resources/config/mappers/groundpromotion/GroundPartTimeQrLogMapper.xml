<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPartTimeQrLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="cooperate_type" jdbcType="INTEGER" property="cooperateType" />
    <result column="shelf_id" jdbcType="BIGINT" property="shelfId" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="scan_time" jdbcType="TIMESTAMP" property="scanTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="shelf_url" jdbcType="VARCHAR" property="shelfUrl" />
    <result column="user_type_tags" jdbcType="VARCHAR" property="userTypeTags" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, biz_type, cooperate_type, shelf_id, poi_id, user_id, platform, scan_time, 
    create_time, update_time, shelf_url, user_type_tags
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_part_time_qr_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_part_time_qr_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_part_time_qr_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLogExample">
    delete from ground_part_time_qr_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog" useGeneratedKeys="true">
    insert into ground_part_time_qr_log (code, biz_type, cooperate_type, 
      shelf_id, poi_id, user_id, 
      platform, scan_time, create_time, 
      update_time, shelf_url, user_type_tags
      )
    values (#{code,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, #{cooperateType,jdbcType=INTEGER}, 
      #{shelfId,jdbcType=BIGINT}, #{poiId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, 
      #{platform,jdbcType=INTEGER}, #{scanTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{shelfUrl,jdbcType=VARCHAR}, #{userTypeTags,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog" useGeneratedKeys="true">
    insert into ground_part_time_qr_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="cooperateType != null">
        cooperate_type,
      </if>
      <if test="shelfId != null">
        shelf_id,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="scanTime != null">
        scan_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="shelfUrl != null">
        shelf_url,
      </if>
      <if test="userTypeTags != null">
        user_type_tags,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="cooperateType != null">
        #{cooperateType,jdbcType=INTEGER},
      </if>
      <if test="shelfId != null">
        #{shelfId,jdbcType=BIGINT},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="scanTime != null">
        #{scanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shelfUrl != null">
        #{shelfUrl,jdbcType=VARCHAR},
      </if>
      <if test="userTypeTags != null">
        #{userTypeTags,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLogExample" resultType="java.lang.Long">
    select count(*) from ground_part_time_qr_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_part_time_qr_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperateType != null">
        cooperate_type = #{record.cooperateType,jdbcType=INTEGER},
      </if>
      <if test="record.shelfId != null">
        shelf_id = #{record.shelfId,jdbcType=BIGINT},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.scanTime != null">
        scan_time = #{record.scanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shelfUrl != null">
        shelf_url = #{record.shelfUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.userTypeTags != null">
        user_type_tags = #{record.userTypeTags,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_part_time_qr_log
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      cooperate_type = #{record.cooperateType,jdbcType=INTEGER},
      shelf_id = #{record.shelfId,jdbcType=BIGINT},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      platform = #{record.platform,jdbcType=INTEGER},
      scan_time = #{record.scanTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      shelf_url = #{record.shelfUrl,jdbcType=VARCHAR},
      user_type_tags = #{record.userTypeTags,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog">
    update ground_part_time_qr_log
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="cooperateType != null">
        cooperate_type = #{cooperateType,jdbcType=INTEGER},
      </if>
      <if test="shelfId != null">
        shelf_id = #{shelfId,jdbcType=BIGINT},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="scanTime != null">
        scan_time = #{scanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shelfUrl != null">
        shelf_url = #{shelfUrl,jdbcType=VARCHAR},
      </if>
      <if test="userTypeTags != null">
        user_type_tags = #{userTypeTags,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPartTimeQrLog">
    update ground_part_time_qr_log
    set code = #{code,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      cooperate_type = #{cooperateType,jdbcType=INTEGER},
      shelf_id = #{shelfId,jdbcType=BIGINT},
      poi_id = #{poiId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      scan_time = #{scanTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      shelf_url = #{shelfUrl,jdbcType=VARCHAR},
      user_type_tags = #{userTypeTags,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_part_time_qr_log
    (code, biz_type, cooperate_type, shelf_id, poi_id, user_id, platform, scan_time, 
      create_time, update_time, shelf_url, user_type_tags)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.code,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, #{item.cooperateType,jdbcType=INTEGER}, 
        #{item.shelfId,jdbcType=BIGINT}, #{item.poiId,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT}, 
        #{item.platform,jdbcType=INTEGER}, #{item.scanTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.shelfUrl,jdbcType=VARCHAR}, #{item.userTypeTags,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>