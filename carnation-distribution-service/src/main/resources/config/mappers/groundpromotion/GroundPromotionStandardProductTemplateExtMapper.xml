<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionStandardProductTemplateExtMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionStandardProductTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, template_id, city_id, product_id, status, add_time, update_time
  </sql>

  <update id="updateStatusByTemplateId" parameterType="map">
    UPDATE
    ground_promotion_standard_product_template
    SET
    status = #{status,jdbcType=INTEGER}
    WHERE
    template_id = #{templateId,jdbcType=BIGINT}
  </update>


</mapper>