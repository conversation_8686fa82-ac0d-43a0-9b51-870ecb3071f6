<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.LiveUserIntentionResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="live_id" jdbcType="BIGINT" property="liveId" />
    <result column="anchor_id" jdbcType="BIGINT" property="anchorId" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_platform" jdbcType="INTEGER" property="userPlatform" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="params" jdbcType="VARCHAR" property="params" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, live_id, anchor_id, event_type, user_id, user_platform, event_time, channel, 
    biz_code, biz_type, create_time, update_time, params
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from live_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from live_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from live_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResultExample">
    delete from live_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResult" useGeneratedKeys="true">
    insert into live_user_intention_result (live_id, anchor_id, event_type, 
      user_id, user_platform, event_time, 
      channel, biz_code, biz_type, 
      create_time, update_time, params
      )
    values (#{liveId,jdbcType=BIGINT}, #{anchorId,jdbcType=BIGINT}, #{eventType,jdbcType=INTEGER}, 
      #{userId,jdbcType=BIGINT}, #{userPlatform,jdbcType=INTEGER}, #{eventTime,jdbcType=TIMESTAMP}, 
      #{channel,jdbcType=INTEGER}, #{bizCode,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{params,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResult" useGeneratedKeys="true">
    insert into live_user_intention_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="liveId != null">
        live_id,
      </if>
      <if test="anchorId != null">
        anchor_id,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userPlatform != null">
        user_platform,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="params != null">
        params,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="liveId != null">
        #{liveId,jdbcType=BIGINT},
      </if>
      <if test="anchorId != null">
        #{anchorId,jdbcType=BIGINT},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params != null">
        #{params,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResultExample" resultType="java.lang.Long">
    select count(*) from live_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update live_user_intention_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.liveId != null">
        live_id = #{record.liveId,jdbcType=BIGINT},
      </if>
      <if test="record.anchorId != null">
        anchor_id = #{record.anchorId,jdbcType=BIGINT},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userPlatform != null">
        user_platform = #{record.userPlatform,jdbcType=INTEGER},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.params != null">
        params = #{record.params,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update live_user_intention_result
    set id = #{record.id,jdbcType=BIGINT},
      live_id = #{record.liveId,jdbcType=BIGINT},
      anchor_id = #{record.anchorId,jdbcType=BIGINT},
      event_type = #{record.eventType,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_platform = #{record.userPlatform,jdbcType=INTEGER},
      event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      channel = #{record.channel,jdbcType=INTEGER},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      params = #{record.params,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResult">
    update live_user_intention_result
    <set>
      <if test="liveId != null">
        live_id = #{liveId,jdbcType=BIGINT},
      </if>
      <if test="anchorId != null">
        anchor_id = #{anchorId,jdbcType=BIGINT},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        user_platform = #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params != null">
        params = #{params,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.LiveUserIntentionResult">
    update live_user_intention_result
    set live_id = #{liveId,jdbcType=BIGINT},
      anchor_id = #{anchorId,jdbcType=BIGINT},
      event_type = #{eventType,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_platform = #{userPlatform,jdbcType=INTEGER},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      channel = #{channel,jdbcType=INTEGER},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      params = #{params,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into live_user_intention_result
    (live_id, anchor_id, event_type, user_id, user_platform, event_time, channel, biz_code, 
      biz_type, create_time, update_time, params)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.liveId,jdbcType=BIGINT}, #{item.anchorId,jdbcType=BIGINT}, #{item.eventType,jdbcType=INTEGER}, 
        #{item.userId,jdbcType=BIGINT}, #{item.userPlatform,jdbcType=INTEGER}, #{item.eventTime,jdbcType=TIMESTAMP}, 
        #{item.channel,jdbcType=INTEGER}, #{item.bizCode,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.params,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>