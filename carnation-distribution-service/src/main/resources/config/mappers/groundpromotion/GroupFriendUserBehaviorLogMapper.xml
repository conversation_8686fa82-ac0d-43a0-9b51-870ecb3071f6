<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroupFriendUserBehaviorLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="wechat_user_id" jdbcType="VARCHAR" property="wechatUserId" />
    <result column="channel_code_id" jdbcType="BIGINT" property="channelCodeId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, corp_id, user_id, event_type, event_time, wechat_user_id, channel_code_id, add_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from group_friend_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from group_friend_user_behavior_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from group_friend_user_behavior_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLogExample">
    delete from group_friend_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLog" useGeneratedKeys="true">
    insert into group_friend_user_behavior_log (corp_id, user_id, event_type, 
      event_time, wechat_user_id, channel_code_id, 
      add_time, update_time)
    values (#{corpId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{eventType,jdbcType=INTEGER}, 
      #{eventTime,jdbcType=TIMESTAMP}, #{wechatUserId,jdbcType=VARCHAR}, #{channelCodeId,jdbcType=BIGINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLog" useGeneratedKeys="true">
    insert into group_friend_user_behavior_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="wechatUserId != null">
        wechat_user_id,
      </if>
      <if test="channelCodeId != null">
        channel_code_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="wechatUserId != null">
        #{wechatUserId,jdbcType=VARCHAR},
      </if>
      <if test="channelCodeId != null">
        #{channelCodeId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLogExample" resultType="java.lang.Long">
    select count(*) from group_friend_user_behavior_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update group_friend_user_behavior_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.corpId != null">
        corp_id = #{record.corpId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=INTEGER},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.wechatUserId != null">
        wechat_user_id = #{record.wechatUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.channelCodeId != null">
        channel_code_id = #{record.channelCodeId,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update group_friend_user_behavior_log
    set id = #{record.id,jdbcType=BIGINT},
      corp_id = #{record.corpId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      event_type = #{record.eventType,jdbcType=INTEGER},
      event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      wechat_user_id = #{record.wechatUserId,jdbcType=VARCHAR},
      channel_code_id = #{record.channelCodeId,jdbcType=BIGINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLog">
    update group_friend_user_behavior_log
    <set>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="wechatUserId != null">
        wechat_user_id = #{wechatUserId,jdbcType=VARCHAR},
      </if>
      <if test="channelCodeId != null">
        channel_code_id = #{channelCodeId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupFriendUserBehaviorLog">
    update group_friend_user_behavior_log
    set corp_id = #{corpId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      event_type = #{eventType,jdbcType=INTEGER},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      wechat_user_id = #{wechatUserId,jdbcType=VARCHAR},
      channel_code_id = #{channelCodeId,jdbcType=BIGINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into group_friend_user_behavior_log
    (corp_id, user_id, event_type, event_time, wechat_user_id, channel_code_id, add_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.corpId,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, #{item.eventType,jdbcType=INTEGER}, 
        #{item.eventTime,jdbcType=TIMESTAMP}, #{item.wechatUserId,jdbcType=VARCHAR}, #{item.channelCodeId,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>