<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.AppNewUserIntentionResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="long_order_id" jdbcType="BIGINT" property="longOrderId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, long_order_id, user_id, platform, distributor_code, channel, biz_code, 
    biz_type, params, create_time, update_time, add_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_new_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_new_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_new_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResultExample">
    delete from app_new_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResult" useGeneratedKeys="true">
    insert into app_new_user_intention_result (order_id, long_order_id, user_id, 
      platform, distributor_code, channel, 
      biz_code, biz_type, params, 
      create_time, update_time, add_time
      )
    values (#{orderId,jdbcType=VARCHAR}, #{longOrderId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, 
      #{platform,jdbcType=INTEGER}, #{distributorCode,jdbcType=VARCHAR}, #{channel,jdbcType=INTEGER}, 
      #{bizCode,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, #{params,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResult" useGeneratedKeys="true">
    insert into app_new_user_intention_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="longOrderId != null">
        long_order_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="params != null">
        params,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        #{params,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResultExample" resultType="java.lang.Long">
    select count(*) from app_new_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_new_user_intention_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.longOrderId != null">
        long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.params != null">
        params = #{record.params,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_new_user_intention_result
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      platform = #{record.platform,jdbcType=INTEGER},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=INTEGER},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      params = #{record.params,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResult">
    update app_new_user_intention_result
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        long_order_id = #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        params = #{params,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.AppNewUserIntentionResult">
    update app_new_user_intention_result
    set order_id = #{orderId,jdbcType=VARCHAR},
      long_order_id = #{longOrderId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      params = #{params,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into app_new_user_intention_result
    (order_id, long_order_id, user_id, platform, distributor_code, channel, biz_code, 
      biz_type, params, create_time, update_time, add_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=VARCHAR}, #{item.longOrderId,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT}, 
        #{item.platform,jdbcType=INTEGER}, #{item.distributorCode,jdbcType=VARCHAR}, #{item.channel,jdbcType=INTEGER}, 
        #{item.bizCode,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, #{item.params,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.addTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>