<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.DistributionOrderReceiptLogMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="long_order_id" jdbcType="BIGINT" property="longOrderId" />
    <result column="distributor_code" jdbcType="VARCHAR" property="distributorCode" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="ext_key" jdbcType="VARCHAR" property="extKey" />
    <result column="opt_type" jdbcType="INTEGER" property="optType" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="total_money" jdbcType="BIGINT" property="totalMoney" />
    <result column="total_use_money" jdbcType="BIGINT" property="totalUseMoney" />
    <result column="use_money" jdbcType="BIGINT" property="useMoney" />
    <result column="refund_money" jdbcType="BIGINT" property="refundMoney" />
    <result column="total_buy_num" jdbcType="INTEGER" property="totalBuyNum" />
    <result column="total_use_num" jdbcType="INTEGER" property="totalUseNum" />
    <result column="action_time" jdbcType="TIMESTAMP" property="actionTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="buy_magic_member_coupons" jdbcType="INTEGER" property="buyMagicMemberCoupons" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, long_order_id, distributor_code, order_type, biz_id, biz_type, ext_key, 
    opt_type, poi_id, user_id, platform, product_id, product_type, total_money, total_use_money, 
    use_money, refund_money, total_buy_num, total_use_num, action_time, add_time, pay_time, 
    create_time, update_time, buy_magic_member_coupons
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from distribution_order_receipt_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from distribution_order_receipt_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from distribution_order_receipt_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLogExample">
    delete from distribution_order_receipt_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLog" useGeneratedKeys="true">
    insert into distribution_order_receipt_log (order_id, long_order_id, distributor_code, 
      order_type, biz_id, biz_type, 
      ext_key, opt_type, poi_id, 
      user_id, platform, product_id, 
      product_type, total_money, total_use_money, 
      use_money, refund_money, total_buy_num, 
      total_use_num, action_time, add_time, 
      pay_time, create_time, update_time, 
      buy_magic_member_coupons)
    values (#{orderId,jdbcType=VARCHAR}, #{longOrderId,jdbcType=BIGINT}, #{distributorCode,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=INTEGER}, #{bizId,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, 
      #{extKey,jdbcType=VARCHAR}, #{optType,jdbcType=INTEGER}, #{poiId,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{platform,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, 
      #{productType,jdbcType=INTEGER}, #{totalMoney,jdbcType=BIGINT}, #{totalUseMoney,jdbcType=BIGINT}, 
      #{useMoney,jdbcType=BIGINT}, #{refundMoney,jdbcType=BIGINT}, #{totalBuyNum,jdbcType=INTEGER}, 
      #{totalUseNum,jdbcType=INTEGER}, #{actionTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}, 
      #{payTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{buyMagicMemberCoupons,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLog" useGeneratedKeys="true">
    insert into distribution_order_receipt_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="longOrderId != null">
        long_order_id,
      </if>
      <if test="distributorCode != null">
        distributor_code,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="extKey != null">
        ext_key,
      </if>
      <if test="optType != null">
        opt_type,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="totalMoney != null">
        total_money,
      </if>
      <if test="totalUseMoney != null">
        total_use_money,
      </if>
      <if test="useMoney != null">
        use_money,
      </if>
      <if test="refundMoney != null">
        refund_money,
      </if>
      <if test="totalBuyNum != null">
        total_buy_num,
      </if>
      <if test="totalUseNum != null">
        total_use_num,
      </if>
      <if test="actionTime != null">
        action_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="buyMagicMemberCoupons != null">
        buy_magic_member_coupons,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="distributorCode != null">
        #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="extKey != null">
        #{extKey,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        #{optType,jdbcType=INTEGER},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="totalMoney != null">
        #{totalMoney,jdbcType=BIGINT},
      </if>
      <if test="totalUseMoney != null">
        #{totalUseMoney,jdbcType=BIGINT},
      </if>
      <if test="useMoney != null">
        #{useMoney,jdbcType=BIGINT},
      </if>
      <if test="refundMoney != null">
        #{refundMoney,jdbcType=BIGINT},
      </if>
      <if test="totalBuyNum != null">
        #{totalBuyNum,jdbcType=INTEGER},
      </if>
      <if test="totalUseNum != null">
        #{totalUseNum,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyMagicMemberCoupons != null">
        #{buyMagicMemberCoupons,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLogExample" resultType="java.lang.Long">
    select count(*) from distribution_order_receipt_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update distribution_order_receipt_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.longOrderId != null">
        long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      </if>
      <if test="record.distributorCode != null">
        distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=INTEGER},
      </if>
      <if test="record.extKey != null">
        ext_key = #{record.extKey,jdbcType=VARCHAR},
      </if>
      <if test="record.optType != null">
        opt_type = #{record.optType,jdbcType=INTEGER},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.totalMoney != null">
        total_money = #{record.totalMoney,jdbcType=BIGINT},
      </if>
      <if test="record.totalUseMoney != null">
        total_use_money = #{record.totalUseMoney,jdbcType=BIGINT},
      </if>
      <if test="record.useMoney != null">
        use_money = #{record.useMoney,jdbcType=BIGINT},
      </if>
      <if test="record.refundMoney != null">
        refund_money = #{record.refundMoney,jdbcType=BIGINT},
      </if>
      <if test="record.totalBuyNum != null">
        total_buy_num = #{record.totalBuyNum,jdbcType=INTEGER},
      </if>
      <if test="record.totalUseNum != null">
        total_use_num = #{record.totalUseNum,jdbcType=INTEGER},
      </if>
      <if test="record.actionTime != null">
        action_time = #{record.actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.buyMagicMemberCoupons != null">
        buy_magic_member_coupons = #{record.buyMagicMemberCoupons,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update distribution_order_receipt_log
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      long_order_id = #{record.longOrderId,jdbcType=BIGINT},
      distributor_code = #{record.distributorCode,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=INTEGER},
      ext_key = #{record.extKey,jdbcType=VARCHAR},
      opt_type = #{record.optType,jdbcType=INTEGER},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      platform = #{record.platform,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=INTEGER},
      total_money = #{record.totalMoney,jdbcType=BIGINT},
      total_use_money = #{record.totalUseMoney,jdbcType=BIGINT},
      use_money = #{record.useMoney,jdbcType=BIGINT},
      refund_money = #{record.refundMoney,jdbcType=BIGINT},
      total_buy_num = #{record.totalBuyNum,jdbcType=INTEGER},
      total_use_num = #{record.totalUseNum,jdbcType=INTEGER},
      action_time = #{record.actionTime,jdbcType=TIMESTAMP},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      buy_magic_member_coupons = #{record.buyMagicMemberCoupons,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLog">
    update distribution_order_receipt_log
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="longOrderId != null">
        long_order_id = #{longOrderId,jdbcType=BIGINT},
      </if>
      <if test="distributorCode != null">
        distributor_code = #{distributorCode,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="extKey != null">
        ext_key = #{extKey,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        opt_type = #{optType,jdbcType=INTEGER},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="totalMoney != null">
        total_money = #{totalMoney,jdbcType=BIGINT},
      </if>
      <if test="totalUseMoney != null">
        total_use_money = #{totalUseMoney,jdbcType=BIGINT},
      </if>
      <if test="useMoney != null">
        use_money = #{useMoney,jdbcType=BIGINT},
      </if>
      <if test="refundMoney != null">
        refund_money = #{refundMoney,jdbcType=BIGINT},
      </if>
      <if test="totalBuyNum != null">
        total_buy_num = #{totalBuyNum,jdbcType=INTEGER},
      </if>
      <if test="totalUseNum != null">
        total_use_num = #{totalUseNum,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        action_time = #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyMagicMemberCoupons != null">
        buy_magic_member_coupons = #{buyMagicMemberCoupons,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.DistributionOrderReceiptLog">
    update distribution_order_receipt_log
    set order_id = #{orderId,jdbcType=VARCHAR},
      long_order_id = #{longOrderId,jdbcType=BIGINT},
      distributor_code = #{distributorCode,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      biz_id = #{bizId,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=INTEGER},
      ext_key = #{extKey,jdbcType=VARCHAR},
      opt_type = #{optType,jdbcType=INTEGER},
      poi_id = #{poiId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      product_type = #{productType,jdbcType=INTEGER},
      total_money = #{totalMoney,jdbcType=BIGINT},
      total_use_money = #{totalUseMoney,jdbcType=BIGINT},
      use_money = #{useMoney,jdbcType=BIGINT},
      refund_money = #{refundMoney,jdbcType=BIGINT},
      total_buy_num = #{totalBuyNum,jdbcType=INTEGER},
      total_use_num = #{totalUseNum,jdbcType=INTEGER},
      action_time = #{actionTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      buy_magic_member_coupons = #{buyMagicMemberCoupons,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into distribution_order_receipt_log
    (order_id, long_order_id, distributor_code, order_type, biz_id, biz_type, ext_key, 
      opt_type, poi_id, user_id, platform, product_id, product_type, total_money, total_use_money, 
      use_money, refund_money, total_buy_num, total_use_num, action_time, add_time, pay_time, 
      create_time, update_time, buy_magic_member_coupons)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=VARCHAR}, #{item.longOrderId,jdbcType=BIGINT}, #{item.distributorCode,jdbcType=VARCHAR}, 
        #{item.orderType,jdbcType=INTEGER}, #{item.bizId,jdbcType=VARCHAR}, #{item.bizType,jdbcType=INTEGER}, 
        #{item.extKey,jdbcType=VARCHAR}, #{item.optType,jdbcType=INTEGER}, #{item.poiId,jdbcType=BIGINT}, 
        #{item.userId,jdbcType=BIGINT}, #{item.platform,jdbcType=INTEGER}, #{item.productId,jdbcType=BIGINT}, 
        #{item.productType,jdbcType=INTEGER}, #{item.totalMoney,jdbcType=BIGINT}, #{item.totalUseMoney,jdbcType=BIGINT}, 
        #{item.useMoney,jdbcType=BIGINT}, #{item.refundMoney,jdbcType=BIGINT}, #{item.totalBuyNum,jdbcType=INTEGER}, 
        #{item.totalUseNum,jdbcType=INTEGER}, #{item.actionTime,jdbcType=TIMESTAMP}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.payTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.buyMagicMemberCoupons,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>