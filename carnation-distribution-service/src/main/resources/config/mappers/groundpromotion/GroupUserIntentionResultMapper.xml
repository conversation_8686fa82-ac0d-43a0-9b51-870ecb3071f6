<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroupUserIntentionResultMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_type" jdbcType="INTEGER" property="groupType" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_platform" jdbcType="INTEGER" property="userPlatform" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" />
    <result column="event_time" jdbcType="TIMESTAMP" property="eventTime" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, group_type, channel, user_id, user_platform, event_type, event_time, group_id, 
    biz_code, biz_type, params, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from group_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from group_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from group_user_intention_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResultExample">
    delete from group_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResult" useGeneratedKeys="true">
    insert into group_user_intention_result (group_type, channel, user_id, 
      user_platform, event_type, event_time, 
      group_id, biz_code, biz_type, 
      params, add_time, update_time
      )
    values (#{groupType,jdbcType=INTEGER}, #{channel,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, 
      #{userPlatform,jdbcType=INTEGER}, #{eventType,jdbcType=INTEGER}, #{eventTime,jdbcType=TIMESTAMP}, 
      #{groupId,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, 
      #{params,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResult" useGeneratedKeys="true">
    insert into group_user_intention_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupType != null">
        group_type,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userPlatform != null">
        user_platform,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="eventTime != null">
        event_time,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="params != null">
        params,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupType != null">
        #{groupType,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        #{params,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResultExample" resultType="java.lang.Long">
    select count(*) from group_user_intention_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update group_user_intention_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groupType != null">
        group_type = #{record.groupType,jdbcType=INTEGER},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userPlatform != null">
        user_platform = #{record.userPlatform,jdbcType=INTEGER},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=INTEGER},
      </if>
      <if test="record.eventTime != null">
        event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.params != null">
        params = #{record.params,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update group_user_intention_result
    set id = #{record.id,jdbcType=BIGINT},
      group_type = #{record.groupType,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_platform = #{record.userPlatform,jdbcType=INTEGER},
      event_type = #{record.eventType,jdbcType=INTEGER},
      event_time = #{record.eventTime,jdbcType=TIMESTAMP},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_type = #{record.bizType,jdbcType=VARCHAR},
      params = #{record.params,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResult">
    update group_user_intention_result
    <set>
      <if test="groupType != null">
        group_type = #{groupType,jdbcType=INTEGER},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userPlatform != null">
        user_platform = #{userPlatform,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventTime != null">
        event_time = #{eventTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        params = #{params,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroupUserIntentionResult">
    update group_user_intention_result
    set group_type = #{groupType,jdbcType=INTEGER},
      channel = #{channel,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_platform = #{userPlatform,jdbcType=INTEGER},
      event_type = #{eventType,jdbcType=INTEGER},
      event_time = #{eventTime,jdbcType=TIMESTAMP},
      group_id = #{groupId,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      params = #{params,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into group_user_intention_result
    (group_type, channel, user_id, user_platform, event_type, event_time, group_id, biz_code, 
      biz_type, params, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groupType,jdbcType=INTEGER}, #{item.channel,jdbcType=INTEGER}, #{item.userId,jdbcType=BIGINT}, 
        #{item.userPlatform,jdbcType=INTEGER}, #{item.eventType,jdbcType=INTEGER}, #{item.eventTime,jdbcType=TIMESTAMP}, 
        #{item.groupId,jdbcType=VARCHAR}, #{item.bizCode,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, 
        #{item.params,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>