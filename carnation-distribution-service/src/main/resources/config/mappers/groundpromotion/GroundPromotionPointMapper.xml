<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.groundpromotion.repository.dao.GroundPromotionPointMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="point_name" jdbcType="VARCHAR" property="pointName" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="lng" jdbcType="DECIMAL" property="lng" />
    <result column="lat" jdbcType="DECIMAL" property="lat" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="poi_id" jdbcType="BIGINT" property="poiId" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, point_name, shop_id, show_type, lng, lat, create_time, update_time, poi_id, status
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPointExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ground_promotion_point
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ground_promotion_point
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ground_promotion_point
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPointExample">
    delete from ground_promotion_point
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint" useGeneratedKeys="true">
    insert into ground_promotion_point (point_name, shop_id, show_type, 
      lng, lat, create_time, 
      update_time, poi_id, status
      )
    values (#{pointName,jdbcType=VARCHAR}, #{shopId,jdbcType=VARCHAR}, #{showType,jdbcType=INTEGER}, 
      #{lng,jdbcType=DECIMAL}, #{lat,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{poiId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint" useGeneratedKeys="true">
    insert into ground_promotion_point
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointName != null">
        point_name,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="showType != null">
        show_type,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointName != null">
        #{pointName,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="showType != null">
        #{showType,jdbcType=INTEGER},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DECIMAL},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPointExample" resultType="java.lang.Long">
    select count(*) from ground_promotion_point
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ground_promotion_point
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pointName != null">
        point_name = #{record.pointName,jdbcType=VARCHAR},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=VARCHAR},
      </if>
      <if test="record.showType != null">
        show_type = #{record.showType,jdbcType=INTEGER},
      </if>
      <if test="record.lng != null">
        lng = #{record.lng,jdbcType=DECIMAL},
      </if>
      <if test="record.lat != null">
        lat = #{record.lat,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ground_promotion_point
    set id = #{record.id,jdbcType=BIGINT},
      point_name = #{record.pointName,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=VARCHAR},
      show_type = #{record.showType,jdbcType=INTEGER},
      lng = #{record.lng,jdbcType=DECIMAL},
      lat = #{record.lat,jdbcType=DECIMAL},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      poi_id = #{record.poiId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint">
    update ground_promotion_point
    <set>
      <if test="pointName != null">
        point_name = #{pointName,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="showType != null">
        show_type = #{showType,jdbcType=INTEGER},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DECIMAL},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.groundpromotion.repository.dto.GroundPromotionPoint">
    update ground_promotion_point
    set point_name = #{pointName,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=VARCHAR},
      show_type = #{showType,jdbcType=INTEGER},
      lng = #{lng,jdbcType=DECIMAL},
      lat = #{lat,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      poi_id = #{poiId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ground_promotion_point
    (point_name, shop_id, show_type, lng, lat, create_time, update_time, poi_id, status
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.pointName,jdbcType=VARCHAR}, #{item.shopId,jdbcType=VARCHAR}, #{item.showType,jdbcType=INTEGER}, 
        #{item.lng,jdbcType=DECIMAL}, #{item.lat,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.poiId,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
</mapper>