<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.product.dao.DistributionProductInfoDao">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.product.bo.DistributionProductInfoDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="relate_product_id" jdbcType="BIGINT" property="relateProductId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="line_price" jdbcType="DOUBLE" property="linePrice" />
    <result column="price" jdbcType="DOUBLE" property="price" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_pic_list" jdbcType="VARCHAR" property="productPicList" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="basic_hot_value" jdbcType="INTEGER" property="basicHotValue" />
    <result column="hot_value" jdbcType="INTEGER" property="hotValue" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="commission_id" jdbcType="BIGINT" property="commissionId" />
    <result column="distribution_share_id" jdbcType="BIGINT" property="distributionShareId" />
    <result column="updater_mis" jdbcType="VARCHAR" property="updaterMis" />
    <result column="reviewer_mis" jdbcType="VARCHAR" property="reviewerMis" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, relate_product_id, product_type, line_price, price,product_name, product_pic_list,
    channel,basic_hot_value,hot_value,start_time,end_time,status,commission_id,distribution_share_id,
    updater_mis,reviewer_mis,add_time,update_time
  </sql>

  <insert id="insertProduct" parameterType="com.sankuai.carnation.distribution.product.bo.DistributionProductInfoDo" useGeneratedKeys="true" keyProperty="id">
    insert into
    distribution_product_info
    (<include refid="Base_Column_List" />)
    values (#{id,jdbcType=BIGINT}, #{relateProductId,jdbcType=BIGINT},
    #{productType,jdbcType=INTEGER},#{linePrice,jdbcType=DOUBLE},#{price,jdbcType=DOUBLE},
    #{productName,jdbcType=VARCHAR}, #{productPicList,jdbcType=VARCHAR},
    #{channel,jdbcType=INTEGER},#{basicHotValue,jdbcType=INTEGER},#{hotValue,jdbcType=INTEGER},
    #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},
    #{status,jdbcType=INTEGER},#{commissionId,jdbcType=BIGINT},
    #{distributionShareId,jdbcType=BIGINT},
    #{updaterMis,jdbcType=VARCHAR}, #{reviewerMis,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <select id="queryProduct" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_product_info
    where id = #{id,jdbcType=BIGINT} 
  </select>

  <select id="queryByRelateProductId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_product_info
    where relate_product_id = #{relateProductId,jdbcType=BIGINT}
    and product_type = #{productType,jdbcType=INTEGER} and
    status = #{status,jdbcType=INTEGER}
  </select>

  <select id="queryProductList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_product_info
    where status = #{status,jdbcType=INTEGER} and
    channel in
    <foreach collection="channelList" index="index" item="channel" open="(" separator="," close=")">
        #{channel,jdbcType=INTEGER}
    </foreach>
      and now() between start_time and end_time
    order by hot_value desc
    limit #{startNum,jdbcType=INTEGER},#{size,jdbcType=INTEGER}
  </select>

  <select id="queryProductListByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_product_info
    where  id in
    <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

  <update id="updateProductById" parameterType="com.sankuai.carnation.distribution.product.bo.DistributionProductInfoDo">
    update distribution_product_info
    <set>
    <if test="status != null">
      status = #{status,jdbcType=INTEGER},
    </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateHotValue" parameterType="com.sankuai.carnation.distribution.product.bo.DistributionProductInfoDo">
    update distribution_product_info
    <set>
        hot_value = #{hotValue,jdbcType=INTEGER},
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryProductListForQueryOrderSize" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_product_info
    where status = #{status,jdbcType=INTEGER}
    limit #{startNum,jdbcType=INTEGER},#{size,jdbcType=INTEGER}
  </select>

</mapper>