<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSortMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSort">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="sort_scene" jdbcType="VARCHAR" property="sortScene" />
    <result column="product_item_id" jdbcType="BIGINT" property="productItemId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_code, sort_scene, product_item_id, sort, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSortExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_item_sort
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_item_sort
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_item_sort
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSortExample">
    delete from product_item_sort
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSort" useGeneratedKeys="true">
    insert into product_item_sort (biz_code, sort_scene, product_item_id, 
      sort, status, add_time, 
      update_time)
    values (#{bizCode,jdbcType=VARCHAR}, #{sortScene,jdbcType=VARCHAR}, #{productItemId,jdbcType=BIGINT}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSort" useGeneratedKeys="true">
    insert into product_item_sort
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="sortScene != null">
        sort_scene,
      </if>
      <if test="productItemId != null">
        product_item_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="sortScene != null">
        #{sortScene,jdbcType=VARCHAR},
      </if>
      <if test="productItemId != null">
        #{productItemId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSortExample" resultType="java.lang.Long">
    select count(*) from product_item_sort
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update product_item_sort
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sortScene != null">
        sort_scene = #{record.sortScene,jdbcType=VARCHAR},
      </if>
      <if test="record.productItemId != null">
        product_item_id = #{record.productItemId,jdbcType=BIGINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update product_item_sort
    set id = #{record.id,jdbcType=BIGINT},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      sort_scene = #{record.sortScene,jdbcType=VARCHAR},
      product_item_id = #{record.productItemId,jdbcType=BIGINT},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSort">
    update product_item_sort
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="sortScene != null">
        sort_scene = #{sortScene,jdbcType=VARCHAR},
      </if>
      <if test="productItemId != null">
        product_item_id = #{productItemId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSort">
    update product_item_sort
    set biz_code = #{bizCode,jdbcType=VARCHAR},
      sort_scene = #{sortScene,jdbcType=VARCHAR},
      product_item_id = #{productItemId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>