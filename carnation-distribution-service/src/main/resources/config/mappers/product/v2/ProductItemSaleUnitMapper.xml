<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.product.v2.repository.dao.ProductItemSaleUnitMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_item_id" jdbcType="BIGINT" property="productItemId" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_item_id, product_type, product_id, sku_id, amount, status, add_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnitCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_item_sale_units
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from product_item_sale_units
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from product_item_sale_units
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnitCriteria">
    delete from product_item_sale_units
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_item_sale_units (product_item_id, product_type, product_id, 
      sku_id, amount, status, 
      add_time, update_time)
    values (#{productItemId,jdbcType=BIGINT}, #{productType,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, 
      #{skuId,jdbcType=BIGINT}, #{amount,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into product_item_sale_units
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productItemId != null">
        product_item_id,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productItemId != null">
        #{productItemId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnitCriteria" resultType="java.lang.Long">
    select count(*) from product_item_sale_units
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update product_item_sale_units
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productItemId != null">
        product_item_id = #{record.productItemId,jdbcType=BIGINT},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=BIGINT},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update product_item_sale_units
    set id = #{record.id,jdbcType=BIGINT},
      product_item_id = #{record.productItemId,jdbcType=BIGINT},
      product_type = #{record.productType,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=BIGINT},
      sku_id = #{record.skuId,jdbcType=BIGINT},
      amount = #{record.amount,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit">
    update product_item_sale_units
    <set>
      <if test="productItemId != null">
        product_item_id = #{productItemId,jdbcType=BIGINT},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.product.v2.repository.db.ProductItemSaleUnit">
    update product_item_sale_units
    set product_item_id = #{productItemId,jdbcType=BIGINT},
      product_type = #{productType,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>