<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.product.dao.FixCommissionStrategyDao">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.product.bo.FixCommissionStrategyDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="commission_value" jdbcType="DOUBLE" property="commissionValue" />
    <result column="shop_commission_value" jdbcType="DOUBLE" property="shopCommissionValue" />
    <result column="updater_mis" jdbcType="VARCHAR" property="updaterMis" />
    <result column="reviewer_mis" jdbcType="VARCHAR" property="reviewerMis" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, commission_value, shop_commission_value, updater_mis, reviewer_mis,add_time, update_time
  </sql>

  <insert id="insertStrategy" parameterType="com.sankuai.carnation.distribution.product.bo.FixCommissionStrategyDo" useGeneratedKeys="true" keyProperty="id">
    insert into
    distribution_fix_commission_strategy
    (<include refid="Base_Column_List" />)
    values (#{id,jdbcType=BIGINT}, #{commissionValue,jdbcType=DOUBLE},
    #{shopCommissionValue,jdbcType=DOUBLE},
        #{updaterMis,jdbcType=VARCHAR}, #{reviewerMis,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <select id="queryStrategy" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_fix_commission_strategy
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="queryStrategyList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_fix_commission_strategy
    where id in
    <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
    #{id,jdbcType=BIGINT}
  </foreach>
  </select>

</mapper>