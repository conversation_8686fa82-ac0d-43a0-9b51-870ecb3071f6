<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.product.dao.DistributionShareMaterialDao">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.product.bo.DistributionShareMaterialDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="share_context" jdbcType="VARCHAR" property="shareContext" />
    <result column="share_pic_list" jdbcType="VARCHAR" property="sharePicList" />
    <result column="share_pic_wish_qrcode" jdbcType="VARCHAR" property="sharePicWishQrCode" />
    <result column="updater_mis" jdbcType="VARCHAR" property="updaterMis" />
    <result column="reviewer_mis" jdbcType="VARCHAR" property="reviewerMis" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, share_context, share_pic_list, share_pic_wish_qrcode, updater_mis, reviewer_mis,add_time, update_time
  </sql>

  <insert id="insertMaterial" parameterType="com.sankuai.carnation.distribution.product.bo.DistributionShareMaterialDo" useGeneratedKeys="true" keyProperty="id">
    insert into
    distribution_share_material_info
    (<include refid="Base_Column_List" />)
    values (#{id,jdbcType=BIGINT}, #{shareContext,jdbcType=VARCHAR}, #{sharePicList,jdbcType=VARCHAR},
        #{sharePicWishQrCode,jdbcType=VARCHAR},
        #{updaterMis,jdbcType=VARCHAR}, #{reviewerMis,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <select id="queryMaterial" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from distribution_share_material_info
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="queryMaterialList" resultType="com.sankuai.carnation.distribution.product.bo.DistributionShareMaterialDo">
    select
    <include refid="Base_Column_List" />
    from distribution_share_material_info
    where id in
    <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

</mapper>