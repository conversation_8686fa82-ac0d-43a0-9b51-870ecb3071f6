<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QROfflineDealGroupDao">
    <resultMap id="BaseResultMap"
               type="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QROfflineDealGroup">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dp_dealgroup_id" jdbcType="BIGINT" property="dpDealGroupId"/>
        <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_account" jdbcType="VARCHAR" property="createAccount"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , dp_dealgroup_id, dp_shop_id, status, create_account, add_time, update_time
    </sql>

    <insert id="create"
            parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QROfflineDealGroup">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into
        qr_offline_dealgroup (dp_dealgroup_id, dp_shop_id, status, create_account)
        values
        (#{dpDealGroupId}
        , #{dpShopId}
        , #{status}
        , #{createAccount})
    </insert>

    <update id="updateStatus">
        update
            qr_offline_dealgroup
        set status = #{status}
        where dp_dealgroup_id = #{dpDealGroupId}
          and dp_shop_id = #{dpShopId}
    </update>

    <select id="queryByDpShopIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        qr_offline_dealgroup
        where
        dp_shop_id = #{dpShopId} and status = #{status}
    </select>

    <select id="loadByDpShopIdAndDpDealGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        qr_offline_dealgroup
        where
        dp_dealgroup_id = #{dpDealGroupId} and dp_shop_id = #{dpShopId}
    </select>

    <select id="queryByDpShopIdsAndDpDealGroupIdAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        qr_offline_dealgroup
        where
        dp_dealgroup_id = #{dpDealGroupId} and status = #{status}
        and dp_shop_id in
        <foreach collection="dpShopIds" index="index" item="dpShopId" separator="," close=")" open="(">
            #{dpShopId}
        </foreach>
    </select>

    <select id="paginationQueryBounded" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        qr_offline_dealgroup
        where status = 1
    </select>
    <select id="queryBoundedDealGroupBeforeUpdateTime"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        qr_offline_dealgroup
        where update_time between #{lastDate} and #{actionDate}
    </select>
</mapper>