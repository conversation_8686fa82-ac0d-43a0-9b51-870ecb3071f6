<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.task.repository.dao.QrShopPromoteTradeDataExtMapper">
    <resultMap id="BaseResultMap"
               type="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
        <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
        <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
        <result column="cat0_id" jdbcType="INTEGER" property="cat0Id" />
        <result column="cat0_name" jdbcType="VARCHAR" property="cat0Name" />
        <result column="cat1_id" jdbcType="INTEGER" property="cat1Id" />
        <result column="cat1_name" jdbcType="VARCHAR" property="cat1Name" />
        <result column="cat2_id" jdbcType="INTEGER" property="cat2Id" />
        <result column="cat2_name" jdbcType="VARCHAR" property="cat2Name" />
        <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
        <result column="dim_city_rank" jdbcType="VARCHAR" property="dimCityRank" />
        <result column="mt_shop_fivescore_new" jdbcType="DECIMAL" property="mtShopFivescoreNew" />
        <result column="dp_shop_fivescore" jdbcType="DECIMAL" property="dpShopFivescore" />
        <result column="dp_region_id" jdbcType="VARCHAR" property="dpRegionId" />
        <result column="dp_region_name" jdbcType="VARCHAR" property="dpRegionName" />
        <result column="mt_region_id" jdbcType="VARCHAR" property="mtRegionId" />
        <result column="mt_region_name" jdbcType="VARCHAR" property="mtRegionName" />
        <result column="mt_district_id" jdbcType="BIGINT" property="mtDistrictId" />
        <result column="mt_district_name" jdbcType="VARCHAR" property="mtDistrictName" />
        <result column="total_score" jdbcType="DECIMAL" property="totalScore" />
        <result column="shopcode_order_cnt" jdbcType="BIGINT" property="shopcodeOrderCnt" />
        <result column="techcode_order_cnt" jdbcType="BIGINT" property="techcodeOrderCnt" />
        <result column="gtv_rank" jdbcType="BIGINT" property="gtvRank" />
        <result column="shop_gtv" jdbcType="DECIMAL" property="shopGtv" />
        <result column="city_id" jdbcType="BIGINT" property="cityId" />
        <result column="city_name" jdbcType="VARCHAR" property="cityName" />
        <result column="gtv_city_rank" jdbcType="BIGINT" property="gtvCityRank" />
        <result column="shopcode_order_city_rank" jdbcType="BIGINT" property="shopcodeOrderCityRank" />
        <result column="op_type" jdbcType="INTEGER" property="opType" />
        <result column="kacka_shopcode_order_city_rank" jdbcType="BIGINT" property="kackaShopcodeOrderCityRank" />
        <result column="shopcode_order_amt" jdbcType="DECIMAL" property="shopcodeOrderAmt" />
        <result column="shopcode_order_amt_city_rank" jdbcType="BIGINT" property="shopcodeOrderAmtCityRank" />
        <result column="kacka_shopcode_order_amt_city_rank" jdbcType="BIGINT" property="kackaShopcodeOrderAmtCityRank" />
        <result column="shopcode_actual_pay_amt" jdbcType="DECIMAL" property="shopcodeActualPayAmt" />
        <result column="shopcode_actual_consume_amt" jdbcType="DECIMAL" property="shopcodeActualConsumeAmt" />
        <result column="shopcode_actual_pay_amt_city_rank" jdbcType="BIGINT" property="shopcodeActualPayAmtCityRank" />
        <result column="shopcode_actual_consume_amt_city_rank" jdbcType="BIGINT" property="shopcodeActualConsumeAmtCityRank" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, update_time, partition_date, dp_shop_id, mt_shop_id, cat0_id, cat0_name, cat1_id,
    cat1_name, cat2_id, cat2_name, shop_type, dim_city_rank, mt_shop_fivescore_new, dp_shop_fivescore,
    dp_region_id, dp_region_name, mt_region_id, mt_region_name, mt_district_id, mt_district_name,
    total_score, shopcode_order_cnt, techcode_order_cnt, gtv_rank, shop_gtv, city_id,
    city_name, gtv_city_rank, shopcode_order_city_rank, op_type, kacka_shopcode_order_city_rank,
    shopcode_order_amt, shopcode_order_amt_city_rank, kacka_shopcode_order_amt_city_rank,
    shopcode_actual_pay_amt, shopcode_actual_consume_amt, shopcode_actual_pay_amt_city_rank,
    shopcode_actual_consume_amt_city_rank
    </sql>
    <sql id="sql_selectLatestPartitionDate">
        select partition_date
        from qr_shop_promote_trade_data
        order by partition_date desc limit 1
    </sql>

    <select id="selectLatestPartitionDate" resultType="string">
        <include refid="sql_selectLatestPartitionDate"/>
    </select>
    <select id="selectInLatestPartitionByExample"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QRShopPromoteTradeDataCriteria"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from qr_shop_promote_trade_data
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        and partition_date = (
        <include refid="sql_selectLatestPartitionDate"/>
        )
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>


    <select id="selectByPartitionAndDpShopId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qr_shop_promote_trade_data
        where dp_shop_id = #{dpShopId}
        and partition_date = DATE_FORMAT(#{partitionDate},'%Y-%m-%d')
    </select>
</mapper>