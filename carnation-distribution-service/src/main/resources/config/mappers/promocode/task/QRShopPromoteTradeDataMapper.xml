<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.task.repository.dao.QrShopPromoteTradeDataMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="partition_date" jdbcType="VARCHAR" property="partitionDate" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="cat0_id" jdbcType="INTEGER" property="cat0Id" />
    <result column="cat0_name" jdbcType="VARCHAR" property="cat0Name" />
    <result column="cat1_id" jdbcType="INTEGER" property="cat1Id" />
    <result column="cat1_name" jdbcType="VARCHAR" property="cat1Name" />
    <result column="cat2_id" jdbcType="INTEGER" property="cat2Id" />
    <result column="cat2_name" jdbcType="VARCHAR" property="cat2Name" />
    <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
    <result column="dim_city_rank" jdbcType="VARCHAR" property="dimCityRank" />
    <result column="mt_shop_fivescore_new" jdbcType="DECIMAL" property="mtShopFivescoreNew" />
    <result column="dp_shop_fivescore" jdbcType="DECIMAL" property="dpShopFivescore" />
    <result column="dp_region_id" jdbcType="VARCHAR" property="dpRegionId" />
    <result column="dp_region_name" jdbcType="VARCHAR" property="dpRegionName" />
    <result column="mt_region_id" jdbcType="VARCHAR" property="mtRegionId" />
    <result column="mt_region_name" jdbcType="VARCHAR" property="mtRegionName" />
    <result column="mt_district_id" jdbcType="BIGINT" property="mtDistrictId" />
    <result column="mt_district_name" jdbcType="VARCHAR" property="mtDistrictName" />
    <result column="total_score" jdbcType="DECIMAL" property="totalScore" />
    <result column="shopcode_order_cnt" jdbcType="BIGINT" property="shopcodeOrderCnt" />
    <result column="techcode_order_cnt" jdbcType="BIGINT" property="techcodeOrderCnt" />
    <result column="gtv_rank" jdbcType="BIGINT" property="gtvRank" />
    <result column="shop_gtv" jdbcType="DECIMAL" property="shopGtv" />
    <result column="city_id" jdbcType="BIGINT" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="gtv_city_rank" jdbcType="BIGINT" property="gtvCityRank" />
    <result column="shopcode_order_city_rank" jdbcType="BIGINT" property="shopcodeOrderCityRank" />
    <result column="op_type" jdbcType="INTEGER" property="opType" />
    <result column="kacka_shopcode_order_city_rank" jdbcType="BIGINT" property="kackaShopcodeOrderCityRank" />
    <result column="shopcode_order_amt" jdbcType="DECIMAL" property="shopcodeOrderAmt" />
    <result column="shopcode_order_amt_city_rank" jdbcType="BIGINT" property="shopcodeOrderAmtCityRank" />
    <result column="kacka_shopcode_order_amt_city_rank" jdbcType="BIGINT" property="kackaShopcodeOrderAmtCityRank" />
    <result column="shopcode_actual_pay_amt" jdbcType="DECIMAL" property="shopcodeActualPayAmt" />
    <result column="shopcode_actual_consume_amt" jdbcType="DECIMAL" property="shopcodeActualConsumeAmt" />
    <result column="shopcode_actual_pay_amt_city_rank" jdbcType="BIGINT" property="shopcodeActualPayAmtCityRank" />
    <result column="shopcode_actual_consume_amt_city_rank" jdbcType="BIGINT" property="shopcodeActualConsumeAmtCityRank" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, update_time, partition_date, dp_shop_id, mt_shop_id, cat0_id, cat0_name, cat1_id, 
    cat1_name, cat2_id, cat2_name, shop_type, dim_city_rank, mt_shop_fivescore_new, dp_shop_fivescore, 
    dp_region_id, dp_region_name, mt_region_id, mt_region_name, mt_district_id, mt_district_name, 
    total_score, shopcode_order_cnt, techcode_order_cnt, gtv_rank, shop_gtv, city_id, 
    city_name, gtv_city_rank, shopcode_order_city_rank, op_type, kacka_shopcode_order_city_rank, 
    shopcode_order_amt, shopcode_order_amt_city_rank, kacka_shopcode_order_amt_city_rank, 
    shopcode_actual_pay_amt, shopcode_actual_consume_amt, shopcode_actual_pay_amt_city_rank, 
    shopcode_actual_consume_amt_city_rank
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qr_shop_promote_trade_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qr_shop_promote_trade_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from qr_shop_promote_trade_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeDataExample">
    delete from qr_shop_promote_trade_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData" useGeneratedKeys="true">
    insert into qr_shop_promote_trade_data (update_time, partition_date, dp_shop_id, 
      mt_shop_id, cat0_id, cat0_name, 
      cat1_id, cat1_name, cat2_id, 
      cat2_name, shop_type, dim_city_rank, 
      mt_shop_fivescore_new, dp_shop_fivescore, dp_region_id, 
      dp_region_name, mt_region_id, mt_region_name, 
      mt_district_id, mt_district_name, total_score, 
      shopcode_order_cnt, techcode_order_cnt, gtv_rank, 
      shop_gtv, city_id, city_name, 
      gtv_city_rank, shopcode_order_city_rank, op_type, 
      kacka_shopcode_order_city_rank, shopcode_order_amt, 
      shopcode_order_amt_city_rank, kacka_shopcode_order_amt_city_rank, 
      shopcode_actual_pay_amt, shopcode_actual_consume_amt, 
      shopcode_actual_pay_amt_city_rank, shopcode_actual_consume_amt_city_rank
      )
    values (#{updateTime,jdbcType=TIMESTAMP}, #{partitionDate,jdbcType=VARCHAR}, #{dpShopId,jdbcType=BIGINT}, 
      #{mtShopId,jdbcType=BIGINT}, #{cat0Id,jdbcType=INTEGER}, #{cat0Name,jdbcType=VARCHAR}, 
      #{cat1Id,jdbcType=INTEGER}, #{cat1Name,jdbcType=VARCHAR}, #{cat2Id,jdbcType=INTEGER}, 
      #{cat2Name,jdbcType=VARCHAR}, #{shopType,jdbcType=VARCHAR}, #{dimCityRank,jdbcType=VARCHAR}, 
      #{mtShopFivescoreNew,jdbcType=DECIMAL}, #{dpShopFivescore,jdbcType=DECIMAL}, #{dpRegionId,jdbcType=VARCHAR}, 
      #{dpRegionName,jdbcType=VARCHAR}, #{mtRegionId,jdbcType=VARCHAR}, #{mtRegionName,jdbcType=VARCHAR}, 
      #{mtDistrictId,jdbcType=BIGINT}, #{mtDistrictName,jdbcType=VARCHAR}, #{totalScore,jdbcType=DECIMAL}, 
      #{shopcodeOrderCnt,jdbcType=BIGINT}, #{techcodeOrderCnt,jdbcType=BIGINT}, #{gtvRank,jdbcType=BIGINT}, 
      #{shopGtv,jdbcType=DECIMAL}, #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, 
      #{gtvCityRank,jdbcType=BIGINT}, #{shopcodeOrderCityRank,jdbcType=BIGINT}, #{opType,jdbcType=INTEGER}, 
      #{kackaShopcodeOrderCityRank,jdbcType=BIGINT}, #{shopcodeOrderAmt,jdbcType=DECIMAL}, 
      #{shopcodeOrderAmtCityRank,jdbcType=BIGINT}, #{kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT}, 
      #{shopcodeActualPayAmt,jdbcType=DECIMAL}, #{shopcodeActualConsumeAmt,jdbcType=DECIMAL}, 
      #{shopcodeActualPayAmtCityRank,jdbcType=BIGINT}, #{shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData" useGeneratedKeys="true">
    insert into qr_shop_promote_trade_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="partitionDate != null">
        partition_date,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="cat0Id != null">
        cat0_id,
      </if>
      <if test="cat0Name != null">
        cat0_name,
      </if>
      <if test="cat1Id != null">
        cat1_id,
      </if>
      <if test="cat1Name != null">
        cat1_name,
      </if>
      <if test="cat2Id != null">
        cat2_id,
      </if>
      <if test="cat2Name != null">
        cat2_name,
      </if>
      <if test="shopType != null">
        shop_type,
      </if>
      <if test="dimCityRank != null">
        dim_city_rank,
      </if>
      <if test="mtShopFivescoreNew != null">
        mt_shop_fivescore_new,
      </if>
      <if test="dpShopFivescore != null">
        dp_shop_fivescore,
      </if>
      <if test="dpRegionId != null">
        dp_region_id,
      </if>
      <if test="dpRegionName != null">
        dp_region_name,
      </if>
      <if test="mtRegionId != null">
        mt_region_id,
      </if>
      <if test="mtRegionName != null">
        mt_region_name,
      </if>
      <if test="mtDistrictId != null">
        mt_district_id,
      </if>
      <if test="mtDistrictName != null">
        mt_district_name,
      </if>
      <if test="totalScore != null">
        total_score,
      </if>
      <if test="shopcodeOrderCnt != null">
        shopcode_order_cnt,
      </if>
      <if test="techcodeOrderCnt != null">
        techcode_order_cnt,
      </if>
      <if test="gtvRank != null">
        gtv_rank,
      </if>
      <if test="shopGtv != null">
        shop_gtv,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="gtvCityRank != null">
        gtv_city_rank,
      </if>
      <if test="shopcodeOrderCityRank != null">
        shopcode_order_city_rank,
      </if>
      <if test="opType != null">
        op_type,
      </if>
      <if test="kackaShopcodeOrderCityRank != null">
        kacka_shopcode_order_city_rank,
      </if>
      <if test="shopcodeOrderAmt != null">
        shopcode_order_amt,
      </if>
      <if test="shopcodeOrderAmtCityRank != null">
        shopcode_order_amt_city_rank,
      </if>
      <if test="kackaShopcodeOrderAmtCityRank != null">
        kacka_shopcode_order_amt_city_rank,
      </if>
      <if test="shopcodeActualPayAmt != null">
        shopcode_actual_pay_amt,
      </if>
      <if test="shopcodeActualConsumeAmt != null">
        shopcode_actual_consume_amt,
      </if>
      <if test="shopcodeActualPayAmtCityRank != null">
        shopcode_actual_pay_amt_city_rank,
      </if>
      <if test="shopcodeActualConsumeAmtCityRank != null">
        shopcode_actual_consume_amt_city_rank,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="partitionDate != null">
        #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="cat0Id != null">
        #{cat0Id,jdbcType=INTEGER},
      </if>
      <if test="cat0Name != null">
        #{cat0Name,jdbcType=VARCHAR},
      </if>
      <if test="cat1Id != null">
        #{cat1Id,jdbcType=INTEGER},
      </if>
      <if test="cat1Name != null">
        #{cat1Name,jdbcType=VARCHAR},
      </if>
      <if test="cat2Id != null">
        #{cat2Id,jdbcType=INTEGER},
      </if>
      <if test="cat2Name != null">
        #{cat2Name,jdbcType=VARCHAR},
      </if>
      <if test="shopType != null">
        #{shopType,jdbcType=VARCHAR},
      </if>
      <if test="dimCityRank != null">
        #{dimCityRank,jdbcType=VARCHAR},
      </if>
      <if test="mtShopFivescoreNew != null">
        #{mtShopFivescoreNew,jdbcType=DECIMAL},
      </if>
      <if test="dpShopFivescore != null">
        #{dpShopFivescore,jdbcType=DECIMAL},
      </if>
      <if test="dpRegionId != null">
        #{dpRegionId,jdbcType=VARCHAR},
      </if>
      <if test="dpRegionName != null">
        #{dpRegionName,jdbcType=VARCHAR},
      </if>
      <if test="mtRegionId != null">
        #{mtRegionId,jdbcType=VARCHAR},
      </if>
      <if test="mtRegionName != null">
        #{mtRegionName,jdbcType=VARCHAR},
      </if>
      <if test="mtDistrictId != null">
        #{mtDistrictId,jdbcType=BIGINT},
      </if>
      <if test="mtDistrictName != null">
        #{mtDistrictName,jdbcType=VARCHAR},
      </if>
      <if test="totalScore != null">
        #{totalScore,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeOrderCnt != null">
        #{shopcodeOrderCnt,jdbcType=BIGINT},
      </if>
      <if test="techcodeOrderCnt != null">
        #{techcodeOrderCnt,jdbcType=BIGINT},
      </if>
      <if test="gtvRank != null">
        #{gtvRank,jdbcType=BIGINT},
      </if>
      <if test="shopGtv != null">
        #{shopGtv,jdbcType=DECIMAL},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="gtvCityRank != null">
        #{gtvCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeOrderCityRank != null">
        #{shopcodeOrderCityRank,jdbcType=BIGINT},
      </if>
      <if test="opType != null">
        #{opType,jdbcType=INTEGER},
      </if>
      <if test="kackaShopcodeOrderCityRank != null">
        #{kackaShopcodeOrderCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeOrderAmt != null">
        #{shopcodeOrderAmt,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeOrderAmtCityRank != null">
        #{shopcodeOrderAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="kackaShopcodeOrderAmtCityRank != null">
        #{kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeActualPayAmt != null">
        #{shopcodeActualPayAmt,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeActualConsumeAmt != null">
        #{shopcodeActualConsumeAmt,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeActualPayAmtCityRank != null">
        #{shopcodeActualPayAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeActualConsumeAmtCityRank != null">
        #{shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeDataExample" resultType="java.lang.Long">
    select count(*) from qr_shop_promote_trade_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qr_shop_promote_trade_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.partitionDate != null">
        partition_date = #{record.partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.mtShopId != null">
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="record.cat0Id != null">
        cat0_id = #{record.cat0Id,jdbcType=INTEGER},
      </if>
      <if test="record.cat0Name != null">
        cat0_name = #{record.cat0Name,jdbcType=VARCHAR},
      </if>
      <if test="record.cat1Id != null">
        cat1_id = #{record.cat1Id,jdbcType=INTEGER},
      </if>
      <if test="record.cat1Name != null">
        cat1_name = #{record.cat1Name,jdbcType=VARCHAR},
      </if>
      <if test="record.cat2Id != null">
        cat2_id = #{record.cat2Id,jdbcType=INTEGER},
      </if>
      <if test="record.cat2Name != null">
        cat2_name = #{record.cat2Name,jdbcType=VARCHAR},
      </if>
      <if test="record.shopType != null">
        shop_type = #{record.shopType,jdbcType=VARCHAR},
      </if>
      <if test="record.dimCityRank != null">
        dim_city_rank = #{record.dimCityRank,jdbcType=VARCHAR},
      </if>
      <if test="record.mtShopFivescoreNew != null">
        mt_shop_fivescore_new = #{record.mtShopFivescoreNew,jdbcType=DECIMAL},
      </if>
      <if test="record.dpShopFivescore != null">
        dp_shop_fivescore = #{record.dpShopFivescore,jdbcType=DECIMAL},
      </if>
      <if test="record.dpRegionId != null">
        dp_region_id = #{record.dpRegionId,jdbcType=VARCHAR},
      </if>
      <if test="record.dpRegionName != null">
        dp_region_name = #{record.dpRegionName,jdbcType=VARCHAR},
      </if>
      <if test="record.mtRegionId != null">
        mt_region_id = #{record.mtRegionId,jdbcType=VARCHAR},
      </if>
      <if test="record.mtRegionName != null">
        mt_region_name = #{record.mtRegionName,jdbcType=VARCHAR},
      </if>
      <if test="record.mtDistrictId != null">
        mt_district_id = #{record.mtDistrictId,jdbcType=BIGINT},
      </if>
      <if test="record.mtDistrictName != null">
        mt_district_name = #{record.mtDistrictName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalScore != null">
        total_score = #{record.totalScore,jdbcType=DECIMAL},
      </if>
      <if test="record.shopcodeOrderCnt != null">
        shopcode_order_cnt = #{record.shopcodeOrderCnt,jdbcType=BIGINT},
      </if>
      <if test="record.techcodeOrderCnt != null">
        techcode_order_cnt = #{record.techcodeOrderCnt,jdbcType=BIGINT},
      </if>
      <if test="record.gtvRank != null">
        gtv_rank = #{record.gtvRank,jdbcType=BIGINT},
      </if>
      <if test="record.shopGtv != null">
        shop_gtv = #{record.shopGtv,jdbcType=DECIMAL},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=BIGINT},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.gtvCityRank != null">
        gtv_city_rank = #{record.gtvCityRank,jdbcType=BIGINT},
      </if>
      <if test="record.shopcodeOrderCityRank != null">
        shopcode_order_city_rank = #{record.shopcodeOrderCityRank,jdbcType=BIGINT},
      </if>
      <if test="record.opType != null">
        op_type = #{record.opType,jdbcType=INTEGER},
      </if>
      <if test="record.kackaShopcodeOrderCityRank != null">
        kacka_shopcode_order_city_rank = #{record.kackaShopcodeOrderCityRank,jdbcType=BIGINT},
      </if>
      <if test="record.shopcodeOrderAmt != null">
        shopcode_order_amt = #{record.shopcodeOrderAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.shopcodeOrderAmtCityRank != null">
        shopcode_order_amt_city_rank = #{record.shopcodeOrderAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="record.kackaShopcodeOrderAmtCityRank != null">
        kacka_shopcode_order_amt_city_rank = #{record.kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="record.shopcodeActualPayAmt != null">
        shopcode_actual_pay_amt = #{record.shopcodeActualPayAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.shopcodeActualConsumeAmt != null">
        shopcode_actual_consume_amt = #{record.shopcodeActualConsumeAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.shopcodeActualPayAmtCityRank != null">
        shopcode_actual_pay_amt_city_rank = #{record.shopcodeActualPayAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="record.shopcodeActualConsumeAmtCityRank != null">
        shopcode_actual_consume_amt_city_rank = #{record.shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qr_shop_promote_trade_data
    set id = #{record.id,jdbcType=BIGINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      partition_date = #{record.partitionDate,jdbcType=VARCHAR},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
      cat0_id = #{record.cat0Id,jdbcType=INTEGER},
      cat0_name = #{record.cat0Name,jdbcType=VARCHAR},
      cat1_id = #{record.cat1Id,jdbcType=INTEGER},
      cat1_name = #{record.cat1Name,jdbcType=VARCHAR},
      cat2_id = #{record.cat2Id,jdbcType=INTEGER},
      cat2_name = #{record.cat2Name,jdbcType=VARCHAR},
      shop_type = #{record.shopType,jdbcType=VARCHAR},
      dim_city_rank = #{record.dimCityRank,jdbcType=VARCHAR},
      mt_shop_fivescore_new = #{record.mtShopFivescoreNew,jdbcType=DECIMAL},
      dp_shop_fivescore = #{record.dpShopFivescore,jdbcType=DECIMAL},
      dp_region_id = #{record.dpRegionId,jdbcType=VARCHAR},
      dp_region_name = #{record.dpRegionName,jdbcType=VARCHAR},
      mt_region_id = #{record.mtRegionId,jdbcType=VARCHAR},
      mt_region_name = #{record.mtRegionName,jdbcType=VARCHAR},
      mt_district_id = #{record.mtDistrictId,jdbcType=BIGINT},
      mt_district_name = #{record.mtDistrictName,jdbcType=VARCHAR},
      total_score = #{record.totalScore,jdbcType=DECIMAL},
      shopcode_order_cnt = #{record.shopcodeOrderCnt,jdbcType=BIGINT},
      techcode_order_cnt = #{record.techcodeOrderCnt,jdbcType=BIGINT},
      gtv_rank = #{record.gtvRank,jdbcType=BIGINT},
      shop_gtv = #{record.shopGtv,jdbcType=DECIMAL},
      city_id = #{record.cityId,jdbcType=BIGINT},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      gtv_city_rank = #{record.gtvCityRank,jdbcType=BIGINT},
      shopcode_order_city_rank = #{record.shopcodeOrderCityRank,jdbcType=BIGINT},
      op_type = #{record.opType,jdbcType=INTEGER},
      kacka_shopcode_order_city_rank = #{record.kackaShopcodeOrderCityRank,jdbcType=BIGINT},
      shopcode_order_amt = #{record.shopcodeOrderAmt,jdbcType=DECIMAL},
      shopcode_order_amt_city_rank = #{record.shopcodeOrderAmtCityRank,jdbcType=BIGINT},
      kacka_shopcode_order_amt_city_rank = #{record.kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT},
      shopcode_actual_pay_amt = #{record.shopcodeActualPayAmt,jdbcType=DECIMAL},
      shopcode_actual_consume_amt = #{record.shopcodeActualConsumeAmt,jdbcType=DECIMAL},
      shopcode_actual_pay_amt_city_rank = #{record.shopcodeActualPayAmtCityRank,jdbcType=BIGINT},
      shopcode_actual_consume_amt_city_rank = #{record.shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData">
    update qr_shop_promote_trade_data
    <set>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="partitionDate != null">
        partition_date = #{partitionDate,jdbcType=VARCHAR},
      </if>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="cat0Id != null">
        cat0_id = #{cat0Id,jdbcType=INTEGER},
      </if>
      <if test="cat0Name != null">
        cat0_name = #{cat0Name,jdbcType=VARCHAR},
      </if>
      <if test="cat1Id != null">
        cat1_id = #{cat1Id,jdbcType=INTEGER},
      </if>
      <if test="cat1Name != null">
        cat1_name = #{cat1Name,jdbcType=VARCHAR},
      </if>
      <if test="cat2Id != null">
        cat2_id = #{cat2Id,jdbcType=INTEGER},
      </if>
      <if test="cat2Name != null">
        cat2_name = #{cat2Name,jdbcType=VARCHAR},
      </if>
      <if test="shopType != null">
        shop_type = #{shopType,jdbcType=VARCHAR},
      </if>
      <if test="dimCityRank != null">
        dim_city_rank = #{dimCityRank,jdbcType=VARCHAR},
      </if>
      <if test="mtShopFivescoreNew != null">
        mt_shop_fivescore_new = #{mtShopFivescoreNew,jdbcType=DECIMAL},
      </if>
      <if test="dpShopFivescore != null">
        dp_shop_fivescore = #{dpShopFivescore,jdbcType=DECIMAL},
      </if>
      <if test="dpRegionId != null">
        dp_region_id = #{dpRegionId,jdbcType=VARCHAR},
      </if>
      <if test="dpRegionName != null">
        dp_region_name = #{dpRegionName,jdbcType=VARCHAR},
      </if>
      <if test="mtRegionId != null">
        mt_region_id = #{mtRegionId,jdbcType=VARCHAR},
      </if>
      <if test="mtRegionName != null">
        mt_region_name = #{mtRegionName,jdbcType=VARCHAR},
      </if>
      <if test="mtDistrictId != null">
        mt_district_id = #{mtDistrictId,jdbcType=BIGINT},
      </if>
      <if test="mtDistrictName != null">
        mt_district_name = #{mtDistrictName,jdbcType=VARCHAR},
      </if>
      <if test="totalScore != null">
        total_score = #{totalScore,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeOrderCnt != null">
        shopcode_order_cnt = #{shopcodeOrderCnt,jdbcType=BIGINT},
      </if>
      <if test="techcodeOrderCnt != null">
        techcode_order_cnt = #{techcodeOrderCnt,jdbcType=BIGINT},
      </if>
      <if test="gtvRank != null">
        gtv_rank = #{gtvRank,jdbcType=BIGINT},
      </if>
      <if test="shopGtv != null">
        shop_gtv = #{shopGtv,jdbcType=DECIMAL},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="gtvCityRank != null">
        gtv_city_rank = #{gtvCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeOrderCityRank != null">
        shopcode_order_city_rank = #{shopcodeOrderCityRank,jdbcType=BIGINT},
      </if>
      <if test="opType != null">
        op_type = #{opType,jdbcType=INTEGER},
      </if>
      <if test="kackaShopcodeOrderCityRank != null">
        kacka_shopcode_order_city_rank = #{kackaShopcodeOrderCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeOrderAmt != null">
        shopcode_order_amt = #{shopcodeOrderAmt,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeOrderAmtCityRank != null">
        shopcode_order_amt_city_rank = #{shopcodeOrderAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="kackaShopcodeOrderAmtCityRank != null">
        kacka_shopcode_order_amt_city_rank = #{kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeActualPayAmt != null">
        shopcode_actual_pay_amt = #{shopcodeActualPayAmt,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeActualConsumeAmt != null">
        shopcode_actual_consume_amt = #{shopcodeActualConsumeAmt,jdbcType=DECIMAL},
      </if>
      <if test="shopcodeActualPayAmtCityRank != null">
        shopcode_actual_pay_amt_city_rank = #{shopcodeActualPayAmtCityRank,jdbcType=BIGINT},
      </if>
      <if test="shopcodeActualConsumeAmtCityRank != null">
        shopcode_actual_consume_amt_city_rank = #{shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTradeData">
    update qr_shop_promote_trade_data
    set update_time = #{updateTime,jdbcType=TIMESTAMP},
      partition_date = #{partitionDate,jdbcType=VARCHAR},
      dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      cat0_id = #{cat0Id,jdbcType=INTEGER},
      cat0_name = #{cat0Name,jdbcType=VARCHAR},
      cat1_id = #{cat1Id,jdbcType=INTEGER},
      cat1_name = #{cat1Name,jdbcType=VARCHAR},
      cat2_id = #{cat2Id,jdbcType=INTEGER},
      cat2_name = #{cat2Name,jdbcType=VARCHAR},
      shop_type = #{shopType,jdbcType=VARCHAR},
      dim_city_rank = #{dimCityRank,jdbcType=VARCHAR},
      mt_shop_fivescore_new = #{mtShopFivescoreNew,jdbcType=DECIMAL},
      dp_shop_fivescore = #{dpShopFivescore,jdbcType=DECIMAL},
      dp_region_id = #{dpRegionId,jdbcType=VARCHAR},
      dp_region_name = #{dpRegionName,jdbcType=VARCHAR},
      mt_region_id = #{mtRegionId,jdbcType=VARCHAR},
      mt_region_name = #{mtRegionName,jdbcType=VARCHAR},
      mt_district_id = #{mtDistrictId,jdbcType=BIGINT},
      mt_district_name = #{mtDistrictName,jdbcType=VARCHAR},
      total_score = #{totalScore,jdbcType=DECIMAL},
      shopcode_order_cnt = #{shopcodeOrderCnt,jdbcType=BIGINT},
      techcode_order_cnt = #{techcodeOrderCnt,jdbcType=BIGINT},
      gtv_rank = #{gtvRank,jdbcType=BIGINT},
      shop_gtv = #{shopGtv,jdbcType=DECIMAL},
      city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      gtv_city_rank = #{gtvCityRank,jdbcType=BIGINT},
      shopcode_order_city_rank = #{shopcodeOrderCityRank,jdbcType=BIGINT},
      op_type = #{opType,jdbcType=INTEGER},
      kacka_shopcode_order_city_rank = #{kackaShopcodeOrderCityRank,jdbcType=BIGINT},
      shopcode_order_amt = #{shopcodeOrderAmt,jdbcType=DECIMAL},
      shopcode_order_amt_city_rank = #{shopcodeOrderAmtCityRank,jdbcType=BIGINT},
      kacka_shopcode_order_amt_city_rank = #{kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT},
      shopcode_actual_pay_amt = #{shopcodeActualPayAmt,jdbcType=DECIMAL},
      shopcode_actual_consume_amt = #{shopcodeActualConsumeAmt,jdbcType=DECIMAL},
      shopcode_actual_pay_amt_city_rank = #{shopcodeActualPayAmtCityRank,jdbcType=BIGINT},
      shopcode_actual_consume_amt_city_rank = #{shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into qr_shop_promote_trade_data
    (update_time, partition_date, dp_shop_id, mt_shop_id, cat0_id, cat0_name, cat1_id, 
      cat1_name, cat2_id, cat2_name, shop_type, dim_city_rank, mt_shop_fivescore_new, 
      dp_shop_fivescore, dp_region_id, dp_region_name, mt_region_id, mt_region_name, 
      mt_district_id, mt_district_name, total_score, shopcode_order_cnt, techcode_order_cnt, 
      gtv_rank, shop_gtv, city_id, city_name, gtv_city_rank, shopcode_order_city_rank, 
      op_type, kacka_shopcode_order_city_rank, shopcode_order_amt, shopcode_order_amt_city_rank, 
      kacka_shopcode_order_amt_city_rank, shopcode_actual_pay_amt, shopcode_actual_consume_amt, 
      shopcode_actual_pay_amt_city_rank, shopcode_actual_consume_amt_city_rank)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.updateTime,jdbcType=TIMESTAMP}, #{item.partitionDate,jdbcType=VARCHAR}, #{item.dpShopId,jdbcType=BIGINT}, 
        #{item.mtShopId,jdbcType=BIGINT}, #{item.cat0Id,jdbcType=INTEGER}, #{item.cat0Name,jdbcType=VARCHAR}, 
        #{item.cat1Id,jdbcType=INTEGER}, #{item.cat1Name,jdbcType=VARCHAR}, #{item.cat2Id,jdbcType=INTEGER}, 
        #{item.cat2Name,jdbcType=VARCHAR}, #{item.shopType,jdbcType=VARCHAR}, #{item.dimCityRank,jdbcType=VARCHAR}, 
        #{item.mtShopFivescoreNew,jdbcType=DECIMAL}, #{item.dpShopFivescore,jdbcType=DECIMAL}, 
        #{item.dpRegionId,jdbcType=VARCHAR}, #{item.dpRegionName,jdbcType=VARCHAR}, #{item.mtRegionId,jdbcType=VARCHAR}, 
        #{item.mtRegionName,jdbcType=VARCHAR}, #{item.mtDistrictId,jdbcType=BIGINT}, #{item.mtDistrictName,jdbcType=VARCHAR}, 
        #{item.totalScore,jdbcType=DECIMAL}, #{item.shopcodeOrderCnt,jdbcType=BIGINT}, 
        #{item.techcodeOrderCnt,jdbcType=BIGINT}, #{item.gtvRank,jdbcType=BIGINT}, #{item.shopGtv,jdbcType=DECIMAL}, 
        #{item.cityId,jdbcType=BIGINT}, #{item.cityName,jdbcType=VARCHAR}, #{item.gtvCityRank,jdbcType=BIGINT}, 
        #{item.shopcodeOrderCityRank,jdbcType=BIGINT}, #{item.opType,jdbcType=INTEGER}, 
        #{item.kackaShopcodeOrderCityRank,jdbcType=BIGINT}, #{item.shopcodeOrderAmt,jdbcType=DECIMAL}, 
        #{item.shopcodeOrderAmtCityRank,jdbcType=BIGINT}, #{item.kackaShopcodeOrderAmtCityRank,jdbcType=BIGINT}, 
        #{item.shopcodeActualPayAmt,jdbcType=DECIMAL}, #{item.shopcodeActualConsumeAmt,jdbcType=DECIMAL}, 
        #{item.shopcodeActualPayAmtCityRank,jdbcType=BIGINT}, #{item.shopcodeActualConsumeAmtCityRank,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
</mapper>