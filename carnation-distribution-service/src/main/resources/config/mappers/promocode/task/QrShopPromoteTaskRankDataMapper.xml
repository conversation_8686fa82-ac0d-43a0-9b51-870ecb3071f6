<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.task.repository.dao.QrShopPromoteTaskRankDataMapper">
    <resultMap id="BaseResultMap"
               type="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankData">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="partition_date" jdbcType="DATE" property="partitionDate"/>
        <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId"/>
        <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId"/>
        <result column="cat1_id" jdbcType="INTEGER" property="cat1Id"/>
        <result column="cat1_name" jdbcType="VARCHAR" property="cat1Name"/>
        <result column="region_name" jdbcType="VARCHAR" property="regionName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="city_id" jdbcType="BIGINT" property="cityId"/>
        <result column="sort_limit" jdbcType="INTEGER" property="sortLimit"/>
        <result column="rank" jdbcType="INTEGER" property="rank"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="sort_filed" jdbcType="VARCHAR" property="sortFiled"/>
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, task_id, partition_date, dp_shop_id, mt_shop_id, cat1_id, cat1_name, region_name,
    city_name, city_id, sort_limit, rank, add_time, update_time, sort_filed, sort_order
    </sql>
    <select id="selectByExample"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankDataExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from qr_shop_promote_task_rank_data
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                limit ${offset}, ${rows}
            </if>
            <if test="offset == null">
                limit ${rows}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from qr_shop_promote_task_rank_data
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from qr_shop_promote_task_rank_data
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankDataExample">
        delete from qr_shop_promote_task_rank_data
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankData"
            useGeneratedKeys="true">
        insert into qr_shop_promote_task_rank_data (task_id, partition_date, dp_shop_id,
                                                    mt_shop_id, cat1_id, cat1_name,
                                                    region_name, city_name, city_id,
                                                    sort_limit, rank, sort_filed, sort_order)
        values (#{taskId,jdbcType=BIGINT}, #{partitionDate,jdbcType=DATE}, #{dpShopId,jdbcType=BIGINT},
                #{mtShopId,jdbcType=BIGINT}, #{cat1Id,jdbcType=INTEGER}, #{cat1Name,jdbcType=VARCHAR},
                #{regionName,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{cityId,jdbcType=BIGINT},
                #{sortLimit,jdbcType=INTEGER}, #{rank,jdbcType=INTEGER}, #{sortFiled,jdbcType=VARCHAR}, #{sortOrder,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankData"
            useGeneratedKeys="true">
        insert into qr_shop_promote_task_rank_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="partitionDate != null">
                partition_date,
            </if>
            <if test="dpShopId != null">
                dp_shop_id,
            </if>
            <if test="mtShopId != null">
                mt_shop_id,
            </if>
            <if test="cat1Id != null">
                cat1_id,
            </if>
            <if test="cat1Name != null">
                cat1_name,
            </if>
            <if test="regionName != null">
                region_name,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="sortLimit != null">
                sort_limit,
            </if>
            <if test="rank != null">
                rank,
            </if>
            <if test="sortFiled != null">
                sort_filed,
            </if>
            <if test="sortOrder != null">
                sort_order,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="partitionDate != null">
                #{partitionDate,jdbcType=DATE},
            </if>
            <if test="dpShopId != null">
                #{dpShopId,jdbcType=BIGINT},
            </if>
            <if test="mtShopId != null">
                #{mtShopId,jdbcType=BIGINT},
            </if>
            <if test="cat1Id != null">
                #{cat1Id,jdbcType=INTEGER},
            </if>
            <if test="cat1Name != null">
                #{cat1Name,jdbcType=VARCHAR},
            </if>
            <if test="regionName != null">
                #{regionName,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="sortLimit != null">
                #{sortLimit,jdbcType=INTEGER},
            </if>
            <if test="rank != null">
                #{rank,jdbcType=INTEGER},
            </if>
            <if test="sortFiled != null">
                #{sortFiled,jdbcType=VARCHAR},
            </if>
            <if test="sortOrder != null">
                #{sortOrder,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankDataExample"
            resultType="java.lang.Long">
        select count(*) from qr_shop_promote_task_rank_data
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update qr_shop_promote_task_rank_data
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.taskId != null">
                task_id = #{record.taskId,jdbcType=BIGINT},
            </if>
            <if test="record.partitionDate != null">
                partition_date = #{record.partitionDate,jdbcType=DATE},
            </if>
            <if test="record.dpShopId != null">
                dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
            </if>
            <if test="record.mtShopId != null">
                mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
            </if>
            <if test="record.cat1Id != null">
                cat1_id = #{record.cat1Id,jdbcType=INTEGER},
            </if>
            <if test="record.cat1Name != null">
                cat1_name = #{record.cat1Name,jdbcType=VARCHAR},
            </if>
            <if test="record.regionName != null">
                region_name = #{record.regionName,jdbcType=VARCHAR},
            </if>
            <if test="record.cityName != null">
                city_name = #{record.cityName,jdbcType=VARCHAR},
            </if>
            <if test="record.cityId != null">
                city_id = #{record.cityId,jdbcType=BIGINT},
            </if>
            <if test="record.sortLimit != null">
                sort_limit = #{record.sortLimit,jdbcType=INTEGER},
            </if>
            <if test="record.rank != null">
                rank = #{record.rank,jdbcType=INTEGER},
            </if>
            <if test="record.sortFiled != null">
                sort_filed = #{record.sortFiled,jdbcType=VARCHAR},
            </if>
            <if test="record.sortOrder != null">
                sort_order = #{record.sortOrder,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update qr_shop_promote_task_rank_data
        set id = #{record.id,jdbcType=BIGINT},
        task_id = #{record.taskId,jdbcType=BIGINT},
        partition_date = #{record.partitionDate,jdbcType=DATE},
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
        mt_shop_id = #{record.mtShopId,jdbcType=BIGINT},
        cat1_id = #{record.cat1Id,jdbcType=INTEGER},
        cat1_name = #{record.cat1Name,jdbcType=VARCHAR},
        region_name = #{record.regionName,jdbcType=VARCHAR},
        city_name = #{record.cityName,jdbcType=VARCHAR},
        city_id = #{record.cityId,jdbcType=BIGINT},
        sort_limit = #{record.sortLimit,jdbcType=INTEGER},
        rank = #{record.rank,jdbcType=INTEGER},
        sort_filed = #{record.sortFiled,jdbcType=VARCHAR},
        sort_order = #{record.sortOrder,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankData">
        update qr_shop_promote_task_rank_data
        <set>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
            <if test="partitionDate != null">
                partition_date = #{partitionDate,jdbcType=DATE},
            </if>
            <if test="dpShopId != null">
                dp_shop_id = #{dpShopId,jdbcType=BIGINT},
            </if>
            <if test="mtShopId != null">
                mt_shop_id = #{mtShopId,jdbcType=BIGINT},
            </if>
            <if test="cat1Id != null">
                cat1_id = #{cat1Id,jdbcType=INTEGER},
            </if>
            <if test="cat1Name != null">
                cat1_name = #{cat1Name,jdbcType=VARCHAR},
            </if>
            <if test="regionName != null">
                region_name = #{regionName,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="sortLimit != null">
                sort_limit = #{sortLimit,jdbcType=INTEGER},
            </if>
            <if test="rank != null">
                rank = #{rank,jdbcType=INTEGER},
            </if>
            <if test="sortFiled != null">
                sort_filed = #{sortFiled,jdbcType=VARCHAR},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.sankuai.carnation.distribution.promocode.task.repository.model.QrShopPromoteTaskRankData">
        update qr_shop_promote_task_rank_data
        set task_id        = #{taskId,jdbcType=BIGINT},
            partition_date = #{partitionDate,jdbcType=DATE},
            dp_shop_id     = #{dpShopId,jdbcType=BIGINT},
            mt_shop_id     = #{mtShopId,jdbcType=BIGINT},
            cat1_id        = #{cat1Id,jdbcType=INTEGER},
            cat1_name      = #{cat1Name,jdbcType=VARCHAR},
            region_name    = #{regionName,jdbcType=VARCHAR},
            city_name      = #{cityName,jdbcType=VARCHAR},
            city_id        = #{cityId,jdbcType=BIGINT},
            sort_limit     = #{sortLimit,jdbcType=INTEGER},
            rank           = #{rank,jdbcType=INTEGER},
            sort_filed     = #{sortFiled,jdbcType=VARCHAR},
            sort_order     = #{sortOrder,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into qr_shop_promote_task_rank_data
        (task_id, partition_date, dp_shop_id, mt_shop_id, cat1_id, cat1_name, region_name,
        city_name, city_id, sort_limit, rank, sort_filed, sort_order
        )
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.taskId,jdbcType=BIGINT}, #{item.partitionDate,jdbcType=DATE}, #{item.dpShopId,jdbcType=BIGINT},
            #{item.mtShopId,jdbcType=BIGINT}, #{item.cat1Id,jdbcType=INTEGER}, #{item.cat1Name,jdbcType=VARCHAR},
            #{item.regionName,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, #{item.cityId,jdbcType=BIGINT},
            #{item.sortLimit,jdbcType=INTEGER}, #{item.rank,jdbcType=INTEGER}, #{item.sortFiled,jdbcType=VARCHAR},
            #{item.sortOrder,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
</mapper>