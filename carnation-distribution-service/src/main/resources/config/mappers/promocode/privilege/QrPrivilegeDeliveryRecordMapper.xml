<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QrPrivilegeDeliveryRecordMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QrPrivilegeDeliveryRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="privilege_code" jdbcType="VARCHAR" property="privilegeCode" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="platform" jdbcType="TINYINT" property="platform" />
    <result column="dp_shop_id" jdbcType="BIGINT" property="dpShopId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="deliver_source" jdbcType="VARCHAR" property="deliverSource" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, privilege_code, biz_id, platform, dp_shop_id, status, start_time, end_time, deliver_source, 
    add_time, update_time, task_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QrPrivilegeDeliveryRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qr_privilege_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from qr_privilege_delivery_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from qr_privilege_delivery_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QrPrivilegeDeliveryRecordExample">
    delete from qr_privilege_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QrPrivilegeDeliveryRecord" useGeneratedKeys="true">
    insert into qr_privilege_delivery_record (privilege_code, biz_id, platform, 
      dp_shop_id, status, start_time, 
      end_time, deliver_source, add_time, 
      update_time, task_id)
    values (#{privilegeCode,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, #{platform,jdbcType=TINYINT}, 
      #{dpShopId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{deliverSource,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{taskId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QrPrivilegeDeliveryRecord" useGeneratedKeys="true">
    insert into qr_privilege_delivery_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="privilegeCode != null">
        privilege_code,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="deliverSource != null">
        deliver_source,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="privilegeCode != null">
        #{privilegeCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverSource != null">
        #{deliverSource,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QrPrivilegeDeliveryRecordExample" resultType="java.lang.Long">
    select count(*) from qr_privilege_delivery_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qr_privilege_delivery_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.privilegeCode != null">
        privilege_code = #{record.privilegeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=TINYINT},
      </if>
      <if test="record.dpShopId != null">
        dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliverSource != null">
        deliver_source = #{record.deliverSource,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qr_privilege_delivery_record
    set id = #{record.id,jdbcType=BIGINT},
      privilege_code = #{record.privilegeCode,jdbcType=VARCHAR},
      biz_id = #{record.bizId,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=TINYINT},
      dp_shop_id = #{record.dpShopId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      deliver_source = #{record.deliverSource,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task_id = #{record.taskId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QrPrivilegeDeliveryRecord">
    update qr_privilege_delivery_record
    <set>
      <if test="privilegeCode != null">
        privilege_code = #{privilegeCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=TINYINT},
      </if>
      <if test="dpShopId != null">
        dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverSource != null">
        deliver_source = #{deliverSource,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QrPrivilegeDeliveryRecord">
    update qr_privilege_delivery_record
    set privilege_code = #{privilegeCode,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=TINYINT},
      dp_shop_id = #{dpShopId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      deliver_source = #{deliverSource,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into qr_privilege_delivery_record
    (privilege_code, biz_id, platform, dp_shop_id, status, start_time, end_time, deliver_source, 
      add_time, update_time, task_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.privilegeCode,jdbcType=VARCHAR}, #{item.bizId,jdbcType=VARCHAR}, #{item.platform,jdbcType=TINYINT}, 
        #{item.dpShopId,jdbcType=BIGINT}, #{item.status,jdbcType=TINYINT}, #{item.startTime,jdbcType=TIMESTAMP}, 
        #{item.endTime,jdbcType=TIMESTAMP}, #{item.deliverSource,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.taskId,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>