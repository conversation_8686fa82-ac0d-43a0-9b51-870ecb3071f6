<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QrPrivilegeDeliveryRecordExtMapper">
  <insert id="insertSelectiveWithCheck" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QrPrivilegeDeliveryRecord" useGeneratedKeys="true">
    insert into qr_privilege_delivery_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="privilegeCode != null">
        privilege_code,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="dpShopId != null">
        dp_shop_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="deliverSource != null">
        deliver_source,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
    </trim>
    <trim prefix="select" suffix="" suffixOverrides=",">
      <if test="privilegeCode != null">
        #{privilegeCode,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="dpShopId != null">
        #{dpShopId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliverSource != null">
        #{deliverSource,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
    </trim>
    from dual where not exists (
        select 1 from qr_privilege_delivery_record
        where privilege_code = #{privilegeCode,jdbcType=VARCHAR}
          and biz_id = #{bizId,jdbcType=VARCHAR}
          and dp_shop_id = #{dpShopId,jdbcType=BIGINT}
          and platform = #{platform,jdbcType=TINYINT}
        limit 1 for update);
  </insert>
</mapper>