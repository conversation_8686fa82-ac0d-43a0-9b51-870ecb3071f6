<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QRPrivilegeTemplateExtMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="display_content_type" jdbcType="TINYINT" property="displayContentType" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt">
    <result column="display_content" jdbcType="LONGVARCHAR" property="displayContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, display_content_type, add_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    display_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExtCriteria" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qr_privilege_template_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExtCriteria" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qr_privilege_template_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from qr_privilege_template_ext
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from qr_privilege_template_ext
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExtCriteria">
    delete from qr_privilege_template_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt" useGeneratedKeys="true">
    insert into qr_privilege_template_ext (template_id, display_content_type, add_time, 
      update_time, display_content)
    values (#{templateId,jdbcType=BIGINT}, #{displayContentType,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{displayContent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt" useGeneratedKeys="true">
    insert into qr_privilege_template_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        template_id,
      </if>
      <if test="displayContentType != null">
        display_content_type,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="displayContent != null">
        display_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="displayContentType != null">
        #{displayContentType,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="displayContent != null">
        #{displayContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExtCriteria" resultType="java.lang.Long">
    select count(*) from qr_privilege_template_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update qr_privilege_template_ext
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.displayContentType != null">
        display_content_type = #{record.displayContentType,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.displayContent != null">
        display_content = #{record.displayContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update qr_privilege_template_ext
    set id = #{record.id,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      display_content_type = #{record.displayContentType,jdbcType=TINYINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      display_content = #{record.displayContent,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update qr_privilege_template_ext
    set id = #{record.id,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      display_content_type = #{record.displayContentType,jdbcType=TINYINT},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt">
    update qr_privilege_template_ext
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="displayContentType != null">
        display_content_type = #{displayContentType,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="displayContent != null">
        display_content = #{displayContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt">
    update qr_privilege_template_ext
    set template_id = #{templateId,jdbcType=BIGINT},
      display_content_type = #{displayContentType,jdbcType=TINYINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      display_content = #{displayContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QRPrivilegeTemplateExt">
    update qr_privilege_template_ext
    set template_id = #{templateId,jdbcType=BIGINT},
      display_content_type = #{displayContentType,jdbcType=TINYINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>