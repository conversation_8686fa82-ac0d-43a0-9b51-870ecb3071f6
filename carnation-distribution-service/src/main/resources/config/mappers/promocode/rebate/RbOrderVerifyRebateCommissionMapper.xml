<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.rebate.repository.dao.RbOrderVerifyRebateCommissionMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="rebate_biz_type" jdbcType="INTEGER" property="rebateBizType" />
    <result column="rebate_biz_id" jdbcType="VARCHAR" property="rebateBizId" />
    <result column="rebate_user_type" jdbcType="INTEGER" property="rebateUserType" />
    <result column="rebate_user_id" jdbcType="VARCHAR" property="rebateUserId" />
    <result column="predict_rebate_cent" jdbcType="BIGINT" property="predictRebateCent" />
    <result column="final_rebate_cent" jdbcType="BIGINT" property="finalRebateCent" />
    <result column="rebate_desc" jdbcType="VARCHAR" property="rebateDesc" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="activity_record_id" jdbcType="BIGINT" property="activityRecordId" />
    <result column="platform_user" jdbcType="VARCHAR" property="platformUser" />
    <result column="group_new_user" jdbcType="VARCHAR" property="groupNewUser" />
    <result column="verify_amount" jdbcType="BIGINT" property="verifyAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_type, order_id, coupon_id, rebate_biz_type, rebate_biz_id, rebate_user_type, 
    rebate_user_id, predict_rebate_cent, final_rebate_cent, rebate_desc, ext_info, status, 
    add_time, update_time, activity_record_id, platform_user, group_new_user, verify_amount
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.example.RbOrderVerifyRebateCommissionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rb_order_verify_rebate_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rb_order_verify_rebate_commission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rb_order_verify_rebate_commission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.example.RbOrderVerifyRebateCommissionExample">
    delete from rb_order_verify_rebate_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission" useGeneratedKeys="true">
    insert into rb_order_verify_rebate_commission (order_type, order_id, coupon_id, 
      rebate_biz_type, rebate_biz_id, rebate_user_type, 
      rebate_user_id, predict_rebate_cent, final_rebate_cent, 
      rebate_desc, ext_info, status, 
      add_time, update_time, activity_record_id, 
      platform_user, group_new_user, verify_amount
      )
    values (#{orderType,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, #{couponId,jdbcType=VARCHAR}, 
      #{rebateBizType,jdbcType=INTEGER}, #{rebateBizId,jdbcType=VARCHAR}, #{rebateUserType,jdbcType=INTEGER}, 
      #{rebateUserId,jdbcType=VARCHAR}, #{predictRebateCent,jdbcType=BIGINT}, #{finalRebateCent,jdbcType=BIGINT}, 
      #{rebateDesc,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{activityRecordId,jdbcType=BIGINT}, 
      #{platformUser,jdbcType=VARCHAR}, #{groupNewUser,jdbcType=VARCHAR}, #{verifyAmount,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission" useGeneratedKeys="true">
    insert into rb_order_verify_rebate_commission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="rebateBizType != null">
        rebate_biz_type,
      </if>
      <if test="rebateBizId != null">
        rebate_biz_id,
      </if>
      <if test="rebateUserType != null">
        rebate_user_type,
      </if>
      <if test="rebateUserId != null">
        rebate_user_id,
      </if>
      <if test="predictRebateCent != null">
        predict_rebate_cent,
      </if>
      <if test="finalRebateCent != null">
        final_rebate_cent,
      </if>
      <if test="rebateDesc != null">
        rebate_desc,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="activityRecordId != null">
        activity_record_id,
      </if>
      <if test="platformUser != null">
        platform_user,
      </if>
      <if test="groupNewUser != null">
        group_new_user,
      </if>
      <if test="verifyAmount != null">
        verify_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="rebateBizType != null">
        #{rebateBizType,jdbcType=INTEGER},
      </if>
      <if test="rebateBizId != null">
        #{rebateBizId,jdbcType=VARCHAR},
      </if>
      <if test="rebateUserType != null">
        #{rebateUserType,jdbcType=INTEGER},
      </if>
      <if test="rebateUserId != null">
        #{rebateUserId,jdbcType=VARCHAR},
      </if>
      <if test="predictRebateCent != null">
        #{predictRebateCent,jdbcType=BIGINT},
      </if>
      <if test="finalRebateCent != null">
        #{finalRebateCent,jdbcType=BIGINT},
      </if>
      <if test="rebateDesc != null">
        #{rebateDesc,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityRecordId != null">
        #{activityRecordId,jdbcType=BIGINT},
      </if>
      <if test="platformUser != null">
        #{platformUser,jdbcType=VARCHAR},
      </if>
      <if test="groupNewUser != null">
        #{groupNewUser,jdbcType=VARCHAR},
      </if>
      <if test="verifyAmount != null">
        #{verifyAmount,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.example.RbOrderVerifyRebateCommissionExample" resultType="java.lang.Long">
    select count(*) from rb_order_verify_rebate_commission
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rb_order_verify_rebate_commission
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateBizType != null">
        rebate_biz_type = #{record.rebateBizType,jdbcType=INTEGER},
      </if>
      <if test="record.rebateBizId != null">
        rebate_biz_id = #{record.rebateBizId,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateUserType != null">
        rebate_user_type = #{record.rebateUserType,jdbcType=INTEGER},
      </if>
      <if test="record.rebateUserId != null">
        rebate_user_id = #{record.rebateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.predictRebateCent != null">
        predict_rebate_cent = #{record.predictRebateCent,jdbcType=BIGINT},
      </if>
      <if test="record.finalRebateCent != null">
        final_rebate_cent = #{record.finalRebateCent,jdbcType=BIGINT},
      </if>
      <if test="record.rebateDesc != null">
        rebate_desc = #{record.rebateDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activityRecordId != null">
        activity_record_id = #{record.activityRecordId,jdbcType=BIGINT},
      </if>
      <if test="record.platformUser != null">
        platform_user = #{record.platformUser,jdbcType=VARCHAR},
      </if>
      <if test="record.groupNewUser != null">
        group_new_user = #{record.groupNewUser,jdbcType=VARCHAR},
      </if>
      <if test="record.verifyAmount != null">
        verify_amount = #{record.verifyAmount,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rb_order_verify_rebate_commission
    set id = #{record.id,jdbcType=BIGINT},
      order_type = #{record.orderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      rebate_biz_type = #{record.rebateBizType,jdbcType=INTEGER},
      rebate_biz_id = #{record.rebateBizId,jdbcType=VARCHAR},
      rebate_user_type = #{record.rebateUserType,jdbcType=INTEGER},
      rebate_user_id = #{record.rebateUserId,jdbcType=VARCHAR},
      predict_rebate_cent = #{record.predictRebateCent,jdbcType=BIGINT},
      final_rebate_cent = #{record.finalRebateCent,jdbcType=BIGINT},
      rebate_desc = #{record.rebateDesc,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      activity_record_id = #{record.activityRecordId,jdbcType=BIGINT},
      platform_user = #{record.platformUser,jdbcType=VARCHAR},
      group_new_user = #{record.groupNewUser,jdbcType=VARCHAR},
      verify_amount = #{record.verifyAmount,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission">
    update rb_order_verify_rebate_commission
    <set>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="rebateBizType != null">
        rebate_biz_type = #{rebateBizType,jdbcType=INTEGER},
      </if>
      <if test="rebateBizId != null">
        rebate_biz_id = #{rebateBizId,jdbcType=VARCHAR},
      </if>
      <if test="rebateUserType != null">
        rebate_user_type = #{rebateUserType,jdbcType=INTEGER},
      </if>
      <if test="rebateUserId != null">
        rebate_user_id = #{rebateUserId,jdbcType=VARCHAR},
      </if>
      <if test="predictRebateCent != null">
        predict_rebate_cent = #{predictRebateCent,jdbcType=BIGINT},
      </if>
      <if test="finalRebateCent != null">
        final_rebate_cent = #{finalRebateCent,jdbcType=BIGINT},
      </if>
      <if test="rebateDesc != null">
        rebate_desc = #{rebateDesc,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activityRecordId != null">
        activity_record_id = #{activityRecordId,jdbcType=BIGINT},
      </if>
      <if test="platformUser != null">
        platform_user = #{platformUser,jdbcType=VARCHAR},
      </if>
      <if test="groupNewUser != null">
        group_new_user = #{groupNewUser,jdbcType=VARCHAR},
      </if>
      <if test="verifyAmount != null">
        verify_amount = #{verifyAmount,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.carnation.distribution.promocode.rebate.repository.db.RbOrderVerifyRebateCommission">
    update rb_order_verify_rebate_commission
    set order_type = #{orderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      rebate_biz_type = #{rebateBizType,jdbcType=INTEGER},
      rebate_biz_id = #{rebateBizId,jdbcType=VARCHAR},
      rebate_user_type = #{rebateUserType,jdbcType=INTEGER},
      rebate_user_id = #{rebateUserId,jdbcType=VARCHAR},
      predict_rebate_cent = #{predictRebateCent,jdbcType=BIGINT},
      final_rebate_cent = #{finalRebateCent,jdbcType=BIGINT},
      rebate_desc = #{rebateDesc,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      activity_record_id = #{activityRecordId,jdbcType=BIGINT},
      platform_user = #{platformUser,jdbcType=VARCHAR},
      group_new_user = #{groupNewUser,jdbcType=VARCHAR},
      verify_amount = #{verifyAmount,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into rb_order_verify_rebate_commission
    (order_type, order_id, coupon_id, rebate_biz_type, rebate_biz_id, rebate_user_type, 
      rebate_user_id, predict_rebate_cent, final_rebate_cent, rebate_desc, ext_info, 
      status, add_time, update_time, activity_record_id, platform_user, group_new_user, 
      verify_amount)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderType,jdbcType=INTEGER}, #{item.orderId,jdbcType=VARCHAR}, #{item.couponId,jdbcType=VARCHAR}, 
        #{item.rebateBizType,jdbcType=INTEGER}, #{item.rebateBizId,jdbcType=VARCHAR}, #{item.rebateUserType,jdbcType=INTEGER}, 
        #{item.rebateUserId,jdbcType=VARCHAR}, #{item.predictRebateCent,jdbcType=BIGINT}, 
        #{item.finalRebateCent,jdbcType=BIGINT}, #{item.rebateDesc,jdbcType=VARCHAR}, #{item.extInfo,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.activityRecordId,jdbcType=BIGINT}, #{item.platformUser,jdbcType=VARCHAR}, 
        #{item.groupNewUser,jdbcType=VARCHAR}, #{item.verifyAmount,jdbcType=BIGINT})
    </foreach>
  </insert>
</mapper>