<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.rebate.repository.dao.RbOrderVerifyRebateCommissionExtMapper">
    <select id="sumVerifyAmountByActivityId" resultType="java.lang.Long">
        SELECT SUM(verify_amount)
        FROM rb_order_verify_rebate_commission
        WHERE activity_record_id = #{activityId}
          AND status = 1
    </select>
</mapper>
