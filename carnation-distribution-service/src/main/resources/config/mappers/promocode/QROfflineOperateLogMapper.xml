<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.carnation.distribution.promocode.privilege.repository.dao.QROfflineOperateLogDao">
  <resultMap id="BaseResultMap" type="com.sankuai.carnation.distribution.promocode.privilege.repository.model.QROfflineOperateLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="operate_id" jdbcType="INTEGER" property="operateId" />
    <result column="operate_account" jdbcType="VARCHAR" property="operateAccount" />
    <result column="operate_content" jdbcType="VARCHAR" property="operateContent" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, operate_type, operate_id, operate_account, operate_content, add_time, update_time
  </sql>

  <insert id="create" parameterType="map">
    insert into
        qr_offline_operate_log(operate_type, operate_id, operate_account, operate_content)
    values
    (#{qrOfflineOperateLog.operateType}
    , #{qrOfflineOperateLog.operateId}
    , #{qrOfflineOperateLog.operateAccount}
    , #{qrOfflineOperateLog.operateContent})
  </insert>
</mapper>