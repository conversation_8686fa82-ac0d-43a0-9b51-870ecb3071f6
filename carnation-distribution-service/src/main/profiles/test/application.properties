#å¥åº·æ£æµç¸å³éç½®,å¦éæ¹å¨è¯·åæ­¥ä¿®æ¹check.shæä»¶TEST_URL
management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive
#serverç¸å³
server.port=8080

# ?????????????(????)?
# all_init_bean-???????bean?????????????
# thrift-????MDP??/?MDP??thrift-client/thrift-server???????
# mafka-????MDP??mafka-consumer/mafka-producer???????
# zebra-????MDP??/?MDP??zebra-dataSource???????
# cellar-????MDP??/?MDP??cellar???????
# squirrel-????MDP??/?MDP??squirrel???????
mdp.runtime.asyncinit.quick-open=thrift,cellar,squirrel,mafka,zebra

# ???????????????????????????????????????????????
mdp.runtime.asyncinit.thread-pool-size=32
